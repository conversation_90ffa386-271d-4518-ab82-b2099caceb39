const mongoose = require("mongoose");

const NotificationEventTemplatesSchema = new mongoose.Schema({
    event: { type: String },
    template: { type: String },
    isMedia: { type: Boolean },
    mediaType: { type: String },
    config: { type: Object },
    header: { type: Object },
    ctaConfig: { type: Object },

});

const NotificationEventTemplates = mongoose.model("NotificationEventTemplates", NotificationEventTemplatesSchema, "NotificationEventTemplates");

module.exports.NotificationEventTemplates = NotificationEventTemplates;
