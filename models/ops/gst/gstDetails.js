const mongoose = require("mongoose");
const joi = require("@hapi/joi");
const jwt = require("jsonwebtoken");
const config = require("config");
// const Schema = mongoose.Schema;

const gstDetailsXambitSchema = new mongoose.Schema({
    gstNo: { type: String },
    business: {
        name: { type: String },
        ids: [{
            id_kind: { type: Number },
            id: { type: String },
        },],
        promoters: [String],
        trade_name: { type: String },
        alt_name: { type: String },
        address: {
            line1: { type: String },
            line2: { type: String },
            city: { type: String },
            state_code: { type: String },
            state: { type: String },
            postal_code: { type: String },
            country: { type: String }
        },
        other_ids: [String],
        email: { type: String },
        phone: { type: String },
        bank: { type: String },
        cheque: { type: String },
        wire: { type: String },
        website: { type: String }
    },
    gstrbusiness: {
        kind: { type: String },
        jurisdiction_name: { type: String },
        jurisdiction_code: { type: String },
        taxpayer_kind: { type: String },
        einvoice_status: { type: Boolean },
        nature_of_business: [String],
        date_of_registration: { type: Date, format: "date-time" },
        jurisdiction_state_name: { type: String },
        jurisdiction_state_code: { type: String },
        date_cancel: { type: Date, format: "date-time" },
    },
    entries: [{
        period: {
            start:{type : String},
            end : {type : String},
            buffer_month_before: { type: String },
            buffer_month_after: { type: String },
        },
        source: { type: String },
        // collection: { type: String },
        gstin: { type: String },
        num_paid_with_cash: { type: Number },
        breakup_paid_with_cash: { type: Object },
        paid_igst_with_cash: {
            tax: { type: Number },
            interest: { type: Number },
            late_fee: { type: Number }
        },
        paid_cgst_with_cash: {
            tax: { type: Number },
            interest: { type: Number },
            late_fee: { type: Number }
        },
        paid_sgst_with_cash: {
            tax: { type: Number },
            interest: { type: Number },
            late_fee: { type: Number }
        },
        paid_cess_with_cash: {
            tax: { type: Number },
            interest: { type: Number },
            late_fee: { type: Number }
        },
        paid_igst_with_igst_cred: { type: Number },
        paid_igst_with_cgst_cred: { type: Number },
        paid_igst_with_sgst_cred: { type: Number },
        paid_cgst_with_igst_cred: { type: Number },
        paid_cgst_with_cgst_cred: { type: Number },
        paid_sgst_with_igst_cred: { type: Number },
        paid_sgst_with_sgst_cred: { type: Number },
        paid_cess_with_cess_cred: { type: Number },
        supply_outward_zero_rated: {
            sgst: { type: Number },
            cgst: { type: Number },
            igst: { type: Number },
            cess: { type: Number },
            tax: { type: Number },
            kind: { type: String }
        },
        supply_outward_nil_rated: {
            sgst: { type: Number },
            cgst: { type: Number },
            igst: { type: Number },
            cess: { type: Number },
            tax: { type: Number },
            kind: { type: String }
        },
        supply_outward_non_gst: {
            sgst: { type: Number },
            cgst: { type: Number },
            igst: { type: Number },
            cess: { type: Number },
            tax: { type: Number },
            kind: { type: String }
        },
        supply_outward_taxable: {
            sgst: { type: Number },
            cgst: { type: Number },
            igst: { type: Number },
            cess: { type: Number },
            tax: { type: Number },
            kind: { type: String }
        },
        supply_inward: {
            sgst: { type: Number },
            cgst: { type: Number },
            igst: { type: Number },
            cess: { type: Number },
            tax: { type: Number },
            kind: { type: String }
        },
        supply_interstate_unregistered: {
            breakup: { type: Object },
            numstates: { type: Number },
            overall: {
                count: { type: Number },
                totalval: { type: Number },
                totaligst: { type: Number },
                maxval: { type: Number },
                minval: { type: Number },
                maxigst: { type: Number },
                minigst: { type: Number },
                meanval: { type: Number },
                meanigst: { type: Number },
                stddevval: { type: Number },
                stddevigst: { type: Number }
            }
        },
        supply_interstate_composition: {
            breakup: { type: Object },
            numstates: { type: Number },
            overall: {
                count: { type: Number },
                totalval: { type: Number },
                totaligst: { type: Number },
                maxval: { type: Number },
                minval: { type: Number },
                maxigst: { type: Number },
                minigst: { type: Number },
                meanval: { type: Number },
                meanigst: { type: Number },
                stddevval: { type: Number },
                stddevigst: { type: Number }
            }
        },
        supply_interstate_uin: {
            breakup: { type: Object },
            numstates: { type: Number },
            overall: {
                count: { type: Number },
                totalval: { type: Number },
                totaligst: { type: Number },
                maxval: { type: Number },
                minval: { type: Number },
                maxigst: { type: Number },
                minigst: { type: Number },
                meanval: { type: Number },
                meanigst: { type: Number },
                stddevval: { type: Number },
                stddevigst: { type: Number }
            }
        },
        supply_ecommerce: {
            registered: { type: String },
            unregistered: { type: String }
        },
        interest: {
            sgst: { type: Number },
            cgst: { type: Number },
            igst: { type: Number },
            cess: { type: Number },
            tax: { type: Number },
            kind: { type: String }
        },
        latefee: {
            sgst: { type: Number },
            cgst: { type: Number },
            igst: { type: Number },
            cess: { type: Number },
            tax: { type: Number },
            kind: { type: String }
        },
        itc_available: {
            sgst: { type: Number },
            cgst: { type: Number },
            igst: { type: Number },
            cess: { type: Number },
            tax: { type: Number },
            kind: { type: String }
        },
        itc_reverse: {
            sgst: { type: Number },
            cgst: { type: Number },
            igst: { type: Number },
            cess: { type: Number },
            tax: { type: Number },
            kind: { type: String }
        },
        itc_net: {
            sgst: { type: Number },
            cgst: { type: Number },
            igst: { type: Number },
            cess: { type: Number },
            tax: { type: Number },
            kind: { type: String }
        },
        itc_not_eligible: {
            sgst: { type: Number },
            cgst: { type: Number },
            igst: { type: Number },
            cess: { type: Number },
            tax: { type: Number },
            kind: { type: String }
        },
        revenue: { type: Number },
        tax: { type: Number },
        created: { type: Date, format: "date-time" },
        derived_from: { type: Array },
    }],
    metrics: [{
        order: { type: Number },
        datapoint: {
            Metric: {
                kind: { type: Number },
                kindstr: { type: String },
                name: { type: String },
                value: {
                    value: { type: Number },
                    kind: { type: Number }
                },
                period: { type: Number },
                is_estimate: { type: Boolean },
                note: { type: String },
                start: { type: String },
                end: { type: String }
            }
        }
    }],
    created: { type: Date, format: "date-time" },
    updated: { type: Date, format: "date-time" },
    period: {
        start: { type: String },
        end: { type: String },
        buffer_month_before: { type: String },
        buffer_month_after: { type: String },
    },
    authdate: { type: Date, format: "date-time" },
    insertedOn: { type: Date, default: Date.now, format: "date-time" },
    userId: { type: String },
    gstPrivateData: { type: Boolean, default: false }
    // creator: {
    //     id: { type: String },
    //     name: { type: String },
    //     email: { type: String },
    //     perm: { type: Number },
    // }
})

module.exports.gstDetailsXambit = mongoose.model("gstDetailsXambit", gstDetailsXambitSchema, "gstDetailsXambit");

//--------------------------------------------------Validations--------------------------------------------------//

module.exports.validateGstData = function (model) {
    const GSTdata = joi.object({
        gstNo: joi.string().allow("", null).required(),
        userId: joi.string().required(),
    });

    return GSTdata.validate(model);
};