const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const MerchantShopTypeSchema = new mongoose.Schema({
    merchantCode: { type: String },
    partnerCode: { type: String },
    partnerId: Schema.ObjectId,
    shopType: { type: String },
    month: { type: Number },
    year: { type: Number },
    insertedOn: { type: Date, default: Date.now, format: "date-time" },
});

exports.MerchantShopType = mongoose.model("MerchantShopType", MerchantShopTypeSchema, "MerchantShopType");