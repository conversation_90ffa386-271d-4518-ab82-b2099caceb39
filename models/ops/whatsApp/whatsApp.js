const mongoose = require("mongoose");

const whatsAppLogSchema = new mongoose.Schema({
    userId: mongoose.Schema.ObjectId,
    loanId: mongoose.Schema.ObjectId,
    merchantCode: { type: String },
    trxnId: { type: String },
    productId: { type: String },
    request: { type: String },
    response: { type: String },
    requestSentOn: { type: Date },
    responseReceivedOn: { type: Date },
    mobileNo: { type: String },
    template: { type: String },
    type: { type: String },
    url: { type: String },
    channel: { type: String },
    parameters: { type: String },
    mid: { type: String },
    currentStatus: { type: String },
    reason: { type: String },
    statusUpdatedOn: { type: Date },
    code: { type: String },
    deliveryStatus: [
        {
            eventType: { type: String },
            timestamp: { type: String },
            date: { type: String },
            mid: { type: String },
            status: { type: String },
            reason: { type: String },
            code: { type: String },
            insertedOn: { type: Date }
        }
    ]
});

const WhatsAppLog = mongoose.model("WhatsAppLog", whatsAppLogSchema, "WhatsAppLog");

module.exports.WhatsAppLog = WhatsAppLog;

const wAWebhookLogSchema = new mongoose.Schema({
    response: { type: String },
    insertedOn: { type: Date },
    eventType: { type: String },
    channel: { type: String },
    recipient: {
        to: { type: String },
        recipient_type: { type: String },
        reference: {
            cust_ref: { type: String },
            messageTag1: { type: String },
            conversationId: { type: String }
        }
    },
    events: {
        eventType: { type: String },
        timestamp: { type: Date },
        date: { type: String },
        mid: { type: String }
    },
    eventContent: {
        message: {
            from: { type: String },
            id: { type: String },
            text: {
                body: { type: String }
            },
            document: {
                caption: { type: String },
                filename: { type: String },
                mime_type: { type: String },
                sha5: { type: String },
                fileLink: { type: String }
            },
            image: {
                caption: { type: String },
                mime_type: { type: String },
                sha5: { type: String },
                fileLink: { type: String }
            },
            location: {
                latitude: { type: String },
                longitude: { type: String }
            },
            contacts: [
                {
                    addresses: [
                    ],
                    emails: [
                    ],
                    name: {
                        first_name: { type: String },
                        formatted_name: { type: String }
                    },
                    org: {
                    },
                    phones: [
                        {
                            phone: { type: String },
                            type: { type: String },
                            wa_id: { type: String }
                        }
                    ],
                    urls: [
                    ]
                }
            ],
            video: {
                mime_type: { type: String },
                sha5: { type: String },
                fileLink: { type: String }
            },
            button: {
                payload: { type: String },
                text: { type: String }
            },
            to: { type: String },
            contentType: { type: String },
            contextmid: { type: String },
            attachmentType: { type: String }
        }
    },
    notificationAttributes: {
        status: { type: String },
        reason: { type: String },
        code: { type: String }
    },
    smsFallback: {
        reason: { type: String },
        status: { type: String }
    },
    exception: { type: String }
});

const WAWebhookLog = mongoose.model("WAWebhookLog", wAWebhookLogSchema, "WAWebhookLog");

module.exports.WAWebhookLog = WAWebhookLog;
