const mongoose = require("mongoose");
const joi = require("@hapi/joi");
const jwt = require("jsonwebtoken");
const config = require("config");
const Schema = mongoose.Schema;

const smsLogsSchema = mongoose.Schema({
  url: { type: String },
  methodName: { type: String, required: true },
  mobileNo: { type: String, required: true },
  status: { type: String },
  response: { type: String },
  responseBody: { type: String },
  exception: { type: String },
  creationDate: { type: Date, default: Date.now, required: true },
});

const smsLog = new mongoose.model("smsLog", smsLogsSchema);

module.exports.smsLog = smsLog;

const messageQueueSchema = mongoose.Schema({
  loanId: Schema.ObjectId,
  source: { type: String },
  module: { type: String },
  apiType: { type: String },
  type: { type: String },
  partnerType: { type: String },
  emiNumber: { type: Number },
  subType: { type: String },
  noOfAttempts: { type: Number },
  requestModel: { type: Object },
  errorMsg: { type: String },
  isActive: { type: Boolean, default: true },
  insertedOn: { type: Date, default: Date.now, format: "date-time" },
  updateOn: { type: Date, default: Date.now, format: "date-time" }
});

module.exports.MessageQueue = new mongoose.model("MessageQueue", messageQueueSchema, "MessageQueue");