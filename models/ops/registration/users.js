const mongoose = require("mongoose");
const joi = require("@hapi/joi");
const jwt = require("jsonwebtoken");
const config = require("config");
const Schema = mongoose.Schema;

// const passwordComplexity = require('joi-password-complexity');

// const complexityOptions = {
//   min: 5,
//   max: 30,
//   lowerCase: 1,
//   upperCase: 1,
//   numeric: 1,
//   symbol: 1,
//   requirementCount: 2,
// }

const userInfoSchema = new mongoose.Schema({
    panNo: { type: String },
    referrerId: {type : String},
    occupationType : {type : String},
    merchantCode: { type: String },
    panDocDetails: [{
        fileName: { type: String },
        filePath: { type: String },
        isActive: { type: Boolean, default: true },
        insertedOn: { type: Date, default: Date.now, format: "date-time" },
    }],
    panDocVerification: {
        isNumberVerified: { type: Boolean },
        isDocVerified: { type: Boolean },
        type: { type: String },
        isDocMatch: { type: Boolean },
        createdOn: { type: Date },
        details: {
            name: { type: String },
            docNo: { type: String },
            dob: { type: String },
            document_type: { type: String },
            id_proof_type: { type: String },
            typeOfHolder: { type: String },
            isIndividual: { type: Boolean }, //Optional
            isValid: { type: Boolean }, //Optional
            firstName: { type: String }, //Optional
            middleName: { type: String }, //Optional
            lastName: { type: String }, //Optional
            title: { type: String }, //Optional
            panStatus: { type: String }, //Optional
            panStatusCode: { type: String }, //Optional
            aadhaarSeedingStatus: { type: String }, //Optional
            aadhaarSeedingStatusCode: { type: String }, //Optional
            lastUpdatedOn: { type: String },
            //docPath: { type: String },
            photoPath: { type: String },
            fathers_name: { type: String }
        },
        ocr: {
            name: { type: String },
            fathers_name: { type: String },
            id_no: { type: String },
            dob: { type: String },
            id_type: { type: String },
            signaturePath: { type: String },
            photoPath: { type: String },
        },
        verificationResponse: {
            is_pan_dob_valid: { type: Boolean },
            name_matched: { type: Boolean },
            verified: { type: Boolean },
        },
        errorDetails: {
            api: {
                details: { type: String },
                code: { type: String },
                message: { type: String }
            },
            ocr: {
                details: { type: String },
                code: { type: String },
                message: { type: String }
            }
        }
    },
    userDetails: {
        companyName : {type : String},
        educationQualification : { type: String },
        residenceOwnership :  { type: String },
        loanAmount: { type: String },
        userName: { type: String },
        password: { type: String },
        role: { type: String },
        firstName: { type: String },
        middleName: { type: String },
        lastName: { type: String },
        emailId: { type: Array },
        // mobileNo: { type: Array },
        mobileNo: [
            {
                mobileNumber: { type: String },
                isPrimary: { type: Boolean },
                isVerified: { type: Boolean },
                channel: {
                    sms: { type: Object },
                    whatsapp: { type: Object },
                }
            }
        ],
        dob: { type: Date },
        employment: { type: String },
        isEmailValidated: { type: Boolean, default: false },
        isMobileValidated: { type: Boolean, default: false },
        clientCode: { type: String },
        clientCodeGenDate: { type: Date },
        clientCodeGenBy: { type: String },
        kycRemarks: { type: String },
        kycStatus: { type: String },
        passwordModifiedOn: { type: Date, format: "date-time" },
        registeredFrom: { type: String },
        imageUrl: { type: String },
        socialMediaId: { type: String },
        documentConsent: { type: Boolean, default: false },
        docConsentGivenOn: { type: Date, default: null },
        kycApprovedOn: { type: Date, default: null },
        kycUpdatedOn: { type: Date, default: null },
        kycUpdatedBy: { type: Schema.ObjectId },
        kycUpdatedByRole: { type: String },
        adminModifiedDate: { type: Date },
        entitlementRole: { type: Array },
        loan: {
            lastUpdatedByLoanId: { type: Schema.ObjectId },
            lastUpdatedByLoanNo: { type: String },
            lastUpdatedByPartner: { type: String },
            lastUpdatedByPartnerId: { type: Schema.ObjectId },
            updatedOn: { type: Date }
        },
        detailsMismatch: {
            isMismatch: { type: Boolean },
            type: { type: String },
            details: {
                poiName: { type: String },
                poaName: { type: String },
                poiDob: { type: Date },
                poaDob: { type: Date }
            },
            insertedOn: { type: Date },
            modifiedOn: { type: Date }
        },
        whatsApp: {
            whatsAppNumber: { type: String },
            whatsAppConsent: { type: Boolean, default: false },
            consentGivenOn: { type: Date }
        },
        primaryMobileUpdatedOn: { type: Date }
    },
    addressDetails: [{
        add1: { type: String },
        add2: { type: String },
        add3: { type: String },
        type: { type: String },
        city: { type: String },
        state: { type: String },
        pincode: { type: String },
        country: { type: String },
        insertedOn: { type: Date, default: Date.now, format: "date-time" },
        perAdd1: { type: String },
        perAdd2: { type: String },
        perAdd3: { type: String },
    }],
    questionnairreDetails: {
        phoneConnectionType: { type: String },
        whatsApp: { type: String },
        gender: { type: String },
        maritalStatus: { type: String },
        children: { type: String },
        degree: { type: String },
        monthlyIncome: { type: String },
        monthlyOutgoing: { type: String },
        jobType: { type: String },
        homeOwnershipType: { type: String },
        vehicle: { type: String },
        purchasedInOneYear: { type: String },
        recommended: { type: String },
        recommendedBy: { type: String },
        modifiedOn: { type: Date }
    },
    tnc: { type: String },
    tncAgreedOn: { type: Date, default: Date.now, format: "date-time" },
    profileDocDetails: [{
        fileName: { type: String },
        filePath: { type: String },
        isActive: { type: Boolean, default: true },
        insertedOn: { type: Date, default: Date.now, format: "date-time" },
    }],
    poaDetails: [{
        poaNo: { type: String },
        poaType: { type: String },
        poaVerificationDocType: { type: String },
        isMasked: { type: Boolean, default: false },
        docDetails: [{
            fileName: { type: String },
            filePath: { type: String },
            position: { type: String },
            isActive: { type: Boolean, default: true },
            insertedOn: { type: Date, default: Date.now, format: "date-time" },
            documentPassword: { type: String }
        }],
        insertedOn: { type: Date, default: Date.now, format: "date-time" },
        docVerification: {
            isNumberVerified: { type: Boolean },
            isDocVerified: { type: Boolean },
            type: { type: String },
            verificationType: { type: String },
            isDocMatch: { type: Boolean },
            createdOn: { type: Date },
            dl: {
                ocr: {
                    name: { type: String },
                    id_no: { type: String },
                    dob: { type: String },
                    id_type: { type: String },
                    photoPath: { type: String },
                },
                verificationResponse: {
                    verified: { type: Boolean },
                    Date_Of_Issue: { type: String },
                    NT: { type: String },
                    Old_New_DL_No: { type: String },
                    Holders_Name: { type: String },
                    Hazardous_Valid_Till: { type: String },
                    NonTransport: { type: String },
                    Transport: { type: String },
                    CurrentStatus: { type: String },
                    LastTransactionAt: { type: String },
                }
            },
            voterId: {
                ocr: {
                    name: { type: String },
                    fathers_name: { type: String },
                    id_no: { type: String },
                    address: { type: String },
                    gender: { type: String },
                    id_type: { type: String },
                    date_of_issue: { type: String },
                    photoPath: { type: String },
                    locality_or_post_office: { type: String },
                    district_or_city: { type: String },
                    address_information: {
                        address: { type: String },
                        locality_or_post_office: { type: String },
                        district_or_city: { type: String }
                    }
                },
                verificationResponse: {
                    verified: { type: Boolean },
                    result_id_no: { type: Boolean },
                    result_name: { type: Boolean },
                    pc_name: { type: String },
                    st_code: { type: String },
                    ps_lat_long_1_coordinate: { type: String },
                    gender: { type: String },
                    rln_name_v2: { type: String },
                    rln_name_v1: { type: String },
                    rln_name_v3: { type: String },
                    name_v1: { type: String },
                    name_v2: { type: String },
                    name_v3: { type: String },
                    ps_lat_long: { type: String },
                    pc_no: { type: String },
                    last_update: { type: String },
                    id: { type: String },
                    dist_no: { type: String },
                    ps_no: { type: String },
                    ps_name: { type: String },
                    ps_name_v1: { type: String },
                    st_name: { type: String },
                    dist_name: { type: String },
                    rln_type: { type: String },
                    pc_name_v1: { type: String },
                    part_name_v1: { type: String },
                    ac_name_v1: { type: String },
                    part_no: { type: String },
                    dist_name_v1: { type: String },
                    ps_lat_long_0_coordinate: { type: String },
                    _version_: { type: String },
                    name: { type: String },
                    section_no: { type: String },
                    ac_no: { type: String },
                    slno_inpart: { type: String },
                    rln_name: { type: String },
                    age: { type: String },
                    part_name: { type: String },
                    id_no: { type: String }
                }
            },
            passport: {
                ocr: {
                    name: { type: String },
                    id_no: { type: String },
                    dob: { type: String },
                    gender: { type: String },
                    id_type: { type: String },
                    type: { type: String },
                    country_code: { type: String },
                    sur_name: { type: String },
                    nationality: { type: String },
                    place_of_birth: { type: String },
                    place_of_issue: { type: String },
                    photoPath: { type: String },
                }
            },
            uid: {
                details: {
                    docNo: { type: String },
                    document_type: { type: String },
                    id_proof_type: { type: String },
                    gender: { type: String },
                    //image: { type: String },
                    name: { type: String },
                    dob: { type: String },
                    current_address: { type: String },
                    permanent_address: { type: String },
                    current_address_details: {
                        address: { type: String },
                        locality_or_post_office: { type: String },
                        district_or_city: { type: String },
                        state: { type: String },
                        pincode: { type: String }
                    },
                    permanent_address_details: {
                        address: { type: String },
                        locality_or_post_office: { type: String },
                        district_or_city: { type: String },
                        state: { type: String },
                        pincode: { type: String }
                    },
                    //docPath: { type: String },
                    photoPath: { type: String },
                },
                ocr: {
                    name: { type: String },
                    id_no: { type: String },
                    dob: { type: String },
                    gender: { type: String },
                    id_type: { type: String },
                    address: { type: String },
                    photoPath: { type: String },
                    address_information: {
                        address: { type: String },
                        locality_or_post_office: { type: String },
                        district_or_city: { type: String },
                        state: { type: String },
                        pincode: { type: String }
                    }

                }
            }
        },
        errorDetails: {
            api: {
                details: { type: String },
                code: { type: String },
                message: { type: String }
            },
            ocr: {
                details: { type: String },
                code: { type: String },
                message: { type: String }
            }
        }
    }],
    gstDetails: [{
        gstNo: { type: String },
        docDetails: [{
            fileName: { type: String },
            filePath: { type: String },
            position: { type: String },
            isActive: { type: Boolean, default: true },
            insertedOn: { type: Date, default: Date.now, format: "date-time" },
        },],
        insertedOn: { type: Date, default: Date.now, format: "date-time" },
        docVerification: {
            isGstVerified: { type: Boolean },
            gstin: { type: String },
            corporate_name: { type: String },
            details: {
                stj_cd: { type: String },
                dty: { type: String },
                lgnm: { type: String },
                cxdt: { type: String },
                gstin: { type: String },
                nba: { type: String },
                lstupdt: { type: String },
                ctb: { type: String },
                rgdt: { type: String },
            },
            addr: {
                bnm: { type: String },
                loc: { type: String },
                st: { type: String },
                bno: { type: String },
                dst: { type: String },
                stcd: { type: String },
                city: { type: String },
                flno: { type: String },
                lt: { type: String },
                pncd: { type: String },
                lg: { type: String },
            },
            ntr: { type: String },
            ctj_cd: { type: String },
            trade_nam: { type: String },
            sts: { type: String },
            ctj: { type: String },
            createdOn: { type: Date }
        },
        errorDetails: {
            details: { type: String },
            code: { type: String },
            message: { type: String }
        }
    }],
    udyogAadhaar: {
        aadhaarNo: { type: String },
        docDetails: [{
            fileName: { type: String },
            filePath: { type: String },
            position: { type: String },
            isActive: { type: Boolean, default: true },
            insertedOn: { type: Date, default: Date.now, format: "date-time" },
        },],

        addressDetails:{
            area: { type: String },
            city: { type: String },
            district: { type: String },
            door:{ type: String },
            nameOfPremises: { type: String },
            pin: { type: String },
            road: { type: String },
            state: { type: String },
        },
        generalDetails : {
            appliedDate:  { type: Date },
            commencementDate:  { type: Date },
            dicName:  { type: String },
            enterpriseName:  { type: String },
            enterpriseType:  { type: String },
            expiryDate:  { type: Date },
            majorActivity:  { type: String },
            modifiedDate: { type: Date },
            socialCategory: { type: String },
            state: { type: String },
        },
        nicDetails :{
            activityType: { type: String },
            nic2Digit:{ type: String },
            nic4Digit: { type: String },
            nic5Digit: { type: String },
        },
        insertedOn: { type: Date, default: Date.now, format: "date-time" },
        requestId :{type : String},
        lastVerifiedOn : {type : Date},
    },
    udyamAadhaar: {
        aadhaarNo: { type: String },
        // docDetails: [{
        //     fileName: { type: String },
        //     filePath: { type: String },
        //     position: { type: String },
        //     isActive: { type: Boolean, default: true },
        //     insertedOn: { type: Date, default: Date.now, format: "date-time" },
        // },],

        enterpriseType:{
            classificationDate: { type: String },
            classificationYear: { type: String },
            type: { type: String },
        },
        generalDetails : {
            appliedDate: { type: Date },
            commencementDate: { type: Date },
            dateOfInc: { type: Date },
            dicName:{ type: String },
            enterpriseName: { type: String },
            enterpriseType:{ type: String },
            majorActivity: { type: String },
            msmeDi: { type: String },
            organizationType:{ type: String },
            socialCategory:{ type: String },
            state:{ type: String },
        },
        nicDetails :{
            activityType: { type: String },
            date : { type: Date },
            nic2Digit:{ type: String },
            nic4Digit: { type: String },
            nic5Digit: { type: String },
        },
        officialAddress : {
            block:  { type: String },
            city:  { type: String },
            district:  { type: String },
            door:  { type: String },
            email: { type: String },
            mobile: { type: String },
            nameOfPremises:  { type: String },
            pin: { type: String },
            road: { type: String },
            state: { type: String },
            town:  { type: String },
        },
        unitDetails :{
            block:  { type: String },
            building:  { type: String },
            city: { type: String },
            district:  { type: String },
            flat: { type: String },
            pin:  { type: String },
            road:  { type: String },
            state: { type: String },
            unitName: { type: String },
            village: { type: String },
        },
        insertedOn: { type: Date, default: Date.now, format: "date-time" },
        requestId :{type : String},
        lastVerifiedOn : {type : Date},
    },
    additionalDocuments: [{
        docNo: { type: String },
        docType: { type: String },
        docDetails: [{
            fileName: { type: String },
            filePath: { type: String },
            position: { type: String },
            isActive: { type: Boolean, default: true },
            insertedOn: { type: Date, default: Date.now, format: "date-time" },
        }],
        insertedOn: { type: Date, default: Date.now, format: "date-time" },
    }],
    shopDetails: [{
        partner: { type: String },
        merchantCode: { type: String },
        trxnId: { type: String },
        productId: { type: String },
        shopName: { type: String },
        shopLocation: { type: String },
        shopType: { type: String },
        shopVintage: { type: String },
        shopOwnershipType: { type: String },
        shopAddress: { type: String },
        city: { type: String },
        state: { type: String },
        pincode: { type: String },
        companyType: { type: String },
        businessVintage: { type: String },
        contactNo: { type: String },
        insertedOn: { type: Date, default: Date.now, format: "date-time" },
        modifiedOn: { type: Date, format: "date-time" },
        modifiedByPartner: { type: String },
        lastUpdatedBy: { type: Schema.ObjectId },
        lastUpdatedByRole: { type: String },
        shopSector: { type: String },
        natureOfBussiness:  { type: String }
    }],
    shopDetailsUpdatedOn: { type: Date },
    kycStepDetails: [{
        Step: { type: String },
        isCompleted: { type: Boolean, default: true },
        insertedOn: { type: Date, default: Date.now, format: "date-time" },
        completedOn: { type: Date, format: "date-time" },
    }],
    investmentOptions: [{
        consent: { type: String },
        insertedOn: { type: Date, default: Date.now, format: "date-time" },
        amount: [{
            range: { type: String },
            isActive: { type: Boolean, default: true },
            insertedOn: { type: Date, default: Date.now, format: "date-time" },
        },],
        interestRate: [{
            range: { type: String },
            isActive: { type: Boolean, default: true },
            insertedOn: { type: Date, default: Date.now, format: "date-time" },
        },],
        geographic: [{
            state: { type: String },
            isActive: { type: Boolean, default: true },
            insertedOn: { type: Date, default: Date.now, format: "date-time" },
        },],
        employment: [{
            type: { type: String },
            isActive: { type: Boolean, default: true },
            insertedOn: { type: Date, default: Date.now, format: "date-time" },
        },],
        rePaymentFrequency: [{
            type: { type: String },
            isActive: { type: Boolean, default: true },
            insertedOn: { type: Date, default: Date.now, format: "date-time" },
        },],
        loanType: [{
            type: { type: String },
            isActive: { type: Boolean, default: true },
            insertedOn: { type: Date, default: Date.now, format: "date-time" },
        },],
        tenure: [{
            months: { type: Number },
            isActive: { type: Boolean, default: true },
            insertedOn: { type: Date, default: Date.now, format: "date-time" },
        },],
        minInvestment: { type: Number },
        maxInvestment: { type: Number },
    }],
    monthlyTranCount: [{
        year: { type: String },
        partner: { type: String },
        serviceType: { type: String },
        jan: { type: Number },
        feb: { type: Number },
        mar: { type: Number },
        apr: { type: Number },
        may: { type: Number },
        jun: { type: Number },
        jul: { type: Number },
        aug: { type: Number },
        sep: { type: Number },
        oct: { type: Number },
        nov: { type: Number },
        dec: { type: Number },
    }],
    monthlyTranVol: [{
        year: { type: String },
        partner: { type: String },
        serviceType: { type: String },
        jan: { type: Number },
        feb: { type: Number },
        mar: { type: Number },
        apr: { type: Number },
        may: { type: Number },
        jun: { type: Number },
        jul: { type: Number },
        aug: { type: Number },
        sep: { type: Number },
        oct: { type: Number },
        nov: { type: Number },
        dec: { type: Number },
    }],
    monthlyActiveDays: [{
        year: { type: String },
        partner: { type: String },
        serviceType: { type: String },
        jan: { type: Number },
        feb: { type: Number },
        mar: { type: Number },
        apr: { type: Number },
        may: { type: Number },
        jun: { type: Number },
        jul: { type: Number },
        aug: { type: Number },
        sep: { type: Number },
        oct: { type: Number },
        nov: { type: Number },
        dec: { type: Number },
    }],
    totalMonthlyTranCount: [{
        year: { type: String },
        partner: { type: String },
        serviceType: { type: String },
        totalCount: { type: Number },
    }],
    totalMonthlyTranVol: [{
        year: { type: String },
        partner: { type: String },
        serviceType: { type: String },
        totalCount: { type: Number },
    }],
    totalActiveDays: [{
        year: { type: String },
        partner: { type: String },
        serviceType: { type: String },
        totalCount: { type: Number },
    }],
    profileDocDetails: [{
        fileName: { type: String },
        filePath: { type: String },
        isActive: { type: Boolean, default: true },
        insertedOn: { type: Date, default: Date.now, format: "date-time" },
    }],
    ipAddress: { type: String },
    insertedOn: { type: Date, default: Date.now, format: "date-time" },
    modifiedOn: { type: Date },
    tnc: { type: String },
    tncAgreedOn: { type: Date, default: Date.now, format: "date-time" },
    crifScore: { type: Number },
    delinquentAcctsLastSixMonths: { type: Number },
    creditHistoryLength: { type: String },
    crifScoreUpdateDate: { type: Date, format: "date-time" },
    digilocker: {
        execution_request_id: { type: String },
        created_at: { type: Date },
        updated_at: { type: Date },
        reference_id: { type: String },
        status: { type: String },
        processing_done: { type: Boolean },
        face_match_status: { type: String },
        validation_result: {},
        face_match_obj_type: { type: String },
        obj_analysis_status: { type: String }
    },
    issues: [{ type: Object }],
    isKycRecapture: { type: Boolean, default: false },
    kycRecaptureReason: { type: String },
    blockLoginAccess: { type: Boolean, default: false }
});

// ledgerDetails: {
//     virtualAccountNo: { type: String },
//     balance: { type: Number },
//     holdAmount: { type: Number },
//     totalVACredit: { type: Number },
//     payOut: { type: Number },
//     daysInvested: { type: Number },
//     insertedOn: { type: Date, default: Date.now, format: "date-time" },
//     updatedOn: { type: Date, default: Date.now, format: "date-time" },
// }
//Token Generation
userInfoSchema.methods.generateAuthToken = function (data) {
    //console.log(this.userDetails);
    return jwt.sign({ id: this._id, role: this.userDetails.role, ...data }, config.get("webtokens.jsonWebToken"));
};
const UserInfo = mongoose.model("UserInfo", userInfoSchema, "UserInfo");
module.exports.UserInfo = UserInfo;
const openMarketUserInfoSchema =new mongoose.Schema({

    firstName: { type: String },
    middleName: { type: String },
    lastName: { type: String },
    emailId: { type: String },
    mobileNo : { type: String },
    companyName : {type : String},
    address : {type : String},
    pincode : {type : String},
    state : {type : String},
    city: {type : String},
    panNo : {type : String},
    aadhaarNo:  {type : String},
    income : {type : String},
    partnerId : {type : String},
    occupation: {type : String},
    educationQualification: {type : String},
    residenceOwnership: {type : String},
    dob: {type : String},
    referrerId: {type : String},
})
const OpenMarketUserInfo = mongoose.model("openMarketUserInfo", openMarketUserInfoSchema, "openMarketUserInfo");
module.exports.OpenMarketUserInfo = OpenMarketUserInfo;

const userInfoHistorySchema = new mongoose.Schema({
    userId: { type: Schema.ObjectId },
    panNo: { type: String },
    merchantCode: { type: String },
    panDocDetails: [{
        fileName: { type: String },
        filePath: { type: String },
        isActive: { type: Boolean, default: true },
        insertedOn: { type: Date, default: Date.now, format: "date-time" },
    }],
    panDocVerification: {
        isNumberVerified: { type: Boolean },
        isDocVerified: { type: Boolean },
        type: { type: String },
        isDocMatch: { type: Boolean },
        createdOn: { type: Date },
        details: {
            name: { type: String },
            docNo: { type: String },
            dob: { type: String },
            document_type: { type: String },
            id_proof_type: { type: String },
            typeOfHolder: { type: String },
            isIndividual: { type: Boolean }, //Optional
            isValid: { type: Boolean }, //Optional
            firstName: { type: String }, //Optional
            middleName: { type: String }, //Optional
            lastName: { type: String }, //Optional
            title: { type: String }, //Optional
            panStatus: { type: String }, //Optional
            panStatusCode: { type: String }, //Optional
            aadhaarSeedingStatus: { type: String }, //Optional
            aadhaarSeedingStatusCode: { type: String }, //Optional
            lastUpdatedOn: { type: String },
            docPath: { type: String },
            photoPath: { type: String },
            fathers_name: { type: String }
        },
        ocr: {
            name: { type: String },
            fathers_name: { type: String },
            id_no: { type: String },
            dob: { type: String },
            id_type: { type: String },
            signaturePath: { type: String },
            photoPath: { type: String },
        },
        verificationResponse: {
            is_pan_dob_valid: { type: Boolean },
            name_matched: { type: Boolean },
            verified: { type: Boolean },
        },
        errorDetails: {
            api: {
                details: { type: String },
                code: { type: String },
                message: { type: String }
            },
            ocr: {
                details: { type: String },
                code: { type: String },
                message: { type: String }
            }
        }
    },
    userDetails: {
        userName: { type: String },
        password: { type: String },
        role: { type: String },
        firstName: { type: String },
        middleName: { type: String },
        lastName: { type: String },
        emailId: { type: Array },
        mobileNo: { type: Array },
        dob: { type: Date },
        employment: { type: String },
        isEmailValidated: { type: Boolean, default: false },
        isMobileValidated: { type: Boolean, default: false },
        clientCode: { type: String },
        clientCodeGenDate: { type: Date },
        clientCodeGenBy: { type: String },
        kycRemarks: { type: String },
        kycStatus: { type: String },
        passwordModifiedOn: { type: Date, format: "date-time" },
        registeredFrom: { type: String },
        imageUrl: { type: String },
        socialMediaId: { type: String },
        documentConsent: { type: Boolean, default: false },
        docConsentGivenOn: { type: Date, default: null },
        kycApprovedOn: { type: Date, default: null },
        kycUpdatedOn: { type: Date, default: null },
        kycUpdatedBy: { type: Schema.ObjectId },
        kycUpdatedByRole: { type: String },
        adminModifiedDate: { type: Date },
        loan: {
            lastUpdatedByLoanId: { type: Schema.ObjectId },
            lastUpdatedByLoanNo: { type: String },
            lastUpdatedByPartner: { type: String },
            lastUpdatedByPartnerId: { type: Schema.ObjectId },
            updatedOn: { type: Date }
        },
        detailsMismatch: {
            isMismatch: { type: Boolean },
            type: { type: String },
            details: {
                poiName: { type: String },
                poaName: { type: String },
                poiDob: { type: Date },
                poaDob: { type: Date }
            },
            insertedOn: { type: Date },
            modifiedOn: { type: Date }
        }
    },
    addressDetails: [{
        add1: { type: String },
        add2: { type: String },
        add3: { type: String },
        type: { type: String },
        city: { type: String },
        state: { type: String },
        pincode: { type: String },
        country: { type: String },
        insertedOn: { type: Date, default: Date.now, format: "date-time" },
        perAdd1: { type: String },
        perAdd2: { type: String },
        perAdd3: { type: String },
    }],
    questionnairreDetails: {
        phoneConnectionType: { type: String },
        whatsApp: { type: String },
        gender: { type: String },
        maritalStatus: { type: String },
        children: { type: String },
        degree: { type: String },
        monthlyIncome: { type: String },
        monthlyOutgoing: { type: String },
        jobType: { type: String },
        homeOwnershipType: { type: String },
        vehicle: { type: String },
        purchasedInOneYear: { type: String },
        recommended: { type: String },
        recommendedBy: { type: String },
        modifiedOn: { type: Date }
    },
    tnc: { type: String },
    tncAgreedOn: { type: Date, default: Date.now, format: "date-time" },
    profileDocDetails: [{
        fileName: { type: String },
        filePath: { type: String },
        isActive: { type: Boolean, default: true },
        insertedOn: { type: Date, default: Date.now, format: "date-time" },
    }],
    poaDetails: [{
        poaNo: { type: String },
        poaType: { type: String },
        poaVerificationDocType: { type: String },
        isMasked: { type: Boolean, default: false },
        docDetails: [{
            fileName: { type: String },
            filePath: { type: String },
            position: { type: String },
            isActive: { type: Boolean, default: true },
            insertedOn: { type: Date, default: Date.now, format: "date-time" },
            documentPassword: { type: String }
        }],
        insertedOn: { type: Date, default: Date.now, format: "date-time" },
        docVerification: {
            isNumberVerified: { type: Boolean },
            isDocVerified: { type: Boolean },
            type: { type: String },
            verificationType: { type: String },
            isDocMatch: { type: Boolean },
            createdOn: { type: Date },
            dl: {
                ocr: {
                    name: { type: String },
                    id_no: { type: String },
                    dob: { type: String },
                    id_type: { type: String },
                    photoPath: { type: String },
                },
                verificationResponse: {
                    verified: { type: Boolean },
                    Date_Of_Issue: { type: String },
                    NT: { type: String },
                    Old_New_DL_No: { type: String },
                    Holders_Name: { type: String },
                    Hazardous_Valid_Till: { type: String },
                    NonTransport: { type: String },
                    Transport: { type: String },
                    CurrentStatus: { type: String },
                    LastTransactionAt: { type: String },
                }
            },
            voterId: {
                ocr: {
                    name: { type: String },
                    fathers_name: { type: String },
                    id_no: { type: String },
                    address: { type: String },
                    gender: { type: String },
                    id_type: { type: String },
                    date_of_issue: { type: String },
                    photoPath: { type: String },
                    locality_or_post_office: { type: String },
                    district_or_city: { type: String },
                    address_information: {
                        address: { type: String },
                        locality_or_post_office: { type: String },
                        district_or_city: { type: String }
                    }
                },
                verificationResponse: {
                    verified: { type: Boolean },
                    result_id_no: { type: Boolean },
                    result_name: { type: Boolean },
                    pc_name: { type: String },
                    st_code: { type: String },
                    ps_lat_long_1_coordinate: { type: String },
                    gender: { type: String },
                    rln_name_v2: { type: String },
                    rln_name_v1: { type: String },
                    rln_name_v3: { type: String },
                    name_v1: { type: String },
                    name_v2: { type: String },
                    name_v3: { type: String },
                    ps_lat_long: { type: String },
                    pc_no: { type: String },
                    last_update: { type: String },
                    id: { type: String },
                    dist_no: { type: String },
                    ps_no: { type: String },
                    ps_name: { type: String },
                    ps_name_v1: { type: String },
                    st_name: { type: String },
                    dist_name: { type: String },
                    rln_type: { type: String },
                    pc_name_v1: { type: String },
                    part_name_v1: { type: String },
                    ac_name_v1: { type: String },
                    part_no: { type: String },
                    dist_name_v1: { type: String },
                    ps_lat_long_0_coordinate: { type: String },
                    _version_: { type: String },
                    name: { type: String },
                    section_no: { type: String },
                    ac_no: { type: String },
                    slno_inpart: { type: String },
                    rln_name: { type: String },
                    age: { type: String },
                    part_name: { type: String },
                    id_no: { type: String }
                }
            },
            passport: {
                ocr: {
                    name: { type: String },
                    id_no: { type: String },
                    dob: { type: String },
                    gender: { type: String },
                    id_type: { type: String },
                    type: { type: String },
                    country_code: { type: String },
                    sur_name: { type: String },
                    nationality: { type: String },
                    place_of_birth: { type: String },
                    place_of_issue: { type: String },
                    photoPath: { type: String },
                }
            },
            uid: {
                details: {
                    docNo: { type: String },
                    document_type: { type: String },
                    id_proof_type: { type: String },
                    gender: { type: String },
                    image: { type: String },
                    name: { type: String },
                    dob: { type: String },
                    current_address: { type: String },
                    permanent_address: { type: String },
                    current_address_details: {
                        address: { type: String },
                        locality_or_post_office: { type: String },
                        district_or_city: { type: String },
                        state: { type: String },
                        pincode: { type: String }
                    },
                    permanent_address_details: {
                        address: { type: String },
                        locality_or_post_office: { type: String },
                        district_or_city: { type: String },
                        state: { type: String },
                        pincode: { type: String }
                    },
                    docPath: { type: String },
                    photoPath: { type: String },
                },
                ocr: {
                    name: { type: String },
                    id_no: { type: String },
                    dob: { type: String },
                    gender: { type: String },
                    id_type: { type: String },
                    address: { type: String },
                    photoPath: { type: String },
                    address_information: {
                        address: { type: String },
                        locality_or_post_office: { type: String },
                        district_or_city: { type: String },
                        state: { type: String },
                        pincode: { type: String }
                    }

                }
            }
        },
        errorDetails: {
            api: {
                details: { type: String },
                code: { type: String },
                message: { type: String }
            },
            ocr: {
                details: { type: String },
                code: { type: String },
                message: { type: String }
            }
        }
    }],
    gstDetails: [{
        gstNo: { type: String },
        docDetails: [{
            fileName: { type: String },
            filePath: { type: String },
            position: { type: String },
            isActive: { type: Boolean, default: true },
            insertedOn: { type: Date, default: Date.now, format: "date-time" },
        },],
        insertedOn: { type: Date, default: Date.now, format: "date-time" },
        docVerification: {
            isGstVerified: { type: Boolean },
            gstin: { type: String },
            corporate_name: { type: String },
            details: {
                stj_cd: { type: String },
                dty: { type: String },
                lgnm: { type: String },
                cxdt: { type: String },
                gstin: { type: String },
                nba: { type: String },
                lstupdt: { type: String },
                ctb: { type: String },
                rgdt: { type: String },
            },
            addr: {
                bnm: { type: String },
                loc: { type: String },
                st: { type: String },
                bno: { type: String },
                dst: { type: String },
                stcd: { type: String },
                city: { type: String },
                flno: { type: String },
                lt: { type: String },
                pncd: { type: String },
                lg: { type: String },
            },
            ntr: { type: String },
            ctj_cd: { type: String },
            trade_nam: { type: String },
            sts: { type: String },
            ctj: { type: String },
            createdOn: { type: Date }
        },
        errorDetails: {
            details: { type: String },
            code: { type: String },
            message: { type: String }
        }
    }],
    udyogAadhaar: {
        aadhaarNo: { type: String },
        docDetails: [{
            fileName: { type: String },
            filePath: { type: String },
            position: { type: String },
            isActive: { type: Boolean, default: true },
            insertedOn: { type: Date, default: Date.now, format: "date-time" },
        },],
        insertedOn: { type: Date, default: Date.now, format: "date-time" },
    },
    additionalDocuments: [{
        docNo: { type: String },
        docType: { type: String },
        docDetails: [{
            fileName: { type: String },
            filePath: { type: String },
            position: { type: String },
            isActive: { type: Boolean, default: true },
            insertedOn: { type: Date, default: Date.now, format: "date-time" },
        }],
        insertedOn: { type: Date, default: Date.now, format: "date-time" },
    }],
    shopDetails: [{
        partner: { type: String },
        merchantCode: { type: String },
        trxnId: { type: String },
        productId: { type: String },
        shopName: { type: String },
        shopLocation: { type: String },
        shopType: { type: String },
        shopVintage: { type: String },
        shopOwnershipType: { type: String },
        shopAddress: { type: String },
        city: { type: String },
        state: { type: String },
        pincode: { type: String },
        companyType: { type: String },
        businessVintage: { type: String },
        contactNo: { type: String },
        insertedOn: { type: Date, default: Date.now, format: "date-time" },
        modifiedOn: { type: Date, format: "date-time" },
        modifiedByPartner: { type: String },
        lastUpdatedBy: { type: Schema.ObjectId },
        lastUpdatedByRole: { type: String },
        shopSector: { type: String },
        natureOfBussiness:  { type: String }
    }],
    shopDetailsUpdatedOn: { type: Date },
    kycStepDetails: [{
        Step: { type: String },
        isCompleted: { type: Boolean, default: true },
        insertedOn: { type: Date, default: Date.now, format: "date-time" },
        completedOn: { type: Date, format: "date-time" },
    },],
    investmentOptions: [{
        consent: { type: String },
        insertedOn: { type: Date, default: Date.now, format: "date-time" },
        amount: [{
            range: { type: String },
            isActive: { type: Boolean, default: true },
            insertedOn: { type: Date, default: Date.now, format: "date-time" },
        },],
        interestRate: [{
            range: { type: String },
            isActive: { type: Boolean, default: true },
            insertedOn: { type: Date, default: Date.now, format: "date-time" },
        },],
        geographic: [{
            state: { type: String },
            isActive: { type: Boolean, default: true },
            insertedOn: { type: Date, default: Date.now, format: "date-time" },
        },],
        employment: [{
            type: { type: String },
            isActive: { type: Boolean, default: true },
            insertedOn: { type: Date, default: Date.now, format: "date-time" },
        },],
        rePaymentFrequency: [{
            type: { type: String },
            isActive: { type: Boolean, default: true },
            insertedOn: { type: Date, default: Date.now, format: "date-time" },
        },],
        loanType: [{
            type: { type: String },
            isActive: { type: Boolean, default: true },
            insertedOn: { type: Date, default: Date.now, format: "date-time" },
        },],
        tenure: [{
            months: { type: Number },
            isActive: { type: Boolean, default: true },
            insertedOn: { type: Date, default: Date.now, format: "date-time" },
        },],
        minInvestment: { type: Number },
        maxInvestment: { type: Number },
    },],
    monthlyTranCount: [{
        year: { type: String },
        partner: { type: String },
        serviceType: { type: String },
        jan: { type: Number },
        feb: { type: Number },
        mar: { type: Number },
        apr: { type: Number },
        may: { type: Number },
        jun: { type: Number },
        jul: { type: Number },
        aug: { type: Number },
        sep: { type: Number },
        oct: { type: Number },
        nov: { type: Number },
        dec: { type: Number },
    },],
    monthlyTranVol: [{
        year: { type: String },
        partner: { type: String },
        serviceType: { type: String },
        jan: { type: Number },
        feb: { type: Number },
        mar: { type: Number },
        apr: { type: Number },
        may: { type: Number },
        jun: { type: Number },
        jul: { type: Number },
        aug: { type: Number },
        sep: { type: Number },
        oct: { type: Number },
        nov: { type: Number },
        dec: { type: Number },
    },],
    monthlyActiveDays: [{
        year: { type: String },
        partner: { type: String },
        serviceType: { type: String },
        jan: { type: Number },
        feb: { type: Number },
        mar: { type: Number },
        apr: { type: Number },
        may: { type: Number },
        jun: { type: Number },
        jul: { type: Number },
        aug: { type: Number },
        sep: { type: Number },
        oct: { type: Number },
        nov: { type: Number },
        dec: { type: Number },
    },],
    totalMonthlyTranCount: [{
        year: { type: String },
        partner: { type: String },
        serviceType: { type: String },
        totalCount: { type: Number },
    },],
    totalMonthlyTranVol: [{
        year: { type: String },
        partner: { type: String },
        serviceType: { type: String },
        totalCount: { type: Number },
    },],
    totalActiveDays: [{
        year: { type: String },
        partner: { type: String },
        serviceType: { type: String },
        totalCount: { type: Number },
    },],
    profileDocDetails: [{
        fileName: { type: String },
        filePath: { type: String },
        isActive: { type: Boolean, default: true },
        insertedOn: { type: Date, default: Date.now, format: "date-time" },
    },],
    ipAddress: { type: String },
    insertedOn: { type: Date, default: Date.now, format: "date-time" },
    modifiedOn: { type: Date },
    tnc: { type: String },
    tncAgreedOn: { type: Date, default: Date.now, format: "date-time" },
    crifScore: { type: Number },
    delinquentAcctsLastSixMonths: { type: Number },
    creditHistoryLength: { type: String },
    crifScoreUpdateDate: { type: Date, format: "date-time" },
    digilocker: {
        execution_request_id: { type: String },
        created_at: { type: Date },
        updated_at: { type: Date },
        reference_id: { type: String },
        status: { type: String },
        processing_done: { type: Boolean },
        face_match_status: { type: String },
        validation_result: {},
        face_match_obj_type: { type: String },
        obj_analysis_status: { type: String }
    },
    deletedOn: { type: Date, default: Date.now, format: "date-time" },
    issues: [{ type: Object }],
    isKycRecapture: { type: Boolean, default: false },
    kycRecaptureReason: { type: String }
});
const UserInfoHistory = mongoose.model("UserInfoHistory", userInfoHistorySchema, "UserInfoHistory");
module.exports.UserInfoHistory = UserInfoHistory;

const userPartnerInfoSchema = new mongoose.Schema({
    userId: { type: Schema.ObjectId },
    partnerId: { type: Schema.ObjectId },
    merchantCode: { type: String },
    isRetailer: { type: Boolean, default: false },
    isPartnerUser: { type: Boolean, default: false },
    onboardedDate: { type: Date, format: "date-time" },
    reviewLink: { type: String },
    incorrectReviewDocument: { type: String },
    loanHistory: [{
        trxnId: { type: String },
        productId: { type: String },
        partner: { type: String },
        merchantCode: { type: String },
        providerName: { type: String },
        amount: { type: Number },
        outstandingAmount: { type: Number },
        defaultedPayments: { type: Number },
        status: { type: String },
        startDate: { type: Date, format: "date-time" },
        endDate: { type: Date, format: "date-time" },
        insertedOn: { type: Date, default: Date.now, format: "date-time" }
    }],
    // ledgerDetails: {
    //     virtualAccountNo: { type: String },
    //     balance: { type: Number },
    //     holdAmount: { type: Number },
    //     totalVACredit: { type: Number },
    //     payOut: { type: Number },
    //     daysInvested: { type: Number },
    //     insertedOn: { type: Date, default: Date.now, format: "date-time" },
    //     updatedOn: { type: Date, default: Date.now, format: "date-time" },
    // },
    insertedOn: { type: Date, default: Date.now, format: "date-time" },
    updatedOn: { type: Date, format: "date-time" },
    bankDetails: [{
        bankName: { type: String },
        branch: { type: String },
        accountNo: { type: String },
        accountName: { type: String },
        ifsc: { type: String },
        accountType: { type: String },
        address: { type: String },
        city: { type: String },
        state: { type: String },
        docDetails: [{
            fileName: { type: String },
            filePath: { type: String },
            isActive: { type: Boolean },
            insertedOn: { type: Date, format: "date-time" },
            documentPassword: { type: String }
        }],
        insertedOn: { type: Date, default: Date.now, format: "date-time" },
        docVerification: {
            isBankVerified: { type: Boolean },
            id: { type: String },
            verified: { type: Boolean },
            verifiedAt: { type: Date },
            beneficiary_name_with_bank: { type: String },
            createdOn: { type: Date },
            validatedForLoanId: { type: Schema.ObjectId }
        },
        errorDetails: {
            details: { type: String },
            code: { type: String },
            message: { type: String }
        }
    }],
    updateBankLink: { type: String },
    nsfDays: { type: Number, default: 0 },
    nsfDaysUpdatedOn: { type: Date, format: "date-time" }
});
exports.UserPartnerInfo = mongoose.model("UserPartnerInfo", userPartnerInfoSchema, "UserPartnerInfo");

const userPartnerInfoHistorySchema = new mongoose.Schema({
    userPartnerInfoId: { type: Schema.ObjectId },
    userId: { type: Schema.ObjectId },
    partnerId: { type: Schema.ObjectId },
    merchantCode: { type: String },
    isRetailer: { type: Boolean, default: false },
    isPartnerUser: { type: Boolean, default: false },
    onboardedDate: { type: Date, format: "date-time" },
    reviewLink: { type: String },
    incorrectReviewDocument: { type: String },
    loanHistory: [{
        trxnId: { type: String },
        productId: { type: String },
        partner: { type: String },
        merchantCode: { type: String },
        providerName: { type: String },
        amount: { type: Number },
        outstandingAmount: { type: Number },
        defaultedPayments: { type: Number },
        status: { type: String },
        startDate: { type: Date, format: "date-time" },
        endDate: { type: Date, format: "date-time" },
        insertedOn: { type: Date, default: Date.now, format: "date-time" }
    }],
    ledgerDetails: {
        virtualAccountNo: { type: String },
        balance: { type: Number },
        holdAmount: { type: Number },
        totalVACredit: { type: Number },
        payOut: { type: Number },
        daysInvested: { type: Number },
        insertedOn: { type: Date, default: Date.now, format: "date-time" },
        updatedOn: { type: Date, default: Date.now, format: "date-time" },
    },
    insertedOn: { type: Date, default: Date.now, format: "date-time" },
    updatedOn: { type: Date, format: "date-time" },
    bankDetails: [{
        bankName: { type: String },
        branch: { type: String },
        accountNo: { type: String },
        accountName: { type: String },
        ifsc: { type: String },
        accountType: { type: String },
        address: { type: String },
        city: { type: String },
        state: { type: String },
        docDetails: [{
            fileName: { type: String },
            filePath: { type: String },
            isActive: { type: Boolean },
            insertedOn: { type: Date, format: "date-time" },
            documentPassword: { type: String }
        }],
        insertedOn: { type: Date, default: Date.now, format: "date-time" },
        docVerification: {
            isBankVerified: { type: Boolean },
            id: { type: String },
            verified: { type: Boolean },
            verifiedAt: { type: Date },
            beneficiary_name_with_bank: { type: String },
            createdOn: { type: Date },
            validatedForLoanId: { type: Schema.ObjectId }
        },
        errorDetails: {
            details: { type: String },
            code: { type: String },
            message: { type: String }
        }
    }],
    updateBankLink: { type: String },
    nsfDays: { type: Number, default: 0 },
    deletedOn: { type: Date, default: Date.now, format: "date-time" }
});
exports.UserPartnerInfoHistory = mongoose.model("UserPartnerInfoHistory", userPartnerInfoHistorySchema, "UserPartnerInfoHistory");

const kycInfoSchema = new mongoose.Schema({
    userId: { type: Schema.ObjectId },
    existingUserId: { type: Schema.ObjectId },
    panNo: { type: String },
    firstName: { type: String },
    middleName: { type: String },
    lastName: { type: String },
    addressDetails: [{
        add1: { type: String },
        add2: { type: String },
        add3: { type: String },
        type: { type: String },
        city: { type: String },
        state: { type: String },
        pincode: { type: String },
        country: { type: String },
        insertedOn: { type: Date, default: Date.now, format: "date-time" },
        perAdd1: { type: String },
        perAdd2: { type: String },
        perAdd3: { type: String },
    }],
    panDocDetails: [{
        fileName: { type: String },
        filePath: { type: String },
        isActive: { type: Boolean, default: true },
        insertedOn: { type: Date, default: Date.now, format: "date-time" },
    }],
    panDocVerification: {
        isNumberVerified: { type: Boolean },
        isDocVerified: { type: Boolean },
        type: { type: String },
        isDocMatch: { type: Boolean },
        createdOn: { type: Date },
        details: {
            name: { type: String },
            docNo: { type: String },
            dob: { type: String },
            document_type: { type: String },
            id_proof_type: { type: String },
            typeOfHolder: { type: String },
            isIndividual: { type: Boolean }, //Optional
            isValid: { type: Boolean }, //Optional
            firstName: { type: String }, //Optional
            middleName: { type: String }, //Optional
            lastName: { type: String }, //Optional
            title: { type: String }, //Optional
            panStatus: { type: String }, //Optional
            panStatusCode: { type: String }, //Optional
            aadhaarSeedingStatus: { type: String }, //Optional
            aadhaarSeedingStatusCode: { type: String }, //Optional
            lastUpdatedOn: { type: String },
            //docPath: { type: String },
            photoPath: { type: String },
            fathers_name: { type: String }
        },
        ocr: {
            name: { type: String },
            fathers_name: { type: String },
            id_no: { type: String },
            dob: { type: String },
            id_type: { type: String },
            signaturePath: { type: String },
            photoPath: { type: String },
        },
        verificationResponse: {
            is_pan_dob_valid: { type: Boolean },
            name_matched: { type: Boolean },
            verified: { type: Boolean },
        },
        errorDetails: {
            api: {
                details: { type: String },
                code: { type: String },
                message: { type: String }
            },
            ocr: {
                details: { type: String },
                code: { type: String },
                message: { type: String }
            }
        }
    },
    poaDetails: [{
        poaNo: { type: String },
        poaType: { type: String },
        poaVerificationDocType: { type: String },
        isMasked: { type: Boolean, default: false },
        docDetails: [{
            fileName: { type: String },
            filePath: { type: String },
            position: { type: String },
            isActive: { type: Boolean, default: true },
            insertedOn: { type: Date, default: Date.now, format: "date-time" },
            documentPassword: { type: String }
        }],
        insertedOn: { type: Date, default: Date.now, format: "date-time" },
        docVerification: {
            isNumberVerified: { type: Boolean },
            isDocVerified: { type: Boolean },
            type: { type: String },
            verificationType: { type: String },
            isDocMatch: { type: Boolean },
            createdOn: { type: Date },
            dl: {
                ocr: {
                    name: { type: String },
                    id_no: { type: String },
                    dob: { type: String },
                    id_type: { type: String },
                    photoPath: { type: String },
                },
                verificationResponse: {
                    verified: { type: Boolean },
                    Date_Of_Issue: { type: String },
                    NT: { type: String },
                    Old_New_DL_No: { type: String },
                    Holders_Name: { type: String },
                    Hazardous_Valid_Till: { type: String },
                    NonTransport: { type: String },
                    Transport: { type: String },
                    CurrentStatus: { type: String },
                    LastTransactionAt: { type: String },
                }
            },
            voterId: {
                ocr: {
                    name: { type: String },
                    fathers_name: { type: String },
                    id_no: { type: String },
                    address: { type: String },
                    gender: { type: String },
                    id_type: { type: String },
                    date_of_issue: { type: String },
                    photoPath: { type: String },
                    locality_or_post_office: { type: String },
                    district_or_city: { type: String },
                    address_information: {
                        address: { type: String },
                        locality_or_post_office: { type: String },
                        district_or_city: { type: String }
                    }
                },
                verificationResponse: {
                    verified: { type: Boolean },
                    result_id_no: { type: Boolean },
                    result_name: { type: Boolean },
                    pc_name: { type: String },
                    st_code: { type: String },
                    ps_lat_long_1_coordinate: { type: String },
                    gender: { type: String },
                    rln_name_v2: { type: String },
                    rln_name_v1: { type: String },
                    rln_name_v3: { type: String },
                    name_v1: { type: String },
                    name_v2: { type: String },
                    name_v3: { type: String },
                    ps_lat_long: { type: String },
                    pc_no: { type: String },
                    last_update: { type: String },
                    id: { type: String },
                    dist_no: { type: String },
                    ps_no: { type: String },
                    ps_name: { type: String },
                    ps_name_v1: { type: String },
                    st_name: { type: String },
                    dist_name: { type: String },
                    rln_type: { type: String },
                    pc_name_v1: { type: String },
                    part_name_v1: { type: String },
                    ac_name_v1: { type: String },
                    part_no: { type: String },
                    dist_name_v1: { type: String },
                    ps_lat_long_0_coordinate: { type: String },
                    _version_: { type: String },
                    name: { type: String },
                    section_no: { type: String },
                    ac_no: { type: String },
                    slno_inpart: { type: String },
                    rln_name: { type: String },
                    age: { type: String },
                    part_name: { type: String },
                    id_no: { type: String }
                }
            },
            passport: {
                ocr: {
                    name: { type: String },
                    id_no: { type: String },
                    dob: { type: String },
                    gender: { type: String },
                    id_type: { type: String },
                    type: { type: String },
                    country_code: { type: String },
                    sur_name: { type: String },
                    nationality: { type: String },
                    place_of_birth: { type: String },
                    place_of_issue: { type: String },
                    photoPath: { type: String },
                }
            },
            uid: {
                details: {
                    docNo: { type: String },
                    document_type: { type: String },
                    id_proof_type: { type: String },
                    gender: { type: String },
                    //image: { type: String },
                    name: { type: String },
                    dob: { type: String },
                    current_address: { type: String },
                    permanent_address: { type: String },
                    current_address_details: {
                        address: { type: String },
                        locality_or_post_office: { type: String },
                        district_or_city: { type: String },
                        state: { type: String },
                        pincode: { type: String }
                    },
                    permanent_address_details: {
                        address: { type: String },
                        locality_or_post_office: { type: String },
                        district_or_city: { type: String },
                        state: { type: String },
                        pincode: { type: String }
                    },
                    //docPath: { type: String },
                    photoPath: { type: String },
                },
                ocr: {
                    name: { type: String },
                    id_no: { type: String },
                    dob: { type: String },
                    gender: { type: String },
                    id_type: { type: String },
                    address: { type: String },
                    photoPath: { type: String },
                    address_information: {
                        address: { type: String },
                        locality_or_post_office: { type: String },
                        district_or_city: { type: String },
                        state: { type: String },
                        pincode: { type: String }
                    }

                }
            }
        },
        errorDetails: {
            api: {
                details: { type: String },
                code: { type: String },
                message: { type: String }
            },
            ocr: {
                details: { type: String },
                code: { type: String },
                message: { type: String }
            }
        }
    }],
    isActive: { type: Boolean },
    insertedOn: { type: Date, default: Date.now, format: "date-time" },
    updatedOn: { type: Date }
});
exports.KYCInfo = mongoose.model("KYCInfo", kycInfoSchema, "KYCInfo");


const missingPincodesSchema = new mongoose.Schema({
    partnerId: { type: Schema.ObjectId },
    merchantCode: { type: String },
    city: { type: String },
    state: { type: String },
    pincode: { type: String },
    insertedOn: { type: Date, default: Date.now, format: "date-time" },
    updatedOn: { type: Date, default: Date.now, format: "date-time" }
});
exports.MissingPincodes = mongoose.model("MissingPincodes", missingPincodesSchema, "MissingPincodes");

function validateUser(model) {
    const user = joi.object({
        userName: joi.string().min(5).max(255).required(),
        firstName: joi.string().min(2).max(100).required(),
        middleName: joi.string().max(100).allow("").optional(),
        lastName: joi.string().min(1).max(100).required(),
        mobileNo: joi.string().min(10).required(),
        emailId: joi.string().min(5).max(255).required().email(),
        password: joi.string().min(5).max(255).required(), //.passwordComplexity(complexityOptions).validate()
        partnerName: joi.string().required(),
        ipAddress: joi.string().required(),
        dob: joi.string(),
        otp: joi.string().required()
    });

    return user.validate(model);
};
module.exports.validate = validateUser;

function validateQuestionnairre(model) {
    const user = joi.object({
        whatsApp: joi.string().max(30).allow("").optional(),
        gender: joi.string().max(30).allow("").optional(),
        maritalStatus: joi.string().max(30).allow("").optional(),
        children: joi.string().max(30).allow("").optional(),
        degree: joi.string().max(30).allow("").optional(),
        monthlyIncome: joi.string().max(30).allow("").optional(),
        monthlyOutgoing: joi.string().max(30).allow("").optional(),
        jobType: joi.string().max(30).allow("").optional(),
        homeOwnershipType: joi.string().max(30).allow("").optional(),
        vehicle: joi.string().max(30).allow("").optional(),
        purchasedInOneYear: joi.string().max(30).allow("").optional(),
        recommended: joi.string().optional(),
        recommendedBy: joi.string().max(30).allow("").optional(),
        loanId: joi.string().optional()
    });

    return user.validate(model);
};
module.exports.validateQuestionnairre = validateQuestionnairre;

function validateKYC(model) {
    const user = joi.object({
        firstName: joi.string().max(50).required(),
        middleName: joi.string().max(50).allow("").optional(),
        lastName: joi.string().max(50).required(),
        alternateEmail: joi.string().max(100).email().allow("").optional(),
        alternateMobile: joi.string().max(20).allow("").optional(),
        employment: joi.string().max(100).required(),
        panNo: joi.string().max(20).required(),
        addressDetails: joi.array().items(
            joi.object({
                add1: joi.string().max(100).required(),
                add2: joi.string().max(100).allow().optional(),
                add3: joi.string().max(100).required(),
                type: joi.string().max(50).required(),
                city: joi.string().max(50).required(),
                state: joi.string().max(50).required(),
                pincode: joi.string().max(50).required(),
                country: joi.string().max(50).required(),
                perAdd1: joi.string().max(100).required(),
                perAdd2: joi.string().max(100).allow().optional(),
                perAdd3: joi.string().max(100).required(),
            })
        ),
        panDocDetails: joi
            .array()
            .items(
                joi.object({
                    fileName: joi.string().max(100).required(),
                    imageStream: joi.string().required(),
                })
            )
            .required(),
        poaDetails: joi
            .array()
            .items(
                joi.object({
                    poaNo: joi.string().max(100).required(),
                    poaType: joi.string().max(100).required(),
                    docDetails: joi.array.items(
                        joi.object({
                            fileName: joi.string().max(100).required(),
                            imageStream: joi.string().required(),
                        })
                    ),
                })
            )
            .required(),
        gstDetails: joi
            .array()
            .items(
                joi.object({
                    gstNo: joi.string().max(100).required(),
                    docDetails: joi.array.items(
                        joi.object({
                            fileName: joi.string().max(100).required(),
                            imageStream: joi.string().required(),
                        })
                    ),
                })
            )
            .required(),
        bankDetails: joi
            .array()
            .items(
                joi.object({
                    accountNo: joi.string().max(100).required(),
                    ifsc: joi.string().max(100).required(),
                    accountType: joi.string().max(100).required(),
                    docDetails: joi.array.items(
                        joi.object({
                            fileName: joi.string().max(100).required(),
                            imageStream: joi.string().required(),
                        })
                    ),
                })
            )
            .required(),
        additionalDocuments: joi
            .array()
            .items(
                joi.object({
                    docNo: joi.string().max(100),
                    docType: joi.string().max(100),
                    docDetails: joi.array.items(
                        joi.object({
                            fileName: joi.string().max(100).required(),
                            imageStream: joi.string().required(),
                        })
                    ),
                })
            )
            .optional(),
    });

    return user.validate(model);
};
module.exports.validateKYC = validateKYC;

function validateKYCStatus(model) {
    const user = joi.object({
        kycStatus: joi.string().min(1).max(100).required(),
        remarks: joi.string().max(100).allow("").optional(),
        poaDocType: joi.string().max(100).allow("").optional()
    });

    return user.validate(model);
};
module.exports.validateKYCStatus = validateKYCStatus;

const clientCodeRepositorySchema = new mongoose.Schema({
    panNumber: { type: String },
    clientCode: { type: String },
    prefix: { type: String },
    code: { type: Number },
    insertedOn: { type: Date, default: Date.now, format: "date-time" },
});

module.exports.ClientCodeRepository = mongoose.model(
    "ClientCodeRepository",
    clientCodeRepositorySchema,
    "ClientCodeRepository"
);

function validatePassword(model) {
    const user = joi.object({
        password: joi.string().min(5).max(255).required(),
        otp: joi.string().required(),
        value: joi.string().required()
    });

    return user.validate(model);
};
module.exports.validatePassword = validatePassword;

const payinpayoutSchema = new mongoose.Schema({
    //userId: Schema.ObjectId,
    partnerId: Schema.ObjectId,
    type: { type: String },
    amount: { type: Number },
    utrNo: { type: String },
    transactionDate: { type: Date, default: Date.now, format: "date-time" },
    depositDate: { type: Date, default: Date.now, format: "date-time" },
    insertedOn: { type: Date, default: Date.now, format: "date-time" },
    isActive: { type: Boolean, default: true },
    borrowingPartnerId: Schema.ObjectId
});

exports.PayInPayOut = mongoose.model("PayInPayOut", payinpayoutSchema, "PayInPayOut");

module.exports.validateSavePayInPayOut = function (model) {
    const payinpayout = joi.object({
        partnerId: joi.string().min(1).max(255).required(),
        type: joi.string().required(),
        amount: joi.number().required().min(0),
        utrNo: joi.string().required(),
        transactionDate: joi.string().required(),
        depositDate: joi.string().required(),
    });

    return payinpayout.validate(model);
};

module.exports.validateSavePayOut = function (model) {
    const payinpayout = joi.object({
        partnerId: joi.string().min(1).max(255).required(),
        amount: joi.number().required(),
        utrNo: joi.string().required(),
        depositDate: joi.string().required(),
        borrowingPartnerId: joi.string().required()
    });

    return payinpayout.validate(model);
};

module.exports.validatePayin = function (model) {
    const payinpayout = joi.object({
        partnerId: joi.string().required(),
        id: joi.string().required(),
    });

    return payinpayout.validate(model);
};

module.exports.validateRejectReasonList = function (model) {
    const rejectReasonList = joi.object({
        type: joi.string().required()
    });

    return rejectReasonList.validate(model);
};

const tempCreditScoreSchema = new mongoose.Schema({
    userId: { type: String },
    creditScore: { type: Number },
    delinquentAcctsLastSixMonths: { type: Number },
    creditHistoryLength: { type: String },
    updateDate: { type: String }
});

exports.Temp_CreditScore = mongoose.model("Temp_CreditScore", tempCreditScoreSchema, "Temp_CreditScore");


const EngagementSessionSchema = new mongoose.Schema({
    userId: { type: String },
    "sessionToken": { type: String },
    "expiryTime": { type: Date },
    "isLogout": { type: Boolean },
    "meta": { type: Object },
    "insertedOn": { type: Date },
    "modifiedOn": { type: Date },
})

exports.EngagementSession = mongoose.model("EngagementSession", EngagementSessionSchema, "EngagementSession");