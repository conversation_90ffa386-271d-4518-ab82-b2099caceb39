const mongoose = require("mongoose");
const joi = require("@hapi/joi");
const jwt = require("jsonwebtoken");
const config = require("config");
const Schema = mongoose.Schema;


const OtpValidation = new mongoose.Schema({
  notifyTo: { type: String },
  notificationType: { type: String },
  channel: { type: String },
  otp: { type: String },
  otpValidOn: { type: Date, format: "date-time" },
  notifiedCount: { type: Number },
  lastNotifiedOn: { type: Date, format: "date-time" },
  validatedCount: { type: Number },
  lastValidatedOn: { type: Date, format: "date-time" },
  insertedOn: { type: Date, format: "date-time" },
  modifiedOn: { type: Date, format: "date-time" }
});

module.exports.OtpValidation = mongoose.model("OtpValidation", OtpValidation, "OtpValidation");


const OtpValidationHistory = new mongoose.Schema({
  otpValidationId: { type: Schema.ObjectId },
  notifyTo: { type: String },
  notificationType: { type: String },
  channel: { type: String },
  otp: { type: String },
  otpValidOn: { type: Date, format: "date-time" },
  notifiedCount: { type: Number },
  lastNotifiedOn: { type: Date, format: "date-time" },
  validatedCount: { type: Number },
  lastValidatedOn: { type: Date, format: "date-time" },
  insertedOn: { type: Date, format: "date-time" },
  modifiedOn: { type: Date, format: "date-time" }
});

module.exports.OtpValidationHistory = mongoose.model("OtpValidationHistory", OtpValidationHistory, "OtpValidationHistory");

