const mongoose = require('mongoose');

const invoiceFinancingNotificationSchema = new mongoose.Schema({
    // User identification
    userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'InvoiceFinancingUsers'
    },
    adminId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'InvoiceFinancingUsers'
    },
    recipientType: {
        type: String,
        // enum: ['user', 'anchorAdmin', 'lenderAdmin']
    },

    // Notification content
    title: String,
    message: String,
    type: {
        type: String,
        // enum: [
        //     'KYC_UPDATE',
        //     'INVOICE_UPLOADED',
        //     'OFFER_RECEIVED',
        //     'LOAN_DISBURSED',
        //     'PAYMENT_UPDATE',
        //     'DOCUMENT_UPLOAD',
        //     'CREDIT_LINE_UPDATE',
        //     'GENERAL_UPDATE',
        //     'NEW_APPLICATION',
        //     'DOCUMENT_PENDING',
        //     'PAYMENT_OVERDUE',
        //     'APPLICATION_STATUS'
        // ]
    },

    // Reference data
    referenceType: {
        type: String,
        // enum: [
        //     'kyc',
        //     'invoice',
        //     'offer',
        //     'loan',
        //     'payment',
        //     'document',
        //     'creditLine'
        // ]
    },
    referenceId: mongoose.Schema.Types.ObjectId,

    // Status
    isRead: {
        type: Boolean,
        default: false
    },
    isArchived: {
        type: Boolean,
        default: false
    },

    // Email tracking
    emailSent: {
        type: Boolean,
        default: false
    },
    emailSentAt: Date,

    // Navigation
    actionUrl: String,

    // Timestamps
    createdAt: {
        type: Date,
        default: Date.now
    },
    readAt: Date,
    archivedAt: Date,
    dataClassificationType: {
        type: String,
        enum: ['internal', 'restricted',],
        default: 'internal'
    }
});

// Indexes
invoiceFinancingNotificationSchema.index({ userId: 1, isRead: 1 });
invoiceFinancingNotificationSchema.index({ adminId: 1, isRead: 1 });
invoiceFinancingNotificationSchema.index({ createdAt: -1 });

const InvoiceFinancingNotifications = mongoose.model('InvoiceFinancingNotifications', invoiceFinancingNotificationSchema);

module.exports = InvoiceFinancingNotifications;