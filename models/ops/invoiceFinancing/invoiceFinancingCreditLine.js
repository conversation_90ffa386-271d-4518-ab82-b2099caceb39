const mongoose = require('mongoose');

// Schema for credit line review history
const creditLineReviewSchema = new mongoose.Schema({
    reviewDate: {
        type: Date,
        default: Date.now
    },
    reviewedBy: {
        type: String,
        required: true
    },
    status: {
        type: String,
        enum: [
            'DRAFT',
            'ON_HOLD',
            'UNDER_REVIEW',
            'APPROVED',
            'REJECTED',
            'ACTIVE',
            'SUSPENDED',
            'EXPIRED'
        ], default: 'UNDER_REVIEW'
    },
    deactivationCategory: {
        type: String,
        enum: [
            'Delinquent',       // customer behind payment schedule
            'Fraud',            // potential fraud investigated or determined
            'Customer Requested'// mostly as a result of consent withdrawal
        ],
    },
    creditRiskAssessment: {
        type: String,
        enum: ['LOW', 'MEDIUM', 'HIGH'],
    },
    rejectionReasons: [{
        type: String
    }],
    notes: {
        type: String
    },
    dataClassificationType: {
        type: String,
        enum: ['internal', 'restricted',],
        default: 'internal'
    }
}, {
    timestamps: true
});

// Updated credit line schema
const invoiceFinancingCreditLineSchema = new mongoose.Schema({
    // Unique Identifiers
    creditLineId: {
        type: String,
        required: true,
        unique: true,
        trim: true
    },
    userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    borrowerId: {
        type: String,
        required: true,
        trim: true
    },

    offerAccepted: { type: Boolean, required: true, default: false },

    // Financial Parameters
    currency: {
        type: String,
        default: 'QAR',
        enum: ['QAR', 'USD', 'EUR']
    },
    creditLimit: {
        type: Number,
        required: true
    },
    availableBalance: {
        type: Number,
        default: function () { return this.creditLimit; }
    },
    utilizedAmount: {
        type: Number,
        default: 0
    },

    // Credit Line Status and Review
    creditLineStatus: {
        type: String,
        enum: [
            'DRAFT',
            'ON_HOLD',
            'UNDER_REVIEW',
            'APPROVED',
            'REJECTED',
            'ACTIVE',
            'SUSPENDED',
            'EXPIRED'
        ],
        default: 'DRAFT'
    },
    deactivationCategory: {
        type: String,
        enum: [
            'Delinquent',
            'Fraud',
            'Customer Requested'
        ],
    },
    reviewHistory: [creditLineReviewSchema],

    // Financial Terms
    tenure: {
        type: Number, // in days
        required: true
    },
    interestRate: {
        type: Number,
        required: true
    },
    commitmentFeePercentage: {
        type: Number,
        default: 0.5 // 0.5% of unused balance
    },
    processingFeeType: { type: String, default: "flat" },
    processingFee: {
        type: Number,
        required: true
    },
    latePaymentFeePercentage: {
        type: Number,
        default: 2 // 2% of overdue amount
    },

    // --- NEW: Lender Configuration Snapshot ---
    lenderInvoiceStalePeriodDays: {
        type: Number, // Stale period (in days) for invoices, configured by the lender.
        default: 30
    },
    offerVisibilityWindowHours: {
        type: Number, // Time (in hours) after the last offer is received before any offer is visible.
        default: 72
    },
    // --- END: Lender Configuration Snapshot ---

    // Additional Metadata
    applicationDate: {
        type: Date,
        default: Date.now
    },
    approvalDate: {
        type: Date
    },
    expiryDate: {
        type: Date
    },

    // Detailed Financial Tracking
    totalDisbursed: { type: Number, default: 0 },
    totalRepaid: { type: Number, default: 0 },
    totalInterestCharged: { type: Number, default: 0 },
    totalCommitmentFees: { type: Number, default: 0 },
    totalLatePaymentFees: { type: Number, default: 0 },

    // Compliance and Risk
    riskProfile: {
        type: String,
        enum: ['LOW', 'MEDIUM', 'HIGH']
    },
    complianceNotes: { type: String },

    // New Credit Line Assignment Field
    creditLineAssignedBy: {
        type: String,
        enum: ['lenderAdmin', 'superAdmin'],
        default: null
    },
    lenderId: {
        type: mongoose.Schema.Types.ObjectId
    },
    dataClassificationType: {
        type: String,
        enum: ['internal', 'restricted',],
        default: 'internal'
    }
}, {
    timestamps: true
});

// Indexes for performance
invoiceFinancingCreditLineSchema.index({ userId: 1 });
invoiceFinancingCreditLineSchema.index({ creditLineId: 1 });
invoiceFinancingCreditLineSchema.index({ creditLineStatus: 1 });

// Pre-save middleware
invoiceFinancingCreditLineSchema.pre('save', function (next) {
    if (this.isModified('creditLimit')) {
        this.availableBalance = this.creditLimit;
    }
    next();
});

const InvoiceFinancingCreditLine = mongoose.model('InvoiceFinancingCreditLine', invoiceFinancingCreditLineSchema);

module.exports = { InvoiceFinancingCreditLine };