const mongoose = require("mongoose");
const Schema = mongoose.Schema;

/**
 * @description Defines the structure for an individual field change.
 * This will be part of an array within each audit log entry.
 */
const ChangedValueSchema = new Schema({
    fieldPath: {
        type: String,
        trim: true,
        description: "Dot-notation path to the changed field (e.g., 'email', 'kyc.addressLine1', 'shareholders.0.firstName'). For array additions/deletions, this might indicate the array path and index."
    },
    previousValue: {
        type: Schema.Types.Mixed,
        description: "The value of the field before the change. Can be null/undefined for new creations or new array elements."
    },
    newValue: {
        type: Schema.Types.Mixed,
        description: "The value of the field after the change. Can be null/undefined for deletions."
    },
    changeType: {
        type: String,
        // enum: ['MODIFIED', 'ADDED', 'REMOVED'],
        default: 'MODIFIED',
        description: "Type of change, particularly useful for array elements or whole sub-documents."
    },
    dataClassificationType: {
        type: String,
        enum: ['internal', 'restricted',],
        default: 'internal'
    }
}, { _id: false });

/**
 * @description Main schema for an audit log entry.
 * Each document in the AuditLogs collection will follow this structure.
 */
const AuditLogSchema = new Schema({
    timestamp: {
        type: Date,
        default: Date.now,
        index: true,
        description: "Timestamp of when the action occurred."
    },
    actor: { // Renamed from userId for clarity, as actor can be user, admin, or system
        actorId: {
            type: String,
            refPath: 'actor.actorModel',
            index: true,
            description: "The _id of the user, admin, or system entity that performed the action."
        },
        actorModel: {
            type: String,
            // enum: ['InvoiceFinancingUsers', 'Admin', 'System'], 
            description: "The Mongoose model name for the actor (e.g., 'InvoiceFinancingUsers', 'Admin', 'System')."
        },
        actorIdentifier: { // e.g., email or username, for easier readability in logs if actorId is an ObjectId
            type: String,
            trim: true
        }
    },
    actionType: {
        type: String,
        trim: true,
        index: true,
        description: "Categorizes the action (e.g., USER_CREATE, USER_UPDATE_PROFILE, KYC_DOCUMENT_UPLOAD, SHAREHOLDER_ADD, BANK_STATEMENT_VERIFY, USER_LOGIN). Be specific."
    },
    actionDescription: {
        type: String,
        trim: true,
        description: "A human-readable summary of the action performed."
    },
    target: {
        documentId: {
            type: String,
            index: true,
            description: "The _id of the primary MongoDB document that was affected (e.g., an InvoiceFinancingUsers document's _id)."
        },
        documentType: {
            type: String,
            trim: true,
            description: "The Mongoose model name of the affected document (e.g., 'InvoiceFinancingUsers')."
        },
        subDocumentPath: { // e.g., "kyc.passport", "shareholders"
            type: String,
            trim: true,
            description: "Path to the sub-document or array within the target document if the change is nested. E.g., 'kyc.passport' or 'shareholders'."
        },
        subDocumentId: { // The _id of an element within an array (e.g., a specific shareholder's _id)
            type: String,
            index: true,
            description: "The _id of the specific sub-document array element that was affected (e.g., a shareholder's _id from the 'shareholders' array)."
        }
    },
    changes: [ChangedValueSchema], // Array of specific field changes
    requestDetails: {
        ipAddress: { type: String, trim: true },
        userAgent: { type: String, trim: true },
        apiEndpoint: { type: String, trim: true }, // API endpoint that triggered the action
        correlationId: { type: String, trim: true, index: true } // Useful for tracing a request through multiple services/logs
    },
    status: {
        type: String,
        // enum: ['SUCCESS', 'FAILURE', 'PENDING_ACTION'],
        default: 'SUCCESS',
        description: "Status of the logged action (e.g., if the action itself succeeded or failed)."
    },
    failureReason: {
        type: String,
        trim: true,
        description: "If the action status is 'FAILURE', this field can store the reason."
    },
    notes: { // General notes or comments about the audit entry
        type: String,
        trim: true
    },
    version: { // Optional: If your main documents are versioned, you can link to the version before/after
        before: { type: Schema.Types.Mixed },
        after: { type: Schema.Types.Mixed }
    },
    additionalMeta: { // For any other contextual information not covered by other fields
        type: Schema.Types.Mixed
    },
    dataClassificationType: {
        type: String,
        enum: ['internal', 'restricted',],
        default: 'internal'
    }
}, {
    timestamps: { createdAt: 'loggedAt', updatedAt: false }, // 'loggedAt' for when the audit entry was created
    collection: 'AuditLogs' // Explicitly name the collection
});

// Compound Indexes for common query patterns
AuditLogSchema.index({ "actor.actorId": 1, "actor.actorModel": 1, timestamp: -1 });
AuditLogSchema.index({ actionType: 1, timestamp: -1 });
AuditLogSchema.index({ "target.documentId": 1, "target.documentType": 1, timestamp: -1 });
AuditLogSchema.index({ "target.subDocumentId": 1, timestamp: -1 }); // If querying for specific sub-document changes often

module.exports = mongoose.model("InvoiceFinancingAuditLog", AuditLogSchema);