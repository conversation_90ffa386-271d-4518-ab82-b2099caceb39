const mongoose = require('mongoose');

// DocumentSchema remains the same
const DocumentSchema = new mongoose.Schema({
    filePath: { type: String },
    signedUrl: { type: String },
    uploadedOn: { type: Date, default: Date.now },
    expiresOn: { type: Date },
    mimeType: { type: String },
    dataClassificationType: {
        type: String,
        enum: ['internal', 'restricted',],
        default: 'internal'
    }
}, { _id: false });

// Sub-schema for Disbursement Information remains the same
const DisbursementInfoSchema = new mongoose.Schema({
    utr: { type: String, trim: true },
    disbursedOn: { type: Date },
    disbursedAmount: { type: Number },
    uploadedOn: { type: Date, default: Date.now },
    dataClassificationType: {
        type: String,
        enum: ['internal', 'restricted',],
        default: 'internal'
    }
}, { _id: false });

// --- Updated Offers Schema ---
const invoiceFinancingOffersSchema = new mongoose.Schema({
    merchantId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'InvoiceFinancingUsers',
        required: true,
        index: true
    },
    invoiceId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'InvoiceFinancingInvoices',
        index: true
    },
    offerType: {
        type: String,
        enum: ['creditLineOffer', 'invoiceDiscountingOffer'],
        required: true
    },
    lenderId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'InvoiceFinancingUsers',
        required: true,
        index: true
    },
    invoiceDiscountingPercentage: { type: Number },
    creditLimit: {
        type: Number,
        required: true
    },
    emiDetails: [
        {
            emiNumber: { type: Number},
            interestAmount: { type: Number },
            principalRecovered: { type: Number },
            principalOutstanding: { type: Number },
            rePaymentAmount: { type: Number },
            rePaymentDate: { type: Date },
            rePaymentStatus: {
                type: String,
                enum: ['PENDING', 'PAID', 'PARTIAL', 'OVERDUE', 'WAIVED'],
                default: 'PENDING'
            },
            paidAmount: { type: Number, default: 0 },
            rePaymentActualDate: { type: Date },
            rePaymentReceivedDate: { type: Date },
            paymentUTR: { type: String, trim: true },
            penalty: { type: Number, default: 0 },
            moratoriumInterest: { type: Number, default: 0 },
            updatedOn: { type: Date },
            insertedOn: { type: Date, default: Date.now },
            isPrincipalPaid: { type: Boolean, default: false },
            principalPaidOn: { type: Date },
            holdCollection: { type: Boolean, default: false }
        }
    ],
    tenureDays: {
        type: Number,
        required: true
    },
    processingFee: {
        type: {
            type: String,
            enum: ['percentage', 'flat'],
            required: true
        },
        value: {
            type: Number,
            required: true
        },
        _id: false
    },
    invoiceContract: { type: DocumentSchema },
    facilityContract: { type: DocumentSchema },
    otherCharges: {
        type: Number,
        default: 0
    },
    emiRepaymentFrequency: { type: String, enum: ['DAILY', 'WEEKLY', 'MONTHLY'] },
    interestRate: { type: String },
    status: {
        type: String,
        enum: [
            'PENDING',
            'ACCEPTED',
            'LOAN_CONTRACT_ACCEPTED',
            'INITIATED_FUND_TRANSFER',
            'READY_FOR_DISBURSAL',
            'LOAN_IN_PROGRESS',
            'PAID',
            'LOAN_CANCELLED',
            'REJECTED',
            'EXPIRED',
            'WRITTEN_OFF',
            'DEFAULTED',
            'OVERDUE'
        ],
        default: 'PENDING',
        index: true
    },
    expiryDate: {
        type: Date // Offer timeout date. Will be set automatically for credit line offers.
    },
    visibleAt: {
        type: Date // Timestamp when the offer should become visible to the MSME.
    },
    responseDate: { type: Date },
    responseComments: { type: String },
    uploadedAt: { type: Date, default: Date.now },
    contractDetails: {
        type: mongoose.Schema.Types.Mixed
    },
    facilityDetails: {
        type: mongoose.Schema.Types.Mixed
    },
    disbursementInfo: { type: DisbursementInfoSchema },
    dataClassificationType: {
        type: String,
        enum: ['internal', 'restricted',],
        default: 'internal'
    }
}, {
    timestamps: true
});

// Middleware to set default expiry for new credit line offers
invoiceFinancingOffersSchema.pre('save', function(next) {
    // If the document is new and is a 'creditLineOffer', set the expiry date
    if (this.isNew && this.offerType === 'creditLineOffer') {
        const FOUR_DAYS_IN_MS = 4 * 24 * 60 * 60 * 1000;
        this.expiryDate = new Date(Date.now() + FOUR_DAYS_IN_MS);
    }
    next();
});


// Indexes
invoiceFinancingOffersSchema.index({ merchantId: 1 });
invoiceFinancingOffersSchema.index({ invoiceId: 1 });
invoiceFinancingOffersSchema.index({ status: 1 });
invoiceFinancingOffersSchema.index({ lenderId: 1 });

const InvoiceFinancingOffers = mongoose.model('InvoiceFinancingOffers', invoiceFinancingOffersSchema);

module.exports = InvoiceFinancingOffers;