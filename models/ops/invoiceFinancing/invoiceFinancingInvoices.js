const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const DocumentSchema = new Schema({
    filePath: { type: String },  // Permanent storage path
    signedUrl: { type: String }, // Temporary signed URL for access
    uploadedOn: { type: Date, default: Date.now },
    expiresOn: { type: Date },   // When the signed URL expires
    mimeType: { type: String },
    dataClassificationType: {
        type: String,
        enum: ['internal', 'restricted'],
        default: 'internal'
    }
    // Document type (PDF, image, etc.)
});

// MODIFICATION: Added schema options to include virtuals in output
const invoiceFinancingInvoicesSchema = new mongoose.Schema({
    userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    panNo: {
        type: String,
        trim: true
    },
    gstin: {
        type: String,
        trim: true
    },
    invoiceNumber: {
        type: String,
        required: true,
        trim: true
    },
    invoiceDate: {
        type: String, // Kept as String for backward compatibility
        required: true,
        trim: true
    },
    dueDate: {
        type: String, // Kept as String for backward compatibility
        trim: true
    },
    totalAmount: {
        type: String,
        required: true,
        trim: true
    },
    supplierName: {
        type: String,
        required: true,
        trim: true
    },
    customerName: {
        type: String,
        required: true,
        trim: true
    },
    billingAddress: {
        type: String,
        trim: true
    },
    customerAddress: {
        type: String,
        trim: true
    },
    s3Url: {
        type: String,
        required: true
    },
    signedUrl: {
        type: String
    },
    gcsUrl: {
        type: String
    },
    uploadedAt: {
        type: Date,
        default: Date.now
    },
    isEligible: {
        type: Boolean,
        default: true
    },
    totalTaxAmount: {
        type: Number, // Or String
        trim: true,
    },
    supplierPhone: {
        type: String,
        trim: true,
    },
    supplierEmail: {
        type: String,
        trim: true,
    },
    supplierWebsite: {
        type: String,
        trim: true,
    },
    invoiceType: {
        type: String,
        trim: true,
    },
    lineItems: [{ // Array of objects for line items
        description: { type: String, trim: true },
        amount: { type: String, trim: true }, // Or Number
        quantity: { type: String, trim: true }, // Or Number
        unit_price: { type: String, trim: true }  // Or Number
        // Add other relevant line item fields here
    }],
    status: {
        type: String,
        enum: [
            'APPOVAL_PENDING',
            'VERIFICATION_PENDING_ANCHOR',
            'VERIFIED_ANCHOR',
            'VERIFICATION_PENDING_LENDER',
            'ACCEPTED_LENDER',
            'MORE_INFO_NEEDED_ANCHOR',
            'MORE_INFO_NEEDED_LENDER',
            'REJECTED_ANCHOR',
            'REJECTED_LENDER',
            'READY_FOR_DISBURSAL',
            'LOAN_IN_PROGRESS',
            'DISBURSED',
            'LOAN_CANCELLED',
            'WRITTEN_OFF_PAID'
        ],
        default: 'APPOVAL_PENDING'
    },
    appliedForDiscountingAt: {
        type: Date
    },
    additionalInvoiceDocuments: {
        type: [DocumentSchema], // Array of DocumentSchema
        validate: [
            {
                validator: function (arr) {
                    return arr.length <= 10; // Limit the array size to 10
                },
                message: 'Cannot upload more than 10 additional documents per invoice.'
            }
        ],
        default: [] // Default to an empty array
    },
    verificationComments: {
        type: String
    },
    verifiedBy: {
        type: String
    },
    verifiedAt: {
        type: Date
    },
    documentValidated: {
        type: Boolean,
        default: false
    },
    extractedData: {
        type: mongoose.Schema.Types.Mixed,
        default: null
    },
    dataClassificationType: {
        type: String,
        enum: ['internal', 'restricted',],
        default: 'internal'
    },
    invoicePhysicalSignContract: { type: DocumentSchema }
}, {
    // ADDED: This ensures virtual fields are included in API responses
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});

// --- NEW VIRTUAL PROPERTY ---
// ADDED: Calculates invoiceAge dynamically without storing it in the DB.
// This is safe for production as it doesn't change stored data.
invoiceFinancingInvoicesSchema.virtual('invoiceAge').get(function() {
    // 'this' refers to the document instance
    if (!this.invoiceDate) {
        return null;
    }

    // Attempt to create a Date object from the string.
    // This relies on the string being in a format the JS Date constructor
    // can parse reliably (e.g., "YYYY-MM-DD").
    const invoiceD = new Date(this.invoiceDate);

    // Production-safe: Check if the date string was parsed successfully.
    // If format is invalid, returns null instead of crashing the app.
    if (isNaN(invoiceD.getTime())) {
        console.error(`Could not parse invalid date string for invoice: ${this._id}`);
        return null;
    }

    const now = new Date();
    // Calculate the difference in milliseconds
    const differenceInTime = now.getTime() - invoiceD.getTime();
    
    // Convert milliseconds to days and return the whole number
    const differenceInDays = Math.floor(differenceInTime / (1000 * 3600 * 24));
    
    return differenceInDays;
});

// Add indexes for common queries
invoiceFinancingInvoicesSchema.index({ userId: 1, status: 1 });
invoiceFinancingInvoicesSchema.index({ gstin: 1 });
invoiceFinancingInvoicesSchema.index({ invoiceNumber: 1 });

const InvoiceFinancingInvoices = mongoose.model('InvoiceFinancingInvoices', invoiceFinancingInvoicesSchema);

module.exports = InvoiceFinancingInvoices;