const mongoose = require('mongoose');

const SuperAdminSchema = new mongoose.Schema({
  username: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    minlength: 3,
    maxlength: 50,
  },
  email: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true,
    match: /^[^\s@]+@[^\s@]+\.[a-z]{2,3}$/, // Improved email validation
  },
  lenderName: {
    type: String,
    trim: true,
  },
  logoUrl: {
    type: String,
    trim: true,
  },
  password: {
    type: String,
    required: true,
    minlength: 8,
  },
  role: {
    type: String,
    enum: ['superadmin', 'buyerAdmin', 'lenderAdmin', 'complianceOfficer'],
    default: 'superadmin',
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
  lastLogin: {
    type: Date,
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  // New Lender-Specific Fields
  lenderType: {
    type: String,
    enum: ['bank', 'nbfc', 'fintech', 'other'],
    default: 'other',
  },
  financialInstitutionLicense: {
    type: String,
    trim: true,
  },
  contactPersonName: {
    type: String,
    trim: true,
  },
  contactPersonPhone: {
    type: String,
    trim: true,
  },
  contactPersonEmail: {
    type: String,
    trim: true,
    lowercase: true,
    match: /^[^\s@]+@[^\s@]+\.[a-z]{2,3}$/,
  },
  websiteUrl: {
    type: String,
    trim: true,
  },
  address: {
    type: String,
    trim: true,
  },
  dataClassificationType: {
    type: String,
    enum: ['internal', 'restricted'],
    default: 'internal'
  },
  invoiceStalePeriodDays: {
    type: Number,
    min: 0,
    default: 30,
  },
}, {
  timestamps: true,
});

module.exports = mongoose.model('SuperAdmin', SuperAdminSchema);