const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const setDate = (val) => {
    // Return null if the value is falsy (e.g., null, undefined, '')
    if (!val) {
        return null;
    }

    // If it's already a valid Date object, return it directly
    if (val instanceof Date && !isNaN(val)) {
        return val;
    }

    // If it's a string, attempt to parse it.
    // This example assumes 'MM/DD/YYYY'. Adjust format as needed.
    if (typeof val === 'string') {
        // new Date() with specific parameters is more reliable than parsing a string directly
        const parts = val.split('/');
        if (parts.length === 3) {
            // Note: Month is 0-indexed in JavaScript's Date (0-11)
            const [month, day, year] = parts.map(Number);
            return new Date(year, month - 1, day);
        }
    }

    // Return null for any other invalid format
    return null;
};

const ConsentDetailsSchema = new Schema({
    version: { type: String, required: true },
    agreedOn: { type: Date, required: true, default: Date.now }
}, { _id: false });

const BusinessDetailsSchema = new Schema({
    // Core Business Info
    businessAge: {
        type: String,
        enum: ['> 2 years', '< 2 years'],
        // No 'required: false' needed, absence implies not required unless explicitly set.
    },
    crValidity: {
        type: String,
        enum: ['> 2 months', '< 2 months'],
    },
    companyType: {
        type: String,
        trim: true
    },
    // SECTOR IS DUPLICATED - once above companyType, once below businessType. Remove one.
    // Let's assume the one above companyType was intended.
    sector: { // This was the first 'sector' field
        type: String,
        trim: true
    },
    turnover: {
        type: String,
        trim: true
    },
    employees: {
        type: String,
        trim: true
    },
    legalEntityName: { type: String, trim: true },
    crNumber: { type: String, trim: true },
    crIssueDate: { type: Date, set: setDate },
    crExpiryDate: { type: Date, set: setDate },
    legalForm: { type: String, trim: true },
    taxRegNo: { type: String, trim: true },
    tinNumber: { type: String, trim: true },
    branchCount: { type: Number },
    firmNationality: { type: String, trim: true },
    isQatarBased: { type: Boolean, default: true },

    // Establishment Info
    establishmentId: { type: String, trim: true },
    establishmentIdIssueDate: { type: Date },
    establishmentIdExpiryDate: { type: Date },
    establishmentName: { type: String, trim: true },

    // Trade License Dates
    tlIssueDate: { type: Date, set: setDate },
    tlExpiryDate: { type: Date, set: setDate },

    // Business Type/Sector (This 'sector' is a duplicate, decide which one to keep or rename)
    businessName: { type: String, trim: true },
    businessType: { type: String, trim: true },
    // sector: { type: String, trim: true }, // This is the second 'sector' field - DUPLICATE
    ownershipType: { type: String, trim: true },
    natureOfBusiness: { type: String, trim: true },
    yearEstablished: { type: String },
    monthlyTurnover: { type: Number },

    businessAddressVerification: { // This seems fine as an embedded object, not a separate schema unless very complex
        status: {
            type: String,
            enum: ['INITIATED', 'PENDING', 'VERIFIED', 'REJECTED', 'SKIPPED'],
            default: 'PENDING'
        },
        shuftiReferenceId: {
            type: String,
            trim: true,
            index: true
        },
        shuftiCallbackData: {
            type: Schema.Types.Mixed
        },
        verifiedOn: {
            type: Date
        },
        verificationNotes: {
            type: String,
            trim: true
        },
        verifiedBy: {
            type: Schema.Types.ObjectId,
            ref: 'Admin'
        }
    },

    // Business Address
    businessAddressLine1: { type: String, trim: true },
    businessAddressLine2: { type: String, trim: true },
    businessCity: { type: String, trim: true },
    businessCountry: { type: String, trim: true },
    businessZone: { type: String, trim: true },
    businessStreetNo: { type: String, trim: true },
    businessBuildingNo: { type: String, trim: true },
    dataClassificationType: {
        type: String,
        enum: ['internal', 'restricted',],
        default: 'internal'
    }
}, { _id: false }); // Use _id: false if you don't want an _id for this sub-document

// Define a structure for documents that need signed URLs
const DocumentSchema = new Schema({
    filePath: { type: String },     // Permanent storage path
    signedUrl: { type: String },    // Temporary signed URL for access
    uploadedOn: { type: Date, default: Date.now },
    expiresOn: { type: Date },      // When the signed URL expires
    mimeType: { type: String },     // Document type (PDF, image, etc.)
    shuftiReferenceId: {             // Reference ID sent to/received from Shufti Pro
        type: String,
        trim: true,
        index: true // Optional: Index if you query by this often
    },
    shuftiCallbackData: {
        type: Schema.Types.Mixed // Allows storing any JSON structure
    },
    extractedFields: { type: Schema.Types.Mixed },
    extractionStatus: {
        type: String,
        enum: ['NOT_ATTEMPTED', 'SUCCESS', 'FAILED', 'PENDING', 'SKIPPED'],
        default: 'NOT_ATTEMPTED'
    },
    // Verification fields for each document
    verificationStatus: {
        type: String,
        enum: ['SUBMITTED', 'VERIFIED', 'REJECTED', 'PENDING', 'SKIPPED'],
        default: 'SUBMITTED'
    },
    verificationNotes: {
        type: String,
        trim: true
    },
    verifiedOrRejectedOn: {
        type: Date
    },
    verifiedOrRejectedBy: {
        type: Schema.Types.ObjectId,
        ref: 'Admin' // Assuming an Admin model exists
    },
    isRequested: {
        type: Boolean,
        default: false // Default to false, meaning not requested
    },
    requestedOn: {
        type: Date, // Timestamp when the document was requested
        default: null // Null until requested
    },
    dataClassificationType: {
        type: String,
        enum: ['internal', 'restricted',],
        default: 'internal'
    }
}, { _id: false });

// Address structure for Shareholders
const ShareholderAddressSchema = new Schema({
    zone: { type: String, trim: true },
    streetNo: { type: String, trim: true },
    buildingNo: { type: String, trim: true },
    floorNo: { type: String, trim: true },
    unitNo: { type: String, trim: true },
    additionalLandmark: { type: String, trim: true },
    dataClassificationType: {
        type: String,
        enum: ['internal', 'restricted',],
        default: 'internal'
    }
}, { _id: false });

// Sub-Schema for Individual Shareholder
const ShareholderSchema = new Schema({
    firstName: { type: String, trim: true },
    middleName: { type: String, trim: true },
    lastName: { type: String, trim: true },
    kycVerificationStatus: { type: String, default: "INITIATED", enum: ["INITIATED", "REINITIATED", "UNDER_REVIEW", "APPROVED", "REJECTED", "REVIEW", "INFO_NEEDED", "PENDING"] }, // Existing field, kept for backward compatibility
    verificationNotes: { type: String }, // Existing field
    email: { type: String, trim: true },
    address: { type: ShareholderAddressSchema },
    passport: { type: DocumentSchema },
    qid: { type: DocumentSchema },
    shuftiReferenceId: {           // Existing field: Reference ID sent to/received from Shufti Pro (can be used for initial/main request)
        type: String,
        trim: true,
        index: true
    },
    proofOfAddress: { type: DocumentSchema },

    // --- NEW Shufti Pro KYC Fields for Shareholder ---
    amlStatus: {
        type: String,
        enum: ['NOT_CHECKED', 'PENDING', 'CLEAR', 'HIT', 'ERROR', 'REJECTED', 'SKIPPED'],
        default: 'NOT_CHECKED',
        index: true
    },
    sanctionsStatus: {
        type: String,
        enum: ['NOT_CHECKED', 'PASSED', 'HIT', 'ERROR'],
        default: 'NOT_CHECKED',
        index: true
    },

    // Warnings and Regulatory Enforcement Filter
    warningsRegulatoryStatus: {
        type: String,
        enum: ['NOT_CHECKED', 'PASSED', 'HIT', 'ERROR'],
        default: 'NOT_CHECKED',
        index: true
    },

    // Fitness and Probity Filter
    fitnessProbityStatus: {
        type: String,
        enum: ['NOT_CHECKED', 'PASSED', 'HIT', 'ERROR'],
        default: 'NOT_CHECKED',
        index: true
    },

    // PEP (Politically Exposed Person) Filters
    pepOverallStatus: { // General PEP status, 'HIT' if any level is a hit
        type: String,
        enum: ['NOT_CHECKED', 'PASSED', 'HIT', 'ERROR'],
        default: 'NOT_CHECKED',
        index: true
    },
    pepLevel1Status: {
        type: String,
        enum: ['NOT_CHECKED', 'PASSED', 'HIT', 'ERROR'],
        default: 'NOT_CHECKED'
    },
    pepLevel2Status: {
        type: String,
        enum: ['NOT_CHECKED', 'PASSED', 'HIT', 'ERROR'],
        default: 'NOT_CHECKED'
    },
    pepLevel3Status: {
        type: String,
        enum: ['NOT_CHECKED', 'PASSED', 'HIT', 'ERROR'],
        default: 'NOT_CHECKED'
    },
    pepLevel4Status: {
        type: String,
        enum: ['NOT_CHECKED', 'PASSED', 'HIT', 'ERROR'],
        default: 'NOT_CHECKED'
    },
    amlNotes: { // Notes specific to AML check
        type: String,
        trim: true
    },
    amlLastChecked: { // Timestamp of when the AML check was completed/updated
        type: Date
    },
    amlShuftiReferenceId: { // Optional: If AML check is a separate Shufti Pro transaction
        type: String,
        trim: true,
        index: true
    },
    amlShuftiCallbackData: { // Optional: Store raw callback for AML if separate
        type: Schema.Types.Mixed
    },
    kycExpirationDate: {
        type: Date
        // Default value will be set by the pre-save hook
    },
    kycEmailSent: {
        type: Boolean,
        default: false // Safe default for existing records
    },
    kycEmailSentOn: {
        type: Date,
        default: null // null is safe for existing records
    },
    videoKyc: { // Using a nested object for video KYC details
        status: {
            type: String,
            enum: ['NOT_ATTEMPTED', 'INITIATED', 'PENDING_REVIEW', 'APPROVED', 'REJECTED', 'FAILED_ATTEMPT', 'EXPIRED', 'SKIPPED'],
            default: 'NOT_ATTEMPTED',
            index: true
        },
        shuftiReferenceId: { // Unique ID for the specific video KYC session
            type: String,
            trim: true,
            index: true
        },
        initiatedOn: { type: Date },
        completedOn: { type: Date },
        shuftiCallbackData: { type: Schema.Types.Mixed }, // Raw callback data for video KYC
        declineReason: { type: String, trim: true }, // Reason if rejected/failed
        reviewNotes: { type: String, trim: true }, // Internal notes for video KYC review
        recordingUrl: { type: String, trim: true } // Link to video recording if provided
    },

    addressVerification: { // Using a nested object for address verification details
        status: {
            type: String,
            enum: ['NOT_ATTEMPTED', 'INITIATED', 'PENDING', 'VERIFIED', 'REJECTED', 'SKIPPED', 'ERROR'],
            default: 'NOT_ATTEMPTED',
            index: true
        },
        shuftiReferenceId: { // Unique ID for address verification process
            type: String,
            trim: true,
            index: true
        },
        shuftiCallbackData: { type: Schema.Types.Mixed }, // Raw callback data for address verification
        verifiedOn: { type: Date }, // Timestamp of verification completion
        verificationNotes: { type: String, trim: true }, // Notes for address verification (can be distinct from the root verificationNotes)
        rejectionReason: { type: String, trim: true } // Reason if address verification rejected
    },

    documentKycStatus: { // This serves as the Shufti-specific "KYC status" for identity documents
        type: String,
        enum: ['NOT_ATTEMPTED', 'SUBMITTED', 'PENDING_REVIEW', 'VERIFIED', 'REJECTED', 'INFO_NEEDED', 'EXPIRED', 'SKIPPED', 'ERROR', 'INITIATED'],
        default: 'NOT_ATTEMPTED',
        index: true
    },
    documentKycNotes: { // Notes related to document verification (e.g. clarity issues, specific document rejection reason)
        type: String,
        trim: true
    },
    documentKycShuftiReferenceId: { // Optional: If document KYC is a distinct Shufti Pro transaction from the main shuftiReferenceId
        type: String,
        trim: true,
        index: true
    },
    documentKycShuftiCallbackData: { // Optional: Store raw callback for document KYC if separate
        type: Schema.Types.Mixed
    },
    dataClassificationType: {
        type: String,
        enum: ['internal', 'restricted',],
        default: 'internal'
    }
}, {
    timestamps: { createdAt: 'addedOn', updatedAt: 'modifiedOn' }
});

// Main User Schema
const InvoiceFinancingUserSchema = new mongoose.Schema({
    // --- Basic User Information ---
    uniqueId: {
        type: String,
        unique: true,
        index: true,
        default: function () {
            const alphanumeric = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
            let uniqueId = '';
            for (let i = 0; i < 6; i++) {
                uniqueId += alphanumeric.charAt(Math.floor(Math.random() * alphanumeric.length));
            }
            return uniqueId;
        }
    },
    firstName: { type: String },
    middleName: { type: String },
    lastName: { type: String },
    password: { type: String }, // Consider hashing/security best practices separately
    isActive: { type: String, default: "ACTIVE" },
    email: {
        type: String,
        index: { // Define a partial unique index
            unique: true,
            partialFilterExpression: { email: { $type: "string" } }
        },
        set: v => {
            if (v === '' || v === undefined || v === null) {
                return null; // Normalize to null
            }
            return v.trim().toLowerCase(); // Trim and convert to lowercase for consistency
        },
        default: null
    },

    mobileNo: {
        type: String,
        index: { // Define a partial unique index
            unique: true,
            partialFilterExpression: { mobileNo: { $type: "string" } }
        },
        set: v => {
            if (v === '' || v === undefined || v === null) {
                return null; // Normalize to null
            }
            // Consider any specific formatting/cleaning needed for mobile numbers, e.g., removing spaces or symbols
            return v.trim();
        },
        default: null
    },
    googleId: { type: String }, // For Google Sign-In

    // --- Root level identifiers sometimes shown with Business Details ---
    licenseNumber: { type: String }, // Trade License Number

    // --- KYC Information ---
    kyc: {
        // --- Personal Identification & Details ---
        qatariId: { type: DocumentSchema }, // Document upload for QID
        qidNumber: { type: String, trim: true }, // ADDED: The actual QID number
        qidExpiryDate: { type: Date }, // ADDED: QID Expiry
        passport: { type: DocumentSchema }, // Document upload for Passport
        dateOfBirth: { type: Date },
        gender: { type: String, default: "M", enum: ["M", "F", "Other"] },
        nationality: { type: String, trim: true }, // ADDED: Personal Nationality

        isBeneficialOwner: { type: Boolean },

        addressLine1: { type: String, trim: true },
        addressLine2: { type: String, trim: true },
        city: { type: String, trim: true },
        state: { type: String, trim: true }, // Or Province
        country: { type: String, trim: true },
        postalCode: { type: String, trim: true },
        utilityBill: { type: DocumentSchema }, // Proof of address document
        kycExpirationDate: { type: Date },

        applicationStatus: {
            registration: {
                status: {
                    type: String,
                    enum: ['Pending', 'Submitted', 'Completed', 'RequiresChanges'],
                    default: 'Pending'
                },
                submittedOn: { type: Date }
            },
            creditEvaluation: {
                status: {
                    type: String,
                    enum: ['Locked', 'Pending', 'InProgress', 'Submitted', 'Completed', 'RequiresChanges'],
                    default: 'Locked'
                },
                submittedOn: { type: Date }
            },
            creditLine: {
                status: {
                    type: String,
                    enum: ['Locked', 'Pending', 'InProgress', 'Submitted', 'Completed', 'RequiresChanges'],
                    default: 'Locked'
                },
                submittedOn: { type: Date }
            }
        },

        // --- Employment and Income ---
        employmentDetails: {
            employerName: { type: String, trim: true },
            position: { type: String, trim: true },
            workId: { type: String, trim: true }, // E.g., Employee ID
            employmentLetter: { type: DocumentSchema }
        },
        incomeDetails: {
            monthlyIncome: { type: Number },
            monthlyExpenses: { type: Number },
            proofOfIncome: { type: DocumentSchema },
            sourceOfWealth: { type: String, trim: true },
            ifscCode: { type: String, trim: true }, // Specific to India? Adapt if needed.
            accountNumber: { type: String, trim: true }
        },
        shuftiAmlScreening: {
            type: Schema.Types.Mixed // Allows storing any JSON structure
        },
        amlStatus: {
            type: String,
            enum: ['NOT_CHECKED', 'PENDING', 'CLEAR', 'HIT', 'ERROR', 'REJECTED', 'SKIPPED'], // Possible statuses
            default: 'NOT_CHECKED',
            index: true // Good to index for querying users with hits/pending checks
        },
        sanctionsStatus: {
            type: String,
            enum: ['NOT_CHECKED', 'PASSED', 'HIT', 'ERROR'],
            default: 'NOT_CHECKED',
            index: true
        },

        // Warnings and Regulatory Enforcement Filter
        warningsRegulatoryStatus: {
            type: String,
            enum: ['NOT_CHECKED', 'PASSED', 'HIT', 'ERROR'],
            default: 'NOT_CHECKED',
            index: true
        },

        // Fitness and Probity Filter
        fitnessProbityStatus: {
            type: String,
            enum: ['NOT_CHECKED', 'PASSED', 'HIT', 'ERROR'],
            default: 'NOT_CHECKED',
            index: true
        },

        // PEP (Politically Exposed Person) Filters
        pepOverallStatus: { // General PEP status, 'HIT' if any level is a hit
            type: String,
            enum: ['NOT_CHECKED', 'PASSED', 'HIT', 'ERROR'],
            default: 'NOT_CHECKED',
            index: true
        },
        pepLevel1Status: {
            type: String,
            enum: ['NOT_CHECKED', 'PASSED', 'HIT', 'ERROR'],
            default: 'NOT_CHECKED'
        },
        pepLevel2Status: {
            type: String,
            enum: ['NOT_CHECKED', 'PASSED', 'HIT', 'ERROR'],
            default: 'NOT_CHECKED'
        },
        pepLevel3Status: {
            type: String,
            enum: ['NOT_CHECKED', 'PASSED', 'HIT', 'ERROR'],
            default: 'NOT_CHECKED'
        },
        pepLevel4Status: {
            type: String,
            enum: ['NOT_CHECKED', 'PASSED', 'HIT', 'ERROR'],
            default: 'NOT_CHECKED'
        },
        amlNotes: { // To store summary, e.g., "PEP Match Found", "Sanctions List Hit", or error details
            type: String,
            trim: true
        },
        amlLastChecked: { // Timestamp of when the check was completed/updated
            type: Date
        },
        agreedToInformationAccuracyOn: { type: Date },
        termsAndConditionsConsent: { type: ConsentDetailsSchema },
        privacyPolicyConsent: { type: ConsentDetailsSchema },
        financialDataConsent: { type: ConsentDetailsSchema },
        maritalStatus: { type: String, trim: true }, // Consider enum: ['Single', 'Married', ...]
        hasChildren: { type: Boolean },
        homeOwnership: { type: String, trim: true }, // Consider enum: ['Owned', 'Rented', ...]
        vehicleOwnership: { type: Boolean },

        // --- Education & Background ---
        education: { type: String, trim: true }, // Consider enum or standard values
        sectorExperience: { type: String, trim: true }, // E.g., "5+ years in Retail"

        videoKyc: {
            status: {
                type: String,
                enum: ['NOT_ATTEMPTED', 'INITIATED', 'PENDING_REVIEW', 'APPROVED', 'REJECTED', 'FAILED_ATTEMPT', 'EXPIRED'],
                default: 'NOT_ATTEMPTED',
                index: true // Good for finding users needing video KYC
            },
            shuftiReferenceId: { // Unique ID for the specific video KYC session
                type: String,
                trim: true,
                index: true
            },
            initiatedOn: { // Timestamp when the process was started (e.g., modal opened/API call made)
                type: Date
            },
            completedOn: { // Timestamp when Shufti Pro finalized the verification (approved/rejected)
                type: Date
            },
            shuftiCallbackData: { // Store the raw callback data from Shufti Pro
                type: Schema.Types.Mixed
            },
            declineReason: { // Store reason text/code if rejected or failed
                type: String,
                trim: true
            },
            reviewNotes: { // Internal notes specifically for the video KYC review
                type: String,
                trim: true
            },
            reviewedBy: { // Reference to an internal admin/reviewer
                type: Schema.Types.ObjectId,
                ref: 'Admin' // Assuming you have an Admin model
            },
            recordingUrl: { // Optional: If Shufti Pro provides a link to the recording (check their callback data)
                type: String,
                trim: true
            }
        },

        // --- Business Details (for Business Users/Applicants) ---
        businessDetails: BusinessDetailsSchema,

        riskRating: {
            type: String,
            enum: ['Very High', 'High', 'Medium', 'Low'],
            index: true // Recommended for faster queries based on severity.
        },

        // --- Overall KYC Verification ---
        verificationStatus: { type: String, default: "INITIATED", enum: ["INITIATED", "REINITIATED", "UNDER_REVIEW", "APPROVED", "REJECTED", "REVIEW", "INFO_NEEDED"] },
        verifiedOn: { type: Date },
        verificationNotes: { type: String, trim: true }, // General notes for overall KYC status

        // --- Buyers Array ---
        buyers: [{
            registrationEmailSent: {
                type: Boolean,
                default: false // Safe default for existing records
            },
            registrationEmailSentOn: {
                type: Date,
                default: null // null is safe for existing records
            },
            buyerName: { type: String, required: true, trim: true }, // Added trim
            buyerAddress: { type: String, trim: true },
            contactPerson: { type: String, trim: true },
            contactPhone: { type: String, trim: true },
            contactEmail: { type: String, trim: true, lowercase: true }, // Added lowercase
            businessType: { type: String, trim: true },
            registrationNumber: { type: String, trim: true },
            annualPurchaseVolume: { type: Number },
            paymentTerms: { type: String, trim: true },
        }],

        // --- Directors Array ---
        directors: [{
            directorName: { type: String, required: true, trim: true },
            directorAddress: { type: String, trim: true },
            contactPerson: { type: String, trim: true }, // Usually directorName, but maybe different contact
            contactPhone: { type: String, trim: true },
            contactEmail: { type: String, trim: true, lowercase: true },
            isUBO: { type: Boolean, default: false }, // Ultimate Beneficial Owner flag
            nationalId: { type: String, trim: true }, // Or Passport number
            nationality: { type: String, trim: true },
            dateOfBirth: { type: Date },
            idDocument: { type: DocumentSchema }, // Uploaded ID doc for director
            position: { type: String, trim: true }, // e.g., CEO, Managing Director
            shareholdingPercentage: { type: Number },
            isAuthorizedSignatory: { type: Boolean, default: false }
        }],

        // --- Additional Preferences ---
        hasWhatsapp: { type: Boolean, default: false },
        recentMajorPurchases: { type: Boolean, default: false },

        // --- Bank Statement Reference (if primary one is stored under KYC) ---
        bankStatement: { type: DocumentSchema }, // Moved from root for logical grouping? Check original intent.

    }, // End of kyc object

    // --- Business Registration Documents (Root Level) ---
    commercialRegistration: { type: DocumentSchema },
    tradeLicense: { type: DocumentSchema },
    taxCard: { type: DocumentSchema },
    establishmentCard: { type: DocumentSchema },
    memorandumOfAssociation: { type: DocumentSchema },
    articleOfAssociation: { type: DocumentSchema },
    commercialCreditReport: { type: DocumentSchema }, // Often called CCR
    auditedFinancialReport: { type: DocumentSchema },
    cashFlow: { type: DocumentSchema }, // Cash Flow Statement/Projection
    bankStatement: { type: DocumentSchema }, // Duplicated here and under kyc? Consolidate or clarify intent. Let's keep one at root for now.
    ccrDocument: { type: DocumentSchema }, // Another CCR field? Or specific upload slot? Consolidate if possible.
    cashFlowDocument: { type: DocumentSchema }, // Another CashFlow field? Consolidate.
    statementOfAccount: { type: DocumentSchema },
    otherDocument: { type: DocumentSchema },
    otherDocumentTwo: { type: DocumentSchema },
    otherDocument3: { type: DocumentSchema },
    otherDocument4: { type: DocumentSchema },
    otherDocument5: { type: DocumentSchema },
    otherDocument6: { type: DocumentSchema },
    otherDocument7: { type: DocumentSchema },   //this was the easiest way to add extra documents. Initially
    otherDocument8: { type: DocumentSchema },   //we only have 2 docs which were supposed to be renamed
    otherDocument9: { type: DocumentSchema },   //requirements changed midway, not enough time to change existing implementation. otherDocument and otherDocumentTwo
    otherDocument10: { type: DocumentSchema },  // are used everywhere, so this was the easiest way. If this is to be converted into an array, lots of changes are needed in the frontend
    invoiceAdditionalDoc1: { type: DocumentSchema },
    invoiceAdditionalDoc2: { type: DocumentSchema },
    cashFlowLedger: { type: DocumentSchema },
    additionalCashFlowLedgers: [DocumentSchema], // This will store documents 2 through 15.
    camFile: { type: DocumentSchema },
    signedCreditLineContract: { type: DocumentSchema },

    // --- Associated Parties ---
    shareholders: [ShareholderSchema],
    authorizedSignatories: [{
        name: { type: String, trim: true },
        position: { type: String, trim: true },
        idDocument: { type: DocumentSchema }, // E.g., QID/Passport Scan
        contactNumber: { type: String, trim: true }
    }],
    beneficialOwners: [{ // Often overlaps with Shareholders/Directors but required by AML
        name: { type: String, trim: true },
        ownership: { type: Number }, // Percentage
        idDocument: { type: DocumentSchema }
    }],

    // --- Internal Tracking Flags ---
    isBankStatementUploaded: { type: Boolean, default: false },
    isFirstInvoiceUploaded: { type: Boolean, default: false },
    isCreditLineAssigned: { type: Boolean, default: false },

    // --- Timestamps ---
    insertedOn: { type: Date, default: Date.now },
    updatedOn: { type: Date },
    dataClassificationType: {
        type: String,
        enum: ['internal', 'restricted',],
        default: 'restricted'
    }

}, {
    // Enable timestamps for automatic createdAt and updatedAt
    timestamps: { createdAt: 'insertedOn', updatedAt: 'updatedOn' }
});

// --- Indexes for Performance (Optional but Recommended) ---
InvoiceFinancingUserSchema.index({ email: 1 });
InvoiceFinancingUserSchema.index({ mobileNo: 1 });
InvoiceFinancingUserSchema.index({ "kyc.crNumber": 1 });

// Pre-save hook for the main user schema
InvoiceFinancingUserSchema.pre('save', function (next) {
    const now = new Date();

    // Set KYC expiration date for the main user if it's a new document and not already set
    if (this.isNew && this.kyc && !this.kyc.kycExpirationDate) {
        const expirationDate = new Date(now);
        expirationDate.setFullYear(expirationDate.getFullYear() + 1);
        this.kyc.kycExpirationDate = expirationDate;
    }

    // Set KYC expiration date for each shareholder if not already set
    if (this.shareholders && this.shareholders.length > 0) {
        this.shareholders.forEach(shareholder => {
            if (!shareholder.kycExpirationDate) {
                const shareholderExpDate = new Date(now);
                shareholderExpDate.setFullYear(shareholderExpDate.getFullYear() + 1);
                shareholder.kycExpirationDate = shareholderExpDate;
            }
        });
    }

    next();
});

module.exports = mongoose.model("InvoiceFinancingUsers", InvoiceFinancingUserSchema, "InvoiceFinancingUsers");