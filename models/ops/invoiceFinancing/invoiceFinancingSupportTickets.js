const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const DocumentSchema = new Schema({
    filePath: { type: String },
    signedUrl: { type: String },
    uploadedOn: { type: Date, default: Date.now },
    expiresOn: { type: Date },
    mimeType: { type: String },
    fileName: { type: String, trim: true },
}, { _id: false });

const CommunicationLogEntrySchema = new Schema({
    senderType: {
        type: String,
        enum: ['User', 'Agent', 'System'],
        required: true
    },
    senderId: {
        type: Schema.Types.ObjectId,
        required: true
    },
    senderName: { type: String, trim: true },
    message: {
        type: String,
        required: true,
        trim: true,
        maxlength: 1500
    },
    timestamp: {
        type: Date,
        default: Date.now
    },
    attachment: { type: DocumentSchema },
}, { _id: false });

const InternalNoteSchema = new Schema({
    addedBy: {
        type: Schema.Types.ObjectId,
        ref: 'Admin',
        required: true
    },
    note: {
        type: String,
        required: true,
        trim: true,
        maxlength: 1000
    },
    timestamp: {
        type: Date,
        default: Date.now
    }
}, { _id: false });

const SupportTicketSchema = new Schema({
    ticketId: {
        type: String,
        unique: true,
        index: true,
        required: true,
        default: function () {
            const prefix = 'TKT-';
            const alphanumeric = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
            let uniquePart = '';
            for (let i = 0; i < 6; i++) {
                uniquePart += alphanumeric.charAt(Math.floor(Math.random() * alphanumeric.length));
            }
            return prefix + uniquePart;
        }
    },
    // TO:
    userId: {
        type: Schema.Types.ObjectId,
        required: true,
        refPath: 'userModel' // Points to the field that defines the model
    },
    userModel: {
        type: String,
        required: true,
        enum: ['InvoiceFinancingUsers', 'SuperAdmin'] // List of possible user models
    },
    userDisplayInfo: {
        uniqueId: { type: String, trim: true },
        fullName: { type: String, trim: true },
        email: { type: String, trim: true },
        mobileNo: { type: String, trim: true },
        businessName: { type: String, trim: true },
        dataClassificationType: {
            type: String,
            enum: ['internal', 'restricted'],
            default: 'restricted'
        }
    },
    subject: {
        type: String,
        required: true,
        trim: true,
        maxlength: 200
    },
    description: {
        type: String,
        required: true,
        trim: true,
        maxlength: 2000
    },
    category: {
        type: String,
        enum: [
            'Account & Profile',
            'Loan Application',
            'Invoice Financing',
            'Payments & Transactions',
            'KYC & Compliance',
            'Technical Issue',
            'Feedback & Suggestions',
            'Dispute - Invoice Discrepancies',
            'Dispute - Financing & Fees',
            'Dispute - Resolution Process',
            'Other'
        ],
        required: true,
        index: true
    },
    subCategory: {
        type: String,
        trim: true,
    },
    priority: {
        type: String,
        enum: ['Low', 'Medium', 'High', 'Urgent'],
        default: 'Medium',
        required: true,
        index: true
    },
    status: {
        type: String,
        enum: [
            'Open',
            'Pending Customer Reply',
            'Waiting for Internal Team',
            'Resolved',
            'Closed',
            'Reopened'
        ],
        default: 'Open',
        required: true,
        index: true
    },
    currentAssignee: {
        type: Schema.Types.ObjectId,
        ref: 'Admin',
        index: true,
        sparse: true
    },
    relatedEntity: {
        type: {
            type: String,
            enum: ['Invoice', 'LoanApplication', 'PaymentTransaction', 'KYCProfile', 'BusinessDetails', 'UserAccount', 'Other'],
            trim: true
        },
        id: {
            type: Schema.Types.ObjectId,
        },
        referenceNumber: {
            type: String,
            trim: true
        },
    },
    communicationLog: [CommunicationLogEntrySchema],
    internalNotes: [InternalNoteSchema],
    resolutionDetails: {
        type: String,
        trim: true,
        maxlength: 1000
    },
    resolvedAt: {
        type: Date
    },
    resolvedBy: {
        type: Schema.Types.ObjectId,
        ref: 'Admin'
    },
    closedAt: {
        type: Date
    },
    closedBy: {
        type: Schema.Types.ObjectId,
        ref: 'Admin'
    },
    attachments: [DocumentSchema],
    customerRating: {
        type: Number,
        min: 1,
        max: 5
    },
    customerFeedback: {
        type: String,
        trim: true,
        maxlength: 500
    },
    firstResponseTimeHours: {
        type: Number
    },
    timeToResolutionHours: {
        type: Number
    },
    lastCustomerReplyAt: { type: Date },
    lastAgentReplyAt: { type: Date },
    dataClassificationType: {
        type: String,
        enum: ['internal', 'restricted'],
        default: 'restricted'
    }
}, {
    timestamps: { createdAt: 'createdAt', updatedAt: 'updatedAt' }
});

SupportTicketSchema.pre('save', async function (next) {
    if (this.userId && (this.isNew || this.isModified('userId') || !this.userDisplayInfo || !this.userDisplayInfo.fullName)) {
        try {
            const User = mongoose.models.InvoiceFinancingUsers || mongoose.model('InvoiceFinancingUsers');
            const user = await User.findById(this.userId).select('firstName lastName email mobileNo uniqueId kyc.businessDetails.businessName dataClassificationType');

            if (user) {
                this.userDisplayInfo = {
                    uniqueId: user.uniqueId,
                    fullName: `${user.firstName || ''} ${user.lastName || ''}`.trim(),
                    email: user.email,
                    mobileNo: user.mobileNo,
                    businessName: user.kyc && user.kyc.businessDetails ? user.kyc.businessDetails.businessName : null,
                    dataClassificationType: user.dataClassificationType || 'restricted'
                };
            } else {
                console.warn(`SupportTicket: User with ID ${this.userId} not found during pre-save operation for ticket ${this.ticketId}.`);
            }
        } catch (error) {
            console.error(`Error populating userDisplayInfo for ticket ${this.ticketId}:`, error);
            next(error);
        }
    }

    if (this.isModified('communicationLog') && this.communicationLog.length > 0) {
        const firstAgentReply = this.communicationLog.find(log => log.senderType === 'Agent');
        if (firstAgentReply && !this.firstResponseTimeHours) {
            if (this.createdAt) {
                const timeDiffMs = firstAgentReply.timestamp.getTime() - this.createdAt.getTime();
                this.firstResponseTimeHours = timeDiffMs / (1000 * 60 * 60);
            }
        }

        const lastEntry = this.communicationLog[this.communicationLog.length - 1];
        if (lastEntry) {
            if (lastEntry.senderType === 'User') {
                this.lastCustomerReplyAt = lastEntry.timestamp;
            } else if (lastEntry.senderType === 'Agent') {
                this.lastAgentReplyAt = lastEntry.timestamp;
            }
        }
    }

    if (this.isModified('status') && this.status === 'Resolved' && !this.resolvedAt) {
        this.resolvedAt = new Date();
        if (this.createdAt) {
            this.timeToResolutionHours = (this.resolvedAt.getTime() - this.createdAt.getTime()) / (1000 * 60 * 60);
        }
    }

    if (this.isModified('status') && this.status === 'Closed' && !this.closedAt) {
        this.closedAt = new Date();
    }

    next();
});

SupportTicketSchema.index({ status: 1, priority: -1 });
SupportTicketSchema.index({ userId: 1, createdAt: -1 });
SupportTicketSchema.index({ currentAssignee: 1, status: 1 });
SupportTicketSchema.index({ category: 1, status: 1 });
SupportTicketSchema.index({ 'userDisplayInfo.email': 1 });
SupportTicketSchema.index({ 'relatedEntity.id': 1, 'relatedEntity.type': 1 });

module.exports = mongoose.model("InvoiceFinancingSupportTicket", SupportTicketSchema);