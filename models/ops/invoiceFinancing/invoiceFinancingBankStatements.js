const mongoose = require('mongoose');

const lineItemSchema = new mongoose.Schema({
    description: {
        type: String,
        required: true
    },
    totalAmount: {
        type: Number,
        required: true
    },
    confidence: {
        type: Number,
        required: true
    },
    dataClassificationType: {
        type: String,
        enum: ['internal', 'restricted',],
        default: 'internal'
    }
});

const invoiceFinancingBankStatementsSchema = new mongoose.Schema({
    userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    panNo: {
        type: String,
        trim: true
    },
    gstin: {
        type: String,
        required: true,
        trim: true
    },
    s3Url: {
        type: String,
        required: true
    },
    signedUrl: {
        type: String
    },
    gcsUrl: {
        type: String
    },
    uploadedAt: {
        type: Date,
        default: Date.now
    },
    // New fields from Mindee API
    customerName: {
        type: String,
        required: true
    },
    customerId: {
        type: String
    },
    supplierName: {
        type: String
    },
    supplierAddress: {
        type: String
    },
    supplierEmail: {
        type: String
    },
    totalAmount: {
        type: Number,
        required: true
    },
    category: {
        type: String
    },
    locale: {
        type: String
    },
    lineItems: [lineItemSchema],
    documentType: {
        type: String
    },
    // Metadata
    confidence: {
        type: Number,
        required: true
    },
    processingTime: {
        type: Number
    },
    status: {
        type: String,
        enum: ['VERIFICATION_PENDING', 'VERIFIED', 'REJECTED'],
        default: 'VERIFICATION_PENDING'
    },
    verificationComments: {
        type: String
    },
    verifiedBy: {
        type: String
    },
    verifiedAt: {
        type: Date
    },
    dataClassificationType: {
        type: String,
        enum: ['internal', 'restricted',],
        default: 'internal'
    }
});

// Add indexes for common queries
invoiceFinancingBankStatementsSchema.index({ userId: 1, status: 1 });
invoiceFinancingBankStatementsSchema.index({ gstin: 1 });

const InvoiceFinancingBankStatements = mongoose.model('InvoiceFinancingBankStatements', invoiceFinancingBankStatementsSchema);

module.exports = InvoiceFinancingBankStatements;