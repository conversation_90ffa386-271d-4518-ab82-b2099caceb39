const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const invoiceFinancingApplicationsSchema = new Schema({
    userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    panNo: {
        type: String,
        required: false
    },
    gstin: {
        type: String,
        required: true
    },
    status: {
        type: String,
        required: true,
        enum: ['VERIFICATION_PENDING', 'VERIFIED', 'REJECTED', 'APPROVED', 'DISBURSED'],
        default: 'VERIFICATION_PENDING'
    },
    verificationComments: {
        type: String
    },
    verifiedBy: {
        type: String
    },
    verifiedAt: {
        type: Date
    },
    bankStatementId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'InvoiceFinancingBankStatements'
    },
    invoiceIds: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'InvoiceFinancingInvoices'
    }],
    requestedAmount: {
        type: Number
    },
    approvedAmount: {
        type: Number
    },
    interestRate: {
        type: Number
    },
    tenureInDays: {
        type: Number
    },
    processingFee: {
        type: Number
    },
    dataClassificationType: {
        type: String,
        enum: ['internal', 'restricted',],
        default: 'internal'
    }
}, {
    timestamps: true
});

// Add indexes for common queries
invoiceFinancingApplicationsSchema.index({ userId: 1 });
invoiceFinancingApplicationsSchema.index({ status: 1 });
invoiceFinancingApplicationsSchema.index({ gstin: 1 });

module.exports = mongoose.model('InvoiceFinancingApplications', invoiceFinancingApplicationsSchema);