const mongoose = require('mongoose');
const reportSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true, // Name of the file (e.g., weekly_security_incidents.csv)
  },
  path: {
    type: String,
    required: true, // Public GCS path or signed URL
  },
  date: {
    type: String,
    required: true, // Format: YYYY-MM-DD
  },
  time: {
    type: String,
    required: true, // Format: HH:mm:ss
  }
}, {
  timestamps: true // Adds createdAt and updatedAt
});
module.exports = mongoose.model('invoiceFinancingReports', reportSchema);