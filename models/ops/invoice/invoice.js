const mongoose = require("mongoose");
const joi = require("@hapi/joi");
const jwt = require("jsonwebtoken");
const config = require("config");
const Schema = mongoose.Schema;

const invoiceJobSchema = new mongoose.Schema(
    {
        partnerId: { type: Schema.ObjectId },
        invoiceNo: { type: String },
        details: {
            invoiceType: { type: String },
            invoiceDate: { type: String },
            startDate: { type: Date, format: "date-time" },
            endDate: { type: Date, format: "date-time" }
        },
        status: { type: String },
        steps: [
            {
                step: { type: String },
                status: { type: String },
                response: {
                    data: { type: String },
                    success: { type: Boolean },
                    message: { type: String },
                },
                insertedOn: { type: Date, format: "date-time" },
                completedOn: { type: Date, format: "date-time" }
            }
        ],
        insertedOn: { type: Date, format: "date-time" },
        updatedOn: { type: Date, format: "date-time" },
        dispatcherStartedOn: { type: Date, format: "date-time" },
        dispathcherCompletedOn: { type: Date, format: "date-time" },
        error: {
            errorMsg: { type: String }
        },
        transformTime: { type: Number }
    });

module.exports.InvoiceJob = mongoose.model("InvoiceJob", invoiceJobSchema, "InvoiceJob");

const invoiceSchema = new mongoose.Schema(
    {
        invoiceNo: { type: String },
        invoiceType: { type: String },
        invoiceText: { type: String },
        partnerId: { type: Schema.ObjectId },
        invoiceDate: { type: Date, format: "date-time" },
        items: [
            {
                componentKey: { type: String },
                description: { type: String },
                notes: { type: String },
                isTaxable: { type: Boolean },
                hsn: { type: String },
                taxDetails: {
                    igst: {
                        percentage: { type: Number },
                        amount: { type: Number }
                    }
                },
                amount: { type: Number },
                totalTaxPercentage: { type: Number },
                taxAmount: { type: Number },
                totalAmount: { type: Number }
            }
        ],
        taxDetails: {
            totalIgst: { type: Number },
        },
        totalTaxableAmount: { type: Number },
        totalAmount: { type: Number },
        totalTaxAmount: { type: Number },
        totalTaxAmountInWords: { type: String },
        totalInvoiceAmount: { type: Number },
        totalInvoiceAmountInWords: { type: String },
        invoiceStartDate: { type: Date, format: "date-time" },
        invoiceEndDate: { type: Date, format: "date-time" },
        status: { type: String },
        paymentStatus: { type: String },
        billingInfo: {
            invoiceFrom: {
                address: { type: String },
                gstNo: { type: String },
                cin: { type: String },
                emailId: { type: String },
                stateName: { type: String },
                stateCode: { type: String },
                name: { type: String },
                panNo: { type: String },
                hsn: { type: String },
                bankDetails: {
                    name: { type: String },
                    accountNumber: { type: String },
                    ifscCode: { type: String }
                }
            },
            invoiceTo: {
                address: { type: String },
                gstNo: { type: String },
                cin: { type: String },
                emailId: { type: String },
                stateName: { type: String },
                stateCode: { type: String },
                name: { type: String },
                panNo: { type: String },
                hsn: { type: String },
                bankDetails: {
                    name: { type: String },
                    accountNumber: { type: String },
                    ifscCode: { type: String }
                }
            }
        },
        dueDate: { type: Date, format: "date-time" },
        collectionDate: { type: Date, format: "date-time" },
        filePath: { type: String },
        misFilePath: { type: String },
        notes: { type: String },
        actionBy: { type: Schema.ObjectId },
        actionOn: { type: Date, format: "date-time" },
        insertedOn: { type: Date, format: "date-time" },
        updatedOn: { type: Date, format: "date-time" },
    });

module.exports.Invoice = mongoose.model("Invoice", invoiceSchema, "Invoice");

const invoiceLineItemSchema = new mongoose.Schema(
    {
        invoiceNo: { type: String },
        partnerId: { type: Schema.ObjectId },
        componentKey: { type: String },
        description: { type: String },
        details: {
            loanId: { type: Schema.ObjectId },
            loanNo: { type: String },
            lenderLoanId: { type: String },
            componentKey: { type: String },
            loanAmount: { type: Number },
            amount: { type: Number },
            gst: { type: Number }
        },
        insertedOn: { type: Date, format: "date-time" },
        totalAmount: { type: Number }
    });

module.exports.InvoiceLineItems = mongoose.model("InvoiceLineItems", invoiceLineItemSchema, "InvoiceLineItems");

const invoiceMasterSchema = new mongoose.Schema(
    {
        prefix: { type: String },
        invoiceType: { type: String },
        type: { type: String },
        counter: { type: Number },
        components: [
            {
                name: { type: String },
                status: { type: String },
                displayOrder: { type: Number },
                defaultNotes: { type: String },
                isTaxable: { type: Boolean },
                taxPercentage: {
                    cgst: { type: Number },
                    sgst: { type: Number },
                    igst: { type: Number }
                },
                key: { type: String },
                defaultDescription: { type: String }
            }
        ],
        excelConfig: {
            "Sr No.": { type: String },
            "FundFina Loan App.No.": { type: String },
            "Lender Loan No": { type: String },
            "Loan Amount": { type: String },
            "Component": { type: String },
            "Description": { type: String },
            "Amount": { type: String }
        },
        dueDateType: { type: String },
        dueDateValue: { type: Number },
        isActive: { type: Boolean },
        invoiceFor: { type: String },
        insertedOn: { type: Date, format: "date-time" },
    });

module.exports.InvoiceMaster = mongoose.model("InvoiceMaster", invoiceMasterSchema, "InvoiceMaster");
