const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const NocLog = new mongoose.Schema({
    mailBox: { type: Object },
    uploadStatus: { type: String },
    fileName: { type: String },
    subject: { type: String },
    emailFrom: { type: String },
    emailMeta: { type: Object },
    url: { type: String },
    emailReceivedOn: { type: Date, format: "date-time", default: new Date() },
    insertedOn: { type: Date, format: "date-time", default: new Date() },
    modifiedOn: { type: Date, format: "date-time", default: new Date() },
    failureReason: { type: String },
    loanNo: { type: String },
    filePath: { type: String }
}, { strict: false });

module.exports.NocLog = mongoose.model("NocLog", NocLog, "NocLog");

