const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const settlementWebhookLog = new mongoose.Schema({
    insertedOn: { type: Date, format: "date-time", default: new Date() }
}, { strict: false });

module.exports.SettlementWebhookLog = mongoose.model("SettlementWebhookLog", settlementWebhookLog, "SettlementWebhookLog");

const SettlementMISStage = new mongoose.Schema({
    merchantCode: { type: Number },
    productId: { type: String },
    referenceNo: { type: String },
    emiNumber: { type: Number },
    rePaymentAmount: { type: Number },
    collecedOn: { type: String },
    utrNo: { type: String },
    channel: { type: String },
    channelInfo: { type: Object },
    insertedOn: { type: Date, format: "date-time", default: new Date() }
}, { strict: false });

module.exports.SettlementMISStage = mongoose.model("SettlementMISStage", SettlementMISStage, "SettlementMISStage");


const SettlementMISLog = new mongoose.Schema({
    info: { type: Object },
    channel: { type: String },
    insertedOn: { type: Date, format: "date-time", default: new Date() },
    modifiedOn: { type: Date, format: "date-time", default: new Date() },
    stagingStatus: { type: String },
    failureReason: { type: String },

}, { strict: false });

module.exports.SettlementMISLog = mongoose.model("SettlementMISLog", SettlementMISLog, "SettlementMISLog");


const SettlementTransactions = new mongoose.Schema({
    utrNo: { type: String },
    date: { type: Date, format: "date-time" },
    insertedOn: { type: Date, format: "date-time" },
    updatedOn: { type: Date, format: "date-time" },
    isMISReceived: { type: Boolean },
    isSettlementReceived: { type: Boolean },
    misAmount: { type: Number },
    partnerId: { type: Schema.ObjectId },
    settlementType: { type: String },
    status: { type: String },
    totalMisCount: { type: Number },
    transactionAmount: { type: Number },
    installmentCount: { type: Number },
    loanFeesCount: { type: Number },

}, { strict: false });

module.exports.SettlementTransactions = mongoose.model("SettlementTransactions", SettlementTransactions, "SettlementTransactions");


const PayoutTransactions = new mongoose.Schema({
}, { strict: false });

module.exports.PayoutTransactions = mongoose.model("PayoutTransactions", PayoutTransactions, "PayoutTransactions");


const PayoutStaging = new mongoose.Schema({
}, { strict: false });

module.exports.PayoutStaging = mongoose.model("PayoutStaging", PayoutStaging, "PayoutStaging");

const SettlementReconciliation = new mongoose.Schema({
    totalReceivedCount: { type: Number },
    totalSettledCount: { type: Number },
    totalRequestSent: { type: Number },
    totalRequestedAmount: { type: Number },
    totalReceivedAmount: { type: Number },
    totalSettledAmount: { type: Number },
    partnerId: { type: Schema.ObjectId },
    transactionsDate: { type: Date },
    insertedOn: { type: Date, format: "date-time", default: new Date() },
    updatedOn: { type: Date, format: "date-time", default: new Date() }
}, { strict: false });

module.exports.SettlementReconciliation = mongoose.model("SettlementReconciliation", SettlementReconciliation, "SettlementReconciliation");


const ManualQueue = new mongoose.Schema({
}, { strict: false });

module.exports.ManualQueue = mongoose.model("ManualQueue", ManualQueue, "ManualQueue");




