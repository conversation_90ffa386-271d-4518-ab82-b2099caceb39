const joi = require("@hapi/joi");
const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const loanDisbursmentTransactions = new mongoose.Schema({
    loanId: { type: Schema.ObjectId },
    userId: { type: Schema.ObjectId },
    partner: { type: String },
    partnerId: { type: Schema.ObjectId },
    loanNo: { type: String },
    trxnId: { type: String },
    productId: { type: String },
    productType: { type: String },
    customerRefNo: { type: String },
    amount: { type: Number },
    purpose: { type: String },
    currency: { type: String },
    loanType: { type: String },
    disbursalDetails: {
        bankReferenceNo: { type: String },
        paymentMode: { type: String },
        dot: { type: String },
        bankCode: { type: String },
        disbursedBy: Schema.ObjectId,
        disbursedOn: { type: Date, format: "date-time" },
        isManual: { type: Boolean, default: true },
        stampingCharges: { type: Number },
        gstCharges: { type: Number },
        processingFee: { type: Number },
        interestPreRecovered: { type: Number },
        totalChargesRecovered: { type: Number },
        disbursalAmount: { type: Number },
        emiDueDate: { type: Date, format: "date-time" },
        custProcessingFee: { type: Number },
        custGstCharges: { type: Number },
        custTotalChargesRecovered: { type: Number },
        partnerDisbursedOn: { type: Date, format: "date-time" }
    },
    transactionDetails: Schema.Types.Mixed,
    disbursalStatus: { type: String },
    disbursalType: { type: String },
    disburalSteps: Schema.Types.Mixed,
    updatedOn: { type: Date, format: "date-time" },
    insertedOn: { type: Date, format: "date-time" }
}, { strict: false });

module.exports.LoanDisbursmentTransactions = mongoose.model("LoanDisbursmentTransactions", loanDisbursmentTransactions, "LoanDisbursmentTransactions");


const loanDisbursmentTransactionsHistory = new mongoose.Schema({
    loanId: { type: Schema.ObjectId },
    userId: { type: Schema.ObjectId },
    loanNo: { type: String },
    partner: { type: String },
    partnerId: { type: Schema.ObjectId },
    trxnId: { type: String },
    productId: { type: String },
    productType: { type: String },
    amount: { type: Number },
    trancheNo: { type: String },
    purpose: { type: String },
    currency: { type: String },
    loanType: { type: String },
    customerRefNo: { type: String },
    disbursalDetails: {
        bankReferenceNo: { type: String },
        paymentMode: { type: String },
        dot: { type: String },
        bankCode: { type: String },
        disbursedBy: Schema.ObjectId,
        disbursedOn: { type: Date, format: "date-time" },
        isManual: { type: Boolean, default: true },
        stampingCharges: { type: Number },
        gstCharges: { type: Number },
        processingFee: { type: Number },
        interestPreRecovered: { type: Number },
        totalChargesRecovered: { type: Number },
        disbursalAmount: { type: Number },
        emiDueDate: { type: Date, format: "date-time" },
        custProcessingFee: { type: Number },
        custGstCharges: { type: Number },
        custTotalChargesRecovered: { type: Number },
        partnerDisbursedOn: { type: Date, format: "date-time" }
    },
    transactionDetails: Schema.Types.Mixed,
    disbursalStatus: { type: String },
    disbursalType: { type: String },
    disburalSteps: Schema.Types.Mixed,
    updatedOn: { type: Date, format: "date-time" },
    insertedOn: { type: Date, format: "date-time" }
}, { strict: false });

module.exports.LoanDisbursmentTransactionsHistory = mongoose.model("LoanDisbursmentTransactionsHistory", loanDisbursmentTransactionsHistory, "LoanDisbursmentTransactionsHistory");


const loanDisbursmentTransactionsBatch = new mongoose.Schema({
    updatedOn: { type: Date, format: "date-time" },
    insertedOn: { type: Date, format: "date-time" }
}, { strict: false });

module.exports.LoanDisbursmentTransactionsBatch = mongoose.model("LoanDisbursmentTransactionsBatch", loanDisbursmentTransactionsBatch, "LoanDisbursmentTransactionsBatch");

const loanDisbursmentTransactionsBatchHistory = new mongoose.Schema({
    updatedOn: { type: Date, format: "date-time" },
    insertedOn: { type: Date, format: "date-time" }
}, { strict: false });

module.exports.LoanDisbursmentTransactionsBatchHistory = mongoose.model("LoanDisbursmentTransactionsBatchHistory", loanDisbursmentTransactionsBatchHistory, "LoanDisbursmentTransactionsBatchHistory");


const gatewayDisbursalRejectionCodes = new mongoose.Schema({
    gatewayType: { type: String },
    codes: [{ type: String }]
}, { strict: false });

module.exports.GatewayDisbursalRejectionCodes = mongoose.model("GatewayDisbursalRejectionCodes", gatewayDisbursalRejectionCodes, "GatewayDisbursalRejectionCodes");


//--------------------------------------------------------------------------------------------------------------------//
module.exports.validateSearchDisbursal = function (model) {
    const searchDisbursal = joi.object({
        fromDate: joi.string().required(),
        toDate: joi.string().required(),
        borrowingPartner: joi.string().allow(null, ""),
        status: joi.string().allow(null, ""),
        productId: joi.string().allow(null, ""),
        loanNo: joi.string().allow(null, ""),
        lendingPartner: joi.string().allow(null, "")
    })

    return searchDisbursal.validate(model);
}

module.exports.validateBankDisbursal = function (model) {
    const bankDisbursal = joi.object({
        loanId: joi.string().required(),
        bankReferenceNo: joi.string().required(),
        paymentMode: joi.string().required(),
        bankCode: joi.string().required(),
        dateOfTransaction: joi.string().required()
    });

    return bankDisbursal.validate(model);
}

module.exports.validateSearchDisbursalTransactions = function (model) {
    const searchDisbursalTransactions = joi.object({
        fromDate: joi.string().required(),
        toDate: joi.string().required()
    })

    return searchDisbursalTransactions.validate(model);
}