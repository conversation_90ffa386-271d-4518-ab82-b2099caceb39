const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const paymentCollectionSchema = new mongoose.Schema({
    loanId: Schema.ObjectId,
    loanNo: { type: String },
    loanStatus: { type: String },
    productId: { type: String },
    productType: { type: String },
    partnerId: Schema.ObjectId,
    partner: { type: String },
    partnerCode: { type: String },
    merchantCode: { type: String },
    trxnId: { type: String },
    emiNumber: { type: Number },
    referenceNo: { type: String },
    disbursedAmount: { type: Number },
    disbursementDate: { type: Date, format: 'date-time' },
    dueDate: { type: Date, format: 'date-time' },
    repayAmount: { type: Number },
    penalty: { type: Number },
    moratoriumInterest: { type: Number },
    processingCharge: { type: Number },
    totalRepayAmount: { type: Number },
    accountNo: { type: String },
    ifsc: { type: String },
    source: { type: String },
    accountName: { type: String },
    rePaymentDate: { type: Date, format: 'date-time' },
    scheduledDueDate: { type: Date, format: 'date-time' },
    expiryDate: { type: Date, format: 'date-time' },
    isExisting: { type: Boolean, default: true },
    interestAmount: { type: Number },
    principleRecovered: { type: Number },
    responseDescription: { type: String },
    responseStatus: { type: Boolean, default: false },
    responseReceivedOn: { type: Date, format: 'date-time' },
    isPaid: { type: Boolean, default: false },
    paidOn: { type: Date, format: 'date-time' },
    isActive: { type: Boolean, default: true },
    insertedOn: { type: Date, default: Date.now, format: 'date-time' },
    updatedOn: { type: Date, format: 'date-time' },
    batchId: { type: String },
    batchIdUpdatedOn: { type: Date, format: 'date-time' },
    meta: { type: Object },
    channel: { type: String },
    referenceNo: { type: String },
    isConsolidated: { type: Boolean, default: false },
    from: { type: String }
});
exports.PaymentCollection = mongoose.model('PaymentCollection', paymentCollectionSchema, "PaymentCollection");

const RepaymentLogSchema = new mongoose.Schema({
    partnerId: Schema.ObjectId,
    loans: { type: Number },
    status: { type: String },
    productType: { type: String },
    totalInserted: { type: Number },
    insertedLoans: { type: Array },
    loanInstallments: { type: Array },
    insertedOn: { type: Date, default: Date.now, format: 'date-time' },
});
exports.RepaymentLog = mongoose.model('RepaymentLog', RepaymentLogSchema, "RepaymentLog");

const paymentCollectionRepositorySchema = new mongoose.Schema({
    loanId: Schema.ObjectId,
    loanNo: { type: String },
    productId: { type: String },
    productType: { type: String },
    partnerId: Schema.ObjectId,
    partner: { type: String },
    partnerCode: { type: String },
    merchantCode: { type: String },
    trxnId: { type: String },
    emiNumber: { type: Number },
    referenceNo: { type: String },
    disbursedAmount: { type: Number },
    disbursementDate: { type: Date, format: 'date-time' },
    dueDate: { type: Date, format: 'date-time' },
    repayAmount: { type: Number },
    penalty: { type: Number },
    moratoriumInterest: { type: Number },
    processingCharge: { type: Number },
    totalRepayAmount: { type: Number },
    accountNo: { type: String },
    ifsc: { type: String },
    source: { type: String },
    accountName: { type: String },
    rePaymentDate: { type: Date, format: 'date-time' },
    expiryDate: { type: Date, format: 'date-time' },
    isExisting: { type: Boolean, default: true },
    interestAmount: { type: Number },
    principleRecovered: { type: Number },
    responseDescription: { type: String },
    responseStatus: { type: Boolean, default: false },
    responseReceivedOn: { type: Date, format: 'date-time' },
    isPaid: { type: Boolean, default: false },
    paidOn: { type: Date, format: 'date-time' },
    isActive: { type: Boolean, default: true },
    insertedOn: { type: Date, default: Date.now, format: 'date-time' },
    updatedOn: { type: Date, format: 'date-time' },
    deletedOn: { type: Date, default: Date.now, format: 'date-time' },
});
exports.PaymentCollectionRepository = mongoose.model('PaymentCollectionRepository', paymentCollectionRepositorySchema, "PaymentCollectionRepository");

const paymentCollectionInquirySchema = new mongoose.Schema({
    partnerId: Schema.ObjectId,
    productId: { type: String },
    referenceNo: { type: String },
    repaymentStatus: { type: String },
    dueDate: { type: Date, format: 'date-time' },
    repayAmount: { type: Number },
    paidAt: { type: Date, format: 'date-time' },
    autoRetryAttempts: { type: Number },
    isPaid: { type: Boolean, default: true },
    isPrincipalPaid: { type: Boolean },
    isActive: { type: Boolean, default: true },
    errorCode: { type: String },
    errorDesc: { type: String },
    errorData: { type: String },
    requestRef: { type: String },
    insertedOn: { type: Date, default: Date.now, format: 'date-time' },
    updatedOn: { type: Date, format: 'date-time' }
});
exports.PaymentCollectionInquiry = mongoose.model('PaymentCollectionInquiry', paymentCollectionInquirySchema, "PaymentCollectionInquiry");

const paymentCollectionInquiryRepositorySchema = new mongoose.Schema({
    partnerId: Schema.ObjectId,
    productId: { type: String },
    referenceNo: { type: String },
    repaymentStatus: { type: String },
    dueDate: { type: Date, format: 'date-time' },
    repayAmount: { type: Number },
    paidAt: { type: Date, format: 'date-time' },
    autoRetryAttempts: { type: Number },
    isPaid: { type: Boolean, default: true },
    isPrincipalPaid: { type: Boolean },
    isActive: { type: Boolean, default: true },
    errorCode: { type: String },
    errorDesc: { type: String },
    errorData: { type: String },
    requestRef: { type: String },
    insertedOn: { type: Date, default: Date.now, format: 'date-time' },
    deletedOn: { type: Date, default: Date.now, format: 'date-time' }
});
exports.PaymentCollectionInquiryRepository = mongoose.model('PaymentCollectionInquiryRepository', paymentCollectionInquiryRepositorySchema, "PaymentCollectionInquiryRepository");

const paymentCollectionEnquiryLog = new mongoose.Schema({
    request: { type: Object },
    response: { type: Object },
    batchId: { type: String },
    insertedOn: { type: Date, default: Date.now, format: 'date-time' }
});
exports.PaymentCollectionEnquiryLog = mongoose.model('PaymentCollectionEnquiryLog', paymentCollectionEnquiryLog, "PaymentCollectionEnquiryLog");


const FollowUpSchema = new mongoose.Schema({
    type: { type: String },
    loanId: Schema.ObjectId,
    loanNo: { type: String },
    userId: Schema.ObjectId,
    emiNumber: { type: String },
    amount: { type: Number },
    name: { type: String },
    emailId: { type: Array },
    mobileNo: { type: Array },
    isPending: { type: Boolean, default: true },
    insertedOn: { type: Date, default: Date.now, format: 'date-time' }
});
exports.FollowUpRepository = mongoose.model('FollowUpRepository', FollowUpSchema, "FollowUpRepository");

const schedularDetailsSchema = new mongoose.Schema({
    name: { type: String },
    message: { type: String },
    response: { type: String },
    insertedOn: { type: Date }
});
exports.SchedularDetails = mongoose.model("SchedularDetails", schedularDetailsSchema, "SchedularDetails");

const outgoingPaymentsSchema = new mongoose.Schema({
    transactionType: { type: String },
    clientCode: { type: String },
    debitAccountNo: { type: String },
    transactionTypeCode: { type: String },
    valueDate: { type: String },
    penalty: { type: String },
    amount: { type: String },
    beneficiaryName: { type: String },
    beneficiaryAccountNo: { type: String },
    ifscCode: { type: String },
    customerRefNo: { type: String },
    beneficiaryEmailId: { type: String },
    beneficiaryMobileNo: { type: String },
    remarks: { type: String },
    paymentType: { type: String },
    purposeCode: { type: String },
    beneficiaryAccountType: { type: String },
    payableLocation: { type: String },
    printBranchName: { type: String },
    modeOfDelivery: { type: String },
    transactionCurrency: { type: String },
    beneficiaryAdd1: { type: String },
    beneficiaryAdd2: { type: String },
    beneficiaryAdd3: { type: String },
    beneficiaryAdd4: { type: String },
    beneficiaryId: { type: String },
    fileName: { type: String },
    bankResponseFileName: { type: String },
    utrReferenceNo: { type: String },
    insertedOn: { type: Date, default: Date.now, format: 'date-time' },
    updatedOn: { type: Date, default: Date.now, format: 'date-time' }
});
exports.OutgoingPayments = mongoose.model("OutgoingPayments", outgoingPaymentsSchema, "OutgoingPayments");

const repaymentWebHook = new mongoose.Schema({
    partnerId: Schema.ObjectId,
    productId: { type: String },
    referenceNo: { type: String },
    amount: { type: Number },
    isPaid: { type: Boolean, default: false },
    isPrincipalPaid: { type: Boolean, default: false },
    paidOn: { type: Date, format: 'date-time' },
    isDBUpdated: { type: Boolean, default: false },
    status: { type: String },
    errorDesc: { type: String },
    insertedOn: { type: Date, default: Date.now, format: 'date-time' },
    updatedOn: { type: Date, default: Date.now, format: 'date-time' }
});
exports.RepaymentWebHook = mongoose.model('RepaymentWebHook', repaymentWebHook, "RepaymentWebHook");