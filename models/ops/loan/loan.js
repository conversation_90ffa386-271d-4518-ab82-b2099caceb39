const mongoose = require("mongoose");
const joi = require("@hapi/joi");
const jwt = require("jsonwebtoken");
const config = require("config");
const Schema = mongoose.Schema;

const loanInfoSchema = new mongoose.Schema({
  userId: { type: Schema.ObjectId },
  loanNo: { type: String },
  leadId: {type: String},
  partner: { type: String },
  partnerId: { type: Schema.ObjectId },
  merchantCode: { type: String },
  merchantType: { type: String },
  trxnId: { type: String },
  productId: { type: String },
  productType: { type: String },
  amount: { type: Number },
  trancheNo: { type: String },
  purpose: { type: String },
  description: { type: String },
  maturity: { type: Number },
  interestRate: { type: Number },
  currency: { type: String },
  campaignTime: { type: String },
  loanType: { type: String },
  repaymentFrequency: { type: String },
  additionalDescription: { type: String },
  isMaturityMonth: { type: Boolean, default: true },
  emiAmount: { type: Number, default: null },
  interestType: { type: String },
  delinquencyString: { type: String },
  lenderDetails: [{
    lenderId: Schema.ObjectId,
    lenderPartnerId: Schema.ObjectId,
    lenderName: { type: String },
    amount: { type: Number },
    investedOn: { type: Date, default: Date.now, format: "date-time" },
    lenderCustId: { type: String },
    lenderLoanId: { type: String },
    lenderLeadId: { type: String },
    lenderProductType: { type: String },
    trancheType: { type: String },
    responseFromLender: { type: String },
    errorMsg: { type: String },
    errorDate: { type: Date, format: "date-time" },
    status: {type: String}
  }],
  flowDetails: [{
    Step: { type: String },
    changedBy: Schema.ObjectId,
    changedByRole: { type: String },
    insertedOn: { type: Date, format: "date-time" },
  }],
  loanContractDetails: {
    ipAddress: { type: String },
    isAccepted: { type: Boolean, default: false },
    emailedOn: { type: Date, format: "date-time" },
    acceptedOn: { type: Date, format: "date-time" },
    contractFile: { type: String },
    photoFileName: { type: String },
    photoFilePath: { type: String },
    contractUrl: { type: String },
    eNachUrl: { type: String },
    authMode: { type: String },
    redirectionLongUrl: { type: String },
    contractSuccessCallBackUrl: { type: String },
    eNachErrorCallBackUrl: { type: String },
    disbursalConsent: { type: Boolean },
    disbursalConsentGivenOn: { type: Date, format: "date-time" },
    kfsFile: { type: String },
    pNachFile: { type: String },
    coordinates: {
      latitude: { type: Number },
      longitude: { type: Number },
      isApi: { type: Boolean }
    },
    nocFile: { type: String },
    shopDocuments: [{
      shopName: { type: String },
      shopInternalPhotoFileName: { type: String },
      shopInternalPhotoFilePath: { type: String },
      shopExternalPhotoFileName: { type: String },
      shopExternalPhotoFilePath: { type: String },
      shopVideoFileName: { type: String },
      shopVideoFilePath: { type: String }
    }]
  },
  disbursalDetails: {
    bankReferenceNo: { type: String },
    paymentMode: { type: String },
    dot: { type: String },
    bankCode: { type: String },
    disbursedBy: Schema.ObjectId,
    disbursedOn: { type: Date, format: "date-time" },
    channel: { type: String },
    isManual: { type: Boolean, default: true },
    stampingCharges: { type: Number },
    gstCharges: { type: Number },
    processingFee: { type: Number },//processing Fee Fundfina
    interestPreRecovered: { type: Number },
    totalChargesRecovered: { type: Number },
    disbursalAmount: { type: Number },
    emiDueDate: { type: Date, format: "date-time" },
    custProcessingFee: { type: Number },//processing Fee of Customer
    custGstCharges: { type: Number },
    custTotalChargesRecovered: { type: Number },
    partnerDisbursedOn: { type: Date, format: "date-time" }
  },
  emiDetails: [
    {
      emiNumber: { type: Number },
      interestAmount: { type: Number },
      principalRecovered: { type: Number },
      principalOutstanding: { type: Number },
      penalty: { type: Number },
      moratoriumInterest: { type: Number },
      rePaymentAmount: { type: Number },
      rePaymentDate: { type: Date, format: "date-time" },
      rePaymentActualDate: { type: Date, format: "date-time" },
      rePaymentStatus: { type: String },
      rePaymentReceivedDate: { type: Date, format: "date-time" },
      insertedOn: { type: Date, format: "date-time" },
      updatedOn: { type: Date, format: "date-time" },
      isPrincipalPaid: { type: Boolean, default: false }, //Only for Daily Loans
      principalPaidOn: { type: Date, format: "date-time" },
      rePaymentReceivedSource: { type: String },
      holdCollection: { type: Boolean, default: false }
    },
  ],
  isDraft: { type: Boolean, default: true },
  status: { type: String },
  postDisbursalStatus: { type: String },
  adminRemarks: { type: String },
  clientRemarks: { type: String },
  rejectDescription: { type: String },
  isActive: { type: Boolean, default: true },
  insertedOn: { type: Date, default: Date.now, format: "date-time" },
  updatedOn: { type: Date, format: "date-time" },
  signatureDetails: {
    name: { type: String }
  },
  lastStepCompleted: { type: String },
  lastStepCompletedAt: { type: Date },
  loanStepDetails: [{
    Step: { type: String },
    isCompleted: { type: Boolean, default: true },
    insertedOn: { type: Date, default: Date.now, format: "date-time" },
    completedOn: { type: Date, format: "date-time" },
  }],
  adminModifiedOn: { type: Date },
  ipAddress: { type: String },
  disbursalBankDetails: {
    bankName: { type: String },
    branch: { type: String },
    accountNo: { type: String },
    accountName: { type: String },
    ifsc: { type: String },
    accountType: { type: String },
    address: { type: String },
    city: { type: String },
    state: { type: String },
    insertedOn: { type: Date, default: Date.now, format: "date-time" },
    updatedOn: { type: Date, format: "date-time" }
  },
  primaryCollectionType: { type: String },
  secondaryCollectionType: { type: String },
  byPassEnach: { type: Boolean, default: false },
  isParentLoan: { type: Boolean, default: false },
  invoiceRequestId: { type: Schema.ObjectId },
  adhocDays: { type: Number },
  tenure: { type: Number },
  arrears: [{
    delinquency: { type: String },
    amount: { type: Number },
    installmentsCollected: { type: Number },
    insertedOn: { type: Date, format: "date-time" }
  }],
  disbursalError: Schema.Types.Mixed,
  disbursalStatus: { type: String },
  batchId: { type: String },
  isRedisbursal: { type: Boolean, default: false },
  isStp: { type: Boolean, default: false },
  discount: {
    campaignId: mongoose.Schema.ObjectId,
    discountAmount: { type: Number },
    actualInterestRate: { type: Number },
    discountedInterestRate: { type: Number },
    isPaid: { type: Boolean, default: false },
    paidOn: { type: Date },
    utrNo: { type: String },
    insertedOn: { type: Date, default: Date.now, format: "date-time" },
    updatedOn: { type: Date, format: "date-time" }
  },
  kycId: { type: Schema.ObjectId },
  campaignId: { type: Schema.ObjectId },
  teleCallingActions: [
    {
      outcome: { type: String },
      calledOn: { type: Date, default: Date.now, format: "date-time" },
      calledBy: { type: Schema.ObjectId },
      borrowerResponse: { type: String },
      date: { type: Date, format: "date-time" },
      //willApplyOn: { type: Date, format: "date-time" },
      notConnectedReason: { type: String },
      isRenewals: { type: Boolean }
    }
  ],
  note: { type: String },
  principalAmount: {
    collected: { type: Number },
    outstanding: { type: Number },
    settled: { type: Number }
  },
  interestAmount: {
    collected: { type: Number },
    outstanding: { type: Number },
    settled: { type: Number }
  },
  totalEmisCollected: { type: Number },
  penalty: { type: Number },
  enterprisePartnerFee: { type: Number },
  offerId: { type: Schema.ObjectId },
  processingFeePer: { type: Number },
  isCollectionTeamCalled: { type: Boolean },
  processingFeeAmount: { type: Number },
  isForceMandate: { type: Boolean, default: false },
  processingFeeAmount: { type: Number },
  tranche: {
    id: { type: String }
  },
  lastEmiPaidDate: { type: Date },
  nextEmiRepaymentDate: { type: Date },

  // Emi consolidated information starts
  moratoriumInterestOutstanding: { type: Number },
  feeAmountOutstanding: { type: Number },
  totalOutstanding: { type: Number },
  principalOutstandingTillDate: { type: Number },
  interestOutstandingTillDate: { type: Number },
  moratoriumInterestOutstandingTillDate: { type: Number },
  feeAmountOutstandingTillDate: { type: Number },
  pendingInstallmentsTillDate: { type: Number },
  totalMoratoriumInterest: { type: Number },
  totalInstallmentAmount: { type: Number },
  totalOutstandingAmountTillDate: { type: Number },
  pendingInstallments: { type: Number },
  photoMatch: { type: Object },
  issues: [{ type: Object }],
  totalPhotoMatchAttempt: { type: Number },
  // Emi consolidated information ends
  isSettled: { type: Boolean, default: false },
  settlement: {
    totalSettledAmount: { type: Number },
    principalSetteled: { type: Number },
    interestSetteled: { type: Number },
    fldgSetteledAmount: { type: Number },
    fldgSettledDate: { type: Date },
    lastSettlementDate: { type: Date },
    insertedOn: { type: Date },
    updateOn: { type: Date },
  },
  batchId: { type: String },
  batchIdUpdatedOn: { type: Date, format: 'date-time' },
  isMobileNumberVerified: { type: Boolean, default: false },
  isMobileNumberConfirmed: { type: Boolean, default: false },
  isAutoLogin: { type: Boolean, default: false },
  reviewReason: { type: String },
  isForeclosure: { type: Boolean, default: false },
  adjustedLoanInterest: { type: Number, default: 0 },
  foreclosureReason: { type: String },
  foreclosureBy: { type: Schema.ObjectId },
  foreclosureOn: { type: Date },
  loanFeeType: { type: String, default: "VERSION2" },
  usedGraceDays: { type: Number, default: 0 },
  lastRecoveryDate: { type: Date, format: 'date-time' },
  totalRecoveredAmount: { type: Number, default: 0 }
});

module.exports.LoanInfo = mongoose.model("LoanInfo", loanInfoSchema, "LoanInfo");

const loanInfoHistorySchema = new mongoose.Schema({
  loanId: { type: Schema.ObjectId },
  userId: { type: Schema.ObjectId },
  loanNo: { type: String },
  partner: { type: String },
  partnerId: { type: Schema.ObjectId },
  merchantCode: { type: String },
  merchantType: { type: String },
  trxnId: { type: String },
  productId: { type: String },
  productType: { type: String },
  amount: { type: Number },
  trancheNo: { type: String },
  purpose: { type: String },
  description: { type: String },
  maturity: { type: Number },
  interestRate: { type: Number },
  currency: { type: String },
  campaignTime: { type: String },
  loanType: { type: String },
  repaymentFrequency: { type: String },
  additionalDescription: { type: String },
  isMaturityMonth: { type: Boolean, default: true },
  emiAmount: { type: Number, default: null },
  interestType: { type: String },
  delinquencyString: { type: String },
  lenderDetails: [{
    lenderId: Schema.ObjectId,
    lenderPartnerId: Schema.ObjectId,
    amount: { type: Number },
    investedOn: { type: Date, default: Date.now, format: "date-time" },
    lenderCustId: { type: String },
    lenderLoanId: { type: String },
    lenderLeadId: { type: String },
    lenderProductType: { type: String },
    responseFromLender: { type: String },
    trancheType: { type: String }
  }],
  flowDetails: [{
    Step: { type: String },
    changedBy: Schema.ObjectId,
    changedByRole: { type: String },
    insertedOn: { type: Date, format: "date-time" },
  }],
  loanContractDetails: {
    ipAddress: { type: String },
    isAccepted: { type: Boolean, default: false },
    emailedOn: { type: Date, format: "date-time" },
    acceptedOn: { type: Date, format: "date-time" },
    contractFile: { type: String },
    photoFileName: { type: String },
    photoFilePath: { type: String },
    contractUrl: { type: String },
    eNachUrl: { type: String },
    authMode: { type: String },
    redirectionLongUrl: { type: String },
    contractSuccessCallBackUrl: { type: String },
    eNachErrorCallBackUrl: { type: String }
  },
  disbursalDetails: {
    bankReferenceNo: { type: String },
    paymentMode: { type: String },
    dot: { type: String },
    bankCode: { type: String },
    disbursedBy: Schema.ObjectId,
    disbursedOn: { type: Date, format: "date-time" },
    channel: { type: String },
    isManual: { type: Boolean, default: true },
    stampingCharges: { type: Number },
    gstCharges: { type: Number },
    processingFee: { type: Number },//processing Fee Fundfina
    interestPreRecovered: { type: Number },
    totalChargesRecovered: { type: Number },
    disbursalAmount: { type: Number },
    emiDueDate: { type: Date, format: "date-time" },
    custProcessingFee: { type: Number },//processing Fee of Customer
    custGstCharges: { type: Number },
    custTotalChargesRecovered: { type: Number },
    partnerDisbursedOn: { type: Date, format: "date-time" }
  },
  emiDetails: [
    {
      emiNumber: { type: Number },
      interestAmount: { type: Number },
      principalRecovered: { type: Number },
      principalOutstanding: { type: Number },
      penalty: { type: Number },
      moratoriumInterest: { type: Number },
      rePaymentAmount: { type: Number },
      rePaymentDate: { type: Date, format: "date-time" },
      rePaymentActualDate: { type: Date, format: "date-time" },
      rePaymentStatus: { type: String },
      rePaymentReceivedDate: { type: Date, format: "date-time" },
      insertedOn: { type: Date, format: "date-time" },
      updatedOn: { type: Date, format: "date-time" },
      isPrincipalPaid: { type: Boolean, default: false }, //Only for Daily Loans
      principalPaidOn: { type: Date, format: "date-time" },
      rePaymentReceivedSource: { type: String },
      holdCollection: { type: Boolean, default: false }
    },
  ],
  isDraft: { type: Boolean, default: true },
  status: { type: String },
  postDisbursalStatus: { type: String },
  adminRemarks: { type: String },
  clientRemarks: { type: String },
  rejectDescription: { type: String },
  isActive: { type: Boolean, default: true },
  insertedOn: { type: Date, default: Date.now, format: "date-time" },
  updatedOn: { type: Date, format: "date-time" },
  signatureDetails: {
    name: { type: String }
  },
  lastStepCompleted: { type: String },
  lastStepCompletedAt: { type: Date },
  loanStepDetails: [{
    Step: { type: String },
    isCompleted: { type: Boolean, default: true },
    insertedOn: { type: Date, default: Date.now, format: "date-time" },
    completedOn: { type: Date, format: "date-time" },
  }],
  adminModifiedOn: { type: Date },
  ipAddress: { type: String },
  disbursalBankDetails: {
    bankName: { type: String },
    branch: { type: String },
    accountNo: { type: String },
    accountName: { type: String },
    ifsc: { type: String },
    accountType: { type: String },
    address: { type: String },
    city: { type: String },
    state: { type: String },
    insertedOn: { type: Date, default: Date.now, format: "date-time" },
    updatedOn: { type: Date, format: "date-time" }
  },
  primaryCollectionType: { type: String },
  secondaryCollectionType: { type: String },
  byPassEnach: { type: Boolean, default: false },
  isParentLoan: { type: Boolean, default: false },
  invoiceRequestId: { type: Schema.ObjectId },
  adhocDays: { type: Number },
  tenure: { type: Number },
  arrears: [{
    delinquency: { type: String },
    amount: { type: Number },
    insertedOn: { type: Date, format: "date-time" }
  }],
  disbursalError: Schema.Types.Mixed,
  disbursalStatus: { type: String },
  batchId: { type: String },
  isRedisbursal: { type: Boolean, default: false },
  isStp: { type: Boolean, default: false },
  discount: {
    campaignId: mongoose.Schema.ObjectId,
    discountAmount: { type: Number },
    actualInterestRate: { type: Number },
    discountedInterestRate: { type: Number },
    isPaid: { type: Boolean, default: false },
    paidOn: { type: Date },
    utrNo: { type: String },
    insertedOn: { type: Date, default: Date.now, format: "date-time" },
    updatedOn: { type: Date, format: "date-time" }
  },
  kycId: { type: Schema.ObjectId },
  campaignId: { type: Schema.ObjectId },
  teleCallingActions: [
    {
      outcome: { type: String },
      calledOn: { type: Date, default: Date.now, format: "date-time" },
      calledBy: { type: Schema.ObjectId },
      borrowerResponse: { type: String },
      date: { type: Date, format: "date-time" },
      //willApplyOn: { type: Date, format: "date-time" },
      notConnectedReason: { type: String }
    }
  ],
  note: { type: String },
  principalAmount: {
    collected: { type: Number },
    outstanding: { type: Number },
    settled: { type: Number }
  },
  interestAmount: {
    collected: { type: Number },
    outstanding: { type: Number },
    settled: { type: Number }
  },
  totalEmisCollected: { type: Number },
  penalty: { type: Number },
  enterprisePartnerFee: { type: Number },
  offerId: { type: Schema.ObjectId },
  processingFeePer: { type: Number },
  isCollectionTeamCalled: { type: Boolean },
  processingFeeAmount: { type: Number },
  isForceMandate: { type: Boolean, default: false },
  processingFeeAmount: { type: Number },
  tranche: {
    id: { type: String }
  },
  lastEmiPaidDate: { type: Date },
  nextEmiRepaymentDate: { type: Date },
  isSettled: { type: Boolean },
  settlement: {
    totalSettledAmount: { type: Number },
    principalSetteled: { type: Number },
    interestSetteled: { type: Number },
    fldgSetteledAmount: { type: Number },
    fldgSettledDate: { type: Date },
    lastSettlementDate: { type: Date },
    insertedOn: { type: Date },
    updateOn: { type: Date },
  },
  batchId: { type: String },
  batchIdUpdatedOn: { type: Date, format: 'date-time' },
  isMobileNumberVerified: { type: Boolean, default: false },
  isMobileNumberConfirmed: { type: Boolean, default: false },
  isAutoLogin: { type: Boolean, default: false },
  reviewReason: { type: String },
  isForeclosure: { type: Boolean, default: false },
  adjustedLoanInterest: { type: Number, default: 0 },
  foreclosureReason: { type: String },
  foreclosureBy: { type: Schema.ObjectId },
  foreclosureOn: { type: Date },
  loanFeeType: { type: String, default: "VERSION2" },
  usedGraceDays: { type: Number, default: 0 },
  lastRecoveryDate: { type: Date, format: 'date-time' },
  totalRecoveredAmount: { type: Number, default: 0 }
});

module.exports.LoanInfoHistory = mongoose.model("LoanInfoHistory", loanInfoHistorySchema, "LoanInfoHistory");

const loanIdRepositorySchema = new mongoose.Schema({
  loanId: { type: Number },
});

module.exports.LoanIdRepository = mongoose.model("LoanIdRepository", loanIdRepositorySchema, "LoanIdRepository");

const invoiceRequestSchema = new mongoose.Schema({
  partnerId: { type: Schema.ObjectId },
  merchantCode: { type: String },
  invoiceId: { type: String },
  invoiceAmount: { type: Number },
  source: { type: String },
  dotTransaction: { type: Date, format: "date-time" },
  offerId: { type: String },
  deliveryAgent: { type: String },
  deliveryAgentMobile: { type: String },
  items: [{
    item: { type: String },
    quantity: { type: Number },
    amount: { type: Number }
  }],
  message: { type: String },
  isActive: { type: Boolean, default: true },
  isSaved: { type: Boolean, default: false },
  insertedBy: { type: Schema.ObjectId },
  insertedOn: { type: Date, default: Date.now, format: "date-time" },
  updatedOn: { type: Date, format: "date-time" },
});
module.exports.InvoiceRequests = mongoose.model("InvoiceRequests", invoiceRequestSchema, "InvoiceRequests");

const MiscellaneousSchema = new mongoose.Schema({
  type: { type: String },
  data: [
    {
      loanId: { type: Schema.ObjectId },
      offerId: { type: Schema.ObjectId },
      otherActiveLoans: { type: Number }
    }
  ]
});

module.exports.Miscellaneous = mongoose.model("Miscellaneous", MiscellaneousSchema, "Miscellaneous");


const TeleCallingAuditSchema = new mongoose.Schema({
  loanId: { type: Schema.ObjectId },
  userId: { type: Schema.ObjectId },
  loanNo: { type: String },
  partner: { type: String },
  partnerId: { type: Schema.ObjectId },
  merchantCode: { type: String },
  merchantType: { type: String },
  teleCallingActions: [
    {
      outcome: { type: String },
      calledOn: { type: Date, default: Date.now, format: "date-time" },
      calledBy: { type: Schema.ObjectId },
      borrowerResponse: { type: String },
      date: { type: Date, format: "date-time" },
      amount: { type: Number },
      notConnectedReason: { type: String },
      mobileNo: { type: String }
    }
  ],
  note: { type: String },
  insertedOn: { type: Date, default: Date.now, format: "date-time" },
  updatedOn: { type: Date, format: "date-time" },
  status: { type: String },
  disbursedOn: { type: Date, format: "date-time" },
  productType: { type: String },
  amount: { type: Number },
  adhocDays: { type: Number },
  tenure: { type: Number },
  lenderDetails: [{
    lenderId: Schema.ObjectId,
    lenderPartnerId: Schema.ObjectId,
    amount: { type: Number },
    investedOn: { type: Date, default: Date.now, format: "date-time" },
    lenderCustId: { type: String },
    lenderLoanId: { type: String },
    lenderLeadId: { type: String },
    lenderProductType: { type: String },
    trancheType: { type: String }
  }],
  rePaymentDate: { type: Date, format: "date-time" }
});

module.exports.TeleCallingAudit = mongoose.model("TeleCallingAudit", TeleCallingAuditSchema, "TeleCallingAudit");

const updateBankDetailHistorySchema = new mongoose.Schema({
  loanId: { type: Schema.ObjectId },
  insertedBy: { type: Schema.ObjectId },
  insertedOn: { type: Date, default: Date.now, format: "date-time" },
  updatedOn: { type: Date, format: "date-time" },
});
module.exports.updateBankDetailHistory = mongoose.model("UpdateBankDetailHistory", updateBankDetailHistorySchema, "UpdateBankDetailHistory");

const loanInstallmentsSchema = new mongoose.Schema({
  loanId: { type: Schema.ObjectId },
  loanNo: { type: String },
  installmentNumber: { type: Number },
  partnerId: { type: Schema.ObjectId },
  installmentPrincipal: { type: Number },
  initialPrincipalOutstanding: { type: Number },
  installmentInterest: { type: Number },
  adjustedInterest: { type: Number, default: 0 },
  installmentAmount: { type: Number },
  moratoriumInterest: { type: Number },
  dueDate: { type: Date, format: "date-time" },
  actualDueDate: { type: Date, format: "date-time" },
  channel: { type: String },
  status: { type: String },
  holdCollectionViaWallet: { type: Boolean, default: false },
  lastRepaymentRequestedDate: { type: Date, format: "date-time" },
  principalReceived: { type: Number },
  interestReceived: { type: Number },
  moratoriumInterestReceived: { type: Number },
  principalOutstanding: { type: Number },
  interestOutstanding: { type: Number },
  moratoriumInterestOutstanding: { type: Number },
  transactionDate: { type: Date, format: "date-time" },
  lendingPartnerUTR: { type: String },
  lendingPartnerSettlementDate: { type: Date, format: "date-time" },
  insertedOn: { type: Date, default: Date.now, format: "date-time" },
  isPrincipalPaid: { type: Boolean, default: false }, //Only for Daily Loans
  principalPaidOn: { type: Date, format: "date-time" },//Only for Daily Loans
  updatedOn: { type: Date, format: "date-time" },
  updatedBy: { type: String },
  referenceNo: { type: String },
  holdCollectionStartDate: { type: Date, format: "date-time" },
  holdCollectionEndDate: { type: Date, format: "date-time" },
  isForeclosure: { type: Boolean, default: false },
  productType: { type: String },
  enterprisePartnerSettlementInfo: { type: Object },
  enterprisePartnerSettlementStatus: { type: String },
  enterprisePartnerSettlementDate: { type: Date, format: "date-time" },
  enterprisePartnerSettlementUpdatedBy: { type: String },
  enterprisePartnerSettlementUpdatedOn: { type: Date, format: "date-time" },
  isRecovered: { type: Boolean, default: false },
  recoveryDate: { type: Date, format: "date-time" }
});

module.exports.LoanInstallments = mongoose.model("LoanInstallments", loanInstallmentsSchema, "LoanInstallments");

const loanFeeSchema = new mongoose.Schema({
  loanInstallmentId: { type: Schema.ObjectId },
  loanId: { type: Schema.ObjectId },
  loanNo: { type: String },
  installmentNumber: { type: Number },
  channel: { type: String },
  feeType: { type: String }, //Late Fee
  feeAmount: { type: Number },
  status: { type: String },
  holdCollectionViaWallet: { type: Boolean, default: false },
  feeAmountReceived: { type: Number },
  feeAmountOutstanding: { type: Number },
  feeDueDate: { type: Date, format: "date-time" },
  feeActualDueDate: { type: Date, format: "date-time" },
  lastFeeRepaymentRequestedDate: { type: Date, format: "date-time" },
  feeCollectedDate: { type: Date, format: "date-time" },
  isWaiveOff: { type: Boolean, default: false },
  insertedOn: { type: Date, default: Date.now, format: "date-time" },
  updatedOn: { type: Date, format: "date-time" },
  updatedBy: { type: String },
  referenceNo: { type: String },
  numberOfDays: { type: Number },

  enterprisePartnerSettlementInfo: { type: Object },
  enterprisePartnerSettlementStatus: { type: String },
  enterprisePartnerSettlementDate: { type: Date, format: "date-time" },
  enterprisePartnerSettlementUpdatedBy: { type: String },
  enterprisePartnerSettlementUpdatedOn: { type: Date, format: "date-time" }

});

module.exports.LoanFees = mongoose.model("LoanFees", loanFeeSchema, "LoanFees");

const holdCollectionLogSchema = new mongoose.Schema({
  loanId: { type: Schema.ObjectId },
  holdFlag: { type: Boolean },
  installmentNumbers: { type: String },
  Type: { type: String },
  holdBy: { type: String },
  insertedOn: { type: Date, default: Date.now, format: "date-time" }
});
module.exports.HoldCollectionLog = mongoose.model("HoldCollectionLog", holdCollectionLogSchema, "HoldCollectionLog");
//--------------------------------------------------Validations--------------------------------------------------//

module.exports.validateLoan = function (model) {
  const loan = joi.object({
    loanId: joi.string().allow("").required(),
    amount: joi.number().min(5000).required(),
    purpose: joi.string().min(3).max(100).required(),
    description: joi.string().min(3).max(100).required(),
    repaymentFrequency: joi.string().min(3).max(255).required(),
    campaignTime: joi.string().allow("").optional(),
    ipAddress: joi.string().allow("").optional(),
    partnerId: joi.string().allow("").optional(),
    isStp: joi.boolean().allow("", null, false).optional()
  });

  return loan.validate(model);
};

module.exports.validateAdditionDescription = function (model) {
  const loan = joi.object({
    additionalDescription: joi.string().max(200).allow("").optional(),
  });

  return joi.validate(model, loan);
};

module.exports.validateLoanStatus = function (model) {
  const loan = joi.object({
    loanStatus: joi.string().min(2).max(100).required(),
    rejectReason: joi.string().allow("", null),
    rejectDescription: joi.string().allow("", null)
  });

  return loan.validate(model);
}

const documentLog = new mongoose.Schema({
  loanId: { type: Schema.ObjectId },
  type: { type: String },
  value1: { type: String },
  value2: { type: String },
  insertedOn: { type: Date }
});

module.exports.DocumentLog = mongoose.model("DocumentLog", documentLog, "DocumentLog");

const photoMatchLog = new mongoose.Schema({
  loanId: { type: Schema.ObjectId },
  userId: { type: Schema.ObjectId },
  token: { type: String },
  url: { type: String },
  responseRaw: { type: String },
  responseObj: { type: Object },
  insertedOn: { type: Date, default: new Date() },
  updatedOn: { type: Date, default: new Date() },
  provider: { type: String, default: "SIGNZY" }
});

module.exports.PhotoMatchLog = mongoose.model("PhotoMatchLog", photoMatchLog, "PhotoMatchLog");
