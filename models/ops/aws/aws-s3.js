let AWS = require("aws-sdk"); // Keep for backward compatibility
let Config = require("config");
let fs = require("fs");
const { PDFDocument } = require('pdf-lib');
const { Storage } = require('@google-cloud/storage'); // Add GCP Storage library
const path = require('path');
const { DocumentProcessorServiceClient } = require('@google-cloud/documentai').v1;

const gcpKeyFilePath = path.resolve(__dirname, '../../../ff-madad-inv-194e94a01626.json'); // Use path.resolve
console.log(`DEBUG: Attempting to load key file from: ${gcpKeyFilePath}`); // Add log to check path

const docAiClient = new DocumentProcessorServiceClient({
  keyFilename: gcpKeyFilePath, // Use keyfile if exists
  apiEndpoint: 'eu-documentai.googleapis.com'
});

// Configure aws with credentials (keeping for backward compatibility)
AWS.config.region = Config.get("awsCredentials.awsRegion");
AWS.config.update({
  accessKeyId: Config.get("awsCredentials.accessId"),
  secretAccessKey: Config.get("awsCredentials.accessKey"),
  signatureVersion: 'v4',
});

// AWS variables (keeping for backward compatibility)
const bucketName = Config.get("awsCredentials.bucketName");
const bankStatementsBucketName = Config.get("awsCredentials.bankStatementsBucketName");
const BankStatementsBucket = Config.get("BankStatements.BankStatementsBucket");
const expireSeconds = Config.get("awsCredentials.signUrlTimeout");
const s3 = new AWS.S3();

// Configure Google Cloud Storage
const gcpConfig = {
  "projectId": "ff-madad-inv",
  "bucketName": "madad_inv",
  "bankStatementsBucket": "madad_inv",
  "signUrlTimeout": *********
};
const gcpStorage = new Storage({
  keyFilename: path.join(__dirname, '../../../ff-madad-inv-194e94a01626.json'),
  projectId: gcpConfig.projectId
});

// GCP bucket names
const gcpBucketName = gcpConfig.bucketName;
const gcpBankStatementsBucket = gcpConfig.bankStatementsBucket;

/**
 * General upload function for S3 (for backward compatibility)
 * @param {string} filename - Name of file to upload
 * @param {string} filePath - Path to file
 * @returns {Promise<Object>} Upload response from S3
 */


// --- Assume these are defined/required elsewhere ---
// let Config = require("config");
// const { Storage } = require('@google-cloud/storage');
// const path = require('path');
// const { DocumentProcessorServiceClient } = require('@google-cloud/documentai').v1;
// const gcpConfig = { projectId: 'ff-madad-inv', /* ... */ };
// const docAiClient = new DocumentProcessorServiceClient({ /* ... */ });

// --- Helper Functions (Essential for Bank Statement Parsing) ---

/**
 * Extracts text from a Document AI text anchor.
 * @param {object} textAnchor - The textAnchor object from the Document AI response.
 * @param {string} fullText - The full text of the document.
 * @returns {string} The extracted text, trimmed and with normalized whitespace.
 */
const getText = (textAnchor, fullText) => {
  if (!textAnchor?.textSegments || !fullText) {
    return '';
  }
  let text = '';
  for (const segment of textAnchor.textSegments) {
    const startIndex = segment.startIndex ? parseInt(segment.startIndex.toString(), 10) : 0;
    // Ensure endIndex doesn't exceed fullText length
    const endIndex = segment.endIndex ? Math.min(parseInt(segment.endIndex.toString(), 10), fullText.length) : fullText.length;
    if (startIndex < endIndex) { // Add check for valid range
      text += fullText.substring(startIndex, endIndex);
    }
  }
  return text.replace(/\s+/g, ' ').trim();
};

/**
 * Safely parses monetary amounts from text.
 * Removes currency symbols, thousand separators, and handles negative signs.
 * Assumes '.' as the decimal separator.
 * @param {string} text - The text containing the amount.
 * @returns {number} The parsed amount as a float, or 0 if parsing fails.
 */
const parseAmount = (text) => {
  if (typeof text !== 'string' || !text) return 0;
  // Remove currency symbols (common ones), thousand separators (commas, spaces), and whitespace
  // Keep digits, period (decimal separator), and hyphen (for negative)
  const cleaned = text.replace(/[^\d.-]/g, '');

  // Handle potential trailing minus for some accounting formats (e.g., "100.00-")
  if (cleaned.endsWith('-')) {
    // Ensure it's not just "-"
    if (cleaned.length > 1) {
      return parseFloat('-' + cleaned.slice(0, -1)) || 0;
    } else {
      return 0; // Just a hyphen is not a valid number
    }
  }
  // Handle cases like ".-" or "--" which are not valid numbers
  if (isNaN(parseFloat(cleaned))) {
    return 0;
  }
  return parseFloat(cleaned) || 0; // Return 0 if parsing results in NaN
};

/**
 * Placeholder for mime type detection - replace with your actual implementation
 * @param {string} gcsUri - The GCS URI of the file.
 * @returns {string|null} The inferred mime type or null.
 */
function getContentType(gcsUri) {
  // Basic example: infer from extension (replace with robust logic if needed)
  if (gcsUri.toLowerCase().endsWith('.pdf')) return 'application/pdf';
  if (gcsUri.toLowerCase().endsWith('.tiff') || gcsUri.toLowerCase().endsWith('.tif')) return 'image/tiff';
  if (gcsUri.toLowerCase().endsWith('.jpg') || gcsUri.toLowerCase().endsWith('.jpeg')) return 'image/jpeg';
  if (gcsUri.toLowerCase().endsWith('.png')) return 'image/png';
  console.warn(`Cannot infer mime type from URI: ${gcsUri}. Defaulting might occur.`);
  return null; // Let the calling code handle the default
}

function formatDocumentBankStatementResponse(entities) {
  const result = {};
  const transactions = [];
  const tempFieldValues = {}; // Stores type -> Set<string>


   // NEW: Initialize total deposits and withdrawals
   let totalDeposits = 0;
   let totalWithdrawals = 0;

  if (!entities || !Array.isArray(entities)) {
      return {};
  }


  // Phase 1: Initial collection of unique values and building transactions array
  for (const entity of entities) {
      if (!entity || typeof entity.type !== 'string') {
          continue;
      }
      const type = entity.type.trim();
      if (!type) {
          continue;
      }

      const rawMentionText = (typeof entity.mentionText === 'string') ? entity.mentionText : '';
      let valueText = rawMentionText;
      if (entity.normalizedValue &&
          entity.normalizedValue.text !== undefined &&
          entity.normalizedValue.text !== null) {
          valueText = String(entity.normalizedValue.text);
      }
      const value = valueText.trim();

      if (type === 'table_item') {
          const transactionRow = {};
          if (entity.properties && Array.isArray(entity.properties)) {
              for (const prop of entity.properties) {
                  if (!prop || typeof prop.type !== 'string') {
                      continue;
                  }
                 
                  const propType = prop.type.trim();
                  if (!propType) {
                      continue;
                  }
                  const propRawMentionText = (typeof prop.mentionText === 'string') ? prop.mentionText : '';
                  let propValueText = propRawMentionText;
                  if (prop.normalizedValue &&
                      prop.normalizedValue.text !== undefined &&
                      prop.normalizedValue.text !== null) {
                      propValueText = String(prop.normalizedValue.text);
                  }
                  const propValue = propValueText.trim();
                  transactionRow[propType] = propValue;
              }
          }
          if (Object.keys(transactionRow).length > 0) {
              transactions.push(transactionRow);
          }
      } else {
          if (value !== '') {
              if (!tempFieldValues[type]) {
                  tempFieldValues[type] = new Set();
              }
              tempFieldValues[type].add(value);
          }
      }
  }

  // --- NEW: Calculate total deposits and withdrawals from transactions ---
  for (const transaction of transactions) {
    const depositAmountStr = transaction['table_item/transaction_deposit'];
    if (depositAmountStr && typeof depositAmountStr === 'string' && depositAmountStr.trim() !== '') {
        // Assuming parseAmount is available and handles various numeric string formats
        const amount = parseAmount(depositAmountStr); 
        totalDeposits += Math.abs(amount); // Deposits are usually positive, abs for safety
    }

    const withdrawalAmountStr = transaction['table_item/transaction_withdrawal'];
    if (withdrawalAmountStr && typeof withdrawalAmountStr === 'string' && withdrawalAmountStr.trim() !== '') {
        // Assuming parseAmount is available
        const amount = parseAmount(withdrawalAmountStr);
        totalWithdrawals += Math.abs(amount); // Sum absolute values for total withdrawal amount
    }
}

  // --- Derive statement_start_date and statement_end_date from transactions ---
  const allParsedTransactionDates = [];

  const parseDateString = (dateStr) => {
      if (!dateStr || typeof dateStr !== 'string') return null;

      // Try YYYY-MM-DD (this format is directly parsable and common for normalized dates)
      if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
          const parts = dateStr.split('-');
          const year = parseInt(parts[0], 10);
          const month = parseInt(parts[1], 10); // Month is 1-12
          const day = parseInt(parts[2], 10);   // Day is 1-31

          // Basic sanity check for date components
          if (year > 1000 && month >= 1 && month <= 12 && day >= 1 && day <= 31) {
              const d = new Date(year, month - 1, day); // JS Date month is 0-11
              // Verify that the JS Date object reflects the input, avoiding issues like Feb 30 becoming Mar 2
              if (d.getFullYear() === year && d.getMonth() === month - 1 && d.getDate() === day) {
                  return d;
              }
          }
      }

      // Try DD/MM/YYYY
      if (/^\d{1,2}\/\d{1,2}\/\d{4}$/.test(dateStr)) {
          const parts = dateStr.split('/');
          const day = parseInt(parts[0], 10);
          const month = parseInt(parts[1], 10);
          const year = parseInt(parts[2], 10);
           if (year > 1000 && month >= 1 && month <= 12 && day >= 1 && day <= 31) {
              // Assuming DD/MM/YYYY. If MM/DD/YYYY is possible, this needs adjustment or a more robust parser.
              const d = new Date(year, month - 1, day);
              if (d.getFullYear() === year && d.getMonth() === month - 1 && d.getDate() === day) {
                  return d;
              }
          }
      }
    
      return null;
  };

  for (const transaction of transactions) {
      // Use the exact keys as shown in your transaction data sample
      const depositDateStr = transaction['table_item/transaction_deposit_date'];
      const withdrawalDateStr = transaction['table_item/transaction_withdrawal_date'];

      if (depositDateStr) {
          const parsedDate = parseDateString(depositDateStr);
          if (parsedDate) allParsedTransactionDates.push(parsedDate);
      }
      if (withdrawalDateStr) {
          const parsedDate = parseDateString(withdrawalDateStr);
          if (parsedDate) allParsedTransactionDates.push(parsedDate);
      }
  }

  let minTransactionDate = null;
  let maxTransactionDate = null;

  if (allParsedTransactionDates.length > 0) {
      // Ensure all dates are valid before getting time
      const validDates = allParsedTransactionDates.filter(d => d instanceof Date && !isNaN(d.getTime()));
      if (validDates.length > 0) {
          minTransactionDate = new Date(Math.min(...validDates.map(date => date.getTime())));
          maxTransactionDate = new Date(Math.max(...validDates.map(date => date.getTime())));
      }
  }
  // --- End of date derivation logic ---

  // Phase 2: Heuristics for IBAN and Currency
  const workingFieldValues = {};
  for (const key in tempFieldValues) {
      if (tempFieldValues.hasOwnProperty(key)) {
          workingFieldValues[key] = new Set(tempFieldValues[key]);
      }
  }

  const qatariIbanRegex = /^QA\d{2}[A-Z0-9]{25}$/;
  if (workingFieldValues['account_number'] instanceof Set) {
      const originalAccountNumbers = workingFieldValues['account_number'];
      const newAccountNumbers = new Set();
      if (!workingFieldValues['iban']) {
          workingFieldValues['iban'] = new Set();
      }
      for (const accNum of originalAccountNumbers) {
          const cleanedAccNum = accNum.replace(/[()\s-]/g, '');
          if (qatariIbanRegex.test(cleanedAccNum)) {
              workingFieldValues['iban'].add(cleanedAccNum);
          } else {
              newAccountNumbers.add(accNum);
          }
      }
      if (newAccountNumbers.size > 0) {
          workingFieldValues['account_number'] = newAccountNumbers;
      } else {
          delete workingFieldValues['account_number'];
      }
  }
  
  const currencyKeywords = ['QATARI RIYAL', 'RIYAL', 'QAR', 'USD', 'US DOLLAR', 'EUR', 'EURO', 'AED', 'SAR'];
  if (workingFieldValues['account_type'] instanceof Set) {
      const originalAccountTypes = workingFieldValues['account_type'];
      const newAccountTypes = new Set();
      if (!workingFieldValues['currency']) {
          workingFieldValues['currency'] = new Set();
      }
      for (const accType of originalAccountTypes) {
          let isCurrency = false;
          const upperAccType = accType.toUpperCase();
          for (const keyword of currencyKeywords) {
              if (upperAccType.includes(keyword.toUpperCase())) {
                  workingFieldValues['currency'].add(accType);
                  isCurrency = true;
                  break;
              }
          }
          if (!isCurrency) {
              newAccountTypes.add(accType);
          }
      }
      if (newAccountTypes.size > 0) {
          workingFieldValues['account_type'] = newAccountTypes;
      } else {
          delete workingFieldValues['account_type'];
      }
  }
  
  // Phase 3: Finalize the result object
  for (const type in workingFieldValues) {
      if (type === 'statement_start_date' || type === 'statement_end_date') {
          continue; 
      }
      if (workingFieldValues.hasOwnProperty(type) && workingFieldValues[type] instanceof Set) {
          const uniqueValuesArray = Array.from(workingFieldValues[type]);
          if (uniqueValuesArray.length > 0) {
              result[type] = uniqueValuesArray[0];
          }
      }
  }

  const formatDateToYYYYMMDD = (dateObj) => {
      if (!dateObj || !(dateObj instanceof Date) || isNaN(dateObj.getTime())) return null;
      const year = dateObj.getFullYear();
      const month = String(dateObj.getMonth() + 1).padStart(2, '0');
      const day = String(dateObj.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
  };

  result.statement_start_date = formatDateToYYYYMMDD(minTransactionDate);
  result.statement_end_date = formatDateToYYYYMMDD(maxTransactionDate);

  // NEW: Add calculated totals to the result, formatted to 2 decimal places
  result.total_deposits = parseFloat(totalDeposits.toFixed(2));
  result.total_withdrawals = parseFloat(totalWithdrawals.toFixed(2));

  if (transactions.length > 0) {
      result.transactions = transactions;
  }

  return result;
}

// --- Helper function to download from GCS as Buffer ---
async function downloadGcsFileAsBuffer(gcsUri, storageClient) {
  console.log(`Downloading GCS file: ${gcsUri}`);
  const [bucketName, ...filePathParts] = gcsUri.replace('gs://', '').split('/');
  const fileName = filePathParts.join('/');
  
  const [contents] = await storageClient.bucket(bucketName).file(fileName).download();
  console.log(`Successfully downloaded ${gcsUri} (Size: ${contents.length} bytes)`);
  return contents;
}

// --- Helper function to upload buffer to GCS ---
async function uploadBufferToGcs(buffer, bucketName, destinationFileName, mimeType, storageClient) {
  console.log(`Uploading buffer to gs://${bucketName}/${destinationFileName}`);
  const file = storageClient.bucket(bucketName).file(destinationFileName);
  await file.save(buffer, { contentType: mimeType });
  const gcsPath = `gs://${bucketName}/${destinationFileName}`;
  console.log(`Successfully uploaded to ${gcsPath}`);
  return gcsPath;
}

async function extractDocumentFields({
  gcsUri,
  processorId, // This ID determines which logic path is effectively used
  documentType,
  projectId = gcpConfig.projectId,
  location = 'eu',
  mimeType = null
}) {
  // --- Parameter Validation (Remains the same) ---
  if (!gcsUri || !processorId || !projectId || !location || !documentType) {
    console.error('Missing required parameters for extractDocumentFields:', { gcsUri, processorId, documentType, projectId, location });
    return { error: 'Missing required parameters (gcsUri, processorId, documentType, projectId, location).', data: null };
  }
  if (!gcsUri.startsWith('gs://')) {
    console.error(`Invalid GCS URI format: ${gcsUri}. Must start with gs://`);
    return { error: 'Invalid GCS URI format. Must start with gs://', data: null };
  }

  // --- Request Setup (Remains the same) ---
  const name = `projects/${projectId}/locations/${location}/processors/${processorId}`;
  console.log(`Processing document ${gcsUri} of type ${documentType} with processor: ${name}`);
  const inferredMimeType = mimeType || getContentType(gcsUri);
  // Default to application/pdf if inference fails or not provided
  const effectiveMimeType = inferredMimeType || 'application/pdf';
  if (!inferredMimeType) {
    console.warn(`Could not reliably determine mime type for ${gcsUri}. Defaulting to ${effectiveMimeType}. Provide explicitly if different.`);
  }

  let document;

  // Configuration for PDF splitting
  const MAX_PAGES_PER_CHUNK = 15; 
  try {
    if (documentType === 'Bank Statement') {

      let shouldSplit = false;
      let pdfDocInstanceForSplitting;
      let originalPdfFileBytes; // To store downloaded PDF bytes for reuse

      try {
        console.log(`Checking page count for Bank Statement: ${gcsUri}`);
        originalPdfFileBytes = await downloadGcsFileAsBuffer(gcsUri, gcpStorage);
        pdfDocInstanceForSplitting = await PDFDocument.load(originalPdfFileBytes);
        const numberOfPages = pdfDocInstanceForSplitting.getPageCount();
        
        if (numberOfPages > MAX_PAGES_PER_CHUNK) {
          shouldSplit = true;
          console.log(`Bank Statement ${gcsUri} has ${numberOfPages} pages. Splitting for synchronous processing (max ${MAX_PAGES_PER_CHUNK} pages/chunk).`);
        } else {
          console.log(`Bank Statement ${gcsUri} has ${numberOfPages} pages. No splitting needed. Will process synchronously.`);
        }
      } catch (pdfLoadError) {
        console.error(`Failed to load Bank Statement PDF ${gcsUri} to get page count: ${pdfLoadError.message}. Attempting to process as a single document.`, pdfLoadError);
        shouldSplit = false; 
      
        // For simplicity, if load fails, originalPdfFileBytes might be undefined.
        if (!originalPdfFileBytes) { // Attempt to download again if initial load failed but we proceed
            try {
                originalPdfFileBytes = await downloadGcsFileAsBuffer(gcsUri, gcpStorage);
            } catch (e) {
                console.error(`Failed to download PDF ${gcsUri} for single synchronous processing after initial load failure.`, e);
                throw e; // Rethrow if download also fails here
            }
        }
      }

      if (shouldSplit && pdfDocInstanceForSplitting) {
        const aggregatedEntities = [];
        const aggregatedTables = [];
        const aggregatedTexts = [];
        let cumulativePageOffset = 0; // To adjust page numbers from chunks

        const totalPages = pdfDocInstanceForSplitting.getPageCount();
        console.log(`Splitting PDF into chunks for synchronous processing. Total pages: ${totalPages}`);

        for (let pageStartIndex = 0; pageStartIndex < totalPages; pageStartIndex += MAX_PAGES_PER_CHUNK) {
          const chunkNumber = Math.floor(pageStartIndex / MAX_PAGES_PER_CHUNK) + 1;
          console.log(`Processing chunk ${chunkNumber} (pages ${pageStartIndex + 1} - ${Math.min(pageStartIndex + MAX_PAGES_PER_CHUNK, totalPages)})`);
          
          const subDocument = await PDFDocument.create();
          const endIndex = Math.min(pageStartIndex + MAX_PAGES_PER_CHUNK, totalPages);
          const pageIndicesToCopy = Array.from({ length: endIndex - pageStartIndex }, (_, k) => pageStartIndex + k);

          if (pageIndicesToCopy.length === 0) continue;

          const copiedPages = await subDocument.copyPages(pdfDocInstanceForSplitting, pageIndicesToCopy);
          copiedPages.forEach(page => subDocument.addPage(page));
          
          const subDocumentBytes = await subDocument.save();

          console.log(`Sending chunk ${chunkNumber} (size: ${subDocumentBytes.length} bytes) for synchronous processing.`);
          const requestChunk = {
            name, // Processor name
            inlineDocument: {
              content: Buffer.from(subDocumentBytes).toString('base64'),
              mimeType: 'application/pdf',
            },
           
          };

          const [responseChunk] = await docAiClient.processDocument(requestChunk);
          const currentChunkDoc = responseChunk.document;

          if (currentChunkDoc) {
            const numPagesInThisChunk = currentChunkDoc.pages ? currentChunkDoc.pages.length : pageIndicesToCopy.length;
            console.log(`Chunk ${chunkNumber} processed. Found ${numPagesInThisChunk} pages in chunk response.`);

            // Adjust page numbers and aggregate entities
            (currentChunkDoc.entities || []).forEach(entity => {
              const newEntity = JSON.parse(JSON.stringify(entity)); // Deep copy
              if (newEntity.pageAnchor?.pageRefs) {
                newEntity.pageAnchor.pageRefs.forEach(ref => {
                  if (typeof ref.page === 'number') {
                    ref.page += cumulativePageOffset;
                  }
                });
              }
              aggregatedEntities.push(newEntity);
            });

            // Adjust page numbers and aggregate tables
            (currentChunkDoc.tables || []).forEach(table => {
              const newTable = JSON.parse(JSON.stringify(table)); // Deep copy
              if (newTable.layout?.pageAnchor?.pageRefs) {
                newTable.layout.pageAnchor.pageRefs.forEach(ref => {
                  if (typeof ref.page === 'number') {
                    ref.page += cumulativePageOffset;
                  }
                });
              }
              aggregatedTables.push(newTable);
            });

            aggregatedTexts.push(currentChunkDoc.text || '');
            cumulativePageOffset += numPagesInThisChunk;
            console.log(`Aggregated results from chunk ${chunkNumber}. Cumulative page offset: ${cumulativePageOffset}`);
          } else {
            console.warn(`Chunk ${chunkNumber} processing returned no document object.`);
          }
        }

        document = { // Name used in the rest of your code
          entities: aggregatedEntities,
          tables: aggregatedTables,
          text: aggregatedTexts.join('\n\n'), // Concatenate text
          // Note: Reconstructing the full 'pages' array is complex and often not needed if entities/tables have adjusted page refs.
        };
        console.log("Successfully aggregated results from all PDF chunks using synchronous processing.");

      } else { // Not splitting OR pdfDocInstanceForSplitting was not available (e.g. initial load failed)
        console.log(`INFO: Using single SYNCHRONOUS processing for Bank Statement: ${gcsUri}`);
        if (!originalPdfFileBytes) { // If initial load failed, try downloading again
            console.log(`Original PDF bytes not available, re-downloading for single synchronous processing...`);
            originalPdfFileBytes = await downloadGcsFileAsBuffer(gcsUri, gcpStorage);
        }

        const requestSingle = {
          name,
          inlineDocument: {
            content: Buffer.from(originalPdfFileBytes).toString('base64'),
            mimeType: 'application/pdf',
          },
        };
        
        console.log('Sending single document for synchronous processing...');
        const [responseSingle] = await docAiClient.processDocument(requestSingle);
        document = responseSingle.document; // Name used in the rest of your code
        console.log("Successfully processed document using single synchronous call.");
      }
    }
      else { // For non-Bank Statement types, use single document processing
      console.log(`INFO: Using SINGLE document processing for ${documentType}: ${gcsUri}`);
      const request = {
        name,
        gcsDocument: {
          gcsUri: gcsUri,
          mimeType: effectiveMimeType,
        },
        // processOptions: { ocrConfig: { /* ... */ } }
      };
      console.log('Sending request to Document AI...');
      const [result] = await docAiClient.processDocument(request);
      document = result.document; // Assign to the shared document variable
    }
    // --- Document AI API Call (Remains the same) ---
    // console.log('Sending request to Document AI...');
    // const [result] = await docAiClient.processDocument(request);
    // const { document } = result;
    // console.log("resssssssssssssssssssssss", processorId, documentType);
    // --- Basic Document Check (Remains the same) ---
    // Note: Bank Statement Parser might return a document object even without top-level entities if it finds tables.
    if (!document) {
      console.warn(`Document AI response for ${gcsUri} did not contain a document object.`);
      return { error: 'No document object found in the response.', data: null };
    }

    // --- Initialize Variables ---
    let extractedData = {};
    const allEntities = document.entities || [];
    const allTables = document.tables || [];
    const fullText = document.text || ''; // Get full text for helper function

    console.log(`Document AI processing complete for ${gcsUri}. Found ${allEntities.length} entities and ${allTables.length} tables.`);

    // --- Get Entity Value Helper (Specific to this function's scope) ---
    // This helper prioritizes mentionText, suitable for custom extractors.
    // The Bank Statement logic below will use its own getEntityValue if needed, or parse directly.
    const getEntityValueSimple = (entities, typeName) => {
      const entity = entities.find(e => e.type === typeName);
      // Prioritize mentionText for consistency with original code for non-bank statements
      return entity ? (entity.mentionText || '').replace(/\s+/g, ' ').trim() : null;
    };

    // --- Document Type Specific Logic ---

    // ========================================================================
    // Logic for NON-BANK STATEMENT types (Using Custom Extractor Schema)
    // THIS SECTION REMAINS UNCHANGED FOR BACKWARD COMPATIBILITY
    // ========================================================================
    if (documentType === 'Commercial Registration') {

    const extractStructuredEntitiesCr = (entities) => {
  // Initialize arrays to hold the structured data
  const associatedParties = [];
  const branches = [];
  const businessActivities = [];

  // A helper to convert a TypeName to a camelCase key (e.g., "PartyName" -> "partyName")
  const toCamelCase = (str) => {
    if (!str) return '';
    return str.charAt(0).toLowerCase() + str.slice(1);
  };

  // A helper function to process a parent entity (like AssociatedParties)
  const processParentEntity = (parentEntity) => {
    const item = {};
    // Each parent entity has a 'properties' array holding its child fields
    for (const prop of parentEntity.properties) {
      // Clean up the value, replacing newlines and multiple spaces
      const value = (prop.mentionText || '').replace(/\s+/g, ' ').trim();
      // Convert the type to a camelCase key and assign the value
      item[toCamelCase(prop.type)] = value;
    }
    return item;
  };

  // Iterate over all entities to find the parent container entities
  for (const entity of entities) {
    switch (entity.type) {
      case 'AssociatedParties':
        associatedParties.push(processParentEntity(entity));
        break;
      case 'Branches':
        branches.push(processParentEntity(entity));
        break;
      case 'BusinessActivities':
        businessActivities.push(processParentEntity(entity));
        break;
      // Default case to ignore other entity types
      default:
        break;
    }
  }

  return {
    associatedParties,
    branches,
    businessActivities,
  };
};

      const extracted = extractStructuredEntitiesCr(allEntities);
      console.log(`DEBUG: Extracting ${documentType} fields using Custom Extractor logic.`);
      extractedData = {
        'commercial_reg_number': getEntityValueSimple(allEntities, 'RegistrationNumber'),
        'tax_reg_number': getEntityValueSimple(allEntities, 'TaxNumber'),
        'trade_name': getEntityValueSimple(allEntities, 'EntityName'),
        'issue_date': getEntityValueSimple(allEntities, 'DocumentIssueDate'),
        'creation_date': getEntityValueSimple(allEntities, 'RegistrationDate'),
        'expiry_date': getEntityValueSimple(allEntities, 'ExpiryDate'),
        'commercial_reg_status': getEntityValueSimple(allEntities, 'RegistrationStatus'),
        'mail_id': getEntityValueSimple(allEntities, 'mail_box'),
        'address': getEntityValueSimple(allEntities, 'RegisteredAddress'),
        'capital': getEntityValueSimple(allEntities, 'Capital'),
        'entityNationality': getEntityValueSimple(allEntities, 'EntityNationality'),
        'issuingAuthority': getEntityValueSimple(allEntities, 'IssuingAuthority'),
        'legalForm': getEntityValueSimple(allEntities, 'LegalForm'),
        'associatedParties': extracted?.associatedParties || [],
        'branches': extracted?.branches || [],
        'businessActivities': extracted?.businessActivities || []
      };
    } else if (documentType === 'Trade License') {
      console.log(`DEBUG: Extracting ${documentType} fields using Custom Extractor logic.`);
      extractedData = {
        'trade_license_number': getEntityValueSimple(allEntities, 'trade_license_number'),
        'trade_license_expiry_date': getEntityValueSimple(allEntities, 'trade_license_expiry_date'),
        'trade_license_issue_date': getEntityValueSimple(allEntities, 'trade_license_issue_date')
      };
    } else if (documentType === 'Tax Card') {
      console.log(`DEBUG: Extracting ${documentType} fields using Custom Extractor logic.`);
      extractedData = {
        'tin_number': getEntityValueSimple(allEntities, 'tin_number'),
        'trade_name': getEntityValueSimple(allEntities, 'trade_name')
      };
    } else if (documentType === 'Establishment Card') {
      console.log(`DEBUG: Extracting ${documentType} fields using Custom Extractor logic.`);
      extractedData = {
        'establishment_id': getEntityValueSimple(allEntities, 'establishment_id'),
        'establishment_name': getEntityValueSimple(allEntities, 'establishment_name'),
        'issue_date': getEntityValueSimple(allEntities, 'issue_date'),
        'expiry_date': getEntityValueSimple(allEntities, 'expiry_date'),
        'sector': getEntityValueSimple(allEntities, 'sector')
      };

      // ========================================================================
      // Logic for BANK STATEMENT type (Using Bank Statement Parser Schema)
      // THIS IS THE NEW/MODIFIED SECTION
      // ========================================================================
    } else if (documentType === 'Bank Statement') {
      console.log(`DEBUG: Extracting ${documentType} fields using Bank Statement Parser logic.`);
      console.log('Bank Statement', formatDocumentBankStatementResponse(allEntities));

      // --- Helper specific to Bank Statement Parser (prioritizes normalized value) ---
      const getBankEntityValue = (entities, typeName) => {
        const entity = entities.find(e => e.type === typeName);
        const normValue = entity?.normalizedValue?.text; // Normalized value is often better for dates/amounts
        const mentionText = entity?.mentionText;
        const value = normValue || mentionText || ''; // Use normalized first
        return value.replace(/\s+/g, ' ').trim();
      };

//       // const formattedBS = formatDocumentEntitiesJS(result);

//       function findItemByKey(key) {
//         return formattedBS?.find(item => item.type === key)?.mentionText || null;
//       }
//       // ... inside 'else if (documentType === 'Bank Statement')' block ...
//       // let ibanValue = getBankEntityValue(allEntities, 'iban');
//       // console.log(`DEBUG IBAN: Initial ibanValue from 'iban' type = '${ibanValue}'`);

//       // if (!ibanValue) {
//       //   console.log("DEBUG IBAN: Fallback: Looking for 'unique_code'.");
//       //   const uniqueCodeEntity = allEntities.find(e => e.type === 'unique_code');

//       //   if (uniqueCodeEntity) {
//       //     console.log("DEBUG IBAN: Fallback: Found uniqueCodeEntity:", JSON.stringify(uniqueCodeEntity));
//       //     if (uniqueCodeEntity.mentionText) {
//       //       console.log("DEBUG IBAN: Fallback: uniqueCodeEntity.mentionText =", uniqueCodeEntity.mentionText);
//       //       const rawMentionText = uniqueCodeEntity.mentionText;
//       //       const ibanMatch = rawMentionText.match(/[A-Z0-9]{2,}\b/);
//       //       if (ibanMatch && ibanMatch[0]) {
//       //         console.log("DEBUG IBAN: Fallback: Regex matched:", ibanMatch[0]);
//       //         ibanValue = ibanMatch[0];
//       //       } else {
//       //         console.error("DEBUG IBAN: Fallback: Regex did NOT match or ibanMatch[0] is empty. ibanMatch:", ibanMatch);
//       //       }
//       //     } else {
//       //       console.error("DEBUG IBAN: Fallback: uniqueCodeEntity.mentionText is MISSING.");
//       //     }
//       //   } else {
//       //     console.error("DEBUG IBAN: Fallback: 'unique_code' entity NOT FOUND in allEntities.");
//       //     // Optional: Log all available entity types if unique_code is missing
//       //     // const availableTypes = allEntities.map(e => e.type);
//       //     // console.log("DEBUG IBAN: Available entity types:", JSON.stringify(availableTypes));
//       //   }
//       // }
//       // console.log(`DEBUG IBAN: Final ibanValue before assignment = '${ibanValue}'`);


// function extractTransactionsFromEntities(allEntities, parseAmountFunc) {
//   const transactions = [];

//   if (!Array.isArray(allEntities)) {
//       console.warn("extractTransactionsFromEntities: allEntities is not an array.");
//       return transactions;
//   }
//   if (typeof parseAmountFunc !== 'function') {
//       console.warn("extractTransactionsFromEntities: parseAmountFunc is not a function. Monetary values might not be parsed correctly.");
//       // Fallback to a simple parseFloat, though this is less robust than your parseAmount
//       parseAmountFunc = (text) => parseFloat(text.replace(/[^0-9.-]/g, '')) || 0;
//   }

//   // Helper to get numeric amount from a 'moneyValue' object
//   const getAmountFromNormalizedMoneyValue = (moneyValue) => {
//       if (!moneyValue) return { amount: 0, currencyCode: null };
//       const units = parseFloat(moneyValue.units || "0");
//       const nanos = parseInt(moneyValue.nanos || "0", 10);
//       // nanos represents the fractional part, scaled by 10^9 (e.g., 420,000,000 for 0.42)
//       return {
//           amount: units + (nanos / 1_000_000_000),
//           currencyCode: moneyValue.currencyCode || null
//       };
//   };

//   // Filter for entities that represent a single transaction line
//   const transactionLineEntities = allEntities.filter(entity => entity.type === 'transactions');

//   for (const lineEntity of transactionLineEntities) {
//       // Initialize with all potential keys for consistent object structure
//       const currentTransaction = {
//           transaction_date: null,
//           transaction_description: null,
//           transaction_debit: 0,
//           transaction_credit: 0, // Assuming this type might appear
//           transaction_balance: null
//       };

//       if (Array.isArray(lineEntity.properties)) {
//           for (const prop of lineEntity.properties) {
//               const propType = prop.type;
//               const rawMentionText = prop.mentionText || '';
//               const normalizedValue = prop.normalizedValue;

//               switch (propType) {
//                   case 'transaction_date':
//                       if (normalizedValue && normalizedValue.dateValue) {
//                           const dv = normalizedValue.dateValue;
//                           currentTransaction.transaction_date = `${dv.year}-${String(dv.month).padStart(2, '0')}-${String(dv.day).padStart(2, '0')}`;
//                       } else {
//                           currentTransaction.transaction_date = rawMentionText; // May require further parsing by caller
//                       }
//                       break;
//                   case 'transaction_description':
//                       currentTransaction.transaction_description = rawMentionText;
//                       break;
//                   case 'transaction_debit':
//                       let debitData = { amount: 0, currencyCode: null };
//                       if (normalizedValue && normalizedValue.moneyValue) {
//                           debitData = getAmountFromNormalizedMoneyValue(normalizedValue.moneyValue);
//                       } else {
//                           debitData.amount = parseAmountFunc(rawMentionText);
//                       }
//                       currentTransaction.transaction_debit = Math.abs(debitData.amount); // Store as positive value
//                       break;
//                   case 'transaction_credit':
//                       let creditData = { amount: 0, currencyCode: null };
//                       if (normalizedValue && normalizedValue.moneyValue) {
//                           creditData = getAmountFromNormalizedMoneyValue(normalizedValue.moneyValue);
//                       } else {
//                           creditData.amount = parseAmountFunc(rawMentionText);
//                       }
//                       currentTransaction.transaction_credit = Math.abs(creditData.amount); // Store as positive value
//                       break;
//                   case 'transaction_balance':
//                       let balanceData = { amount: 0, currencyCode: null };
//                       if (normalizedValue && normalizedValue.moneyValue) {
//                           balanceData = getAmountFromNormalizedMoneyValue(normalizedValue.moneyValue);
//                       } else {
//                           balanceData.amount = parseAmountFunc(rawMentionText);
//                       }
//                       currentTransaction.transaction_balance = balanceData.amount;
//                       break;
//               }
//           }
//       }
      
//       // Add the processed transaction object to the list
//       transactions.push(currentTransaction);
//   }
//   return transactions;
// }

const bankStatementCoreData = formatDocumentBankStatementResponse(allEntities);

const analysis = analyzeBankStatement(bankStatementCoreData);

    extractedData = {
        account_name: bankStatementCoreData.account_name // Assuming your formatter extracts 'account_name'
                        || getBankEntityValue(allEntities, 'account_name') 
                        || null,
        account_number: bankStatementCoreData.account_number 
                        || getBankEntityValue(allEntities, 'account_number') 
                        || null,
        currency: bankStatementCoreData.currency // This should be correctly derived by your formatter
                        || getBankEntityValue(allEntities, 'currency') 
                        || null,
        iban: (bankStatementCoreData.iban // IBAN from formatter (includes heuristics)
                        || getBankEntityValue(allEntities, 'iban') 
                        || '')
                        .replace(/[()]/g, '') || null,
        swift_code: bankStatementCoreData.swift_code 
                        || getBankEntityValue(allEntities, 'swift_code') 
                        || null,
        bank_name: bankStatementCoreData.bank_name 
                        || getBankEntityValue(allEntities, 'bank_name') 
                        || null,
        bank_address: bankStatementCoreData.bank_address 
                        || getBankEntityValue(allEntities, 'bank_address') 
                        || getBankEntityValue(allEntities, 'address') // Fallback to generic 'address'
                        || null,
        customer_name: bankStatementCoreData.client_name // Mapping 'client_name' from formatter
                        || getBankEntityValue(allEntities, 'customer_name') // Then try 'customer_name'
                        || getBankEntityValue(allEntities, 'client_name')   // Then 'client_name' again as fallback
                        || null,
        customer_address: bankStatementCoreData.customer_address // Assuming formatter might provide this
                        || getBankEntityValue(allEntities, 'customer_address') 
                        || null,
        
        // Dates derived by formatDocumentBankStatementResponse from transactions
        statement_period_start_date: bankStatementCoreData.statement_start_date 
                        || getBankEntityValue(allEntities, 'statement_period_start_date') // Fallback
                        || getBankEntityValue(allEntities, 'statement_start_date') 
                        || null,
        statement_period_end_date: bankStatementCoreData.statement_end_date 
                        || getBankEntityValue(allEntities, 'statement_period_end_date') // Fallback
                        || getBankEntityValue(allEntities, 'statement_end_date')
                        || null,
        statement_date: bankStatementCoreData.statement_date // Assuming formatter might provide this
                        || getBankEntityValue(allEntities, 'statement_date') 
                        || null,

        // Monetary values: use parseAmount for consistency if getting from raw entities
        // formatDocumentBankStatementResponse already provides starting_balance/ending_balance as strings (if from top-level entities)
        // and total_deposits/total_withdrawals as numbers.
        opening_balance: parseAmount(
                            bankStatementCoreData.starting_balance || // From formatter (might be string)
                            getBankEntityValue(allEntities, 'opening_balance') ||
                            getBankEntityValue(allEntities, 'starting_balance') || "0" // Default to "0" before parsing
                         ),
        closing_balance: parseAmount(
                            bankStatementCoreData.ending_balance || // From formatter (might be string)
                            getBankEntityValue(allEntities, 'closing_balance') ||
                            getBankEntityValue(allEntities, 'ending_balance') || "0"
                         ),

        // Totals are already calculated as numbers by formatDocumentBankStatementResponse
        total_deposits: bankStatementCoreData.total_deposits !== undefined 
                        ? bankStatementCoreData.total_deposits 
                        : parseAmount(getBankEntityValue(allEntities, 'total_deposits') || getBankEntityValue(allEntities, 'total_credit') || "0"),
        total_withdrawals: bankStatementCoreData.total_withdrawals !== undefined 
                        ? bankStatementCoreData.total_withdrawals
                        : parseAmount(getBankEntityValue(allEntities, 'total_withdrawals') || getBankEntityValue(allEntities, 'total_debit') || "0"),
        
        // Account Type (potentially corrected by your formatter's heuristic)
        account_type: bankStatementCoreData.account_type 
                        || getBankEntityValue(allEntities, 'account_type') 
                        || null,

        // Transactions array from your formatter
        transactions: bankStatementCoreData.transactions || [],
        analysis,
        // Initialize as per your target structure
        monthly_summaries: {}, 
        yearly_summaries: {},
      };
      

      // 2. Extract Transactions from Tables
      console.log(`DEBUG: Processing ${allTables.length} tables for transactions...`);
      let transactionCount = 0;
      let foundTransactionTable = false;

      for (const [tableIndex, table] of allTables.entries()) {
        const headerRows = table.headerRows || [];
        const bodyRows = table.bodyRows || [];

        if (bodyRows.length === 0) continue; // Skip tables without body

        // --- Identify transaction table columns (CRITICAL & CUSTOMIZABLE) ---
        let dateCol = -1, descCol = -1, debitCol = -1, creditCol = -1, balanceCol = -1;
        let isLikelyTransactionTable = false;

        if (headerRows.length > 0) {
          const firstHeaderCells = headerRows[0].cells || [];
          // Use the 'getText' helper defined outside the function
          const headerTexts = firstHeaderCells.map(cell => getText(cell.layout?.textAnchor, fullText).toLowerCase());
          console.log(`DEBUG: Table ${tableIndex} Header Texts: ${headerTexts.join(' | ')}`);

          // ** Adjust these keywords based on your statement headers **
          headerTexts.forEach((headerText, index) => {
            if (['date', 'posting date', 'value date', 'valeur'].some(kw => headerText.includes(kw))) dateCol = index;
            else if (['description', 'transaction', 'details', 'narrative', 'libellé', 'opération'].some(kw => headerText.includes(kw))) descCol = index;
            // Be careful with 'amount' - check if separate debit/credit exist
            else if (['debit', 'withdrawal', 'payment', 'débit'].some(kw => headerText.includes(kw))) debitCol = index;
            else if (['credit', 'deposit', 'crédit'].some(kw => headerText.includes(kw))) creditCol = index;
            else if (['balance', 'solde'].some(kw => headerText.includes(kw))) balanceCol = index;
            // Handle single 'amount' column if debit/credit aren't separate
            else if (['amount', 'montant'].some(kw => headerText.includes(kw)) && debitCol === -1 && creditCol === -1) debitCol = index; // Assign to debit first, logic below will handle sign
          });

          // Refined logic for single 'Amount' column
          if (debitCol !== -1 && creditCol !== -1 && debitCol === creditCol) {
            console.warn(`DEBUG: Table ${tableIndex} - Both Debit and Credit seem assigned to the same column index ${debitCol} ('Amount'?). Resetting CreditCol, will use sign.`);
            creditCol = -1; // Force using sign logic from the single debitCol
          } else if (debitCol !== -1 && creditCol === -1 && headerTexts[debitCol]?.includes('amount')) {
            console.log(`DEBUG: Table ${tableIndex} - Single 'Amount' column found at index ${debitCol}. Will use sign to determine debit/credit.`);
            // Keep debitCol assigned, logic below handles sign
          }


          // Check if essential columns were found
          if (dateCol !== -1 && descCol !== -1 && (debitCol !== -1 || creditCol !== -1)) {
            isLikelyTransactionTable = true;
            foundTransactionTable = true;
            console.log(`DEBUG: Table ${tableIndex} identified as potential transaction table. Columns - Date:${dateCol}, Desc:${descCol}, Debit:${debitCol}, Credit:${creditCol}, Balance:${balanceCol}`);
          } else {
            console.log(`DEBUG: Table ${tableIndex} skipped - Did not find essential header columns (Date, Desc, Debit/Credit).`);
          }
        } else {
          console.warn(`DEBUG: Table ${tableIndex} has no header row. Cannot reliably identify transaction columns.`);
        }

        if (!isLikelyTransactionTable) continue;

        // --- Process Body Rows ---
        for (const [rowIndex, row] of bodyRows.entries()) {
          const cells = row.cells || [];
          // Ensure row has enough cells for the identified columns
          const maxColIndex = Math.max(dateCol, descCol, debitCol, creditCol, balanceCol);
          if (cells.length <= maxColIndex) {
            // console.log(`DEBUG: Skipping row ${rowIndex} in Table ${tableIndex}, not enough cells (${cells.length}) for identified columns (max index ${maxColIndex}).`);
            continue;
          }

          const dateText = dateCol > -1 ? getText(cells[dateCol]?.layout?.textAnchor, fullText) : '';
          // Skip rows that don't look like they have a valid date entry
          if (!dateText || !/\d/.test(dateText)) {
            // console.log(`DEBUG: Skipping row ${rowIndex} in Table ${tableIndex}, date field '${dateText}' doesn't look like a valid date.`);
            continue;
          }

          const description = descCol > -1 ? getText(cells[descCol]?.layout?.textAnchor, fullText) : '';
          let debitAmount = 0;
          let creditAmount = 0;

          // Use the 'parseAmount' helper defined outside the function
          if (debitCol !== -1 && creditCol !== -1) {
            // Separate Debit and Credit columns
            debitAmount = parseAmount(getText(cells[debitCol]?.layout?.textAnchor, fullText));
            creditAmount = parseAmount(getText(cells[creditCol]?.layout?.textAnchor, fullText));
          } else if (debitCol !== -1) {
            // Single 'Amount' column (assigned to debitCol) or only Debit column found
            const amountVal = parseAmount(getText(cells[debitCol]?.layout?.textAnchor, fullText));
            // Check sign convention: negative usually means debit/withdrawal
            if (amountVal < 0) {
              debitAmount = Math.abs(amountVal);
            } else {
              // If positive, assume it's credit *unless* it was explicitly labeled Debit
              // This handles cases where debits are positive in a debit-only column
              const headerText = (headerRows[0]?.cells[debitCol]) ? getText(headerRows[0].cells[debitCol].layout?.textAnchor, fullText).toLowerCase() : "";
              if (headerText.includes('debit') || headerText.includes('withdrawal') || headerText.includes('payment') || headerText.includes('débit')) {
                debitAmount = amountVal; // Positive value in an explicitly Debit column
              } else {
                creditAmount = amountVal; // Assume positive value in a general 'Amount' column is credit
              }
            }
          } else if (creditCol !== -1) {
            // Only Credit column found
            creditAmount = parseAmount(getText(cells[creditCol]?.layout?.textAnchor, fullText));
            // Assume credit amounts are positive
            if (creditAmount < 0) {
              console.warn(`DEBUG: Negative value found in Credit-only column ${creditCol} for row ${rowIndex}. Treating as positive credit: ${Math.abs(creditAmount)}`);
              creditAmount = Math.abs(creditAmount);
            }
          }

          const balance = balanceCol > -1 ? parseAmount(getText(cells[balanceCol]?.layout?.textAnchor, fullText)) : null;

          // Add transaction if it looks valid
          if (dateText && (debitAmount !== 0 || creditAmount !== 0 || description)) {
            extractedData.transactions.push({
              date: dateText, // Keep raw date text for now, parse later
              description: description,
              debit: debitAmount,
              credit: creditAmount,
              balance: balance, // Will be null if not found/parsed
            });
            transactionCount++;
          } else {
            // console.log(`DEBUG: Skipping row ${rowIndex} in Table ${tableIndex} due to zero amounts and no description, or invalid date.`);
          }
        }
      } // End of table loop

      if (!foundTransactionTable) {
        console.warn(`WARNING: For ${gcsUri}, no tables were identified as containing transactions based on headers. Transaction list will be empty.`);
      }
      console.log(`DEBUG: Extracted ${transactionCount} transactions from tables for ${gcsUri}.`);

      // 3. Calculate Monthly and Yearly Summaries from extracted transactions
      console.log("DEBUG: Calculating monthly and yearly summaries...");
      const monthlySummaries = {};
      const yearlySummaries = {};

      for (const tx of extractedData.transactions) {
        try {
          // Attempt robust date parsing (requires careful handling of formats)
          // Example: Try common formats like YYYY-MM-DD, DD/MM/YYYY, MM/DD/YYYY etc.
          // This is a simplified example, consider a date parsing library for production
          let year, month;
          const dateStr = tx.date.replace(/[.,]/g, '/').replace(/\s+/g, ''); // Normalize separators

          // Try YYYY/MM/DD or YYYY-MM-DD
          let match = dateStr.match(/^(\d{4})[/-](\d{1,2})[/-](\d{1,2})$/);
          if (match) {
            year = match[1];
            month = match[2].padStart(2, '0');
          } else {
            // Try DD/MM/YYYY or DD-MM-YYYY
            match = dateStr.match(/^(\d{1,2})[/-](\d{1,2})[/-](\d{4})$/);
            if (match) {
              year = match[3];
              month = match[2].padStart(2, '0');
            } else {
              // Try MM/DD/YYYY or MM-DD-YYYY
              match = dateStr.match(/^(\d{1,2})[/-](\d{1,2})[/-](\d{4})$/);
              if (match) {
                // Ambiguous MM/DD vs DD/MM - assume MM/DD for this example if day > 12
                // A more robust solution would check locale or use a library
                if (parseInt(match[1], 10) <= 12) {
                  year = match[3];
                  month = match[1].padStart(2, '0');
                } else {
                  console.warn(`DEBUG: Ambiguous date format ${tx.date}, potentially DD/MM/YYYY but parsed as MM/DD. Review parsing logic.`);
                  // Fallback or skip? For now, try parsing as MM/DD
                  year = match[3];
                  month = match[1].padStart(2, '0');
                }
              }
            }
          }

          if (year && month && parseInt(month, 10) >= 1 && parseInt(month, 10) <= 12) {
            const yearMonth = `${year}-${month}`;
            const yearStr = year;

            // Initialize if not present
            if (!monthlySummaries[yearMonth]) {
              monthlySummaries[yearMonth] = { debit: 0, credit: 0, transaction_count: 0 };
            }
            if (!yearlySummaries[yearStr]) {
              yearlySummaries[yearStr] = { debit: 0, credit: 0, transaction_count: 0 };
            }

            // Accumulate (ensure amounts are numbers)
            monthlySummaries[yearMonth].debit += (tx.debit || 0);
            monthlySummaries[yearMonth].credit += (tx.credit || 0);
            monthlySummaries[yearMonth].transaction_count++;

            yearlySummaries[yearStr].debit += (tx.debit || 0);
            yearlySummaries[yearStr].credit += (tx.credit || 0);
            yearlySummaries[yearStr].transaction_count++;

          } else {
            console.warn(`DEBUG: Could not parse date '${tx.date}' (normalized: '${dateStr}') for transaction summary. Skipping summation for this TX.`);
          }
        } catch (e) {
          console.error(`Error processing transaction date for summary: ${tx.date}`, e);
        }
      }

      // Round summaries to 2 decimal places for currency
      Object.keys(monthlySummaries).forEach(key => {
        monthlySummaries[key].debit = parseFloat(monthlySummaries[key].debit.toFixed(2));
        monthlySummaries[key].credit = parseFloat(monthlySummaries[key].credit.toFixed(2));
      });
      Object.keys(yearlySummaries).forEach(key => {
        yearlySummaries[key].debit = parseFloat(yearlySummaries[key].debit.toFixed(2));
        yearlySummaries[key].credit = parseFloat(yearlySummaries[key].credit.toFixed(2));
      });

      extractedData.monthly_summaries = monthlySummaries;
      extractedData.yearly_summaries = yearlySummaries;
      console.log("DEBUG: Summary calculation complete.");

      // ========================================================================
      // Fallback for UNMAPPED types (Remains the same)
      // ========================================================================
    } else if (documentType === 'invoice') {

      console.log(`DEBUG: Extracting ${documentType} fields using Custom Extractor logic.`);
      // Initialize extractedData for invoice
      // Assume getEntityValueSimple and allEntities are defined as in your existing code.

      // 1. Attempt to get existing invoice_id or invoice_number
      let determinedInvoiceId = getEntityValueSimple(allEntities, 'invoice_id') || getEntityValueSimple(allEntities, 'invoice_number');

      // 2. Fetch the values needed for the custom ID and for the extractedData object
      const determinedInvoiceDate = getEntityValueSimple(allEntities, 'invoice_date');
      const determinedTotalAmount = getEntityValueSimple(allEntities, 'total_amount') || getEntityValueSimple(allEntities, 'final_amount') || getEntityValueSimple(allEntities, 'net_amount');

      // 3. If invoice_id is still null (or falsy) and we have an amount and date, generate one
      if (!determinedInvoiceId && determinedTotalAmount && determinedInvoiceDate) {
        // Convert to strings to ensure consistent handling
        let amountStr = String(determinedTotalAmount);
        let dateStr = String(determinedInvoiceDate);

        // Normalize the amount string (same as before)
        amountStr = amountStr.replace(/\s/g, '').replace(/,/g, '');

        // Normalize the date string (same as before)
        dateStr = dateStr.replace(/\s/g, '').replace(/[.\/-]/g, '-');

        // Create a unique base string. Prefixes and separators are important
        // to avoid collisions if, for example, amountStr could look like a dateStr.
        const uniqueBaseString = `amt:${amountStr}|date:${dateStr}|v1`; // Added a version "v1" for future-proofing if logic changes

        // Custom function to generate a less obvious ID from the base string
        function generateCustomAnonymousId(input) {
          let hashVal1 = 0;
          let hashVal2 = 0;

          if (input.length === 0) {
            return "000000000000"; // Default for empty input, fixed length
          }

          for (let i = 0; i < input.length; i++) {
            const charCode = input.charCodeAt(i);
            // First hash: a common simple algorithm (variant of djb2)
            hashVal1 = ((hashVal1 << 5) - hashVal1) + charCode;
            hashVal1 |= 0; // Convert to 32bit integer

            // Second hash: slightly different calculation for variety, processes chars differently
            hashVal2 = ((hashVal2 << 3) + (hashVal2 >> 2) + charCode) * (i % 2 === 0 ? 31 : 33);
            hashVal2 |= 0; // Convert to 32bit integer
          }

          // Ensure positive numbers for base conversion, then convert to base36 (0-9a-z)
          const part1 = Math.abs(hashVal1).toString(36);
          const part2 = Math.abs(hashVal2).toString(36);

          // Combine the two parts and ensure a relatively fixed length
          // This aims for a total generated part length of around 12-16 characters
          let combinedId = part1 + part2;

          const targetLength = 14; // Desired length for the generated part of the ID

          if (combinedId.length > targetLength) {
            // If too long, take a slice from the start and a slice from the end
            // to preserve variability from both hash parts.
            combinedId = combinedId.substring(0, Math.ceil(targetLength / 2)) + combinedId.substring(combinedId.length - Math.floor(targetLength / 2));
          } else {
            // If too short, pad it. Using characters from the original combinedId or fixed chars.
            combinedId = combinedId.padEnd(targetLength, '0');
            // A slightly better padding could be to repeat parts of the ID itself if short
            if (combinedId.length < targetLength && combinedId.length > 0) {
              combinedId = (combinedId + part1 + part2).substring(0, targetLength);
            } else if (combinedId.length === 0) { // Should not happen if input is not empty
              combinedId = "0".repeat(targetLength);
            }
          }

          return "ID_" + combinedId.substring(0, targetLength); // GID for Generated ID
        }

        determinedInvoiceId = generateCustomAnonymousId(uniqueBaseString);
      }

      // 4. Construct the extractedData object
      extractedData = {
        invoice_id: determinedInvoiceId, // This will be the original, or the newly generated one
        invoice_date: determinedInvoiceDate,
        due_date: getEntityValueSimple(allEntities, 'due_date'),
        net_amount: getEntityValueSimple(allEntities, 'net_amount'),
        total_tax_amount: getEntityValueSimple(allEntities, 'total_tax_amount'),
        total_amount: determinedTotalAmount,
        supplier_name: getEntityValueSimple(allEntities, 'supplier_name') || getEntityValueSimple(allEntities, 'supplier'),
        supplier_address: getEntityValueSimple(allEntities, 'supplier_address'),
        supplier_phone: getEntityValueSimple(allEntities, 'supplier_phone'),
        supplier_email: getEntityValueSimple(allEntities, 'supplier_email'),
        supplier_website: getEntityValueSimple(allEntities, 'supplier_website'),
        customer_name: getEntityValueSimple(allEntities, 'customer_name') || getEntityValueSimple(allEntities, 'receiver_name') || getEntityValueSimple(allEntities, 'buyer_name'),
        customer_address: getEntityValueSimple(allEntities, 'customer_address') || getEntityValueSimple(allEntities, 'receiver_address') || getEntityValueSimple(allEntities, 'buyer_address'),
        billing_address: getEntityValueSimple(allEntities, 'ship_to_address'),
        shipping_address: getEntityValueSimple(allEntities, 'ship_from_address'),
        gstin: getEntityValueSimple(allEntities, 'gstin') || getEntityValueSimple(allEntities, 'supplier_tax_id') || getEntityValueSimple(allEntities, 'vat_id'),
        invoice_type: getEntityValueSimple(allEntities, 'invoice_type'),
        line_items: [], // Initialize for structured line items
      };

      // Improved fallback logic for invoice extraction
      // First, try to extract total amount from the document text if not found by the parser
      if (!extractedData.total_amount || extractedData.total_amount === "0.00") {
        // Look for total amount in document text using regex patterns
        const totalAmountPatterns = [
          /TOTAL\s*[:|]\s*(\d+(?:,\d+)*(?:\.\d+)?)/i,
          /TOTAL AMOUNT\s*[:|]\s*(\d+(?:,\d+)*(?:\.\d+)?)/i,
          /TOTAL\s*(?:QAR|QR|AED|USD|EUR|GBP)?\s*(\d+(?:,\d+)*(?:\.\d+)?)/i,
          /GRAND TOTAL\s*[:|]\s*(\d+(?:,\d+)*(?:\.\d+)?)/i,
          /AMOUNT\s*DUE\s*[:|]\s*(\d+(?:,\d+)*(?:\.\d+)?)/i,
          /AMOUNT\s*[:|]\s*(\d+(?:,\d+)*(?:\.\d+)?)/i
        ];

        for (const pattern of totalAmountPatterns) {
          const match = fullText.match(pattern);
          if (match && match[1]) {
            extractedData.total_amount = match[1].replace(/,/g, '');
            console.log(`Extracted total amount from text pattern: ${extractedData.total_amount}`);
            break;
          }
        }
      }

      // Fallback table-based line item extraction if still needed
      if (extractedData.invoice_id === null || extractedData.total_amount === null || extractedData.total_amount === "0.00" || extractedData.supplier_name === null) {
        for (const table of allTables) {
          const headerTexts = (table.headerRows?.[0]?.cells || []).map(cell =>
            getText(cell.layout?.textAnchor, fullText).toLowerCase()
          );

          const hasLikelyItemStructure = headerTexts.some(h =>
            ['item', 'description', 'product', 'particular'].some(keyword => h.includes(keyword))
          ) && headerTexts.some(h =>
            ['amount', 'total', 'price', 'value'].some(keyword => h.includes(keyword))
          );

          if (hasLikelyItemStructure) {
            for (const row of table.bodyRows || []) {
              const cells = row.cells || [];
              const values = cells.map(c => getText(c.layout?.textAnchor, fullText).trim());

              // Try to find the index of columns based on header texts
              const descIndex = headerTexts.findIndex(h =>
                ['item', 'description', 'product', 'particular'].some(keyword => h.includes(keyword))
              );
              const qtyIndex = headerTexts.findIndex(h =>
                ['qty', 'quantity', 'nos', 'count'].some(keyword => h.includes(keyword))
              );
              const priceIndex = headerTexts.findIndex(h =>
                ['price', 'rate', 'unit price', 'unit rate'].some(keyword => h.includes(keyword))
              );
              const amountIndex = headerTexts.findIndex(h =>
                ['amount', 'value', 'total', 'sum'].some(keyword => h.includes(keyword))
              );

              // Initialize item with most likely values based on column positions
              const item = {
                description: descIndex >= 0 && descIndex < values.length ? values[descIndex] : values[0] || null,
                quantity: qtyIndex >= 0 && qtyIndex < values.length ? values[qtyIndex] :
                  values.find(v => /^\d+(\.\d+)?$/.test(v) && parseFloat(v) < 1000) || null,
                unitPrice: priceIndex >= 0 && priceIndex < values.length ? values[priceIndex] :
                  values.find(v => /^\d+(\.\d+)?$/.test(v) && parseFloat(v) > 0) || null,
                amount: amountIndex >= 0 && amountIndex < values.length ? values[amountIndex] : null
              };

              // Ensure we have enough data to include this item
              if (item.description) {
                // If we have quantity and unit price but no amount, calculate it
                if (item.quantity && item.unitPrice && !item.amount) {
                  const qty = parseFloat(item.quantity.replace(/,/g, ''));
                  const price = parseFloat(item.unitPrice.replace(/,/g, ''));
                  if (!isNaN(qty) && !isNaN(price)) {
                    item.amount = (qty * price).toFixed(2);
                  }
                }

                // If we have an amount but no unit price and we have quantity, calculate unit price
                if (item.amount && item.quantity && !item.unitPrice) {
                  const amt = parseFloat(item.amount.replace(/,/g, ''));
                  const qty = parseFloat(item.quantity.replace(/,/g, ''));
                  if (!isNaN(amt) && !isNaN(qty) && qty !== 0) {
                    item.unitPrice = (amt / qty).toFixed(2);
                  }
                }

                extractedData.line_items.push(item);
              }
            }
            break; // Use only first matching table
          }
        }

        // Look for tables without headers that might contain line items
        if (extractedData.line_items.length === 0) {
          for (const table of allTables) {
            if (!table.headerRows || table.headerRows.length === 0) {
              for (const row of table.bodyRows || []) {
                const cells = row.cells || [];
                const values = cells.map(c => getText(c.layout?.textAnchor, fullText).trim());

                // Try to identify if this is a line item row
                // Heuristic: If we have 3+ cells and at least one numeric value
                if (values.length >= 3 && values.some(v => /^\d+(\.\d+)?$/.test(v))) {
                  // Assume first cell is description, then look for numbers
                  const item = {
                    description: values[0] || null,
                    quantity: values.find((v, i) => i > 0 && /^\d+(\.\d+)?$/.test(v) && parseFloat(v) < 1000) || null,
                    unitPrice: values.find((v, i) => i > 0 && /^\d+(\.\d+)?$/.test(v) && parseFloat(v) > 0) || null,
                    amount: values[values.length - 1] || null // Often amount is the last column
                  };

                  if (item.description) {
                    extractedData.line_items.push(item);
                  }
                }
              }
              if (extractedData.line_items.length > 0) break; // Found items in a table
            }
          }
        }

        // Calculate total amount from line items if still needed
        if ((!extractedData.total_amount || extractedData.total_amount === "0.00") && extractedData.line_items.length > 0) {
          let calculatedTotal = 0;

          for (const item of extractedData.line_items) {
            if (item.amount && parseFloat(item.amount.replace(/,/g, '')) > 0) {
              calculatedTotal += parseFloat(item.amount.replace(/,/g, ''));
            } else if (item.quantity && item.unitPrice) {
              const qty = parseFloat(item.quantity.replace(/,/g, ''));
              const price = parseFloat(item.unitPrice.replace(/,/g, ''));
              if (!isNaN(qty) && !isNaN(price)) {
                calculatedTotal += qty * price;
              }
            }
          }

          if (calculatedTotal > 0) {
            extractedData.total_amount = calculatedTotal.toFixed(2);
            console.log(`Calculated total amount from line items: ${extractedData.total_amount}`);
          }
        }

        // Look for total in the document text near keywords if still needed
        if (!extractedData.total_amount || extractedData.total_amount === "0.00") {
          // Try to find total amount in text near the end of the document
          const lastFourthOfText = fullText.substring(Math.floor(fullText.length * 0.75));

          // Look for numbers after total-related keywords
          const totalKeywords = ['total', 'amount due', 'grand total', 'balance due', 'to pay'];
          for (const keyword of totalKeywords) {
            const pattern = new RegExp(`${keyword}\\s*(?::|=|is|\\s)?\\s*(\\d+(?:,\\d+)*(?:\\.\\d+)?)`, 'i');
            const match = lastFourthOfText.match(pattern);
            if (match && match[1]) {
              extractedData.total_amount = match[1].replace(/,/g, '');
              console.log(`Found total amount near "${keyword}": ${extractedData.total_amount}`);
              break;
            }
          }
        }
      }

      // If we have line items but missing information in them, try to fill in the gaps
      if (extractedData.line_items.length > 0) {
        extractedData.line_items = extractedData.line_items.map(item => {
          // If we have quantity and unit price but no amount, calculate it
          if (!item.amount && item.quantity && item.unitPrice) {
            const qty = parseFloat(item.quantity.replace(/,/g, ''));
            const price = parseFloat(item.unitPrice.replace(/,/g, ''));
            if (!isNaN(qty) && !isNaN(price)) {
              item.amount = (qty * price).toFixed(2);
            }
          }

          // If we have amount and quantity but no unit price, calculate it
          if (!item.unitPrice && item.amount && item.quantity) {
            const amt = parseFloat(item.amount.replace(/,/g, ''));
            const qty = parseFloat(item.quantity.replace(/,/g, ''));
            if (!isNaN(amt) && !isNaN(qty) && qty !== 0) {
              item.unitPrice = (amt / qty).toFixed(2);
            }
          }

          return item;
        });
      }

      // Attempt to extract structured line items
      // This assumes your custom processor defines 'line_item' entities,
      // and each 'line_item' entity has 'properties' for description, amount, etc.
      const lineItemEntities = allEntities.filter(e => e.type === 'line_item');
      if (lineItemEntities.length > 0) {
        extractedData.line_items = lineItemEntities.map(lineEntity => {
          // Helper to find property value within a line_item entity's properties
          const findPropValue = (properties, typeName) => {
            const prop = (properties || []).find(p => p.type === typeName);
            return prop ? (prop.mentionText || '').replace(/\s+/g, ' ').trim() : null;
          };

          return {
            description: findPropValue(lineEntity.properties, 'line_item/description') || findPropValue(lineEntity.properties, 'description'),
            amount: findPropValue(lineEntity.properties, 'line_item/amount') || findPropValue(lineEntity.properties, 'amount'),
            quantity: findPropValue(lineEntity.properties, 'line_item/quantity') || findPropValue(lineEntity.properties, 'quantity'),
            unit_price: findPropValue(lineEntity.properties, 'line_item/unit_price') || findPropValue(lineEntity.properties, 'unit_price'),
            // You can add other line item sub-fields here if your processor extracts them
            // raw_text: lineEntity.mentionText // Optionally include the full mention text of the line item
          };
        }).filter(item => item.description || item.amount); // Filter out empty line items
      } else {
        // Fallback if 'line_item' entities are not structured with properties,
        // but 'line_item/description' and 'line_item/amount' are flat lists.
        // This assumes a 1:1 correspondence in order.
        const descriptions = allEntities.filter(e => e.type === 'line_item/description').map(e => e.mentionText.replace(/\s+/g, ' ').trim());
        const amounts = allEntities.filter(e => e.type === 'line_item/amount').map(e => e.mentionText.replace(/\s+/g, ' ').trim());

        if (descriptions.length > 0 || amounts.length > 0) {
          const count = Math.max(descriptions.length, amounts.length);
          for (let i = 0; i < count; i++) {
            extractedData.line_items.push({
              description: descriptions[i] || null,
              amount: amounts[i] || null,
            });
          }
        }
      }


      // If total_amount was not directly extracted but net and tax are, calculate it
      if (!extractedData.total_amount && extractedData.net_amount && extractedData.total_tax_amount) {
        const net = parseFloat(extractedData.net_amount.replace(/,/g, '')) || 0;
        const tax = parseFloat(extractedData.total_tax_amount.replace(/,/g, '')) || 0;
        if (net !== 0 || tax !== 0) { // Avoid setting total to 0 if both are null/unparseable but could be validly 0
          extractedData.total_amount = (net + tax).toFixed(2);
        }
      }
      // Default billing_address to supplier_address if not specifically found
      if (!extractedData.billing_address && extractedData.supplier_address) {
        extractedData.billing_address = extractedData.supplier_address;
      }

      console.log(`Extracted invoice data:`, JSON.stringify(extractedData, null, 2));
    } else if (documentType === 'Cash Flow Ledger') {
      console.log(`DEBUG: Extracting ${documentType} fields using Custom Extractor logic.`);
      extractedData = {
        "companyName": getEntityValueSimple(allEntities, 'companyName'),
        "currency": getEntityValueSimple(allEntities, 'currency'),
        "documentTitle": getEntityValueSimple(allEntities, 'documentTitle'),
        "periodEndDate": getEntityValueSimple(allEntities, 'periodEndDate'),
        "recipientAddress": getEntityValueSimple(allEntities, 'recipientAddress'),
        "recipientName": getEntityValueSimple(allEntities, 'recipientName'),
        "senderAddress": getEntityValueSimple(allEntities, 'senderAddress'),
        "senderName": getEntityValueSimple(allEntities, 'senderName'),
        "statementPeriodEndDate": getEntityValueSimple(allEntities, 'statementPeriodEndDate'),
        "statementPeriodStartDate": getEntityValueSimple(allEntities, 'statementPeriodStartDate'),
        "summaryAmountReceivedValue": getEntityValueSimple(allEntities, 'summaryAmountReceivedValue'),
        "summaryBalanceDueValue": getEntityValueSimple(allEntities, 'summaryBalanceDueValue'),
        "summaryInvoicedAmountValue": getEntityValueSimple(allEntities, 'summaryInvoicedAmountValue'),
        "summaryOpeningBalanceValue": getEntityValueSimple(allEntities, 'summaryOpeningBalanceValue'),
        "transactionAmount": getEntityValueSimple(allEntities, 'transactionAmount'),
        "transactionBalance": getEntityValueSimple(allEntities, 'transactionBalance'),
        "transactionDate": getEntityValueSimple(allEntities, 'transactionDate'),
        "transactionDetails": getEntityValueSimple(allEntities, 'transactionDetails'),
        "transactionPayment": getEntityValueSimple(allEntities, 'transactionPayment'),
        "transactionType": getEntityValueSimple(allEntities, 'transactionType')
      };

      console.log('cashflow ledger', extractedData);
    } else if (documentType === 'Audited Financial Report') {
      const parseValue = (field) => {
        const value = getEntityValueSimple(allEntities, field);
        return typeof value === 'string' && value.includes('(')
          ? -1 * Number(value.replace(/[(),]/g, ''))
          : value;
      };
      
       extractedData = {
        financialYearEnd: getEntityValueSimple(allEntities, 'financialYearEnd'),
        endDate: getEntityValueSimple(allEntities, 'endDate'),
        startDate: getEntityValueSimple(allEntities, 'startDate'),
        revenueFromOperations: parseValue('revenueFromOperations'),
        grossOperatingProfit: parseValue('grossOperatingProfit'),
        operatingLossForTheYear: parseValue('operatingLossForTheYear'),
        netLossForTheYear: parseValue('netLossForTheYear'),
        netIncomeAfterTax: parseValue('netIncomeAfterTax'),
        totalComprehensiveIncome: parseValue('totalComprehensiveIncome'),
        totalAssets: parseValue('totalAssets'),
        totalEquity: parseValue('totalEquity'),
        totalLiabilities: parseValue('totalLiabilities')
      };
      console.log(`DEBUG: Extracting ${documentType} fields using Audited Financial Report Extractor logic.`);
      console.log('Audited Financial Report', extractedData);
    } else if (documentType === 'Commercial Credit Report') {
      extractedData = {
        "CreditBureauContactInformation": getEntityValueSimple(allEntities, 'CreditBureauContactInformation'),
        "CreditBureauName": getEntityValueSimple(allEntities, 'CreditBureauName'),
        "ReportGenerationDate": getEntityValueSimple(allEntities, 'ReportGenerationDate'),
        "ReportReferenceID": getEntityValueSimple(allEntities, 'ReportReferenceID'),
        "ReportTitle": getEntityValueSimple(allEntities, 'ReportTitle'),
        "SubjectName": getEntityValueSimple(allEntities, 'SubjectName'),
        "SubjectPrimaryIdentifier": getEntityValueSimple(allEntities, 'SubjectPrimaryIdentifier'),
        "CurrencyOfAmounts": getEntityValueSimple(allEntities, 'CurrencyOfAmounts'),
        "AccountDateLastReported": getEntityValueSimple(allEntities, 'AccountDateLastReported'),
        "AccountHolderName": getEntityValueSimple(allEntities, 'AccountHolderName'),
        "InquiryDate": getEntityValueSimple(allEntities, 'InquiryDate'),
        "InquiryPurpose": getEntityValueSimple(allEntities, 'InquiryPurpose'),
        "LenderName": getEntityValueSimple(allEntities, 'LenderName'),
        "OwnershipOrRelationshipDetails": getEntityValueSimple(allEntities, 'OwnershipOrRelationshipDetails')
      };
      console.log(`DEBUG: Extracting ${documentType} fields using Commercial Credit Report Extractor logic.`);
      console.log('Commercial Credit Report', extractedData);
    } else if (documentType === 'passport') {
      extractedData = {
        "address": getEntityValueSimple(allEntities, 'address'),
        "dateOfExpiry": getEntityValueSimple(allEntities, 'dateOfExpiry'),
        "dateOfIssue": getEntityValueSimple(allEntities, 'dateOfIssue'),
        "dob": getEntityValueSimple(allEntities, 'dob'),
        "fatherName": getEntityValueSimple(allEntities, 'fatherName'),
        "fileNumber": getEntityValueSimple(allEntities, 'fileNumber'),
        "givenName": getEntityValueSimple(allEntities, 'givenName'),
        "machineReadableZone": getEntityValueSimple(allEntities, 'machineReadableZone'),
        "motherName": getEntityValueSimple(allEntities, 'motherName'),
        "nationality": getEntityValueSimple(allEntities, 'nationality'),
        "passportNumber": getEntityValueSimple(allEntities, 'passportNumber'),
        "placeOfBirth": getEntityValueSimple(allEntities, 'placeOfBirth'),
        "placeOfIssue": getEntityValueSimple(allEntities, 'placeOfIssue'),
        "sex": getEntityValueSimple(allEntities, 'sex'),
        "surname": getEntityValueSimple(allEntities, 'surname')
      };
      console.log(`DEBUG: Extracting ${documentType} fields using passport Extractor logic.`);
      console.log('passport', extractedData);
    }
    else {
      console.warn(`No specific extraction mapping defined for document type: ${documentType}. Extracting all found entities by type.`);
      // Fallback: Extract all entities as key-value pairs using the simple helper
      extractedData = allEntities.reduce((acc, entity) => {
        // Use getEntityValueSimple for consistency with the original fallback
        acc[entity.type] = getEntityValueSimple(allEntities, entity.type);
        return acc;
      }, {});
      // Check if ANYTHING was extracted (entities or tables) before returning error
      if (Object.keys(extractedData).length === 0 && allTables.length === 0) {
        console.warn(`No entities or tables extracted via fallback for type ${documentType} and URI ${gcsUri}.`);
        // Return empty data object instead of error to maintain compatibility if caller expects an object
        // return { error: `No entities or tables extracted for type ${documentType}.`, data: null };
        return { error: null, data: {} };
      } else if (Object.keys(extractedData).length === 0 && allTables.length > 0) {
        console.log(`Fallback extracted no entities, but found ${allTables.length} tables for ${documentType}. Returning empty data object.`);
        return { error: null, data: {} }; // Return empty data, tables weren't processed by fallback
      }
    }

    // --- Final Return (Remains the same) ---
    // Log only if data was actually extracted
    if (Object.keys(extractedData).length > 0 || (documentType === 'Bank Statement' && extractedData.transactions?.length > 0)) {
      console.log(`DEBUG Final Mapped Data for ${gcsUri} (${documentType}):`, JSON.stringify(extractedData, null, 2));
    } else {
      console.log(`DEBUG Final Mapped Data for ${gcsUri} (${documentType}) is empty.`);
    }

    return { error: null, data: extractedData };

  } catch (error) {
    // --- Error Handling (Remains the same) ---
    console.error(`Error processing document ${gcsUri} with Document AI processor ${processorId}: ${error.message}`, error);
    const details = error.details || error.message;
    // Log more details if available (e.g., from gRPC error)
    console.error("Full Error Details:", JSON.stringify(error, null, 2));
    return { error: `Document AI processing failed: ${details}`, data: null };
  }
}

async function upload(filename, filePath) {
  try {
    const params = {
      Bucket: bucketName,
      Key: filename,
      Body: fs.readFileSync(filePath)
    };

    const res = await s3.upload(params).promise();
    return { error: null, response: res };
  } catch (error) {
    console.error('Error in upload:', error);
    return { error, response: null };
  }
}

/**
 * Upload file to Google Cloud Storage
 * @param {string} filename - Name of file to upload
 * @param {string} filePath - Path to file 
 * @returns {Promise<Object>} Upload response from GCS
 */

async function uploadToGCS(filename, filePath) {
  try {
    const bucket = gcpStorage.bucket(gcpConfig.bucketName);
    const file = bucket.file(filename);

    // Read file and upload to GCS
    await new Promise((resolve, reject) => {
      fs.createReadStream(filePath)
        .pipe(file.createWriteStream({
          metadata: {
            contentType: getContentType(filename)
          }
        }))
        .on('error', (err) => reject(err))
        .on('finish', () => resolve());
    });

    // Generate resource URL
    const resourceUrl = `gs://${gcpConfig.bucketName}/${filename}`;

    // Generate long-lived signed URL with max expiration
    const [signedUrl] = await file.getSignedUrl({
      action: 'read',
      expires: Date.now() + (gcpConfig.signUrlTimeout * 1000) // Use max timeout from config
    });

    return {
      error: null,
      response: {
        resourceUrl: resourceUrl,
        signedUrl: signedUrl
      }
    };
  } catch (error) {
    console.error('Error in GCS upload:', error);
    return { error, response: null };
  }
}

/**
 * Get signed URL for file upload (S3 version - backward compatibility)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
function getSignedUrl(req, res) {
  const fileName = req.query.fileName;

  if (!fileName) {
    return res.status(400).send({ message: "Missing parameter 'fileName'" });
  }

  const uniqueFileName = uniqueIdGenerator();
  const key = `${uniqueFileName}-${fileName}`;

  s3.getSignedUrl(
    "putObject",
    {
      Bucket: bucketName,
      Key: key,
      Expires: expireSeconds
    },
    (err, url) => {
      if (err) {
        console.error('Error generating signed URL:', err);
        return res.status(500).send("Internal error");
      }
      res.status(200).send({
        signedUrl: url,
        name: key
      });
    }
  );
}

/**
 * Get GCS signed URL for file upload
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function getGCSSignedUrl(req, res) {
  try {
    const fileName = req.query.fileName;

    if (!fileName) {
      return res.status(400).send({ message: "Missing parameter 'fileName'" });
    }

    const uniqueFileName = uniqueIdGenerator();
    const key = `${uniqueFileName}-${fileName}`;

    const bucket = gcpStorage.bucket(gcpBucketName);
    const file = bucket.file(key);

    const [signedUrl] = await file.getSignedUrl({
      action: 'write',
      version: 'v4',
      expires: Date.now() + expireSeconds * 1000,
      contentType: getContentType(fileName)
    });

    res.status(200).send({
      signedUrl: signedUrl,
      name: key,
      publicUrl: `https://storage.googleapis.com/${gcpBucketName}/${key}`
    });
  } catch (error) {
    console.error('Error generating GCS signed URL:', error);
    return res.status(500).send("Internal error");
  }
}

/**
 * Get signed URL for file download (S3 version - backward compatibility)
 * @param {Object} payload - Object containing URL
 * @returns {string|null} Signed URL or null
 */
function getSignedUrlToDownload(payload) {
  try {
    if (!payload.url) {
      return null;
    }

    return s3.getSignedUrl("getObject", {
      Bucket: bucketName,
      Key: payload.url,
      Expires: expireSeconds
    });
  } catch (error) {
    console.error('Error getting signed URL for download:', error);
    return null;
  }
}

/**
 * Get GCS signed URL for file download
 * @param {Object} payload - Object containing URL
 * @returns {Promise<string|null>} Signed URL or null
 */
async function getGCSSignedUrlToDownload(payload) {
  try {
    if (!payload.url) {
      return null;
    }

    // Extract the filename from the URL
    const url = new URL(payload.url);
    const pathname = url.pathname;
    // The path starts with /bucketname/filename, so we extract just the filename
    const key = pathname.split('/').slice(2).join('/');

    if (!key) {
      console.error('Invalid GCS URL:', payload.url);
      return null;
    }

    const bucket = gcpStorage.bucket(gcpBucketName);
    const file = bucket.file(key);

    const [signedUrl] = await file.getSignedUrl({
      action: 'read',
      expires: Date.now() + expireSeconds * 1000
    });

    return signedUrl;
  } catch (error) {
    console.error('Error getting GCS signed URL for download:', error);
    return null;
  }
}

/**
 * Upload bank statements to S3 (backward compatibility)
 * @param {string} filename - Name of file
 * @param {string} filePath - Path to file
 * @returns {Promise<Object>} Upload response
 */
/**
 * General file upload function with GCS
 * @param {string} filename - Original filename
 * @param {string} filePath - Path to file on disk
 * @param {string} userId - User ID for file tracking
 * @param {string} contentType - Optional content type, otherwise detected from filename
 * @returns {Promise<Object>} Upload response with URLs
 */

// New function specifically for Buffer uploads
async function uploadBufferToGCS(filename, fileData, userId, contentType = null) {
  try {
    console.log(`Starting GCS upload for Buffer: ${filename}, user: ${userId}`);

    const now = new Date();
    const timestamp = `${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}_${now.getHours().toString().padStart(2, '0')}${now.getMinutes().toString().padStart(2, '0')}${now.getSeconds().toString().padStart(2, '0')}`;

    // Generate cleaned filename
    const fileExt = path.extname(filename);
    const baseFilename = path.basename(filename, fileExt).replace(/[^a-z0-9]/gi, '_').toLowerCase();

    // Create a clean key path for the file
    const key = `user_files/${userId || 'unknown'}/${baseFilename}_${timestamp}${fileExt}`;

    // Determine content type
    const detectedContentType = contentType || getContentType(filename);

    console.log(`Uploading to GCS path: ${key} with content type: ${detectedContentType}`);

    const bucket = gcpStorage.bucket(gcpBucketName);
    const file = bucket.file(key);

    // Upload Buffer to GCS
    await new Promise((resolve, reject) => {
      const uploadStream = file.createWriteStream({
        metadata: {
          contentType: detectedContentType
        },
        resumable: false // For smaller files, non-resumable uploads are faster
      });

      uploadStream.on('error', (err) => {
        console.error(`Error uploading to GCS: ${key}`, err);
        reject(new Error(`GCS upload error: ${err.message}`));
      });

      uploadStream.on('finish', () => {
        console.log(`Successfully uploaded to GCS: ${key}`);
        resolve();
      });

      if (Buffer.isBuffer(fileData)) {
        uploadStream.end(fileData);
      } else {
        return reject(new Error('Expected Buffer for upload, but received incorrect data.'));
      }
    });

    // Generate public URL (always available)
    const publicUrl = `https://storage.googleapis.com/${gcpBucketName}/${key}`;

    // Generate signed URL with expiration (for protected access)
    const [signedUrl] = await file.getSignedUrl({
      action: 'read',
      expires: Date.now() + (gcpConfig.signUrlTimeout || 3600) * 1000,
      responseDisposition: 'inline',
      responseType: detectedContentType
    });

    console.log(`Generated signed URL for GCS file: ${key}`);

    return {
      error: null,
      response: {
        Location: publicUrl,
        signedUrl: signedUrl,
        key: key,
        contentType: detectedContentType,
        uploadedAt: new Date().toISOString()
      }
    };
  } catch (error) {
    console.error('Error in uploadBufferToGCS:', error);
    return {
      error: error.message || 'Unknown upload error',
      response: null
    };
  }
}

async function uploadFileToGCS(filename, fileData, userId, contentType = null, isBufferUpload = false) {
  try {
    console.log(`Starting GCS upload for file: ${filename}, user: ${userId}, isBufferUpload: ${isBufferUpload}`);

    const now = new Date();
    const timestamp = `${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}_${now.getHours().toString().padStart(2, '0')}${now.getMinutes().toString().padStart(2, '0')}${now.getSeconds().toString().padStart(2, '0')}`;

    // Generate cleaned filename
    const fileExt = path.extname(filename);
    const baseFilename = path.basename(filename, fileExt).replace(/[^a-z0-9]/gi, '_').toLowerCase();

    // Create a clean key path for the file
    const key = `user_files/${userId || 'unknown'}/${baseFilename}_${timestamp}${fileExt}`;

    // Determine content type
    const detectedContentType = contentType || getContentType(filename);

    console.log(`Uploading to GCS path: ${key} with content type: ${detectedContentType}`);

    const bucket = gcpStorage.bucket(gcpBucketName);
    const file = bucket.file(key);

    // Upload to GCS
    await new Promise((resolve, reject) => {
      const uploadStream = file.createWriteStream({
        metadata: {
          contentType: detectedContentType
        },
        resumable: false // For smaller files, non-resumable uploads are faster
      });

      uploadStream.on('error', (err) => {
        console.error(`Error uploading to GCS: ${key}`, err);
        reject(new Error(`GCS upload error: ${err.message}`));
      });

      uploadStream.on('finish', () => {
        console.log(`Successfully uploaded to GCS: ${key}`);
        resolve();
      });

      // Handle Buffer or file path based on the new isBufferUpload field
      if (isBufferUpload) {
        // Handle Buffer data, ensuring it's a proper Buffer object
        const bufferData = Buffer.isBuffer(fileData) ? fileData : Buffer.from(fileData);
        uploadStream.end(bufferData);
      } else {
        if (Buffer.isBuffer(fileData)) {
          return reject(new Error('Expected file path for upload, but received buffer.'));
        }
        if (!fs.existsSync(fileData)) {
          return reject(new Error(`File not found at path: ${fileData}`));
        }
        const fileStream = fs.createReadStream(fileData);
        fileStream.on('error', (err) => {
          console.error(`Error reading file: ${fileData}`, err);
          reject(new Error(`Error reading file: ${err.message}`));
        });
        fileStream.pipe(uploadStream);
      }
    });

    // Generate public URL (always available)
    const publicUrl = `https://storage.googleapis.com/${gcpBucketName}/${key}`;

    // Generate signed URL with expiration (for protected access)
    const [signedUrl] = await file.getSignedUrl({
      action: 'read',
      expires: Date.now() + (gcpConfig.signUrlTimeout || 3600) * 1000,
      responseDisposition: 'inline',
      responseType: detectedContentType
    });

    console.log(`Generated signed URL for GCS file: ${key}`);

    // Clean up the local file if it exists
    // try {
    //   if (!isBufferUpload && typeof fileData === 'string' && fs.existsSync(fileData)) {
    //     fs.unlinkSync(fileData);
    //     console.log(`Cleaned up local file: ${fileData}`);
    //   }
    // } catch (cleanupError) {
    //   console.warn(`Warning: Could not clean up local file: ${fileData}`, cleanupError);
    //   // Non-fatal error, continue execution
    // }

    return {
      error: null,
      response: {
        Location: publicUrl,
        signedUrl: signedUrl,
        key: key,
        gcsUri: `gs://${gcpBucketName}/${key}`,
        filePath: key, // Add filePath explicitly in the response
        contentType: detectedContentType,
        uploadedAt: new Date().toISOString()
      }
    };
  } catch (error) {
    console.error('Error in uploadFileToGCS:', error);
    return {
      error: error.message || 'Unknown upload error',
      response: null
    };
  }
}

async function uploadBankStatements(filename, filePath) {
  try {
    const params = {
      Bucket: bankStatementsBucketName,
      Key: filename,
      Body: fs.readFileSync(filePath)
    };

    const res = await s3.upload(params).promise();
    return { error: null, response: res };
  } catch (error) {
    console.error('Error uploading bank statement:', error);
    return { error, response: null };
  }
}

/**
 * Upload bank statements to GCS
 * @param {string} filename - Name of file
 * @param {string} filePath - Path to file
 * @returns {Promise<Object>} Upload response
 */
async function uploadBankStatementsToGCS(filename, filePath) {
  try {
    const bucket = gcpStorage.bucket(gcpBankStatementsBucket);
    const file = bucket.file(filename);

    // Read file and upload to GCS
    await new Promise((resolve, reject) => {
      fs.createReadStream(filePath)
        .pipe(file.createWriteStream({
          metadata: {
            contentType: 'application/pdf'
          }
        }))
        .on('error', (err) => reject(err))
        .on('finish', () => resolve());
    });

    // Generate public URL
    const publicUrl = `https://storage.googleapis.com/${gcpBankStatementsBucket}/${filename}`;

    // Generate signed URL with expiration
    const [signedUrl] = await file.getSignedUrl({
      action: 'read',
      expires: Date.now() + expireSeconds * 1000
    });

    return {
      error: null,
      response: {
        Location: publicUrl,
        signedUrl: signedUrl
      }
    };
  } catch (error) {
    console.error('Error uploading bank statement to GCS:', error);
    return { error, response: null };
  }
}

/**
 * Get signed URL for bank statement download (S3 version - backward compatibility)
 * @param {Object} payload - Object containing URL
 * @returns {string|null} Signed URL
 */
async function getSingedUrlToDownloadForBankStatement(payload) {
  try {
    if (!payload.url) {
      return null;
    }

    let url = payload.url;
    if (url.startsWith("/")) {
      url = url.substr(1);
    }

    return s3.getSignedUrl("getObject", {
      Bucket: bankStatementsBucketName,
      Key: url,
      Expires: expireSeconds
    });
  } catch (error) {
    console.error('Error getting signed URL for bank statement:', error);
    return null;
  }
}

/**
 * Get GCS signed URL for bank statement download
 * @param {Object} payload - Object containing URL
 * @returns {Promise<string|null>} Signed URL
 */
async function getGCSSignedUrlForBankStatement(payload) {
  try {
    if (!payload.url) {
      return null;
    }

    // Extract the filename from the URL
    const url = new URL(payload.url);
    const pathname = url.pathname;
    // The path starts with /bucketname/filename, so we extract just the filename
    let key = pathname.split('/').slice(2).join('/');

    if (!key) {
      console.error('Invalid GCS URL:', payload.url);
      return null;
    }

    const bucket = gcpStorage.bucket(gcpBankStatementsBucket);
    const file = bucket.file(key);

    const [signedUrl] = await file.getSignedUrl({
      action: 'read',
      expires: Date.now() + expireSeconds * 1000,
      responseDisposition: 'inline',
      responseType: 'application/pdf'
    });

    return signedUrl;
  } catch (error) {
    console.error('Error getting GCS signed URL for bank statement:', error);
    return null;
  }
}

/**
 * Upload bank statements with timestamp (S3 version - backward compatibility)
 * @param {string} filename - Original filename
 * @param {string} filePath - Path to file
 * @param {string} userId - User ID
 * @returns {Promise<Object>} Upload response
 */
async function uploadBankStatementsToAws(filename, filePath, userId) {
  try {
    const now = new Date();
    const timestamp = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;

    // Create a clean key without special characters
    const key = `User-Bankstatement-pdf-files/${userId || 'undefined'}_${timestamp.replace(/:/g, '')}.pdf`;

    const params = {
      Bucket: BankStatementsBucket,
      Key: key,
      Body: fs.readFileSync(filePath),
      ContentType: 'application/pdf'
    };

    const res = await s3.upload(params).promise();

    // Generate signed URL immediately after upload
    const signedUrl = await getSignedUrlForInvoice(res.Location);

    return {
      error: null,
      response: {
        ...res,
        signedUrl
      }
    };
  } catch (error) {
    console.error('Error uploading bank statement to AWS:', error);
    return { error, response: null };
  }
}

/**
 * Upload bank statements to GCS with timestamp
 * @param {string} filename - Original filename
 * @param {string} filePath - Path to file
 * @param {string} userId - User ID
 * @returns {Promise<Object>} Upload response
 */
async function uploadBankStatementsToGCS(filename, filePath, userId) {
  try {
    const now = new Date();
    const timestamp = `${now.getHours().toString().padStart(2, '0')}${now.getMinutes().toString().padStart(2, '0')}${now.getSeconds().toString().padStart(2, '0')}`;

    // Create a clean key without special characters
    const key = `User-Bankstatement-pdf-files/${userId || 'undefined'}_${timestamp}.pdf`;

    const bucket = gcpStorage.bucket(gcpBankStatementsBucket);
    const file = bucket.file(key);

    // Read file and upload to GCS
    await new Promise((resolve, reject) => {
      fs.createReadStream(filePath)
        .pipe(file.createWriteStream({
          metadata: {
            contentType: 'application/pdf'
          }
        }))
        .on('error', (err) => reject(err))
        .on('finish', () => resolve());
    });

    // Generate public URL
    const publicUrl = `https://storage.googleapis.com/${gcpBankStatementsBucket}/${key}`;

    // Generate signed URL with expiration
    const [signedUrl] = await file.getSignedUrl({
      action: 'read',
      expires: Date.now() + expireSeconds * 1000,
      responseDisposition: 'inline',
      responseType: 'application/pdf'
    });

    return {
      error: null,
      response: {
        Location: publicUrl,
        signedUrl: signedUrl
      }
    };
  } catch (error) {
    console.error('Error uploading bank statement to GCS:', error);
    return { error, response: null };
  }
}

/**
 * Generate signed URL for invoice documents (S3 version - backward compatibility)
 * @param {string} s3Url - Full S3 URL of document
 * @returns {Promise<string>} Signed URL
 */
async function getSignedUrlForInvoice(s3Url) {
  try {
    if (!s3Url) return null;

    // Extract the key correctly from the URL
    const urlParts = new URL(s3Url);
    let key = urlParts.pathname.substring(1); // Remove leading slash

    // Remove the doubled folder path if it exists
    if (key.includes('User-Bankstatement-pdf-files/User-Bankstatement-pdf-files/')) {
      key = key.replace('User-Bankstatement-pdf-files/User-Bankstatement-pdf-files/', 'User-Bankstatement-pdf-files/');
    }

    // Decode the URL-encoded characters in the key
    key = decodeURIComponent(key);

    const params = {
      Bucket: BankStatementsBucket,
      Key: key,
      Expires: expireSeconds,
      ResponseContentDisposition: 'inline', // This will make the PDF display in browser
      ResponseContentType: 'application/pdf'
    };

    return new Promise((resolve, reject) => {
      s3.getSignedUrl('getObject', params, (err, url) => {
        if (err) {
          console.error('Error generating signed URL:', err);
          reject(err);
        } else {
          resolve(url);
        }
      });
    });
  } catch (error) {
    console.error('Error in getSignedUrlForInvoice:', error);
    return null;
  }
}

/**
 * Get GCS signed URL for invoice documents
 * @param {string} gcsUrl - Full GCS URL of document
 * @returns {Promise<string>} Signed URL
 */
async function getGCSSignedUrlForInvoice(gcsUrl) {
  try {
    if (!gcsUrl) return null;

    // Extract the filename from the URL
    const url = new URL(gcsUrl);
    const pathname = url.pathname;
    // The path starts with /bucketname/filename, so we extract just the filename
    let key = pathname.split('/').slice(2).join('/');

    // Check if there's a duplicated path and fix it
    if (key.includes('User-Bankstatement-pdf-files/User-Bankstatement-pdf-files/')) {
      key = key.replace('User-Bankstatement-pdf-files/User-Bankstatement-pdf-files/', 'User-Bankstatement-pdf-files/');
    }

    // Decode URL-encoded characters
    key = decodeURIComponent(key);

    const bucket = gcpStorage.bucket(gcpBankStatementsBucket);
    const file = bucket.file(key);

    const [signedUrl] = await file.getSignedUrl({
      action: 'read',
      expires: Date.now() + expireSeconds * 1000,
      responseDisposition: 'inline',
      responseType: 'application/pdf'
    });

    return signedUrl;
  } catch (error) {
    console.error('Error getting GCS signed URL for invoice:', error);
    return null;
  }
}

/**
 * Refresh signed URLs for multiple documents (S3 version - backward compatibility)
 * @param {Array<Object>} documents - Array of documents with s3Url property
 * @returns {Promise<Array<Object>>} Documents with refreshed signed URLs
 */
async function refreshSignedUrls(documents) {
  try {
    if (!Array.isArray(documents)) {
      throw new Error('Input must be an array of documents');
    }

    return await Promise.all(documents.map(async (doc) => {
      if (!doc.s3Url) return doc;

      const signedUrl = await getSignedUrlForInvoice(doc.s3Url);

      return {
        ...doc,
        signedUrl: signedUrl || doc.s3Url, // Fallback to original URL if signing fails
        s3Url: signedUrl || doc.s3Url // Also update s3Url to use the signed URL
      };
    }));
  } catch (error) {
    console.error('Error refreshing signed URLs:', error);
    return documents;
  }
}

/**
 * Refresh GCS signed URLs for multiple documents
 * @param {Array<Object>} documents - Array of documents with gcsUrl property
 * @returns {Promise<Array<Object>>} Documents with refreshed signed URLs
 */
async function refreshGCSSignedUrls(documents) {
  try {
    if (!Array.isArray(documents)) {
      throw new Error('Input must be an array of documents');
    }

    return await Promise.all(documents.map(async (doc) => {
      // Check for either gcsUrl or s3Url (for backward compatibility)
      const url = doc.gcsUrl || doc.s3Url;
      if (!url) return doc;

      const signedUrl = await getGCSSignedUrlForInvoice(url);

      return {
        ...doc,
        signedUrl: signedUrl || url, // Fallback to original URL if signing fails
        gcsUrl: url, // Keep original URL
        s3Url: signedUrl || url // Update s3Url for backward compatibility
      };
    }));
  } catch (error) {
    console.error('Error refreshing GCS signed URLs:', error);
    return documents;
  }
}

/**
 * Upload invoice document to AWS (backward compatibility)
 * @param {string} filename - Name of file
 * @param {string} filePath - Path to file
 * @param {string} userId - User ID
 * @returns {Promise<Object>} Upload response with signed URL
 */
async function uploadInvoiceToAws(filename, filePath, userId) {
  try {
    // First upload the file using existing method
    const uploadResult = await uploadBankStatementsToAws(filename, filePath, userId);

    if (uploadResult.error) {
      throw uploadResult.error;
    }

    // Generate signed URL for immediate use
    const signedUrl = await getSignedUrlForInvoice(uploadResult.response.Location);

    return {
      error: null,
      response: {
        ...uploadResult.response,
        signedUrl,
        originalUrl: uploadResult.response.Location
      }
    };
  } catch (error) {
    console.error('Error uploading invoice to AWS:', error);
    return { error, response: null };
  }
}

/**
 * Upload invoice document to GCS
 * @param {string} filename - Name of file
 * @param {string} filePath - Path to file
 * @param {string} userId - User ID
 * @returns {Promise<Object>} Upload response with signed URL
 */
async function uploadInvoiceToGCS(filename, filePath, userId) {
  try {
    // First upload the file using the GCS method
    const uploadResult = await uploadBankStatementsToGCS(filename, filePath, userId);

    if (uploadResult.error) {
      throw uploadResult.error;
    }

    // Generate signed URL for immediate use
    const signedUrl = await getGCSSignedUrlForInvoice(uploadResult.response.Location);

    return {
      error: null,
      response: {
        ...uploadResult.response,
        signedUrl,
        originalUrl: uploadResult.response.Location
      }
    };
  } catch (error) {
    console.error('Error uploading invoice to GCS:', error);
    return { error, response: null };
  }
}

/**
 * Generate a unique ID
 * @returns {string} Unique ID
 */
const uniqueIdGenerator = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
};

/**
 * Get content type based on file extension
 * @param {string} filename 
 * @returns {string} Content type
 */
function getContentType(filename) {
  const ext = path.extname(filename).toLowerCase();

  switch (ext) {
    case '.pdf': return 'application/pdf';
    case '.jpg':
    case '.jpeg': return 'image/jpeg';
    case '.png': return 'image/png';
    case '.txt': return 'text/plain';
    case '.doc': return 'application/msword';
    case '.docx': return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
    case '.xls': return 'application/vnd.ms-excel';
    case '.xlsx': return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    default: return 'application/octet-stream';
  }
}

// Error handling wrapper for async functions
const asyncHandler = (fn) => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};


function formatDocumentEntitiesJS(data) {
  const formattedEntities = [];

  if (typeof data !== 'object' || data === null) {
    // console.error("Error: Input data must be an object.");
    return formattedEntities;
  }

  const document = data.document;
  if (typeof document !== 'object' || document === null) {
    // console.error("Error: 'document' key not found in data or is not an object.");
    return formattedEntities;
  }

  const entities = document.entities;
  if (!Array.isArray(entities)) {
    // console.error("Error: 'document.entities' key not found or is not an array.");
    return formattedEntities;
  }

  for (const entity of entities) {
    if (typeof entity === 'object' && entity !== null) {
      const entityType = entity.type;
      const mentionText = entity.mentionText;

      // Ensure both 'type' and 'mentionText' are present and not undefined
      if (entityType !== undefined && mentionText !== undefined) {
        formattedEntities.push({
          type: entityType,
          mentionText: mentionText
        });
      }
      // else {
      // Optionally, you could log or handle entities missing these fields:
      // console.warn("Skipping entity due to missing 'type' or 'mentionText':", entity);
      // }
    }
    // else {
    // Optionally, log or handle items in the entities list that are not objects:
    // console.warn("Skipping non-object item in entities list:", entity);
    // }
  }

  return formattedEntities;
}


function analyzeBankStatement(data) {
  // Initial checks on the input 'data' (expected to be the 'extractedFields' object)
  if (!data || typeof data !== 'object') {
      return {
          error: "Invalid input: 'data' is not a valid object.",
          // Provide default structure for all expected result keys
          monthly_net_cash_flow: {}, 
          average_transaction_size_deposit: 0, 
          average_transaction_size_withdrawal: 0, 
          average_transaction_size_overall: 0,
          cash_retention_rate: 0, 
          monthly_balances: {}, 
          bounced_items_count: 0, 
          bounced_items_details: [],
          counterparty_concentration_inflows_summary: {},
          counterparty_concentration_inflows_details: [],
          loan_repayments_observed_count: 0, 
          loan_repayments_observed_details: [],
          significant_transactions_by_amount_details: [], 
          unusual_transactions_by_description_details: [],
          top_significant_transactions: { top_credits: [], top_debits: [] } 
      };
  }

  if (!data.transactions || !Array.isArray(data.transactions)) {
      // console.error("Debug: data.transactions is missing or not an array. data.transactions:", data.transactions);
      return {
          error: "'data.transactions' is missing or not an array.",
          // Provide default structure
          monthly_net_cash_flow: {}, average_transaction_size_deposit: 0, average_transaction_size_withdrawal: 0, average_transaction_size_overall: 0,
          cash_retention_rate: 0, monthly_balances: {}, bounced_items_count: 0, bounced_items_details: [],
          counterparty_concentration_inflows_summary: {}, counterparty_concentration_inflows_details: [],
          loan_repayments_observed_count: 0, loan_repayments_observed_details: [],
          significant_transactions_by_amount_details: [], unusual_transactions_by_description_details: [],
          top_significant_transactions: { top_credits: [], top_debits: [] } 
      };
  }
  const transactionsRaw = data.transactions;

  const results = {
      monthly_net_cash_flow: {},
      average_transaction_size_deposit: 0,
      average_transaction_size_withdrawal: 0,
      average_transaction_size_overall: 0,
      cash_retention_rate: 0,
      monthly_balances: {},
      bounced_items_count: 0,
      bounced_items_details: [],
      counterparty_concentration_inflows_summary: {},
      counterparty_concentration_inflows_details: [],
      loan_repayments_observed_count: 0,
      loan_repayments_observed_details: [],
      significant_transactions_by_amount_details: [],
      unusual_transactions_by_description_details: [],
      top_significant_transactions: { top_credits: [], top_debits: [] },
      opening_balance_period: data.opening_balance,
      closing_balance_period: data.closing_balance,
      total_deposits_period: data.total_deposits,
      total_withdrawals_period: data.total_withdrawals
  };

  function safeFloat(value) {
      if (value === null || value === undefined || String(value).trim() === "") {
          return 0.0;
      }
      try {
          return parseFloat(String(value).replace(/,/g, ''));
      } catch (e) {
          return 0.0;
      }
  }

  function parseDate(dateStr) {
      if (!dateStr || typeof dateStr !== 'string' || dateStr.trim() === "") {
          return null;
      }
      // 1. Try exact YYYY-MM-DD format (assume UTC for consistency)
      if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
          const [year, month, day] = dateStr.split('-').map(Number);
          if (!isNaN(year) && !isNaN(month) && !isNaN(day) && month >= 1 && month <= 12 && day >=1 && day <=31) {
              const dateObj = new Date(Date.UTC(year, month - 1, day));
              if (!isNaN(dateObj.getTime())) return dateObj;
          }
      }
      // 2. ISO 8601 format (includes time and Z for UTC or timezone offset)
      if (/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d+)?(?:Z|[+-]\d{2}(?::\d{2})?|[+-]\d{4})?$/.test(dateStr)) {
          const dateObj = new Date(dateStr);
          if (!isNaN(dateObj.getTime())) return dateObj;
      }
      // Explicitly return null for formats not suitable for aggregation or ambiguous
      if (/^\d{4}$/.test(dateStr) || /^\d{1,2}\/\d{1,2}$/.test(dateStr) || /^\d{1,2}\/\d{1,2}\/\d{2,4}$/.test(dateStr)) {
          return null;
      }
      return null;
  }

  const processedTransactions = [];
  // console.log(`Debug: Starting to process ${transactionsRaw.length} raw transactions.`);
  transactionsRaw.forEach((txRaw, index) => {
      const tx = {};
      // Ensure txRaw is an object, otherwise skip
      if (typeof txRaw !== 'object' || txRaw === null) {
          // console.log(`Debug: txRaw at index ${index} is not an object, skipping.`);
          return;
      }

      const rawDeposit = txRaw['table_item/transaction_deposit'];
      const rawWithdrawal = txRaw['table_item/transaction_withdrawal'];
      tx.deposit_amount = Math.abs(safeFloat(rawDeposit));
      tx.withdrawal_amount = Math.abs(safeFloat(rawWithdrawal));

      if (tx.deposit_amount > 0) {
          tx.amount = tx.deposit_amount;
          tx.type = 'deposit';
      } else if (tx.withdrawal_amount > 0) {
          tx.amount = tx.withdrawal_amount;
          tx.type = 'withdrawal';
      } else {
          tx.amount = 0;
          tx.type = 'description_only';
      }

      tx.description_deposit = txRaw['table_item/transaction_deposit_description'] || '';
      tx.description_withdrawal = txRaw['table_item/transaction_withdrawal_description'] || '';
      tx.description = (tx.description_deposit + " " + tx.description_withdrawal).trim();

      const rawDateDeposit = txRaw['table_item/transaction_deposit_date'];
      const rawDateWithdrawal = txRaw['table_item/transaction_withdrawal_date'];
      const dateDeposit = parseDate(rawDateDeposit);
      const dateWithdrawal = parseDate(rawDateWithdrawal);

      if (dateDeposit && dateWithdrawal && dateDeposit.getTime() === dateWithdrawal.getTime()) {
          tx.date = dateDeposit;
      } else if (dateDeposit) {
          tx.date = dateDeposit;
      } else {
          tx.date = dateWithdrawal;
      }
      
      // Debugging log for individual transaction processing
      // if (index < 5) { // Log first 5 for inspection
      //     console.log(`Debug txRaw[${index}]:`, JSON.stringify(txRaw));
      //     console.log(`Debug parsed tx[${index}]: date=${tx.date}, deposit_amount=${tx.deposit_amount}, withdrawal_amount=${tx.withdrawal_amount}, type=${tx.type}`);
      // }

      if (tx.date && (tx.deposit_amount > 0 || tx.withdrawal_amount > 0)) {
          processedTransactions.push(tx);
      }
  });

  // console.log(`Debug: Finished processing. ${processedTransactions.length} transactions are valid.`);

  if (processedTransactions.length === 0) {
      results.error = "No valid transactions with parseable dates and non-zero amounts found to process.";
      return results; // Return the results object which now contains the error and default values
  }

  processedTransactions.sort((a, b) => a.date.getTime() - b.date.getTime());

  // --- 1. Monthly Net Cash Flow ---
  const monthlySummary = {};
  processedTransactions.forEach(tx => {
      if (!tx.date || isNaN(tx.date.getTime())) return;
      const monthYearKey = `${tx.date.getUTCFullYear()}-${String(tx.date.getUTCMonth() + 1).padStart(2, '0')}`;
      if (!monthlySummary[monthYearKey]) {
          monthlySummary[monthYearKey] = { deposits: 0, withdrawals: 0, net_flow: 0, transactions_count: 0 };
      }
      if (tx.type === 'deposit') monthlySummary[monthYearKey].deposits += tx.amount;
      else if (tx.type === 'withdrawal') monthlySummary[monthYearKey].withdrawals += tx.amount;
      monthlySummary[monthYearKey].transactions_count += 1;
  });
  Object.keys(monthlySummary).sort().forEach(month => {
      monthlySummary[month].net_flow = monthlySummary[month].deposits - monthlySummary[month].withdrawals;
      results.monthly_net_cash_flow[month] = monthlySummary[month];
  });

  // --- Monthly Balances (End-Of-Month) ---
  let currentEomBalance = safeFloat(results.opening_balance_period);
  const sortedMonthsForBalance = Object.keys(results.monthly_net_cash_flow).sort();
  if (sortedMonthsForBalance.length > 0 && results.opening_balance_period !== undefined) {
      results.monthly_balances[`${sortedMonthsForBalance[0]}_start_period_balance`] = currentEomBalance;
      sortedMonthsForBalance.forEach(monthKey => {
          currentEomBalance += results.monthly_net_cash_flow[monthKey].net_flow;
          results.monthly_balances[monthKey] = currentEomBalance;
      });
  } else if (results.opening_balance_period === undefined) {
      results.monthly_balances.error = "Opening balance not available for calculation.";
  }

  // --- 2. Average Transaction Size ---
  const depositTxns = processedTransactions.filter(tx => tx.type === 'deposit');
  const withdrawalTxns = processedTransactions.filter(tx => tx.type === 'withdrawal');
  const totalDepositVal = depositTxns.reduce((sum, tx) => sum + tx.amount, 0);
  const numDeposits = depositTxns.length;
  const totalWithdrawalVal = withdrawalTxns.reduce((sum, tx) => sum + tx.amount, 0);
  const numWithdrawals = withdrawalTxns.length;

  if (numDeposits > 0) results.average_transaction_size_deposit = totalDepositVal / numDeposits;
  if (numWithdrawals > 0) results.average_transaction_size_withdrawal = totalWithdrawalVal / numWithdrawals;
  if ((numDeposits + numWithdrawals) > 0) results.average_transaction_size_overall = (totalDepositVal + totalWithdrawalVal) / (numDeposits + numWithdrawals);

  // --- 3. Cash Retention Rate ---
  const openingBal = safeFloat(results.opening_balance_period);
  const closingBal = safeFloat(results.closing_balance_period);
  const totalDepsPeriod = safeFloat(results.total_deposits_period);
  if (results.opening_balance_period === undefined || results.closing_balance_period === undefined || results.total_deposits_period === undefined) {
      results.cash_retention_rate = "N/A";
  } else if (totalDepsPeriod > 0) {
      results.cash_retention_rate = ((closingBal - openingBal) / totalDepsPeriod) * 100;
  } else {
      results.cash_retention_rate = (openingBal === closingBal && totalDepsPeriod === 0) ? 0 : "N/A (No deposits)";
  }

  // --- 4. Bounced Items ---
  const bouncedKeywords = ["returned chq", "insufficient funds", "cheque returned", "chgs-cheque returned"];
  processedTransactions.forEach(tx => {
      if (!tx.description) return;
      const descLower = tx.description.toLowerCase();
      if (bouncedKeywords.some(keyword => descLower.includes(keyword))) {
          results.bounced_items_count++;
          results.bounced_items_details.push({
              date: tx.date ? `${tx.date.getUTCFullYear()}-${String(tx.date.getUTCMonth() + 1).padStart(2, '0')}-${String(tx.date.getUTCDate()).padStart(2, '0')}` : "N/A",
              description: tx.description, amount: tx.amount, type: tx.type
          });
      }
  });

  // --- 5. Counterparty Concentration (Inflows) ---
  const inflowCounterparties = {};
  depositTxns.forEach(tx => {
      if (tx.amount > 0) {
          const desc = tx.description_deposit || tx.description;
          let cpName = "Unknown";
          if (desc.match(/INWARD REMITTANCE\s*([A-Z0-9\s&.-]+)/i)) cpName = desc.match(/INWARD REMITTANCE\s*([A-Z0-9\s&.-]+)/i)[1].trim();
          else if (desc.toLowerCase().includes("regular clearing") && desc.toLowerCase().includes("ref/nin")) cpName = (desc.match(/REF\/NIN\s*([A-Z0-9\s.-]*)/i) && desc.match(/REF\/NIN\s*([A-Z0-9\s.-]*)/i)[1].trim()) ? `REGULAR CLEARING REF: ${desc.match(/REF\/NIN\s*([A-Z0-9\s.-]*)/i)[1].trim()}` : "REGULAR CLEARING (REF/NIN)";
          else if (desc.toLowerCase().includes("internal transfer")) cpName = (desc.match(/INTERNAL TRANSFER\s*.*?([A-Z\s.&-]{5,})/i) && desc.match(/INTERNAL TRANSFER\s*.*?([A-Z\s.&-]{5,})/i)[1].trim()) ? `INTERNAL TRANSFER: ${desc.match(/INTERNAL TRANSFER\s*.*?([A-Z\s.&-]{5,})/i)[1].trim()}` : "INTERNAL TRANSFER";
          else if (desc.toUpperCase().includes("CASH DEPOSIT")) cpName = "CASH DEPOSIT";

          if (!inflowCounterparties[cpName]) inflowCounterparties[cpName] = { total_amount: 0, count: 0 };
          inflowCounterparties[cpName].total_amount += tx.amount;
          inflowCounterparties[cpName].count++;
          results.counterparty_concentration_inflows_details.push({
              date: tx.date ? `${tx.date.getUTCFullYear()}-${String(tx.date.getUTCMonth() + 1).padStart(2, '0')}-${String(tx.date.getUTCDate()).padStart(2, '0')}` : "N/A",
              amount: tx.amount, identified_source: cpName, description: desc
          });
      }
  });
  if (totalDepsPeriod > 0) {
      Object.keys(inflowCounterparties).forEach(cp => {
          results.counterparty_concentration_inflows_summary[cp] = {
              total_amount: inflowCounterparties[cp].total_amount, count: inflowCounterparties[cp].count,
              percentage_of_total_deposits: (inflowCounterparties[cp].total_amount / totalDepsPeriod) * 100
          };
      });
      results.counterparty_concentration_inflows_summary = Object.fromEntries(Object.entries(results.counterparty_concentration_inflows_summary).sort(([,a],[,b]) => b.total_amount - a.total_amount));
  }

  // --- 6. Loan Repayments Observed ---
  const loanKeywords = ["loan", "emi", "instalment", "finance", "repayment"];
  withdrawalTxns.forEach(tx => {
      if (!tx.description) return;
      if (loanKeywords.some(k => tx.description.toLowerCase().includes(k))) {
          results.loan_repayments_observed_count++;
          results.loan_repayments_observed_details.push({
              date: tx.date ? `${tx.date.getUTCFullYear()}-${String(tx.date.getUTCMonth() + 1).padStart(2, '0')}-${String(tx.date.getUTCDate()).padStart(2, '0')}` : "N/A",
              description: tx.description, amount: tx.amount
          });
      }
  });

  // --- 7. Significant & Unusual Transactions ---
  const sigFactor = 3, sigFixed = 50000;
  const unusualKwds = ["penalty", "chargeback", "legal", "seizure", "fund to sister company"];
  processedTransactions.forEach(tx => {
      if (!tx.date || isNaN(tx.date.getTime())) return;
      const txDetail = {
          date: `${tx.date.getUTCFullYear()}-${String(tx.date.getUTCMonth() + 1).padStart(2, '0')}-${String(tx.date.getUTCDate()).padStart(2, '0')}`,
          description: tx.description, amount: tx.amount, type: tx.type
      };
      if ((results.average_transaction_size_overall > 0 && tx.amount > (results.average_transaction_size_overall * sigFactor)) || tx.amount > sigFixed) {
          results.significant_transactions_by_amount_details.push(txDetail);
      }
      if (tx.description) {
          const descLower = tx.description.toLowerCase();
          if (unusualKwds.some(k => descLower.includes(k))) {
               if (!results.significant_transactions_by_amount_details.some(s => s.date === txDetail.date && s.amount === txDetail.amount && s.description === txDetail.description)) { // Avoid duplicates if already significant by amount
                  results.unusual_transactions_by_description_details.push({...txDetail, reason: `Keyword match`});
               }
          }
          if (tx.type === 'withdrawal' && (descLower.includes("atm cash") || descLower.includes("csh withdrawal-chq")) && tx.amount >= 15000) {
               if (!results.significant_transactions_by_amount_details.some(s => s.date === txDetail.date && s.amount === txDetail.amount) && 
                   !results.unusual_transactions_by_description_details.some(s => s.date === txDetail.date && s.amount === txDetail.amount)) {
                  results.unusual_transactions_by_description_details.push({...txDetail, reason: "Large Cash Withdrawal"});
               }
          }
      }
  });


  // --- 8. Top Significant Transactions (Top 3 Credits and Debits) ---
  const sortedDepositsForTop = [...depositTxns].sort((a, b) => b.amount - a.amount);
  const sortedWithdrawalsForTop = [...withdrawalTxns].sort((a, b) => b.amount - a.amount);

  results.top_significant_transactions = {
      top_credits: sortedDepositsForTop.slice(0, 3).map(tx => ({
          date: tx.date ? `${tx.date.getUTCFullYear()}-${String(tx.date.getUTCMonth() + 1).padStart(2, '0')}-${String(tx.date.getUTCDate()).padStart(2, '0')}` : "N/A",
          description: tx.description,
          amount: tx.amount,
          type: tx.type
      })),
      top_debits: sortedWithdrawalsForTop.slice(0, 3).map(tx => ({
          date: tx.date ? `${tx.date.getUTCFullYear()}-${String(tx.date.getUTCMonth() + 1).padStart(2, '0')}-${String(tx.date.getUTCDate()).padStart(2, '0')}` : "N/A",
          description: tx.description,
          amount: tx.amount,
          type: tx.type
      }))
  };
  
  return results;
}

module.exports = {
  // S3 functions (backward compatibility)
  getSignedUrl,
  getSignedUrlToDownload,
  upload,
  uploadBankStatements,
  getSingedUrlToDownloadForBankStatement,
  uploadBankStatementsToAws,
  getSignedUrlForInvoice,
  refreshSignedUrls,
  uploadInvoiceToAws,
  uploadBufferToGCS,

  // New GCS functions
  uploadToGCS,
  getGCSSignedUrl,
  getGCSSignedUrlToDownload,
  uploadBankStatementsToGCS,
  getGCSSignedUrlForBankStatement,
  getGCSSignedUrlForInvoice,
  refreshGCSSignedUrls,
  uploadInvoiceToGCS,
  uploadFileToGCS,
  asyncHandler,
  extractDocumentFields
};