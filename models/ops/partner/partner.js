const mongoose = require("mongoose");
const path = require('path')
const joi = require("@hapi/joi");
const { boolean } = require("@hapi/joi");
const { Date } = require("mongoose");
const Schema = mongoose.Schema;
let dbConfig = require("config");

const caPath = path.join(__dirname, '../../../certs/madad-ff-dev-uat-mongodb-private.crt');
const options = {
  tls: true,
  tlsCAFile: caPath,
  keepAlive: true,
  useUnifiedTopology: true,
  useNewUrlParser: true,
  useCreateIndex: true,
  useFindAndModify: false,
}

const mongoosedbCredit = mongoose.createConnection(dbConfig.get("dbConfig.CreditRiskEngineConnectionString"), options);


const partnerMasterSchema = new mongoose.Schema({
  partnerName: { type: String },
  partnerCode: { type: String },
  partnerType: { type: String },
  interestType: { type: String },
  roundType: { type: String },
  typeOfEntity: { type: String },
  doi: { type: Date },
  processingCharge: { type: Number },
  authKey: { type: String },
  shortauthKey: { type: String },
  dispayHeaderFooter: { type: Boolean, default: true },
  fldgPer: { type: Number },
  isActive: { type: Boolean, default: false },
  insertedOn: { type: Date, default: Date.now },
  contractTemplate: { type: String },
  displayLogo: { type: Boolean, default: false },
  logoFileName: { type: String, default: null },
  displayTrueScore: { type: Boolean, default: true },
  isApiCall: { type: Boolean, default: false },
  apiAccess: {
    loanRepayment: { type: Boolean, default: false },
    setEmiSchedule: { type: Boolean, default: false },
    loanRepaymentEnquiry: { type: Boolean, default: false },
    loanRepaymentDailyLoans: { type: Boolean, default: false },
    loanRepaymentForALLStatus: { type: Boolean, default: false }, //set true if need to call Written-Off repayment request while calling Loan In Progress requests
    consolidatedLoanRepayment: { type: Boolean, default: false },
  },
  isRetailer: { type: Boolean, default: true },
  showHeaderMenu: { type: Boolean, default: false },
  showFooterMenu: { type: Boolean, default: false },
  headerContent: { type: String },
  isloanDisabled: { type: Boolean, default: true },
  isReviewDisabled: { type: Boolean, default: true },
  callBackUrl: { type: String },
  contractSuccessCallBackUrl: { type: String },
  eNachErrorCallBackUrl: { type: String },
  displayName: { type: String },
  disbursalAccount: { type: String },
  bankAccounts: [{
    name: { type: String },
    bankName: { type: String },
    branch: { type: String },
    type: { type: String },
    no: { type: String },
    ifsc: { type: String },
    transactionType: { type: String },
    paymentType: { type: String },
    purposeCode: { type: String },
    address: { type: String },
    city: { type: String },
    state: { type: String },
    isActive: { type: Boolean, default: true },
    insertedOn: { type: Date, default: Date.now, format: "date-time" },
    updatedOn: { type: Date, default: Date.now, format: "date-time" }
  }],
  multiUserLoansAllowed: { type: Boolean, default: true },
  emails: {
    collectionReportEmailsTo: { type: String },
    collectionReprotEmailCc: { type: String },
    disbursalReportEmailsTo: { type: String },
    disbursalReportEmailsCc: { type: String },
    opsReportEmailTo: { type: String },
    opsReportEmailCc: { type: String },
    nocEmailsTo: { type: String },
    nocEmailsCc: { type: String }
  },
  displayLoanTabs: { type: Boolean, default: true },
  brandName: { type: String },
  token: { type: String },
  tokenExpireAt: { type: Date, format: "date-time" },
  isWalkInCustomer: { type: Boolean, default: false },
  creditContractTemplate: { type: String },
  activeLoansAllowed: { type: Boolean, default: true },
  processingFeeDisPer: { type: Number, default: 0 },
  hideFFLogo: { type: Boolean, default: false },
  panNo: { type: String },
  addressDetails: [{
    add1: { type: String },
    add2: { type: String },
    add3: { type: String },
    type: { type: String },
    city: { type: String },
    state: { type: String },
    pincode: { type: String },
    country: { type: String },
    insertedOn: { type: Date, default: Date.now, format: "date-time" },
  }],
  ledgerDetails: {
    virtualAccountNo: { type: String },
    balance: { type: Number },
    holdAmount: { type: Number },
    totalVACredit: { type: Number },
    payOut: { type: Number },
    daysInvested: { type: Number },
    insertedOn: { type: Date, default: Date.now, format: "date-time" },
    updatedOn: { type: Date, default: Date.now, format: "date-time" },
  },
  grievanceDetails: {
    name: { type: String },
    email: { type: String },
    contactNo: { type: String },
    address: { type: String }
  },
  secretKey: { type: String }
});

exports.PartnerMaster = mongoose.model("PartnerMaster", partnerMasterSchema, "PartnerMaster");

const ledgerAccountSchema = new mongoose.Schema({
  userId: Schema.ObjectId,
  partnerId: Schema.ObjectId,
  loanId: Schema.ObjectId,
  transactionDate: { type: Date, format: "date-time" },
  depositDate: { type: Date, format: "date-time" },
  utrNo: { type: String },
  narration: { type: String },
  installmentNumber: { type: Number },
  creditAmt: { type: Number },
  debitAmt: { type: Number },
  holdAmt: { type: Number },
  balance: { type: Number },
  insertedOn: { type: Date, default: Date.now, format: "date-time" },
  borrowingPartnerId: Schema.ObjectId,
  type: { type: String }
});

exports.LedgerAccount = mongoose.model("LedgerAccount", ledgerAccountSchema, "LedgerAccount");

const PartnerLoansSchema = new mongoose.Schema({
  partnerId: Schema.ObjectId,
  trxnId: { type: String },
  productId: { type: String },
  loanNo: { type: String },
  panNo: { type: String },
  clientCode: { type: String },
  merchantCode: { type: String },
  loanRequest: {
    firstName: { type: String },
    middleName: { type: String },
    lastName: { type: String },
    mobileNo: { type: String },
    emailId: { type: String },
    aadhaarNo: { type: String },
    city: { type: String },
    street: { type: String },
    state: { type: String },
    pincode: { type: String },
    merchantType: { type: String },
    loanAmount: { type: Number },
    tenure: { type: Number },
    onboardedDate: { type: Date, format: "date-time" },
    isWeekendLoan: { type: String }
  },
  personalDetails: {
    name: { type: String },
    gender: { type: String },
    qualification: { type: String },
    dob: { type: Date, format: "date-time" },
    maritalStatus: { type: String },
    mobile: { type: String },
    alternateMobile: { type: String },
    homeOwnershipType: { type: String },
    currentAdd: { type: String },
    city: { type: String },
    state: { type: String },
    pincode: { type: String },
    marital_status: { type: String },
    phoneConnectionType: { type: String },
    whatsAppInMobile: { type: String },
    children: { type: String },
    ownHome: { type: String },
    vehicle: { type: String },
    avgMonthlyOutgoings: { type: String },
    avgMonthlyIncome: { type: String },
    jobType: { type: String },
    whiteGoodsIn24Months: { type: String },
    educationalDegree: { type: String },
  },
  shopDetails: {
    name: { type: String },
    type: { type: String },
    add: { type: String },
    city: { type: String },
    state: { type: String },
    pincode: { type: String },
    location: { type: String },
    contactNo: { type: String },
    ownershipType: { type: String },
    companyType: { type: String },
    shopVinatge: { type: String },
    businessVintage: { type: String },
  },
  accoutDetails: {
    mobile: { type: String },
    panNo: { type: String },
    merchantCode: { type: String },
  },
  bankDetails: {
    accountName: { type: String },
    accountType: { type: String },
    accountNo: { type: String },
    ifsc: { type: String },
  },
  transactionDetails: [
    {
      primary: { type: String },
      type: { type: String },
      value: { type: String },
      monthYear: { type: String },
      valueType: { type: String },
      primaryCount: { type: String },
    },
  ],
  loanHistory: [
    {
      providerName: { type: String },
      amount: { type: Number },
      status: { type: String },
      totalRepaidAmount: { type: Number },
    },
  ],
  documents: [
    {
      docNo: { type: String },
      docType: { type: String },
      docPath: { type: String },
      docExtension: { type: String }
    }
  ],
  loanOffer: [
    {
      step_num: { type: String },
      proceed: { type: Boolean },
      claimed: { type: Boolean },
      reason: { type: String },
      description: { type: String },
      loanAmount: { type: Number },
      interestRate: { type: Number },
      interestType: { type: String },
      repaymentDays: { type: Number },
      processingFee: { type: Number },
      loanType: { type: String },
      tenure: { type: Number },
      autoRepaymentStatus: { type: Number },
      additionalData: { type: String },
      expiryDate: { type: Date, format: "date-time" },
      totalAmountPayable: { type: Number },
      interestPayable: { type: Number },
      emiAmount: { type: Number },
      totalEmi: { type: Number },
      requestedAt: { type: Date, format: "date-time" },
      disbursedAmount: { type: Number },
      isActive: { type: Boolean },
      insertedOn: { type: Date, default: Date.now, format: "date-time" },
      deactivatedOn: { type: Date, format: "date-time" },
    },
  ],
  loanOfferResponse: [
    {
      description: { type: String },
      status: { type: Number },
      isOfferAcceptance: { type: Boolean },
      insertedOn: { type: Date, default: Date.now, format: "date-time" },
    },
  ],
  borrowerAcceptance: [
    {
      step_num: { type: Number },
      customerAccept: { type: Boolean },
      reason: { type: String },
      description: { type: String },
      insertedOn: { type: Date, default: Date.now, format: "date-time" },
    },
  ],
  disbursal: [
    {
      remarks: { type: String },
      disbursedOn: { type: String },
      bankReferenceNo: { type: String },
      amount: { type: Number },
      agentCode: { type: String },
      source: { type: String },
      success: { type: Boolean },
      errorDesc: { type: String },
      bank: { type: String },
      paymentMode: { type: Number },
      clientRefId: { type: Number },
      bc: { type: String },
      bankCode: { type: String },
      insertedOn: { type: Date, default: Date.now, format: "date-time" },
    },
  ],
  disbursalResponse: [
    {
      discription: { type: String },
      status: { type: Number },
      insertedOn: { type: Date, default: Date.now, format: "date-time" },
    },
  ],
  notifications: [
    {
      type: { type: String },
      processingFee: { type: String },
      amountOffered: { type: String },
      tenure: { type: String },
      loanAmount: { type: String },
      amountDisbursed: { type: String },
      emiCounts: { type: String },
      emiAmount: { type: String },
      emiAmountInterest: { type: String },
      emiAmountPrinciple: { type: String },
      yearlyInterestRate: { type: String },
      status: { type: String },
      requestRef: { type: String },
      err_code: { type: String },
      desc: { type: String },
      bankReferenceNo: { type: String },
      insertedOn: { type: Date, default: Date.now, format: "date-time" },
    },
  ],
  statusUpdate: [
    {
      loanStatus: { type: String },
      status: { type: String },
      description: { type: String },
      insertedOn: { type: Date, default: Date.now, format: "date-time" },
    },
  ],
  ipAddress: { type: String },
  virtualAccountNo: { type: String },
  isActive: { type: Boolean, default: true },
  insertedOn: { type: Date, default: Date.now, format: "date-time" },
  modifiedOn: { type: Date, format: "date-time" },
  deactivatedOn: { type: Date, format: "date-time" },
  digilocker: {
    execution_request_id: { type: String },
    created_at: { type: Date },
    updated_at: { type: Date },
    reference_id: { type: String },
    status: { type: String },
    processing_done: { type: Boolean },
    face_match_status: { type: String },
    validation_result: { type: Object },
    face_match_obj_type: { type: String },
    obj_analysis_status: { type: String },
    document_details: {
      aadhaar: {
        docNo: { type: String },
        document_type: { type: String },
        id_proof_type: { type: String },
        gender: { type: String },
        image: { type: String },
        name: { type: String },
        dob: { type: String },
        current_address: { type: String },
        permanent_address: { type: String },
        current_address_details: {
          address: { type: String },
          locality_or_post_office: { type: String },
          district_or_city: { type: String },
          state: { type: String },
          pincode: { type: String },
        },
        permanent_address_details: {
          address: { type: String },
          locality_or_post_office: { type: String },
          district_or_city: { type: String },
          state: { type: String },
          pincode: { type: String },
        },
        docPath: { type: String },
      },
      pan: {
        name: { type: String },
        docNo: { type: String },
        dob: { type: String },
        document_type: { type: String },
        id_proof_type: { type: String },
        typeOfHolder: { type: String },
        isIndividual: { type: Boolean },
        isValid: { type: Boolean },
        firstName: { type: String },
        middleName: { type: String },
        lastName: { type: String },
        title: { type: String },
        panStatus: { type: String },
        panStatusCode: { type: String },
        aadhaarSeedingStatus: { type: String },
        aadhaarSeedingStatusCode: { type: String },
        lastUpdatedOn: { type: String },
        docPath: { type: String },
      }
    }
  },
  contractSuccessCallBackUrl: { type: String },
  eNachErrorCallBackUrl: { type: String },
  isBankValidated: { type: Boolean, default: false },
  beneficiary_name: { type: String },
  responseSuccess: { type: Boolean },
  responseMessage: { type: String },
  errorMsg: { type: String }
});

exports.PartnerLoans = mongoose.model("PartnerLoans", PartnerLoansSchema, "PartnerLoans");

const PartnerLoansHistorySchema = new mongoose.Schema({
  partnerLoanId: Schema.ObjectId,
  partnerId: Schema.ObjectId,
  trxnId: { type: String },
  productId: { type: String },
  loanNo: { type: String },
  panNo: { type: String },
  clientCode: { type: String },
  merchantCode: { type: String },
  loanRequest: {
    firstName: { type: String },
    middleName: { type: String },
    lastName: { type: String },
    mobileNo: { type: String },
    emailId: { type: String },
    aadhaarNo: { type: String },
    city: { type: String },
    street: { type: String },
    state: { type: String },
    pincode: { type: String },
    merchantType: { type: String },
    loanAmount: { type: Number },
    tenure: { type: Number },
    onboardedDate: { type: Date, format: "date-time" },
    isWeekendLoan: { type: String }
  },
  personalDetails: {
    name: { type: String },
    gender: { type: String },
    qualification: { type: String },
    dob: { type: Date, format: "date-time" },
    maritalStatus: { type: String },
    mobile: { type: String },
    alternateMobile: { type: String },
    homeOwnershipType: { type: String },
    currentAdd: { type: String },
    city: { type: String },
    state: { type: String },
    pincode: { type: String },
    marital_status: { type: String },
    phoneConnectionType: { type: String },
    whatsAppInMobile: { type: String },
    children: { type: String },
    ownHome: { type: String },
    vehicle: { type: String },
    avgMonthlyOutgoings: { type: String },
    avgMonthlyIncome: { type: String },
    jobType: { type: String },
    whiteGoodsIn24Months: { type: String },
    educationalDegree: { type: String },
  },
  shopDetails: {
    name: { type: String },
    type: { type: String },
    add: { type: String },
    city: { type: String },
    state: { type: String },
    pincode: { type: String },
    location: { type: String },
    contactNo: { type: String },
    ownershipType: { type: String },
    companyType: { type: String },
    shopVinatge: { type: String },
    businessVintage: { type: String },
  },
  accoutDetails: {
    mobile: { type: String },
    panNo: { type: String },
    merchantCode: { type: String },
  },
  bankDetails: {
    accountName: { type: String },
    accountType: { type: String },
    accountNo: { type: String },
    ifsc: { type: String },
  },
  transactionDetails: [
    {
      primary: { type: String },
      type: { type: String },
      value: { type: String },
      monthYear: { type: String },
      valueType: { type: String },
      primaryCount: { type: String },
    },
  ],
  loanHistory: [
    {
      providerName: { type: String },
      amount: { type: Number },
      status: { type: String },
      totalRepaidAmount: { type: Number },
    },
  ],
  documents: [
    {
      docNo: { type: String },
      docType: { type: String },
      docPath: { type: String },
      docExtension: { type: String }
    }
  ],
  loanOffer: [
    {
      step_num: { type: String },
      proceed: { type: Boolean },
      claimed: { type: Boolean },
      reason: { type: String },
      description: { type: String },
      loanAmount: { type: Number },
      interestRate: { type: Number },
      interestType: { type: String },
      repaymentDays: { type: Number },
      processingFee: { type: Number },
      loanType: { type: String },
      tenure: { type: Number },
      autoRepaymentStatus: { type: Number },
      additionalData: { type: String },
      expiryDate: { type: Date, format: "date-time" },
      totalAmountPayable: { type: Number },
      interestPayable: { type: Number },
      emiAmount: { type: Number },
      totalEmi: { type: Number },
      requestedAt: { type: Date, format: "date-time" },
      disbursedAmount: { type: Number },
      isActive: { type: Boolean },
      insertedOn: { type: Date, default: Date.now, format: "date-time" },
      deactivatedOn: { type: Date, format: "date-time" },
    },
  ],
  loanOfferResponse: [
    {
      description: { type: String },
      status: { type: Number },
      isOfferAcceptance: { type: Boolean },
      insertedOn: { type: Date, default: Date.now, format: "date-time" },
    },
  ],
  borrowerAcceptance: [
    {
      step_num: { type: Number },
      customerAccept: { type: Boolean },
      reason: { type: String },
      description: { type: String },
      insertedOn: { type: Date, default: Date.now, format: "date-time" },
    },
  ],
  disbursal: [
    {
      remarks: { type: String },
      disbursedOn: { type: String },
      bankReferenceNo: { type: String },
      amount: { type: Number },
      agentCode: { type: String },
      source: { type: String },
      bank: { type: String },
      paymentMode: { type: Number },
      clientRefId: { type: Number },
      bc: { type: String },
      bankCode: { type: String },
      bankReferenceNo: { type: String },
      errorDesc: { type: String },
      isDisbursed: { type: Boolean },
      insertedOn: { type: Date, default: Date.now, format: "date-time" },
    },
  ],
  disbursalResponse: [
    {
      discription: { type: String },
      status: { type: Number },
      insertedOn: { type: Date, default: Date.now, format: "date-time" },
    },
  ],
  notifications: [
    {
      type: { type: String },
      processingFee: { type: String },
      amountOffered: { type: String },
      tenure: { type: String },
      loanAmount: { type: String },
      amountDisbursed: { type: String },
      emiCounts: { type: String },
      emiAmount: { type: String },
      emiAmountInterest: { type: String },
      emiAmountPrinciple: { type: String },
      yearlyInterestRate: { type: String },
      status: { type: String },
      requestRef: { type: String },
      err_code: { type: String },
      desc: { type: String },
      insertedOn: { type: Date, default: Date.now, format: "date-time" },
    },
  ],
  statusUpdate: [
    {
      loanStatus: { type: String },
      status: { type: String },
      description: { type: String },
      insertedOn: { type: Date, default: Date.now, format: "date-time" },
    },
  ],
  ipAddress: { type: String },
  virtualAccountNo: { type: String },
  isActive: { type: Boolean, default: true },
  insertedOn: { type: Date, default: Date.now, format: "date-time" },
  modifiedOn: { type: Date, format: "date-time" },
  deactivatedOn: { type: Date, format: "date-time" },
  deletedOn: { type: Date, default: Date.now, format: "date-time" }
});

exports.PartnerLoansHistory = mongoose.model("PartnerLoansHistory", PartnerLoansHistorySchema, "PartnerLoansHistory");

const PreApprovalsSchema = new mongoose.Schema({
  partnerId: mongoose.Schema.ObjectId,
  partnerTransactionId: mongoose.Schema.ObjectId,
  productId: { type: String },
  merchantCode: { type: String },
  merchantType: { type: String },
  trxnId: { type: String },
  loanType: { type: String },
  productType: { type: String },
  transactionMonth: { type: Number },
  transactionYear: { type: Number },
  tenureInDays: { type: Number },
  processingFee: { type: String },
  interestType: { type: String },
  annualIntrestRate: { type: String },
  approvedAmount: { type: Number },
  emiAmt: { type: Number },
  trueScore: { type: Number },
  isWhatsApp: { type: Boolean },
  whatsAppContactedOn: { type: Date },
  offerStartDate: { type: Date },
  offerSentOn: { type: Date },
  expiredOn: { type: Date },
  isActive: { type: Boolean, default: true },
  approvedBY: { type: String },
  insertedOn: { type: Date, default: Date.now },
  modifiedOn: { type: Date },
  totalInterest: { type: Number },
  processingFeeAmount: { type: Number },
  gstCharges: { type: Number },
  stampingCharges: { type: Number },
  disbursalCharges: { type: Number },
  disbursedAmount: { type: Number },
  totalAmountPayable: { type: Number },
  errorMsg: { type: String },
  status: { type: String },
  source: { type: String },
  recommandedLendingPartner: { type: String },
  isApplied: { type: Boolean, default: false },
  loanDescription: { type: String }
});
exports.PreApprovals = mongoose.model("PreApprovals", PreApprovalsSchema, "PreApprovals");

const PartnerTransactionsSchema = new mongoose.Schema({
  partnerId: Schema.ObjectId,
  merchantCode: { type: String },
  mobileNo: { type: String },
  emailId: { type: String },
  onboardedDate: { type: Date, format: "date-time" },
  pincode: { type: String },
  pageNo: { type: Number },
  totalPages: { type: Number },
  requestRef: { type: String },
  totalDays: [
    {
      year: { type: String },
      jan: { type: Number },
      feb: { type: Number },
      mar: { type: Number },
      apr: { type: Number },
      may: { type: Number },
      jun: { type: Number },
      jul: { type: Number },
      aug: { type: Number },
      sep: { type: Number },
      oct: { type: Number },
      nov: { type: Number },
      dec: { type: Number },
    },
  ],
  activeDays: [
    {
      year: { type: String },
      jan: { type: Number },
      feb: { type: Number },
      mar: { type: Number },
      apr: { type: Number },
      may: { type: Number },
      jun: { type: Number },
      jul: { type: Number },
      aug: { type: Number },
      sep: { type: Number },
      oct: { type: Number },
      nov: { type: Number },
      dec: { type: Number },
    },
  ],
  tranCount: [
    {
      year: { type: String },
      jan: { type: Number },
      feb: { type: Number },
      mar: { type: Number },
      apr: { type: Number },
      may: { type: Number },
      jun: { type: Number },
      jul: { type: Number },
      aug: { type: Number },
      sep: { type: Number },
      oct: { type: Number },
      nov: { type: Number },
      dec: { type: Number },
    },
  ],
  tranVol: [
    {
      year: { type: String },
      jan: { type: Number },
      feb: { type: Number },
      mar: { type: Number },
      apr: { type: Number },
      may: { type: Number },
      jun: { type: Number },
      jul: { type: Number },
      aug: { type: Number },
      sep: { type: Number },
      oct: { type: Number },
      nov: { type: Number },
      dec: { type: Number },
    },
  ],
  minWalletBalance: [
    {
      year: { type: String },
      jan: { type: Number },
      feb: { type: Number },
      mar: { type: Number },
      apr: { type: Number },
      may: { type: Number },
      jun: { type: Number },
      jul: { type: Number },
      aug: { type: Number },
      sep: { type: Number },
      oct: { type: Number },
      nov: { type: Number },
      dec: { type: Number },
    },
  ],
  maxWalletBalance: [
    {
      year: { type: String },
      jan: { type: Number },
      feb: { type: Number },
      mar: { type: Number },
      apr: { type: Number },
      may: { type: Number },
      jun: { type: Number },
      jul: { type: Number },
      aug: { type: Number },
      sep: { type: Number },
      oct: { type: Number },
      nov: { type: Number },
      dec: { type: Number },
    },
  ],
  serviceCount: [
    {
      year: { type: String },
      serviceType: { type: String },
      jan: { type: Number },
      feb: { type: Number },
      mar: { type: Number },
      apr: { type: Number },
      may: { type: Number },
      jun: { type: Number },
      jul: { type: Number },
      aug: { type: Number },
      sep: { type: Number },
      oct: { type: Number },
      nov: { type: Number },
      dec: { type: Number },
    },
  ],
  serviceVol: [
    {
      year: { type: String },
      serviceType: { type: String },
      jan: { type: Number },
      feb: { type: Number },
      mar: { type: Number },
      apr: { type: Number },
      may: { type: Number },
      jun: { type: Number },
      jul: { type: Number },
      aug: { type: Number },
      sep: { type: Number },
      oct: { type: Number },
      nov: { type: Number },
      dec: { type: Number },
    },
  ],
  trueScore: { type: Number },
  isNewTransaction: { type: Boolean, default: true },
  insertedOn: { type: Date, default: Date.now, format: "date-time" },
  modifiedOn: { type: Date, format: "date-time" },
});

exports.PartnerTransactions = mongoose.model("PartnerTransactions", PartnerTransactionsSchema, "PartnerTransactions");

const TrancheMasterSchema = new mongoose.Schema({
  partnerId: Schema.ObjectId,
  no: { type: Number },
  trancheNo: { type: String },
  amount: { type: Number },
  disbursedAmount: { type: Number },
  transactionDate: { type: Date, format: "date-time" },
  depositDate: { type: Date, format: "date-time" },
  fldgAmount: { type: Number },
  expiryDate: { type: Date, format: "date-time" },
  isOpen: { type: Boolean },
  isCurrent: { type: Boolean },
  insertedOn: { type: Date, default: Date.now, format: "date-time" },
  modifiedOn: { type: Date, format: "date-time" }
});

exports.TrancheMaster = mongoose.model("TrancheMaster", TrancheMasterSchema, "TrancheMaster");

const AccessDetailsSchema = new mongoose.Schema({
  api: { type: String },
  partnerId: Schema.ObjectId,
  authKey: { type: String },
  publicIP: { type: String },
  responseTime: { type: Number },
  isSuccess: { type: Boolean },
  req: { type: Object },
  response: { type: Object },
  errorMsg: { type: String },
  insertedOn: { type: Date, default: Date.now, format: "date-time" },
  modifiedOn: { type: Date, format: "date-time" },
  endPoint: { type: String },
  type: { type: String },
  requestTime: { type: Date, format: "date-time" },
  responseTime: { type: Date, format: "date-time" },
  timeTaken: { type: Number },
  httpStatusCode: { type: Number },
  httpVerb: { type: String },
  requestFreeMemory: { type: Number },
  responseFreeMemory: { type: Number },
  systemFreeMemoryDiff: { type: Number },
  processHeapMemoryUsedDiff: { type: Number },
  totalMemory: { type: Number },
  requestProcessMeta: { type: Object },
  responseProcessMeta: { type: Object }
});
exports.AccessDetails = mongoose.model("AccessDetails", AccessDetailsSchema, "AccessDetails");

const notificationsSchema = new mongoose.Schema({
  partnerId: mongoose.Schema.ObjectId,
  merchantCode: { type: String },
  trxnId: { type: String },
  productId: { type: String },
  message: [{
    userId: mongoose.Schema.ObjectId,
    loanId: mongoose.Schema.ObjectId,
    type: { type: String },
    mobileNo: { type: String },
    channel: { type: String },
    responseCode: { type: String },
    status: { type: String },
    sentOn: { type: Date },
    deliveredOn: { type: Date },
    readOn: { type: Date },
    reason: { type: String },
    statusCode: { type: String }
  }],
  insertedOn: { type: Date, default: Date.now },
  modifiedOn: { type: Date }
});
exports.Notifications = mongoose.model("Notifications", notificationsSchema, "Notifications");

const partnerAllocationRejectionSchema = new mongoose.Schema({
  partnerId: mongoose.Schema.ObjectId,
  userId: mongoose.Schema.ObjectId,
  loanId: mongoose.Schema.ObjectId,
  apiType: { type: String },
  errorMsg: { type: String },
  insertedOn: { type: Date, default: Date.now }
});
exports.PartnerAllocationRejection = mongoose.model("PartnerAllocationRejection", partnerAllocationRejectionSchema, "PartnerAllocationRejection");

const lendingPartnerAllocationSchema = new mongoose.Schema({
  userId: mongoose.Schema.ObjectId,
  loanId: mongoose.Schema.ObjectId,
  lenderId: mongoose.Schema.ObjectId,
  lenderPartnerId: mongoose.Schema.ObjectId,
  isBankDetailsSubmitted: { type: Boolean, default: false }, //Only for Lendbox
  lenderCustId: { type: String },
  lenderLoanId: { type: String },
  lenderLeadId: { type: String },
  insertedOn: { type: Date, default: Date.now },
  mobileNo: { type: String },
  panNo: { type: String },
  updatedOn: { type: Date },
  lenderProductType: { type: String },
  isActive: { type: Boolean, default: true },
  lenderApproval: {
    status: { type: String },
    updatedBy: mongoose.Schema.ObjectId,
    updatedByUser: { type: String },
    updatedOn: { type: Date },
    ipAddress: { type: String },
    comments: { type: String }
  }
});
exports.LendingPartnerAllocation = mongoose.model("LendingPartnerAllocation", lendingPartnerAllocationSchema, "LendingPartnerAllocation");

const NotificationsList = new mongoose.Schema({
  partnerId: mongoose.Schema.ObjectId,
  merchantCode: { type: String },
  userId: mongoose.Schema.ObjectId,
  productId: { type: String },
  trxnId: { type: String },
  product: { type: String },
  loanId: mongoose.Schema.ObjectId,
  channel: { type: String },
  mobileNo: { type: String },
  email: { type: String },
  templateType: { type: String },
  status: { type: String },
  responseCode: { type: String },
  sentOn: { type: Date },
  deliveredOn: { type: Date },
  readOn: { type: Date },
  reason: { type: String },
  statusCode: { type: String },
  insertedOn: { type: Date, default: Date.now },
  modifiedOn: { type: Date },
  fallbackMessageId: mongoose.Schema.ObjectId,
  mailerId: mongoose.Schema.ObjectId,
  messageTag: { type: String },
  preApprovalId: mongoose.Schema.ObjectId,
  eventTimeStamp: { type: Date },
  smsModel: {
    From: { type: String },
    To: { type: String },
    Msg: { type: String },
    TemplateName: { type: String },
    VAR1: { type: String },
    VAR2: { type: String },
    VAR3: { type: String },
    VAR4: { type: String },
    VAR5: { type: String },
    VAR6: { type: String },
    VAR7: { type: String },
    VAR8: { type: String },
    VAR9: { type: String },
    VAR10: { type: String },
    VAR11: { type: String },
    VAR12: { type: String },
    Type: { type: String },
    CTA: { type: String },
    Attachment1: { type: String },
    AttachmentName1: { type: String },
    AttachmentPath1: { type: String }
  },
  isFallbackMessage: { type: Boolean },
  smsType: { type: String },
  numberType: { type: String },
  url: { type: String },
  primaryMobileNo: { type: String },
  meta: {},
  ruleId: mongoose.Schema.ObjectId,
});

exports.NotificationsList = mongoose.model("NotificationsList", NotificationsList, "NotificationsList");

const dndSchema = new mongoose.Schema({
  mobileNo: { type: String },
  countryCode: { type: String },
  channel: { type: String },
  isActive: { type: Boolean },
  insertedOn: { type: Date, default: Date.now },
  updatedOn: { type: Date }
});

exports.DND = mongoose.model("DND", dndSchema, "DND");

const configParametersSchema = new mongoose.Schema({
  channel: { type: String },
  templateType: { type: String },
  startDate: { type: Date },
  endDate: { type: Date },
  isActive: { type: Boolean },
  insertedOn: { type: Date, default: Date.now },
  updatedOn: { type: Date },
  minutes: { type: Number },
  latePaymentAllowed: { type: Number },
  avgLatePaymentAllowed: { type: Number },
  config: { type: Object }
});

exports.ConfigParameters = mongoose.model("ConfigParameters", configParametersSchema, "ConfigParameters");

const partnerConfigSchema = new mongoose.Schema({
  partnerId: mongoose.Schema.ObjectId,
  bankValidationAmount: { type: Number },
  eNachAmount: { type: Number },
  disbursalType: { type: String },
  isApiPartner: { type: Boolean, default: false },
  collectionType: { type: String },
  LC_InvoiceTenure: { type: Number },
  LC_InvoiceFrequency: { type: String },
  LC_ParentTenure: { type: Number },
  LC_ParentFrequency: { type: String },
  creditGrid: {
    minMonthlySalesVol: { type: Number },
    minLoanAmt: { type: Number },
    maxLoanAmt: { type: Number },
    merchantVintage: { type: Number },
    minLoanTenure: { type: Number },
    maxLoanTenure: { type: Number },
    minBureauScore: { type: Number },
    ntcAllowed: { type: Boolean },
    bankStatementReq: { type: Boolean },
    minAge: { type: Number },
    maxAge: { type: Number },
    minTrueScore: { type: Number },
    pinCodes: { type: Array }
  },
  loanAllocationValidation: [{
    parameter: { type: String },
    value: { type: String }
  }],
  isEmailCancellation: { type: Boolean, default: false },
  campaign: [{
    campaignId: mongoose.Schema.ObjectId,
    // name: { type: String },
    isActive: { type: Boolean },
    // from: { type: Date },
    // to: { type: Date },
    // type: { type: String },
    // discountType: { type: String },
    // discountAmount: { type: Number },
    // discountPercentage: { type: Number },
    // compute: { type: String },
    // loanType: { type: String },
    discountText: { type: String },
    bankText: { type: String },
    insertedOn: { type: Date, default: Date.now, format: "date-time" },
    updatedOn: { type: Date, default: Date.now, format: "date-time" }
  }],
  partnerName: { type: String },
  allowedDocs: [{ type: String }],
  autofill_OTP: { type: Boolean, default: false },
  isMandateRequired: { type: Boolean, default: true },
  blockMobilePanCombo: { type: Boolean, default: false },
  hurdleRate: { type: Number },
  settlementMode: { type: String },
  primaryTrancheValue: { type: String },
  isRejectExistingMonthLoans: { type: Boolean, default: true },
  onboardingType: { type: String },
  allowMobileEdit: { type: Boolean, default: true },
  internalNachLink: { type: Boolean, default: true },
  isLoanApprovalRequired: { type: Boolean, default: false },
  source: { type: Number },
  questionnairre: {
    whatsApp: { type: Boolean, default: true },
    gender: { type: Boolean, default: true },
    maritalStatus: { type: Boolean, default: true },
    children: { type: Boolean, default: true },
    homeOwnershipType: { type: Boolean, default: true },
    vehicle: { type: Boolean, default: true },
    monthlyOutgoing: { type: Boolean, default: true },
    monthlyIncome: { type: Boolean, default: true },
    jobType: { type: Boolean, default: true },
    purchasedInOneYear: { type: Boolean, default: true },
    degree: { type: Boolean, default: true },
    purchasedInOneYear: { type: Boolean, default: true },
    recommendedBy: { type: Boolean, default: true }
  },
  preApproval: {
    loanDescription: { type: Boolean, default: false }
  },
  apiSecurityEnabled: { type: Boolean, default: true },
  updatePartnerBank: { type: Boolean, default: false },
  incompleteApplicationReportFrequency: { type: Number }
});

exports.PartnerConfig = mongoose.model("PartnerConfig", partnerConfigSchema, "PartnerConfig");

const productMasterSchema = new mongoose.Schema({
  "invoiceFinancingDate": { type: Date },
  "invoiceFinancingDueDate": { type: Date },
  "product": { type: String },
  "desc": { type: String },
  "penaltyCharges": { type: Number },
  "penaltyChargePercentage": { type: Number },
  "penaltyGraceDays": {
    "version2": { type: Number }
  },
  "name": { type: String },
  "type": { type: String },
  "isActive": { type: Boolean },
  "tenure": {
    "min": { type: Number },
    "max": { type: Number },
    "valueType": { type: String }
  },
  "kycRecaptureInDays": { type: Number },
  "startDate": { type: Date, default: Date.now, format: "date-time" },
  "endDate": { type: Date, default: Date.now, format: "date-time" },
  "lender": {
    "id": { type: String },
    "name": { type: String }
  },
  "loanAmountRange": {
    "min": { type: Number },
    "max": { type: Number }
  },
  "category": { type: String },
  "description": { type: String },
  "loan": Schema.Types.Mixed,
  "subVention": Schema.Types.Mixed,
  "latePayment": {
    "valueType": { type: String },
    "value": { type: Number },
    "graceDays": { type: Number },
    "installments": {
      "type": { type: String },
      "principal": { type: Boolean },
      "interest": { type: Boolean }
    },
    "lateAfter": { type: Number },
    "delinquentAfter": { type: Number },
    "defaultAfter": { type: Number },
    "writeOffAfter": { type: Number }
  },
  "interest": {
    "type": { type: String },
    "charges": {
      "valueType": { type: String },
      "value": { type: Number },
    }
  },
  "createdOn": { type: Date, default: Date.now, format: "date-time" },
  "updatedOn": { type: Date, default: Date.now, format: "date-time" },
  "createdBy": mongoose.Schema.ObjectId,
  "updatedBy": mongoose.Schema.ObjectId,
  "penaltyGraceDays": {
    "version2": { type: Number },
  }
});
exports.ProductMaster = mongoose.model("ProductMaster", productMasterSchema, "ProductMaster");

const productMasterHistorySchema = new mongoose.Schema({
  "productMasterObject": productMasterSchema,
  "modifiedOn": { type: Date, default: Date.now, format: "date-time" },
  "actionType": { type: String },
  "modifiedBy": mongoose.Schema.ObjectId,
  // "product": { type: String },
  // "desc": { type: String },
  // "penaltyCharges": { type: Number },
  // "name": { type: String },
  // "type": { type: String },
  // "isActive": { type: Boolean },
  // "tenure": {
  //   "min": { type: Number },
  //   "max": { type: Number }
  // },
  // "startDate": { type: Date, default: Date.now, format: "date-time" },
  // "endDate": { type: Date, default: Date.now, format: "date-time" },
  // "lender": {
  //   "id": { type: String },
  //   "name": { type: String }
  // },
  // "loanAmountRange": {
  //   "min": { type: Number },
  //   "max": { type: Number }
  // },
  // "category": { type: String },
  // "description": { type: String },
  // "loan": Schema.Types.Mixed,
  // "subVention": Schema.Types.Mixed,
  // "latePayment": {
  //   "valueType": { type: String },
  //   "value": { type: Number },
  //   "graceDays": { type: Number },
  //   "installments": {
  //     "type": { type: String },
  //     "principal": { type: Boolean },
  //     "interest": { type: Boolean }
  //   },
  //   "lateAfter": { type: Number },
  //   "delinquentAfter": { type: Number },
  //   "defaultAfter": { type: Number },
  //   "writeOffAfter": { type: Number }
  // },
  // "interest": {
  //   "type": { type: String },
  //   "charges": {
  //     "valueType": { type: String },
  //     "value": { type: Number },
  //   }
  // },
  // "createdOn": { type: Date, default: Date.now, format: "date-time" },
  // "updatedOn": { type: Date, default: Date.now, format: "date-time" },
  // "createdBy": mongoose.Schema.ObjectId,
  // "updatedBy": mongoose.Schema.ObjectId
});
exports.ProductMasterHistory = mongoose.model("ProductMasterHistory", productMasterHistorySchema, "ProductMasterHistory");

//ProductMaster old schema fields
// "product": { type: String },
//   "desc": { type: String },
//   "penaltyCharges": { type: Number },

//ProductMaster new schema fields
// "name": { type: String },
//   "type": { type: String },
//   "isActive": { type: Boolean },
//   "tenure": {
//     "valueType": { type: String },
//     "value": { type: Number }
//   },
//   "startDate": { type: Date, default: Date.now, format: "date-time" },
//   "endDate": { type: Date, default: Date.now, format: "date-time" },
//   "lender": {
//     "id": { type: String },
//     "name": { type: String }
//   },
//   "loanAmountRange": {
//     "min": { type: Number },
//     "max": { type: Number }
//   },
//   "category": { type: String },
//   "description": { type: String },
//   "loan": Schema.Types.Mixed,
//   "subVention": Schema.Types.Mixed,
//   "latePayment": {
//     "valueType": { type: String },
//     "value": { type: Number },
//     "graceDays": { type: Number },
//     "installments": {
//       "type": { type: String },
//       "principal": { type: Boolean },
//       "interest": { type: Boolean }
//     },
//     "lateAfter": { type: Number },
//     "delinquentAfter": { type: Number },
//     "defaultAfter": { type: Number },
//     "writeOffAfter": { type: Number }
//   },
//   "createdOn": { type: Date, default: Date.now, format: "date-time" },
//   "updatedOn": { type: Date, default: Date.now, format: "date-time" },
//   "createdBy": mongoose.Schema.ObjectId,
//   "updatedBy": mongoose.Schema.ObjectId

// const productMasterSchema2 = new mongoose.Schema({
//   "name": { type: String },
//   "type": { type: String },
//   "isActive": { type: Boolean },
//   "tenure": {
//     "valueType": { type: String },
//     "value": { type: Number }
//   },
//   "startDate": { type: Date, default: Date.now, format: "date-time" },
//   "endDate": { type: Date, default: Date.now, format: "date-time" },
//   "lender": {
//     "id": { type: String },
//     "name": { type: String }
//   },
//   "loanAmountRange": {
//     "min": { type: Number },
//     "max": { type: Number }
//   },
//   "category": { type: String },
//   "description": { type: String },
//   "loan": Schema.Types.Mixed,
//   "subVention": Schema.Types.Mixed,
//   "latePayment": {
//     "valueType": { type: String },
//     "value": { type: Number },
//     "graceDays": { type: Number },
//     "installments": {
//       "type": { type: String },
//       "principal": { type: Boolean },
//       "interest": { type: Boolean }
//     },
//     "lateAfter": { type: Number },
//     "delinquentAfter": { type: Number },
//     "defaultAfter": { type: Number },
//     "writeOffAfter": { type: Number }
//   },
//   "createdOn": { type: Date, default: Date.now, format: "date-time" },
//   "updatedOn": { type: Date, default: Date.now, format: "date-time" },
//   "createdBy": mongoose.Schema.ObjectId,
//   "updatedBy": mongoose.Schema.ObjectId
// });
// exports.ProductMaster2 = mongoose.model("ProductMaster2", productMasterSchema2, "ProductMaster2");

const ProductPartnerConfigSchema = new mongoose.Schema({
  partnerId: mongoose.Schema.ObjectId,
  productId: mongoose.Schema.ObjectId,
  childLoanApprovedLimitInPer: { type: Number },
  interestComputation: { type: String },
  penaltyGraceDays: [
    {
      frequency: { type: String },
      days: { type: Number }
    },
  ],
  minTenure: { type: Number },
  maxTenure: { type: Number }
});
exports.ProductPartnerConfig = mongoose.model("ProductPartnerConfig", ProductPartnerConfigSchema, "ProductPartnerConfig");


const lenderAccountSummary = new mongoose.Schema({
  lenderId: mongoose.Schema.ObjectId,
  lenderName: { type: String },
  lenderCode: { type: String },
  accountName: { type: String },
  accounType: { type: String },
  accountNumber: { type: String },
  ifsc: { type: String },
  lenderClientCode: { type: String },
  availableBalance: { type: Number },
  currentBalance: { type: Number },
  insertedOn: { type: Date, default: Date.now, format: "date-time" },
  updatedOn: { type: Date }
}, { strict: false })

exports.LenderAccountSummary = mongoose.model("LenderAccountSummary", lenderAccountSummary, "LenderAccountSummary");


const jobStatusSchema = new mongoose.Schema({
  name: { type: String },
  status: { type: String },
  response: mongoose.Schema.Types.Mixed,
  error: mongoose.Schema.Types.Mixed,
  insertedOn: { type: Date },
  updatedOn: { type: Date }
}, { strict: false });

exports.JobStatus = mongoose.model("JobStatus", jobStatusSchema, "JobStatus");

const fundfinaPolicySchema = new mongoose.Schema({
  policyName: { type: String },
  durationInMonths: { type: Number },
  isActive: { type: Boolean },
  loanAppropriationSequence: { type: Array }
});

exports.FundfinaPolicy = mongoose.model("FundfinaPolicy", fundfinaPolicySchema, "FundfinaPolicy");
const partnerTrancheSchema = new mongoose.Schema({
  partnerId: { type: mongoose.Schema.ObjectId, required: true },
  reverseResidualInDays: { type: Number },
  interestRate: { type: Number },
  identifier: { type: String },
  tenure: {
    value: { type: Number },
    valueType: { type: String, default: "DAYS" }
  },
  currencyType: { type: String, default: "INR" },
  amount: { type: Number, required: true },
  deploymentType: { type: String },
  type: { type: String },
  startDate: { type: Date, default: Date.now, format: "date-time" },
  endDate: { type: Date, default: Date.now, format: "date-time" },
  fldg: {
    amount: { type: Number },
    transactionDate: { type: Date, default: Date.now, format: "date-time", required: true },
    utrNumber: { type: String, required: true }
  },
  isActive: { type: Boolean, required: true },
  insertedOn: { type: Date, default: Date.now, format: "date-time" },
  updatedOn: { type: Date, default: Date.now, format: "date-time" },
  updatedBy: { type: mongoose.Schema.ObjectId },
}, { strict: false });

module.exports.Tranche = mongoose.model("Tranche", partnerTrancheSchema, "Tranche");

const trueScoreBetaSchema = new mongoose.Schema({
  pan_no: { type: String },
  userType: { type: String },
  userId: { type: String },
  partnerId: mongoose.Schema.ObjectId,
  enterprise: { type: String },
  merchantCode: { type: String },
  partnerMerchantCode: { type: String },
  merchantType: { type: String },
  transactionMonth: { type: Number },
  transactionYear: { type: Number },
  activation_dt: { type: Date, format: 'date-time' },
  email: { type: String },
  mobile: { type: String },
  TS_08: { type: Number },
  TS_09: { type: Number },
  TS_10: { type: Number },
  p_delinquent: { type: Number },
  p_default: { type: Number },
  p_writeoff: { type: Number },
  wasEverWrittenOff: { type: String },
  ErrorString: { type: String },
  oldTRUEscore: { type: Number },
  TRUEscore_v2: { type: Number },
  TRUEscore_v3: { type: Number },
  approvedAmount: { type: Number },
  tranCount: [{
    year: String,
    jan: Number,
    feb: Number,
    mar: Number,
    apr: Number,
    may: Number,
    jun: Number,
    jul: Number,
    aug: Number,
    sep: Number,
    oct: Number,
    nov: Number,
    dec: Number
  }],
  tranVol: [{
    year: String,
    jan: Number,
    feb: Number,
    mar: Number,
    apr: Number,
    may: Number,
    jun: Number,
    jul: Number,
    aug: Number,
    sep: Number,
    oct: Number,
    nov: Number,
    dec: Number
  }],
  insertedOn: { type: Date, default: Date.now, format: 'date-time' },
  updatedOn: { type: Date, default: Date.now, format: 'date-time' }
})
exports.TrueScoreBeta = mongoosedbCredit.model("TrueScoreBeta", trueScoreBetaSchema, "TrueScoreBeta");


// ------------------------------Validations--------------------------------------------------

module.exports.accessDetailsModel = function () {
  return {
    api: null,
    partnerId: null,
    authKey: null,
    publicIP: null,
    responseTime: null,
    isSuccess: null,
    req: null,
    errorMsg: null
  }
}

module.exports.validatePartner = function (model) {
  const user = joi.object({
    userName: joi.string().min(5).max(255).required(),
    mobileNo: joi.string().min(10).required(),
    emailId: joi.string().min(5).max(255).required().email(),
    password: joi.string().min(5).max(255).required(), //.passwordComplexity(complexityOptions).validate()
    partnerName: joi.string().required(),
    partnerType: joi.string().required(),
    typeOfEntity: joi.string().required(),
    ipAddress: joi.string().required(),
    doi: joi.string(),
    otp: joi.string().required()
  });

  return user.validate(model);
}

module.exports.validateRequestLoan = function (model) {
  const requestLoan = joi.object({
    ekoTrxnId: joi.number().required(),
    product_id: joi.string().optional().allow(null, ""),
    agent_code: joi.string().required(),
    loan_amount: joi.string().required(),
    city: joi.string().required(),
    street: joi.string().optional(),
    state: joi.string().required(),
    pincode: joi.string().required(),
    firstName: joi.string().required(),
    lastName: joi.string().required(),
    panNo: joi.string().required(),
    aadhaarNo: joi.string().optional().allow(null, ""),
    dob: joi.string().required(),
    mobileNo: joi.string().required(),
    emailID: joi.string().required(),
    agent_type: joi.string().required(),
    onboardedDate: joi.string().required(),
    isWeekendLoan: joi.number().optional(),
    ipAddress: joi.string().optional().allow(null, ""),
    virtualAccountNo: joi.string().optional().allow(null, ""),
    personalDetails: joi.object({
      qualification: joi.string().optional().allow(null, ""),
      dob: joi.string().optional().allow(null, ""),
      name: joi.string().optional().allow(null, ""),
      gender: joi.string().optional().allow(null, ""),
      marital_status: joi.string().optional().allow(null, ""),
      mobile: joi.string().optional().allow(null, ""),
      alternate_mobile: joi.string().optional().allow(null, ""),
      home_ownership_type: joi.string().optional().allow(null, ""),
      current_address: joi.string().required(),
      city: joi.string().required(),
      state: joi.string().required(),
      pincode: joi.string().required(),
      maritalStatus: joi.string().optional().allow(null, ""),
      phoneConnectionType: joi.string().optional().allow(null, ""),
      whatsAppInMobile: joi.string().optional().allow(null, ""),
      children: joi.string().optional().allow(null, ""),
      ownHome: joi.string().optional().allow(null, ""),
      vehicle: joi.string().optional().allow(null, ""),
      avgMonthlyOutgoings: joi.string().optional().allow(null, ""),
      avgMonthlyIncome: joi.string().optional().allow(null, ""),
      jobType: joi.string().optional().allow(null, ""),
      whiteGoodsIn24Months: joi.string().optional().allow(null, ""),
      educationalDegree: joi.string().optional().allow(null, "")
    }).required(),
    shopDetails: joi.object({
      pincode: joi.string().optional().allow(null, ""),
      state: joi.string().optional().allow(null, ""),
      shop_address: joi.string().optional().allow(null, ""),
      shop_type: joi.string().optional().allow(null, ""),
      city: joi.string().optional().allow(null, ""),
      shop_name: joi.string().optional().allow(null, ""),
      shop_location: joi.string().optional().allow(null, ""),
      shop_vintage: joi.string().optional().allow(null, ""),
      shop_contact_number: joi.string().optional().allow(null, ""),
      business_vintage: joi.string().optional().allow(null, ""),
      shop_ownership_type: joi.string().optional().allow(null, ""),
      company_type: joi.string().optional().allow(null, ""),
      shopLocation: joi.string().optional().allow(null, ""),
      shopAddress: joi.string().optional().allow(null, ""),
      shopType: joi.string().optional().allow(null, ""),
      shopName: joi.string().optional().allow(null, ""),
      shopSector: joi.string().optional().allow(null, ""),
      natureOfBussiness: joi.string().optional().allow(null, ""),
    }).optional().allow(null),
    accountDetails: joi.object({
      mobile: joi.string().optional().allow(null, ""),
      panNumber: joi.string().optional().allow(null, ""),
      merchantCode: joi.string().optional().allow(null, ""),
    }).optional().allow(null),
    bankDetails: joi.object({
      accountName: joi.string().required(),
      accountType: joi.string().required(),
      accountNo: joi.string().required(),
      ifsc: joi.string().required()
    }).optional().allow(null),
    documents: joi.array().items(
      joi.object({
        docNo: joi.string().optional().allow(null, ""),
        docType: joi.string().optional().allow(null, ""),
        docPath: joi.string().optional().allow(null, ""),
        docExtension: joi.string().optional().allow(null, ""),
      })
    ).optional().allow(null),
    transactions: joi.array().items(
      joi.object({
        transactionsType: joi.string().optional().allow(null, ""),
        transactionsValue: joi.string().optional().allow(null, ""),
        transactionsMonthYear: joi.string().optional().allow(null, ""),
        transactionsValueType: joi.string().optional().allow(null, ""),
        primary: joi.string().optional().allow(null, ""),
        primaryCount: joi.string().optional().allow(null, ""),
      })
    ).optional().allow(null),
    ekoLoanHistory: joi.array().items(
      joi.object({
        loanProviderName: joi.string().optional().allow(null, ""),
        loanAmount: joi.string().optional().allow(null, ""),
        status: joi.string().optional().allow(null, ""),
        totalRepaidAmount: joi.number().optional().allow(null, ""),
      })
    ).optional().allow(null)
  });

  return requestLoan.validate(model);
};

module.exports.validateLoanRequest = function (model) {
  const requestLoan = joi.object({
    trxnId: joi.number().required(),
    productId: joi.string().optional().allow(null, ""),
    agent_code: joi.string().required(),
    loan_amount: joi.string().required(),
    tenure: joi.number().optional().allow(null, ""),
    city: joi.string().required(),
    street: joi.string().optional(),
    state: joi.string().required(),
    pincode: joi.string().required(),
    firstName: joi.string().required(),
    lastName: joi.string().required(),
    panNo: joi.string().required(),
    aadhaarNo: joi.string().optional().allow(null, ""),
    dob: joi.string().required(),
    mobileNo: joi.string().required(),
    emailID: joi.string().required(),
    agent_type: joi.string().required(),
    onboardedDate: joi.string().required(),
    isWeekendLoan: joi.number().optional(),
    ipAddress: joi.string().optional().allow(null, ""),
    virtualAccountNo: joi.string().optional().allow(null, ""),
    ekoLoanHistory: joi.array().items(
      joi.object({
        loanProviderName: joi.string().optional().allow(null, ""),
        loanAmount: joi.string().optional().allow(null, ""),
        status: joi.string().optional().allow(null, ""),
        totalRepaidAmount: joi.number().optional().allow(null, ""),
      })
    ).optional().allow(null),
    loanHistory: joi.array().items(
      joi.object({
        loanProviderName: joi.string().optional().allow(null, ""),
        loanAmount: joi.string().optional().allow(null, ""),
        status: joi.string().optional().allow(null, ""),
        totalRepaidAmount: joi.number().optional().allow(null, ""),
      })
    ).optional().allow(null),
    personalDetails: joi.object({
      qualification: joi.string().optional().allow(null, ""),
      dob: joi.string().optional().allow(null, ""),
      name: joi.string().optional().allow(null, ""),
      gender: joi.string().optional().allow(null, ""),
      marital_status: joi.string().optional().allow(null, ""),
      mobile: joi.string().optional().allow(null, ""),
      alternate_mobile: joi.string().optional().allow(null, ""),
      home_ownership_type: joi.string().optional().allow(null, ""),
      current_address: joi.string().required(),
      city: joi.string().required(),
      state: joi.string().required(),
      pincode: joi.string().required(),
      phoneConnectionType: joi.string().optional().allow(null, ""),
      whatsAppInMobile: joi.string().optional().allow(null, ""),
      children: joi.string().optional().allow(null, ""),
      ownHome: joi.string().optional().allow(null, ""),
      vehicle: joi.string().optional().allow(null, ""),
      avgMonthlyOutgoings: joi.string().optional().allow(null, ""),
      avgMonthlyIncome: joi.string().optional().allow(null, ""),
      jobType: joi.string().optional().allow(null, ""),
      whiteGoodsIn24Months: joi.string().optional().allow(null, ""),
      educationalDegree: joi.string().optional().allow(null, "")
    }).required(),
    shopDetails: joi.object({
      pincode: joi.string().optional().allow(null, ""),
      state: joi.string().optional().allow(null, ""),
      shop_address: joi.string().optional().allow(null, ""),
      shop_type: joi.string().optional().allow(null, ""),
      city: joi.string().optional().allow(null, ""),
      shop_name: joi.string().optional().allow(null, ""),
      shop_location: joi.string().optional().allow(null, ""),
      shop_vintage: joi.string().optional().allow(null, ""),
      shop_contact_number: joi.string().optional().allow(null, ""),
      business_vintage: joi.string().optional().allow(null, ""),
      shop_ownership_type: joi.string().optional().allow(null, ""),
      company_type: joi.string().optional().allow(null, ""),
    }).optional().allow(null),
    accountDetails: joi.object({
      mobile: joi.string().optional().allow(null, ""),
      panNumber: joi.string().optional().allow(null, ""),
      merchantCode: joi.string().optional().allow(null, ""),
    }).optional().allow(null),
    bankDetails: joi.object({
      accountName: joi.string().required(),
      accountType: joi.string().required().allow("Savings", "Current"),
      accountNo: joi.string().required(),
      ifsc: joi.string().required()
    }).optional().allow(null),
    transactions: joi.array().items(
      joi.object({
        primary: joi.string().optional().allow(null, ""),
        primaryCount: joi.string().optional().allow(null, ""),
        transactionsType: joi.string().optional().allow(null, ""),
        transactionsValue: joi.string().optional().allow(null, ""),
        transactionsMonthYear: joi.string().optional().allow(null, ""),
        transactionsValueType: joi.string().optional().allow(null, ""),
      })
    ).optional().allow(null),
    documents: joi.array().items(
      joi.object({
        docNo: joi.string().optional().allow(null, ""),
        docType: joi.string().optional().allow(null, ""),
        docPath: joi.string().optional().allow(null, ""),
        docExtension: joi.string().optional().allow(null, ""),
      })
    ).optional().allow(null),
    contractSuccessCallBackUrl: joi.string().optional().allow(null, ""),
    eNachErrorCallBackUrl: joi.string().optional().allow(null, ""),
    isBankValidated: joi.boolean().default(false).allow(null),
    beneficiary_name: joi.string().optional().allow(null, ""),
    digilocker: joi.object({
      execution_request_id: joi.string().required(),
      created_at: joi.string().required(),
      updated_at: joi.string().required(),
      reference_id: joi.string().required(),
      status: joi.string().required(),
      processing_done: joi.boolean().required(),
      face_match_status: joi.string().required(),
      validation_result: joi.object({
      }).optional().allow(null),
      face_match_obj_type: joi.string().required(),
      obj_analysis_status: joi.string().required(),
      document_details: {
        aadhaar: {
          docNo: joi.string().required(),
          document_type: joi.string().required(),
          id_proof_type: joi.string().required(),
          gender: joi.string().required(),
          image: joi.string().required(),
          name: joi.string().required(),
          dob: joi.string().required(),
          current_address: joi.string().required(),
          permanent_address: joi.string().required(),
          current_address_details: {
            address: joi.string().required(),
            locality_or_post_office: joi.string().required(),
            district_or_city: joi.string().required(),
            state: joi.string().required(),
            pincode: joi.string().required(),
          },
          permanent_address_details: {
            address: joi.string().required(),
            locality_or_post_office: joi.string().required(),
            district_or_city: joi.string().required(),
            state: joi.string().required(),
            pincode: joi.string().required(),
          },
          docPath: joi.string().required(),
        },
        pan: {
          name: joi.string().required(),
          docNo: joi.string().required(),
          dob: joi.string().required(),
          document_type: joi.string().required(),
          id_proof_type: joi.string().required(),
          typeOfHolder: joi.string().optional().allow(null, ""),
          isIndividual: joi.boolean().optional().allow(null, ""),
          isValid: joi.boolean().optional().allow(null, ""),
          firstName: joi.string().optional().allow(null, ""),
          middleName: joi.string().optional().allow(null, ""),
          lastName: joi.string().optional().allow(null, ""),
          title: joi.string().optional().allow(null, ""),
          panStatus: joi.string().optional().allow(null, ""),
          panStatusCode: joi.string().optional().allow(null, ""),
          aadhaarSeedingStatus: joi.string().optional().allow(null, ""),
          aadhaarSeedingStatusCode: joi.string().optional().allow(null, ""),
          lastUpdatedOn: joi.string().required(),
          docPath: joi.string().required(),
        }
      }
    }).optional().allow(null)
  });

  return requestLoan.validate(model);
};

module.exports.validateBorrowerLoanAcceptance = function (model) {
  const borrowerLoanAcceptance = joi.object({
    product_id: joi.string().required(),
    step_num: joi.number().required(),
    data: joi.object({
      consent: joi.object({
        customerAccept: joi.boolean().required(),
        reason: joi.string().optional().allow(null, ""),
        description: joi.string().optional().allow(null, ""),
      })
    })
  });

  return borrowerLoanAcceptance.validate(model);
};

module.exports.validateGetUserDetails = function (model) {
  const userDetailsRequest = joi.object({
    userId: joi.string().required(),
    loanAmount: joi.string().required(),
    trxnId: joi.string().optional().allow(null, ""),
    browserName: joi.string().optional().allow(null, ""),
    browserVersion: joi.string().optional().allow(null, ""),
    fullBrowserVersion: joi.string().optional().allow(null, ""),
    mobileVendor: joi.string().optional().allow(null, ""),
    mobileModel: joi.string().optional().allow(null, ""),
    osName: joi.string().optional().allow(null, ""),
    osVersion: joi.string().optional().allow(null, ""),
    isAutoLogin: joi.boolean().optional().allow(null, ""),
  });

  return userDetailsRequest.validate(model);
};

module.exports.validateGetLoanStatus = function (model) {
  const loanDetailsRequest = joi.object({
    productId: joi.string().optional().allow(null),
    loanNo: joi.string().optional().allow(null),
    isEmiScheduleRequire: joi.boolean().optional(),
    isTransactionRequire: joi.boolean().optional()
  });

  return loanDetailsRequest.validate(model);
};

module.exports.validateSaveTranche = function (model) {
  const tranche = joi.object({
    trancheId: joi.string().required(),
    amount: joi.number().required().min(0),
    transactionDate: joi.string().required(),
    depositDate: joi.string().required(),
    transactionDate: joi.string().required(),
    fldgPer: joi.number().required(),
    isOpen: joi.boolean().required()
  });

  return tranche.validate(model);
};

module.exports.validateRegisterPartner = function (model) {
  const user = joi.object({
    partnerName: joi.string().required(),
    partnerType: joi.string().required(),
    typeOfEntity: joi.string().required(),
    doi: joi.string(),
    add1: joi.string().required(),
    add2: joi.string().optional().allow(null, ""),
    add3: joi.string().required(),
    city: joi.string().required(),
    state: joi.string().required(),
    country: joi.string().required(),
    pincode: joi.string().required(),
    userName: joi.string().min(5).max(255).required(),
    password: joi.string().min(5).max(255).required(),
    mobileNo: joi.string().min(10).required(),
    emailId: joi.string().min(5).max(255).required().email(),
    panNo: joi.string().required(),
    panFile: joi.string().required(),
    panFileStream: joi.string().required(),
    udyogAadhaarNo: joi.string().optional().allow(null, ""),
    udyogAadhaarFile: joi.string().optional().allow(null, ""),
    udyogAadhaarFileStream: joi.string().optional().allow(null, ""),
    accountNo: joi.string().required(),
    ifsc: joi.string().required(),
    accountType: joi.string().required(),//Current/Escrow/Nodal
    bankFile: joi.string().required(),
    bankFileStream: joi.string().required(),
    gstNo: joi.string().optional().allow(null, ""),
    gstFile: joi.string().optional().allow(null, ""),
    gstFileStream: joi.string().optional().allow(null, "")
  });

  return user.validate(model);
}

module.exports.validateSaveChildLoan = function (model) {
  const request = joi.object({
    userId: joi.string().required(),
    partnerId: joi.string().required(),
    merchantCode: joi.string().required(),
    invoiceId: joi.string(),
    invoiceAmount: joi.number().required(),
    isExcelUpload: joi.boolean().allow(null),
    offerId: joi.string().optional().allow(null),
    deliveryAgent: joi.string().optional().allow(null),
    deliveryAgentMobile: joi.number().optional().allow(null),
  });
  return request.validate(model);
}

module.exports.validateCreateOrder = function (model) {
  const request = joi.object({
    userId: joi.string().required(),
    orderId: joi.string().required(),
    transactionAmount: joi.number().required(),
    dot: joi.string().required(),
    isOnlineCheckOut: joi.boolean().required(),
    items: joi.array().items(
      joi.object({
        item: joi.string().required(),
        quantity: joi.number().required(),
        amount: joi.number().required()
      })
    ).optional().allow(null)
  });
  return request.validate(model);
}

module.exports.validateConfirmLoanRequest = function (model) {
  const request = joi.object({
    loanNo: joi.string().required(),
    partnerCustRefId: joi.string().required(),
    partnerLoanRefId: joi.string().optional().allow(null, ""),
    partnerLeadId: joi.string().required()
  });
  return request.validate(model);
}

module.exports.validateGenerateOTPReq = function (model) {
  const request = joi.object({
    merchantCode: joi.string().required(),
    mobileNo: joi.string().required(),
    offerId: joi.number().required()
  });
  return request.validate(model);
}

module.exports.validateValidateOTPReq = function (model) {
  const request = joi.object({
    offerId: joi.number().required(),
    mobileNo: joi.string().required(),
    otp: joi.string().required()
  });
  return request.validate(model);
}

module.exports.validateOfferDetailsReq = function (model) {
  const request = joi.object({
    productId: joi.string().required(),
    loanAmount: joi.number().required(),
    tenure: joi.number().optional().allow(null),
    loanStartDate: joi.string().optional().allow(null),
  });
  return request.validate(model);
}

module.exports.validateUpdateEmiCollectionsReq = function (model) {
  const request = joi.object({
    emiPayments: joi.array().items(
      joi.object({
        productId: joi.string().required(),
        referenceNo: joi.string().required(),
        amount: joi.number().required(),
        isPaid: joi.boolean().required(),
        isPrincipalPaid: joi.boolean().required(),
        paidOn: joi.date().required()
      })
    ).optional().allow(null),
    repaymentFailures: joi.array().items(
      joi.object({
        productId: joi.string().required(),
        referenceNo: joi.string().required(),
        errorMessage: joi.string().required()
      })
    ).optional().allow(null)
  });
  return request.validate(model);
}