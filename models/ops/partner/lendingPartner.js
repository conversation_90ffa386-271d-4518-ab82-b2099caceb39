//Libraries
// ------------------------------------------------------------------------------------------------------------------------------------------
const mongoose = require("mongoose");
const debug = require("debug")("app:lendingPartner");
const IsNullEmpty = require("isnullemptyorwhitespace");
const { date } = require("@hapi/joi");
const { dateAdd } = require("dateadd");
const config = require("config");
const fetch = require("node-fetch");
const apiPath = __dirname;
//---------------------------------------------------------------------------------------------------------------------------

//Models
//---------------------------------------------------------------------------------------------------------------------------
const { LoanInfo } = require("../../../models/ops/loan/loan");
const { UserInfo, UserPartnerInfo } = require("../../../models/ops/registration/users");
const { PreApprovals } = require("../../../models/ops/partner/partner");
const { PartnerMaster } = require("./partner");
const { UserDeviceInfo } = require("../../../models/ops/common/master");
// ------------------------------------------------------------------------------------------------------------------------------------------

//External Methods
//---------------------------------------------------------------------------------------------------------------------------
const { getFromDate, getToDate, getYMDFormattedDateWithTime } = require("../../../services/ops/common/common");
const { saveErrorLog } = require("../../../services/ops/common/masters");
const { getPayOutModuleDetails, getFundfinaLoans } = require("../../../services/ops/admin/admin");
const loanService = require("../../../services/ops/loan/loan");
const { getLendingUserPartner } = require("../../../services/ops/common/DbDocuments");
const installmentsService = require("../../../services/ops/loan/installments");
const Logger = require("../../../middleware/logger");
const logger = new Logger();
//---------------------------------------------------------------------------------------------------------------------------

//Internal WorkFlow Methods
//---------------------------------------------------------------------------------------------------------------------------

module.exports.getDataForLoanApplication = async function (model) {
    let output = {
        data: null,
        success: false,
        message: "",
        status: 501,
    };
    try {
        let loanInfo = await LoanInfo.findById(model.loanId);
        if (loanInfo) {
            let existingLoansCount = 1;
            const userInfo = await UserInfo.findById(loanInfo.userId);
            const userPartnerInfo = await UserPartnerInfo.findOne({ partnerId: mongoose.Types.ObjectId(loanInfo.partnerId), userId: mongoose.Types.ObjectId(userInfo._id) }).select({ onboardedDate: 1 });

            logger.logs("userPartnerInfo", userPartnerInfo);

            const docCount = await LoanInfo.find({
                userId: mongoose.Types.ObjectId(loanInfo.userId),
                "lenderDetails.lenderPartnerId": mongoose.Types.ObjectId(model.lenderId)
            }).countDocuments();

            existingLoansCount += docCount;

            if (userInfo) {
                let record = {
                    userId: userInfo._id,
                    loanReqDate: loanInfo.insertedOn,
                    firstName: userInfo.userDetails.firstName,
                    middleName: userInfo.userDetails.middleName,
                    lastName: userInfo.userDetails.lastName,
                    addressDetails: userInfo.addressDetails,
                    panNo: userInfo.panNo,
                    profileDoc: userInfo.profileDocDetails,
                    dob: userInfo.userDetails.dob,
                    mobileNo: userInfo.userDetails.mobileNo, //mobile number change - no change
                    emailId: userInfo.userDetails.emailId,
                    panDocDetails: userInfo.panDocDetails,
                    shopDetails: userInfo.shopDetails,
                    poaDetails: userInfo.poaDetails,
                    loanId: loanInfo._id,
                    loanNo: loanInfo.loanNo,
                    productId: loanInfo.productId,
                    loanAmount: loanInfo.amount,
                    interestRate: loanInfo.interestRate,
                    tenure: loanInfo.maturity,
                    frequency: loanInfo.repaymentFrequency,
                    adminRemarks: loanInfo.adminRemarks,
                    purpose: loanInfo.purpose,
                    description: loanInfo.description,
                    loanCycle: existingLoansCount,
                    panDocVerification: userInfo.panDocVerification,
                    questionnairreDetails: userInfo.questionnairreDetails,
                    disbursedOn: Date.now(),
                    firstEmiDate: dateAdd("day", 1, Date.now()),
                    loanType: loanInfo.loanType,
                    lenderName: "",
                    ipAddress: loanInfo.ipAddress,
                    insertedOn: loanInfo.insertedOn,
                    bankName: loanInfo.disbursalBankDetails.bankName,
                    branch: loanInfo.disbursalBankDetails.branch,
                    accountNo: loanInfo.disbursalBankDetails.accountNo,
                    ifsc: loanInfo.disbursalBankDetails.ifsc,
                    accountType: loanInfo.disbursalBankDetails.accountType,
                    userPhotoPath: loanInfo.loanContractDetails.photoFilePath ? loanInfo.loanContractDetails.photoFilePath : null,
                    userVideoPath: loanInfo.photoMatch && loanInfo.photoMatch.videoPath ? loanInfo.photoMatch.videoPath : null,
                    kycStatus: userInfo.userDetails.kycStatus,
                    loanHistory: [],
                    trueScore: null,
                    trueScoreMonth: null,
                    trueScoreYear: null,
                    monthlyTransactions: [],
                    digilocker: userInfo.digilocker,
                    userDeviceInfo: null,
                    isPhotoMatch: loanInfo.photoMatch ? loanInfo.photoMatch.isMatched : false,
                    photoMatchPercentage: loanInfo.photoMatch && loanInfo.photoMatch.isMatched ? loanInfo.photoMatch.percentage : 0,
                };

                const userDeviceInfo = await UserDeviceInfo.findOne({ partnerId: loanInfo.partnerId, merchantCode: loanInfo.merchantCode }).sort({ insertedOn: -1 });
                if (userDeviceInfo != null) record.userDeviceInfo = userDeviceInfo;

                const preApproval = await PreApprovals.findOne({
                    partnerId: mongoose.Types.ObjectId(loanInfo.partnerId),
                    merchantCode: loanInfo.merchantCode,
                    productType: loanInfo.productType.split("_")[0],
                    $or: [{ trxnId: loanInfo.trxnId }, { productId: loanInfo.trxnId }, { productId: loanInfo.productId }]
                }).sort({ insertedOn: -1 });


                // let preApproval = await PreApprovals.findOne({ productId: { $ne: null, $eq: loanInfo.productId } });
                // if (IsNullEmpty(preApproval)) {
                //     preApproval = await PreApprovals.findOne({ trxnId: { $ne: null, $eq: loanInfo.trxnId } });
                // }

                if (!IsNullEmpty(preApproval)) {
                    const charges = await loanService.getLoanCharges(loanInfo.amount, loanInfo._id, preApproval._id, false);

                    if (charges.success) {
                        record.totalCharges = charges.totalCharges;
                        record.trueScore = preApproval.trueScore;
                        record.trueScoreMonth = preApproval.transactionMonth;
                        record.trueScoreYear = preApproval.transactionYear;

                        const transactionModel = {
                            partnerId: loanInfo.partnerId,
                            merchantCode: loanInfo.merchantCode,
                            currentYear: preApproval.transactionYear,
                            currentMonth: preApproval.transactionMonth
                        };

                        url = config.get("keys.creditApiUrl") + "/api/admin/getPartnerUserTransactions";

                        const requestBody = JSON.stringify(transactionModel);
                        //logger.logs("notification body", requestBody);
                        const transactionResponse = await fetch(url, {
                            method: "POST",
                            body: requestBody,
                            headers: {
                                "Content-Type": "application/json"
                            },
                        }).then(function (u) {
                            return u.json();
                        }).then(function (json) {
                            return json;
                        }).catch((err) => { logger.logs("err :", err); return err; });

                        if (transactionResponse && transactionResponse.success == true && transactionResponse.data && transactionResponse.data.length > 0) {
                            for (let i = 0; i < transactionResponse.data.length; i++) {
                                record.monthlyTransactions.push({
                                    month: transactionResponse.data[i].month,
                                    year: transactionResponse.data[i].year,
                                    count: transactionResponse.data[i].tranCount,
                                    value: transactionResponse.data[i].tranVolume,
                                    activeDays: transactionResponse.data[i].activeDays
                                });
                                if (i == 11) {
                                    i = transactionResponse.data.length;
                                }
                            }
                        }


                        const fundfinaLoans = await getFundfinaLoans(userInfo._id);
                        if (fundfinaLoans && fundfinaLoans.length > 0) {
                            for (const historyRecord of fundfinaLoans) {
                                record.loanHistory.push({
                                    loanNo: historyRecord.loanNo,
                                    amount: historyRecord.amount,
                                    status: historyRecord.status,
                                    outstandingAmount: historyRecord.outstandingAmount
                                });
                            }
                        }

                        const emis = await loanService.emiSchedule(loanInfo._id, new Date(Date.now()), false);

                        if (userPartnerInfo) {
                            record.onboardedDate = userPartnerInfo.onboardedDate;
                        }

                        if (!IsNullEmpty(emis)) {
                            record.firstEmiAmount = emis.data[0].rePaymentAmount;
                        }

                        const approvedByAdmin = await loanInfo.flowDetails.filter(x => x.Step == "Approved By Admin");
                        if (approvedByAdmin != null && approvedByAdmin.length > 0) {
                            record.approvedOn = approvedByAdmin[0].insertedOn;
                        } else {
                            const loanContractAccepted = await await loanInfo.flowDetails.filter(x => x.Step == "Loan Contract Accepted");
                            if (loanContractAccepted != null && loanContractAccepted.length > 0) {
                                record.approvedOn = loanContractAccepted[0].insertedOn;
                            }
                        }

                        const lendingPartner = await PartnerMaster.findById(model.lenderId);

                        if (!IsNullEmpty(lendingPartner)) {
                            record.lenderName = lendingPartner.displayName;
                        }

                        output = {
                            data: record,
                            success: true,
                            message: "fetched successfully",
                            status: 200,
                        };
                    } else output.message = charges.message;
                } else output.message = "Offer not Found";
            } else output.message = "UserInfo not Found";
        } else output.message = "Loan not Found";
    }
    catch (ex) {
        logger.logs(ex);
        saveErrorLog("getDataForLoanApplication", apiPath, model, { error: ex.message, stackTrace: ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex });
        output.message = ex.message;
    }
    return output;
};

// module.exports.getDataForLoanPreSanction = async function (loanId, lenderId) {
//     let output = {
//         data: null,
//         success: false,
//         message: "",
//         status: 501,
//     };
//     try {
//         //logger.logs("loanId", loanId);
//         const loanInfo = await LoanInfo.findById(loanId);
//         if (loanInfo) {
//             let record = {
//                 loanNo: loanInfo.loanNo,
//                 loanId: loanInfo._id,
//                 productId: loanInfo.productId,
//                 amount: loanInfo.amount,
//                 interestRate: loanInfo.interestRate,
//                 adminRemarks: loanInfo.adminRemarks,
//                 disbursedOn: Date.now(),
//                 firstEmiDate: dateAdd("day", 1, Date.now()),
//                 maturity: loanInfo.maturity,
//                 repaymentFrequency: loanInfo.repaymentFrequency,
//                 purpose: loanInfo.purpose,
//                 loanType: loanInfo.loanType,
//                 lenderName: ""
//             }

//             const emis = await loanService.emiSchedule(loanInfo._id, new Date(Date.now()), false);

//             if (!IsNullEmpty(emis)) {
//                 record.firstEmiAmount = emis.data[0].rePaymentAmount;
//             }

//             const approvedByAdmin = await loanInfo.flowDetails.filter(x => x.Step == "Approved By Admin");
//             record.approvedOn = approvedByAdmin[0].insertedOn;

//             const userInfo = await UserInfo.findById(loanInfo.userId);
//             record.bankName = userInfo.bankDetails[0].bankName;
//             record.branch = userInfo.bankDetails[0].branch;
//             record.accountNo = userInfo.bankDetails[0].accountNo;
//             record.ifsc = userInfo.bankDetails[0].ifsc;

//             const lendingPartner = await PartnerMaster.findById(lenderId);
//             if (!IsNullEmpty(lendingPartner)) {
//                 record.lenderName = lendingPartner.displayName;
//             }
//             let existingLoansCount = 1;
//             const docCount = await LoanInfo.find({ userId: mongoose.Types.ObjectId(userInfo._id), "lenderDetails[0].lenderId": mongoose.Types.ObjectId(lenderId) }).countDocuments();
//             existingLoansCount += docCount;

//             record.existingLoansFromSameLenderCount = existingLoansCount;

//             output = {
//                 data: record,
//                 success: true,
//                 message: "fetched successfully",
//                 status: 200,
//             };
//         } else {
//             output.message = "Loan not Found";
//         }
//     } catch (ex) {
//         logger.logs(ex);
//         const model = { loanId: loanId };
//         saveErrorLog("getDataForLoanSanction", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
//         output.message = ex.message;
//     }
//     return output;
// };


module.exports.getDataForLoanSanction = async function (loanId, module = "disbursement") {
    let output = {
        data: null,
        success: false,
        message: "",
        status: 501,
    };
    try {
        logger.logs("loanId", loanId);
        const loanInfo = await LoanInfo.findById(loanId);
        if (loanInfo) {
            let record = {
                lenderCustId: loanInfo.lenderDetails[0].lenderCustId,
                lenderLoanId: loanInfo.lenderDetails[0].lenderLoanId,
                loanNo: loanInfo.loanNo,
                loanId: loanInfo._id,
                productId: loanInfo.productId,
                amount: loanInfo.amount,
                interestRate: loanInfo.interestRate,
                totalInterestAmount: 0,
                adminRemarks: loanInfo.adminRemarks,
                disbursedOn: IsNullEmpty(loanInfo.disbursalDetails.disbursedOn) ? new Date() : loanInfo.disbursalDetails.disbursedOn,
                firstEmiDate: null,
                lastEmiDate: null,
                firstEmiAmount: null,
                lastEmiAmount: null,
                contractFile: loanInfo.loanContractDetails.contractFile,
                maturity: loanInfo.maturity,
                repaymentFrequency: loanInfo.repaymentFrequency,
                installments: null,
                purpose: loanInfo.purpose,
                loanType: loanInfo.loanType,
                bankName: loanInfo.disbursalBankDetails.bankName,
                branch: loanInfo.disbursalBankDetails.branch,
                accountNo: loanInfo.disbursalBankDetails.accountNo,
                ifsc: loanInfo.disbursalBankDetails.ifsc,
                approvedOn: null,
                existingLoansFromSameLenderCount: null,
                photoPath: loanInfo.loanContractDetails.photoFilePath ? loanInfo.loanContractDetails.photoFilePath : null,
                disbursalBankDetails: loanInfo.disbursalBankDetails,
                userBankDetails: null,
                kfsFile: loanInfo.loanContractDetails.kfsFile ? loanInfo.loanContractDetails.kfsFile : null
            };

            if (module != "makeLive") {
                const installmentDetails = await installmentsService.getInstallmentDetails(loanId, true);

                if (installmentDetails.success) {
                    const remarks = loanInfo.adminRemarks ? loanInfo.adminRemarks : "emi " + installmentDetails.firstInstallmentAmount.toString();
                    record.adminRemarks = remarks;
                    const emis = await installmentsService.convertEmiDetailsFormat(loanId, installmentDetails.installments);
                    record.totalInterestAmount = installmentDetails.totalInterestAmount;
                    record.firstEmiDate = installmentDetails.firstInstallmentDate;
                    record.lastEmiDate = installmentDetails.lastInstallmentDate;
                    record.firstEmiAmount = installmentDetails.firstInstallmentAmount;
                    record.lastEmiAmount = installmentDetails.lastInstallmentAmount;
                    record.installments = emis.data;
                } else {
                    output.message = "No Installments Found";
                    return output;
                }
            } else {
                const schedule = await loanService.emiSchedule(loanId, new Date(Date.now()), false);

                if (schedule.success) {
                    const totalInterestAmount = schedule.data.filter((x) => x.interestAmount > 0).reduce(function (prev, cur) {
                        return prev + cur.interestAmount;
                    }, 0);

                    record.totalInterestAmount = totalInterestAmount;
                    record.firstEmiDate = schedule.data[0].rePaymentActualDate;
                    record.lastEmiDate = schedule.data[schedule.data.length - 1].rePaymentActualDate;
                    record.firstEmiAmount = schedule.data[0].rePaymentAmount;
                    record.lastEmiAmount = schedule.data[schedule.data.length - 1].rePaymentAmount;
                    //record.installments = schedule.data;

                    const formattedEmis = await installmentsService.convertEmiScheduleToInstallments(loanInfo._id, loanInfo.loanNo, schedule.data, loanInfo.partnerId);

                    record.installments = formattedEmis.data;

                } else {
                    output.message = "No Installments Found";
                    return output;
                }
            }

            const approvedByAdmin = await loanInfo.flowDetails.filter(x => x.Step == "Approved By Admin");
            if (approvedByAdmin != null && approvedByAdmin.length > 0) {
                record.approvedOn = approvedByAdmin[0].insertedOn;
            } else {
                const loanContractAccepted = await await loanInfo.flowDetails.filter(x => x.Step == "Loan Contract Accepted");
                if (loanContractAccepted != null && loanContractAccepted.length > 0) {
                    record.approvedOn = loanContractAccepted[0].insertedOn;
                }
            }

            const docCount = await LoanInfo.find({ userId: mongoose.Types.ObjectId(loanInfo.userId), "lenderDetails[0].lenderPartnerId": mongoose.Types.ObjectId(loanInfo.lenderDetails[0].lenderPartnerId) }).countDocuments();

            record.existingLoansFromSameLenderCount = 1 + docCount;

            const userPartnerInfo = await UserPartnerInfo.findOne({ userId: mongoose.Types.ObjectId(loanInfo.userId), partnerId: mongoose.Types.ObjectId(loanInfo.partnerId) });

            if (userPartnerInfo && userPartnerInfo.bankDetails && userPartnerInfo.bankDetails.length > 0) {
                record.userBankDetails = userPartnerInfo.bankDetails;
            }

            output = {
                data: record,
                success: true,
                message: "fetched successfully",
                status: 200,
            };

        } else output.message = "Loan not Found";

    } catch (ex) {
        logger.logs(ex);
        const model = { loanId: loanId };
        saveErrorLog("getDataForLoanSanction", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};

module.exports.getDataForLoanRepayment = async function (loanId, emiNumber = 0) {
    let output = {
        data: null,
        success: false,
        message: "",
        status: 501,
    };
    try {
        let loanInfo = await LoanInfo.findById(loanId);
        let request = null;
        if (loanInfo) {
            if (emiNumber != 0) {
                const installment = await installmentsService.getInstallments(loanId, null, null, emiNumber);

                if (installment.success) {
                    request = {
                        loanId: loanInfo._id,
                        loanNo: loanInfo.loanNo,
                        lenderCustId: loanInfo.lenderDetails[0].lenderCustId,
                        lenderLoanId: loanInfo.lenderDetails[0].lenderLoanId,
                        rePaymentAmount: installment.data[0].installmentAmount,
                        principalRecovered: installment.data[0].installmentPrincipal,
                        interestAmount: installment.data[0].installmentInterest,
                        penalty: installment.data[0].penalty,
                        rePaymentActualDate: installment.data[0].actualDueDate,
                        updatedOn: installment.data[0].updatedOn,
                        receivedEmiDetails: installment.data
                    };
                } else output.message = installment.message;
            } else {
                const installments = await installmentsService.getInstallments(loanId, null, ["Received"], null);

                if (installments.success) {
                    request = {
                        loanId: loanInfo._id,
                        loanNo: loanInfo.loanNo,
                        lenderCustId: loanInfo.lenderDetails[0].lenderCustId,
                        lenderLoanId: loanInfo.lenderDetails[0].lenderLoanId,
                        receivedEmiDetails: installments.data
                    };
                } else output.message = installments.message;
            }
            output = {
                data: request,
                success: true,
                message: "fetched successfully",
                status: 200,
            };
        } else {
            output.message = "Loan not Found";
        }
    } catch (ex) {
        logger.logs(ex);
        const model = { loanId: loanId, emiNumber: emiNumber };
        saveErrorLog("getDataForLoanRepayment", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};

module.exports.getDataForLoanDocuments = async function (loanId) {
    let output = {
        data: null,
        success: false,
        message: "",
        status: 501,
    };
    try {
        let loanInfo = await LoanInfo.findById(loanId);
        if (loanInfo) {
            let userInfo = await UserInfo.findById(loanInfo.userId);
            if (userInfo) {
                let record = {
                    lenderCustId: loanInfo.lenderDetails[0].lenderCustId,
                    lenderLoanId: loanInfo.lenderDetails[0].lenderLoanId,
                    profileDoc: loanInfo.loanContractDetails.photoFilePath,
                    panDocDetails: userInfo.panDocDetails,
                    poaDetails: userInfo.poaDetails,
                    contractFile: loanInfo.loanContractDetails.contractFile,
                    partnerId: loanInfo.partnerId,
                    merchantCode: loanInfo.merchantCode,
                    productId: loanInfo.productId,
                    trxnId: loanInfo.trxnId,
                    kfsFile: loanInfo.loanContractDetails.kfsFile ? loanInfo.loanContractDetails.kfsFile : null
                };
                output = {
                    data: record,
                    success: true,
                    message: "fetched successfully",
                    status: 200,
                };
            } else {
                output.message = "UserInfo not Found";
            }
        } else {
            output.message = "Loan not Found";
        }
    }
    catch (ex) {
        logger.logs(ex);
        const model = { loanId: loanId };
        saveErrorLog("getDataForLoanDocuments", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
}

module.exports.getDataForLoan = async function (loanId) {
    let output = {
        data: null,
        success: false,
        message: "",
        status: 501,
    };
    try {
        let loanInfo = await LoanInfo.findById(loanId);
        if (loanInfo) {
            const userInfo = await UserInfo.findById(loanInfo.userId);

            const loanCycle = await LoanInfo.find({ userId: mongoose.Types.ObjectId(loanInfo.userId), "flowDetails": { $elemMatch: { "Step": "Loan In Progress" } } }).countDocuments();

            if (userInfo) {
                const installmentDetails = await installmentsService.getInstallmentDetails(loanId, true);

                const emiDetailsFormat = await installmentsService.convertEmiDetailsFormat(installmentDetails.installments);

                if (installmentDetails.success) {
                    let record = {
                        userId: userInfo._id,
                        loanReqDate: loanInfo.insertedOn,
                        firstName: userInfo.userDetails.firstName,
                        middleName: userInfo.userDetails.middleName,
                        lastName: userInfo.userDetails.lastName,
                        addressDetails: userInfo.addressDetails,
                        panNo: userInfo.panNo,
                        profileDoc: userInfo.profileDocDetails,
                        dob: userInfo.userDetails.dob,
                        mobileNo: userInfo.userDetails.mobileNo, //mobile number change - no change
                        emailId: userInfo.userDetails.emailId,
                        panDocDetails: userInfo.panDocDetails,
                        shopDetails: userInfo.shopDetails,
                        poaDetails: userInfo.poaDetails,
                        loanId: loanInfo._id,
                        loanNo: loanInfo.loanNo,
                        loanAmount: loanInfo.amount,
                        tenure: loanInfo.tenure,
                        frequency: loanInfo.repaymentFrequency,
                        purpose: loanInfo.purpose,
                        description: loanInfo.description,
                        loanCycle: loanCycle,
                        panDocVerification: userInfo.panDocVerification,
                        lenderCustId: loanInfo.lenderDetails[0].lenderCustId,
                        lenderLoanId: loanInfo.lenderDetails[0].lenderLoanId,
                        productId: loanInfo.productId,
                        amount: loanInfo.amount,
                        interestRate: loanInfo.interestRate,
                        totalInterestAmount: installmentDetails.totalInterestAmount,
                        adminRemarks: loanInfo.adminRemarks,
                        disbursedOn: loanInfo.disbursalDetails.disbursedOn,
                        firstEmiDate: installmentDetails.firstInstallmentDate,
                        lastEmiDate: installmentDetails.lastInstallmentDate,
                        firstEmiAmount: installmentDetails.firstInstallmentAmount,
                        lastEmiAmount: installmentDetails.lastInstallmentAmount,
                        contractFile: loanInfo.loanContractDetails.contractFile,
                        maturity: loanInfo.maturity,
                        repaymentFrequency: loanInfo.repaymentFrequency,
                        installments: installmentDetails.installments,
                        receivedEmiDetails: null,
                        questionnairreDetails: userInfo.questionnairreDetails
                    };

                    if (!IsNullEmpty(installmentDetails.installments)) {
                        const receivedEmis = installmentDetails.installments.filter((x) => x.status == "Received");
                        record.receivedEmiDetails = receivedEmis;
                    }

                    output = {
                        data: record,
                        success: true,
                        message: "fetched successfully",
                        status: 200,
                    };
                } else output.message = installmentDetails.message;
            } else output.message = "UserInfo not Found";
        } else output.message = "Loan not Found";
    }
    catch (ex) {
        logger.logs(ex);
        const model = { loanId: loanId };
        saveErrorLog("getDataForLoan", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
}

module.exports.getlenderPartnerCode = async function (loanId) {
    let output = {
        partnerId: null,
        partnerCode: null,
        displayName: null,
        success: false,
        message: "",
        status: 501,
    };
    try {
        const loanInfo = await LoanInfo.findById(loanId);
        //console.log("loanInfo.lenderDetails", loanInfo.lenderDetails);
        if (!IsNullEmpty(loanInfo.lenderDetails)) {
            // const lendingUserPartner = await getLendingUserPartner(mongoose.Types.ObjectId(loanInfo.lenderDetails[0].lenderId));

            // //const userInfo = await UserInfo.findById(loanInfo.lenderDetails[0].lenderId);
            // if (lendingUserPartner.success) {
            // const partnerMaster = await PartnerMaster.findById(lendingUserPartner.data.partnerId);
            const partnerMaster = await PartnerMaster.findById(loanInfo.lenderDetails[0].lenderPartnerId);
            let chargeInPer = 0;
            const preApproval = await PreApprovals.findOne({
                partnerId: mongoose.Types.ObjectId(loanInfo.partnerId),
                merchantCode: loanInfo.merchantCode,
                productType: loanInfo.productType.split("_")[0],
                $or: [{ trxnId: loanInfo.trxnId }, { productId: loanInfo.trxnId }, { productId: loanInfo.productId }]
            }).sort({ insertedOn: -1 });

            if (!IsNullEmpty(preApproval)) {
                chargeInPer = preApproval.processingFee.replace("%", "").trim();
            } else {
                chargeInPer = partnerMaster.processingCharge;
            }
            logger.logs("chargeInPer : ", chargeInPer);
            if (!IsNullEmpty(partnerMaster)) {
                output = {
                    partnerId: partnerMaster._id,
                    partnerCode: partnerMaster.partnerCode,
                    displayName: partnerMaster.displayName,
                    processingCharge: chargeInPer,
                    success: true,
                    message: "fetched Successfully",
                    status: 200,
                };
            } else {
                output.message = "Lending Partner Not Found";
            }
            // } else {
            //     output.message = lendingUserPartner.message;
            // }
        } else {
            output.message = "Lender Details Not Found";
        }
    } catch (ex) {
        logger.logs(ex);
        const model = { loanId: loanId };
        saveErrorLog("getlenderPartnerCode", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }

    return output;
};

module.exports.getDataForEmiSchedule = async function (loanId) {
    let output = {
        partnerCode: null,
        success: false,
        message: "",
        status: 501,
    };
    try {
        const loanInfo = await LoanInfo.findById(loanId);

        if (!IsNullEmpty(loanInfo)) {
            const request = {
                lenderCustId: loanInfo.lenderDetails[0].lenderCustId,
                lenderLoanId: loanInfo.lenderDetails[0].lenderLoanId,
                repaymentFrequency: loanInfo.repaymentFrequency,
                maturity: loanInfo.maturity,
                tenure: loanInfo.tenure,
                installments: null
            };

            const _installments = await installmentsService.getInstallments(loanId);

            if (_installments.success) {
                request.installments = _installments.data;
            } else {
                const emiRequest = await loanService.emiSchedule(loanInfo._id, new Date(Date.now()), false);

                if (emiRequest.success) {
                    const convertedInstallments = await installmentsService.convertEmiScheduleToInstallments(loanInfo._id, loanInfo.loanNo, emiRequest.data, loanInfo.partnerId);
                    if (convertedInstallments.success)
                        request.installments = convertedInstallments.data;
                    else {
                        output.message = convertedInstallments.message;
                        return output;
                    }
                } else {
                    output.message = emiRequest.message;
                    return output;
                }
            }

            output = {
                data: request,
                success: true,
                message: "fetched successfully",
                status: 200,
            };
        } else {
            output.message = "Loan Not Found";
        }
    } catch (ex) {
        logger.logs(ex);
        const model = { loanId: loanId };
        saveErrorLog("getDataForEmiSchedule", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};

module.exports.getDataforAddAddress = async function (loanId) {
    let output = {
        partnerCode: null,
        success: false,
        message: "",
        status: 501,
    };
    try {
        const loanInfo = await LoanInfo.findById(loanId);
        if (!IsNullEmpty(loanInfo)) {
            const userInfo = await UserInfo.findById(loanInfo.userId);
            if (!IsNullEmpty(userInfo)) {
                const request = {
                    lenderCustId: loanInfo.lenderDetails[0].lenderCustId,
                    lenderLoanId: loanInfo.lenderDetails[0].lenderLoanId,
                    addressDetails: userInfo.addressDetails
                };
                output = {
                    data: request,
                    success: true,
                    message: "fetched successfully",
                    status: 200,
                };
            } else {
                output.message = "User Not Found";
            }
        } else {
            output.message = "Loan Not Found";
        }
    } catch (ex) {
        logger.logs(ex);
        const model = { loanId: loanId };
        saveErrorLog("getDataforAddAddress", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }

    return output;
};

module.exports.getBorrowingPartnerDetails = async function (loanId) {
    let output = {
        processingCharge: 0,
        success: false,
        message: "",
        status: 501,
    };
    try {
        const loanInfo = await LoanInfo.findById(loanId);
        if (!IsNullEmpty(loanInfo)) {
            const partnerMaster = await PartnerMaster.findById(loanInfo.partnerId);
            if (!IsNullEmpty(partnerMaster)) {
                //let processingCharge = 0;
                const preApproval = await PreApprovals.findOne({
                    partnerId: mongoose.Types.ObjectId(loanInfo.partnerId),
                    merchantCode: loanInfo.merchantCode,
                    productType: loanInfo.productType.split("_")[0],
                    $or: [{ trxnId: loanInfo.trxnId }, { productId: loanInfo.trxnId }, { productId: loanInfo.productId }]
                }).sort({ insertedOn: -1 });
                if (!IsNullEmpty(preApproval)) {
                    const charges = await loanService.getLoanCharges(loanInfo.amount, loanInfo._id, preApproval._id, false);

                    if (charges.success) {
                        output = {
                            processingCharge: charges.processingChargePer,
                            success: true,
                            message: "fetched successfully",
                            status: 200,
                        };
                    } else output.message = "Offer Not Found";
                } else output.message = "Offer Not Found";
            } else output.message = "Borrowing Partner Not Found";
        } else output.message = "Loan Not Found";
    } catch (ex) {
        logger.logs(ex);
        const model = { loanId: loanId };
        saveErrorLog("getlenderPartnerCode", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }

    return output;
};

module.exports.getLenderLoans = async function (model) {
    let output = {
        data: null,
        success: false,
        message: "",
        status: 501,
    };
    try {
        let fromDate = null;
        let toDate = null;
        let query = { "disbursalDetails.disbursedOn": { $exists: true } };

        if (model.fromDate) {
            try {
                fromDate = getFromDate(model.fromDate);

            } catch (ex1) {
                output.message = "Invalid fromDate. Date should be in DD/MM/YYYY Format";
            }
        }

        if (model.toDate) {
            try {
                toDate = getToDate(model.toDate);
            } catch (ex1) {
                output.message = "Invalid toDate. Date should be in DD/MM/YYYY Format";
            }
        }

        if (fromDate && toDate)
            query = { "disbursalDetails.disbursedOn": { $gte: fromDate, $lte: toDate } };
        else if (fromDate)
            query = { "disbursalDetails.disbursedOn": { $gte: fromDate } };
        else if (toDate)
            query = { "disbursalDetails.disbursedOn": { $lte: toDate } };

        const userPartnerInfo = await UserPartnerInfo.findOne({ partnerId: mongoose.Types.ObjectId(model.partnerId), isPartnerUser: true });

        if (userPartnerInfo) {
            query["lenderDetails.lenderPartnerId"] = mongoose.Types.ObjectId(model.partnerId);

            const loanInfo = await LoanInfo.find(query).select("loanNo disbursalDetails.disbursedOn").sort("disbursalDetails.disbursedOn");
            const records = [];
            if (loanInfo) {
                for (const loan of loanInfo) {
                    const record = {
                        loanNo: loan.loanNo,
                        disbursalDate: getYMDFormattedDateWithTime(loan.disbursalDetails.disbursedOn, "-")
                    }
                    records.push(record);
                }
            }
            output = {
                data: records,
                success: true,
                message: "fetched successfully",
                status: 200,
            };

        } else {
            output.message = "Lending Partner User Not Found";
        }
    } catch (ex) {
        logger.logs(ex);
        saveErrorLog("getLenderLoans", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }

    return output;
}

module.exports.getDataForLoanCancellation = async function (loanId) {
    let output = {
        data: null,
        success: false,
        message: "",
        status: 501,
    };
    try {
        let loanInfo = await LoanInfo.findById(loanId);
        let request = null;
        if (loanInfo) {
            request = {
                loanId: loanInfo._id,
                loanNo: loanInfo.loanNo,
                lenderCustId: loanInfo.lenderDetails[0].lenderCustId,
                lenderLoanId: loanInfo.lenderDetails[0].lenderLoanId
            };
            output = {
                data: request,
                success: true,
                message: "fetched successfully",
                status: 200,
            };
        } else {
            output.message = "Loan not Found";
        }
    } catch (ex) {
        logger.logs(ex);
        const model = { loanId: loanId };
        saveErrorLog("getDataForLoanCancellation", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};

module.exports.getDataForBankDetails = async function (loanId) {
    let output = {
        data: null,
        success: false,
        message: "",
        status: 501,
    };
    try {
        let loanInfo = await LoanInfo.findById(loanId).select({ _id: 1, disbursalBankDetails: 1, lenderDetails: 1 });

        if (loanInfo && loanInfo.disbursalBankDetails) {

            let record = {
                lenderCustId: loanInfo.lenderDetails[0].lenderCustId,
                lenderLoanId: loanInfo.lenderDetails[0].lenderLoanId,
                disbursalBankDetails: loanInfo.disbursalBankDetails
            }
            output = {
                data: record,
                success: true,
                message: "fetched successfully",
                status: 200,
            };
        } else {
            output = {
                data: null,
                success: false,
                message: "No disbursalBankDetails Found",
                status: 200,
            };
        }

    } catch (ex) {
        logger.logs(ex);
        const model = { loanId: loanId, emiNumber: emiNumber };
        saveErrorLog("getDataForBankDetails", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
}
//---------------------------------------------------------------------------------------------------------------------------