<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.6.3/css/font-awesome.min.css" />
<style>
  .page-break-after {
    display: block;
    page-break-after: always;
  }

  .center {
    text-align: center;
  }

  .top {
    margin-top: 900px;
  }

  td,
  th {
    border: 1px solid black;
    text-align: left;
    padding: 8px;
  }

  table {
    border-collapse: collapse;
    width: 100%;
  }

  .w60 {
    width: 60%
  }

    .w25{
    width: 25%
  }

  .w40 {
    width: 40%
  }

  .w33 {
    width: 33.3%
  }


  .w20 {
    width: 20%
  }

  .w10 {
    width: 10%
  }

  .w5 {
    width: 5%
  }

  .w65 {
    width: 65%
  }

  .w50 {
    width: 50%
  }

  .w70 {
    width: 70%
  }

  .w35 {
    width: 35%
  }

  .w30 {
    width: 30%
  }

  .center {
    text-align: center;
  }

  .right {
    text-align: right;
  }

  .left {
    text-align: left;
  }

  .bGray {
    background-color: gray
  }

  .unbreakable {
    page-break-inside: avoid;
  }

  .fa {
    display: inline;
    font-style: normal;
    font-variant: normal;
    font-weight: normal;
    font-size: 14px;
    line-height: 1;
    font-family: FontAwesome;
    font-size: inherit;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .w33 {
    width: 33%;
    float: left;
  }

    .w35L {
    width: 35%;
    float: left;
  }

      .w30L {
    width: 30%;
    float: left;
  }

  .w100 {
    width: 100%;
  }

  .w80 {
    width: 80%;
    float: left;
  }

    .w20L {
    width: 20%;
    float: left;
  }

      .w40L {
    width: 40%;
    float: left;
  }

  .top5 {
    margin-top: 5%;

  }

  .min50 {
    min-width: 200px;

  }
  .mt100 {
    margin-top: 80px;
  }

  .fs12 {
    font-size: 12px;
  }

    .fs10 {
    font-size: 10px;
  }

      .fs14 {
    font-size: 14px;
  }

   .mt50 {
    margin-top: 50px;
  }

  .min200 {
    width: 500px !important;
  }

  .w8 {
    width: 8%;
  }

</style>


<div class="w100 fs14">
  <div class="w40L">
    <span class="min200"><b>As on Date</b>:</span><span>  {{asOnDate}}</span> <br />
    <span class="min200"><b>Account Holder Name</b>:  {{accountHolderName}}</span></br />
    <span class="min200"><b>Virtual Account No</b>:  {{virtualAccountNumber}}</span></br />
    <span class="min200"><b>Beneficiary Bank Acc</b>:  {{accountNo}}</span></br />
    <span class="min200"><b>IFSC</b>: {{ifsc}}</span></br />
  </div>
  <div class="w40L">
    <span class="min200"><b>Account Balance</b>:</span><span> {{#if accountBalance}}<i class="fa fa-inr" aria-hidden="true"></i>{{/if}}{{accountBalance}}</span> <br />
    <span class="min200"><b>Amount On Hold (Committed)</b>: {{#if amountOnHold}}<i class="fa fa-inr" aria-hidden="true"></i>{{/if}}{{amountOnHold}}</span></br />
    <span class="min200"><b>Available Balance</b>: {{#if availableBalance}}<i class="fa fa-inr" aria-hidden="true"></i>{{/if}}{{availableBalance}}</span></br />
    <span class="min200"><b>APR Realised Return</b>:  {{aprRealisedReturn}}<i class="fa fa-percent" aria-hidden="true"></i></span></br />
    <span class="fs10 !important"><i>(Committed mean your funds have been put on hold but not disbursed to borrower yet)</i></span>
  </div>
  <div class="w20L">
    <span class="min200"><b>Total VA Credit</b>:</span><span> {{#if totalVACredit}}<i class="fa fa-inr" aria-hidden="true"></i>{{/if}}{{totalVACredit}}</span> <br />
    <span class="min200"><b>Interest Earned</b>: {{#if interestEarned}}<i class="fa fa-inr" aria-hidden="true"></i>{{/if}}{{interestEarned}}</span></br />
    <span class="min200"><b>EMI's Credited</b>: {{#if emiCredited}}<i class="fa fa-inr" aria-hidden="true"></i>{{/if}}{{emiCredited}}</span></br />
    <span class="min200"><b>AVG Days Invested</b>:  {{avgDaysInvested}}</span></br />
  </div>
</div> 
<br/><br/><br />
<table class="fs12 mt50">
  <thead>
    <tr style="background-color:B51B1F; color:white">
      <td class="center w10"><b>Transaction Date</b></td>
      <td class="center w10"><b>Deposit Date</b></td>
      <td class="center w8"><b>UTR No</b></td>
      <td class="w40 center"><b>Drawer Name/Description</b></td>
      <td class="center w8"><b>Credit Amt</b></td>
      <td class="center w8"><b>Debit Amt</b></td>
      <td class="center w8"><b>Hold Amt</b></td>
      <td class="center w8"><b>Balance</b></td>
    </tr>
  </thead>
  <tbody>
    {{#each data}}
    <tr>
      <td class="center">{{transactionDate}}</td>
      <td class="center">
      {{depositDate}}</td>
      <td class="left">{{utrNo}}</td>
      <td class="left">{{description}}</td>
      <td class="right">{{#if creditAmt}}<i class="fa fa-inr"
          aria-hidden="true"></i>{{/if}}{{creditAmt}}</td>
      <td class="right">{{#if debitAmt}}<i class="fa fa-inr"
          aria-hidden="true"></i>{{/if}}{{debitAmt}}</td>
      <td class="right">{{#if holdAmt}}<i class="fa fa-inr"
          aria-hidden="true"></i>{{/if}}{{holdAmt}}</td>
      <td class="right">{{#if balance}}<i class="fa fa-inr"
          aria-hidden="true"></i>{{/if}}{{balance}}</td>
    </tr>
    {{/each}}
  </tbody>
    <tr>
      <td class="center">Total</td>
      <td class="right" colspan="3" style="background-color:lightsteelblue"></td>
      <td class="right" style="background-color:lightsteelblue">{{#if totalCreditAmt}}<i class="fa fa-inr" aria-hidden="true"></i>{{/if}}{{totalCreditAmt}}</td>
      <td class="right" style="background-color:lightsteelblue">{{#if totalDebitAmt}}<i class="fa fa-inr"
          aria-hidden="true"></i>{{/if}}{{totalDebitAmt}}</td>
      <td class="right">{{#if totalHoldAmt}}<i class="fa fa-inr"
          aria-hidden="true"></i>{{/if}}{{totalHoldAmt}}</td>
      <td class="right">{{#if totalBalance}}<i class="fa fa-inr"
          aria-hidden="true"></i>{{/if}}{{totalBalance}}</td>
    </tr>
</table>

</br />
<table style="width:50%; margin-left: 25%;" class="fs12 mt20">
  <thead>
    <tr style="background-color:B51B1F; color:white">

      <td class="center" style="width:50%"><b>Month</b></td>
      <td class="right"><b>Interest Earned</b></td>
    </tr>
  </thead>
  <tbody>
    {{#each monthlyInterest}}
    <tr>
      <td class="center">{{Month}}</td>
      <td class="right">{{#if interestEarned}}<i class="fa fa-inr" aria-hidden="true"></i>{{/if}}{{interestEarned}}
      </td>
    </tr>
    {{/each}}
  </tbody>

</table>
<br /></br />
