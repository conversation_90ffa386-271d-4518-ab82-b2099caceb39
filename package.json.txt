{"name": "fundfinaapi", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "PORT=$PORT && app_connectionString=$app_connectionString && env=$env && debug=$debug && app_jsonWebToken=$app_jsonWebToken && node --trace-warnings --max-old-space-size=3072 index.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@hapi/joi": "^17.1.1", "aws-sdk": "^2.1156.0", "axios": "^0.21.1", "bcrypt": "^4.0.1", "body-parser": "^1.19.2", "cluster": "^0.7.7", "config": "^3.3.1", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "crypto": "^1.0.1", "crypto-js": "^4.0.0", "date-diff": "^0.2.2", "dateadd": "^1.0.2", "dateformat": "^3.0.3", "debug": "^4.1.1", "dynamic-html-pdf": "^1.0.2", "express": "^4.17.1", "express-async-errors": "^3.1.1", "form-data": "^3.0.0", "fs": "^0.0.1-security", "fs-extra": "^9.0.0", "handlebars": "^4.7.6", "helmet": "^3.22.0", "isnullemptyorwhitespace": "^1.0.4", "joi-objectid": "^3.0.1", "joi-password-complexity": "^4.1.0", "json-2-csv": "^3.10.2", "json2xls": "^0.1.2", "jsonwebtoken": "^8.5.1", "lodash": "^4.17.20", "mkdirp": "^1.0.4", "moment": "^2.27.0", "mongoose": "^5.13.15", "morgan": "^1.10.0", "multer": "^1.4.2", "node-fetch": "^2.6.1", "nodemailer": "^6.4.6", "nodemon": "^2.0.15", "os": "^0.1.2", "path": "^0.12.7", "public-ip": "^4.0.2", "puppeteer": "^3.1.0", "read-excel-file": "^4.1.0", "request": "^2.88.2", "request-promise": "^4.2.5", "sharp": "^0.31.2", "short-unique-id": "^4.4.2", "srv-lookup": "^1.0.1", "ssh2": "^1.11.0", "tunnel-ssh": "^4.1.6", "util": "^0.12.4", "winston": "^3.8.2", "winston-mongodb": "^5.0.1", "xlsx": "^0.17.0"}}