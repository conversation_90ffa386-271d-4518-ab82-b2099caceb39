
const express = require('express');
const router = express.Router();
const _ = require('lodash');
const admin = require("../../../middleware/admin");
const {
    dbPurge
  } = require("../../../services/jobs/db-purge");

router.post("/dbpurge", async (req, res) => {
    let output = {
        success: true,
        message: "Db purge job initated",
        status: 200,
    }
    const response = dbPurge(req);
    return res
        .status(output.status)
        .send(_.pick(output, ["success", "message"]));
});

module.exports = router;