const auth = require("../../../middleware/auth");
const express = require("express");
const router = express.Router();
const mongoose = require("mongoose");
const admin = require("../../../middleware/admin");
const _ = require("lodash");
let bodyParser = require("body-parser");
router.use(bodyParser.urlencoded({ extended: true }));



const {
   paymentsLogger
  } = require("../../../services/ops/settlement/settlement");


router.post("/paymentAlert/:gateway?/:webhookType?", async (req, res) => {
    let output = {
        success: true,
        message: "Insta alert received",
        status: 200,
    }
    const response = paymentsLogger(req);
    return res
        .status(output.status)
        .send(_.pick(output, ["success", "message"]));
});



module.exports = router;