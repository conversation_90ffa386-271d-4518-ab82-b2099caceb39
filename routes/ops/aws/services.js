const express = require("express");
const router = express.Router();
const _ = require("lodash");
const { emailWebHook } = require("../../../services/ops/aws/services");
const bodyParser = require("body-parser");
const { getSignUrl, getSignUrlToDownload, getBulkSignUrlToDownload, getSignInvoiceUrlToDownload } = require("../../../services/ops/aws/s3");
const auth = require("../../../middleware/auth");

router.post("/emailWebHook", bodyParser.text({ type: '*/*' }), async (req, res) => {
    const response = await emailWebHook(req.headers, req.params, req.body);

    return res
        .status(response.status)
        .send(_.pick(response, ["success", "message"]));
});


router.get("/getSignUrl", [auth], async (req, res) => {
    const response = await getSignUrl(req.user.id, req.query);

    return res
        .status(response.status)
        .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/getSignUrlToDownload", [auth], async (req, res) => {
    const response = await getSignUrlToDownload(req.body.url);

    return res
        .status(response.status)
        .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/getBulkSignUrlToDownload", [auth], async (req, res) => {
    const response = await getBulkSignUrlToDownload(req.body.url);

    return res
        .status(response.status)
        .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/getSignedInvoiceUrlToDownload", [auth], async (req, res) => {
    const response = await getSignInvoiceUrlToDownload(req.body.url);

    return res
        .status(response.status)
        .send(_.pick(response, ["data", "success", "message"]));
});

module.exports = router;