const auth = require("../../../middleware/auth");
const express = require("express");
const router = express.Router();
const mongoose = require("mongoose");
const {
  validateKYCStatus,
  validateSavePayInPayOut,
  validatePayin,
  validateSavePayOut,
  validateRejectReasonList
} = require("../../../models/ops/registration/users");

const {
  validateSaveTranche
} = require("../../../models/ops/partner/partner");

const _ = require("lodash");
const bycrypt = require("bcrypt");

const admin = require("../../../middleware/admin");

const lender = require("../../../middleware/lender");

const adminOrLender = require("../../../middleware/adminOrLender");
const { uploadPaymentReconExcel, handleVaAccountSettlement } = require('../../../services/ops/repayment/repayment')
const joi = require("@hapi/joi");
const config = require("config");

const {
  updateKycStatus,
  updateLoanStatus,
  getKycInformation,
  getLoanInformation,
  getKycList,
  getPartnerKycList,
  getLoanList,
  getPersonalLoanList,
  getLoanListForTeleCalling,
  getEmi,
  getLendingPartnerList,
  getUserRole,
  getLoanListForLendingPartner,
  getPersonalLoanListForLendingPartner,
  getUserList,
  getPayInPayOutList,
  deletePayInPayOut,
  savePayInPayOut,
  getEarlyWarningSystem,
  getPortfolioLoanList,
  updateLedgerBalance,
  getDetailOfEmisCounts,
  addEmiRepaymentInLedger,
  resetLedgerBalance,
  getPortfolioReport,
  exportToExcelLedgerAccount,
  getInterestEarned,
  getDaysInvested,
  getPayOutModuleDetails,
  exportToExcelPayOutModuleDetails,
  getPayinPayoutUserList,
  savePayOut,
  getTrancheList,
  saveTranch,
  getPartnerListV2,
  getPartnerList,
  getPartnerConfigurations,
  getTrancheDetails,
  updateLoanTrancheNo,
  getTrancheLoanDetailsV2,
  getRejectReasonList,
  getTrancheMIS,
  getPayOutModuleDetailsNew,
  getEmisCollectionV2,
  getPendingEmisV2,
  markAsPaid,
  changeEnachStatus,
  getFundfinaLoans,
  getLendingPartners,
  getBorrowingPartners,
  getPartners,
  cancelLoan,
  getPendingEmisLoanWiseV2,
  getPendingEmisLoanWiseForTeleCalling,
  changeEnachStatusForLoan,
  holdWalletCollections,
  updateReviewLinks,
  sendEnachAndBankModificationLink,
  sendEngagementAppLink,
  bulkUpdateExistingSchedule,
  loanAllocationValidation,
  getScrutinyInfo,
  updateTeleCallingActions,
  getLoanDetailsTeleCalling,
  updatePartnerConfigurations,
  getPartnerTrancheRecords,
  createPartnerTrancheRecord,
  updatePartnerTrancheRecord,
  deletePartnerTrancheRecord,
  getLoanDetailsTeleCallingCollectionQueue,
  updateTeleCallingActionsForCollectionQueue,
  updateLoanStatusByLender,
  getRenewalsList,
  getPartnerListForProductMaster,
  getVideoVerificationUrl,
  handleFaceMatchApiResponse,
  videoVerificationResult,
  updateLoanMeta,
  getLendingPartnerListProductMaster,
  getAllProductMasterProducts,
  createProductMasterProduct,
  editProductMasterProduct,
  deleteProductMasterProduct,
  searchProductMasterProductByName,
  getLendingPartnerListDisplayName,
  getUserCentricDetail,
  deleteEmisNoLoans,
  deleteEmisNoUsers,
  regenerateLoanContract,
  getBankStatements,
  BankStatementDownloadRequest,
  rollbackLoan,
  getRollbackLoanList,
  getReconcileList,
  insertMISLog,
  getMISLogList,
  getPayoutTransactions,
  getPayoutTransactionsDetails,
  getMISLog,
  getWebhookList,
  getLenderConsolidatedSettlementAmount,
  getDailyMISReport,
  getSettledInformationDatewise,
  getSettlementPendingDateWise,
  getInvoiceList,
  getInvoiceDetails,
  approveInvoice,
  generateUpiLink,
  getPaymentLinkList,
  cancelPaymentLink,
  resendPaymentLink,
  getENachList,
  createManualQueue,
  getManualQueueList,
  manualQueueAction,
  getSettlementTransaction,
  moveBankStatementToManualQueue,
  getIgnoredMISList,
  sendCollectionCaseNotification,
  getLenderPayoutList,
  lenderPayoutInit,
  lenderPayoutStagingInit,
  lenderPayoutReDisburse,
  getLoansListForInvoice,
  getPartnerListByInvoiceType,
  createInvoice,
  getInvoiceListByPartnerId,
  createInvoiceByExcel,
  sendPreLegalNotice,
  getSettlementMISUTRList,
  getPartnerListBasedOnPartnerConfigurations,
  initiateInvoicePayment,
  reInitiateInvoicePayment,
  getReportLogList,
  getReportsDownloadURLs,
  getReportTypes,
  getExcelData,
  getCollectionOutcomeList,
  getCollectionOutcomeDetailsList,
  downloadLoanList,
  getLenderPayoutListV2,
  getPayoutOrderSplitDetails,
  lenderPayoutInitV2,
  lenderPayoutReDisburseV2
} = require("../../../services/ops/admin/admin");

const {
  sendEnachReassignmentRequest
} = require("../../../services/ops/mandate/eNach");

const { getMailModel } = require("../../../services/ops/common/common");
const { validateKYC } = require("../../../services/ops/partner/partner");
const { uploadExcel } = require("../../../services/ops/upload/uploadExcelApis");

const { getLoanListForForeclosure } = require("../../../services/ops/loan/foreclosure");

// const { getAllProductMasterProducts, createProductMasterProduct, editProductMasterProduct, deleteProductMasterProduct, searchProductMasterProductByName } = require("../../../services/ops/admin/admin");

const {
  createMandate, validateResendLinkRequest, updateLoanContractImage, downloadAllDocument
} = require("../../../services/ops/loan/loan");

const { generateCollectionAnalysis } = require('../../../services/ops/report/report');
const { sharedStorageUpdate } = require('../../../startup/service');
const { getForeclosureDetails, initiateForeclosure } = require('../../../services/ops/loan/foreclosure');
const {
  handleUpiWebhook,
  handleUpiWebhookV2
} = require("../../../services/ops/loan/postDisbursal");
const { paymentAdjustments } = require('../../../services/ops/payments-adjustment/payments-adjustment')

router.put("/updateKycStatus/:id", [auth, admin], async (req, res) => {
  // const { error } = validateKYCStatus(req.body);

  // if (error) return res.status(400).send(error.details[0].message);

  const response = await updateKycStatus(
    req.params.id,
    req.body.kycStatus,
    req.body.remarks,
    req.user.id,
    req.body.poaDocType,
    req.body.reviewDoc,
    req.body.detailsMismatch,
    req.body.poiName,
    req.body.poaName,
    req.body.poiDob,
    req.body.poaDob,
    req.body.partnerId,
    req.body.merchantCode
  );

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.put("/updateLoanStatus/:id", [auth, adminOrLender], async (req, res) => {

  const response = await updateLoanStatus(req.params.id, req.body, req.user.id);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.put("/updatePartnerConfigurations", [auth, admin], async (req, res) => {

  const response = await updatePartnerConfigurations(req.body);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/getKycList", [auth, admin], async (req, res) => {
  const response = await getKycList(req.body);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/getPartnerKycList", [auth, admin], async (req, res) => {
  const response = await getPartnerKycList(req.body);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/getLoanListForTeleCalling", [auth], async (req, res) => {
  const user = await getUserRole(req.user.id);

  if (user.success) {
    let response = {};
    response = await getLoanListForTeleCalling(req.body);
    return res
      .status(response.status)
      .send(_.pick(response, ["data", "success", "message", "totalPages"]));
  } else {
    return res
      .status(user.status)
      .send(_.pick(user, ["data", "success", "message", "totalPages"]));
  }
});

router.put("/updateTeleCallingActions", [auth, admin], async (req, res) => {

  const response = await updateTeleCallingActions(req.body, req.user.id);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
})

router.put("/updateLoanMeta", [auth, admin], async (req, res) => {

  const response = await updateLoanMeta(req.body, req.user.id);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
})

router.post("/getLoanDetailsTeleCalling", [auth], async (req, res) => {
  const user = await getUserRole(req.user.id);

  if (user.success) {
    let response = {};
    response = await getLoanDetailsTeleCalling(req.body);
    return res
      .status(response.status)
      .send(_.pick(response, ["data", "success", "message"]));
  } else {
    return res
      .status(user.status)
      .send(_.pick(user, ["data", "success", "message"]));
  }
});

router.put("/updateTeleCallingActionsCollectionQueue", [auth, admin], async (req, res) => {

  const response = await updateTeleCallingActionsForCollectionQueue(req.body, req.user.id);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
})

router.post("/getLoanDetailsTeleCallingCollectionQueue", [auth], async (req, res) => {
  let response = await getLoanDetailsTeleCallingCollectionQueue(req.body);
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/getLoanList", [auth], async (req, res) => {
  const user = await getUserRole(req.user.id);

  if (user.success) {
    let response = {};
    if (user.data == "Admin") response = await getLoanList(req.body);
    else if (user.data == "Lending Partner")
      response = await getLoanListForLendingPartner(req.body, req.user.id);

    return res
      .status(response.status)
      .send(_.pick(response, ["data", "success", "message", "totalPages"]));
  } else {
    return res
      .status(user.status)
      .send(_.pick(user, ["data", "success", "message", "totalPages"]));
  }
});

router.post("/getPersonalLoanList", [auth],  async (req, res) => {
  // const user = await getUserRole(req.user.id);
  
  let response = await getPersonalLoanList(req.body); // temporarily added
  return res
      .status(response.status)
      .send(_.pick(response, ["data", "success", "message", "totalPages"]));
  // if (user.success) {
  //   let response = {};
  //   if (user.data == "Admin") response = await getPersonalLoanList(req.body);
  //   else if (user.data == "Lending Partner")
  //     response = await getPersonalLoanListForLendingPartner(req.body, req.user.id);
// 
  //   return res
  //     .status(response.status)
  //     .send(_.pick(response, ["data", "success", "message", "totalPages"]));
  // } else {
  //   return res
  //     .status(user.status)
  //     .send(_.pick(user, ["data", "success", "message", "totalPages"]));
  // }
});

router.get("/getKycInformation/:id", [auth, admin], async (req, res) => {
  const response = await getKycInformation(req.params.id);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.get("/getLoanInformation/:id", [auth], async (req, res) => {
  const response = await getLoanInformation(req.params.id, req);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/getEmi", [auth, admin], async (req, res) => {

  let validateRes = validateGetEmiRequest(req.body);

  if (validateRes.error) {
    return res
      .status(400)
      .send({ success: false, message: validateRes.error.message });
  }

  const response = await getEmi(req.body);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

function validateGetEmiRequest(model) {

  if (!model) {
    return { error: { message: "Missing Model" } }
  }

  if (model.amount && (!+model.amount || +model.amount < 0)) {
    return { error: { message: "Invalid Amount" } }
  }

  if (model.interestRate && (!+model.interestRate || +model.interestRate < 0)) {
    return { error: { message: "Invalid interestRate" } }
  }

  if (model.tenure && (!+model.tenure || +model.tenure < 0)) {
    return { error: { message: "Invalid tenure" } }
  }

  if (model.adhocDays && (!+model.adhocDays || +model.adhocDays < 0)) {
    return { error: { message: "Invalid adhocDays" } }
  }

  return { error: null, response: {} }

}

router.get("/getLendingPartnerList", [auth, admin], async (req, res) => {
  const response = await getLendingPartnerList(req.params.id);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/getUsersList", [auth, admin], async (req, res) => {
  const response = await getUserList(req.body);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/getPayinPayoutUserList", [auth, admin], async (req, res) => {
  const response = await getPayinPayoutUserList(req.body);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/getPayInPayOutList", [auth, admin], async (req, res) => {
  // console.log("req.body",req.body);
  const response = await getPayInPayOutList(req.body);

  return res
    .status(response.status)
    .send(_.pick(response, ["partnerId", "name", "data", "success", "message"]));
});

router.post("/deletePayInPayOut", [auth, admin], async (req, res) => {
  const { error } = validatePayin(req.body);

  if (error) return res.status(400).send(error.details[0].message);
  const response = await deletePayInPayOut(req.body);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/savePayInPayOut", [auth, admin], async (req, res) => {
  const { error } = validateSavePayInPayOut(req.body);

  if (error) return res.status(400).send(error.details[0].message);

  const response = await savePayInPayOut(req.body);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/getEarlyWarningSystem", [auth, admin], async (req, res) => {
  const response = await getEarlyWarningSystem(req.body);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/getPortfolioLoanList", [auth], async (req, res) => {
  //const response = await getPortfolioLoanList(req.body);

  const response = {
    data: [],
    success: true,
    message: "data fetched successfully",
    status: 200,
  };

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/updateLedgerBalance", [auth, admin], async (req, res) => {
  const response = await updateLedgerBalance();

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/getDetailOfEmisCounts", [auth, admin], async (req, res) => {
  const response = await getDetailOfEmisCounts(req.body);
  //return res.xls('data.xlsx', response.data);
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/addEmiRepaymentInLedger", [auth, admin], async (req, res) => {
  const response = await addEmiRepaymentInLedger(req.body);
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/resetLedgerBalance", [auth, admin], async (req, res) => {
  const response = await resetLedgerBalance(req.body);
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/getPortfolioReport", [auth, admin], async (req, res) => {
  const response = await getPortfolioReport(req.body);
  return res
    .status(response.status)
    .send(
      _.pick(response, [
        "asOnDate",
        "accountHolderName",
        "virtualAccountNumber",
        "accountNo",
        "ifsc",
        "accountBalance",
        "amountOnHold",
        "availableBalance",
        "aprRealisedReturn",
        "totalVACredit",
        "interestEarned",
        "emiCredited",
        "avgDaysInvested",
        "totalCreditAmt",
        "totalDebitAmt",
        "totalHoldAmt",
        "totalBalance",
        "monthlyInterest",
        "data",
        "success",
        "message",
      ])
    );
});

router.post("/exportToExcelLedgerAccount", [auth, admin], async (req, res) => {
  const response = await exportToExcelLedgerAccount(req.body);
  return res
    .status(response.status)
    .send(
      _.pick(response, ["filePath", "fileName", "data", "success", "message"])
    );
});

router.post("/getInterestEarned", [auth, admin], async (req, res) => {
  const response = await getInterestEarned(
    req.body.userId,
    req.body.month,
    req.body.year
  );
  return res.status(200).send({ interest: response });
});

router.post("/getDaysInvested", [auth, admin], async (req, res) => {

  const response = await getDaysInvested(req.body.userId, req.body.endDate);
  return res.status(200).send({ interest: response });
});

router.post("/getPayOutModuleDetails", [auth, admin], async (req, res) => {
  const response = await getPayOutModuleDetailsNew(req.body);
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message", "totalPages", "meta"]));
});

router.post("/exportToExcelPayOutModuleDetails", [auth, admin], async (req, res) => {
  const response = await exportToExcelPayOutModuleDetails(req.body);
  return res
    .status(response.status)
    .send(_.pick(response, ["filePath", "fileName", "success", "message"]));
});

router.post("/getEmiCalculatorData", async (req, res) => {
  const response = await getEmi(req.body);

  let interestRecovered = 0;
  let principalRecovered = 0;
  let total = 0;
  let interestPerc = 0;
  let principalPerc = 0;

  interestRecovered = _.sumBy(response.data, "interestAmount");
  principalRecovered = _.sumBy(response.data, "principalRecovered");

  interestRecovered = Math.round((interestRecovered + Number.EPSILON) * 100) / 100;
  principalRecovered = Math.round((principalRecovered + Number.EPSILON) * 100) / 100;

  total = interestRecovered + principalRecovered;

  total = Math.round((total + Number.EPSILON) * 100) / 100;

  interestPerc = (interestRecovered / total) * 100;

  interestPerc = Math.round((interestPerc + Number.EPSILON) * 100) / 100;

  principalPerc = 100 - interestPerc;

  return res.status(response.status).send({
    emi: response.data[0].rePaymentAmount,
    interestRecovered,
    principalRecovered,
    total,
    interestPerc,
    principalPerc,
  });
});

// router.post("/setRepaymentActualDate", [auth, admin], async (req, res) => {
//   const response = await setRepaymentActualDate(req.body);
//   return res
//     .status(response.status)
//     .send(_.pick(response, ["success", "message"]));
// });

// router.post("/resetRepaymentActualDate", [auth, admin], async (req, res) => {
//   const response = await resetRepaymentActualDate(req.body);
//   return res
//     .status(response.status)
//     .send(_.pick(response, ["success", "message"]));
// });

router.post("/savePayOut", [auth, admin], async (req, res) => {
  const { error } = validateSavePayOut(req.body);

  if (error) return res.status(400).send(error.details[0].message);

  const response = await savePayOut(req.body);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/getEmisCollection", [auth, admin], async (req, res) => {
  const response = await getEmisCollectionV2(req.body);
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/getTrancheList", [auth, admin], async (req, res) => {
  const response = await getTrancheList(req.body);
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/saveTranche", [auth, admin], async (req, res) => {
  const { error } = validateSaveTranche(req.body);

  if (error) return res.status(400).send(error.details[0].message);

  const response = await saveTranch(req.body);
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});


router.post("/getPartnerListV2", [auth], async (req, res) => {
  const response = await getPartnerListV2(req.body);
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message", "totalPages"]));
});

router.post("/getLendingPartnerList", [auth, admin], async (req, res) => {
  const response = await getLendingPartnerListDisplayName(req.body);
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message", "totalPages"]));
});

router.get("/getPartnerConfigurations", [auth], async (req, res) => {
  const response = await getPartnerConfigurations(req.query);
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message", "totalPages"]));
});


router.post("/getPartnerList", [auth], async (req, res) => {
  const response = await getPartnerList(req.body);
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/getTrancheDetails", [auth, admin], async (req, res) => {
  const response = await getTrancheDetails(req.body.trancheId);
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/tempUpdateLoanTrancheNo", [auth, admin], async (req, res) => {
  const response = await updateLoanTrancheNo(req.body);
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/getTrancheLoanDetails", [auth, admin], async (req, res) => {
  const response = await getTrancheLoanDetailsV2(req.body);
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/getRejectReasonList", [auth, admin], async (req, res) => {
  const { error } = validateRejectReasonList(req.body);

  if (error) return res.status(400).send(error.details[0].message);
  const response = await getRejectReasonList(req.body);
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/getTrancheMIS", [auth], async (req, res) => {
  const response = await getTrancheMIS(req.body);
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "tranches", "success", "message"]));
});

router.post("/sendNewMandate", [auth, admin], async (req, res) => {

  //let mailModel = getMailModel();

  //update eNach Status
  //let response = await sendEnachReassignmentRequest(req.body.loanId, mailModel, req.user.id);

  const response = await changeEnachStatusForLoan(req.body.loanId, null, "Mandate", req.user.id);

  return res
    .status(200)
    .send(response);
});

router.post("/getPendingEmis", [auth, admin], async (req, res) => {
  let response = await getPendingEmisV2(req.body.partnerId, req.body.loanNo, req.body.merchantName, req.body.mobileNo, req.body.sortBy, req.body.overdueBy, req.body);
  return res
    .status(200)
    .send(response);
});

router.post("/markAsPaid", [auth, admin], async (req, res) => {

  let response = await markAsPaid(req.body.loanId, req.body.emiNumber, req.body.date, req.user.id);
  return res
    .status(200)
    .send(response);
});

router.post("/changeEnachStatus", [auth, admin], async (req, res) => {
  let response = await changeEnachStatus(req.body.loanId, req.body.emiNumber, req.body.status, req.user.id);

  return res
    .status(200)
    .send(response);
});

router.post("/getFundfinaLoans", [auth], async (req, res) => {
  let response = await getFundfinaLoans(req.body.userId, req);

  return res
    .status(response.status)
    .send(response.data);
});

router.post('/getBorrowingPartners', [auth, admin], async (req, res) => {
  const response = await getBorrowingPartners();
  return res.status(response.status).send(_.pick(response, ['data', 'success', 'message']));
});

router.post('/getLendingPartners', [auth, admin], async (req, res) => {
  const response = await getLendingPartners();
  return res.status(response.status).send(_.pick(response, ['data', 'success', 'message']));
});

router.post('/getPartners', [auth, admin], async (req, res) => {
  const response = await getPartners();
  return res.status(response.status).send(_.pick(response, ['data', 'success', 'message']));
});

router.post("/getPendingEmisLoanWise", [auth, admin], async (req, res) => {
  let response = await getPendingEmisLoanWiseV2(req.body.partnerId, req.body.loanNo, req.body.merchantName, req.body.mobileNo, req.body.overdueBy, req.body);

  return res
    .status(200)
    .send(response);
});

router.post("/changeEnachStatusForLoan", [auth, admin], async (req, res) => {
  let response = await changeEnachStatusForLoan(req.body.loanId, req.body.emiCount, req.body.status, req.user.id);

  return res
    .status(200)
    .send(response);
});

router.post("/holdWalletCollections", [auth, admin], async (req, res) => {
  let response = await holdWalletCollections(req.body.loanId, req.user.id, req.body.holdFlag, req.body.emiNumber);

  return res
    .status(200)
    .send(response);
});

router.post('/cancelLoan', [auth, admin], async (req, res) => {
  const response = await cancelLoan(req.body.loanNo);
  return res.status(response.status).send(_.pick(response, ['success', 'message']));
});


router.post("/sendLoanContract", [auth], async (req, res) => {
  const validateResponse = await validateResendLinkRequest(req.body.loanId, req);

  if (!validateResponse.success)
    return res
      .status(validateResponse.status)
      .send(_.pick(validateResponse, ["success", "message"]));

  const response = await createMandate(req.body.loanId, "resendLink", true, true);

  return res
    .status(response.status)
    .send(_.pick(response, ["success", "message"]));
});

router.post("/updateReviewLinks", [], async (req, res) => {
  const response = await updateReviewLinks();

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/validateKYC", async (req, res) => {
  const response = await validateKYC(req.body.userId, req.body.partnerId, req.body.type);
  return res
    .status(response.status)
    .send(_.pick(response, ["success", "message"]));
});

router.post("/sendEnachAndBankModificationLink", [auth, admin], async (req, res) => {
  const response = await sendEnachAndBankModificationLink(req.body.loanId, req.body.status, req.user.id);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});


router.post("/sendEngagementAppLink", [auth, admin], async (req, res) => {
  const response = await sendEngagementAppLink(req.body, req);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});


router.post("/bulkUpdateExistingSchedule", async (req, res) => {
  const response = await bulkUpdateExistingSchedule();

  return res
    .status(response.status)
    .send(_.pick(response, ["success", "message"]));
});

router.post("/loanAllocationValidation", [auth, admin], async (req, res) => {
  const response = await loanAllocationValidation(req.body.loanId, req.body.lendingPartnerName);

  return res
    .status(200)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/getScrutinyInfo", [auth], async (req, res) => {
  const response = await getScrutinyInfo(req.body.userId, req.body.isPartnerUser, req.body.partnerId, req.body.merchantCode);
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/updateLoanStatusByLender", [auth, lender], async (req, res) => {
  const response = await updateLoanStatusByLender(req.body, req.user.id);
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

// router.post("/updateLoanIdsInMandateCollection", async (req, res) => { /** NEED TO CHECK WITH TEAM */
//   const response = await updateLoanIdsInMandateCollection();

//   return res
//     .status(200)
//     .send(_.pick(response, ["data", "success", "message"]));
// });

router.post("/getPendingEmisLoanWiseTeleCalling", [auth, admin], async (req, res) => {
  let response = await getPendingEmisLoanWiseForTeleCalling(req.body.partnerId, req.body.loanNo, req.body.merchantName, req.body.mobileNo, req.body.overdueBy, req.body);

  return res
    .status(200)
    .send(response);
});

router.put("/updateLoanContractImage", [auth], async (req, res) => {

  const response = await updateLoanContractImage(req.body.loanId, req.body.path);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/getRenewalListForTeleCalling", [auth], async (req, res) => {

  let response = await getRenewalsList(req.body)
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message", "totalPages"]));

});

router.post("/uploadExcel", [auth, admin], async (req, res) => {
  req.setTimeout(6000000);
  let response = await uploadExcel(req, res);
  return res.status(response.status).send(_.pick(response, ["success", "message"]));
});


router.post("/handleFaceMatchResponse", async (req, res) => {

  const response = await handleFaceMatchApiResponse(req.query.id, req.body);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));

});

//Tranche CRUD APIs
router.post("/getPartnerTrancheRecords", [auth, admin], async (req, res) => {

  const response = await getPartnerTrancheRecords(req.body);
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message", "totalPages"]));
});

router.post("/createTrancheRecord", [auth, admin], async (req, res) => {
  const response = await createPartnerTrancheRecord(req.body, req.user.id);

  return res
    .status(response.status)
    .send(_.pick(response, ["success", "message"]));
});

router.post("/updateTrancheRecord", [auth, admin], async (req, res) => {
  const response = await updatePartnerTrancheRecord(req.body, req.user.id);

  return res
    .status(response.status)
    .send(_.pick(response, ["success", "message"]));
});

router.post("/deletePartnerTrancheRecord", [auth, admin], async (req, res) => {
  const response = await deletePartnerTrancheRecord(req.body);

  return res
    .status(response.status)
    .send(_.pick(response, ["success", "message"]));
});

//Tranche CRUD APIs End

router.get("/getVerificationLink", [auth], async (req, res) => {

  const response = await getVideoVerificationUrl(req.query.loanId, req);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});


router.get("/videoVerificationResult", [auth], async (req, res) => {

  const response = await videoVerificationResult(req.query.loanId, req);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

// router.post("/generateWeeklyCollectionAnalysisReport", async (req, res) => {
//   let response = await generateCollectionAnalysis(req.body)
//   return res
//     .status(response.status)
//     .send(_.pick(response, ["data", "success", "message"]));
// });

router.get("/downloadAllDocument", [auth], async (req, res) => {
  let response = await downloadAllDocument(req.query.loanId, req)
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.get("/userCentricData", [auth], async (req, res) => {
  let response = await getUserCentricDetail(req.query.loanId)
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});


router.get("/deleteEmisNoUsers", async (req, res) => {
  console.log("/deleteEmisNoUsers");
  let response = await deleteEmisNoUsers();
  return res
    .sendStatus(200);
});

router.get("/deleteEmisNoLoans", async (req, res) => {
  console.log("/deleteEmisNoLoans");
  let response = await deleteEmisNoLoans();
  return res
    .sendStatus(200);
});

router.post("/regenerateLoanContract", [auth, admin], async (req, res) => {
  const response = await regenerateLoanContract(req.body.loanNo);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

//Product Master page routes start
router.get("/getLendingPartnerListProductMaster", [auth, admin], async (req, res) => {
  const response = await getLendingPartnerListProductMaster(req.body);
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/getAllProductMasterProducts", [auth, admin], async (req, res) => {
  console.log("route: ", req.body);
  const response = await getAllProductMasterProducts(req.body);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message", "totalPages"]));
});

router.post("/createProductMasterProduct", [auth, admin], async (req, res) => {
  const response = await createProductMasterProduct(req.body, req.user.id);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/editProductMasterProduct", [auth, admin], async (req, res) => {
  const response = await editProductMasterProduct(req.body, req.user.id);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/deleteProductMasterProduct", [auth, admin], async (req, res) => {
  const response = await deleteProductMasterProduct(req.body);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/searchProductMasterProduct", [auth, admin], async (req, res) => {
  const response = await searchProductMasterProductByName(req.body);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.get("/startupServiceRefresh", async (req, res) => {

  if (req.query && req.query.key == config.get("startupRefresh.secretKey")) {
    sharedStorageUpdate()
    return res
      .status(200)
      .send("Success");
  }

  return res
    .status(400)
    .send("Invalid Key");
});
//Product Master page routes end


router.get("/bankstatements", [auth], async (req, res) => {

  let response = await getBankStatements(req.query, req)
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message", "totalPages"]));

});

router.get("/bankstatementsDownload", [auth], async (req, res) => {

  let response = await BankStatementDownloadRequest(req.query, req)
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message", "totalPages"]));

});

router.post("/rollbackLoan", [auth, admin], async (req, res) => {

  const response = await rollbackLoan(req.body.loans, req.user.id);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message", "totalPages"]));

});

router.post("/getRollbackLoanList", [auth, admin], async (req, res) => {
  const response = await getRollbackLoanList(req.body);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message", "totalPages"]));

});

router.post("/reconList", [auth], async (req, res) => {
  let response = await getReconcileList(req.body)
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message", "totalPages"]));
});

router.post("/misLog", [auth], async (req, res) => {
  let response = await insertMISLog(req.body, req.user.id, req.user.partnerId)
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/misLogList", [auth], async (req, res) => {
  let response = await getMISLogList(req.body, req.user.partnerId)
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message", "totalPages"]));
});

router.post("/payouttransactions", [auth], async (req, res) => {

  let response = await getPayoutTransactions(req.body, req)
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message", "totalPages"]));

});

router.post("/payouttransactionsDetails", [auth], async (req, res) => {

  let response = await getPayoutTransactionsDetails(req.body, req)
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message", "totalPages"]));

});

router.post("/misloglistV2", [auth], async (req, res) => {

  let response = await getMISLog(req.body, req)
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message", "totalPages"]));

});

router.post("/webhookLogsList", [auth], async (req, res) => {

  let response = await getWebhookList(req.body, req)
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message", "totalPages"]));

});

router.post("/lenderSettlementInformation", [auth], async (req, res) => {

  let response = await getLenderConsolidatedSettlementAmount(req.body, req)
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message", "totalPages"]));

});

router.post("/dailymisreport", [auth], async (req, res) => {

  let response = await getDailyMISReport(req.body, req)
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message", "totalPages"]));

});

router.post("/getsettlementinformationdatewise", [auth], async (req, res) => {

  let response = await getSettledInformationDatewise(req.body, req)
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message", "totalPages"]));

});

router.post("/getPendingSettlementDateWise", [auth], async (req, res) => {

  let response = await getSettlementPendingDateWise(req.body, req)
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message", "totalPages"]));
});

router.post("/getLoanListForForeClosure", [auth, admin], async (req, res) => {
  const user = await getUserRole(req.user.id);

  if (user.success) {
    let response = {};
    if (user.data == "Admin") response = await getLoanListForForeclosure(req.body);

    return res
      .status(response.status)
      .send(_.pick(response, ["data", "success", "message", "totalPages"]));
  } else {
    return res
      .status(user.status)
      .send(_.pick(user, ["data", "success", "message", "totalPages"]));
  }
});

router.post("/getForeclosureDetails", [auth, admin], async (req, res) => {
  let response = await getForeclosureDetails(req.body)
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/initiateForeclosure", [auth, admin], async (req, res) => {
  let response = await initiateForeclosure(req.body, req.user.id)
  return res
    .status(response.status)
    .send(_.pick(response, ["success", "message"]));
});

router.post("/generateUpiLink", [auth], async (req, res) => {

  let response = await generateUpiLink(req.body, req);
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.get("/payment/link/list", [auth], async (req, res) => {
  let response = await getPaymentLinkList(req.query);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message", "totalPages"]));
});

router.post("/cancel/payment/link", [auth], async (req, res) => {
  let response = await cancelPaymentLink(req.body);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.get("/resend/payment/link", [auth], async (req, res) => {
  let response = await resendPaymentLink(req.query);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.get("/payment/enach/list", [auth], async (req, res) => {
  let response = await getENachList(req.query);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message", "totalPages"]));
});

router.post("/getInvoiceList", [auth], async (req, res) => {

  const response = await getInvoiceList(req.body, req.user.id);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message", "totalPages"]));

});

router.get("/getInvoiceDetails/:id", [auth], async (req, res) => {

  const response = await getInvoiceDetails(req.params.id, req.user.id);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message", "totalPages"]));

});

router.post("/approveInvoice", [auth, admin], async (req, res) => {

  const response = await approveInvoice(req.body.status, req.body.invoiceNo, req.body.comments, req.user.id);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message", "totalPages"]));

});


router.post("/initiateForeclosure", [auth, admin], async (req, res) => {
  let response = await initiateForeclosure(req.body, req.user.id)
  return res
    .status(response.status)
    .send(_.pick(response, ["success", "message"]));
});


router.post("/generate/manualqueue", async (req, res) => {
  let response = await createManualQueue(req.body, req);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/handle/fieldCollection", async (req, res) => {
  let response = await handleVaAccountSettlement(req.body, req, res);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.get("/manual/queue", [auth], async (req, res) => {
  let response = await getManualQueueList(req.query);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message", "totalPages"]));
});

router.post("/manual/queue/action", [auth], async (req, res) => {
  let response = await manualQueueAction(req.body, req);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/settlement/transaction", [auth], async (req, res) => {
  let response = await getSettlementTransaction(req.body, req);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/manualqueue/move/bankstatement", [auth], async (req, res) => {
  let response = await moveBankStatementToManualQueue(req.body, req);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/igonored/mislog", [auth], async (req, res) => {
  let response = await getIgnoredMISList(req.body, req);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/getLoansListForInvoice", [auth], async (req, res) => {

  const response = await getLoansListForInvoice(req.body, req.user.id);

  return res
    .status(200)
    .send(_.pick(response, ["data", "success", "message"]));

});

router.post("/getPartnerListByInvoiceType", [auth], async (req, res) => {

  const response = await getPartnerListByInvoiceType(req.body, req.user.id);

  return res
    .status(200)
    .send(_.pick(response, ["data", "success", "message"]));

});

router.post("/createInvoice", [auth], async (req, res) => {

  const response = await createInvoice(req.body, req.user.id);

  return res
    .status(200)
    .send(_.pick(response, ["data", "success", "message"]));

});

router.post("/getInvoiceListByPartnerId", [auth], async (req, res) => {

  const response = await getInvoiceListByPartnerId(req.body, req.user.id);

  return res
    .status(200)
    .send(_.pick(response, ["data", "success", "message"]));

});

router.post("/createInvoiceByExcel", [auth], async (req, res) => {

  const response = await createInvoiceByExcel(req, req.user.id);

  return res
    .status(200)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/sendPreLegalNotice", [auth, admin], async (req, res) => {
  let response = await sendPreLegalNotice(req.body.loanNo);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/collection/notification", [auth, admin], async (req, res) => {

  if (req.query.isFile === "true") {
    let output = await getExcelData(req, res)
    req.body.loanNos = output.data.map(i => i["Loan No"])
    req.body.isSendNotification = req.query.isSendNotification == "true"
  }

  let response = await sendCollectionCaseNotification(req.body);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.get("/lender/payout/list", [auth], async (req, res) => {
  let response = await getLenderPayoutList(req.query, req);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/lender/payout/init", [auth], async (req, res) => {
  let response = await lenderPayoutInit(req.body, req);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/lender/payout/redisburse", [auth], async (req, res) => {
  let response = await lenderPayoutReDisburse(req.body, req);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});


router.post("/getsettlementmisutr", [auth], async (req, res) => {

  const response = await getSettlementMISUTRList(req.body, req.user.id);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message", "totalPages"]));

});

router.get("/getpartnerlistbasedconfigurations", async (req, res) => {

  const response = await getPartnerListBasedOnPartnerConfigurations(req.query, req);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message", "totalPages"]));

});

router.post("/initiateInvoicePayment", [auth, admin], async (req, res) => {

  const response = await initiateInvoicePayment(req.body.status, req.body.invoiceNo, req.user.id);

  return res
    .status(200)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/reInitiateInvoicePayment", [auth, admin], async (req, res) => {

  const response = await reInitiateInvoicePayment(req.body.transactionId, req.user.id);

  return res
    .status(200)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.get("/lender/payout/listv2", [auth], async (req, res) => {
  let response = await getLenderPayoutListV2(req.query, req);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message" , "totalPages"]));
});

router.post("/lender/payout/initv2", [auth], async (req, res) => {
  let response = await lenderPayoutInitV2(req.body, req);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.get("/lender/payout/ordersplit/list", [auth], async (req, res) => {
  let response = await getPayoutOrderSplitDetails(req.query, req);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message" ]));
});

router.post("/lender/payout/redisbursev2", [auth], async (req, res) => {
  let response = await lenderPayoutReDisburseV2(req.body, req);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});
router.post("/upiCallback", async (req, res) => {
  let response = await handleUpiWebhookV2(req.body, req);

  return res
    .status(200)
    .send(response);
});


router.post("/getReportsLogList", [auth, admin], async (req, res) => {

  const response = await getReportLogList(req.query, req);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message", "totalPages"]));
});


router.post("/getReportsDownloadUrls", [auth, admin], async (req, res) => {

  const response = await getReportsDownloadURLs(req.body, req);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/getReportTypes", [auth, admin], async (req, res) => {

  const response = await getReportTypes(req.body, req);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/uploadDisbursalExcel", [auth, admin], async (req, res) => {
  let output = {
    data: {},
    success: true,
    message: "File processing initiated, Please check Email For further information  ERROR/SUCCESS",
    status: 200
  }
  res
    .status(output.status)
    .send(_.pick(output, ["success", "message", "data"]));

  await uploadPaymentReconExcel(req, res);
});

router.post("/collectionOutcomeList", [auth, admin], async (req, res) => {

  const response = await getCollectionOutcomeList(req.body, req);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/collectionOutcomeDetailsList", [auth, admin], async (req, res) => {

  const response = await getCollectionOutcomeDetailsList(req.body, req);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message", "totalPages"]));
});

router.post("/downloadLoanList", [auth, lender], async (req, res) => {

  let response = await downloadLoanList(req.body, req.user.id);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/payment/adjustments", async (req, res) => {

  const response = await paymentAdjustments(req.body, req);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

module.exports = router;