const express = require("express");
const router = express.Router();
const Common = require("../../../services/ops/common/common");
const identityProviderSignzy = require("../../../services/ops/identity-provider/provider-signzy");

router.get("/authroize", async (req, res) => {

    identityProviderSignzy.createAuthUrl(req, res); 
  
});

router.get("/details", async (req, res) => {

    identityProviderSignzy.getAadhaarPanDetails(req, res);
});


router.post("/mask/aadhaar", identityProviderSignzy.maskAadhaar);
router.post("/mask/details", identityProviderSignzy.maskAadhaarCallback);

router.get("/cobrand/load", identityProviderSignzy.load);

module.exports = router;