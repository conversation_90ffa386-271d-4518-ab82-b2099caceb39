const auth = require("../../../middleware/auth");
const express = require("express");
const router = express.Router();

const _ = require("lodash");
const { sendWhatsAppMessage, whatsAppWebHook, migrateNotifications, insertConfigParameters } = require("../../../services/ops/common/whatsApp");

router.post('/whatsAppWebhook', async (req, res) => {

    const response = await whatsAppWebHook(req.body);

    // return res.status(200).send({
    //     data: "",
    //     success: true,
    //     message: "Saved successfully"
    // });

    return res.status(200).send(response);
});

router.post("/sendWhatsAppMessage", async (req, res) => {
    //update eNach Status
    let response = await sendWhatsAppMessage(req.body.userId, req.body.loanId, req.body.template);

    return res
        .status(200)
        .send(response);
});

router.post("/migrateNotifications", async (req, res) => {
    //update eNach Status
    let response = await migrateNotifications();

    return res
        .status(200)
        .send(response);
});

router.post("/insertConfigParameters", async (req, res) => {
    //update eNach Status
    let response = await insertConfigParameters(req.body);

    return res
        .status(200)
        .send(response);
});

module.exports = router;