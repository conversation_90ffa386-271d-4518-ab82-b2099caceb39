const _ = require("lodash");
 
const auth = require("../../../middleware/auth");
const express = require("express");
const router = express.Router();
const admin = require("../../../middleware/admin");
const { getLoanDetail, sendPaymentLink, sendOtpForLogin, verifyOtpForLogin, cancelPaymentLink, resendPaymentLink, sendAgentIdentity, uploadPaymentDocument, getAgentCollectionLogs, resentNotificationJob } = require("../../../services/ops/agent/agent");
const { sendEngagementAppLink } = require("../../../services/ops/admin/admin");

router.post("/login/send-otp", [], async (req, res) => {

    const response = await sendOtpForLogin(req.body, req);

    return res
        .status(response.status)
        .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/login/verify-otp", [], async (req, res) => {

    const response = await verifyOtpForLogin(req, res);

    let responseObj = {
        user: response.data,
        success: response.success,
        message: response.message,
    }

    return res
        .status(response.status)
        .send(responseObj);
});

router.get("/getLoanDetail", [auth], async (req, res) => {

    const response = await getLoanDetail(req.query, req);

    return res
        .status(response.status)
        .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/sendPaymentLink", [auth], async (req, res) => {

    const response = await sendPaymentLink(req.body, req);

    return res
        .status(response.status)
        .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/cancelPaymentLink", [auth], async (req, res) => {

    const response = await cancelPaymentLink(req, res);

    return res
        .status(response.status)
        .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/resendPaymentLink", [auth], async (req, res) => {

    const response = await resendPaymentLink(req, res);

    return res
        .status(response.status)
        .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/sendAgentIdentity", [auth], async (req, res) => {
    const response = await sendAgentIdentity(req, res);

    return res
        .status(response.status)
        .send(_.pick(response, ["data", "success", "message",]));
});

router.put("/uploadPaymentDocument", [auth], async (req, res) => {

    const response = await uploadPaymentDocument(req, res);

    return res
        .status(response.status)
        .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/sendEngagementAppLink", [auth], async (req, res) => {
    const response = await sendEngagementAppLink(req.body, req);

    return res
        .status(response.status)
        .send(_.pick(response, ["data", "success", "message"]));
});

router.get("/agentCollectionList", [auth], async (req, res) => {
    const response = await getAgentCollectionLogs(req.query, req);

    return res
        .status(response.status)
        .send(_.pick(response, ["data", "success", "message", "totalPages"]));
});

router.get("/resentNotificationJob", [], async (req, res) => {
    const response = await resentNotificationJob(req, res);

    return res
        .status(response.status)
        .send(_.pick(response, ["data", "success", "message",]));
});

module.exports = router;