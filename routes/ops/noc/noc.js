const auth = require("../../../middleware/auth");
const express = require("express");
const router = express.Router();
const _ = require("lodash");
const {
    uploadNocDocument,
    sendNocRequestMail,
    sendNocNotifications
} = require("../../../services/ops/noc/noc-service");
const { extractEmail } = require('../../../services/ops/noc/noc-email-extract');


router.post("/uploadNoc", async (req, res) => {
    let output = {
        data: null,
        success: false,
        message: "Oops someting went wrong",
        status: 400,
    };

    output = await uploadNocDocument(req.body.loanNo, req.body.filePath, req.body.fileName);

    return res
        .status(output.status)
        .send(_.pick(output, ["success", "message", "data"]));
});

router.post("/sendNocRequestMail", async (req, res) => {
    const response = await sendNocRequestMail(req.body.loanNo);

    return res
        .status(200)
        .send(_.pick(response, ["success", "message"]));

});


router.post("/extractEmail", async (req, res) => {

    let output = {
        data: {},
        success: true,
        message: "Extract email initiated",
        status: 200,
    };

    res
        .status(output.status)
        .send(_.pick(output, ["success", "message"]));

    extractEmail(req.body, req, res);

});

router.post("/sendNocNotifications", async (req, res) => {
    const response = await sendNocNotifications(req.body.loanNo);

    return res
        .status(200)
        .send(_.pick(response, ["success", "message"]));

});

module.exports = router;