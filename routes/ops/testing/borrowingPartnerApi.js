const express = require('express');
const auth = require("../../../middleware/auth");
const admin = require("../../../middleware/admin");
const router = express.Router();
const _ = require('lodash');
const debug = require("debug")("app:testingBorrowingPartnersAPis");

const { testLoanRepayments, testBulkLoanRepayment, testLoanRepaymentEnquiry, testBulkLoanRepaymentEnquiry } = require("../../../services/ops/testing/BorrowingPartnerAPis");


const { getAccoutBalance, initatePayment, confirmPayment, initateBatchPayment, confirmBatchPayment, statementEnquiry } = require('../../../services/ops/disbursal/payment-gateway-service/payment-gateway-service');
const Logger = require('../../../middleware/logger');
const logger = new Logger();

router.post("/loanRepayment", async (req, res) => {
    const response = await testLoanRepayments(req.body);

    return res
        .status(200)
        .send(_.pick(response, ["product_id", "reference_no", "status", "description"]));
});

router.post("/bulkLoanRepayment", async (req, res) => {
    const response = await testBulkLoanRepayment(req.body);
    return res
        .status(200)
        .send(_.pick(response, ["status", "message"]));
});

router.get("/loanRepaymentEnquiry", async (req, res) => {
    logger.logs("req.query", req.query);
    const response = await testLoanRepaymentEnquiry(req.query.product_id, req.query.reference_number);
    logger.logs("response", response);
    return res
        .status(200)
        .send(_.pick(response, ["repayment_status", "due_date", "repay_amount", "paidAt", "autoRetryAttempts", "referenceNumber", "paidOrNot", "activeOrNot"]));
});

router.post("/bulkLoanRepaymentEnquiry", async (req, res) => {
    const response = await testBulkLoanRepaymentEnquiry(req.body);
    return res
        .status(200)
        .send(_.pick(response, ["status", "desc", "err_code", "request_ref", "reconciliation", "error_enquires", "error"]));
});


// router.post("/getAccountBalance", async (req, res) => {
//     const response = await getAccoutBalance(req.body);
//     return res.status(200).send(_.pick(response, ["status", "message"]));
// });

// router.post("/initatePayment", async (req, res) => {
//     const response = await initatePayment(req.body);
//     return res.status(200).send(_.pick(response, ["status", "message"]));
// });

// router.post("/confirmPayment", async (req, res) => {
//     const response = await confirmPayment(req.body);
//     return res.status(200).send(_.pick(response, ["status", "message"]));
// });

// router.post("/getAccountBalance", async (req, res) => {
//     const response = await getAccoutBalance(req.body);
//     return res.status(200).send(_.pick(response, ["status", "message"]));
// });

// router.post("/initateBatchPayment", async (req, res) => {
//     const response = await initateBatchPayment(req.body);
//     return res.status(200).send(_.pick(response, ["status", "message"]));
// });

// router.post("/confirmBatchPayment", async (req, res) => {
//     const response = await confirmBatchPayment(req.body);
//     return res.status(200).send(_.pick(response, ["status", "message"]));
// });

// router.post("/getStatment", async (req, res) => {

//     let statements = []

//     let response = await getStatmentData(req.body, statements);

//     console.log(statements);

//     return res.status(200).send(response);
// });

// async function getStatmentData(payload, statements) {

//     const [error, response, rawRequest] = await statementEnquiry(payload);
//     if (response.prompt) {
//         if (response.statements && response.statements.length)
//             statements.push(...response.statements);
//         payload.prompt = response.prompt;
//         await getStatmentData(payload, statements);
//     } else {
//         return payload
//     }
// }

module.exports = router;