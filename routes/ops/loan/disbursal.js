const auth = require('../../../middleware/auth');
const express = require('express');
const router = express.Router();
const _ = require('lodash');
const { validateSearchDisbursal, validateBankDisbursal } = require('../../../models/ops/loan/disbursal');
const { getDisbursalList, saveDisbursalDetails, getBorrowingPartners, getActiveTrancheDetails, addTranche,
    exportDisbursalFile, uploadDisbursalExcel, getDisbursalDetails, disburseLoans, getDisbursalListV2 } = require('../../../services/ops/loan/disbursal');
const {  uploadDisbursalExcelV1 } = require('../../../services/ops/disbursal/disbursal-excel');
const { getLoanDisbursalTransactions,reInitiateTransaction } = require('../../../services/ops/disbursal/disbursal-transactions');
const admin = require("../../../middleware/admin");
const { disburseLoansV2, processLoanDisbursal, confirmLoanDisbursal, saveDisbursalDetailsV2, reDisburse, reDisburseLoans } = require("../../../services/ops/disbursal/disbursal")
const { batchDisbursalWrapper, batchPaymentConfirmation, batchRedisburse } = require("../../../services/ops/disbursal/disbursal-batch")


router.post('/getBankDisbursalList', [auth, admin], async (req, res) => {

    const { error } = validateSearchDisbursal(req.body);

    if (error) return res.status(400).send(error.details[0].message);

    const response = await getDisbursalListV2(req.body);

    return res.status(response.status).send(_.pick(response, ['data', 'success', 'message']));

});

router.post('/saveBankDisbursalDetails', [auth, admin], async (req, res) => {
    const { error } = validateBankDisbursal(req.body);

    if (error) return res.status(400).send(error.details[0].message);

    const response = await saveDisbursalDetails(req);

    return res.status(response.status).send(_.pick(response, ['data', 'success', 'message']));
});

router.post('/getBorrowingPartners', [auth, admin], async (req, res) => {
    const response = await getBorrowingPartners();
    return res.status(response.status).send(_.pick(response, ['data', 'success', 'message']));
});

router.post("/getActiveTrancheDetails", async (req, res) => {
    const response = await getActiveTrancheDetails(req.body.loanId);
    return res
        .status(response.status)
        .send(_.pick(response, ["data", "success", "message", "eNachResponse"]));
});

router.post("/addTranche", async (req, res) => {
    const response = await addTranche(req.body.loanId);
    return res
        .status(response.status)
        .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/exportDisbursalFile",[auth], async (req, res) => {
    const response = await exportDisbursalFile(req.body.loans, req.body.paymentMode);
    return res
        .status(response.status)
        .send(_.pick(response, ["filePath", "success", "message"]));
});

router.post("/uploadDisbursalExcel", [auth, admin], async (req, res) => {
    const response = await uploadDisbursalExcelV1(req, res);
    return res
        .status(response.status)
        .send(_.pick(response, ["success", "message"]));
});

router.post("/getDisbursalDetails", async (req, res) => {
    const response = await getDisbursalDetails(req.body.loanId);
    return res
        .status(response.status)
        .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/disburseLoans", [auth, admin], async (req, res) => {
    const response = await disburseLoans(req);
    return res
        .status(response.status)
        .send(_.pick(response, ["success", "message"]));
});


router.post("/disburseLoans/v2", [auth, admin], async (req, res) => {
    const response = await disburseLoansV2(req);
    return res
        .status(response.status)
        .send(_.pick(response, ["success", "message"]));
});

router.post("/disbursalTransactions", [auth, admin], async (req, res) => {
    const response = await getLoanDisbursalTransactions(req.body);
    return res.status(response.status).send(_.pick(response, ['data', 'success', 'message']));
});

router.post("/loanPayments", async (req, res) => {
    let output = {
        success: true,
        message: "Loan payment job initiated",
        status: 200,
    }
    const response = processLoanDisbursal(req);
    return res
        .status(output.status)
        .send(_.pick(output, ["success", "message"]));
});

router.post("/loanPaymentsConfirmation", async (req, res) => {
    let output = {
        success: true,
        message: "Loan payment confirmation job initated",
        status: 200,
    }
    const response = confirmLoanDisbursal(req);
    return res
        .status(output.status)
        .send(_.pick(output, ["success", "message"]));
});


router.post('/saveBankDisbursalDetailsV2', [auth, admin], async (req, res) => {
    
    const { error } = validateBankDisbursal(req.body);

    if (error) return res.status(400).send(error.details[0].message);

    const response = await saveDisbursalDetailsV2(req);

    return res.status(response.status).send(_.pick(response, ['data', 'success', 'message']));
});

router.post('/redisburseLoan', [auth, admin], async (req, res) => {
  
    const response = await reDisburse(req);

    return res.status(response.status).send(_.pick(response, ['data', 'success', 'message']));
});

router.post('/redisburseLoans', [auth, admin], async (req, res) => {
  
    const response = await batchRedisburse(req);

    return res
        .status(response.status)
        .send(_.pick(response, ["success", "message"]));
});


router.post('/reInitiateTransaction', [auth, admin], async (req, res) => {
  
    const response = await reInitiateTransaction(req);

    return res.status(response.status).send(_.pick(response, ['data', 'success', 'message']));
});

router.post("/batchLoanPayments", async (req, res) => {
    let output = {
        success: true,
        message: "Loan batch payment job initiated",
        status: 200,
    }
    const response = batchDisbursalWrapper(req);
    return res
        .status(output.status)
        .send(_.pick(output, ["success", "message"]));
});

router.post("/batchLoanPaymentsConfirmation", async (req, res) => {
    let output = {
        success: true,
        message: "Loan batch payment confirmation job initated",
        status: 200,
    }
    const response = batchPaymentConfirmation(req);
    return res
        .status(output.status)
        .send(_.pick(output, ["success", "message"]));
});

module.exports = router;