const auth = require("../../../middleware/auth");
const express = require("express");
const router = express.Router();
const mongoose = require("mongoose");
const config = require("config");
const { isDate, dateAdd } = require("dateadd");

const {
  LoanInfo,
  validateLoan,
  validateAdditionDescription,
  validateLoanStatus
} = require("../../../models/ops/loan/loan");

const { UserInfo, validateRejectReasonList } = require("../../../models/ops/registration/users");
const _ = require("lodash");
const bycrypt = require("bcrypt");
const admin = require("../../../middleware/admin");
const {
  isValidForNewLoan,
  insertUpdateLoan,
  updateLoanStatus,
  calculateEmi,
  emiSchedule,
  createContract,
  generateOtpManually,
  verifyCustomOtp,
  getLoanDetails,
  getPersonalLoanDetails,
  applyLoanData,
  getLoanId,
  getLoanInformation,
  getContractInformation,
  createPortfolioReport,
  getLoanEstimation,
  updateCollectionType,
  bulkUpdateMandateIds,
  updateHoldCollection,
  bulkInsertProductPartnerConfig,
  updateContractCases,
  moveLoanToAcceptedByClient,
  updateLendingPartner,
  getLoanStatus,
  insertUpdateBankDetails,
  createMandateForLoanByUser,
  getBankInformation,
  getMandateRequireStatus,
  getKfsDetails,
  getLastCompletedStep
} = require("../../../services/ops/loan/loan");

const { getPortfolioReport, getRejectReasonList } = require("../../../services/ops/admin/admin");
const { underReview, signContract, updateCreditLineLoanAmount, saveChildLoanAndSendNotifications, getChildLoanDetails, getPartnerConfig, updateCoordinatesByIp } = require("../../../services/ops/loan/workflow");

const {
  sendCustomSms,
} = require("../../../services/ops/registration/verificationService");

const postDisbursal = require("../../../services/ops/loan/postDisbursal");

const { getProductId, getSmsModel, pushToNotifications } = require("../../../services/ops/common/common");

const { ROLES } = require('../../../services/ops/common/constants').Constants;

router.post("/isValidForNewLoan", [auth], async (req, res) => {
  const response = await isValidForNewLoan(req.user, req.body.partnerId);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/borrowerLoanList", [auth], async (req, res) => {
  const response = await getLoanDetails(req.user, req.body.partnerId);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/personalLoanList", [auth], async (req, res) => {
  const response = await getPersonalLoanDetails(req.user, req.body?.partnerId);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.put("/insertUpdateLoanDescription/:id", [auth], async (req, res) => {
  const partnerResponse = await getPartnerConfig(req.params.id, req);

  if (!partnerResponse.success)
    return res
      .status(partnerResponse.status)
      .send(_.pick(partnerResponse, ["data", "success", "message"]));

  const response = await underReview(req.params.id, req.body.name, req.body.additionalDescription, req.user.id, "User", "front-end", partnerResponse.data.onboardingType === "1 Step" ? false : true);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message", "eNach", "url", "transitionState"]));
});

router.get("/:id", [auth], async (req, res) => {
  const response = await applyLoanData(req.params.id, req.user.id);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message", "estimate", "isStp", "discountText", "bankText", "offerAmount"]));
});

router.post("/insertUpdateLoanInfo", [auth], async (req, res) => {
  console.log("new loan 1 ")
  if (!req.body.loanId) {
    const response = await isValidForNewLoan(req.user, req.body.partnerId);
    console.log(response, "new loan validity")
    if (!response.success) {
      return res
        .status(response.status)
        .send(_.pick(response, ["data", "success", "message"]));
    }
  }

  const { error } = validateLoan(req.body);

  console.log(error, "new loan 3 ")

  if (error) return res.status(400).send(error.details[0].message);

  console.log( "new loan 4 ")

  const response = await insertUpdateLoan(req.body, req.user.id, req);

  console.log(response, "new loan 5 ")

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message", "url", "isStp", "eNach", "digilocker", "isForceDigiLocker", "kycExpired", "isCaptureNewDocument"]));
});

router.put("/updateLoanStatus/:id", [auth], async (req, res) => {
  const { error } = validateLoanStatus(req.body);

  if (error) return res.status(400).send(error.details[0].message);

  const response = await updateLoanStatus(req.params.id, req.body);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/getEmiSchedule", [auth], async (req, res) => {
  const disDate = new Date(req.body.disbursalDate);
  const response = await calculateEmi(
    req.body.loanAmount,
    req.body.tenure,
    req.body.interestRate,
    req.body.isMaturityMonth,
    req.body.repaymentFrequency,
    disDate,
    req.body.interestType,
    false,
    "upper",
    req.body.productType,
    req.body.holdCollection,
    req.body.adhocDays,
    req.body.interestComputation

  );
  return res.status(200).send(response);
});

router.put("/saveEmiSchedule/:id", [auth], async (req, res) => {
  // const disDate = new Date(req.body.disbursalDate);
  // let date2 = new Date();
  // const date1 = dateAdd("day", 1, date2);
  const dotParts = req.body.disbursalDate.split("/");
  const emiDateManipulated = new Date(
    dotParts[2],
    dotParts[1] - 1,
    dotParts[0],
    5,
    30,
    0,
    0
  );

  const response = await emiSchedule(req.params.id, emiDateManipulated, true);
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/sendOTPforLoanContract", [auth], async (req, res) => {
  const data = await generateOtpManually(req.body.loanId, "Loan Contract", "SMS", req);

  if (!data.success)
    return res
      .status(data.status)
      .send(_.pick(data, ["data", "success", "message"]));

  if (data.mobileNo.length != 10 || isNaN(data.mobileNo))
    return res.status(400).send("Invalid Mobile Number");

  let smsModel = getSmsModel();

  smsModel.To = data.mobileNo;
  smsModel.TemplateName = "OTP_LoanContract2";
  smsModel.VAR1 = data.otp;
  smsModel.VAR2 = `@${config.get("keys.webSubdomain")} #${data.otp}`;
  smsModel.Type = "TSMS";
  smsModel.From = config.get("keys.from");

  //const response = await sendCustomSms(smsModel);

  const sendSms = await pushToNotifications(data.partnerId, data.merchantCode, data.productId, data.trxnId,
    data.userId, data.loanId, "SMS", data.mobileNo, smsModel.TemplateName, "loan", null, null, smsModel, false, "TSMS");

  let response = {
    data: "",
    success: false,
    message: sendSms.message,
    status: 200
  };

  //Call sendnotifications
  if (sendSms.success)
    response = await postDisbursal.sendNotifications(sendSms.data._id);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/verifyOTPforLoanContract", [auth], async (req, res) => {
  const verifyOTP = await verifyCustomOtp(req.body.loanId, req.body.otp, "Loan Contract", req.body.otpAutoFill, "SMS", req);

  if (!verifyOTP.success)
    return res
      .status(verifyOTP.status)
      .send(_.pick(verifyOTP, ["data", "success", "message"]));

  // const createContract = await signContract(req.body.loanId, req.body.fileName, req.body.fileStream, req.body.shopPhoto, req.body.shopPhotoStream, req.body.shopVideo, req.body.shopVideoStream, req.body.ipAddress, req.body.disbursalConsent, req.body.coordinates);

  const createContract = await signContract(req.body.loanId, req.body.fileName, req.body.fileStream, req.body.shopInternalPhoto, req.body.shopInternalPhotoStream, req.body.shopExternalPhoto, req.body.shopExternalPhotoStream, req.body.ipAddress, req.body.disbursalConsent, req.body.coordinates);

  if (!req.body.coordinates || !req.body.coordinates.latitude) {
    updateCoordinatesByIp(req.body.loanId, req.ip)
  }

  return res
    .status(createContract.status)
    .send(_.pick(createContract, ["data", "success", "message"]));
});

router.post("/getLoanId", async (req, res) => {
  const response = await getLoanId();
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/getProductId", async (req, res) => {
  const response = await getProductId(
    req.body.tranId,
    req.body.merchantCode,
    req.body.identity
  );
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.get("/getLoanInformation/:id", [auth], async (req, res) => {
  const response = await getLoanInformation(req.params.id, req);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.get("/getContractInformation/:id", [auth], async (req, res) => {
  const response = await getContractInformation(req.params.id, req);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/getPortfolioReport", [auth], async (req, res) => {

  req.body.userId = req.user.id;
  const data = await getPortfolioReport(req.body);

  const response = await createPortfolioReport(
    "portfolio.html",
    _.pick(data, [
      "asOnDate",
      "accountHolderName",
      "virtualAccountNumber",
      "accountNo",
      "ifsc",
      "accountBalance",
      "amountOnHold",
      "availableBalance",
      "aprRealisedReturn",
      "totalVACredit",
      "interestEarned",
      "emiCredited",
      "avgDaysInvested",
      "totalCreditAmt",
      "totalDebitAmt",
      "totalHoldAmt",
      "totalBalance",
      "monthlyInterest",
      "data",
      "success",
      "message",
    ])
  );

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/getRejectReasonList", [auth], async (req, res) => {
  const { error } = validateRejectReasonList(req.body);

  if (error) return res.status(400).send(error.details[0].message);
  const response = await getRejectReasonList(req.body);
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

// router.post("/sendCustomSms", async (req, res) => {

//   let smsModel = getSmsModel();

//   smsModel.To = "**********";
//   smsModel.TemplateName = "O_Notification_2";
//   smsModel.VAR1 = "Congrats!";
//   smsModel.VAR2 = "70000";
//   smsModel.VAR3 = "MasterPay";
//   smsModel.VAR4 = "No";
//   smsModel.VAR5 = "CIBIL";
//   smsModel.VAR6 = "Under 24 hours";
//   smsModel.VAR7 = "https://bit.ly/fndfMpay";
//   smsModel.Type = "TSMS";
//   smsModel.From = config.get("keys.from");

//   const response = await sendCustomSms(smsModel);

//   return res
//     .status(response.status)
//     .send(_.pick(response, ["data", "success", "message"]));
// });

router.post("/getLoanEstimation", [auth], async (req, res) => {

  const loan = await getLoanEstimation(req.body.loanId, req.body.amount);

  return res
    .status(loan.status)
    .send(_.pick(loan, ["data", "success", "message"]));
});

router.post("/updateCollectionType", async (req, res) => {

  const loan = await updateCollectionType();

  return res
    .status(200)
    .send(_.pick(loan, ["data", "success", "message"]));
});

router.post("/bulkUpdateMandateIds", async (req, res) => {

  const loan = await bulkUpdateMandateIds();
  return res
    .status(200)
    .send(_.pick(loan, ["data", "success", "message"]));
});

router.post("/updateHoldCollection", async (req, res) => {

  const loan = await updateHoldCollection();

  return res
    .status(200)
    .send(_.pick(loan, ["data", "success", "message"]));
});

router.post("/sendOTPforCreditLineConsent", async (req, res) => {
  const data = await generateOtpManually(req.body.loanId, "Credit Line Consent", "SMS");

  if (!data.success)
    return res
      .status(data.status)
      .send(_.pick(data, ["data", "success", "message"]));

  if (data.mobileNo.length != 10 || isNaN(data.mobileNo))
    return res.status(400).send("Invalid Mobile Number");

  let smsModel = getSmsModel();

  smsModel.To = data.mobileNo;
  smsModel.TemplateName = "OTP Credit Line Consent";
  smsModel.VAR1 = data.otp;
  smsModel.VAR2 = data.amount;
  smsModel.Type = "TSMS";
  smsModel.From = config.get("keys.from");

  //const response = await sendCustomSms(smsModel);

  const sendSms = await pushToNotifications(data.partnerId, data.merchantCode, data.productId, data.trxnId,
    data.userId, data.loanId, "SMS", data.mobileNo, smsModel.TemplateName, "loan", null, null, smsModel, false, "TSMS");

  let response = {
    data: "",
    success: false,
    message: sendSms.message,
    status: 200
  };

  //Call sendnotifications
  if (sendSms.success)
    response = await postDisbursal.sendNotifications(sendSms.data._id);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/verifyOTPforCreditLineContract", async (req, res) => {
  const verifyOTP = await verifyCustomOtp(req.body.loanId, req.body.otp, "OTP Credit Line Consent", req.body.otpAutoFill, "SMS");

  if (!verifyOTP.success)
    return res
      .status(verifyOTP.status)
      .send(_.pick(verifyOTP, ["data", "success", "message"]));

  const response = await saveChildLoanAndSendNotifications(req.body);

  return res
    .status(response.status)
    .send(response);
});

router.post("/getChildLoanDetails", async (req, res) => {
  const response = await getChildLoanDetails(req.body);
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));

});

router.post("/bulkInsertProductPartnerConfig", async (req, res) => {
  const response = await bulkInsertProductPartnerConfig();
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));

});

router.post("/updateContractCases", async (req, res) => {

  const loan = await updateContractCases();

  return res
    .status(200)
    .send(_.pick(loan, ["data", "success", "message"]));
});

router.post("/moveLoanToAcceptedByClient", async (req, res) => {
  const response = await moveLoanToAcceptedByClient(req.body.loanNo);
  return res
    .status(200)
    .send(_.pick(response, ["data", "success", "message"]));

});

router.post("/updateLoanAmount", async (req, res) => {
  const response = await updateCreditLineLoanAmount(req.body.loanId, req.body.amount);
  return res
    .status(200)
    .send(_.pick(response, ["data", "success", "message"]));

});

router.post("/updateLendingPartner", async (req, res) => {
  const response = await updateLendingPartner(req.body.loanNo, req.body.partnerId);
  return res
    .status(200)
    .send(_.pick(response, ["data", "success", "message"]));

});

router.post("/getLoanStatus", [auth], async (req, res) => {
  const response = await getLoanStatus(req.body.loanId, req);
  return res
    .status(200)
    .send(response);

});

router.put("/insertUpdateBankDetails/:id", [auth], async (req, res) => {

  /** ROLE CHECK */
  if (req.user.role == ROLES.USER && req.params.id != req.user.id) {
    return res
      .status(400)
      .send("Access Denied");
  }

  const response = await insertUpdateBankDetails(req.body, req.params.id, req);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "eNachLink", "success", "message"]));
});

router.post("/createMandateForLoanByUser/:id", [auth], async (req, res) => {

  const response = await createMandateForLoanByUser(req.body, req.params.id, req);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "eNachLink", "success", "message", "authMode", "isMandateSigned"]));
});



router.get("/getBankInformation/:id", [auth], async (req, res) => {

  const response = await getBankInformation(req.params.id);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message", "url", "eNach", "mode"]));
});


router.get("/getMandateRequireStatus/:loanId", [auth], async (req, res) => {

  if (!req.params.loanId) {
    return res
      .status(500)
      .send({ status: 500, message: "loanId missing" });
  }

  const response = await getMandateRequireStatus(req.params.loanId, req);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/getKfsDetails", [auth], async (req, res) => {

  const kfsDetails = await getKfsDetails(req.body.loanId, req.body.fileName, req.body.fileStream, req.body.ipAddress);

  return res
    .status(kfsDetails.status)
    .send(_.pick(kfsDetails, ["data", "success", "message"]));
});

router.post("/getLastCompletedStep", [auth], async (req, res) => {

  const response = await getLastCompletedStep(req.body.loanId, req);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "isForceDigiLocker", "isCaptureNewDocument", "success", "message"]));
});

module.exports = router;

