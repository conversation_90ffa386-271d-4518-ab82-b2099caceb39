const express = require("express");
const router = express.Router();
var bodyParser = require('body-parser');
const { smsWebhook } = require("../../../services/ops/common/sms");

router.use(bodyParser.json()); // support json encoded bodies
router.use(bodyParser.urlencoded({ extended: true })); // support encoded bodies

router.post("/updateTmessageStatus", async (req, res) => {
    const response = await smsWebhook(req.body, "TSMS");

    return res
        .status(200)
        .send(response);
});

router.get("/updateOtpMessageStatus", async (req, res) => {
    const response = await smsWebhook(req.query, "OTP");

    return res
        .status(200)
        .send(response);
});


module.exports = router;