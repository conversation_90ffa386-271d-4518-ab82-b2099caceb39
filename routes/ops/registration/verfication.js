const auth = require("../../../middleware/auth");
const express = require("express");
const router = express.Router();
const mongoose = require("mongoose");
const {
  UserInfo,
  validate,
  validateMobile,
  validatePassword,
} = require("../../../models/ops/registration/users");
const _ = require("lodash");
const bycrypt = require("bcrypt");
const admin = require("../../../middleware/admin");
const {
  verifyOTP,
  getMobileFromUserName,
  generateOtpManually,
  verifyCustomOtp,
  getMaskedMobileNo,
  getMaskedEmailId,
  getMobileFromUserId,
  verifyCustomOtpForMerchant,
  updateMobile,
  updateWhatsAppConsent,
  confirmMerchantMobileNumber,
  getMobileNoFromLoanId,
  getPartnerId,
  validateOtpRequest,
  allowMobileEdit
} = require("../../../services/ops/registration/verificationService");
const { sendMail, replaceMailImagePath } = require("../../../services/ops/common/mailer");
const { saveDeviceInfo } = require("../../../services/ops/common/masters");
const { response } = require("express");
const config = require("config");
const { getMailModel, getSmsModel, pushToNotifications } = require("../../../services/ops/common/common");
const postDisbursal = require("../../../services/ops/loan/postDisbursal");
const { sendOTPForMerchant } = require("../../../services/ops/loan/loan");
const { LoanInfo } = require("../../../models/ops/loan/loan");
const { sendOtp } = require("../../../services/ops/otp-service/otp-service");
const otpService = require('../../../services/ops/otp-service/otp-service');
const { OTP_CHANNELS, NOTIFICATION_TYPES } = require('../../../services/ops/common/constants').Constants;
const { getPartnerConfig } = require("../../../services/ops/common/DbDocuments");
const { getSessionExpireTime } = require('../../../services/ops/common/common');
const Idfy = require("../../../partnerApiCalls/Idfy");
const Respo = require('../../../partnerApiCalls/Respo')
const joi = require("@hapi/joi");

router.post("/checkUserName", async (req, res) => {
  const user = await UserInfo.findOne({
    "userDetails.userName": { $regex: req.body.userName, $options: "i" },
  });

  if (!user) return res.send({ success: true, message: "No user found" });

  return res.send({ success: false, message: "UserName already exists." });
});

router.post("/checkEmail", async (req, res) => {
  const user = await UserInfo.findOne({
    "userDetails.emailId": { $regex: req.body.emailId, $options: "i" },
  });

  if (!user) return res.send({ success: true, message: "No user found" });

  return res.send({ success: false, message: "Please Enter Valid Email" });
});

router.post("/checkMobile", async (req, res) => {
  if (req.body.mobileNo.length != 10 || isNaN(req.body.mobileNo))
    return res.status(400).send("Invalid Mobile Number");


  const user = await UserInfo.findOne({
    "userDetails.mobileNo.mobileNumber": req.body.mobileNo, //mobile number change
  });

  if (!user) return res.send({ success: true, message: "No user found" });

  return res.send({ success: false, message: "Please Enter Valid Mobile" });
});

router.post("/checkMobileOTP", async (req, res) => {
  if (req.body.mobileNo.length != 10 || isNaN(req.body.mobileNo))
    return res.status(400).send("Invalid Mobile Number");

  const user = await UserInfo.findOne({
    "userDetails.mobileNo.mobileNumber": req.body.mobileNo, //mobile number change
  });

  if (!user) {
    //const response = await sendMobileOTP(req.body.mobileNo);

    // const sendSms = await pushToNotifications(null, null, null, null,
    //   null, null, "SMS", req.body.mobileNo, "OTP", "registration", null, null, null, false, "OTP");

    /** CHANGES MADE START */

    let obj = {
      notifyTo: req.body.mobileNo,
      channel: OTP_CHANNELS.SMS,
      notificationType: req.body.from && req.body.from == NOTIFICATION_TYPES.PARTNER_REGISTRATION ? NOTIFICATION_TYPES.PARTNER_REGISTRATION : NOTIFICATION_TYPES.MERCHANT_REGISTRATION
    }
    let { error: sendOtpError, response: sendOtpResponse } = await sendOtp(obj);

    if (sendOtpError) {
      let response = {
        data: "",
        success: false,
        message: sendOtpError.message, // CUSTOM ERROR MESSAGE
        status: 200
      };

      return res
        .status(response.status)
        .send(_.pick(response, ["data", "success", "message"]));
    }

    let messageType = "OTP_Registeration";
    let smsModel = getSmsModel();

    smsModel.To = req.body.mobileNo;
    smsModel.TemplateName = messageType;
    smsModel.VAR1 = sendOtpResponse.otp;
    smsModel.VAR2 = "";
    smsModel.Type = "TSMS";
    smsModel.From = config.get("keys.from");

    const sendSms = await pushToNotifications(null, null, null, null,
      null, null, "SMS", req.body.mobileNo, messageType, "registration", null, null, smsModel, false);

    /** CHANGES MADE END */

    let response = {
      data: "",
      success: false,
      message: sendSms.message,
      status: 200
    };

    //Call sendnotifications
    if (sendSms.success)
      response = await postDisbursal.sendNotifications(sendSms.data._id);

    return res
      .status(response.status)
      .send(_.pick(response, ["data", "success", "message"]));
  } else {
    return res
      .status(400)
      .send({ success: false, message: "Please Enter Valid Mobile" });
  }
});

router.post("/sendOTP", async (req, res) => {
  if (req.body.mobileNo.length != 10 || isNaN(req.body.mobileNo))
    return res.status(400).send("Invalid Mobile Number");

  //const response = await sendMobileOTP(req.body.mobileNo);

  const sendSms = await pushToNotifications(null, null, null, null,
    null, null, "SMS", req.body.mobileNo, "OTP", "registration", null, null, null, false, "OTP");

  let response = {
    data: "",
    success: false,
    message: sendSms.message,
    status: 200
  };

  //Call sendnotifications
  if (sendSms.success)
    response = await postDisbursal.sendNotifications(sendSms.data._id);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/authenticateOTP", async (req, res) => {
  if (req.body.mobileNo.length != 10 || isNaN(req.body.mobileNo))
    return res.status(400).send("Invalid Mobile Number");

  if (req.body.otp.length != 6 || isNaN(req.body.otp))
    return res.status(400).send("Invalid OTP Number");

  // const response = await verifyOTP(req.body.mobileNo, req.body.otp);

  /** CHANGES START */
  let payload = {
    notifyTo: req.body.mobileNo,
    notificationType: req.body.from && req.body.from || NOTIFICATION_TYPES.MERCHANT_REGISTRATION,
    channel: OTP_CHANNELS.SMS,
    otp: req.body.otp
  }
  const verifyOtpRes = await otpService.verifyOtp(payload);
  /** CHANGES END */

  let response = {
    data: "",
    success: false,
    message: verifyOtpRes && verifyOtpRes.error && verifyOtpRes.error.message || "Invalid OTP",
    status: 200
  };

  if (!verifyOtpRes.error) {
    response.success = true;
    response.message = "Success"
  }

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/sendMail/:id", async (req, res) => {
  let mailModel = getMailModel();
  mailModel.number = "12345";

  const response = await sendMail(req.params.id, null, "Loan Application Emails to Borrower", "Loan Application", mailModel, "User");

  return res.status(response.status).send(response);
});

router.post("/generateOTPFromUsername", async (req, res) => {
  const getUser = await getMobileFromUserName(req.body.value);

  if (!getUser.success) return res.status(getUser.status).send(getUser);

  const mobile = getUser.data.userDetails.mobileNo[0] ? getUser.data.userDetails.mobileNo[0].mobileNumber : ""; //mobile number change
  const email = getUser.data.userDetails.emailId[0];

  // const generateCustomOTP = await generateOtpManually(
  //   getUser.data._id,
  //   req.body.messageType,
  //   mobile,
  //   "SMS"
  // );

  /**  CHANGES START */
  let payload = {
    notifyTo: email,
    channel: OTP_CHANNELS.EMAIL,
    notificationType: NOTIFICATION_TYPES.FORGOT_PASSWORD,
    otpLength: 6
  }
  const { error: sendOtpError, response: sendOtpResponse } = await sendOtp(payload);
  /**  CHANGES END */

  let isSentOnEmail = false;
  let isSentOnMobile = false;
  let maskedEmail = getMaskedEmailId(email);
  let maskedMobile = getMaskedMobileNo(mobile);

  if (sendOtpError) {
    let response = {
      data: "",
      success: false,
      message: sendOtpError && sendOtpError.message,
      status: 200
    };

    return res.status(response.status).send(response);
  }

  if (mobile) {
    let smsModel = getSmsModel();
    smsModel.To = mobile;
    smsModel.TemplateName = req.body.messageType; //Change Password
    smsModel.VAR1 = sendOtpResponse.otp;
    smsModel.Type = "TSMS";
    smsModel.From = config.get("keys.from");
    //const sendSms = await sendCustomSms(smsModel);

    const sendSms = await pushToNotifications(null, null, null, null,
      getUser.data._id, null, "SMS", mobile, req.body.messageType, "otp", null, null, smsModel, false, "TSMS");

    let response = {
      data: "",
      success: false,
      message: sendSms.message,
      status: 200
    };

    //Call sendnotifications
    if (sendSms.success)
      response = await postDisbursal.sendNotifications(sendSms.data._id);

    if (response.success) isSentOnMobile = true;
  }
  if (email) {
    let mailModel = getMailModel();
    mailModel.number = sendOtpResponse.otp;

    const response = await sendMail(getUser.data._id, null, "Registration Emails", req.body.messageType, mailModel, "User");

    if (response.success) isSentOnEmail = true;
  }

  let message = "";

  if (isSentOnEmail && isSentOnMobile) {
    message = `OTP sent on ${maskedMobile} and ${maskedEmail}`;
  } else if (!isSentOnEmail && isSentOnMobile) {
    message = `OTP sent on ${maskedMobile}`;
  } else if (isSentOnEmail && !isSentOnMobile) {
    message = `OTP sent on ${maskedEmail}`;
  } else if (!isSentOnEmail && isSentOnMobile) {
    message = `Some error occurred`;
  }

  return res.status(200).send({
    data: "",
    success: isSentOnEmail || isSentOnMobile,
    message: message,
  });
});

router.post("/generateOTPFromUserId", [auth], async (req, res) => {
  const getUser = await getMobileFromUserId(req.user.id);

  if (!getUser.success) return res.status(getUser.status).send(getUser);

  const mobile = getUser.data.userDetails.mobileNo[0] ? getUser.data.userDetails.mobileNo[0].mobileNumber : ""; //mobile number change
  const email = getUser.data.userDetails.emailId[0];

  let payload = {
    notifyTo: mobile,
    channel: OTP_CHANNELS.SMS,
    notificationType: NOTIFICATION_TYPES.MY_PROFILE
  }
  const { error: sendOtpError, response: sendOtpResponse } = await sendOtp(payload);

  // const generateCustomOTP = await generateOtpManually(
  //   getUser.data._id,
  //   req.body.messageType,
  //   mobile,
  //   "SMS"
  // );

  let isSentOnEmail = false;
  let isSentOnMobile = false;
  let maskedEmail = getMaskedEmailId(email);
  let maskedMobile = getMaskedMobileNo(mobile);

  if (sendOtpError) {

    let response = {
      data: "",
      success: false,
      message: sendOtpError.message,
      status: 200
    };

    return res
      .status(200)
      .send({
        data: "",
        success: false,
        message: sendOtpError.message
      });
  }

  generateCustomOTP = sendOtpResponse;

  if (mobile) {
    let smsModel = getSmsModel();
    smsModel.To = mobile;
    smsModel.TemplateName = req.body.messageType; //Change Password
    smsModel.VAR1 = generateCustomOTP.otp;
    smsModel.Type = "TSMS";
    smsModel.From = config.get("keys.from");
    // const sendSms = await sendCustomSms(smsModel);

    const sendSms = await pushToNotifications(null, null, null, null,
      getUser.data._id, null, "SMS", mobile, req.body.messageType, "otp", null, null, smsModel, false, "TSMS");

    let response = {
      data: "",
      success: false,
      message: sendSms.message,
      status: 200
    };

    //Call sendnotifications
    if (sendSms.success)
      response = await postDisbursal.sendNotifications(sendSms.data._id);

    if (response.success) isSentOnMobile = true;
  }
  if (email) {
    let mailModel = getMailModel();
    mailModel.number = generateCustomOTP.otp;

    const response = await sendMail(getUser.data._id, null, "Registration Emails", req.body.messageType, mailModel, "User");

    if (response.success) isSentOnEmail = true;
  }

  let message = "";

  if (isSentOnEmail && isSentOnMobile) {
    message = `OTP sent on ${maskedMobile} and ${maskedEmail}`;
  } else if (!isSentOnEmail && isSentOnMobile) {
    message = `OTP sent on ${maskedMobile}`;
  } else if (isSentOnEmail && !isSentOnMobile) {
    message = `OTP sent on ${maskedEmail}`;
  } else if (!isSentOnEmail && isSentOnMobile) {
    message = `Some error occurred`;
  }

  return res.status(200).send({
    data: getUser.data._id,
    success: isSentOnEmail || isSentOnMobile,
    message: message,
  });
});

router.post("/changeMobile", [auth], async (req, res) => {
  if (req.body.mobileNo.length != 10 || isNaN(req.body.mobileNo))
    return res.status(400).send("Invalid Mobile Number");

  const user = await UserInfo.findOne({
    "userDetails.mobileNo.mobileNumber": req.body.mobileNo, //mobile number change
  });

  if (!user) {

    // if (req.user.id != user._id.toString()) {
    //   return res
    //     .status(400)
    //     .send("Access denied");
    // }

    /** CHANGES MADE START */
    let obj = {
      notifyTo: req.body.mobileNo,
      channel: OTP_CHANNELS.SMS,
      notificationType: NOTIFICATION_TYPES.MY_PROFILE
    }
    let { error: sendOtpError, response: sendOtpResponse } = await sendOtp(obj);

    if (sendOtpError) {
      return res
        .status(400)
        .send({ success: false, message: sendOtpError.message });
    }

    let messageType = "Change Details";
    let smsModel = getSmsModel();

    smsModel.To = req.body.mobileNo;
    smsModel.TemplateName = messageType;
    smsModel.VAR1 = sendOtpResponse.otp
    smsModel.Type = "TSMS";
    smsModel.From = config.get("keys.from");

    const sendSms = await pushToNotifications(null, null, null, null,
      null, null, "SMS", req.body.mobileNo, messageType, "otp", null, null, smsModel, false, "TSMS");

    /** CHANGES MADE END */

    let response = {
      data: "",
      success: false,
      message: sendSms.message,
      status: 200
    };

    //Call sendnotifications
    if (sendSms.success)
      response = await postDisbursal.sendNotifications(sendSms.data._id);

    return res
      .status(response.status)
      .send(_.pick(response, ["data", "success", "message"]));
  } else {
    return res
      .status(400)
      .send({ success: false, message: "Please Enter Valid Mobile" });
  }
});

router.post("/generateOTPForMerchant", async (req, res) => {

  const response = await getMobileNoFromLoanId(req.body.loanId);

  if (!response.success) return res.status(response.status).send(response);

  const reqSendOTPForMerchant = await sendOTPForMerchant(response.data.userId, req.body.loanId, response.data.mobile, req.body.messageType, true, req.body.isWhatsApp ? "WhatsApp" : "SMS", req.body.isVerifyMobileNumber)

  return res.status(200).send({
    data: "",
    success: reqSendOTPForMerchant.success,
    message: reqSendOTPForMerchant.message,
  });

});


router.post("/generateOTPForMerchantChangeMobileNumber", [auth], async (req, res) => {

  if (!req.body.mobileNo) return res.status(400).send({ success: false, data: null, message: "Invalid mobile number", });

  const response = await validateOtpRequest(req.body.loanId, req.body.mobileNo);

  if (!response.success) return res.status(response.status).send(response);

  const reqSendOTPForMerchant = await sendOTPForMerchant(response.data.userId, req.body.loanId, response.data.mobile, req.body.messageType, true, req.body.isWhatsApp ? "WhatsApp" : "SMS", req.body.isVerifyMobileNumber)

  return res.status(200).send({
    data: "",
    success: reqSendOTPForMerchant.success,
    message: reqSendOTPForMerchant.message,
  });

});

router.post("/getMaskedMobileNumber", async (req, res) => {

  const response = await getMobileNoFromLoanId(req.body.loanId);

  if (!response.success) return res.status(response.status).send(response);

  return res.status(200).send({
    data: {
      maskedMobileNo: response.data.maskedMobileNo,
      merchantCode: response.data.merchantCode
    },
    success: response.success,
    message: response.message,
  });
});

router.post("/getPartnerId", async (req, res) => {

  const response = await getPartnerId(req.body.loanId);

  return res.status(response.status).send(response);

});

router.post("/saveDeviceInfo", async (req, res) => {
  const response = await saveDeviceInfo(req.body, req.ip);
  return res.status(200).send(response);
});

router.post("/verifyCustomOTP", async (req, res) => {
  // const verifyOTP = await verifyCustomOtp(
  //   req.body.userId,
  //   req.body.otp,
  //   req.body.messageType,
  //   "SMS"
  // );

  let user = await UserInfo.findOne({ "userDetails.userName": { $regex: new RegExp("^" + req.body.value && req.body.value.toLowerCase() + "$", "i") } });

  if (!user) {
    user = await UserInfo.findOne({ "userDetails.emailId": req.body.value && req.body.value.toLowerCase() });
  }

  if (!user) {
    return {
      data: "",
      success: false,
      message: "Invalid User",
      status: 400,
    };
  }

  if (user.userDetails.emailId && user.userDetails.emailId.length)
    req.body.value = user.userDetails.emailId[0]

  /** CHANGES START */

  let payload = {
    notifyTo: req.body.value,
    notificationType: req.body && req.body.messageType == "Change Details" ? NOTIFICATION_TYPES.MY_PROFILE : NOTIFICATION_TYPES.FORGOT_PASSWORD,
    channel: req.body && req.body.messageType == "Change Details" ? OTP_CHANNELS.SMS : OTP_CHANNELS.EMAIL,
    otp: req.body.otp
  };
  const verifyOTP = await otpService.verifyOtp(payload)

  let response = {
    data: "",
    success: false,
    message: verifyOTP && verifyOTP.error && verifyOTP.error.message || "Invalid OTP",
    status: 200
  };

  if (!verifyOTP.error) {
    response.success = true
  }
  /** CHANGES END */

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/verifyCustomOTPForMerchant", async (req, res) => {
  const getUser = await getMobileNoFromLoanId(req.body.loanId);

  if (!getUser.success) return res.status(getUser.status).send(getUser);

  let uniqMobileNumber = req.body.mobileNo

  if (!uniqMobileNumber) {
    uniqMobileNumber = getUser.data.mobile[0] ? getUser.data.mobile[0].mobileNumber : ""
  }

  const verifyOTP = await verifyCustomOtpForMerchant(
    getUser.data.userId,
    req.body.otp,
    req.body.messageType,
    req.body.loanId,
    req.body.otpAutoFill,
    req.body.isWhatsApp ? "WhatsApp" : "SMS",
    uniqMobileNumber,
    req.body.isUpdate,
    req.body.isVerifyMobileNumber
  );

  if (req.body.mobileNo) getUser.data.mobileNo = req.body.mobileNo

  if (verifyOTP.success) {
    await updateWhatsAppConsent(getUser.data.userId, uniqMobileNumber, req.body.isWhatsApp); //have to validate it.

    const user = await UserInfo.findById(getUser.data.userId).select("-password");
    const token = user.generateAuthToken({ loggedInFrom: req.headers.loggedinfrom, partnerId: getUser.data.partnerId });
    const partner = await getPartnerConfig(null, verifyOTP.data.partnerId, null);

    let updatePayload = {
      isMobileNumberVerified: true,
      isMobileNumberConfirmed: true //for temp 
    }

    if (req.body.confirmed) updatePayload.isMobileNumberConfirmed = true
    await LoanInfo.updateOne({ _id: req.body.loanId }, { $set: updatePayload })

    if (req.body.isAgent) {
      return res.status(200).send({
        success: true,
        message: "Success",
      })

    }

    let sessionExpireTime = getSessionExpireTime()
    res.cookie('x-auth-token', token, {
      domain: config.get("keys.cookieDomain"),
      httpOnly: true,
      expires: sessionExpireTime
    });

    return res.send({
      success: true,
      message: "authentication successful",
      user: {
        userId: user._id,
        name: user.userDetails.firstName + " " + user.userDetails.lastName,
        token: token,
        loggedInFrom: req.headers.loggedinfrom
      },
      partner: partner
    });
  }
  else
    return res
      .status(verifyOTP.status)
      .send(_.pick(verifyOTP, ["data", "success", "message"]));
});

router.post("/confirmMerchantMobileNumber", [auth], async (req, res) => {

  const response = await confirmMerchantMobileNumber(req.user.id, req.body.loanId)

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/replaceMailImagePath", async (req, res) => {
  const response = await replaceMailImagePath(req.body.value1, req.body.value2);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/allowMobileUpdate", async (req, res) => {

  const response = await allowMobileEdit(req.body.loanId);

  return res.status(200).send(response);
});

router.post("/generateOTPwithMobile", async (req, res) => {

  const response = await validateOtpRequest(req.body.loanId, req.body.mobile);

  if (!response.success) return res.status(response.status).send(response);

  const reqSendOTPForMerchant = await sendOTPForMerchant(response.data.userId, req.body.loanId, response.data.mobile, req.body.messageType, true, req.body.isWhatsApp ? "WhatsApp" : "SMS")

  return res.status(200).send({
    data: "",
    success: reqSendOTPForMerchant.success,
    message: reqSendOTPForMerchant.message,
  });

});

router.post("/verifyOTPwithMobile", async (req, res) => {
  const getUser = await validateOtpRequest(req.body.loanId, req.body.mobile);

  if (!getUser.success) return res.status(getUser.status).send(getUser);

  const verifyOTP = await verifyCustomOtpForMerchant(
    getUser.data.userId,
    req.body.otp,
    req.body.messageType,
    req.body.loanId,
    req.body.otpAutoFill,
    req.body.isWhatsApp ? "WhatsApp" : "SMS",
    getUser.data.mobile[0].mobileNumber
  );

  if (verifyOTP.success) {
    await updateMobile(req.body.loanId, getUser.data.mobile[0].mobileNumber);
    await updateWhatsAppConsent(getUser.data.userId, req.body.mobile, req.body.isWhatsApp);

    const user = await UserInfo.findById(getUser.data.userId).select("-password");
    const token = user.generateAuthToken({ loggedInFrom: req.headers.loggedinfrom, partnerId: getUser.data.partnerId });
    const partner = await getPartnerConfig(null, verifyOTP.data.partnerId, null);

    let updatePayload = {
      isMobileNumberVerified: true,
      isMobileNumberConfirmed: true //for temp 
    }

    if (req.body.confirmed) updatePayload.isMobileNumberConfirmed = true
    await LoanInfo.updateOne({ _id: req.body.loanId }, { $set: updatePayload })

    let sessionExpireTime = getSessionExpireTime()
    res.cookie('x-auth-token', token, {
      domain: config.get("keys.cookieDomain"),
      httpOnly: true,
      expires: sessionExpireTime
    });

    return res.send({
      success: true,
      message: "authentication successful",
      user: {
        userId: user._id,
        name: user.userDetails.firstName + " " + user.userDetails.lastName,
        token: token,
        loggedInFrom: req.headers.loggedinfrom
      },
      partner: partner
    });
  }
  else
    return res
      .status(verifyOTP.status)
      .send(_.pick(verifyOTP, ["data", "success", "message"]));
});

//idfy udyog verification


router.post("/udyogVerification", [auth], async (req, res) => {

  const { error } = validateUserIdJoi(req.body);

  if (error) return res.status(400).send(error.details[0].message);


  let userDetails = await UserInfo.findOne({
    '_id': mongoose.Types.ObjectId(req.body.userId),
  });

  console.log('userDetails', userDetails)
  if (!userDetails) {
    return res.send('User not found')
  }

  if (userDetails?.udyogAadhaar?.requestId) {
    let udyogDetails = await new Idfy().verificationFromRequestId(userDetails?.udyogAadhaar?.requestId); //165395a8-293a-4e0e-a5af-e5e0e8ffe7e7
    console.log('udyog details', udyogDetails?.data[0]?.result?.source_output)
    if (udyogDetails?.status == 200 && udyogDetails?.data[0]?.status == 'in_progress') {
      userDetails.udyogAadhaar.lastVerifiedOn = Date.now();
      userDetails.udyogAadhaar.aadhaarNo = req.body.udyogNumber;
      await userDetails.save();
      return res
        .status(udyogDetails.status)
        .send({ message: 'Udyog details verifying in progress' });

    }
    if (udyogDetails?.status == 200 && udyogDetails?.data[0]?.status == 'completed') {
      let response = udyogDetails?.data[0]?.result?.source_output;
      userDetails.udyogAadhaar.aadhaarNo = req.body.udyogNumber;
      if (response?.address_details.length > 0) {
        userDetails.udyogAadhaar.addressDetails.area = response?.address_details[0]?.address_1?.area;
        userDetails.udyogAadhaar.addressDetails.city = response?.address_details[0]?.address_1?.city;
        userDetails.udyogAadhaar.addressDetails.district = response?.address_details[0]?.address_1?.district;
        userDetails.udyogAadhaar.addressDetails.door = response?.address_details[0]?.address_1?.door;
        userDetails.udyogAadhaar.addressDetails.nameOfPremises = response?.address_details[0]?.address_1?.name_of_premises;
        userDetails.udyogAadhaar.addressDetails.pin = response?.address_details[0]?.address_1?.pin;
        userDetails.udyogAadhaar.addressDetails.road = response?.address_details[0]?.address_1?.road;
        userDetails.udyogAadhaar.addressDetails.state = response?.address_details[0]?.address_1?.state;
      }

      if (response?.general_details) {
        userDetails.udyogAadhaar.generalDetails.appliedDate = response?.general_details?.applied_date
        userDetails.udyogAadhaar.generalDetails.commencementDate = response?.general_details?.commencement_date;
        userDetails.udyogAadhaar.generalDetails.dicName = response?.general_details?.dic_name;
        userDetails.udyogAadhaar.generalDetails.enterpriseName = response?.general_details?.enterprise_name;
        userDetails.udyogAadhaar.generalDetails.enterpriseType = response?.general_details?.enterprise_type;
        userDetails.udyogAadhaar.generalDetails.expiryDate = response?.general_details?.expiry_date;
        userDetails.udyogAadhaar.generalDetails.majorActivity = response?.general_details?.major_activity;
        userDetails.udyogAadhaar.generalDetails.modifiedDate = response?.general_details?.modified_date;
        userDetails.udyogAadhaar.generalDetails.socialCategory = response?.general_details?.social_category;
        userDetails.udyogAadhaar.generalDetails.state = response?.general_details?.state;
      }


      if (response?.nic_details?.length > 0) {
        userDetails.udyogAadhaar.nicDetails.activityType = response?.nic_details[0]?.nic_details_1?.activity_type;
        userDetails.udyogAadhaar.nicDetails.nic2Digit = response?.nic_details[0]?.nic_details_1?.nic_2_digit;
        userDetails.udyogAadhaar.nicDetails.nic4Digit = response?.nic_details[0]?.nic_details_1?.nic_4_digit;
        userDetails.udyogAadhaar.nicDetails.nic5Digit = response?.nic_details[0]?.nic_details_1?.nic_5_digit;
      }
      userDetails.udyogAadhaar.lastVerifiedOn = Date.now();

      await userDetails.save();

      return res
        .status(udyogDetails.status)
        .send({ data: userDetails?.udyogAadhaar, message: 'Udyog details fetched successfully' });
    }


  } else {
    console.log('in else')
    let findUdyogDetails = await new Idfy().udyogVerification(req.body.udyogNumber)
    console.log('details', findUdyogDetails)
    if (findUdyogDetails?.status == 202 && findUdyogDetails?.data?.request_id) {

      let udyogDetails = await new Idfy().verificationFromRequestId(findUdyogDetails?.data?.request_id); //165395a8-293a-4e0e-a5af-e5e0e8ffe7e7
      console.log('udyog details', udyogDetails)
      if (udyogDetails?.status == 200 && udyogDetails?.data[0]?.status == 'in_progress') {
        userDetails.udyogAadhaar.lastVerifiedOn = Date.now();
        userDetails.udyogAadhaar.requestId = findUdyogDetails?.data?.request_id;
        userDetails.udyogAadhaar.aadhaarNo = req.body.udyogNumber;
        await userDetails.save();
        return res
          .status(udyogDetails.status)
          .send({ message: 'Udyog details verifying in progress' });

      }
      if (udyogDetails?.status == 200 && udyogDetails?.data[0]?.status == 'completed') {
        let response = udyogDetails?.data[0]?.result?.source_output;
        userDetails.udyogAadhaar.aadhaarNo = req.body.udyogNumber;
        if (response?.address_details.length > 0) {
          userDetails.udyogAadhaar.addressDetails.area = response?.address_details[0]?.address_1?.area;
          userDetails.udyogAadhaar.addressDetails.city = response?.address_details[0]?.address_1?.city;
          userDetails.udyogAadhaar.addressDetails.district = response?.address_details[0]?.address_1?.district;
          userDetails.udyogAadhaar.addressDetails.door = response?.address_details[0]?.address_1?.door;
          userDetails.udyogAadhaar.addressDetails.nameOfPremises = response?.address_details[0]?.address_1?.name_of_premises;
          userDetails.udyogAadhaar.addressDetails.pin = response?.address_details[0]?.address_1?.pin;
          userDetails.udyogAadhaar.addressDetails.road = response?.address_details[0]?.address_1?.road;
          userDetails.udyogAadhaar.addressDetails.state = response?.address_details[0]?.address_1?.state;
        }

        if (response?.general_details) {
          userDetails.udyogAadhaar.generalDetails.appliedDate = response?.general_details?.applied_date
          userDetails.udyogAadhaar.generalDetails.commencementDate = response?.general_details?.commencement_date;
          userDetails.udyogAadhaar.generalDetails.dicName = response?.general_details?.dic_name;
          userDetails.udyogAadhaar.generalDetails.enterpriseName = response?.general_details?.enterprise_name;
          userDetails.udyogAadhaar.generalDetails.enterpriseType = response?.general_details?.enterprise_type;
          userDetails.udyogAadhaar.generalDetails.expiryDate = response?.general_details?.expiry_date;
          userDetails.udyogAadhaar.generalDetails.majorActivity = response?.general_details?.major_activity;
          userDetails.udyogAadhaar.generalDetails.modifiedDate = response?.general_details?.modified_date;
          userDetails.udyogAadhaar.generalDetails.socialCategory = response?.general_details?.social_category;
          userDetails.udyogAadhaar.generalDetails.state = response?.general_details?.state;
        }


        if (response?.nic_details?.length > 0) {
          userDetails.udyogAadhaar.nicDetails.activityType = response?.nic_details[0]?.nic_details_1?.activity_type;
          userDetails.udyogAadhaar.nicDetails.nic2Digit = response?.nic_details[0]?.nic_details_1?.nic_2_digit;
          userDetails.udyogAadhaar.nicDetails.nic4Digit = response?.nic_details[0]?.nic_details_1?.nic_4_digit;
          userDetails.udyogAadhaar.nicDetails.nic5Digit = response?.nic_details[0]?.nic_details_1?.nic_5_digit;
        }
        userDetails.udyogAadhaar.lastVerifiedOn = Date.now();

        await userDetails.save();
        return res
          .status(udyogDetails.status)
          .send({ data: userDetails?.udyogAadhaar, message: 'Udyog details verified and added successfully' });
      }


    }

  }


  return res
    .status(400)
    .send({ message: 'Unable to fetch the udyog details' });
});


router.post("/udyamVerification", [auth], async (req, res) => {

  const { error } = validateUserIdUdyamJoi(req.body);

  if (error) return res.status(400).send(error.details[0].message);


  let userDetails = await UserInfo.findOne({
    '_id': mongoose.Types.ObjectId(req.body.userId),
  });

  console.log('userDetails', userDetails)
  if (!userDetails) {
    return res.send('User not found')
  }

  if (userDetails?.udyamAadhaar?.requestId) {
    let udyamDetails = await new Idfy().verificationFromRequestId(userDetails?.udyamAadhaar?.requestId); //165395a8-293a-4e0e-a5af-e5e0e8ffe7e7
    console.log('udyam details', udyamDetails?.data)
    if (udyamDetails?.status == 200 && udyamDetails?.data[0]?.status == 'in_progress') {
      userDetails.udyamAadhaar.lastVerifiedOn = Date.now();
      userDetails.udyamAadhaar.aadhaarNo = req.body.udyamNumber;
      await userDetails.save();
      return res
        .status(udyamDetails.status)
        .send({ message: 'Udyam details verifying in progress' });

    }
    if (udyamDetails?.status == 200 && udyamDetails?.data[0]?.status == 'completed') {
      let response = udyamDetails?.data[0]?.result?.source_output;
      userDetails.udyamAadhaar.aadhaarNo = req.body.udyamNumber;
      console.log('ithe', response)

      if (response?.enterprise_type?.length > 0) {
        userDetails.udyamAadhaar.enterpriseType.classificationDate = response?.enterprise_type[0]?.enterprise_type_1?.classification_date;
        userDetails.udyamAadhaar.enterpriseType.classificationYear = response?.enterprise_type[0]?.enterprise_type_1?.classification_year;
        userDetails.udyamAadhaar.enterpriseType.type = response?.enterprise_type[0]?.enterprise_type_1?.enterprise_type;
      }

      if (response?.general_details) {


        userDetails.udyamAadhaar.generalDetails.appliedDate = response?.general_details?.applied_date
        userDetails.udyamAadhaar.generalDetails.commencementDate = response?.general_details?.commencement_date;
        userDetails.udyamAadhaar.generalDetails.dateOfInc = response?.general_details?.date_of_inc;
        userDetails.udyamAadhaar.generalDetails.dicName = response?.general_details?.dic_name;
        userDetails.udyamAadhaar.generalDetails.enterpriseName = response?.general_details?.enterprise_name;
        userDetails.udyamAadhaar.generalDetails.enterpriseType = response?.general_details?.enterprise_type;
        userDetails.udyamAadhaar.generalDetails.majorActivity = response?.general_details?.major_activity;
        userDetails.udyamAadhaar.generalDetails.msmeDi = response?.general_details?.msme_di;
        userDetails.udyamAadhaar.generalDetails.organizationType = response?.general_details?.organization_type;
        userDetails.udyamAadhaar.generalDetails.socialCategory = response?.general_details?.social_category;
        userDetails.udyamAadhaar.generalDetails.state = response?.general_details?.state;

      }


      if (response?.nic_details?.length > 0) {
        userDetails.udyamAadhaar.nicDetails.activityType = response?.nic_details[0]?.nic_details_1?.activity_type;
        userDetails.udyamAadhaar.nicDetails.date = response?.nic_details[0]?.nic_details_1?.date;
        userDetails.udyamAadhaar.nicDetails.nic2Digit = response?.nic_details[0]?.nic_details_1?.nic_2_digit;
        userDetails.udyamAadhaar.nicDetails.nic4Digit = response?.nic_details[0]?.nic_details_1?.nic_4_digit;
        userDetails.udyamAadhaar.nicDetails.nic5Digit = response?.nic_details[0]?.nic_details_1?.nic_5_digit;

      }

      if (response?.official_address) {
        userDetails.udyamAadhaar.officialAddress.block = response?.official_address?.block;
        userDetails.udyamAadhaar.officialAddress.city = response?.official_address?.city;
        userDetails.udyamAadhaar.officialAddress.district = response?.official_address?.district;
        userDetails.udyamAadhaar.officialAddress.door = response?.official_address?.door;
        userDetails.udyamAadhaar.officialAddress.email = response?.official_address?.email;
        userDetails.udyamAadhaar.officialAddress.mobile = response?.official_address?.mobile;
        userDetails.udyamAadhaar.officialAddress.nameOfPremises = response?.official_address?.name_of_premises;
        userDetails.udyamAadhaar.officialAddress.pin = response?.official_address?.pin;
        userDetails.udyamAadhaar.officialAddress.road = response?.official_address?.road;
        userDetails.udyamAadhaar.officialAddress.state = response?.official_address?.state;
        userDetails.udyamAadhaar.officialAddress.town = response?.official_address?.town;
      }


      if (response?.unit_details?.length > 0) {

        userDetails.udyamAadhaar.unitDetails.block = response?.unit_details[0]?.unit_1?.block;
        userDetails.udyamAadhaar.unitDetails.building = response?.unit_details[0]?.unit_1?.building;
        userDetails.udyamAadhaar.unitDetails.city = response?.unit_details[0]?.unit_1?.city;
        userDetails.udyamAadhaar.unitDetails.district = response?.unit_details[0]?.unit_1?.district;
        userDetails.udyamAadhaar.unitDetails.flat = response?.unit_details[0]?.unit_1?.flat;
        userDetails.udyamAadhaar.unitDetails.pin = response?.unit_details[0]?.unit_1?.pin;
        userDetails.udyamAadhaar.unitDetails.road = response?.unit_details[0]?.unit_1?.road;
        userDetails.udyamAadhaar.unitDetails.state = response?.unit_details[0]?.unit_1?.state;
        userDetails.udyamAadhaar.unitDetails.unitName = response?.unit_details[0]?.unit_1?.unit_name;
        userDetails.udyamAadhaar.unitDetails.village = response?.unit_details[0]?.unit_1?.village;


      }






      userDetails.udyamAadhaar.lastVerifiedOn = Date.now();

      await userDetails.save();

      return res
        .status(udyamDetails.status)
        .send({ data: userDetails?.udyamAadhaar, message: 'Udyam details verified and added successfully' });
    }


  } else {
    console.log('in else')
    let findUdyamDetails = await new Idfy().udyamVerification(req.body.udyamNumber)
    console.log('details  ', findUdyamDetails)
    if (findUdyamDetails?.status == 202 && findUdyamDetails?.data?.request_id) {

      let udyamDetails = await new Idfy().verificationFromRequestId(findUdyamDetails?.data?.request_id); //165395a8-293a-4e0e-a5af-e5e0e8ffe7e7
      console.log('udyamDetails ', udyamDetails)
      if (udyamDetails?.status == 200 && udyamDetails?.data[0]?.status == 'in_progress') {
        userDetails.udyamAadhaar.lastVerifiedOn = Date.now();
        userDetails.udyamAadhaar.requestId = findUdyamDetails?.data?.request_id;
        userDetails.udyamAadhaar.aadhaarNo = req.body.udyamNumber;
        await userDetails.save();
        return res
          .status(udyamDetails.status)
          .send({ message: 'Udyam details verifying in progress' });

      }
      if (udyamDetails?.status == 200 && udyamDetails?.data[0]?.status == 'completed') {
        let response = udyamDetails?.data[0]?.result?.source_output;
        userDetails.udyamAadhaar.aadhaarNo = req.body.udyamNumber;

        if (response?.enterprise_type?.length > 0) {
          userDetails.udyamAadhaar.classificationDate = response?.enterprise_type[0]?.enterprise_type_1?.classification_date;
          userDetails.udyamAadhaar.classificationYear = response?.enterprise_type[0]?.enterprise_type_1?.classification_year;
          userDetails.udyamAadhaar.enterpriseType = response?.enterprise_type[0]?.enterprise_type_1?.enterprise_type;
        }

        if (response?.general_details) {


          userDetails.udyamAadhaar.generalDetails.appliedDate = response?.general_details?.applied_date
          userDetails.udyamAadhaar.generalDetails.commencementDate = response?.general_details?.commencement_date;
          userDetails.udyamAadhaar.generalDetails.dateOfInc = response?.general_details?.date_of_inc;
          userDetails.udyamAadhaar.generalDetails.dicName = response?.general_details?.dic_name;
          userDetails.udyamAadhaar.generalDetails.enterpriseName = response?.general_details?.enterprise_name;
          userDetails.udyamAadhaar.generalDetails.enterpriseType = response?.general_details?.enterprise_type;
          userDetails.udyamAadhaar.generalDetails.majorActivity = response?.general_details?.major_activity;
          userDetails.udyamAadhaar.generalDetails.msmeDi = response?.general_details?.msme_di;
          userDetails.udyamAadhaar.generalDetails.organizationType = response?.general_details?.organization_type;
          userDetails.udyamAadhaar.generalDetails.socialCategory = response?.general_details?.social_category;
          userDetails.udyamAadhaar.generalDetails.state = response?.general_details?.state;

        }


        if (response?.nic_details?.length > 0) {
          userDetails.udyamAadhaar.nicDetails.activityType = response?.nic_details[0]?.nic_details_1?.activity_type;
          userDetails.udyamAadhaar.nicDetails.date = response?.nic_details[0]?.nic_details_1?.date;
          userDetails.udyamAadhaar.nicDetails.nic2Digit = response?.nic_details[0]?.nic_details_1?.nic_2_digit;
          userDetails.udyamAadhaar.nicDetails.nic4Digit = response?.nic_details[0]?.nic_details_1?.nic_4_digit;
          userDetails.udyamAadhaar.nicDetails.nic5Digit = response?.nic_details[0]?.nic_details_1?.nic_5_digit;

        }

        if (response?.official_address) {
          userDetails.udyamAadhaar.officialAddress.block = response?.official_address?.block;
          userDetails.udyamAadhaar.officialAddress.city = response?.official_address?.city;
          userDetails.udyamAadhaar.officialAddress.district = response?.official_address?.district;
          userDetails.udyamAadhaar.officialAddress.door = response?.official_address?.door;
          userDetails.udyamAadhaar.officialAddress.email = response?.official_address?.email;
          userDetails.udyamAadhaar.officialAddress.mobile = response?.official_address?.mobile;
          userDetails.udyamAadhaar.officialAddress.nameOfPremises = response?.official_address?.name_of_premises;
          userDetails.udyamAadhaar.officialAddress.pin = response?.official_address?.pin;
          userDetails.udyamAadhaar.officialAddress.road = response?.official_address?.road;
          userDetails.udyamAadhaar.officialAddress.state = response?.official_address?.state;
          userDetails.udyamAadhaar.officialAddress.town = response?.official_address?.town;
        }


        if (response?.unit_details?.length > 0) {

          userDetails.udyamAadhaar.unitDetails.block = response?.unit_details[0]?.unit_1?.block;
          userDetails.udyamAadhaar.unitDetails.building = response?.unit_details[0]?.unit_1?.building;
          userDetails.udyamAadhaar.unitDetails.city = response?.unit_details[0]?.unit_1?.city;
          userDetails.udyamAadhaar.unitDetails.district = response?.unit_details[0]?.unit_1?.district;
          userDetails.udyamAadhaar.unitDetails.flat = response?.unit_details[0]?.unit_1?.flat;
          userDetails.udyamAadhaar.unitDetails.pin = response?.unit_details[0]?.unit_1?.pin;
          userDetails.udyamAadhaar.unitDetails.road = response?.unit_details[0]?.unit_1?.road;
          userDetails.udyamAadhaar.unitDetails.state = response?.unit_details[0]?.unit_1?.state;
          userDetails.udyamAadhaar.unitDetails.unitName = response?.unit_details[0]?.unit_1?.unit_name;
          userDetails.udyamAadhaar.unitDetails.village = response?.unit_details[0]?.unit_1?.village;


        }






        userDetails.udyamAadhaar.lastVerifiedOn = Date.now();
        userDetails.udyamAadhaar.requestId = findUdyamDetails?.data?.request_id
        await userDetails.save();

        return res
          .status(udyamDetails.status)
          .send({ data: userDetails?.udyamAadhaar, message: 'Udyam details verified and added successfully' });
      }


    }

  }


  return res
    .status(400)
    .send({ message: 'Unable to fetch the udyam details' });
});



function validateUserIdJoi(model) {

  const user = joi.object({
    userId: joi.string().required(),
    udyogNumber: joi.string().required(),
  });

  const result = user.validate(model);

  return result;
}
function validateUserIdUdyamJoi(model) {

  const user = joi.object({
    userId: joi.string().required(),
    udyamNumber: joi.string().required(),
  });

  const result = user.validate(model);

  return result;
}

router.post("/respoCustomerEligibility", async (req, res) => {
  try {
    let result = await new Respo().customerEligibility(req.body);
    return res.send({ success: true, data: result })
  } catch (error) {
    return res.status(500).send({ success: false, error: err })
  }
});
router.post("/respoPreApprovalOffer", async (req, res) => {
  try {
    let result = await new Respo().preApprovalOffer(req.body);
    return res.status(200).send({ success: true, data: result })
  } catch (err) {
    return res.status(500).send({ success: false, error: err })
  }
});

module.exports = router;
