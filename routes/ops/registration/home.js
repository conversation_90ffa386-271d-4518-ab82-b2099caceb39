const express = require("express");
const Utils = require("../../../services/ops/common/common");
const router = express.Router();
const auth = require("../../../middleware/auth");
const fs = require('fs');
const config = require("config");
const { saveErrorLog } = require("../../../services/ops/common/masters");
const { UserInfo, OpenMarketUserInfo } = require("../../../models/ops/registration/users");
const Logger = require("../../../middleware/logger");
const Respo = require("../../../partnerApiCalls/Respo");
const LnTFinance = require("../../../partnerApiCalls/LnTFinance");
const logger = new Logger();
const joi = require("@hapi/joi");
const { LoanInfo } = require("../../../models/ops/loan/loan");
const moment = require('moment');
const mongoose = require("mongoose");
const axios = require("axios");
const IsNullEmpty = require("isnullemptyorwhitespace");
const { companyNameData, companyCategoryMapping } = require("../../../companyNameData");
const { PartnerMaster } = require("../../../models/ops/partner/partner");
const PayMe = require('../../../partnerApiCalls/PayMe')
const Signzy = require("../../../partnerApiCalls/Signzy");

const apiPath = __dirname;

router.get("/", (req, res) => {
  // res.redirect(req.baseUrl);
  res.sendFile("index.html", { root: __dirname + "/../../../portal/build" });
  //return res.render("index", { title: "Fundfina API", name: "Fundfina API" });
});

router.post("/image/encrypt", [auth], (req, res) => {
  const url = Utils.imagePathEncrypt(req.body.url);

  res.send({
    url: req.body.url,
    encryptedUrl: "/docs/" + url
  });
});
router.get("/docs/:path", (req, res) => {
  let filePath = Utils.imagePathDecrypt(req.params.path);
  console.log("filePath", filePath);
  try {
    let path = config.get("keys.proofImages") + filePath.replace("/ProofImages", "").replace("\ProofImages", "").trim();
    console.log("path", path);

    if (!path)
      res.redirect("/not-found");

    fs.access(path, error => {
      if (!error && filePath) {
        res.sendFile(filePath, { root: __dirname + "/../../../.." });
        return;
      } else {
        saveErrorLog("/docs/:path", apiPath, req.params.path, `filePath: ${filePath ? filePath : "not found"} Error: ${error && error.message ? error.message : "error not found and filepath not found"}`);
        res.redirect("/not-found");
      }
    });

    // if (fs.existsSync(path)) {
    //   res.sendFile(filePath, { root: __dirname + "/../../../.." });
    //   return;
    // }
  } catch (err) {
    console.error(err)
    res.redirect("/not-found");
    saveErrorLog("/docs/:path", apiPath, req.params.path, err.message);
  }
  //res.redirect("/not-found");
});

//Madhyaam webhooks

router.post("/v1/loanApplication/createLoanApplicationsResponse", (req, res) => {
  console.log('req body here for create loan application response here', req.body)
  res.send({
    status: '200',
    message: 'Success'
  })
});


router.post("/v1/offer/generateOffersResponse", (req, res) => {
  console.log('req body here for generate offer response here', req.body)

  res.send({
    status: '200',
    message: 'Success'
  })
});



router.post("/v1/loan/grantLoanResponse", (req, res) => {
  console.log('req body here for grant loan response here', req.body)
  res.send({
    status: '200',
    message: 'Success'
  })
});

router.post("/masterpay/addPersonalLoanDetails", async (req, res) => {

  const schema = joi.object({
    firstName: joi.string().required(),
    lastName: joi.string().required(),
    merchantCode: joi.string(),
    emailId: joi.string().required(),
    mobileNo: joi.number().required(),
    adhaar: joi.string().required(),
    pan: joi.string().required(),
    occupation: joi.string().required(),
    dob: joi.string().required(),
    referrerId: joi.string(),
    companyName: joi.string().required(),
    educationQualification: joi.string().required(),
    residenceOwnership: joi.string().required(),
    pincode: joi.string().required(),
    // companyCategory: joi.allow(),
    loanAmount: joi.string().required(),
    partnerId: joi.string().required()
  })

  const { error } = schema.validate(req.body);

  if (error) {
    return res.status(400).send(error.details[0].message)
  }

  let { firstName, lastName, emailId, mobileNo, adhaar, pan, occupation, dob, merchantCode, referrerId, companyName,
    educationQualification, residenceOwnership, pincode, loanAmount, partnerId } = req.body

  logger.logs('data received', req.body);

  // Checking if the user already exists or not

  const user = await OpenMarketUserInfo.findOne({ mobileNo: mobileNo, panNo: pan });

  // let createUser;

  if (user) {
    logger.logs('existing user', user);
    // createUser = user;
    user.occupation = occupation;
    user.firstName = firstName;
    user.lastName = lastName;
    user.emailId = emailId;
    user.pincode = pincode;
    user.companyName = companyName;
    user.educationQualification = educationQualification;
    user.residenceOwnership = residenceOwnership;
    user.loanAmount = loanAmount;
    user.dob = dob;
    user.aadhaarNo = adhaar;
    user.referrerId = referrerId;
    user.merchantCode = merchantCode;

    await user.save();
  } else {
    let userData = {
      "panNo": pan,
      "occupation": occupation,
      "firstName": firstName,
      "lastName": lastName,
      "emailId": emailId,
      "mobileNo": mobileNo,
      "pincode": pincode,
      "companyName": companyName,
      "educationQualification": educationQualification,
      "residenceOwnership": residenceOwnership,
      "loanAmount": loanAmount,
      "dob": dob,
      "aadhaarNo": adhaar,
      "referrerId": referrerId,
      "merchantCode": merchantCode
    }
    createUser = await OpenMarketUserInfo.create(userData);
    logger.logs('userCreated', createUser?._id)
  }

  function getMasterPayConfig(amount, status) {
    return {
      method: "POST",
      baseURL: (partnerId == "667ace6846554b63d045aa26"
        ? `https://login.payandserve.com/api/callback/loan/fundfina`
        : `https://m.masterpay.pro/Fundfina_callback`),
      data: {
        merchantCode: merchantCode,
        status: status,
        amount: amount,
        referredId: referrerId,
        firstName,
        lastName,
        mobileNo,
        pan,
        adhaar,
        loanAmount,
        companyName,
        educationQualification,
        residenceOwnership,
        pincode,
      }
    };
  }

  if (occupation == 'salaried') {
    let customerEligible = await new Respo().customerEligibility({
      "mobileNumber": mobileNo,
      "panNumber": pan,
    });

    logger.logs('customerEligible', customerEligible)
    if ((customerEligible.data.message == 'APPLICATION_ALREADY_EXIST' || customerEligible.data.status == 'ACCEPT') && customerEligible.status == 200) {
      console.log('entered here')
      let preAprrovalData = {
        "mobileNumber": mobileNo,
        "email": emailId,
        "panNumber": pan,
        "name": firstName + " " + lastName,
        "dob": moment(dob).format('YYYY-MM-DD'),
        "income": 100000,
        "employmentType": occupation,
        "bureauType": 3
      }

      let pushPreApproval = await new Respo().preApprovalOffer(preAprrovalData)
      logger.logs('pushprreapproval', pushPreApproval?.data, pushPreApproval?.status);

      if (pushPreApproval?.data?.status == 'ACCEPT' && pushPreApproval?.status == 200) {

        logger.logs('reached herere')
        await LoanInfo.create({
          userId: mongoose.Types.ObjectId(createUser?._id),
          merchantCode: merchantCode,
          status: 'Under Review',
          amount: pushPreApproval?.data?.offer,
          productType: "PL",
          partnerId: partnerId,
          lenderDetails: [{
            lenderId: mongoose.Types.ObjectId("662a37969c1f19595a53c7fe"),
            lenderName: "EASY PLATFORM SERVICES PRIVATE LIMITED"
          }]
        })

        const MasterPayconfig = getMasterPayConfig(pushPreApproval?.data?.offer, 'Under Review');
        let responseFromCallback = await axios(MasterPayconfig);
        logger.logs('response master pay callback', responseFromCallback)
        return res.send({
          status: '200',
          message: 'Success'
        })

      } else if (pushPreApproval?.status == 400 && pushPreApproval?.data?.message == "PRE_APPROVAL_OFFER_ALREADY_GENERATED") {

        const MasterPayconfig = getMasterPayConfig(pushPreApproval?.data?.offer, 'Under Review');
        let responseFromCallback = await axios(MasterPayconfig);
        logger.logs('response master pay callback', responseFromCallback)
        return res.send({
          status: '200',
          message: 'Success'
        })
      } else {

        const MasterPayconfig = getMasterPayConfig(undefined, 'Error from Lender');
        let responseFromCallback = await axios(MasterPayconfig);
        logger.logs('response master pay callback', responseFromCallback)
        return res.send({
          status: '200',
          message: pushPreApproval?.data?.message
        })
      }

    }
    else {

      const MasterPayconfig = getMasterPayConfig(undefined, customerEligible.data.message);
      let responseFromCallback = await axios(MasterPayconfig);
      logger.logs('response master pay callback', responseFromCallback)
      return res.send({
        status: '400',
        message: customerEligible?.data?.message
      })
    }
  } else {

    console.log('entered here --- lnt ');
    let filterCompanyCategory = companyCategoryMapping.filter(item => item.companyName === companyName);
    let companyCategory = filterCompanyCategory?.[0]?.finalCategory || 'Other';

    let params = {
      "uid": createUser?._id,
      "productCategory": "CL",
      "sourcingChannel": "CLEAG",
      "applicant_first_name": firstName,
      // "applicant_middle_name": middleName && "",
      "applicant_last_name": lastName,
      "mobileNo": mobileNo,
      "panNo": pan,
      "email": emailId,
      "pincode": pincode,
      "employment_type": occupation === "selfemployed" ? "Self Employed" : "Salaried",
      "dob": moment(dob).format('DD-MM-YYYY'),
      "company_name": companyName,
      "education_qualification": educationQualification,
      "residence_ownership": residenceOwnership,
      "company_category": companyName === "Other" ? "CAT E" : companyCategory
    }
    let leadCreation = await new LnTFinance().leadGeneration(params);

    console.log('aryan1', leadCreation?.data.errorDesc);
    // if(leadCreation?.data?.status === "SUCCESS"){
    //   res.send({ success: 200, message: leadCreation?.data.errorDesc })
    // }else{
    //   res.send({ success: 400, message: leadCreation?.data.errorDesc })
    // }
    logger.logs("response of leadcreation", leadCreation);
    let tempLeadId = leadCreation?.data?.leadId;
    if (leadCreation?.data?.status === "SUCCESS") {
      let customerEligibleParams = {
        "uid": params?.uid,
        "productCategory": "CL",
        "sourcingChannel": "CLEAG"
      }
      let checkEligibility = await new LnTFinance().customerEligibility(customerEligibleParams);
      logger.logs("response of customer eligibility", checkEligibility)
      if (checkEligibility?.data?.status === "SUCCESS") {
        await LoanInfo.create({
          userId: mongoose.Types.ObjectId(params?.uid),
          merchantCode: merchantCode,
          status: 'Under Review',
          amount: checkEligibility?.data?.breMaxEligibilityAmount,
          productType: "PL",
          leadId: tempLeadId ? tempLeadId : null,
          partnerId: partnerId,
          lenderDetails: [{
            lenderId: mongoose.Types.ObjectId("6639fe0491af3654a5813eb8"),
            lenderName: "L&T FINANCE LIMITED"
          }]
        });

        const MasterPayconfig = getMasterPayConfig(checkEligibility?.data?.breMaxEligibilityAmount, 'Under Review');
        let responseFromCallback = await axios(MasterPayconfig);
        logger.logs('response master pay callback', responseFromCallback)
        return res.send({
          status: '200',
          message: 'success'
        })
      } else {

        const MasterPayconfig = getMasterPayConfig(undefined, leadCreation?.data?.errorDesc);
        let responseFromCallback = await axios(MasterPayconfig);
        logger.logs('response master pay callback', responseFromCallback)

        return res.send({
          status: '400',
          message: customerEligible?.data?.errorDesc
        })
      }
    } else {

      const MasterPayconfig = getMasterPayConfig(undefined, leadCreation?.data?.errorDesc);
      let responseFromCallback = await axios(MasterPayconfig);
      logger.logs('response master pay callback', responseFromCallback)
      return res.send({
        status: '400',
        message: leadCreation?.data?.errorDesc
      })
    }
  }

  return res.send({
    status: '400',
    message: 'Please select an appropriate occupation'
  })

});

router.post("/assignPersonalLoan", async (req, res) => {
  try {
    const schema = joi.object({
      loanId: joi.string().required(),
      lenderName: joi.string().required(),
    });

    const { error } = schema.validate(req.body);

    if (error) {
      return res.status(400).send(error.details[0].message);
    }

    logger.logs("data received", req.body);

    const { loanId, lenderName } = req.body;

    const loanInfo = await LoanInfo.findById(loanId);

    if (!loanInfo) {
      return res.status(400).send("LoanInfo is not present for the user");
    }

    const _id = loanInfo.userId;
    const merchantCode = loanInfo.merchantCode;

    const userInfo = await UserInfo.findById(_id);

    if (userInfo) {
      let {
        occupationType,
        userDetails,
        poaDetails,
        addressDetails,
        panNo: pan,
        referrerId,
      } = userInfo;

      const {
        firstName,
        lastName,
        dob,
        loanAmount,
        companyName,
        educationQualification,
        residenceOwnership,
      } = userDetails || {};

      pan = String(pan).toUpperCase();

      const mobileNo = userDetails.mobileNo?.[0]?.mobileNumber;
      const emailId = userDetails.emailId?.[0];
      const adhaar = poaDetails?.[0]?.poaNo;
      const pincode = addressDetails?.[0]?.pincode;

      if (IsNullEmpty(mobileNo)) {
        return res.status(400).send("MobileNo is not present for the user");
      }
      if (IsNullEmpty(emailId)) {
        return res.status(400).send("emailId is not present for the user");
      }
      if (IsNullEmpty(adhaar)) {
        return res.status(400).send("adhaar is not present for the user");
      }
      if (IsNullEmpty(pincode)) {
        return res.status(400).send("pincode is not present for the user");
      }

      function getMasterPayConfig(amount, status) {
        return {
          method: "POST",
          baseURL: `https://m.masterpay.pro/Fundfina_callback`,
          data: {
            merchantCode: merchantCode,
            status: status,
            amount: amount,
            referredId: referrerId,
            firstName,
            lastName,
            mobileNo,
            pan,
            adhaar,
            loanAmount,
            companyName,
            educationQualification,
            residenceOwnership,
            pincode,
          },
        };
      }

      const partnerMaster = await PartnerMaster.findOne({
        partnerCode: { $regex: lenderName, $options: "i" },
      });

      let lenderDetails = {};
      if (partnerMaster) {
        lenderDetails = {
          lenderId: new mongoose.Types.ObjectId(partnerMaster._id),
          lenderName: partnerMaster.partnerName,
        };
      }

      switch (String(lenderName)) {
        case "L&T":
          {
            let filterCompanyCategory = companyCategoryMapping.filter(
              (item) => item.companyName === companyName
            );
            let companyCategory =
              filterCompanyCategory?.[0]?.finalCategory || "Other";

            let params = {
              uid: _id,
              productCategory: "CL",
              sourcingChannel: "CLEAG",
              applicant_first_name: firstName,
              // "applicant_middle_name": middleName && "",
              applicant_last_name: lastName,
              mobileNo: mobileNo,
              panNo: pan,
              email: emailId,
              pincode: pincode,
              employment_type:
                occupationType === "selfemployed"
                  ? "Self Employed"
                  : "Salaried",
              dob: moment(dob).format("DD-MM-YYYY"),
              company_name: companyName,
              education_qualification: educationQualification,
              residence_ownership: residenceOwnership,
              company_category:
                companyCategory === "Other" ? "CAT E" : companyCategory,
            };
            let leadCreation = await new LnTFinance().leadGeneration(params);

            logger.logs("response of leadcreation", leadCreation);
            let tempLeadId = leadCreation?.data?.leadId;
            if (leadCreation?.data?.status === "SUCCESS") {
              let customerEligibleParams = {
                uid: params?.uid,
                productCategory: "CL",
                sourcingChannel: "CLEAG",
              };
              let checkEligibility = await new LnTFinance().customerEligibility(
                customerEligibleParams
              );
              logger.logs("response of customer eligibility", checkEligibility);
              if (checkEligibility?.data?.status === "SUCCESS") {
                await LoanInfo.create({
                  userId: mongoose.Types.ObjectId(params?.uid),
                  merchantCode: merchantCode,
                  status: "Under Review", // TODO: What will be the status here?
                  amount: checkEligibility?.data?.breMaxEligibilityAmount,
                  productType: "PL",
                  leadId: tempLeadId ? tempLeadId : null,
                  lenderDetails: Object.keys(lenderDetails).length
                    ? [lenderDetails]
                    : [],
                });
                if (referrerId) {
                  const MasterPayconfig = getMasterPayConfig(
                    checkEligibility?.data?.breMaxEligibilityAmount,
                    "Under Review"
                  );
                  let responseFromCallback = await axios(MasterPayconfig);
                  logger.logs(
                    "response master pay callback",
                    responseFromCallback
                  );
                }
                return res.status(201).send(checkEligibility?.data?.status);
              } else if (referrerId) {
                const MasterPayconfig = getMasterPayConfig(
                  undefined,
                  leadCreation?.data?.errorDesc
                );
                let responseFromCallback = await axios(MasterPayconfig);
                logger.logs(
                  "response master pay callback",
                  responseFromCallback
                );
              }
            }

            return res
              .status(400)
              .send(leadCreation?.data?.errorDesc || "Error");
          }
          break;
        case "Respo":
          {
            let customerEligible = await new Respo().customerEligibility({
              mobileNumber: mobileNo,
              panNumber: pan,
            });

            logger.logs("customerEligible", customerEligible);
            console.log("test", customerEligible);

            if (
              (customerEligible.data.message == "APPLICATION_ALREADY_EXIST" ||
                customerEligible.data.status == "ACCEPT") &&
              customerEligible.status == 200
            ) {
              let preAprrovalData = {
                mobileNumber: mobileNo,
                email: emailId,
                panNumber: pan,
                name: firstName + " " + lastName,
                dob: moment(dob).format("YYYY-MM-DD"),
                income: 100000,
                employmentType: occupationType,
                bureauType: 3,
              };
              let pushPreApproval = await new Respo().preApprovalOffer(
                preAprrovalData
              );
              logger.logs(
                "pushprreapproval",
                pushPreApproval?.data,
                pushPreApproval?.status
              );
              if (
                pushPreApproval?.data?.status == "ACCEPT" &&
                pushPreApproval?.status == 200
              ) {
                logger.logs("reached here");
                await LoanInfo.create({
                  userId: mongoose.Types.ObjectId(_id),
                  merchantCode: merchantCode,
                  status: "Under Review",
                  amount: pushPreApproval?.data?.offer,
                  productType: "PL",
                  lenderDetails: Object.keys(lenderDetails).length
                    ? [lenderDetails]
                    : [],
                });
                if (referrerId) {
                  const MasterPayconfig = getMasterPayConfig(
                    pushPreApproval?.data?.offer,
                    "Under Review"
                  );
                  let responseFromCallback = await axios(MasterPayconfig);
                  logger.logs(
                    "response master pay callback",
                    responseFromCallback
                  );
                }
                return res
                  .status(201)
                  .send("Loan application successfully generated");
              } else if (
                pushPreApproval?.status == 400 &&
                pushPreApproval?.data?.message ==
                "PRE_APPROVAL_OFFER_ALREADY_GENERATED"
              ) {
                if (referrerId) {
                  const MasterPayconfig = getMasterPayConfig(
                    pushPreApproval?.data?.offer,
                    "Under Review"
                  );
                  let responseFromCallback = await axios(MasterPayconfig);
                  logger.logs(
                    "response master pay callback",
                    responseFromCallback
                  );
                } else {
                  await LoanInfo.updateOne(
                    { _id: loanId },
                    {
                      $set: {
                        amount: pushPreApproval?.data?.offer,
                        status: "Under Review",
                      },
                    }
                  );
                }
                return res.status(409).send("Loan application already exists!");
              }
            } else if (referrerId) {
              const MasterPayconfig = getMasterPayConfig(
                undefined,
                customerEligible.data.message
              );
              let responseFromCallback = await axios(MasterPayconfig);
              logger.logs("response master pay callback", responseFromCallback);
            }

            return res
              .status(400)
              .send(customerEligible?.data?.message || "Error");
          }
          break;

        case "PayMe":
          {
            let userInfo = await OpenMarketUserInfo.findById(_id);

            let {
              firstName,
              lastName,
              mobileNo,
              companyName,
              address,
              pincode,
              panNo,
            } = userInfo;

            try {
              let response = await PayMe.checkUserExistence({
                merchant_id: "98cdbb76-30c2-4bc8-9656-774350eabe8d",
                pan_card_number: panNo,
              });

              if (
                response.message !==
                "User register api stucked into exception!!"
              ) {
                await LoanInfo.create({
                  userId: mongoose.Types.ObjectId(_id),
                  merchantCode: "98cdbb76-30c2-4bc8-9656-774350eabe8d",
                  status: "Under Review",
                  amount: "", // Set appropriate amount if available
                  productType: "PL",
                  leadId: null,
                  lenderDetails: Object.keys(lenderDetails).length
                    ? [lenderDetails]
                    : [],
                });
              } else {
                response = await PayMe.registerUser({
                  email: `${Math.random()
                    .toString(36)
                    .substring(2, 11)}@example.com`,
                  merchant_id: "98cdbb76-30c2-4bc8-9656-774350eabe8d",
                  phone_number: mobileNo,
                  full_name: firstName + " " + lastName,
                });

                if (
                  response.message !==
                  "User register api stucked into exception!!"
                ) {
                  await LoanInfo.create({
                    userId: mongoose.Types.ObjectId(_id),
                    merchantCode: "98cdbb76-30c2-4bc8-9656-774350eabe8d",
                    status: "Under Review",
                    amount: "",
                    productType: "PL",
                    leadId: null,
                    lenderDetails: Object.keys(lenderDetails).length
                      ? [lenderDetails]
                      : [],
                  });
                } else {
                  return res
                    .status(400)
                    .send("An error occurred while assigning to PayMe");
                }
              }

              console.log(
                "checkUserExistence: Checker user merchant, response: ",
                response
              );
            } catch (err) {
              console.log(
                "checkUserExistence: Error while checking user merchant:",
                err.response
              );
              return res.status(500).send("Internal Server Error");
            }
          }
          break;
        default: {
          return res.status(400).send("Please select a lending partner");
        }
      }
    } else {
      // If userInfo does not exists
      return res.status(400).send("UserInfo does not exists");
    }
  } catch (ex) {
    logger.logs(ex);
    saveErrorLog(
      "assignPersonalLoan",
      apiPath,
      req.body,
      ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex
    );
    return res.statusCode(500);
  }
});

router.post("/payme/addPersonalLoanDetails", async (req, res) => {
  const schema = joi.object({
    firstName: joi.string().required(),
    lastName: joi.string().required(),
    merchantCode: joi.allow(),
    emailId: joi.string().required(),
    mobileNo: joi.number().required(),
    adhaar: joi.string().required(),
    pan: joi.string().required(),
    occupation: joi.string().required(),
    dob: joi.string().required(),
    referrerId: joi.allow(),
    companyName: joi.string().required(),
    educationQualification: joi.string().required(),
    residenceOwnership: joi.string().required(),
    pincode: joi.string().required(),
    companyCategory: joi.allow(),
    loanAmount: joi.string().required(),
    partnerId: joi.string().required(),
  });

  const { error } = schema.validate(req.body);
  if (error) {
    return res.status(400).send(error.details[0].message);
  }

  let {
    firstName,
    lastName,
    emailId,
    mobileNo,
    adhaar,
    pan,
    occupation,
    dob,
    merchantCode,
    referrerId,
    companyName,
    educationQualification,
    residenceOwnership,
    pincode,
    loanAmount,
    partnerId,
  } = req.body;

  logger.logs("data received", req.body);

  const user = await UserInfo.findOne({
    $or: [
      { "userDetails.mobileNo.mobileNumber": mobileNo },
      { "userDetails.emailId": emailId },
      { panNo: pan },
    ],
  });

  let createUser;
  if (user) {
    console.log("theuser", user);
    createUser = user;
    firstName = user.userDetails?.firstName || "";
    lastName = user.userDetails?.lastName || "";
    emailId = user.userDetails?.emailId?.[0] || "";
    mobileNo = user.userDetails?.mobileNo?.mobileNumber || "";
    adhaar = user.poaDetails?.[0]?.poaNo || "";
    pan = user.panNo || "";
    occupation = user.occupationType || "";
    dob = user.userDetails?.dob || "";
    companyName = user.userDetails?.companyName || "";
    educationQualification = user.userDetails?.educationQualification || "";
    residenceOwnership = user.userDetails?.residenceOwnership || "";
    pincode = user.addressDetails?.[0]?.pincode || "";
    loanAmount = user.userDetails?.loanAmount || "";
  } else {
    let userData = {
      panNo: pan,
      occupationType: occupation,
      userDetails: {
        firstName: firstName,
        lastName: lastName,
        emailId: emailId,
        mobileNo: [{ mobileNumber: mobileNo }],
        addressDetails: [{ pincode: pincode }],
        companyName: companyName,
        educationQualification: educationQualification,
        residenceOwnership: residenceOwnership,
        loanAmount: loanAmount,
        dob: dob,
      },
      poaDetails: [{ poaNo: adhaar }],
      referrerId: referrerId,
    };
    createUser = await UserInfo.create(userData);
    logger.logs("userCreated", createUser?._id);
  }

  console.log(createUser._id);

  try {
    const paymeRes = await axios.post(
      "https://weedori.paymeindia.in/api/authentication/check_user_merchant/",
      {
        merchant_id: "98cdbb76-30c2-4bc8-9656-774350eabe8d",
        pan_card_number: pan,
      },
      {
        headers: { "Content-Type": "application/json" },
      }
    );

    if (
      paymeRes.data &&
      paymeRes.data.message !== "User register api stucked into exception!!"
    ) {
      try {
        const paymeRegisterRes = await axios.post(
          "https://weedori.paymeindia.in/api/authentication/register_user_merchant/",
          {
            email: emailId,
            merchant_id: "98cdbb76-30c2-4bc8-9656-774350eabe8d",
            phone_number: mobileNo,
            full_name: `${firstName} ${lastName}`,
          },
          {
            headers: { "Content-Type": "application/json" },
          }
        );

        res.send({
          status: "200",
          message: "Successfully registered user",
        });

        //update in our loaninfo DB

        await LoanInfo.create({
          userId: mongoose.Types.ObjectId(params?.uid),
          merchantCode: merchantCode,
          status: "Under Review",
          amount: loanAmount ? loanAmount : 0, //loan amount from body or 0 if not present
          productType: "PL",
          leadId: tempLeadId ? tempLeadId : null,
          partnerId: partnerId,
          lenderDetails: [
            {
              lenderId: "98cdbb76-30c2-4bc8-9656-774350eabe8d",
              lenderName: "PayMe",
            },
          ],
        });
      } catch (error) {
        res.status(500).send({
          status: "500",
          message: `Failed to register user: ${error.message}`,
        });
      }
    } else {
      res.status(403).send({
        status: "403",
        message: `Error checking user merchant: ${paymeRes.data.error}`,
      });
    }
  } catch (error) {
    res.status(500).send({
      status: "500",
      message: `Error checking user merchant: ${error.message}`,
    });
  }
});

router.get("/fetchCompanyList", async (req, res) => {

  const { search } = req.query;
  if (!search) {
    return res.status(400).json({ error: 'Search query is required' });
  }

  const filteredOptions = companyNameData
    .filter(option => typeof option === 'string')
    .filter(option => option.toLowerCase().includes(search.toLowerCase()));

  res.json(filteredOptions);

})


module.exports = router;
