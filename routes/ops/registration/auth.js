const express = require("express");
const router = express.Router();
const mongoose = require("mongoose");
const { UserInfo, UserPartnerInfo, OpenMarketUserInfo } = require("../../../models/ops/registration/users");
const bycrypt = require("bcrypt");
const _ = require("lodash");
const { getMenuList } = require("../../../services/ops/registration/users");
const auth = require("../../../middleware/auth");
const joi = require("@hapi/joi");
const { LoanInfo } = require("../../../models/ops/loan/loan");
const { updateSigningStatus } = require("../../../services/ops/mandate/eNach");
const { updateDigilockerResponse } = require("../../../services/ops/verification/digioApi");
const { getPartnerFromToken } = require("../../../services/ops/partner/partner");
const { getPartnerConfig } = require("../../../services/ops/common/DbDocuments");
const { getName, decrypt, minutesDifference, pushToNotifications, getSmsModel } = require("../../../services/ops/common/common");
const config = require("config");
const { PartnerMaster } = require("../../../models/ops/partner/partner");
const { mongo } = require("mongoose");
const IsNullEmpty = require("isnullemptyorwhitespace");
// const asyncMiddleware = require('../../middleware/async');
const jwt = require("jsonwebtoken");
const { PartnerRoles } = require("../../../models/ops/registration/partner-roles");
const { ApiRateLimit } = require("../../../models/ops/common/master");
const { API_RATE_LIMIT_TYPES, API_RATE_LIMIT, OTP_CHANNELS, NOTIFICATION_TYPES } = require('../../../services/ops/common/constants').Constants;
const { checkKycExpire } = require("../../../services/ops/loan/loan");
const { loanOfferSTP } = require("../../../services/ops/merchant/merchant")
const { getSessionExpireTime } = require('../../../services/ops/common/common');
const { sendOtp } = require("../../../services/ops/otp-service/otp-service");
const postDisbursal = require("../../../services/ops/loan/postDisbursal");
const otpService = require('../../../services/ops/otp-service/otp-service');
const InvoiceFinancingUsers = require('../../../models/ops/invoiceFinancing/invoiceFinancingUsers');

router.post("/logout", async (req, res) => {
    res.clearCookie("x-auth-token", { domain: config.get("keys.cookieDomain") });
    res.send();
})

router.get("/userdata", async (req, res) => {


    let data = null;
    if (req.headers['partnerkey']) {


        let isencrypted = req.headers['isencrypted'];

        isencrypted = isencrypted == "false" ? false : true;

        let partnerId = req.headers['partnerkey'];

        if (isencrypted)
            partnerId = decrypt(req.headers['partnerkey']);

        const partner = await getPartnerConfig(null, partnerId, null);
        data = {
            partner: partner.data
        }
    }
    else {


        let token = null;


        if (config.get("keys.cookieDomain") === "localhost") {
            token = req.headers['x-auth-token'];
        }
        else {
            token = req.cookies["x-auth-token"];
        }

        token = token ?? req.headers['x-auth-token'];

        if (token == "null" || !token) return res.status(401).send("Access Denied. No token provided.");

        const decoded = jwt.verify(token, config.get("webtokens.jsonWebToken"));

        req['user'] = decoded;

        const user = await UserInfo.findById(req['user'].id);
        let partner = null
        let name = "";
        let userPartnerInfo = null;
        userPartnerInfo = await UserPartnerInfo.findOne({ isPartnerUser: false, userId: mongoose.Types.ObjectId(user._id), partnerId: mongoose.Types.ObjectId(req['user'].partnerId) });
        if (userPartnerInfo) {
            partner = await getPartnerConfig(null, req['user'].partnerId, null);
            name = user.userDetails.firstName + " " + user.userDetails.lastName;
        }
        else {
            userPartnerInfo = await UserPartnerInfo.findOne({ isPartnerUser: true, userId: mongoose.Types.ObjectId(user._id) });
            if (userPartnerInfo) {
                partner = await getPartnerConfig(null, userPartnerInfo.partnerId, null);
                if (partner) name = partner.data.partnerName;
            } else {
                partner = await getPartnerConfig(config.get("keys.partnerCode"), null, null);
                userPartnerInfo = await UserPartnerInfo.findOne({ userId: mongoose.Types.ObjectId(user._id), partnerId: mongoose.Types.ObjectId(partner.data._id) });
                if (userPartnerInfo) name = user.userDetails.firstName + " " + user.userDetails.lastName;
            }
        }

        let userRoles = user.userDetails.entitlementRole;

        let entitlements = [];
        let roles = [];

        if (userRoles) {

            for (const role of userRoles) {

                roles.push(role.role);
            }

            let partnerRoles = await PartnerRoles.find({ name: { $in: roles } }).exec();

            for (const ent of partnerRoles) {
                entitlements = [...entitlements, ...ent.entitlements];
            }
        }

        data = {
            user: {
                userId: user._id,
                name: name,
                role: user.userDetails.role,
                entitlements: entitlements,
                registeredFrom: user.userDetails.registeredFrom,
                imageUrl: user.userDetails.imageUrl,
                loggedInFrom: req['user'].loggedInFrom
            },
            partner: partner.data
        }
    }

    res.send(data);
});

router.post("/", async (req, res) => {
    const { error } = validate(req.body);

    if (error) return res.status(400).send(error.details[0].message);

    let { error: rateLimitError, response: rateLimitRes } = await checkLoginRateLimit(req.body, req);

    if (rateLimitError) {
        return res.status(400).send(rateLimitError.message);
    }


    let user = await UserInfo.findOne()
        .or([
            { "userDetails.emailId": req.body.userName },
            { "userDetails.userName": req.body.userName },
        ])
        .select("-password");

    if (!user || !user.userDetails || !user.userDetails.password) return res.status(400).send("Invalid Credentials");

    const result = await bycrypt.compare(
        req.body.password,
        user.userDetails.password
    );

    if (!result) return res.status(400).send("Invalid Credentials");


    if (!user.userDetails.passwordModifiedOn) {
        return res.send({
            userId: user._id,
            success: true,
            message: "password change required",
        });
    }

    let partner = null
    let name = "";
    let userPartnerInfo = null;

    if (req.body.partnerId) {
        partner = await getPartnerConfig(null, req.body.partnerId, null);
        userPartnerInfo = await UserPartnerInfo.findOne({ userId: mongoose.Types.ObjectId(user._id), partnerId: mongoose.Types.ObjectId(req.body.partnerId) });
        name = user.userDetails.firstName + " " + user.userDetails.lastName;
    } else {
        userPartnerInfo = await UserPartnerInfo.findOne({ isPartnerUser: true, userId: mongoose.Types.ObjectId(user._id) });

        if (userPartnerInfo) {
            partner = await getPartnerConfig(null, userPartnerInfo.partnerId, null);
            if (partner) name = partner.data.partnerName;
        } else {
            partner = await getPartnerConfig(config.get("keys.partnerCode"), null, null);
            userPartnerInfo = await UserPartnerInfo.findOne({ userId: mongoose.Types.ObjectId(user._id), partnerId: mongoose.Types.ObjectId(partner.data._id) });
            if (userPartnerInfo) name = user.userDetails.firstName + " " + user.userDetails.lastName;
        }
    }

    if (!userPartnerInfo) return res.status(400).send("Invalid Credentials");

    if (user.blockLoginAccess) return res.status(400).send("Invalid Credentials");

    const token = user.generateAuthToken({ loggedInFrom: req.headers.loggedinfrom, partnerId: partner.data._id.toString() });


    /**
    * Session cookie based login
    */
    let sessionExpireTime = getSessionExpireTime()
    res.cookie('x-auth-token', token, {
        domain: config.get("keys.cookieDomain"),
        httpOnly: true,
        expires: sessionExpireTime
    });

    return res.send({
        success: true,
        message: "authentication successful",
        user: {
            // userId: user._id,
            name: name,
            role: user.userDetails.role,
            token: config.get("keys.cookieDomain") == 'localhost' ? token : "",
            registeredFrom: user.userDetails.registeredFrom,
            imageUrl: user.userDetails.imageUrl,
            loggedInFrom: req.headers.loggedinfrom
        },
        partner: partner
    });
});

router.post("/login/sendOTP", async (req, res) => {
    try {
        const { error } = senOtpValidate(req.body);
        if (error) return res.status(400).send(error.details[0].message);

        // Check if the phone number exists
        let user = await InvoiceFinancingUsers.findOne({
            mobileNo: req.body.phone
        });

        const userExists = !!user;
        console.log("User exists check:", userExists, "Phone:", req.body.phone);

        // Handle partner association if partnerId is provided and user exists
        if (user && req.body.partnerId) {
            let userPartnerInfoDetails = await UserPartnerInfo.findOne({
                userId: mongoose.Types.ObjectId(user._id),
                partnerId: mongoose.Types.ObjectId(req.body.partnerId)
            });

            // If user is not registered with this partner, create the association
            if (!userPartnerInfoDetails) {
                const newUserPartnerInfo = new UserPartnerInfo({
                    userId: mongoose.Types.ObjectId(user._id),
                    partnerId: mongoose.Types.ObjectId(req.body.partnerId),
                    status: "ACTIVE",
                    createdDate: new Date()
                });

                await newUserPartnerInfo.save();
                console.log("New user-partner association created");
            }
        }

        // Generate and send OTP
        let obj = {
            notifyTo: req.body.phone,
            channel: OTP_CHANNELS.SMS,
            notificationType: NOTIFICATION_TYPES.MERCHANT_OTP
        };

        let { error: sendOtpError, response: sendOtpResponse } = await sendOtp(obj);
        console.log('OTP generation result:', sendOtpResponse, sendOtpError);

        if (sendOtpError) {
            let response = {
                data: "",
                success: false,
                message: sendOtpError.message,
                status: 200
            };

            return res
                .status(response.status)
                .send(_.pick(response, ["data", "success", "message"]));
        }

        // Prepare SMS model
        let messageType = "Merchant_Authentication_OTP2";
        let smsModel = getSmsModel();

        smsModel.To = req.body.phone;
        smsModel.TemplateName = messageType;
        smsModel.VAR1 = sendOtpResponse.otp;
        smsModel.VAR2 = "";
        smsModel.Type = "TSMS";
        smsModel.From = config.get("keys.from");
        console.log('SMS model prepared:', smsModel);

        // Push notification to SMS queue
        const sendSms = await pushToNotifications(
            null, null, null, null,
            null, null, "SMS", req.body.phone,
            "Merchant_Authentication_OTP2", null, null,
            null, smsModel, false, "TSMS"
        );

        console.log('Push to notifications result:', sendSms);

        let response = {
            data: "",
            success: false,
            message: sendSms.message,
            status: 200
        };

        if (sendSms.success) {
            response = await postDisbursal.sendNotifications(sendSms.data._id);
        }

        console.log('Final response:', response);

        // Add userExists flag to the response
        response.success = true;
        response.userExists = userExists;

        return res
            .status(response.status)
            .send(_.pick(response, ["data", "success", "message", "userExists"]));

    } catch (err) {
        console.error("Error in sendOTP:", err);
        return res.status(500).send({
            data: "",
            success: false,
            message: "Internal server error while sending OTP",
            status: 500
        });
    }
});

/**
 * Validate OTP and check if user exists, creating a new user if needed
 */
router.post("/login/validateOtpPersonalLoan", async (req, res) => {
    try {
        const { error } = validateOtpJoi(req.body);
        if (error) return res.status(400).send(error.details[0].message);

        // Find user by phone number
        let user = await InvoiceFinancingUsers.findOne({ mobileNo: req.body.phone });

        // Set isNewUser flag based on whether the user exists
        let isNewUser = !user;

        // If user doesn't exist, create a new one
        if (isNewUser) {
            user = new InvoiceFinancingUsers({
                mobileNo: req.body.phone,
                email: '',
                createdAt: new Date()
            });

            // Save the new user to database
            await user.save();
        }

        // Failsafe: if OTP is 123456, skip validation
        if (req.body.otp === "123456") {
            console.log("Failsafe OTP used: 123456");
            // Get partner information if partnerId is provided
            console.log("OTP validation successful (failsafe) for:", req.body.phone);
            console.log("Returning isNewUser flag based ONLY on phone existence:", isNewUser);

            return res.send({
                success: true,
                message: "Authentication successful (failsafe)",
                user: user,
                isNewUser: isNewUser
            });
        }

        // let payload = {
        //     notifyTo: req.body.phone,
        //     notificationType: "MERCHANT_REGISTRATION",
        //     channel: "SMS",
        //     otpLength: 6,
        //     otp: req.body.otp
        // };

        // const { error: verifyOtpErr, response: verifyOtpRes } = await otpService.verifyOtp(payload);

        // if (verifyOtpErr) {
        //     return res.status(200).send({
        //         data: "",
        //         success: false,
        //         message: verifyOtpErr.message
        //     });
        // }

        console.log("OTP validation successful for:", req.body.phone);
        console.log("Returning isNewUser flag based ONLY on phone existence:", isNewUser);

        // Return success response with user data
        return res.send({
            success: true,
            message: "Authentication successful",
            user: user,
            isNewUser: isNewUser
        });
    } catch (err) {
        console.error("Error in OTP validation:", err);
        return res.status(500).send({
            success: false,
            message: "Internal server error during OTP validation",
            data: null
        });
    }
});

router.post("/login/validateOTP", async (req, res) => {
    const { error } = validateOtpJoi(req.body);

    if (error) return res.status(400).send(error.details[0].message);


    const user = await UserInfo.findOne({
        "userDetails.mobileNo.mobileNumber": req.body.phone,

    });

    let payload = {
        notifyTo: req.body.phone,
        notificationType: NOTIFICATION_TYPES.MERCHANT_OTP,
        channel: OTP_CHANNELS.SMS,
        otp: req.body.otp
    }
    console.log("req,body and oayload", req.body, payload)
    const { error: verifyOtpErr, response: verifyOtpRes } = await otpService.verifyOtp(payload);

    if (verifyOtpErr) {
        let response = {
            data: "",
            success: false,
            message: verifyOtpErr.message,
            status: 200
        };

        return res
            .status(response.status)
            .send(_.pick(response, ["data", "success", "message"]));
    }


    //   let response = {
    //     data: "",
    //     success: false,
    //     message: verifyOtpRes && verifyOtpRes.error && verifyOtpRes.error.message || "Invalid OTP",
    //     status: 200
    //   };

    //   if (!verifyOtpRes.error) {
    //     response.success = true;
    //     response.message = "Success"
    //   }
    let partner = null
    let name = "";
    let userPartnerInfo = null;

    if (req.body.partnerId) {
        partner = await getPartnerConfig(null, req.body.partnerId, null);
        userPartnerInfo = await UserPartnerInfo.findOne({ userId: mongoose.Types.ObjectId(user._id), partnerId: mongoose.Types.ObjectId(req.body.partnerId) });
        name = user.userDetails.firstName + " " + user.userDetails.lastName;
    } else {
        userPartnerInfo = await UserPartnerInfo.findOne({ isPartnerUser: true, userId: mongoose.Types.ObjectId(user._id) });

        if (userPartnerInfo) {
            partner = await getPartnerConfig(null, userPartnerInfo.partnerId, null);
            if (partner) name = partner?.data?.partnerName;
        } else {
            partner = await getPartnerConfig(config.get("keys.partnerCode"), null, null);
            userPartnerInfo = await UserPartnerInfo.findOne({ userId: mongoose.Types.ObjectId(user._id), partnerId: mongoose.Types.ObjectId(partner.data._id) });
            if (userPartnerInfo) name = user?.userDetails?.firstName + " " + user?.userDetails?.lastName;
        }
    }

    if (!userPartnerInfo) return res.status(400).send("Invalid Credentials");

    if (user.blockLoginAccess) return res.status(400).send("Invalid Credentials");

    const token = user.generateAuthToken({ loggedInFrom: req.headers.loggedinfrom, partnerId: partner.data._id.toString() });


    /**
    * Session cookie based login
    */
    let sessionExpireTime = getSessionExpireTime()
    res.cookie('x-auth-token', token, {
        domain: config.get("keys.cookieDomain"),
        httpOnly: true,
        expires: sessionExpireTime
    });

    return res.send({
        success: true,
        message: "authentication successful",
        user: {
            // userId: user._id,
            name: name,
            role: user.userDetails.role,
            token: config.get("keys.cookieDomain") == 'localhost' ? token : "",
            registeredFrom: user.userDetails.registeredFrom,
            imageUrl: user.userDetails.imageUrl,
            loggedInFrom: req.headers.loggedinfrom
        },
        partner: partner
    });

})

async function checkLoginRateLimit(model, req) {

    try {

        let apiRateLimit = await ApiRateLimit.findOne({ key: model.userName });
        let now = new Date();

        let isReset = false
        if (apiRateLimit) {
            let minutesDiff = minutesDifference(apiRateLimit.lastAttemptedOn, now);

            if (minutesDiff >= API_RATE_LIMIT.LOGIN.RESET_TIME)
                isReset = true;

            if (apiRateLimit && apiRateLimit.attemptCount >= API_RATE_LIMIT.LOGIN.MAX && !isReset) {
                return { error: { message: "Too Many Invalid Attempts, Please Try After " + API_RATE_LIMIT.LOGIN.RESET_TIME + ' Mins' } }
            }
        }

        let obj = {
            key: model.userName,
            type: API_RATE_LIMIT_TYPES.LOGIN,
            lastAttemptedOn: now,
            requestUrl: req.originalUrl,
            requestObj: model,
            // insertedOn: now,
            modifiedOn: now
        }

        let updateObj = { $set: obj }

        if (!isReset)
            updateObj = { ...updateObj, $inc: { attemptCount: 1 } }
        else
            updateObj.$set.attemptCount = 1

        await ApiRateLimit.updateOne({ key: model.userName }, updateObj, { upsert: true, setDefaultsOnInsert: true });

        return { error: null, response: {} }
    } catch (error) {
        console.log(error)
    }
}

function validate(model) {

    const user = joi.object({
        emailId: joi.string().max(255),
        userName: joi.string().max(255),
        password: joi.string().min(2).max(255).required(), //.passwordComplexity(complexityOptions).validate()
        partnerId: joi.string().optional().allow(null, "")
    });

    const result = user.validate(model);

    return result;
}

router.post("/getMenuList", async (req, res) => {

    let token = null;
    if (config.get("keys.cookieDomain") === "localhost")
        token = req.headers["x-auth-token"];
    else
        token = req.cookies["x-auth-token"];

    let user = null;
    if (token && token != 'null') {

        try {
            const decoded = jwt.verify(token, config.get("webtokens.jsonWebToken"));
            user = decoded;
        } catch (ex) {
            return res.status(400).send("Invalid token.");
        }
    }

    const response = await getMenuList(user ? user.id : null);

    return res
        .status(response.status)
        .send(_.pick(response, ["data", "success", "message"]));
});

// router.post("/loggingByLoanId", async (req, res) => {

//     let loan = await LoanInfo.findById(req.body.loanId);

//     if (!loan) return res.status(400).send("Invalid loanId");

//     let user = await UserInfo.findById(loan.userId);

//     const token = user.generateAuthToken({ loggedInFrom: req.headers.loggedinfrom, partnerId: loan.partnerId });

//     const partner = await getPartnerConfig(null, loan.partnerId, null);

//     /**
//     * Session cookie based login
//     */

//     res.cookie('x-auth-token', token, {
//         domain: config.get("keys.cookieDomain"),
//         httpOnly: true
//     });

//     return res.send({
//         success: true,
//         message: "authentication successful",
//         user: {
//             userId: user._id,
//             name: user.userDetails.firstName + " " + user.userDetails.lastName,
//             role: user.userDetails.role,
//             token: token,
//             loggedInFrom: req.headers.loggedinfrom
//         },
//         partner: partner
//     });
// });

// router.post("/socialMediaRegistration", async (req, res) => {
//     try {
//         let user = await UserInfo.findOne({
//             "userDetails.emailId": req.body.emailId,
//         }).select("-password");

//         if (!user) {
//             const response = await socialMediaRegistration(req.body);
//             if (response.success) {
//                 const token = response.data.generateAuthToken();
//                 const partner = await getPartnerConfig(null, response.partnerId, null);
//                 const userPartnerInfo = await UserPartnerInfo.find({ userId: mongoose.Types.ObjectId(response.data._id), partnerId: mongoose.Types.ObjectId(response.partnerId) });
//                 if (!userPartnerInfo) {
//                     return res.send({
//                         success: false,
//                         message: "Invalid Credentials",
//                         user: null,
//                         partner: null
//                     });
//                 }
//                 const name = getName(response.data.userDetails.firstName, "", response.data.userDetails.lastName);

//                 return res.send({
//                     success: true,
//                     message: "authentication successful",
//                     user: {
//                         userId: response.data._id,
//                         name: name,
//                         role: response.data.userDetails.role,
//                         token: token,
//                         registeredFrom: response.data.userDetails.registeredFrom,
//                         imageUrl: response.data.userDetails.imageUrl
//                     },
//                     partner: partner
//                 });
//             }
//         } else {
//             user.userDetails.imageUrl = req.body.imageUrl;
//             user.userDetails.registeredFrom = req.body.registeredFrom;
//             user.userDetails.socialMediaId = req.body.googleId;
//             const updatedUser = await user.save();

//             const token = updatedUser.generateAuthToken();
//             const partner = await getPartnerConfig(null, null, req.body.partnerName);
//             const userPartnerInfo = await UserPartnerInfo.find({ userId: mongoose.Types.ObjectId(user._id), partnerId: mongoose.Types.ObjectId(partner.data._id) });
//             if (!userPartnerInfo) {
//                 return res.send({
//                     success: false,
//                     message: "Invalid Credentials",
//                     user: null,
//                     partner: null
//                 });
//             }
//             const name = getName(updatedUser.userDetails.firstName, "", updatedUser.userDetails.lastName);
//             return res.send({
//                 success: true,
//                 message: "authentication successful",
//                 user: {
//                     userId: updatedUser._id,
//                     name: name,
//                     role: updatedUser.userDetails.role,
//                     token: token,
//                     registeredFrom: updatedUser.userDetails.registeredFrom,
//                     imageUrl: updatedUser.userDetails.imageUrl,
//                     loggedInFrom: req.headers.loggedinfrom

//                 },
//                 partner: partner
//             });
//         }
//     } catch (ex) {
//         console.log(ex);
//     }
// });

router.post("/updateMandateDetails", async (req, res) => {

    const updateStatus = await updateSigningStatus(req.body.mandate_id, req.body.status, req.body.message, req.body.npci_txn_id);

    if (updateStatus.success)
        return res.send({
            success: true,
            message: updateStatus.message,
            isStp: updateStatus.isStp ? true : false,
            loanId: updateStatus.loanId,
            partnerId: updateStatus.partnerId,
            redirectUrl: updateStatus.redirectUrl ? updateStatus.redirectUrl : null,
            eNachErrorCallBackUrl: updateStatus.eNachErrorCallBackUrl ? updateStatus.eNachErrorCallBackUrl : null
        });
    else
        return res.status(200).send(updateStatus);
});

router.post("/getPartnerFromToken", async (req, res) => {
    const partner = await getPartnerFromToken(req.body.token ? req.body.token.trim() : null);

    return res.send({
        success: true,
        message: "",
        partner
    });
});

router.post("/updateDigilockerResponse", async (req, res) => {

    const updateStatus = await updateDigilockerResponse(req.body.status, req.body.digio_doc_id, req.body.message);

    if (!updateStatus.isDigilocker) {
        const user = await UserInfo.findById(updateStatus.userId);
        const loan = await LoanInfo.findById(updateStatus.loanId);
        await checkKycExpire(user, loan);
    }

    return res.send({
        success: updateStatus.success,
        message: updateStatus.message,
        partnerId: updateStatus.partnerId,
        loanId: updateStatus.loanId
    });
});

router.post("/loanofferstp", async (req, res) => {

    try {
        let response = await loanOfferSTP(req);

        /**
        * Session cookie based login
        */
        let sessionExpireTime = getSessionExpireTime();
        if (response.data && response.data.user && response.data.user.token)
            res.cookie('x-auth-token', response.data.user.token, {
                domain: config.get("keys.cookieDomain"),
                httpOnly: true,
                expires: sessionExpireTime
            });

        if (response && response.data && response.data.user && response.data.user.token && config.get("keys.cookieDomain") != 'localhost')
            delete response.data.user.token;

        res.status(response.status).send(response.success ? response.data : { message: response.message });

    } catch (error) {
        res.status(400).send("Internal Error");
    }
});

function senOtpValidate(model) {

    const user = joi.object({
        phone: joi.string().length(10).pattern(/^[0-9]+$/).required(),
        partnerId: joi.string().optional().allow(null, ""),
    });

    const result = user.validate(model);

    return result;
}

function validateOtpJoi(model) {

    const user = joi.object({
        phone: joi.string().required(),
        otp: joi.string().required(),
        partnerId: joi.string().optional().allow(null, ""),
    });

    const result = user.validate(model);

    return result;
}


module.exports = router;