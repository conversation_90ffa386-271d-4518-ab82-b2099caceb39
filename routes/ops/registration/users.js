const auth = require("../../../middleware/auth");
const express = require("express");
const router = express.Router();
const mongoose = require("mongoose");
const {
  UserInfo,
  validate,
  validateQuestionnairre,
  validatePassword,
} = require("../../../models/ops/registration/users");
const _ = require("lodash");
const admin = require("../../../middleware/admin");
const {
  updateQuestionnairre,
  updateUser,
  insertUser,
  deleteUser,
  insertUpdateKycFromPlatform,
  getUser,
  updatePassword,
  updateDetails,
  uploadProfilePic,
  insertUpdatePartnerKYC,
  getRepaymentDetails,
  getPortFolioDetails,
  getLenderPortFolioDetailsV2,
  getPortFolioDetailsForBorrowingPartnerV2,
  getBorrowingPartnerPortFolioDetailsForDownload,
  updateShopDetails,
  bulkUpdateKycStatus,
  updatePanDocVerification,
  updateAadhaarDocVerification
} = require("../../../services/ops/registration/users");
const { verifyOtp } = require("../../../services/ops/otp-service/otp-service");
const { OTP_CHANNELS, NOTIFICATION_TYPES, ROLES } = require('../../../services/ops/common/constants').Constants;
const { checkPassword } = require('../../../services/ops/common/common');


router.get("/me", [auth], async (req, res) => {
  const user = await UserInfo.findById(req.user.id).select("-password");

  if (!user) return res.status(501).send("User not found");
  return res.send(user);
});

// router.get("/", async (req, res, next) => {
//   const users = await UserInfo.find().sort("firstName");
//   return res.send(users);
// });

router.get("/:id/:isPartnerUser?/:partnerId?/:merchantCode?", [auth], async (req, res) => {

  if (req.user.role != ROLES.ADMIN) {
    if (req.params.id && req.params.id != req.user.id && req.user.role == ROLES.USER) {
      return res
        .status(400)
        .send("Access Denied");
    }

    if (req.params.partnerId && req.params.partnerId != req.user.partnerId && req.user.role == ROLES.BORROWING_PARTNER) {
      return res
        .status(400)
        .send("Access Denied");
    }

    if (req.user.role == ROLES.LENDING_PARTNER && req.params.partnerId && req.params.partnerId != req.user.partnerId) {
      return res
        .status(400)
        .send("Access Denied");
    }

    // if(req.user.role == ROLES.USER && req.params.merchantCode != req.user.merchantCode){
    //   return res
    //   .status(400)
    //   .send("Access Denied");
    // }
  }

  const response = await getUser(req.params.id, req.params.isPartnerUser, req.params.partnerId, req.params.merchantCode);
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/", async (req, res) => {
  const { error } = validate(req.body);

  if (error) return res.status(400).send(error.details[0].message);

  let isValid = checkPassword(req.body.password);

  if (!isValid)
    return res.status(400).send("Invalid Password");

  const response = await insertUser(req.body);

  //Send token as a response header
  //const token = jwt.sign({ id: user._id, email: user.email },  config.get('webtokens.jsonWebToken'));
  //const token = user.generateAuthToken();
  //return res.headers('x-auth-token', token).send(_.pick(result, [ '_id', 'firstName', 'middleName', 'lastName', 'email' ]));

  return res.status(response.status).send(response);
});

router.put("/updatePassword", async (req, res) => {
  const { error } = validatePassword(req.body);

  if (error) return res.status(400).send(error.details[0].message);

  let isValid = checkPassword(req.body.password);

  if (!isValid)
    return res.status(400).send("Invalid Password");

  const response = await updatePassword(req.params.id, req.body.password, req.body);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});


router.put("/insertUpdatePartnerKyc", [auth], async (req, res) => {
  // const response = await insertUpdatePartnerKYC(req.body, req.params.id);

  const response = await insertUpdatePartnerKYC(req.body, req.user.id);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.put("/:id", [auth], async (req, res) => {
  const { error } = validate(req.body);

  if (error) return res.status(400).send(error.details[0].message);

  const result = await updateUser(req.body, req.params.id);

  return res.status(result.status).send(result);
});

router.delete("/:id", [auth], async (req, res) => {
  const response = await deleteUser(req.params.id);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.put("/insertUpdateQuestionnairre/:id", [auth], async (req, res) => {
  const { error } = validateQuestionnairre(req.body);

  if (error) return res.status(400).send(error.details[0].message);

  /** ROLE CHECK */
  if (req.params.id && req.params.id != req.user.id) {
    return res.status(400).send(error.details[0].message);
  }

  const response = await updateQuestionnairre(req.body, req.params.id);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.put("/insertUpdateKyc/:id", [auth], async (req, res) => {
  // const { error } = validateKYC(req.body);

  // console.log(error);

  // if(error) return res.status(400).send(error.details[0].message);

  if (req.params.id && req.params.id != req.user.id && req.user.role == ROLES.USER) {
    return res
      .status(400)
      .send("Access Denied");
  }
  
  const response = await insertUpdateKycFromPlatform(req.body, req.params.id);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/updateDetails", [auth], async (req, res) => {
  const response = await updateDetails(
    req.user.id,
    req.body.value,
    req.body.type
  );

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/updateMobileNo", [auth], async (req, res) => {

  if (req.body.mobileNo.length != 10 || isNaN(req.body.mobileNo))
    return res.status(400).send("Invalid Mobile Number");


  if (!req.body.otp)
    return res.status(400).send("Otp Missing");

  const user = await UserInfo.findOne({
    "userDetails.mobileNo.mobileNumber": req.body.mobileNo, //mobile number change
  });

  if (!user) {

    let obj = {
      notifyTo: req.body.mobileNo,
      channel: OTP_CHANNELS.SMS,
      notificationType: NOTIFICATION_TYPES.MY_PROFILE,
      otp: req.body.otp
    }
    let { error: sendOtpError, response: sendOtpResponse } = await verifyOtp(obj);

    if (sendOtpError) {
      return res
        .status(400)
        .send({ success: false, message: sendOtpError.message });
    }

    const response = await updateDetails(
      req.user.id,
      req.body.mobileNo,
      "mobile"
    );

    return res
      .send(_.pick(response, ["data", "success", "message"]));
  } else {
    return res
      .status(400)
      .send({ success: false, message: "Please Enter Valid Mobile" });
  }
});

router.post("/uploadProfilePic", [auth], async (req, res) => {
  const response = await uploadProfilePic(
    req.user.id,
    req.body.fileName,
    req.body.fileStream
  );

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

// router.post('/getClientCode/:id', async(req,res)=>{
//     const response = await testClientCode(req.params.id);
//     //console.log(response);
//     return res.status(response.status).send(_.pick(response, ['data', 'success', 'message']));
// })

router.post("/getPortFolioDetailsForBorrowingPartner", [auth], async (req, res) => {

  req.body.userId = req.user.id;
  const response = await getPortFolioDetailsForBorrowingPartnerV2(req.body);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message", "totalPages"]));
}
);

router.post("/downloadBorrowingPartnerPortFolioDetails", [auth], async (req, res) => {

  req.body.userId = req.user.id;
  const response = await getBorrowingPartnerPortFolioDetailsForDownload(req.body);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
}
);

router.post("/getRepaymentDetails", [auth], async (req, res) => {
  const response = await getRepaymentDetails(req.body, req);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/getPortFolioSummary", [auth], async (req, res) => {
  const response = await getPortFolioDetails(req.body);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/getPortFolioDetails", [auth], async (req, res) => {

  req.body.userId = req.user.id
  const response = await getLenderPortFolioDetailsV2(req.body);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message", "totalPages"]));
});

router.post("/updateShopDetails", [auth], async (req, res) => {

  /** ROLE CHECK */
  if (req.body.userId && req.user.role == ROLES.USER && req.user.id != req.body.userId) {
    return res
      .status(400)
      .send("Access Denied");
  }
  const response = await updateShopDetails(req.body);
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/bulkUpdateKycStatus", async (req, res) => {
  const response = await bulkUpdateKycStatus();
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/bulkUpdatePanDocVerification", async (req, res) => {
  const response = await updatePanDocVerification();

  return res
    .status(200)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/bulkUpdatePoaDocVerification", async (req, res) => {
  const response = await updateAadhaarDocVerification();

  return res
    .status(200)
    .send(_.pick(response, ["data", "success", "message"]));
});

module.exports = router;
