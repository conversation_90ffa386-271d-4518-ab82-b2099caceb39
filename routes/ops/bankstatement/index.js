const auth = require('../../../middleware/auth');
const express = require('express');
const Bankstatement = require('../../../services/ops/bankstatement');
const _ = require("lodash");
const { BankStatementAnalysis } = require("../../../models/ops/bank-statement/BankStatementAnalysis");
const router = express.Router();
const path = require('path');
const multer = require('multer');
const mongoose = require("mongoose");
const { spawn } = require('child_process');
const { PDFS3LocationURL } = require('../../../models/ops/bank-statement/PDFS3LocationURL');



//bankstatement getdataanalysis                                                                                                                     
router.get("/getDataAnalysis/:collection_id", [auth], async (req, res) => {
    const collectionId = req.params.collection_id;
    try {
        let response = await Bankstatement.getDataAnalysis(collectionId, res)
        if (response.status === 404) {
            return res.status(404).json({ error: "No analysis found in the database" });
        }
        else {
            return res.status(200).json(response);
        }
    } catch (error) {
        console.error("Error occurred while fetching data analysis:", error);
        return res.status(500).json({ error: "Internal Server Error" });
    }
});



//create analysis bank statement
const storage = multer.diskStorage({
    destination: path.join(__dirname, 'uploads'),
    filename: (req, file, cb) => {
        cb(null, file.originalname);
    },
});

const upload = multer({ storage });

router.post('/createNewAnalysis', upload.single('pdfFile'), [auth], async (req, res) => {
    try {
        const userId = req.body.userId;
        // const userId = "asdafadqefaerfq"
        console.log("userid upda-", userId)
        const pdfFile = req.file;
        if (!pdfFile) {
            return res.status(400).json({ error: 'No file uploaded' });
        }
        let response = await Bankstatement.createNewAnalysis(pdfFile, userId, res)
        //return res.status(200).json(response);


    } catch (error) {
        console.error('Error in createNewAnalysis route:', error);
        return res.status(500).send('Internal Server Error');
    }
});



router.post('/Bankstatementwebhook', async (req, res) => {
    try {
        const analysisData = req.body;
        let existingDocument = await BankStatementAnalysis.findOne({ collectionID: analysisData.id });
        if (existingDocument) {
            console.log("Already added into Database");
        } else {
            const id = analysisData.id;
            const status = analysisData.status;
            const files = analysisData.files;
            const metrics = analysisData.metrics;
            const name = analysisData.name;
            const newDocument = {
                collectionID: id,
                status: status,
                files: files,
                metrics: metrics,
                name: name
            };
            const result = await BankStatementAnalysis.create(newDocument);
            res.status(200).send('successfully received');
        }
    } catch (error) {
        console.error('Error in Bankstatementwebhook :', error);
        res.status(500).send('Internal Server Error');
    }
});


router.get("/bankStatementBasedCreditLimit/:userId", async (req, res) => {


    let resData = ""
    let pythonFilePath = 'routes/ops/bankstatement/pythonScript/bankStatementCreditLimit.py';
    let getCollectionId = await PDFS3LocationURL.findOne({ userId: req.params.userId });
    let bankStatementData = await BankStatementAnalysis.aggregate([{
        $match: {
            collectionID: getCollectionId.collectionID
        }
    },
    { $unwind: '$metrics' },
    {
        $project: {
            "Name": { $arrayElemAt: ["$files.statement.account_details.cust_name", 0] },
            "BankStatementEndDate": { $arrayElemAt: ["$files.statement.end_date", 0] },
            "Metric": "$metrics.metric.name",
            "Value": "$metrics.metric.value.val",
            "Value_type": "$metrics.metric.value.value_type",
            "StartDate": "$metrics.metric.start_date",
            "EndDate": "$metrics.metric.end_date"
        }
    }]);


    let py = spawn('/opt/homebrew/opt/python@3.13/bin/python3', [pythonFilePath]),
        data = bankStatementData,
        dataString = '';


    let reqData = JSON.stringify(data);


    py.stdin.write(reqData);

    py.stdin.end();

    py.stdout.on('data', function (data) {
        dataString += data.toString();

        //  console.log('data sring here', dataString)
    });

    py.stdout.on('close', function () {
        console.log('data string here', dataString)
        resData = JSON.stringify(dataString);
        console.log("resData replace", resData)
        resData = dataString.replace(/\r|\n/g, '');

        return res
            .status(200)
            .send({
                creditLimit: resData,
                message: 'Credit Limit based on Bank Statement fetched successfully'
            });

    });

    py.stderr.on('error', (err) => {

        console.log('errr herer', err.toString())
        return res
            .status(400)
            .send({
                creditLimit: err.toString(),
                message: 'Unable to fetch the Credit Limit based on Bank Statement'
            });

    });



})

router.get("/fetchBankStatement/:userId", [auth], async (req, res) => {
    try {
        let getCollectionId = await PDFS3LocationURL.findOne({ userId: req.params.userId });
        let bankStatementData = await BankStatementAnalysis.findOne({
            collectionID: getCollectionId.collectionID
        })
        if (bankStatementData) {
            let monthAbbreviations = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
            let bankInferanceDetails = {};
            let monthWiseData = {};
            if (bankStatementData?.metrics) {
                let metrics = bankStatementData?.metrics;
                for (let i = 0; i < metrics.length; i++) {
                    let e = metrics[i];
                    if (e?.metric?.name === "debits_amount") {
                        bankInferanceDetails = { ...bankInferanceDetails, totalDebitTransactionsVolume: e?.metric?.value?.val }
                    }
                    else if (e?.metric?.name === "avg_monthly_credit_amount") {
                        bankInferanceDetails = { ...bankInferanceDetails, averageMonthlyBalanceAmount: e?.metric?.value?.val }
                    }
                    else if (e?.metric?.name === "credits_amount") {
                        bankInferanceDetails = { ...bankInferanceDetails, totalCreditTransactionsVolume: e?.metric?.value?.val }
                    }
                    else if (e?.metric && ["average_balance_monthwise", "credits_amount_monthwise", "debits_amount_monthwise"].includes(e?.metric?.name) && e?.metric?.start_date) {
                        let dateArray = e?.metric?.start_date.split("-");
                        let year = dateArray[0];
                        let month = dateArray[1];
                        let key = month + "_" + year;
                        if (!monthWiseData[key]) {
                            monthWiseData[key] = { "month": monthAbbreviations[parseInt(month)], year: year };
                        }
                        monthWiseData[key][e?.metric?.name] = e?.metric?.value?.val;
                    }
                }
            }
            return res
                .status(200)
                .send({
                    bankStatementData: bankStatementData,
                    monthWiseData: monthWiseData,
                    bankInferanceDetails: bankInferanceDetails,
                    message: 'Bank Statement fetched successfully.',
                    success: true,
                });
        }
        else {
            return res
                .status(200)
                .send({
                    bankStatementData: "",
                    monthWiseData: {},
                    bankInferanceDetails: {},
                    message: 'No Bank Statement found for this userId.',
                    success: false,
                });
        }
    } catch (err) {
        return res
            .status(400)
            .send({
                bankStatementData: "",
                message: 'Unable to fecth details',
                success: false,
            });
    }
})

module.exports = router;