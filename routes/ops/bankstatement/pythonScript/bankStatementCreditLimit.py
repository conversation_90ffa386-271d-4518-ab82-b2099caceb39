import pandas as pd
import numpy as np
from dateutil.relativedelta import relativedelta
import math
from datetime import date
import sys

def Income_Volatility_Weight(vol):
    if vol <= 1.5:
        return 1.75
    elif (vol>1.5)&(vol<=2.5):
        return 1.5
    elif (vol>2.5)&(vol<=4.5):
        return 1
    elif (vol>4.5)&(vol<=6.5):
        return 0.5
    elif vol>6.5:
        return 0
    

def Credit_to_Debit_Ratio_weight(ratio):
    if ratio < 0.8:
        return 0.5
    elif (ratio>=0.8)&(ratio<=1.8):
        return 1
    elif ratio>1.8:
        return 0.5
    

def Failed_transactions_weight(transactions):
    if transactions == 0:
        return 1.25
    elif (transactions>=1)&(transactions<=3):
        return 1
    elif transactions>3:
        return 0.5
    
def credit_limit(data):
     # print('data13', data)
    data = pd.read_json(data)
    # print('data135', data)
    data.drop(data[data['Metric'].isna()].index,axis=0,inplace=True)
    CurrentDate=date.today()
    BankStatement_previous_6_months=(pd.to_datetime(CurrentDate) - pd.DateOffset(months=6)).date().strftime('%Y-%m-%d')
    data['StartDate']=pd.to_datetime(data['StartDate'],format='%Y-%m-%d %H:%M:%S').dt.strftime('%Y-%m-%d')
    data=data[data['StartDate']>=BankStatement_previous_6_months].copy()
    
    #Check if 4 months data exist. If it does, credit limit is generated, else the limit is 0
    
    if (len(data[data['Metric']=='debits_monthwise']['StartDate'].values)<4) and (data[data['Metric']=='debits_monthwise']['Value'].ne(0).sum(axis=0)<4):
        print(0)
        return 0
    else:
        #Income
        Avg_Fixed_Income=np.mean(data[data['Metric']=='salary_credit_amount_monthwise']['Value'])
        Avg_Credit=np.mean(data[data['Metric']=='credits_amount_monthwise']['Value'])
        
        if Avg_Fixed_Income>0:
            Income=Avg_Fixed_Income
        else:
            Income=Avg_Credit
        
        #Income Volatility Weight
        Income_Volatility=np.std(data[data['Metric']=='credits_amount_monthwise']['Value'])/np.mean(data[data['Metric']=='credits_amount_monthwise']['Value'])
        Income_volatility_Weight=Income_Volatility_Weight(Income_Volatility)
        
        #Credit Debit Ratio Weight
        Credit_Debit_Ratio=np.mean(data[data['Metric']=='credits_amount_monthwise']['Value'])/np.mean(data[data['Metric']=='debits_amount_monthwise']['Value'])
        if math.isnan(Credit_Debit_Ratio):
            Credit_Debit_Ratio=0
            
        Credit_Debit_Ratio_Weight=Credit_to_Debit_Ratio_weight(Credit_Debit_Ratio)
        
        #Failed Transactions Weight
        Failed_transactions=np.sum(data[data['Metric']=='cheque_bounced_monthwise']['Value']) + np.sum(data[data['Metric']=='emi_bounce_monthwise']['Value'])
        
        if math.isnan(Failed_transactions):
            Failed_transactions=0
        #print("Failed_transactions: ",Failed_transactions)
        #Failed_transactions=4
        Failed_Transactions_Weight=Failed_transactions_weight(Failed_transactions)
        Credit_Limit = round(Income * 1.5 * (Income_volatility_Weight * Credit_Debit_Ratio_Weight * Failed_Transactions_Weight),-3)
        print(Credit_Limit)
        return(Credit_Limit)


lines = sys.stdin
# print("herer",lines)


credit_limit(lines)