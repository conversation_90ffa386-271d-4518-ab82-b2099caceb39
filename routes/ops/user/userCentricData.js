const express = require("express");
const router = express.Router();
const _ = require('lodash');
const { validateMerchantPartnerMapping, checkIfShopDataExists, saveMerchantShopTypeData } = require("../../../services/ops/user/userCentricData");
const {
    manualUpdateUpiPaymentRecords,
    getExistingManualPaymentUpdateRecords,
  } = require("../../../services/ops/datacleaning/datacleaning");

  
  /// ------ ===== Routes ===== ----- \\\
  router.post("/getExistingManualPaymentUpdateRecords", async (req, res) => {
    const response = await getExistingManualPaymentUpdateRecords(req.body);
    return res
      .status(response.status)
      .send(_.pick(response, ["success", "message", "data"]));
  });
  router.post("/manualUpdateUpiPaymentRecords", async (req, res) => {
    const response = await manualUpdateUpiPaymentRecords(req.body);
    return res
      .status(response.status)
      .send(_.pick(response, ["success", "message"]));
  });

// router.post("/validateMerchantPartnerMapping", async (req, res) => {
//     const response = await validateMerchantPartnerMapping(req.body);

//     return res
//         .status(response.status)
//         .send(_.pick(response, ["success", "message"]));
// });

router.post("/checkIfShopDataExists", async (req, res) => {
    const response = await checkIfShopDataExists(req.body);

    return res
        .status(response.status)
        .send(_.pick(response, ["success", "message"]));
});

router.post("/saveMerchantShopTypeData", async (req, res) => {
    const response = await saveMerchantShopTypeData(req.body);

    return res
        .status(response.status)
        .send(_.pick(response, ["success", "message"]));
});

module.exports = router;