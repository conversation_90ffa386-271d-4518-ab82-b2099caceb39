const auth = require("../../../middleware/auth");
const express = require("express");
const router = express.Router();
//const mongoose = require("mongoose");
const _ = require("lodash");
const IsNullEmpty = require("isnullemptyorwhitespace");
//const admin = require("../../../middleware/admin");
const { encrypt,  decrypt, getCurrentTimeStamp } = require("../../../services/ops/common/common");

const { insertNewPartner, isValidPartnerName, getLoanStatus, registerPartner, checkPartnerAccess, saveAccessDetails, getPartner, getBorrowingPartnerList, getLoanApplicationsForBorrowingPartnerV2, downloadLoanApplicationsForBorrowingPartnerV2, sendReviewLink, contactDetails } = require("../../../services/ops/partner/partner");

const { validatePartner, validateGetLoanStatus } = require("../../../models/ops/partner/partner");

const { validateRequestLoan, validateLoanRequest, validateBorrowerLoanAcceptance, validateGetUserDetails, PartnerMaster, validateRegisterPartner, accessDetailsModel, validateSaveChildLoan, validateCreateOrder, validateConfirmLoanRequest, validateGenerateOTPReq, validateValidateOTPReq, validateOfferDetailsReq, validateUpdateEmiCollectionsReq
} = require("../../../models/ops/partner/partner");

const { saveEkoLoanRequest, saveLoanRequest, saveBorrowerLoanAcceptance, } = require("../../../services/ops/partner/eko");
const { getUserDetails } = require("../../../services/ops/partner/payOne");
const { saveChildLoan } = require("../../../services/ops/partner/lineOfCredit");
const { getRejectReasonList } = require("../../../services/ops/admin/admin");
const { getLoanDetails, callLendingPartnerAPI, confirmLoanRequest } = require("../../../services/ops/partner/lendingPartner");
const { getCreditLoanDetails, generateOTP, validateOTP, getOfferDetails } = require("../../../services/ops/partner/borrowingPartner");
//const { model } = require("mongoose");
const { createOrder } = require("../../../services/ops/partner/lineOfCredit");
const { updateRepayments } = require("../../../services/ops/loan/postDisbursal");

const {
  settlementMISStage
} = require("../../../services/ops/settlement/settlement");
const { checkPassword } = require('../../../services/ops/common/common');


router.post("/", async (req, res) => {
  const { error } = validatePartner(req.body);

  if (error) return res.status(400).send(error.details[0].message);

  let isValid = checkPassword(req.body.password);

  if (!isValid)
    return res.status(400).send("Invalid Password");

  const response = await insertNewPartner(req.body);

  return res.status(response.status).send(response);
});

router.get("/isValidPartnerName/:partnerName", async (req, res) => {
  const response = await isValidPartnerName(req.params.partnerName);

  return res.status(response.status).send(response);
});

router.post("/requestloan", async (req, res) => {
  const token = req.header("auth-key");
  const model = new accessDetailsModel();
  model.api = "requestloan";
  model.publicIP = await publicIp.v4();
  model.authKey = req.headers["auth-key"];
  model.req = req.body ? req.body : null;
  model.isSuccess = false;
  const start = new Date();

  if (!IsNullEmpty(model.authKey)) {
    partner = await PartnerMaster.findOne({
      authKey: model.authKey,
      partnerType: "Borrowing Partner"
    });

    if (!IsNullEmpty(partner)) {
      model.partnerId = partner._id;
      if (partner.partnerCode.toLowerCase() == "eko") {
        const { error } = validateRequestLoan(req.body);

        if (error) {
          model.errorMsg = error.details[0].message;
          model.responseTime = new Date() - start;
          saveAccessDetails(model);

          return res.status(400).send({
            statusCode: 200,
            product_id: null,
            Success: "HTTP 200 OK",
            Status: 0,
            Description: error.details[0].message
          });
        }
        const response = await saveEkoLoanRequest(req.body);

        if (response.Status == 0) { model.errorMsg = response.Description; } else { model.errorMsg = null; }

        model.isSuccess = response.Status == 1 ? true : false;
        model.responseTime = new Date() - start;
        saveAccessDetails(model);

        return res
          .status(response.statusCode)
          .send(_.pick(response, ["product_id", "Success", "Status", "Description"]));
      } else {
        const { error } = validateLoanRequest(req.body);

        if (error) {
          model.errorMsg = error.details[0].message;
          model.responseTime = new Date() - start;
          saveAccessDetails(model);

          return res.status(400).send({
            statusCode: 200,
            product_id: null,
            Success: "HTTP 200 OK",
            Status: 0,
            Description: error.details[0].message
          });
        }

        const response = await saveLoanRequest(req.body, token);

        if (response.Status == 0) { model.errorMsg = response.Description; } else { model.errorMsg = null; }

        model.isSuccess = response.Status == 1 ? true : false;
        model.responseTime = new Date() - start;
        saveAccessDetails(model);

        return res
          .status(response.statusCode)
          .send(_.pick(response, ["product_id", "Success", "Status", "Description", "nachURL", "loanNo"]));
      }
    } else {
      const response = {
        statusCode: 200,
        product_id: null,
        Success: "HTTP 200 OK",
        Status: 0,
        Description: "Invalid auth-key",
        nachURL: null,
        loanNo: null
      };
      return res
        .status(response.statusCode)
        .send(_.pick(response, ["product_id", "Success", "Status", "Description", "nachURL", "loanNo"]));
    }
  } else {
    const response = {
      statusCode: 200,
      product_id: null,
      Success: "HTTP 200 OK",
      Status: 0,
      Description: "Invalid auth-key",
    };
    return res
      .status(response.statusCode)
      .send(_.pick(response, ["product_id", "Success", "Status", "Description", "nachURL"]));
    // const { error } = validateRequestLoan(req.body);
    // if (error) return res.status(400).send(error.details[0].message);
    // const response = await saveEkoLoanRequest(req.body);

    // return res
    //   .status(response.statusCode)
    //   .send(_.pick(response, ["product_id", "Success", "Status", "Description"]));
  }
});

router.post("/accept", async (req, res) => {
  const model = new accessDetailsModel();
  model.api = "accept";
  model.publicIP = await publicIp.v4();
  model.authKey = req.headers["auth-key"] ? req.headers["auth-key"] : "";
  const start = new Date();
  model.req = req.body ? req.body : null;
  model.isSuccess = false;

  const { error } = validateBorrowerLoanAcceptance(req.body);
  if (error) {
    model.errorMsg = error.details[0].message;
    model.responseTime = new Date() - start;
    saveAccessDetails(model);
    return res.status(400).send(error.details[0].message);
  }

  let response = await checkPartnerAccess(model.authKey, "Borrowing Partner");

  if (!response.success) {
    model.errorMsg = response.message;
    model.responseTime = new Date() - start;
    saveAccessDetails(model);
    const responseModel = {
      statusCode: response.status,
      status: response.success,
      description: response.message
    }
    return res.status(responseModel.statusCode).send(_.pick(responseModel, ["status", "description"]));
  } else {
    model.partnerId = response.partnerId;
  }

  response = await saveBorrowerLoanAcceptance(req.body, model.partnerId);

  if (response.status == 0) {
    model.errorMsg = response.description;
  } else {
    model.req = null;
    model.isSuccess = true;
  }
  model.isSuccess = response.status == 1 ? true : false;
  model.responseTime = new Date() - start;
  saveAccessDetails(model);

  return res
    .status(response.statusCode)
    .send(_.pick(response, ["status", "description"]));
});

router.post("/merchantLoanOfferDecision", async (req, res) => {
  const model = new accessDetailsModel();
  model.api = "merchantLoanOfferDecision";
  model.authKey = req.headers["auth-key"];
  model.publicIP = await publicIp.v4();
  model.req = req.body ? req.body : null;
  model.isSuccess = false;

  const start = new Date();

  const { error } = validateBorrowerLoanAcceptance(req.body);
  if (error) {
    model.errorMsg = error.details[0].message;
    model.responseTime = new Date() - start;
    saveAccessDetails(model);
    return res.status(400).send({ status: 0, description: error.details[0].message });
  }
  let response = await checkPartnerAccess(model.authKey, "Borrowing Partner");

  if (!response.success) {
    model.errorMsg = response.message;
    model.responseTime = new Date() - start;
    saveAccessDetails(model);
    const responseModel = {
      statusCode: response.status,
      status: response.success,
      description: response.message
    }
    return res.status(responseModel.statusCode).send(_.pick(responseModel, ["status", "description"]));
  } else {
    model.partnerId = response.partnerId;
  }

  response = await saveBorrowerLoanAcceptance(req.body, model.partnerId);

  if (response.status == 0) { model.errorMsg = response.description; } else { model.errorMsg = null; }

  model.isSuccess = response.status == 1 ? true : false;
  model.responseTime = new Date() - start;
  saveAccessDetails(model);

  return res.status(response.statusCode).send(_.pick(response, ["status", "description"]));
});

router.post("/getUserDetails", async (req, res) => {
  //Developed for Pay1
  const { error } = validateGetUserDetails(req.body);
  if (error) return res.status(400).send({ data: null, success: false, message: error.details[0].message });
  let model = {};

  let authKey = req.headers["auth-key"];

  if (authKey) {
    model = new accessDetailsModel();
    model.api = "getUserDetails";
    model.type = "userDetails";
    const start = new Date();

    model.publicIP = req.connection.remoteAddress || req.socket.remoteAddress || (req.headers['x-forwarded-for'] || '').split(',')[0] || (req.connection.socket ? req.connection.socket.remoteAddress : null);

    model.req = req.body ? req.body : null;
    model.isSuccess = false;
    model.authKey = authKey;

    let response = await checkPartnerAccess(model.authKey, "Borrowing Partner");

    if (!response.success) {
      model.errorMsg = response.message;
      model.responseTime = new Date() - start;
      saveAccessDetails(model);
      // saveDeviceInfo(response.partnerId ? response.partnerId : null, req.body);
      return res.status(response.status).send(_.pick(response, ["success", "message"]));
    } else {
      model.partnerId = response.partnerId;
      model.partnerCode = response.partnerCode;
    }

    // saveDeviceInfo(response.partnerId, req.body);

    response = await getUserDetails(authKey, req.body);

    if (!response.success) { model.errorMsg = response.message; } else { model.errorMsg = null; }
    model.response = response;
    model.isSuccess = response.success;
    model.responseTime = new Date() - start;
    saveAccessDetails(model);

    // return res.status(200).send(_.pick(response, ["data", "success", "message"]));

    let contactDetailsResponse = await contactDetails(response.data && response.data.userId ? response.data.userId : null,
      response.data && response.data.loanId ? response.data.loanId : null);

    return res.status(200).send({
      data: response.data,
      success: response.success,
      message: response.message,
      contactDetails: contactDetailsResponse
    });
  } else {
    return res
      .status(501)
      .send({ data: null, status: false, message: "auth-key is mandatory" });
  }
});

router.post("/getLoanStatus", async (req, res) => {
  const model = new accessDetailsModel();
  model.api = "getLoanStatus";
  const start = new Date();

  model.publicIP = (req.headers['x-forwarded-for'] || '').split(',')[0] || req.connection.remoteAddress;
  model.req = req.body;
  model.isSuccess = false;
  //console.log("your ip is ", req.ip);
  // console.log("X-Real-IP ", req.header["X-Real-IP"]);
  // console.log("X-Forwarded-For", req.header["x-forwarded-for"]);

  const { error } = validateGetLoanStatus(req.body);

  if (error) {
    model.errorMsg = error.details[0].message;
    model.responseTime = new Date() - start;
    saveAccessDetails(model);
    return res.status(400).send({ success: false, message: error.details[0].message });
  }

  model.authKey = req.headers["auth-key"];

  let response = await checkPartnerAccess(model.authKey, "Borrowing Partner");

  if (!response.success) {
    model.errorMsg = response.message;
    model.responseTime = new Date() - start;
    saveAccessDetails(model);
    return res.status(response.status).send(_.pick(response, ["success", "message"]));
  } else {
    model.partnerId = response.partnerId;
  }
  //console.log(model);
  response = await getLoanStatus(req.body, model.authKey);

  if (!response.success) { model.errorMsg = response.message; } else { model.errorMsg = null; }

  model.isSuccess = response.success;
  model.responseTime = new Date() - start;
  saveAccessDetails(model);
  //console.log("responseTime in ms ", responseTime);
  return res.status(response.status).send(_.pick(response, ["data", "success", "message"]));
});

router.post("/registerPartner", async (req, res) => {
  const { error } = validateRegisterPartner(req.body);
  if (error) return res.status(400).send({ success: false, message: error.details[0].message });

  const response = await registerPartner(req.body);

  return res.status(response.status).send(_.pick(response, ["success", "message"]));
});

router.post("/getRejectReasonsList", async (req, res) => {
  const model = new accessDetailsModel();
  model.api = "getRejectReasonsList";
  model.authKey = req.headers["auth-key"] ? req.headers["auth-key"] : "";
  const start = new Date();
  let response = await checkPartnerAccess(model.authKey, "Borrowing Partner");

  if (!response.success) {
    model.errorMsg = response.message;
    model.responseTime = new Date() - start;
    saveAccessDetails(model);
    const responseModel = {
      data: null,
      success: response.success,
      message: response.message
    }
    return res.status(response.status).send(_.pick(responseModel, ["data", "success", "message"]));
  } else {
    model.partnerId = response.partnerId;
  }
  response = await getRejectReasonList({ type: "client" });

  model.isSuccess = response.success;
  model.responseTime = new Date() - start;
  saveAccessDetails(model);

  return res.status(response.status).send(_.pick(response, ["data", "success", "message"]));
});

router.post("/encrypt", async (req, res) => {

  let text = encrypt(req.body.value);

  return res.status(200).send(text);
});

router.post("/decrypt", async (req, res) => {
  let text = decrypt(req.body.value);

  return res.status(200).send(text);
});

router.post("/borrowerPartnerList", async (req, res) => {
  let response = await getBorrowingPartnerList();

  return res
    .status(200)
    .send(response);
});

router.get("/loan/getLoans", async (req, res) => {
  let model = {};

  if (req.headers["auth-key"]) {
    model = new accessDetailsModel();
    model.api = "loan";
    model.type = "allLoans";
    model.fromDate = req.body.fromDate;
    model.toDate = req.body.toDate;
    const start = new Date();

    model.publicIP = req.connection.remoteAddress || req.socket.remoteAddress || (req.headers['x-forwarded-for'] || '').split(',')[0] || (req.connection.socket ? req.connection.socket.remoteAddress : null);

    model.req = req.body ? req.body : null;
    model.isSuccess = false;
    model.authKey = req.headers["auth-key"];
    let response = await checkPartnerAccess(model.authKey, "Lending Partner");
    if (!response.success) {
      model.errorMsg = response.message;
      model.responseTime = new Date() - start;
      saveAccessDetails(model);
      return res.status(response.status).send(_.pick(response, ["success", "message"]));
    } else {
      model.partnerId = response.partnerId;
      model.partnerCode = response.partnerCode;
    }
    //console.log(model);

    response = await getLoanDetails(model);

    if (!response.success) { model.errorMsg = response.message; } else { model.errorMsg = null; }

    model.isSuccess = response.success;
    model.responseTime = new Date() - start;
    saveAccessDetails(model);

    return res
      .status(response.status)
      .send(_.pick(response, ["data", "success", "message"]));
  } else {
    return res
      .status(501)
      .send({ status: false, message: "auth-key is mandatory" });
  }
});

router.get("/loan/getCreditLoans/:merchantCode?/:isPaymentLinkRequired?", async (req, res) => {
  let model = {};

  if (req.headers["auth-key"]) {
    model = new accessDetailsModel();
    model.api = "getCreditLoans";
    model.type = "creditLoans";
    const start = new Date();

    model.publicIP = req.connection.remoteAddress || req.socket.remoteAddress || (req.headers['x-forwarded-for'] || '').split(',')[0] || (req.connection.socket ? req.connection.socket.remoteAddress : null);
    model.merchantCode = typeof (req.params.merchantCode) == "undefined" ? "" : req.params.merchantCode;
    model.req = model.merchantCode;
    model.body = null;
    model.isSuccess = false;
    model.authKey = req.headers["auth-key"];
    let response = await checkPartnerAccess(model.authKey, "Borrowing Partner");

    if (!response.success) {
      model.errorMsg = response.message;
      model.responseTime = new Date() - start;
      saveAccessDetails(model);
      return res.status(response.status).send(_.pick(response, ["success", "message"]));
    } else {
      model.partnerId = response.partnerId;
      model.partnerCode = response.partnerCode;
      model.isPaymentLinkRequired = typeof (req.params.isPaymentLinkRequired) == "undefined" ? null : JSON.parse(req.params.isPaymentLinkRequired.toLowerCase());
    }

    response = await getCreditLoanDetails(model);

    if (!response.success) { model.errorMsg = response.message; } else { model.errorMsg = null; }

    model.isSuccess = response.success;
    model.responseTime = new Date() - start;
    saveAccessDetails(model);

    return res
      .status(response.status)
      .send(_.pick(response, ["data", "success", "message"]));
  } else {
    return res
      .status(501)
      .send({ status: false, message: "auth-key is mandatory" });
  }
});

router.get("/loan/:loanNo/:type?", async (req, res) => {
  //if (typeof (req.params.loanNo) != "undefined") {
  if (req.params.loanNo) {
    const model = new accessDetailsModel();

    model.loanNo = req.params.loanNo;
    model.type = typeof (req.params.type) == "undefined" ? "" : req.params.type;
    model.api = "loan";
    const start = new Date();

    model.publicIP = req.connection.remoteAddress || req.socket.remoteAddress || (req.headers['x-forwarded-for'] || '').split(',')[0] || (req.connection.socket ? req.connection.socket.remoteAddress : null);

    model.req = req.body;
    model.isSuccess = false;
    model.authKey = req.headers["auth-key"];
    let response = await checkPartnerAccess(model.authKey, "Lending Partner");
    if (!response.success) {
      model.errorMsg = response.message;
      model.responseTime = new Date() - start;
      saveAccessDetails(model);
      return res.status(response.status).send(_.pick(response, ["success", "message"]));
    } else {
      model.partnerId = response.partnerId;
      model.partnerCode = response.partnerCode;
    }
    //console.log(model);
    response = await getLoanDetails(model);

    if (!response.success) { model.errorMsg = response.message; } else { model.errorMsg = null; }

    model.isSuccess = response.success;
    model.responseTime = new Date() - start;
    saveAccessDetails(model);

    return res
      .status(response.status)
      .send(_.pick(response, ["data", "success", "message"]));
  } else {
    return res
      .status(501)
      .send({ status: false, message: "loanNo is required" });
  }

});

router.get("/:id", async (req, res) => {
  if (req.params.id != "loan") {
    let partnerId = decrypt(req.params.id);

    const response = await getPartner(partnerId);

    return res.status(response.status).send(response);
  } else {
    return res
      .status(501)
      .send({ status: false, message: "loanId is required" });
  }

});

router.post("/getLoanApplicationsForBorrowingPartner", [auth], async (req, res) => {

  req.body.userId = req.user.id;
  const response = await getLoanApplicationsForBorrowingPartnerV2(req.body);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message", "totalPages"]));
});

router.post("/downloadLoanApplicationsForBorrowingPartner", [auth], async (req, res) => {

  req.body.userId = req.user.id;
  const response = await downloadLoanApplicationsForBorrowingPartnerV2(req.body);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message",]));
});

router.post("/sendReviewLink", [auth], async (req, res) => {
  const response = await sendReviewLink(req.body , req);

  return res
    .status(response.status)
    .send(_.pick(response, ["success", "message"]));
});

router.post("/saveChildLoan", async (req, res) => {
  const { error } = validateSaveChildLoan(req.body);
  if (error) return res.status(400).send({ success: false, message: error.details[0].message });
  const response = await saveChildLoan(req.body);

  return res.status(response.status).send(_.pick(response, ["success", "message"]));
});

router.put("/confirmLoanRequest", async (req, res) => {
  const model = new accessDetailsModel();
  model.type = typeof (req.params.type) == "undefined" ? "" : req.params.type;
  model.api = "confirmLoanRequest";
  const start = new Date();

  model.publicIP = req.connection.remoteAddress || req.socket.remoteAddress || (req.headers['x-forwarded-for'] || '').split(',')[0] || (req.connection.socket ? req.connection.socket.remoteAddress : null);

  model.req = req.body;
  model.isSuccess = false;
  model.authKey = req.headers["auth-key"];
  const { error } = validateConfirmLoanRequest(req.body);

  if (error) {
    model.errorMsg = error.details[0].message;
    model.responseTime = new Date() - start;
    saveAccessDetails(model);
    return res.status(400).send({ success: false, message: error.details[0].message });
  }

  let response = await checkPartnerAccess(model.authKey, "Lending Partner");
  if (!response.success) {
    model.errorMsg = response.message;
    model.responseTime = new Date() - start;
    saveAccessDetails(model);
    return res.status(response.status).send(_.pick(response, ["success", "message"]));
  } else {
    model.partnerId = response.partnerId;
    model.partnerCode = response.partnerCode;
  }
  response = await confirmLoanRequest(model);

  if (!response.success) { model.errorMsg = response.message; } else { model.errorMsg = null; }

  model.isSuccess = response.success;
  model.responseTime = new Date() - start;
  saveAccessDetails(model);

  return res
    .status(response.status)
    .send(_.pick(response, ["success", "message"]));

});

router.post("/callLendingPartnerAPI", async (req, res) => {
  const response = await callLendingPartnerAPI(req.body);
  return res
    .status(response.status)
    .send(_.pick(response, ["success", "message"]));
});

router.post("/createOrder", async (req, res) => {
  const token = req.header("auth-key");
  const model = new accessDetailsModel();
  model.api = "createOrder";
  model.publicIP = await publicIp.v4();
  model.authKey = req.headers["auth-key"];
  model.req = req.body ? req.body : null;
  model.isSuccess = false;
  const start = new Date();
  let response = {
    status: 200,
    referenceNo: null,
    success: false,
    message: "Some Error Occurred",
  };

  if (!IsNullEmpty(model.authKey)) {
    partner = await PartnerMaster.findOne({
      authKey: model.authKey,
      partnerType: "Borrowing Partner"
    });

    if (!IsNullEmpty(partner)) {
      model.partnerId = partner._id;

      const { error } = validateCreateOrder(req.body);

      if (error) {
        model.errorMsg = error.details[0].message;
        model.responseTime = new Date() - start;
        saveAccessDetails(model);
        response.message = error.details[0].message;
      }

      response = await createOrder(req.body, model.partnerId);

      if (response.success == false) { model.errorMsg = response.message; } else { model.errorMsg = null; }

      model.isSuccess = response.success;
      model.responseTime = new Date() - start;
      saveAccessDetails(model);
    }
    else {
      response.message = "Invalid auth-key";
    }
  } else {
    response.message = "auth-key is Mandatory In Headers";
  }

  return res
    .status(response.status)
    .send(_.pick(response, ["referenceNo", "success", "message"]));
});

router.post("/generateOTP", async (req, res) => {
  const token = req.header("auth-key");
  const model = new accessDetailsModel();
  model.api = "generateOTP";
  model.publicIP = await publicIp.v4();
  model.authKey = req.headers["auth-key"];
  model.req = req.body ? req.body : null;
  model.isSuccess = false;
  const start = new Date();

  let response = {
    status: 200,
    success: false,
    message: "Some Error Occurred"
  };
  //console.log("model.authKey", model.authKey);
  if (!IsNullEmpty(model.authKey)) {
    partner = await PartnerMaster.findOne({
      authKey: { $exists: true, $ne: null, $eq: model.authKey },
      partnerType: "Borrowing Partner"
    });
    //console.log("partner", partner);
    if (!IsNullEmpty(partner)) {
      model.partnerId = partner._id;

      const { error } = validateGenerateOTPReq(req.body);

      if (error) {
        model.errorMsg = error.details[0].message;
        model.responseTime = new Date() - start;
        saveAccessDetails(model);
        response.message = error.details[0].message;
      } else {
        response = await generateOTP(model);
      }

      if (response.success == false) { model.errorMsg = response.message; } else { model.errorMsg = null; }

      model.isSuccess = response.success;
      model.responseTime = new Date() - start;
      saveAccessDetails(model);
    } else {
      response.message = "Invalid auth-key";
    }
  } else {
    response.message = "auth-key is Mandatory In Headers";
  }

  return res
    .status(response.status)
    .send(_.pick(response, ["success", "message"]));
});

router.post("/validateOTP", async (req, res) => {
  const token = req.header("auth-key");
  const model = new accessDetailsModel();
  model.api = "validateOTP";
  model.publicIP = await publicIp.v4();
  model.authKey = req.headers["auth-key"];
  model.req = req.body ? req.body : null;
  model.isSuccess = false;
  const start = new Date();

  let response = {
    status: 200,
    success: false,
    message: "Some Error Occurred"
  };

  if (!IsNullEmpty(model.authKey)) {
    partner = await PartnerMaster.findOne({
      authKey: model.authKey,
      partnerType: "Borrowing Partner"
    });

    if (!IsNullEmpty(partner)) {
      model.partnerId = partner._id;

      const { error } = validateValidateOTPReq(req.body);

      if (error) {
        model.errorMsg = error.details[0].message;
        model.responseTime = new Date() - start;
        saveAccessDetails(model);
        response.message = error.details[0].message;
      } else {
        response = await validateOTP(model);
      }

      if (response.success == false) { model.errorMsg = response.message; } else { model.errorMsg = null; }

      model.isSuccess = response.success;
      model.responseTime = new Date() - start;
      saveAccessDetails(model);
    } else {
      response.message = "Invalid auth-key";
    }
  } else {
    response.message = "auth-key is Mandatory In Headers";
  }

  return res
    .status(response.status)
    .send(_.pick(response, ["success", "message"]));
});

router.post("/offerDetails", async (req, res) => {
  const token = req.header("auth-key");
  const model = new accessDetailsModel();
  model.api = "offerDetails";
  model.publicIP = await publicIp.v4();
  model.authKey = req.headers["auth-key"];
  model.req = req.body ? req.body : null;
  model.isSuccess = false;
  const start = new Date();

  let response = {
    status: 200,
    success: false,
    message: "Some Error Occurred"
  };

  if (!IsNullEmpty(model.authKey)) {
    partner = await PartnerMaster.findOne({
      authKey: model.authKey,
      partnerType: "Borrowing Partner"
    });

    if (!IsNullEmpty(partner)) {
      model.partnerId = partner._id;

      const { error } = validateOfferDetailsReq(req.body);

      if (error) {
        model.errorMsg = error.details[0].message;
        model.responseTime = new Date() - start;
        saveAccessDetails(model);
        response.message = error.details[0].message;
      } else {
        response = await getOfferDetails(model);
      }


      // response = {
      //   status: 200,
      //   success: true,
      //   message: "genearted successfully"
      // }

      if (response.success == false) { model.errorMsg = response.message; } else { model.errorMsg = null; }

      model.isSuccess = response.success;
      model.responseTime = new Date() - start;
      saveAccessDetails(model);
    } else {
      response.message = "Invalid auth-key";
    }
  } else {
    response.message = "auth-key is Mandatory In Headers";
  }

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/updateRepayments", async (req, res) => {
  const token = req.header("auth-key");
  const model = new accessDetailsModel();
  model.api = "updateRepayments";
  model.publicIP = await publicIp.v4();
  model.authKey = req.headers["auth-key"];
  model.isSuccess = false;
  const start = new Date();

  let response = {
    status: 200,
    success: false,
    message: "Some Error Occurred",
    errorList: [],
    successList: []
  };

  if (!IsNullEmpty(model.authKey)) {
    partner = await PartnerMaster.findOne({
      authKey: model.authKey,
      partnerType: "Borrowing Partner"
    });

    if (!IsNullEmpty(partner)) {
      model.partnerId = partner._id;

      const { error } = validateUpdateEmiCollectionsReq(req.body);

      if (error) {
        model.errorMsg = error.details[0].message;
        model.responseTime = new Date() - start;
        saveAccessDetails(model);
        response.message = error.details[0].message;
      } else {
        response = await updateRepayments(partner._id, req.body);
      }

      if (response.success == false) { model.errorMsg = response.message; } else { model.errorMsg = null; }

      model.isSuccess = response.success;
      model.responseTime = new Date() - start;
      saveAccessDetails(model);
    } else {
      response.message = "Invalid auth-key";
    }
  } else {
    response.message = "auth-key is Mandatory In Headers";
  }

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message", "errorList", "successList"]));
});

router.post("/settlementMis", async (req, res) => {
  let output = {
    success: false,
    message: "Unauthorized",
    status: 401,
  }

  let authKey = req.headers["auth-key"];

  if (authKey) {
    let partner = await PartnerMaster.findOne({
      authKey: authKey,
      partnerType: "Borrowing Partner"
    }).lean();

    if (partner) {
      output = await settlementMISStage(req.body);
    }
  }

  return res
    .status(output.status)
    .send(_.pick(output, ["success", "message"]));
});



module.exports = router;
