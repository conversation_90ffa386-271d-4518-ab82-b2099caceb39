const express = require("express");
const router = express.Router();
const _ = require("lodash");
const auth = require("../../../middleware/auth");
const admin = require("../../../middleware/admin");

const {
  requestLoanRepayment,
  updatePostDisbursalStatus,
  requestLoanRepaymentEnquiryV1,
  followUp,
  insertSchedular,
  sendMailForFailure,
  sendEmiDetailsForLendingPartner,
  getLoanCompletionReport, sendNotifications,
  requestLoanRepaymentForDailyLoans,
  getIncompleteApplicationReport,
  sendAllEmiCollectionDetailsForLendingPartner,
  sendAllLoanDisbursedDetailsForLendingPartner,
  disbursalReportForLendingPartner,
  disbursalReportForBorrowingPartner,
  disbursalReportForFundfina,
  getIncompleteApplicationReportForBorrower,
  requestLoanRepaymentForPartners,
  requestLoanRepaymentDailyLoansForPartners,
  requestLoanRepaymentEnquiryForPartnersV1,
  setDeliquencyString,
  whatsAppFallbackScheduler,
  getEDIPayout,
  requestLoanRepaymentForWrittenOff,
  updateNotSufficientFundDaysV2,
  getEmiCollectionReport,
  updateEmiCollectionJob,
  dailyDisbursalReconReport
} = require("../../../services/ops/loan/postDisbursal");
const { requestMessageQueue } = require("../../../services/ops/partner/lendingPartner");
const { requestBorrowingPartnerMessageQueue } = require("../../../services/ops/partner/borrowingPartner");
const { exportDiscountFile } = require("../../../services/ops/loan/disbursal");
const { applyLateFee } = require("../../../services/ops/loan/installments");
const { updateClosedLoansStatus } = require("../../../services/ops/loan/installments");

router.post("/requestLoanRepayment", async (req, res) => {
  const responseLoanRepayment = await requestLoanRepaymentForPartners(req.body);
  if (responseLoanRepayment.success) {
    const responseUpdatePostDisbursalStatus = await updatePostDisbursalStatus();
    if (responseUpdatePostDisbursalStatus.success) {
      await insertSchedular("Request Loan Repayment", "request update post disbursal completed", null);
      await insertSchedular("Request Loan Repayment", "request followup started", null);
      const responseFollowUp = await followUp(req.body);
      if (responseFollowUp.success) {
        await insertSchedular("Request Loan Repayment", "request followup completed", responseFollowUp);
        await insertSchedular("Request Loan Repayment", "request Update Not Sufficient Fund Days Started", null);
        const responseNSFDays = await updateNotSufficientFundDaysV2(req.body);
        if (responseNSFDays.success) {
          await insertSchedular("Request Loan Repayment", "request Update Not Sufficient Fund Days completed", responseNSFDays);
        } else {
          await insertSchedular("Request Loan Repayment", "request Update Not Sufficient Fund Days completed with error", responseNSFDays);

          await sendMailForFailure("Not Sufficient Fund Days Failed", "Not Sufficient Fund Days Failed due to error " + responseNSFDays.message);
        }
        return res
          .status(responseNSFDays.status)
          .send(_.pick(responseNSFDays, ["success", "message"]));
      } else {
        await insertSchedular("Request Loan Repayment", "request followup completed with error", responseFollowUp);

        await sendMailForFailure("Followup Schedular Failed", "Followup Schedular Failed due to error " + responseFollowUp.message);
      }
    } else {
      await insertSchedular("Request Loan Repayment", "request update post disbursal completed with error", responseUpdatePostDisbursalStatus);

      await sendMailForFailure("Update Post Disbursal Status Schedular Failed", "Update Post Disbursal Status Schedular Failed due to error " + responseUpdatePostDisbursalStatus.message);
    }
  } else {
    await insertSchedular("Request Loan Repayment", "request Loan Repayment completed with error", responseLoanRepayment);
    const mailResponse = await sendMailForFailure("Request Loan Repayment Schedular Failed", "Request Loan Repayment Schedular Failed due to error " + responseLoanRepayment.message);
    return res
      .status(responseLoanRepayment.status)
      .send(_.pick(responseLoanRepayment, ["success", "message"]));
  }
});

router.post("/requestLoanRepaymentForWrittenOff", async (req, res) => {
  const responseLoanRepayment = await requestLoanRepaymentForWrittenOff(req.body);
  if (responseLoanRepayment.success) {
    await insertSchedular("Request Loan Repayment for Written-Off", "request followup started", null);
    const responseFollowUp = await followUp(req.body, "Written-Off");
    if (responseFollowUp.success) {
      await insertSchedular("Request Loan Repayment for Written-Off", "request followup completed", responseFollowUp);
    } else {
      await insertSchedular("Request Loan Repayment for Written-Off", "request followup completed with error", responseFollowUp);
      const mailResponse = await sendMailForFailure(" Written-Off Followup Schedular Failed", " Written-Off Followup Schedular Failed due to error " + responseFollowUp.message);
    }
    return res
      .status(responseFollowUp.status)
      .send(_.pick(responseFollowUp, ["data", "success", "message"]));

  } else {
    await insertSchedular("Request Loan Repayment for Written-Off Failed", "Request Loan Repayment for Written-Off Failed due to error" + responseLoanRepayment.message);
    return res
      .status(responseLoanRepayment.status)
      .send(_.pick(responseLoanRepayment, ["success", "message"]));
  }
});

router.post("/requestLoanRepaymentEnquiry", async (req, res) => {
  const response = await requestLoanRepaymentEnquiryForPartnersV1(req.body);
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/requestOnlyLoanRepayment", async (req, res) => {
  await insertSchedular(
    "Request Loan Repayment",
    "request loan repayment started",
    null
  );
  const responseLoanRepayment = await requestLoanRepayment(req.body);

  if (responseLoanRepayment.success) {
    await insertSchedular("Request Loan Repayment", "request loan repayment completed", responseLoanRepayment);
  }
  else {
    await insertSchedular("Request Loan Repayment", "request loan repayment completed with error", responseLoanRepayment);
  }

  return res
    .status(responseLoanRepayment.status)
    .send(_.pick(responseLoanRepayment, ["data", "success", "message"]));
});

router.post("/requestOnlyLoanRepaymentWrittenOff", async (req, res) => {
  await insertSchedular("Request Loan Repayment Written-Off", "request loan repayment Written-Off started", null);
  const responseLoanRepayment = await requestLoanRepaymentForWrittenOff(req.body);

  if (responseLoanRepayment.success) {
    await insertSchedular("Request Loan Repayment Written-Off", "request loan repayment Written-Off completed", responseLoanRepayment);
  }
  else {
    await insertSchedular("Request Loan Repayment Written-Off", "request loan repayment Written-Off completed with error", responseLoanRepayment);
  }

  return res
    .status(responseLoanRepayment.status)
    .send(_.pick(responseLoanRepayment, ["data", "success", "message"]));
});

router.post("/requestOnlyLoanRepaymentEnquiry", async (req, res) => {
  await insertSchedular(
    "Request Loan Repayment",
    "request loan repayment enquiry started",
    null
  );

  const responseRepaymentEnquiry = await requestLoanRepaymentEnquiryV1(req.body);

  if (responseRepaymentEnquiry.success) {
    await insertSchedular(
      "Request Loan Repayment",
      "request loan repayment enquiry completed",
      responseRepaymentEnquiry
    );
  }
  else {
    await insertSchedular(
      "Request Loan Repayment",
      "request loan repayment enquiry completed with error",
      responseRepaymentEnquiry
    );
  }

  return res
    .status(responseRepaymentEnquiry.status)
    .send(_.pick(responseRepaymentEnquiry, ["data", "success", "message"]));
});

router.post("/requestOnlyFollowUp", async (req, res) => {
  await insertSchedular(
    "Request Loan Repayment",
    "request followup started",
    null
  );
  const responseFollowUp = await followUp(req.body, req.body.status);
  if (responseFollowUp.success) {
    await insertSchedular("Request Loan Repayment", "request followup completed", responseFollowUp);
  } else {
    await insertSchedular("Request Loan Repayment", "request followup completed with error", responseFollowUp);
  }
  return res
    .status(responseFollowUp.status)
    .send(_.pick(responseFollowUp, ["data", "success", "message"]));
});

router.post("/requestOnlyPostDisbursal", async (req, res) => {
  await insertSchedular("Request Loan Repayment", "request update post disbursal started", null);
  const responseUpdatePostDisbursalStatus = await updatePostDisbursalStatus();
  if (responseUpdatePostDisbursalStatus.success)
    await insertSchedular("Request Loan Repayment", "request update post disbursal completed", responseUpdatePostDisbursalStatus);
  else
    await insertSchedular("Request Loan Repayment", "request update post disbursal completed with error", responseUpdatePostDisbursalStatus);

  return res
    .status(responseUpdatePostDisbursalStatus.status)
    .send(_.pick(responseUpdatePostDisbursalStatus, ["data", "success", "message"]));
});

router.post("/requestFailedMessageQueue", async (req, res) => {
  await insertSchedular("Request Message Queue", "request message queue started", null);
  const responseRequestMessageQueue = await requestMessageQueue(req.body);
  if (responseRequestMessageQueue.success) {
    await insertSchedular("Request Message Queue", "request message queue completed", responseRequestMessageQueue);
  } else {
    await insertSchedular("Request Message Queue", "request message queue completed with error", responseRequestMessageQueue);
  }
  return res
    .status(responseRequestMessageQueue.status)
    .send(_.pick(responseRequestMessageQueue, ["success", "message"]));
});

router.post("/requestFailedMessageQueueForBorrowingPartner", async (req, res) => {
  //await insertSchedular("Request Message Queue", "request message queue for borrowing partner started", null);
  const responseRequestMessageQueue = await requestBorrowingPartnerMessageQueue(req.body);
  if (responseRequestMessageQueue.success) {
    //await insertSchedular("Request Message Queue", "request message queue for borrowing partner completed", responseRequestMessageQueue);
  } else {
    //await insertSchedular("Request Message Queue", "request message queue for borrowing partner completed with error", responseRequestMessageQueue);
  }
  return res
    .status(responseRequestMessageQueue.status)
    .send(_.pick(responseRequestMessageQueue, ["success", "message"]));
});

router.post("/sendEmiDetailsForLendingPartner", async (req, res) => {
  const response = await sendEmiDetailsForLendingPartner(req.body);
  if (!response.status) {
    await insertSchedular("Lending Partner", "Send emi collection report to lender completed with error", response);
    const mailResponse = await sendMailForFailure("Send emi collection report to lender Failed", "Send emi collection report to lender failed due to error " + response.message);
  } else {
    await insertSchedular("Lending Partner", "Send emi collection report to lender completed", response);
  }
  return res
    .status(response.status)
    .send(_.pick(response, ["success", "message"]));
});

router.post("/requestOnlyLoanCompletionReport", async (req, res) => {
  await insertSchedular("Request Loan Repayment", "request loan completion report started", null);
  const responseRenewal = await getLoanCompletionReport();
  if (responseRenewal.success) {
    responseRenewal.data = null;
    await insertSchedular("Request Loan Repayment", "request loan completion report completed", responseRenewal);
  } else {
    await insertSchedular("Request Loan Repayment", "request loan completion report completed with error", responseRenewal);
  }
  return res
    .status(responseRenewal.status)
    .send(_.pick(responseRenewal, ["data", "success", "message"]));
});

router.post("/requestOnlyIncompleteApplicationReport", async (req, res) => {
  await insertSchedular("Request Loan Repayment", "request Incomplete Application Report started", null);
  const isForDay = req.body.isForDay ? req.body.isForDay : false;
  const responseIncompleteApplication = await getIncompleteApplicationReport(req.body.sendReport, req.body.sendWhatsApp, isForDay);
  if (responseIncompleteApplication.success) {
    await insertSchedular("Request Loan Repayment", "request Incomplete Application Report completed", responseIncompleteApplication);
  } else {
    await insertSchedular("Request Loan Repayment", "request Incomplete Application Report completed with error", responseIncompleteApplication);
  }
  return res
    .status(responseIncompleteApplication.status)
    .send(_.pick(responseIncompleteApplication, ["data", "success", "message"]));
});

router.post("/sendOnlyNotifications", async (req, res) => {
  await insertSchedular("Request Loan Repayment", "request notification started", null);
  let notificationId = null;
  let partnerId = null;
  if (req.body.partnerId) partnerId = req.body.partnerId;
  if (req.body.notificationId) notificationId = req.body.notificationId;
  const responseNotification = await sendNotifications(notificationId, partnerId);
  if (responseNotification.success) {
    await insertSchedular("Request Loan Repayment", "request notification completed", responseNotification);
  } else {
    await insertSchedular("Request Loan Repayment", "request notification completed with error", responseNotification);
  }
  return res
    .status(responseNotification.status)
    .send(_.pick(responseNotification, ["data", "success", "message"]));
});

router.post("/requestLoanRepaymentForDailyLoans", async (req, res) => {
  const responseLoanRepayment = await requestLoanRepaymentDailyLoansForPartners(req.body);
  return res
    .status(responseLoanRepayment.status)
    .send(_.pick(responseLoanRepayment, ["success", "message"]));
});

router.post("/requestOnlyLoanRepaymentForDailyLoans", async (req, res) => {
  await insertSchedular("Request Loan Repayment", "request loan repayment For Daily Loans started", null);
  const responseLoanRepayment = await requestLoanRepaymentForDailyLoans(req.body);

  if (responseLoanRepayment.success) {
    await insertSchedular("Request Loan Repayment", "request loan repayment For Daily Loans completed", responseLoanRepayment);
  }
  else {
    await insertSchedular("Request Loan Repayment", "request loan repayment For Daily Loans completed with error", responseLoanRepayment);
  }

  return res
    .status(responseLoanRepayment.status)
    .send(_.pick(responseLoanRepayment, ["data", "success", "message"]));
});

router.post("/requestOnlyUpdatePostDisbursalStatus", async (req, res) => {
  const response = await updatePostDisbursalStatus();
  return res
    .status(response.status)
    .send(_.pick(response, ["success", "message"]));
});

router.post("/sendAllEmiCollectionDetailsForLendingPartner", async (req, res) => {
  const response = await sendAllEmiCollectionDetailsForLendingPartner(req.body);
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/sendAllLoanDisbursedDetailsForLendingPartner", async (req, res) => {
  const response = await sendAllLoanDisbursedDetailsForLendingPartner(req.body);
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/disbursalReportForLendingPartner", async (req, res) => {
  const response = await disbursalReportForLendingPartner(req.body);
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/disbursalReportForBorrowingPartner", async (req, res) => {
  const response = await disbursalReportForBorrowingPartner(req.body);
  return res
    .status(response.status)
    .send(_.pick(response, ["success", "message"]));
});

router.post("/disbursalReportForFundfina", async (req, res) => {
  const response = await disbursalReportForFundfina(req.body);
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/incompleteApplicationReportForBorrower", async (req, res) => {
  const response = await getIncompleteApplicationReportForBorrower(req.body);
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/requestOnlyPenalty", async (req, res) => {
  const penaltyResponse = await applyLateFee(req.body.partnerId, req.body.productType, req.body.loanId, req.body.applyForHold);

  return res
    .status(penaltyResponse.status)
    .send(_.pick(penaltyResponse, ["success", "message"]));
});

router.post("/requestOnlysetDeliquencyString", async (req, res) => {
  const response = await setDeliquencyString(req.body);

  return res
    .status(response.status)
    .send(_.pick(response, ["success", "message"]));
});

router.post("/whatsAppFallbackScheduler", async (req, res) => {
  let response = await whatsAppFallbackScheduler();

  return res
    .status(200)
    .send(response);
});

router.post("/getEDIPayout", async (req, res) => {
  const response = await getEDIPayout(req.body);

  return res
    .status(response.status)
    .send(_.pick(response, ["success", "message"]));
});

router.post("/requestOnlyUpdateNotSufficientFundDays", async (req, res) => {
  const response = await updateNotSufficientFundDaysV2(req.body);

  return res
    .status(response.status)
    .send(_.pick(response, ["success", "message"]));
});

router.post("/exportDiscountFile", async (req, res) => {
  const response = await exportDiscountFile(req.body);

  return res
    .status(response.status)
    .send(_.pick(response, ["success", "message"]));
});

router.post("/getEmiCollectionReport", async (req, res) => {
  const response = await getEmiCollectionReport(req.body);

  return res
    .status(response.status)
    .send(_.pick(response, ["success", "message"]));
});

router.post("/updateEmiCollectionJob", async (req, res) => {
  const responseLoanRepayment = await updateEmiCollectionJob();
  return res
    .status(responseLoanRepayment.status)
    .send(_.pick(responseLoanRepayment, ["success", "message", "errorList", "successList"]));
});


router.post("/dailyDisbursalReconReport", async (req, res) => {

  let output = {
    success: true,
    message: "Daily disbursal reconciliation report job initated",
    status: 200,
  }
  const response = dailyDisbursalReconReport(req);
  return res
    .status(output.status)
    .send(_.pick(output, ["success", "message"]));

});

router.post("/updateClosedLoansStatus", async (req, res) => {
  const response = await updateClosedLoansStatus();
  return res
    .status(response.status)
    .send(_.pick(response, ["success", "message"]));
});

module.exports = router;