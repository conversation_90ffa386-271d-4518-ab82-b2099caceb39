const auth = require("../../../middleware/auth");
const express = require("express");
const router = express.Router();
const { bulkAccountSummaryUpdateWrapper, getLenderAccountSummaryJobStatus, refreshLenderAccountSummary, getLenderAccountSummaryList , getPreApprovalAmount} = require("../../../services/ops/partner/lendingPartnerAccounts");
const admin = require("../../../middleware/admin");
const _ = require("lodash");
const db = require("../../../startup/db");
const { TrueScoreBeta } = require("../../../models/ops/partner/partner");

router.post("/accountSummaryRefresh", [auth, admin], async (req, res) => {
    const response = await bulkAccountSummaryUpdateWrapper(req.body);
    return res.status(response.status).send(_.pick(response, ["data", "success", "message"]));
});

router.post("/accountSummaryJob", async (req, res) => {
    const response = await bulkAccountSummaryUpdateWrapper(req.body);
    return res.status(response.status).send(_.pick(response, ["data", "success", "message"]));
});

router.get("/getAccountSummaryJobStatus", [auth, admin], async (req, res) => {
    const response = await getLenderAccountSummaryJobStatus(req.body);
    return res.status(response.status).send(_.pick(response, ["data", "success", "message"]));
});

router.post("/refreshLenderAccountSummary", [auth, admin], async (req, res) => {
    const response = await refreshLenderAccountSummary(req.body);
    return res.status(response.status).send(_.pick(response, ["data", "success", "message"]));
});

router.get("/getLenderAccountSummaryList", [auth, admin], async (req, res) => {
    const response = await getLenderAccountSummaryList(req.body);
    return res.status(response.status).send(_.pick(response, ["data", "success", "message"]));
});

router.post("/getPreApprovalAmount", async (req, res) => {
    const response = await getPreApprovalAmount(req.body);
    return res.status(response.status).send(_.pick(response, ["data", "success", "message","status"]));
});

module.exports = router;