const express = require("express");
const router = express.Router();
const _ = require("lodash");
const auth = require("../../../middleware/auth");
const admin = require("../../../middleware/admin");
const lendingPartner = require("../../../middleware/lendingPartner");
const { exportCibilReportForLendingPartnerV1, exportCibilReportForLendingPartnerV2, exportLoansReport } = require("../../../services/ops/partner/reports");

router.post("/exportCibilReport", [auth, lendingPartner], async (req, res) => {
    let response = null;

    req.body.partnerId = req.user.partnerId;
    if (req.body.version == 2) {
        response = await exportCibilReportForLendingPartnerV2(req.body);
    } else {
        response = await exportCibilReportForLendingPartnerV1(req.body);
    }
    return res
        .status(response.status)
        .send(_.pick(response, ["fileName", "fileURL", "success", "message"]));
});

router.post("/exportLoansReport", async (req, res) => {
    const response = await exportLoansReport(req.body);
    return res
        .status(response.status)
        .send(_.pick(response, ["fileName", "fileURL", "success", "message"]));
});

module.exports = router;