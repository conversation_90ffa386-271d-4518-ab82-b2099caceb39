const express = require("express");
const router = express.Router();
const _ = require("lodash");
const config = require("config");

let bodyParser = require("body-parser");
router.use(bodyParser.urlencoded({ extended: true }));

router.post("/", async (req, res) => {
  let url = config.get("keys.webUrl");
  let userId = req.body.userId ? req.body.userId : "";
  let trxnId = req.body.trxnId ? req.body.trxnId : "";
  let loanAmount = req.body.loanAmount ? req.body.loanAmount : "";
  let token = req.body.token ? req.body.token : "";

  let message = "";

  if (!userId || !trxnId || !loanAmount || !token) {
    if (!userId) message += " UserId";
    if (!trxnId) message += " TrxnId";
    if (!loanAmount) message += " LoanAmount";
    if (!token) message += " Token";
    message += " not provided";

    res.redirect(`${url}redirectInvalidData/${message}`);
  } else
    res.redirect(`${url}leadInfo/${userId}/${trxnId}/${loanAmount}/${token}`);
});

module.exports = router;
