const express = require('express');
const auth = require("../../../middleware/auth");
const admin = require("../../../middleware/admin");
const router = express.Router();
const _ = require('lodash');
const { validateGetUserDetails } = require('../../../models/ops/partner/partner');
const { getRandomNo, downloadImage } = require('../../../services/ops/common/common');
const { loanOffer, disburse, statusUpdate } = require('../../../services/ops/partner/eko');
const { getPayOneToken, pushPreApprovals, getUserDetails, sendNotification, getTransactionData } = require('../../../services/ops/partner/payOne');
const { PushToFundfina, validateKYC } = require('../../../services/ops/partner/partner');
const { exportLendingPartnerFailedApis, insertFailedMessageQueue } = require('../../../services/ops/loan/postDisbursal');
const { getTokenFromAphelion } = require("../../../services/ops/partner/lendingPartner");
const { reSignContract, calculateEmi } = require("../../../services/ops/loan/loan");
const { LoanInfo } = require("../../../models/ops/loan/loan");
const { exportPendingEmis } = require("../../../services/ops/partner/reports");
const { updateStates, updateWriteOff, checkForWriteOff, resetDisbursalAmountInPreApproval, saveOnlyDisbursalTransactions, updateLendgerAccountDetailsForRollbackLoans, updateDateOfOnboarding, updatemissingClientCode, cancelCentrumLoans, updateLenderPartnerId, findP2PLoans, addDataInLendingPartnerAllocation, addKYCInfo, UpdateLendingPartner, UpdateLedgerAccount, UpdatePayInPayOut, checkCharges, updateMissingClientCodesOfRAPI, insertofferId, checkMissingBanks, bulkUpdateGenderInfo, bulkInsertDigioBankList, updateCreditScoreData, updateLoanAuthMode, exportImagesforLendingPartner, updateMissingApprovedOn, userInfoCleaning, InsertLoanInstallments, updateNextEmiRepaymentDate, maskAadhaarNoOnPerfDB, updateQuestionnairreConfig, updateProcessingFeeForExisting, updateEmiConsolidatedInformationInLoanInfo, updateWrittenOffInFlowDetails, updatePreApprovalConfig, updateSecretKeyConfig, insertNotificationTemplateForNewPartner, updatePrincipalOutstanding, updateRecoursePending, updateProcessingFeePreApproval, updatePartnerLoans, updateVirtualAccountNo, takeUserBackup, reverseReceivedPayment, updateDisbursalConsent, updateTypeInNotificationTemplates, updateReferenceNoInLoanInstallment, addHoldDateStamps, updatePartnerIdInLoanInstallment, addHoldDateStampsOld, updateReferenceNoInLoanFees, updateProductIdInPreApproval, insertNotificationTemplate, gstListJob, updateDisbursalDetailsForArthan, findDuplicateUserPartner, uploadTrancheDetails, cleanDuplicatePanInUserInfo, cleanDuplicateUserPartnerInfo, updateMissingMerchantCodeInUserPartnerInfo, cleanDuplicateUserPartnerInfo1, getPartnerLoansAndTrueScore, updateMissingArrears, getEarlyPendingEmiLoans, updateEmiRecordsInLoanInfo } = require("../../../services/ops/datacleaning/datacleaning");
const { underReview } = require("../../../services/ops/loan/workflow");
const { computeLoanCashback } = require("../../../services/ops/loan/cashback");


router.post("/loanOffer", [auth, admin], async (req, res) => {
  const response = await loanOffer(req.body.loanId, true, 1, req.body.reason, req.body.description);
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/disburse", [auth, admin], async (req, res) => {
  const response = await disburse(req.body.loanId);
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/updateStatus", [auth, admin], async (req, res) => {
  const response = await statusUpdate(req.body.loanId, req.body.status);
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/getRandom", [auth, admin], async (req, res) => {
  let random = getRandomNo(req.body.len);
  return res
    .status(200)
    .send(random);
});

router.post("/getPay1Token", [auth, admin], async (req, res) => {
  const response = await getPayOneToken();
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/pushPreApprovals", [auth, admin], async (req, res) => {
  //genrete trxnId of max 50 request for pay1
  const response = await pushPreApprovals(req.body);
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/getUserDetails", [auth, admin], async (req, res) => {

  const { error } = validateGetUserDetails(req.body);
  if (error) return res.status(400).send(error.details[0].message);

  let authKey = req.headers["auth-key"];

  const response = await getUserDetails(authKey, req.body);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/DownloadImage", [auth, admin], async (req, res) => {
  // let url="https://pay1dms.s3.amazonaws.com/47007593_1_1_20200501213232.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIAIP36TMKRJCTDEYVQ%2F20200627%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20200627T000000Z&X-Amz-Expires=75606&X-Amz-SignedHeaders=host&X-Amz-Signature=ede26f0416e06c5784a06098e9f6c90426a9429a63b802e29dc1569c9df27b12"
  // let path="\\ProofImages\\5ef77083912dd225103f0ee4\\PAN\\pan_front.jpg";
  // const response= await downloadImage(url,path);

  const response = await downloadImage(req.body.url, req.body.path);
  return res
    .status(response.status)
    .send(_.pick(response, ["success", "message"]));
});

router.post("/sendNotification", [auth, admin], async (req, res) => {
  const response = await sendNotification(req.body.loanId, req.body.type);
  return res
    .status(response.status)
    .send(_.pick(response, ["success", "message"]));
});

router.post("/getTransactions", [auth, admin], async (req, res) => {
  getTransactionData(req.body)//(req.body.pageNo, req.body.isSinglePage,req.body.partnerCode)
    .then((response) => {
      return res
        .status(response.status)
        .send(_.pick(response, ["success", "message", "data", "currentPage", "totalPages", "recordsInserted", "pagesInserted"]));
    })
    .catch((response) => {
      return res
        .status(response.status)
        .send(_.pick(response, ["data", "success", "message", "currentPage", "totalPages", "recordsInserted", "pagesInserted"]));
    })
});


router.post("/testPushToFundfina", [auth, admin], async (req, res) => {
  const response = await PushToFundfina(req.body.partnerLoanId);
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});


router.post("/testValidateKYC", [auth, admin], async (req, res) => {
  const response = await validateKYC(req.body.userId, req.body.partnerId, req.body.type);
  return res
    .status(response.status)
    .send(_.pick(response, ["success", "message"]));
});

// router.post("/getTokenFromAphelion", [auth, admin], async (req, res) => {
//   const response = await getTokenFromAphelion();
//   return res
//     .status(response.status)
//     .send(_.pick(response, ["token", "success", "message"]));
// });

router.post("/exportLendingPartnerFailedApis", [auth, admin], async (req, res) => {
  const response = await exportLendingPartnerFailedApis(req.body);
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/testSignContract", [auth, admin], async (req, res) => {
  const response = await reSignContract(req.body.loanNos);
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));

});

router.post("/testUpdateEmiPaidStatus", [auth, admin], async (req, res) => {
  const response = await updateEmiPaidStatus(req.body.loanId, req.body.emiNumber, req.body.repayAmount, req.body.paidOn, "Manual", "Testing", "test11");
  return res
    .status(response.status)
    .send(_.pick(response, ["success", "message"]));
});

router.post("/testEmiCalculation", [auth, admin], async (req, res) => {
  const disbursalDate = new Date();
  const response = await calculateEmi(req.body.loanAmount, req.body.maturity, req.body.interestRate, req.body.isMaturityMonth, req.body.repaymentFrequency, disbursalDate, req.body.interestType, req.body.considerHoliday, req.body.roundType, req.body.productType, req.body.holdCollection, req.body.adhocDays);
  const _res = {
    emis: response,
    success: true,
    message: "fetched successfully",
    status: 200
  };
  return res
    .status(_res.status)
    .send(_.pick(_res, ["emis", "success", "message"]));
});

router.post("/insertFailedMessageQueue", [auth, admin], async (req, res) => {
  const response = await insertFailedMessageQueue();
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/exportPendingEmis", [auth, admin], async (req, res) => {
  const response = await exportPendingEmis(req.body.partnerId);
  return res
    .status(response.status)
    .send(_.pick(response, ["success", "message"]));
});

router.post("/updateStates", [auth, admin], async (req, res) => {
  const response = await updateStates(req.body.partnerId);
  return res
    .status(response.status)
    .send(_.pick(response, ["success", "message"]));
});

router.post("/updateWriteOff", [auth, admin], async (req, res) => {
  const response = await updateWriteOff(req.body);
  return res
    .status(response.status)
    .send(_.pick(response, ["success", "message"]));
});

router.post("/checkForWriteOff", [auth, admin], async (req, res) => {
  const response = await checkForWriteOff();
  return res
    .status(response.status)
    .send(_.pick(response, ["success", "message"]));
});

router.post("/resetDisbursalAmountInPreApproval", [auth, admin], async (req, res) => {
  const response = await resetDisbursalAmountInPreApproval();
  return res
    .status(response.status)
    .send(_.pick(response, ["success", "message"]));
});

router.post('/saveOnlyDisbursalTransactions', [auth, admin], async (req, res) => {
  const response = await saveOnlyDisbursalTransactions(req.body.loanNo);
  return res.status(response.status).send(_.pick(response, ['success', 'message']));
});

router.post('/updateLedgerAccountDetailsForRollbackLoans', [auth, admin], async (req, res) => {
  const response = await updateLedgerAccountDetailsForRollbackLoans(req.body.loanNo);
  return res.status(response.status).send(_.pick(response, ['success', 'message']));
});

router.post('/updateDateOfOnboarding', [auth, admin], async (req, res) => {
  const response = await updateDateOfOnboarding();
  return res.status(response.status).send(_.pick(response, ['success', 'message']));
});

router.post('/updatemissingClientCode', [auth, admin], async (req, res) => {
  const response = await updatemissingClientCode();
  return res.status(response.status).send(_.pick(response, ['success', 'message']));
});

router.post('/cancelCentrumLoans', [auth, admin], async (req, res) => {
  const response = await cancelCentrumLoans();
  return res.status(response.status).send(_.pick(response, ['data', 'success', 'message']));
});

router.post('/underReview', [auth, admin], async (req, res) => {
  const response = await underReview(req.body.loanId, req.body.name, req.body.additionalDescription, req.body.changedBy, req.body.changeByRole, "front-end");
  return res.status(response.status).send(_.pick(response, ['data', 'success', 'message']));
});

router.post('/updateLenderPartnerId', [auth, admin], async (req, res) => {
  const response = await updateLenderPartnerId();
  return res.status(response.status).send(_.pick(response, ['success', 'message']));
});

router.post('/findP2PLoans', [auth, admin], async (req, res) => {
  const response = await findP2PLoans();
  return res.status(response.status).send(_.pick(response, ['success', 'message']));
});

router.post('/addDataInLendingPartnerAllocation', [auth, admin], async (req, res) => {
  const response = await addDataInLendingPartnerAllocation();
  return res.status(response.status).send(_.pick(response, ['success', 'message']));
});

router.post('/addKYCInfo', [auth, admin], async (req, res) => {
  const response = await addKYCInfo();
  return res.status(response.status).send(_.pick(response, ['success', 'message']));
});

router.post('/computeLoanCashback', [auth, admin], async (req, res) => {
  const response = await computeLoanCashback("62b9577115cc053700a1b977", true);
  return res.status(response.status).send(_.pick(response, ['success', 'message']));
});

router.post('/UpdateLendingPartner', [auth, admin], async (req, res) => {
  const response = await UpdateLendingPartner();
  return res.status(response.status).send(_.pick(response, ['success', 'message']));
});

router.post('/UpdateLedgerAccount', [auth, admin], async (req, res) => {
  const response = await UpdateLedgerAccount();
  return res.status(response.status).send(_.pick(response, ['success', 'message']));
});

router.post('/UpdatePayInPayOut', [auth, admin], async (req, res) => {
  const response = await UpdatePayInPayOut();
  return res.status(response.status).send(_.pick(response, ['success', 'message']));
});

router.post('/checkCharges', [auth, admin], async (req, res) => {
  const response = await checkCharges();
  return res.status(response.status).send(_.pick(response, ['success', 'message']));
});

router.post('/updateMissingClientCodesOfRAPI', [auth, admin], async (req, res) => {
  const response = await updateMissingClientCodesOfRAPI();
  return res.status(response.status).send(_.pick(response, ['success', 'message']));
});

router.post('/insertofferId', [auth, admin], async (req, res) => {
  const response = await insertofferId();
  return res.status(response.status).send(_.pick(response, ['offerNotFound', 'addedOfferId', 'success', 'message']));
});

router.post('/checkMissingBanks', [auth, admin], async (req, res) => {
  const response = await checkMissingBanks();
  return res.status(response.status).send(response);
});

router.post('/bulkUpdateGenderInfo', [auth, admin], async (req, res) => {
  const response = await bulkUpdateGenderInfo();
  return res.status(response.status).send(response);
});

router.post('/bulkInsertDigioBankList', [auth, admin], async (req, res) => {
  const response = await bulkInsertDigioBankList();
  return res.status(response.status).send(response);
});

router.post("/updateCreditScoreData", [auth, admin], async (req, res) => {
  const response = await updateCreditScoreData(req.body);

  return res
    .status(200)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/updateLoanAuthMode", [auth, admin], async (req, res) => {
  const response = await updateLoanAuthMode(req.body);

  return res
    .status(200)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/exportImagesforLendingPartner", [auth, admin], async (req, res) => {
  const response = await exportImagesforLendingPartner(req.body);

  return res
    .status(200)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post('/updateMissingApprovedOn', [auth, admin], async (req, res) => {
  const response = await updateMissingApprovedOn();
  return res.status(response.status).send(_.pick(response, ['success', 'message']));
});

router.post('/userInfoCleaning', [auth, admin], async (req, res) => {
  const response = await userInfoCleaning();
  return res.status(response.status).send(_.pick(response, ['notExists', 'moreThan1', 'success', 'message']));
});

router.post('/testgetInstallmentsPending', [auth, admin], async (req, res) => {
  const loanNos = ["63889"];
  const response = await getInstallmentsPending(null, loanNos, null, null, true, true, Date.now(), null);
  return res.status(response.status).send(response);
});

router.post('/InsertLoanInstallments', [auth, admin], async (req, res) => {
  const response = await InsertLoanInstallments();
  return res.status(response.status).send(_.pick(response, ['success', 'message']));
});

router.post('/updateNextEmiRepaymentDate', [auth, admin], async (req, res) => {
  const response = await updateNextEmiRepaymentDate();
  return res.status(response.status).send(_.pick(response, ['success', 'message']));
});

router.post('/updateEmiConsolidatedInformationInLoanInfo', [auth, admin], async (req, res) => {
  const response = await updateEmiConsolidatedInformationInLoanInfo();
  return res.status(response.status).send(_.pick(response, ['success', 'message']));
});


router.post('/maskAadhaarNoOnPerfDB', [auth, admin], async (req, res) => {
  const response = await maskAadhaarNoOnPerfDB();
  return res.status(response.status).send(_.pick(response, ['success', 'message']));
});

router.post('/updateQuestionnairreConfig', [auth, admin], async (req, res) => {
  const response = await updateQuestionnairreConfig();
  return res.status(response.status).send(_.pick(response, ['success', 'message']));
});

router.post('/updateProcessingFeeForExisting', [auth, admin], async (req, res) => {
  const response = await updateProcessingFeeForExisting();
  return res.status(response.status).send(_.pick(response, ['success', 'message']));
});

router.post('/updateWrittenOffInFlowDetails', async (req, res) => {
  const response = await updateWrittenOffInFlowDetails();
  return res.status(response.status).send(_.pick(response, ['WriteOffLoans', 'success', 'message']));
});

router.post('/updatePreApprovalConfig', [auth, admin], async (req, res) => {
  const response = await updatePreApprovalConfig();
  return res.status(response.status).send(_.pick(response, ['success', 'message']));
});

router.post('/updateSecretKeyConfig', [auth, admin], async (req, res) => {
  const response = await updateSecretKeyConfig();
  return res.status(response.status).send(_.pick(response, ['success', 'message']));
});

router.post('/insertNotificationTemplateForNewPartner', async (req, res) => {
  const response = await insertNotificationTemplateForNewPartner(req.body.partnerId, req.body.url);
  return res.status(response.status).send(_.pick(response, ['success', 'message']));
});

router.post('/updatePrincipalOutstanding', [auth, admin], async (req, res) => {
  const response = await updatePrincipalOutstanding();
  return res.status(response.status).send(_.pick(response, ['success', 'message']));
});

router.post('/updateRecoursePending', [auth, admin], async (req, res) => {
  const response = await updateRecoursePending();
  return res.status(response.status).send(_.pick(response, ['data', 'success', 'message']));
});

router.post('/updateProcessingFeePreApproval', [auth, admin], async (req, res) => {
  const response = await updateProcessingFeePreApproval();
  return res.status(response.status).send(_.pick(response, ['data', 'success', 'message']));
});

router.post('/updatePartnerLoans', [auth, admin], async (req, res) => {
  const response = await updatePartnerLoans();
  return res.status(response.status).send(_.pick(response, ['data', 'success', 'message']));
});

router.post('/takeUserBackup', [auth, admin], async (req, res) => {
  const response = await takeUserBackup(req.body.userId);
  return res.status(response.status).send(_.pick(response, ['success', 'message']));
});

router.post('/updateVirtualAccountNo', [auth, admin], async (req, res) => {
  const response = await updateVirtualAccountNo(req.body.list);
  return res.status(response.status).send(_.pick(response, ['data', 'success', 'message']));
});

router.post('/reverseReceivedPayment', [auth, admin], async (req, res) => {
  const response = await reverseReceivedPayment(req.body);
  return res.status(response.status).send(_.pick(response, ['data', 'success', 'message']));
});

router.post("/updateDisbursalConsent", [auth, admin], async (req, res) => {

  const response = await updateDisbursalConsent();

  return res
    .status(200)
    .send(_.pick(response, ["data", "success", "message"]));

});

router.post("/updateTypeInNotificationTemplates", [auth, admin], async (req, res) => {

  const response = await updateTypeInNotificationTemplates();

  return res
    .status(200)
    .send(_.pick(response, ["data", "success", "message"]));

});

router.post("/updateReferenceNoInLoanInstallment", [auth, admin], async (req, res) => {
  const response = await updateReferenceNoInLoanInstallment();

  return res
    .status(200)
    .send(_.pick(response, ["success", "message"]));

});

router.post("/updatePartnerIdInLoanInstallment", [auth, admin], async (req, res) => {
  const response = await updatePartnerIdInLoanInstallment();

  return res
    .status(200)
    .send(_.pick(response, ["success", "message"]));

});

router.post('/addHoldDateStamps', [auth, admin], async (req, res) => {
  const response = await addHoldDateStamps();
  return res.status(response.status).send(_.pick(response, ['data', 'success', 'message']));
});

router.post('/addHoldDateStampsOld', [auth, admin], async (req, res) => {
  const response = await addHoldDateStampsOld();
  return res.status(response.status).send(_.pick(response, ['data', 'success', 'message']));
});

router.post("/updatePartnerBankConfig", [auth, admin], async (req, res) => {
  const response = await updatePartnerBankConfig();

  return res
    .status(200)
    .send(_.pick(response, ["success", "message"]));

});

router.post("/updateReferenceNoInLoanFees", [auth, admin], async (req, res) => {
  const response = await updateReferenceNoInLoanFees();

  return res
    .status(200)
    .send(_.pick(response, ["success", "message"]));

});

router.post('/updateProductIdInPreApproval', [auth, admin], async (req, res) => {
  const response = await updateProductIdInPreApproval(req.body.list);
  return res.status(response.status).send(_.pick(response, ['data', 'success', 'message']));
});

router.post("/insertNotificationTemplate", [auth, admin], async (req, res) => {
  const response = await insertNotificationTemplate(req.body.partnerId, req.body.url);

  return res
    .status(200)
    .send(_.pick(response, ["success", "message"]));

});

router.post("/gstListJob", [auth, admin], async (req, res) => {
  const response = await gstListJob();

  return res
    .status(200)
    .send(_.pick(response, ["total", "inserted", "success", "message"]));

});

router.post("/updateDisbursalDetailsForArthan", [auth, admin], async (req, res) => {
  const response = await updateDisbursalDetailsForArthan();

  return res
    .status(200)
    .send(_.pick(response, ["success", "message"]));

});

router.post("/findDuplicateUserPartner", [auth, admin], async (req, res) => {
  const response = await findDuplicateUserPartner();

  return res
    .status(200)
    .send(_.pick(response, ["success", "message", "Deleted", "DuplicateRecords", "RecordsExists"]));

});

router.post("/uploadTrancheDetails", [auth, admin], async (req, res) => {
  const response = await uploadTrancheDetails(req.body);

  return res
    .status(200)
    .send(_.pick(response, ["success", "message"]));

});

router.post("/cleanDuplicatePanInUserInfo", [auth, admin], async (req, res) => {
  const response = await cleanDuplicatePanInUserInfo();

  return res
    .status(200)
    .send(_.pick(response, ["success", "message"]));

});

router.post("/cleanDuplicateUserPartnerInfo", [auth, admin], async (req, res) => {
  const response = await cleanDuplicateUserPartnerInfo1();

  return res
    .status(200)
    .send(_.pick(response, ["success", "message"]));

});

router.post("/updateMissingMerchantCodeInUserPartnerInfo", [auth, admin], async (req, res) => {
  const response = await updateMissingMerchantCodeInUserPartnerInfo();

  return res
    .status(200)
    .send(_.pick(response, ["success", "message"]));

});

router.post("/getPartnerLoansAndTrueScore", [auth, admin], async (req, res) => {
  const response = await getPartnerLoansAndTrueScore();

  return res
    .status(200)
    .send(_.pick(response, ["success", "message"]));

});

router.post("/updateMissingArrears", [auth, admin], async (req, res) => {
  const response = await updateMissingArrears();

  return res
    .status(200)
    .send(_.pick(response, ["success", "message"]));

});

router.post("/getEarlyPendingEmiLoans", [auth, admin], async (req, res) => {
  const response = await getEarlyPendingEmiLoans();

  return res
    .status(200)
    .send(_.pick(response, ["paidLoans", "success", "message"]));

});

router.post("/updateEmiRecordsInLoanInfo", [auth, admin], async (req, res) => {
  const response = await updateEmiRecordsInLoanInfo();

  return res
    .status(200)
    .send(_.pick(response, ["success", "message"]));

});

router.post("/testingAPI", async (req, res) => {
  const response = { success: true, message: "testing2" };

  return res
    .status(200)
    .send(_.pick(response, ["success", "message"]));

});

module.exports = router;
