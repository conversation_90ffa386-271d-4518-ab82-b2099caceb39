import pandas as pd
import numpy as np
import json
from pandas import json_normalize
import sys

lines = sys.stdin


def calculate_creditlimit(gst_json):
    
   
    a = pd.read_json(gst_json, typ='series')
    
    data = json_normalize(a['entries'])

    data = data.drop_duplicates(['period.start'])

    avg_mon_revenue = data['revenue'].mean()
    turnover = data['revenue'].sum()
    revenue_volatility = data['revenue'].std()/data['revenue'].mean()
    
    LOW_VOLATILITY_THRESHOLD = 1.5
    MEDIUM_VOLATILITY_THRESHOLD = 3
    HIGH_VOLATILITY_THRESHOLD = 4.5
    VERY_HIGH_VOLATILITY_THRESHOLD = 6
    

    if revenue_volatility < LOW_VOLATILITY_THRESHOLD:
        avg_mon_revenue = avg_mon_revenue
    elif revenue_volatility < MEDIUM_VOLATILITY_THRESHOLD:
        avg_mon_revenue *= 0.75
    elif revenue_volatility < HIGH_VOLATILITY_THRESHOLD:
        avg_mon_revenue *= 0.5
    elif revenue_volatility < VERY_HIGH_VOLATILITY_THRESHOLD:
        avg_mon_revenue *= 0.25
    else:
        avg_mon_revenue *= 0.1

    LOW_TURNOVER_THRESHOLD = 150000
    MEDIUM_TURNOVER_THRESHOLD = 1000000
    HIGH_TURNOVER_THRESHOLD = 2000000
    VERY_HIGH_TURNOVER_THRESHOLD = 3000000

    if turnover <= LOW_TURNOVER_THRESHOLD:
        credit_limit = 0
    elif turnover <= MEDIUM_TURNOVER_THRESHOLD:
        credit_limit = 1.2 * avg_mon_revenue
    elif turnover <= HIGH_TURNOVER_THRESHOLD:
        credit_limit = 2 * avg_mon_revenue
    elif turnover <= VERY_HIGH_TURNOVER_THRESHOLD:
        credit_limit = 3 * avg_mon_revenue
    else:
        credit_limit = 3.5 * avg_mon_revenue
    
    credit_limit = round(int(credit_limit),-5)
    
    print(credit_limit)    
    return credit_limit



calculate_creditlimit(lines)