const auth = require('../../../middleware/auth');
const express = require('express');
const GstService = require('../../../services/ops/gst');
const _ = require("lodash");
const XAmbit = require('../../../partnerApiCalls/XAmbit');
const {validateGstData, gstDetailsXambit} = require("../../../models/ops/gst/gstDetails");
const { spawn } = require('child_process');
const router = express.Router();

router.post("/fetchGstDetails",[auth],async(req,res)=>{
   const {gstNo, userId} = req.body;
   const { error } = validateGstData(req.body);

  if (error) return res.status(400).send(error.details[0].message);

   let response = await GstService.fetchGstDetails(gstNo, userId);
   console.log("resp",response)
   return res
   .status(response.status)
   .send(_.pick(response, ["data", "success", "message"]));
})


router.get("/checkGstNoExist/:gstNo",[auth],async(req,res)=>{
   const {gstNo} = req.params;

   let response = await new XAmbit().checkGstNo(gstNo);
   console.log('res here', response)
   
   return res
   .status(200)
   .send(_.pick(response, ["data", "success", "message", "status"]));
})



router.post("/sendOtpForPrivateData",[auth],async(req,res)=>{
   const {gstNo, gstUsername} = req.body;

   let response = await new XAmbit().createNewAnayalysisAndSendOtp(gstNo, gstUsername);

   return res
   .status(response.status)
   .send(_.pick(response, ["data", "message","status"]));
})

router.post("/validateOtp",[auth],async(req,res)=>{
   const { gstNo, gstUsername, otp} = req.body;

   let response = await new XAmbit().validateOtp(gstNo,gstUsername,otp);
   if(response.status===200){
     let analysisResponse= await new XAmbit().gstDataAnalysis(gstNo);
   }
   return res
   .status(response.status)
   .send(_.pick(response, ["data", "success", "message"]));
})

router.post("/createAnalysis",[auth],async(req,res)=>{
   const { gstNo } = req.body;

   let response = await new XAmbit().gstDataAnalysis(gstNo);
   
   return res
   .status(response.status)
   .send(_.pick(response, ["data", "success", "message"]));
})

//hook url for saving xambit private data
router.post("/privateDataWebhook",async(req,res)=>{
   const data = req.body;
  

  console.log('webhook req', req)
  console.log('webhook data', data?.data)
//   let response =  GstService.privateDataWebhook(req.body);
   
   return {
      status : '200',
      message : 'Success'
   }
})


router.get("/gstBasedCreditLimit/:userId",async(req,res)=>{
  

   let resData=""
   let pythonFilePath = 'routes/ops/gst/pythonScript/gstCreditLimit.py';

   let gstData = await gstDetailsXambit.findOne({  userId: req?.params?.userId  })

   let py = spawn('/usr/bin/python3.6', [pythonFilePath]),
       data = gstData,
       dataString = '';


   let reqData = JSON.stringify(data);


   py.stdin.write(reqData);

   py.stdin.end();

   py.stdout.on('data', function (data) {
       dataString += data.toString();

      //  console.log('data sring here', dataString)
   });

   py.stdout.on('close', function () {
      console.log('data string here', dataString)
      resData = JSON.stringify(dataString);
      console.log("resData replace",resData)
      resData = dataString.replace(/\r|\n/g, '');

      return res
      .status(200)
      .send({
         creditLimit : resData,
         message : 'Credit Limit based on GST fetched successfully'
      });
       
   });

   py.stderr.on('error', (err) => {
      
       console.log('errr herer', err.toString())
      return res
      .status(400)
      .send({
         creditLimit : err.toString(),
         message : 'Unable to fetch the Credit Limit based on GST'
      });
   
   });

   
  
})



module.exports = router;