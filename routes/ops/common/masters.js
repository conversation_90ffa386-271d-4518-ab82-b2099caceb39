const auth = require("../../../middleware/auth");
const express = require("express");
const router = express.Router();
const mongoose = require("mongoose");
const {
  CityStateMaster,
  BankBranchMaster,
  validateIfsc
} = require("../../../models/ops/common/master");
const _ = require("lodash");
const bycrypt = require("bcrypt");
const admin = require("../../../middleware/admin");
const {
  getIfscCodes,
  getBankNames,
  getBranchNames,
  getBankfromBranch,
  getStates,
  getStatesByPincode,
  getCities,
  getDefinitionMaster,
  isValidState,
  isValidIfscCode,
  isValidCity,
  isValidGst,
  isValidPAN,
  isValidPOA,
  checkEmail,
  checkMobile,
  isValidUdyogAadhaar,
  checkPrimaryMobile,
  checkPrimaryEmail,
  saveOtpFailureLog,
  getRoleWiseDefinitionList,
  isValidPOANumber,
  getSupportedMandateMethod,
  getConfiguration,
  getInvoiceTypeList
} = require("../../../services/ops/common/masters");

const { pushToNotifications, getSmsModel } = require("../../../services/ops/common/common");
const postDisbursal = require("../../../services/ops/loan/postDisbursal");
const { sendOtp, verifyOtp } = require('../../../services/ops/otp-service/otp-service');
const { OTP_CHANNELS, NOTIFICATION_TYPES } = require('../../../services/ops/common/constants').Constants
const config = require("config");

router.get("/getIfscCode", [auth], async (req, res) => {
  const response = await getIfscCodes(req.query.ifsc);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.get("/getBankNames", [auth], async (req, res) => {
  const response = await getBankNames(req.query.bankName);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.get("/getBankNames", [auth], async (req, res) => {
  const response = await getBankNames("");

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.get(
  "/getBranchNames/:bankName/:branchName",
  [auth],
  async (req, res) => {
    const response = await getBranchNames(
      req.params.bankName,
      req.params.branchName
    );

    return res
      .status(response.status)
      .send(_.pick(response, ["data", "success", "message"]));
  }
);

router.get(
  "/getBankfromBranch/:bankName/:branchName",
  [auth],
  async (req, res) => {
    const response = await getBankfromBranch(
      req.params.bankName,
      req.params.branchName
    );

    return res
      .status(response.status)
      .send(_.pick(response, ["data", "success", "message"]));
  }
);

router.get("/getStates/:state", [auth], async (req, res) => {
  const response = await getStates(req.params.state, req.query.pincode);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.get("/getStatesByPincode", [auth], async (req, res) => {
  const response = await getStatesByPincode(req.query.pincode);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.get("/getCities/:state/:city", [auth], async (req, res) => {
  const response = await getCities(req.params.state, req.params.city, req.query.pincode);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/getDefinitionMaster", async (req, res) => {
  const response = await getDefinitionMaster(req.body);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.get("/isValidState/:state", [auth], async (req, res) => {
  const response = await isValidState(req.params.state, req.query.pincode);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.get("/isValidCity/:state/:city", [auth], async (req, res) => {
  const response = await isValidCity(req.params.state, req.params.city);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.get("/isValidIfsc/:ifsc", [auth], async (req, res) => {
  const response = await isValidIfscCode(req.params.ifsc);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.get("/isValidPAN/:pan/:userId", [auth], async (req, res) => {
  const response = await isValidPAN(req.params.userId, req.params.pan);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.get("/isValidPOA/:poa/:userId", [auth], async (req, res) => {
  const response = await isValidPOA(req.params.userId, req.params.poa);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.get(
  "/isValidUdyogAadhaar/:udyogAadhaar/:userId",
  [auth],
  async (req, res) => {
    const response = await isValidUdyogAadhaar(
      req.params.userId,
      req.params.udyogAadhaar
    );

    return res
      .status(response.status)
      .send(_.pick(response, ["data", "success", "message"]));
  }
);

router.get("/isValidGST/:gst/:userId", [auth], async (req, res) => {
  const response = await isValidGst(req.params.userId, req.params.gst);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.get(
  "/isValidAlternateEmail/:emailId/:userId",
  [auth],
  async (req, res) => {
    const response = await checkEmail(req.params.userId, req.params.emailId);

    return res
      .status(response.status)
      .send(_.pick(response, ["data", "success", "message"]));
  }
);

router.get(
  "/isValidAlternateMobile/:mobileNo/:userId",
  [auth],
  async (req, res) => {
    const response = await checkMobile(req.params.userId, req.params.mobileNo);

    return res
      .status(response.status)
      .send(_.pick(response, ["data", "success", "message"]));
  }
);

router.get("/isValidPrimaryMobile/:mobileNo/:userId", [auth], async (req, res) => {
  const data = await checkPrimaryMobile(req.params.userId, req.params.mobileNo);

  if (data.success) {
    //const response = await sendMobileOTP(req.params.mobileNo);

    // const sendSms = await pushToNotifications(null, null, null, null,
    //   null, null, "SMS", req.params.mobileNo, "OTP", "registration", null, null, null, false, "OTP");

    /** CHANGES START */
    let payload = {
      notifyTo: req.params.mobileNo,
      notificationType: NOTIFICATION_TYPES.KYC_PAGE,
      channel: OTP_CHANNELS.SMS,
      otpLength: 6
    }
    let otpSend = await sendOtp(payload)

    if (otpSend.error) {

      let response = {
        data: "",
        success: false,
        message: otpSend.error.message,
        status: 200
      };


      response.message = otpSend.error && otpSend.error.message || ""
      return res
        .status(response.status)
        .send(_.pick(response, ["data", "success", "message"]));
    }

    let messageType = "OTP_Registeration";
    let smsModel = getSmsModel();

    smsModel.To = req.params.mobileNo;
    smsModel.TemplateName = messageType;
    smsModel.VAR1 = otpSend.response.otp;
    smsModel.VAR2 = "";
    smsModel.Type = "TSMS";
    smsModel.From = config.get("keys.from");

    const sendSms = await pushToNotifications(null, null, null, null,
      null, null, "SMS", req.params.mobileNo, messageType, "registration", null, null, smsModel, false);

    /** CHANGES END */

    //Call sendnotifications
    if (sendSms.success)
      response = await postDisbursal.sendNotifications(sendSms.data._id);

    return res
      .status(response.status)
      .send(_.pick(response, ["data", "success", "message"]));
  } else {
    return res
      .status(400)
      .send({ success: false, message: data.message });
  }
}
);

router.get(
  "/isValidPrimaryEmail/:emailId/:userId",
  [auth],
  async (req, res) => {
    const response = await checkPrimaryEmail(req.params.userId, req.params.emailId);

    return res
      .status(response.status)
      .send(_.pick(response, ["data", "success", "message"]));
  }
);

router.post("/logUserOtp", async (req, res) => {
  const response = await saveOtpFailureLog(req.body.loanId, req.body.otp, req.body.otpMatch, req.body.otpType, req.body.otpAutoFill, req.body.channel);

  return res
    .status(200)
    .send(_.pick(response, ["data", "success", "message"]));
}
);

router.post("/getRoleWiseDefinitionList", [auth], async (req, res) => {
  const response = await getRoleWiseDefinitionList(req.body, req.user.id);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/isValidPOANumber", [auth], async (req, res) => {
  const response = await isValidPOANumber(req.body.userId, req.body.poaNumber, req.body.poaType);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});


router.get("/getSupportedMandateMethod/:ifsc", [auth], async (req, res) => {

  if (!req.params.ifsc) {
    return res
      .status(500)
      .send({ status: 500, message: "IFSC missing" });
  }

  const response = await getSupportedMandateMethod(req.params.ifsc);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.get("/configuration", [auth], async (req, res) => {

  if (!req.query.channel) {
    return res
      .status(500)
      .send({ status: 500, message: "channel missing" });
  }

  const response = await getConfiguration(req.query.channel);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

router.post("/getInvoiceTypeList", [auth], async (req, res) => {
  const response = await getInvoiceTypeList(req.user.id);

  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));
});

module.exports = router;
