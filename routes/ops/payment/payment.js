const auth = require("../../../middleware/auth");
const express = require("express");
const router = express.Router();
const _ = require("lodash");
const {
    getTransactionBatchApi,
    initateBatchPaymentApi,
    confirmBatchPaymentApi,
    getAccountBalance
} = require("../../../services/ops/payment-api/payment-api");


router.post("/transactionBatch", async (req, res) => {
    let output = {
        data: null,
        success: false,
        message: "Oops someting went wrong",
        status: 400,
    };

    output = await getTransactionBatchApi(req);

    return res
        .status(output.status)
        .send(_.pick(output, ["success", "message", "data"]));
});

router.post("/initiateBatchPayment", async (req, res) => {
    let output = {
        success: false,
        message: "Oops someting went wrong",
        status: 400,
    }
    output = await initateBatchPaymentApi(req);
    return res
        .status(output.status)
        .send(_.pick(output, ["success", "message" , "data"]));
});

router.post("/confirmBatchPayment", async (req, res) => {
    let output = {
        success: false,
        message: "Oops someting went wrong",
        status: 400,
    }
    output = await confirmBatchPaymentApi(req);
    return res
        .status(output.status)
        .send(_.pick(output, ["success", "message" , "data"]));
});

router.post("/accountBalance", async (req, res) => {
    let output = {
        success: false,
        message: "Oops someting went wrong",
        status: 400,
    }
    output = await getAccountBalance(req);
    return res
        .status(output.status)
        .send(_.pick(output, ["success", "message" , "data"]));
});




module.exports = router;