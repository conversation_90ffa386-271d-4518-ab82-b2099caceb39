const express = require("express");
const router = express.Router();
const _ = require("lodash");
const { generateCollectionAnalysis, exportWrittenOffCollectionReport, exportWrittenOffLoans, exportProcessingFeesReport, exportLateFeesCollectionReport, exportWrittenOffRecoveryReport, loanClosureReportForBorrowingPartners, loanDisbursalReportForBorrowingPartners, loanClosureReportForFinanceAndOps } = require('../../../services/ops/reports/reports.js')
const { generateDailyCollectionAnalysis } = require('../../../services/ops/reports/daily-collection-analysis');
const { generateCollectionCasesExcel } = require('../../../services/ops/reports/collection-cases');
const { borrowingPartnerCollectionReport } = require('../../../services/ops/reports/borrowing-partner-collection-report');

router.post("/generateWeeklyCollectionAnalysisReport", async (req, res) => {

  let response = await generateCollectionAnalysis(req.body)
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));

});

router.post("/exportWrittenOffCollectionReport", async (req, res) => {
  const response = await exportWrittenOffCollectionReport(req.body);
  return res
    .status(response.status)
    .send(_.pick(response, ["success", "message"]));
});

router.post("/exportWrittenOffLoans", async (req, res) => {
  const response = await exportWrittenOffLoans(req.body);
  return res
    .status(response.status)
    .send(_.pick(response, ["success", "message"]));
});

router.post("/exportProcessingFeesReport", async (req, res) => {
  const response = await exportProcessingFeesReport(req.body);
  return res
    .status(response.status)
    .send(_.pick(response, ["success", "message"]));
});

router.post("/exportLateFeesCollectionReport", async (req, res) => {
  const response = await exportLateFeesCollectionReport(req.body);
  return res
    .status(response.status)
    .send(_.pick(response, ["success", "message"]));
});

router.post("/exportWrittenOffRecoveryReport", async (req, res) => {
  const response = await exportWrittenOffRecoveryReport(req.body);
  return res
    .status(response.status)
    .send(_.pick(response, ["success", "message"]));
});

router.post("/generateDailyCollectionAnalysisReport", async (req, res) => {

  let response = await generateDailyCollectionAnalysis(req.body)
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));

});

router.post("/loanClosureReportForBorrowingPartners", async (req, res) => {

  let response = await loanClosureReportForBorrowingPartners()
  return res
    .status(response.status)
    .send(_.pick(response, ["success", "message"]));

});

router.post("/loanDisbursalReportForBorrowingPartners", async (req, res) => {

  let response = await loanDisbursalReportForBorrowingPartners()
  return res
    .status(response.status)
    .send(_.pick(response, ["success", "message"]));

});

router.post("/loanClosureReportForFinanceAndOps", async (req, res) => {

  let response = await loanClosureReportForFinanceAndOps()
  return res
    .status(response.status)
    .send(_.pick(response, ["success", "message"]));

});


router.post("/generateCollectionCasesExcel", async (req, res) => {

  let response = await generateCollectionCasesExcel(req.body)
  return res
    .status(response.status)
    .send(_.pick(response, ["data", "success", "message"]));

});

router.post("/generateCollectionCasesExcelPartnerWise", async (req, res) => {

  res
    .status(200)
    .send({
      success: true,
      data: {},
      message: "Report generation succesfully initiated"
    });

  await borrowingPartnerCollectionReport(req.body)

});


module.exports = router;