const express = require("express");
const router = express.Router();
const auth = require('../../../middleware/auth');

const {
    voterIdVerification, dlVerification, ocr, ocrVerification, verifyGstData, verifyPanData, sendDigilockerRequest,sendDigilockerRequestInvoiceFinancing,
    updateDigilockerResponse
} = require("../../../services/ops/verification/digioApi");

const {
    updateEnachResponse, sendEnachRequest, debitWebHook, scheduleNextDayRepayments, scheduleFailedRepayments, getDebitResponse,
    applyEnachPenalty, scheduleUnsentRepayments, cancelMandate, getMandateList, mandateCancellationWebHook, updateAadhaarEnachResponse,
    generatePhysicalNachFile, uploadPhysicalNachFile, sendPhysicalNACHLink, cancelMandateJob
} = require("../../../services/ops/mandate/eNach");

router.post("/gstVerification", async (req, res) => {

    const response = await verifyGstData(
        req.body.userId,
        req.body.gstin
    );

    return res
        .status(200)
        .send(response);
});

router.post("/panVerification", async (req, res) => {

    const response = await verifyPanData(
        req.body.userId,
        req.body.pan_no,
        req.body.full_name,
        req.body.date_of_birth
    );

    return res
        .status(200)
        .send(response);
});

router.post("/voterIdVerification", async (req, res) => {

    const response = await voterIdVerification(
        req.body
    );

    return res
        .status(200)
        .send(response);
});

router.post("/dlVerification", async (req, res) => {

    const response = await dlVerification(
        req.body
    );

    return res
        .status(200)
        .send(response);
});

router.post("/ocr", async (req, res) => {

    const response = await ocr(
        req.body
    );

    return res
        .status(200)
        .send(response);
});

router.post("/eNachWebHook", async (req, res) => {

    //console.log(req.body);
    //update eNach Status
    let nachResponse = await updateEnachResponse(req.body);

    return res
        .status(200)
        .send(nachResponse);
});

router.post("/debitWebHook", async (req, res) => {

    //console.log(req.body);
    //update eNach Status
    let response = debitWebHook(req.body);

    return res
        .status(200)
        .send("Saved successfully");
});

router.post("/eNachRequest", async (req, res) => {

    //update eNach Status
    let nachResponse = sendEnachRequest(req.body);

    return res
        .status(200)
        .send({ message: "request received" });
});

router.post("/digioDailyScheduler", async (req, res) => {

    let getPendingResponse = await getDebitResponse();
    //update eNach Status
    if (getPendingResponse.success) {

        let nextDayResponse = await scheduleNextDayRepayments();

        if (nextDayResponse.success) {
            let failCasesResponse = await scheduleFailedRepayments();

            if (failCasesResponse.success) {

                let unSentCasesResponse = await scheduleUnsentRepayments();

                if (unSentCasesResponse.success) {

                    return res.status(200).send({
                        data: "", success: true, message: "Cases scheduled successfully"
                    })
                }
                else {
                    return res
                        .status(400)
                        .send(unSentCasesResponse);
                }
            }
            else {
                return res
                    .status(400)
                    .send(failCasesResponse);
            }
        }
        else {
            return res
                .status(400)
                .send(nextDayResponse);
        }
    }
    else {
        return res
            .status(400)
            .send(getPendingResponse);
    }

});

router.post("/getDebitResponse", async (req, res) => {

    //update eNach Status
    let response = await getDebitResponse(req.body);

    return res
        .status(200)
        .send(response);
});

router.post("/cancelMandate", async (req, res) => {

    //update eNach Status
    let response = await cancelMandate(req.body);

    return res
        .status(200)
        .send(response);
});

router.get("/cancelMandateJob", async (req, res) => {

    //update eNach Status
    let response = await cancelMandateJob();

    return res
        .status(200)
        .send(response);
});

router.post("/getMandateList", async (req, res) => {

    //update eNach Status
    let response = await getMandateList(req.body.userId);

    return res
        .status(200)
        .send(response);
});

router.post("/mandateCancellationWebHook", async (req, res) => {

    //update eNach Status
    let response = await mandateCancellationWebHook(req.body);

    return res
        .status(200)
        .send(response);
});

router.post("/aadhaarMandateWebhook", async (req, res) => {

    let nachResponse = await updateAadhaarEnachResponse(req.body);

    return res
        .status(200)
        .send({ message: "request received" });
});

router.post("/digilockerWebhook", async (req, res) => {

    console.log(req.body);
    //update eNach Status
    //let response = debitWebHook(req.body);

    return res
        .status(200)
        .send("Saved successfully");
});

router.post("/sendDigilockerRequest", [auth], async (req, res) => {

    let response = await sendDigilockerRequest(req.body.loanId, req);

    return res
        .status(200)
        .send(response);
});

router.post("/sendDigilockerRequestInvoiceFinancing", async (req, res) => {

    let response = await sendDigilockerRequestInvoiceFinancing(req.body.loanId, req);

    return res
        .status(200)
        .send(response);
});

router.post("/generatePhysicalNachFile", [auth], async (req, res) => {

    const response = await generatePhysicalNachFile(req.body.loanId, req);

    return res
        .status(200)
        .send(response);
});

router.post("/uploadPhysicalNachFile", [auth], async (req, res) => {

    const response = await uploadPhysicalNachFile(req.body.loanId, req.body.pNachFile, req.body.pNachFileStream, req.user.id, req);

    return res
        .status(200)
        .send(response);
});

router.post("/sendPhysicalNACHLink", [auth], async (req, res) => {

    const response = await sendPhysicalNACHLink(req.body.loanId, req);

    return res
        .status(200)
        .send(response);
});

module.exports = router;