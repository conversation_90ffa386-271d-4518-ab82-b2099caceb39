const config = require("config");
const DB = require("../../database/factory");
const { saveErrorLog } = require("../ops/common/masters");
const { queryBuilder } = require("./queryBuilderLib");
const apiPath = __dirname;

async function insertBatch(collection, documents) {
    try {
        let bulkInsert = collection.initializeUnorderedBulkOp();
        let insertedIds = [];
        let id;
        documents.forEach(function (doc) {
            id = doc._id;
            // Insert without raising an error for duplicates
            bulkInsert.find({ _id: id }).upsert().replaceOne(doc);
            insertedIds.push(id);
        });
        await bulkInsert.execute();
        return insertedIds;
    } catch (ex) {
        saveErrorLog("dbPurge-insertBatch", apiPath, { collection }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        return [];
    }
}

async function deleteBatch(collection, documents) {
    try {
        let bulkRemove = collection.initializeUnorderedBulkOp();
        documents.forEach(function (doc) {
            bulkRemove.find({ _id: doc._id }).deleteOne();
        });
        await bulkRemove.execute();
    } catch (ex) {
        saveErrorLog("dbPurge-deleteBatch", apiPath, { collection: collection.collectionName }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }
}

async function moveDocuments(sourceCollection, targetCollection, filter, batchSize = 500) {
    try {

        let count;
        while ((count = await sourceCollection.find(filter).count()) > 0) {

            let sourceDocs = await sourceCollection.find(filter).limit(batchSize).toArray();
            let idsOfCopiedDocs = []
            if (targetCollection) {
                idsOfCopiedDocs = await insertBatch(targetCollection, sourceDocs);
                let targetDocs = await targetCollection.find({ _id: { $in: idsOfCopiedDocs } }).toArray();
                await deleteBatch(sourceCollection, targetDocs);
            }
            else {
                await deleteBatch(sourceCollection, sourceDocs);
            }


        }
        //console.log("Completed!" + sourceCollection.collectionName);
        //saveErrorLog("dbPurge - Completed", apiPath, { sourceCollection: sourceCollection ? sourceCollection.collectionName : "" }, "Completed!" + sourceCollection.collectionName + " " + count);

    } catch (ex) {
        saveErrorLog("dbPurge-moveDocuments", apiPath, { sourceCollection: sourceCollection ? sourceCollection.collectionName : "", targetCollection: targetCollection ? targetCollection.collectionName : "", filter, batchSize }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }
}

module.exports.dbPurge = async function () {

    try {

        //let database = DB.get(DB.FUNDFINA);
        const database = DB.get(DB.FUNDFINA);
        const ConfigParameters = DB.getCollection(DB.FUNDFINA, "ConfigParameters");

        let purgeConfig = await ConfigParameters.find({ channel: "DBPURGE" }).toArray();

        let collectionList = purgeConfig && purgeConfig.length ? purgeConfig[0].collectionsList : [];

        console.log(collectionList);
        for (let i = 0; i < collectionList.length; i++) {
            let collectionInfo = collectionList[i];
            let target = {}
            let filterQuery = queryBuilder(collectionInfo.filter, target);
            //let collectionDatabase = DB.get(DB.FUNDFINA);
            let sourceCollection = DB.getCollection(collectionInfo.db, collectionInfo.sourceCollection);
            let targetCollection = collectionInfo.targetCollection ? DB.getCollection(collectionInfo.db, collectionInfo.targetCollection) : "";

            console.log(filterQuery);
            await moveDocuments(sourceCollection, targetCollection, filterQuery, collectionInfo.batchSize);
        }
    } catch (ex) {
        saveErrorLog("dbPurge", apiPath, {}, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }

};

