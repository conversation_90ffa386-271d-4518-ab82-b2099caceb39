
function queryBuilder(config, target) {

    let destinationKeys = Object.keys(config);

    for (let i = 0; i < destinationKeys.length; i++) {

        let key = destinationKeys[i]

        if (typeof config[key] == 'object') {

            target[key] = {};
            queryBuilder(config[key], target[key]);

        } else {

            let value = config[key]
           
            if (typeof value === 'string' && value.includes("__DAYS")) {

                let days = config[key].split("|");

                target[key] = new Date((new Date().getTime() - ((+days[1]) * 24 * 60 * 60 * 1000)));

            } else {

                try {
                    target[key] = config[key]
                }
                catch (err) {
                    target[key] = "";
                }

            }
        }

    }
    return target
}


module.exports = {
    queryBuilder
}