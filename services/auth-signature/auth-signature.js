let Crypto = require("crypto-js");

class Middleware { //TODO : Name has to be change
    static convertArrayOfObjectToStringForHmac(objs) {

        let message = "";

        for (let i = 0; i < objs.length; i++) {

            message += Middleware.convertObjectToStringForHmac(objs[i], "");
        }

        return '[' + message + ']';
    }


    static convertObjectToStringForHmac(obj, message = "") {

        if (obj && Middleware.isObject(obj)) {

            let keys = Object.keys(obj);
            keys.sort();
            message += "{";
            for (let key of keys) {

                let value = obj[key]
                if (!Middleware.isObject(value) || value instanceof Date)
                    message += `"${key}":"${typeof value == "string" ? value.replace(/\s/g, '') : value}",`
                else
                    message += Middleware.convertObjectToStringForHmac(value, `"${key}":`) + ",";
            }
            message = message.substr(0, message.length - 1) + '}';
            return message;
        }

        return "";
    }

    static generateAuthSignature(payload, authToken) {

        try {
            let message = "";
            if (Array.isArray(payload))
                message = Middleware.convertArrayOfObjectToStringForHmac(payload);
            else
                message = Middleware.convertObjectToStringForHmac(payload);

            const calculatedSignature = Crypto.HmacSHA256(message, authToken).toString();

            return calculatedSignature;
        } catch (error) {
            console.log(error)
        }
    }


    static isObject(val) {
        return typeof val == 'object';
    }
}

module.exports = Middleware