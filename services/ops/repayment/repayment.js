const config = require("config");
const { saveErrorLog } = require("../../../services/ops/common/masters");
var XLSX = require('xlsx');
const fs = require("fs");
const multer = require('multer');
const { ManualQueue } = require("../../../models/ops/settlement/settlement");
const { PaymentCollection } = require("../../../models/ops/loan/postDisbursal");
const { AgentCollections } = require("../../../models/ops/agent/agent");
const AuthSignature = require('../../auth-signature/auth-signature');
const installmentService = require("../loan/installments");
const { LoanInstallments, LoanFees, LoanInfo } = require("../../../models/ops/loan/loan");
const fetch = require("node-fetch");
const { pushToNotifications, getMailModel } = require("../../../services/ops/common/common");
const { UserInfo } = require("../../../models/ops/registration/users");
const postDisbursal = require("../../../services/ops/loan/postDisbursal");
const apiPath = __dirname;
const admin = require('../admin/admin');
const { MailerConfig } = require("../../../models/ops/common/mailer");
const { sendExternalMail } = require("../common/mailer");
const moment = require('moment');

async function uploadPaymentReconExcel(req, res) {
    let output = {
        success: false,
        message: "Error Occurred",
        status: 200
    }
    return new Promise((resolve) => {
        try {
            let path = config.get("keys.tempPath");
            let fileOriginalName = "";
            let fileName = "";
            let filePath = "";
            let extension = "";

            let storage = multer.diskStorage({
                destination: function (req, file, cb) {
                    cb(null, path);
                },
                filename: function (req, file, cb) {
                    let i = 1;
                    extension = file.originalname.substring(file.originalname.lastIndexOf('.') - 1 + 1);

                    fileName = file.originalname.substring(0, file.originalname.lastIndexOf('.'));
                    fileOriginalName = fileName + extension;
                    filePath = path + "//" + fileName + extension;
                    while (fs.existsSync(filePath)) {
                        fileName = file.originalname.substring(0, file.originalname.lastIndexOf('.')) + "_" + i.toString();
                        filePath = path + "//" + fileName + extension;
                        i++;
                    }
                    cb(null, fileName + extension);
                }
            });

            let upload = multer({ storage: storage }).single('file')

            upload(req, res, async function (err) {
                if (err instanceof multer.MulterError) {
                    output.message = err;
                    resolve(output);
                } else if (err) {
                    output.message = err;
                    resolve(output);
                } else {
                    const today = new Date(Date.now());
                    let currentMonth = today.getMonth() + 1;
                    currentMonth = currentMonth < 9 ? '0' + currentMonth : currentMonth;

                    let currentDay = today.getDate();
                    currentDay = currentDay < 9 ? '0' + currentDay : currentDay;

                    let hours = today.getHours();
                    hours = hours < 9 ? '0' + hours : hours;

                    let minutes = today.getMinutes();
                    minutes = minutes < 9 ? '0' + minutes : minutes;

                    let seconds = today.getSeconds();
                    seconds = seconds < 9 ? '0' + seconds : seconds;

                    fileName = req.body.type + "_" + currentMonth + currentDay + hours + minutes + seconds;
                    let NewfilePath = path + "//" + fileName + extension;
                    let i = 1;


                    while (!fs.existsSync(filePath)) {
                        await sleep(5000);
                    }

                    while (fs.existsSync(NewfilePath)) {
                        const NewfileName = fileName + "_" + i.toString();
                        NewfilePath = path + "//" + NewfileName + extension;
                        i++;
                    }

                    let response = null;
                    fs.rename(filePath, NewfilePath, async () => {
                        let index = 0;
                        let workbook = XLSX.readFile(NewfilePath);
                        let sheet_name_list = workbook.SheetNames;
                        let xlData = XLSX.utils.sheet_to_json(workbook.Sheets[sheet_name_list[0]]);


                        let checkColumns = ["Transaction ID", "Value Date", "Txn Posted Date", "Description", "Transaction Amount(INR)"];
                        let receivedColumns = [];

                        for (const key in xlData[0]) {
                            if (checkColumns.includes(key)) {
                                receivedColumns.push(key);
                            }
                        }


                        if (checkColumns.length != receivedColumns.length) {
                            output.message = "Columns \"" + checkColumns.join(",") + "\" are mandatory.";
                            if (NewfilePath) {
                                fs.unlink(NewfilePath, function () {
                                    //logger.logs("file deleted successfully");
                                });
                            }
                            let message = `<div>
                            <p><b>**PROCESSING FAILED**</b></p>
                            <p><b>FileName : </b> ${fileOriginalName}</p>
                            <p><b>Error Reason : </b> ${"Columns \"" + checkColumns.join(",") + "\" are mandatory."}</p>
                            <div>`;
                            sendEmail(message);

                            resolve(output);
                            return outout
                        }

                        let transactionsList = [];
                        let referenceNos = [];
                        for (let i = 0; i < xlData.length; i++) {
                            let description = xlData[i][checkColumns[3]];

                            let { error: descriptionExtractorError, response: data } = descriptionValidateAndDataExtractor(description);

                            if (descriptionExtractorError) {
                                output = {
                                    success: false,
                                    message: "Description extractor error",
                                    status: 400
                                }
                                let message = `<div>
                                <p><b>**PROCESSING FAILED**</b></p>
                                <p><b>FileName : </b> ${fileOriginalName}</p>
                                <p><b>Error Reason : </b> ${"Description extractor error"}</p>
                                <div>`;
                                sendEmail(message);
                                resolve(output);
                                return output
                            }

                            if (data) {
                                transactionsList.push({
                                    transactionDate: xlData[i][checkColumns[1]],
                                    amount: xlData[i][checkColumns[4]],
                                    description: xlData[i][checkColumns[3]],
                                    postingDate: xlData[i][checkColumns[2]],
                                    ...data
                                })
                                referenceNos.push(data.referenceNo)
                            }
                        }


                        if (NewfilePath) {
                            fs.unlink(NewfilePath, function () {
                                //logger.logs("file deleted successfully");
                            });
                        }

                        if (!transactionsList.length) {
                            output = {
                                data: {},
                                message: "Success",
                                success: true,
                                status: 200
                            }
                            resolve(output);
                            return output;
                        }

                        let { error: paymentLinksError, response: paymentLinksResponse } = await getPaymentFromPaymentDB(referenceNos);

                        if (paymentLinksError) {
                            output.message = paymentLinksError
                            return output;
                        }

                        let paymentLinkResponseHash = {};
                        for (let i = 0; i < paymentLinksResponse.length; i++) {
                            paymentLinkResponseHash[paymentLinksResponse[i].referenceNo] = paymentLinksResponse[i];
                        }

                        let paymentCollectionRes = await PaymentCollection.find({ referenceNo: { $in: referenceNos } });

                        let paymentCollectionResHash = {}
                        for (let i = 0; i < paymentCollectionRes.length; i++) {
                            paymentCollectionResHash[paymentCollectionRes[i].referenceNo] = paymentCollectionRes[i];
                        }

                        for (let i = 0; i < transactionsList.length; i++) {
                            let transaction = transactionsList[i];
                            let paymentDocument = paymentCollectionResHash[transaction.referenceNo];

                            if (transaction.transactionDate)
                                transaction.transactionDate = '' + transaction.transactionDate

                            let paymentLinkData = paymentLinkResponseHash[transactionsList[i].referenceNo];

                            if (paymentLinkData && paymentLinkData.amount != transaction.amount) {
                                let message = `<div>
                                    <p><b>**AMOUNT MIS MATCH**</b></p>
                                    <p><b>FileName : </b> ${fileOriginalName}</p>
                                    <p><b>LoanNo : </b> ${paymentLinkData.loanNo}</p>
                                    <p><b>EDI Number(s) : </b> ${paymentLinkData.emis && paymentLinkData.emis.join()}</p>
                                    <p><b>ReferenceNo : </b>${paymentLinkData.referenceNo}</p>
                                    <p><b>File Amount : </b> ${transaction.amount}</p>
                                    <p><b>Payment Link Amount : </b> ${paymentLinkData.amount}</p>
                                <div>`;
                                sendEmail(message);
                                continue;
                            }

                            if (paymentLinkData && paymentLinkData.status == "CANCELLED") {
                                let message = `<div>
                                    <p><b>**CANCELLED PAYMENT**</b></p>
                                    <p><b>FileName : </b> ${fileOriginalName}</p>
                                    <p><b>LoanNo : </b> ${paymentLinkData.loanNo}</p>
                                    <p><b>EDI Number(s) : </b> ${paymentLinkData.emis && paymentLinkData.emis.join()}</p>
                                    <p><b>ReferenceNo : </b>${paymentLinkData.referenceNo}</p>
                                    <b>Need to do manual adjustments</b>
                                <div>`;
                                sendEmail(message);
                            }

                            /** Not found matching ref number in payment collection */
                            if (!paymentDocument || (transaction.transactionDate && new Date(transaction.transactionDate) == "Invalid Date")) {

                                let manualQueueReason = !paymentDocument ? "RefereceNo not found/Invalid Reference No" : (transaction.transactionDate && new Date(transaction.transactionDate) == "Invalid Date") && "Invalid Transaction Date";
                                let { error: manualQueueError, response: manualQueueResponse } = await prepareInsertManualQueuePayload(transactionsList[i], manualQueueReason);
                                if (manualQueueError) {
                                    output.message = "Insert Manual Queue Error"
                                    resolve(output);
                                    return output
                                }
                            }

                            /** Found match ref number with payment collection and not marked already paid */
                            if (paymentDocument && paymentDocument.isActive && !paymentDocument.isPaid) {
                                let { error: handleLoanRepaymentsError, response: handleLoanRepaymentsResponse } = await handleLoanRepayments(transaction.referenceNo, transaction, req)
                                if (handleLoanRepaymentsError) {
                                    output.message = "Update payment collection failed";
                                    resolve(output);
                                    return output
                                }
                            }
                        }

                        let message = `<div>
                        <p><b>**PROCESSING COMPLETED**</b></p>
                        <p><b>FileName : </b> ${fileOriginalName}</p>
                    <div>`;
                        sendEmail(message);

                        output = {
                            data: {},
                            message: "Success",
                            success: true,
                            status: 200
                        }
                        resolve(output);
                        return output
                    });
                }
            });
        }
        catch (ex) {
            //logger.logs(ex);
            saveErrorLog("uploadDisbursalExcel", apiPath, null, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
            let message = `<div>
            <p><b>**PROCESSING FAILED**</b></p>
            <p><b>Error Reason : </b> ${ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex}</p>
            <div>`;
            sendEmail(message);
            output.message = ex.message;
            resolve(output);
        }
    });
};


function descriptionValidateAndDataExtractor(description) {
    try {
        let split = description.split("/");

        if (split.length > 3 && split[0].includes("UPI")) {

            let senderInfo = ""
            for (let i = 0; i < split.length; i++) {

                if (i > 2)
                    senderInfo += split[i];
            }

            let data = {
                utrNo: split[1],
                referenceNo: split[2],
                senderInfo
            }
            return { error: null, response: data }
        }
        return { error: null, response: null }
    } catch (ex) {
        saveErrorLog("uploadDisbursalExcel", apiPath, null, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        return { error: ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex, response: null }
    }
}

async function prepareInsertManualQueuePayload(payload, manualQueueReason) {

    try {
        let now = new Date()
        let insertObj = {
            receiverCode: "",
            amount: payload.amount,
            currency: "INR",
            receiverName: "Fundfina",
            receiverAccountNumber: "",
            transactionType: "AMOUNT_COLLECTED",
            creditedDate: payload.transactionDate,
            vaNumber: "",
            utrNo: payload.utrNo,
            senderName: payload.senderInfo,
            senderAccountNo: "",
            status: "PENDING",
            referenceNo: payload.referenceNo,
            narration: payload.description,
            postingDate: payload.postingDate,
            channel: "UPI",
            manualQueueReason,
            insertedOn: now,
            updatedOn: now
        }

        await ManualQueue.insertMany([insertObj], { ordered: false })
        return { error: null, response: {} }
    } catch (ex) {
        saveErrorLog("uploadDisbursalExcel", apiPath, null, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        return { error: null, response: {} }
    }
}

async function handleLoanRepayments(referenceNo, transaction, req) {
    try {
        const paymentCollectionRes = await PaymentCollection.find({ referenceNo, isActive: true, isPaid: false });

        if (!paymentCollectionRes.length) {
            return { error: "No Matching Payment Collection found", response: null }
        }

        let edis = []
        let now = new Date();
        let paidOn = new Date(transaction.transactionDate) && new Date(transaction.transactionDate) != "Invalid Date" ? new Date(transaction.transactionDate) : now
        let updatePayload = {
            isActive: false,
            isPaid: true,
            paidOn,
            updatedOn: now
        }
        const paymentCollectionUpdateRes = await PaymentCollection.updateMany({ referenceNo }, { $set: updatePayload });

        for (let i = 0; i < paymentCollectionRes.length; i++) {
            const paymentCollection = paymentCollectionRes[i];
            const updateEmiPaidStatusResponse = await installmentService.updateEmiPaidStatus(paymentCollection.loanId, paymentCollection.emiNumber, paymentCollection.totalRepayAmount, paidOn, paymentCollection.channel, "System", paymentCollection.referenceNo);
            edis.push(paymentCollection.emiNumber)
            console.log(updateEmiPaidStatusResponse)
        }

        let { error: updateSettledError, response: updateSettledResponse } = await updateLoanInstallmentSettled(paymentCollectionRes[0].loanNo, edis, transaction, paymentCollectionRes[0].referenceNo, req)
        if (updateSettledError) {
            return { error: updateSettledError, response: null }
        }

        let { error: updateAgentCollectionError, response: updateAgentCollectionResponse } = await updateAgentCollections(referenceNo, transaction.postingDate);

        if (updateAgentCollectionError) {
            return { error: updateAgentCollectionError, response: null }
        }

        let { error: insertTransactionError, response: insertTransactionResponse } = await insertSettlementTransaction({
            amount: transaction.amount,
            count: paymentCollectionRes.length,
            partnerId: paymentCollectionRes[0].partnerId,
            channel: paymentCollectionRes[0].channel,
            utrNo: transaction.utrNo,
            orderId: transaction.orderId
        })

        if (insertTransactionError) {
            return { error: insertTransactionError, response: null }
        }

        let { error: updateLinkStatusFailed, response: updateLinkStatusSuccess } = await updatePaymentLinkStatus(paymentCollectionRes[0].referenceNo, "PAID")
        if (updateLinkStatusFailed) {
            return { error: updateLinkStatusFailed, response: null }
        }
        // if (paymentCollectionRes[0].channel == "UPI") {
        //     let { error: linkInvalidateError, response: linkValidateResponse } = await makeInvalidPaymentLink(paymentCollectionRes[0].referenceNo, paymentCollectionRes[0].loanStatus)

        //     if (linkInvalidateError) {
        //         return { error: linkInvalidateError, response: null }
        //     }
        // }


        let mobileNo = updateLinkStatusSuccess && updateLinkStatusSuccess.payment && updateLinkStatusSuccess.payment.meta && updateLinkStatusSuccess.payment.meta.mobileNo;
        await sendNotificationPaymentSuccess(paymentCollectionRes[0], mobileNo);
        return { error: null, response: {} }
    } catch (ex) {
        saveErrorLog("handleLoanRepaymentsManualQueue", apiPath, {}, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        return { error: ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex, response: null }
    }
}

async function updatePaymentLinkStatus(referenceNo, status) {
    try {
        let model = {
            referenceNo,
            status
        }
        let paymentService = config.get("paymentService");
        const signature = AuthSignature.generateAuthSignature(model, paymentService.authToken)
        let headers = {
            'Content-Type': 'application/json',
            "auth-signature": signature,
            "account-id": paymentService.accountId
        }
        let url = `${paymentService.baseUrl}${paymentService.updatePaymentLinkStatus}`;
        let response = await fetch(url, { method: 'POST', headers, body: JSON.stringify(model) })
        let data = await response.json();
        if (data && data.success) {
            return { error: null, response: data.data || {} }
        } else
            return { error: "Update Payment Status failed", response: null }

    } catch (error) {
        saveErrorLog("updatePaymentLinkStatus", apiPath, {}, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        return { error: ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex, response: null }

    }
}


async function updateLoanInstallmentSettled(loanNo, edis, transaction, referenceNo, req) {
    try {

        let now = new Date();
        let updateQuery = {
            loanNo,
            installmentNumber: { $in: edis }
        }
        let updateObj = {
            $set: {
                enterprisePartnerSettlementInfo: {
                    collecedOn: transaction.transactionDate,
                    utrNo: transaction.utrNo,
                    referenceNo: referenceNo
                },
                enterprisePartnerSettlementStatus: "RECEIVED",
                enterprisePartnerSettlementDate: transaction.transactionDate && new Date(transaction.transactionDate) && new Date(transaction.transactionDate) != "Invalid Date" ? new Date(transaction.transactionDate) : new Date(),
                enterprisePartnerSettlementUpdatedBy: req.user && req.user.id || "System",
                enterprisePartnerSettlementUpdatedOn: now
            }
        }

        await LoanInstallments.updateMany(updateQuery, updateObj);
        await LoanFees.updateMany(updateQuery, updateObj);
        return { error: null, response: {} }

    } catch (ex) {
        saveErrorLog("updateLoanInstallmentSettled", apiPath, {}, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        return { error: ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex, response: null }
    }
}

async function updateAgentCollections(referenceNo, postingDate) {
    try {

        let updateQuery = { paymentReferenceNumber: referenceNo }
        let updateObj = {
            $set: {
                status: "Paid",
                updatedOn: new Date(),
                postingDate: postingDate && new Date(postingDate) != "Invalid Date" && new Date(postingDate) || ""
            }
        }

        await AgentCollections.updateOne(updateQuery, updateObj)
        return { error: null, response: {} }
    } catch (ex) {
        saveErrorLog("updateAgentCollections", apiPath, {}, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        return { error: ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex, response: null }
    }
}

async function insertSettlementTransaction(payload) {

    try {

        let reconServiceConfig = config.get("reconService");
        const signature = AuthSignature.generateAuthSignature(payload, reconServiceConfig.secretKey);

        let headers = {
            'Content-Type': 'application/json',
            "auth-signature": signature
        }

        let url = reconServiceConfig.baseUrl + reconServiceConfig.createSettlementTransaction;
        let response = await fetch(url, { method: 'POST', headers, body: JSON.stringify(payload) })
        let data = await response.json();
        if (data && data.success) {
            return { error: null, response: {} }
        }
        return { error: "No Response Found", response: null }
    } catch (ex) {
        saveErrorLog("insertSettlementTransaction", apiPath, {}, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        return { error: ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex, response: null }
    }
}

async function makeInvalidPaymentLink(referenceNo, status) {
    try {

        if (status == "Written-Off") {
            let gateway = "fundfina";
            let model = {
                referenceNo,
                gateway
            }
            let paymentService = config.get("paymentService");
            const signature = AuthSignature.generateAuthSignature(model, paymentService.authToken)
            let headers = {
                'Content-Type': 'application/json',
                "auth-signature": signature,
                "account-id": paymentService.accountId
            }
            let url = `${paymentService.baseUrl}${paymentService.cancelPaymentLink}`;
            let response = await fetch(url, { method: 'POST', headers, body: JSON.stringify(model) })
            let data = await response.json();
            if (data) {
                return { error: null, response: {} }
            }
            return { error: "Link expiry failed", response: null }
        } else {
            return { error: null, response: {} }
        }
    } catch (error) {
        saveErrorLog("makeInvalidPaymentLink", apiPath, {}, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        return { error: ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex, response: null }

    }
}

async function handleVaAccountSettlement(model, req, res) {
    let output = {
        success: false,
        message: "Internal error",
        status: 400
    }
    try {

        let referenceNo = model.referenceNo;

        if (!referenceNo) {
            output.message = "Missing referenceNo"
            return output;
        }
        let paymentCollectionRes = await PaymentCollection.findOne({ referenceNo });
        if (!paymentCollectionRes) {
            model.meta = {
                referenceNo,
                collectedFrom: "FIELD_AGENT"
            }

            let manualQueueRes = await admin.createManualQueue(model);
            if (!manualQueueRes || !manualQueueRes.success) {
                saveErrorLog("handleVaAccountSettlement", apiPath, { model, response: manualQueueRes }, manualQueueRes.message || "Create Manual Queue Failed");
                output.message = "Create Manual Queue Failed";
                return output;
            }
        }

        /** Found match ref number with payment collection and not marked already paid */
        if (paymentCollectionRes && paymentCollectionRes.isActive && !paymentCollectionRes.isPaid) {
            model.transactionDate = model.creditDate;
            model.postingDate = model.postingDate || model.creditDate;
            let { error: handleLoanRepaymentsError, response: handleLoanRepaymentsResponse } = await handleLoanRepayments(referenceNo, model, req)
            if (handleLoanRepaymentsError) {
                saveErrorLog("handleVaAccountSettlement", apiPath, { model, response: manualQueueRes }, manualQueueRes.message || "Create Manual Queue Failed");
                output.message = "handleLoanRepayments Failed";
                return output;
            }
        }

        output = {
            data: {},
            success: true,
            message: "Success",
            status: 200
        }

        return output

    } catch (error) {
        saveErrorLog("handleVaAccountSettlement", apiPath, {}, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex;
        return output;
    }
}

async function sendNotificationPaymentSuccess(paymentCollectionRes, mobileNo) {
    try {

        let loanInfoRes = await LoanInfo.findOne({ _id: paymentCollectionRes.loanId });

        if (!loanInfoRes) {
            saveErrorLog("sendNotificationPaymentSuccess", apiPath, {}, "Invalid LoanId");
            return { error: "Invalid LoanId", response: null }
        }

        let userInfoRes = await UserInfo.findOne({ _id: loanInfoRes.userId });
        if (!userInfoRes) {
            saveErrorLog("sendNotificationPaymentSuccess", apiPath, {}, "Invalid UserId");
            return { error: "Invalid UserId", response: null };
        }

        let primaryMobileNo = userInfoRes && userInfoRes.userDetails && userInfoRes.userDetails.mobileNo && userInfoRes.userDetails.mobileNo.length && userInfoRes.userDetails.mobileNo.filter(item => item.isPrimary);

        if (!mobileNo)
            primaryMobileNo = mobileNo;

        let payload = {
            amount: paymentCollectionRes.totalRepayAmount,
            loanNo: paymentCollectionRes.loanNo,
            partnerName: paymentCollectionRes.partner
        }

        const whatsAppResponse = await pushToNotifications(paymentCollectionRes.partnerId, loanInfoRes.merchantCode, paymentCollectionRes.productId, paymentCollectionRes.trxnId, userInfoRes._id, paymentCollectionRes.loanId, "WhatsApp", mobileNo, "payment_confirmation_v1", "loan", null, null, null, false, null, null, null, null, payload);
        //Call sendnotifications
        if (whatsAppResponse.success)
            await postDisbursal.sendNotifications(whatsAppResponse.data._id, null, payload);

        return { error: null, response: {} }
    } catch (ex) {
        saveErrorLog("sendNotificationPaymentSuccess", apiPath, {}, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        return { error: ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex, response: null }
    }
}

async function sendEmail(html) {
    try {
        const mailerConfig = await MailerConfig.findOne({ name: "Payment Recon File Upload" })

        if (mailerConfig) {
            let mailModel = getMailModel();
            mailModel.to = mailerConfig.to
            mailModel.cc = mailerConfig.cc
            mailModel.date = moment().format("DD/MM/YYYY")
            mailModel.htmlBody = html

            const mailResponse = await sendExternalMail(mailModel, "Payment Recon File Upload", "Payment Recon File Upload");
        }
    } catch (error) {
        saveErrorLog("sendEmail", apiPath, {}, error.stack ? error.stack.toString().replace(/\r?\n|\r/g, " ") : error);
        return { error: error.stack ? error.stack.toString().replace(/\r?\n|\r/g, " ") : error, response: null }
    }
}

async function getPaymentFromPaymentDB(referenceNos) {
    try {
        let paymentService = config.get("paymentService");
        let payload = {
            referenceNo: referenceNos.join(),
            limit: '500'
        }
        const signature = AuthSignature.generateAuthSignature(payload, paymentService.authToken)
        let headers = {
            'Content-Type': 'application/json',
            "auth-signature": signature,
            "account-id": paymentService.accountId
        }
        let url = `${paymentService.baseUrl}${paymentService.paymentLinkList}?referenceNo=${referenceNos.join()}&limit=500`;
        let response = await fetch(url, { method: 'GET', headers })
        let data = await response.json();
        if (data && data.success) {
            return { error: null, response: data.data }
        } else
            return { error: data.message || "Internal error", response: null }
    } catch (ex) {
        saveErrorLog("getPaymentFromPaymentDB", apiPath, {}, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        return { error: ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex, response: null }
    }
}

module.exports = {
    uploadPaymentReconExcel,
    handleVaAccountSettlement,
    sendNotificationPaymentSuccess,
    handleLoanRepayments
}