const mongoose = require("mongoose");
const transactions = mongoose.transactions;
const debug = require("debug")("app:awsServices");
const fs = require("fs");
const config = require("config");
const IsNullEmpty = require("isnullemptyorwhitespace");
const { NotificationsList } = require("../../../models/ops/partner/partner");
const { saveErrorLog } = require("../../../services/ops/common/masters");
const Logger = require("../../../middleware/logger");
const logger = new Logger();
const apiPath = __dirname;

module.exports.emailWebHook = async function (headers, params, body) {
    let output = {
        success: false,
        message: "",
        status: 501,
    };
    try {
        //logger.logs("headers :", headers);

        //logger.logs("params :", params);

        const _body = JSON.parse(body);
        if (_body.eventType == "Send") {
            logger.logs("Event Send");
        } else if (_body.notificationType == "Delivery") {
            logger.logs("Notification Delivery");
        } else if (_body.eventType == "Delivery") {
            logger.logs("Event Delivery");
            let messageTag = null;
            let deliveredOn = null;
            let recipients = null;
            if (!IsNullEmpty(_body.mail.tags.tagName) && _body.mail.tags.tagName.length > 0) {
                messageTag = _body.mail.tags.tagName[0];
            }

            if (!IsNullEmpty(_body.delivery.timestamp)) {
                deliveredOn = _body.delivery.timestamp;
            }

            if (!IsNullEmpty(_body.delivery.recipients)) {
                recipients = _body.delivery.recipients;
            }
            if (!IsNullEmpty(messageTag) && !IsNullEmpty(deliveredOn) && !IsNullEmpty(recipients) && recipients.length > 0) {
                for (const receipient of recipients) {
                    await NotificationsList.updateMany(
                        { email: receipient, messageTag: messageTag, deliveredOn: null },
                        { $set: { status: "delivered", deliveredOn: new Date(deliveredOn), modifiedOn: Date.now() } }
                    );
                }
            }
            logger.logs("messageTag", messageTag);
            logger.logs("deliveredOn", deliveredOn);
            logger.logs("recipients", recipients);

        } else if (_body.eventType == "Open") {
            logger.logs("Event Delivery");
            let messageTag = null;
            let openedOn = null;

            if (!IsNullEmpty(_body.mail.tags.tagName) && _body.mail.tags.tagName.length > 0) {
                messageTag = _body.mail.tags.tagName[0];
            }

            if (!IsNullEmpty(_body.open.timestamp)) {
                openedOn = _body.open.timestamp;
            }
            if (!IsNullEmpty(messageTag) && !IsNullEmpty(openedOn)) {
                await NotificationsList.updateMany(
                    { messageTag: messageTag, readOn: null },
                    { $set: { status: "read", readOn: new Date(openedOn), modifiedOn: Date.now() } }
                );
            }
            logger.logs("messageTag", messageTag);
            logger.logs("openedOn", openedOn);

        } else {
            const _filePath = config.get("keys.errorFilePath");
            const filePath = _filePath + "//Email_Webhook.txt";
            fs.appendFile(filePath, "Body : " + body + "\n\n", function (err) {
                logger.logs(err);
            });
            // fs.appendFile(filePath, "header :" + JSON.stringify(headers) + "\n\n", function (err) {
            //     logger.logs(err);
            // });
        }
        output = {
            success: true,
            message: "Saved Successfully",
            status: 200,
        };

    } catch (ex) {
        const model = { headers, params, body };
        saveErrorLog("emailWebHook", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        logger.logs("Error :", ex);
        output.message = ex.message;
    }
    return output;
};