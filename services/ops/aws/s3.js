const { getCurrentTimeStamp, generateUniqueFileName } = require("../../../services/ops/common/common");
const AWS = require("../../../models/ops/aws/s3");
let Config = require("config");

const getSignUrl = async function (userId, { fileName, type }) {
    let path = ""
    let customBucketName = ""

    let extension = fileName.split(".")
    extension = extension[extension.length - 1]

    if (type === "PAN") {
        path = `ProofImages/${userId}/PAN/panFile_${getCurrentTimeStamp()}.${extension}`
    } else if (type === "GST") {
        path = `ProofImages/${userId}/GST/gstFile_${getCurrentTimeStamp()}.${extension}`
    } else if (type === "ADDITIONAL") {
        path = `ProofImages/${userId}/Additional/additionalFile_${getCurrentTimeStamp()}.${extension}`
    } else if (type === "LOAN_CONTRACT") {
        path = `ProofImages/${userId}/LoanContract/photo_${getCurrentTimeStamp()}.${extension}`
    } else if (type === "UDYOG_AADHAAR") {
        path = `ProofImages/${userId}/UdyogAadhaar/udyogAadhaar_${getCurrentTimeStamp()}.${extension}`
    } else if (type === "PROFILE") {
        path = `ProofImages/${userId}/Profile/photo_${getCurrentTimeStamp()}.${extension}`
    } else if (type == "RECON") {
        let name = generateUniqueFileName();
        path = `ManualQueue/${name}.${extension}`
        customBucketName = Config.get("awsCredentialsRecon.bucketName");
    }
    else if (type === "SHOP_INTERNAL_PHOTO") {
        path = `ProofImages/${userId}/ShopDocs/shopInternalPhoto_${getCurrentTimeStamp()}.${extension}`
    }
    else if (type === "SHOP_EXTERNAL_PHOTO") {
        path = `ProofImages/${userId}/ShopDocs/shopExternalPhoto_${getCurrentTimeStamp()}.${extension}`
    }
    else if (type === "SHOP_VIDEO") {
        path = `ProofImages/${userId}/ShopDocs/video_${getCurrentTimeStamp()}.${extension}`
    }
    else {
        path = `ProofImages/${userId}/${getCurrentTimeStamp()}.${extension}`
    }

    let data = await AWS.getSignedUrl(path, customBucketName)

    return {
        data,
        success: true,
        message: "",
        status: 200,
    };

};

const getSignUrlToDownload = async function (url) {

    let payload = { url }

    let data = await AWS.getSignedUrlToDownload(payload)

    return {
        data,
        success: true,
        message: "",
        status: 200,
    };

};

const getBulkSignUrlToDownload = async function (urls) {

    let data = []
    urls.forEach(url => {
        let payload = { url }
        let signedUrl = AWS.getSignedUrlToDownload(payload)
        data.push(signedUrl)
    });

    return {
        data,
        success: true,
        message: "",
        status: 200,
    };

};

const getSignInvoiceUrlToDownload = async function (url) {

    let payload = { url }

    let data = await AWS.getSignedInvoiceUrlToDownload(payload)

    return {
        data,
        success: true,
        message: "",
        status: 200,
    };

};


module.exports = {
    getSignUrl,
    getSignUrlToDownload,
    getBulkSignUrlToDownload,
    getSignInvoiceUrlToDownload
}