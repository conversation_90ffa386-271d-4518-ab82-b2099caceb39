
const https = require('https');
const fetch = require("node-fetch");

async function httpsRequest(url, options) {
    try {

        let optionPayload = {
            body: options.body,
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            method: options.method
        }

        console.log("httpsRequest", url)

        const response = await fetch(url, {
            ...optionPayload
        });

        let responseStatus = response.ok ? true : false;
        let responseBody = await response.json();
        responseBody.responseStatus = responseStatus;

        return ([null, responseBody, options]);
    } catch (error) {
        return ([error, null, options]);
    }
}
module.exports = {
    httpsRequest
}
