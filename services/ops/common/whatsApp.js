const mongoose = require("mongoose");
const config = require("config");
const { WhatsAppLog, WAWebhookLog } = require("../../../models/ops/whatsApp/whatsApp");
const { UserInfo, UserPartnerInfo } = require("../../../models/ops/registration/users");
const { LoanInfo, InvoiceRequests } = require("../../../models/ops/loan/loan");
const { callKarixApi, getName, updateMobileNumberChannelDetails } = require("./common");
const loanService = require("../../../services/ops/loan/loan");
const workflowService = require("../../../services/ops/loan/workflow");
const { NotificationTemplates, DefinationDetailsMaster } = require("../../../models/ops/common/master");
const { <PERSON>Master, DND, NotificationsList, PreApprovals, Notifications, ConfigParameters, PartnerConfig } = require("../../../models/ops/partner/partner");
const IsNullEmpty = require("isnullemptyorwhitespace");
const { getDMYFormattedDate, getMailModel, getSmsModel, pushToNotifications } = require("../../../services/ops/common/common");
const { sendMail } = require("../../../services/ops/common/mailer");
const { dateAdd } = require("dateadd");
const { sendCustomSms } = require("../../../services/ops/registration/verificationService");
const { saveErrorLog } = require("./masters");
const { generateCustomUrl } = require("../../../services/ops/common/links");
const { computeLoanCashback } = require("../../../services/ops/loan/cashback");
const postDisbursal = require("../../../services/ops/loan/postDisbursal");
const installmentService = require("../loan/installments");
const { NotificationEventTemplates } = require("../../../models/ops/templates/templates");
const { getSignedUrlToDownload } = require("../../../models/ops/aws/s3");
const apiPath = __dirname;

module.exports.sendWhatsAppMessage = async function (userId = null, loanId, template, notificationListId = null) {
    try {

        let user = null;

        if (userId)
            user = await UserInfo.findById(userId);

        let sendMailFlag = false;
        let mailModel = getMailModel();
        let mailCategory = "";
        let mailSubCategory = "";
        let mailUserType = "";
        let name = "";
        let merchantCode = "";
        let trxnId = "";
        let productId = "";
        let whatsAppTemplate = "";
        let content = {};
        let sender = config.get("karix.mobileNo1");

        const notificationList = await NotificationsList.findById(notificationListId);

        if (!notificationList)
            return { data: "Error", success: false, message: "NotificationsList record not found", statusCode: "", mid: "" };

        let mobileNo = notificationList.mobileNo ? notificationList.mobileNo : "";

        if (user)
            name = capitalizeFirstLetter(user.userDetails.firstName) + " " + capitalizeFirstLetter(user.userDetails.lastName);

        const dndRecord = await DND.findOne({ mobileNo: mobileNo, channel: "WhatsApp", isActive: true });

        if (dndRecord && template !== "otp_confirmation")
            return { data: "Error", success: false, message: "Mobile No is in DND", statusCode: "", mid: "" };

        if (mobileNo.length === 10)
            mobileNo = `91${mobileNo}`;

        if (template === "contract_sign_eng") {

            const loan = await LoanInfo.findById(loanId);

            if (!loan)
                return { data: "Error", success: false, message: "Loan not found", statusCode: "", mid: "" };

            const partner = await PartnerMaster.findById(loan.partnerId);

            if (!partner)
                return { data: "Error", success: false, message: "Partner not found", statusCode: "", mid: "" };

            const amount = loan.amount ? loan.amount.toString() : "";

            let contractLink = "";

            if (loan.primaryCollectionType === "eNach" || loan.secondaryCollectionType === "eNach")
                contractLink = notificationList.url || (loan.loanContractDetails && loan.loanContractDetails.eNachUrl ? loan.loanContractDetails.eNachUrl : "");
            else
                contractLink = loan.loanContractDetails && loan.loanContractDetails.contractUrl ? loan.loanContractDetails.contractUrl : "";

            if (!amount)
                return { data: "Error", success: false, message: "Loan amount not found", statusCode: "", mid: "" };

            if (!contractLink)
                return { data: "Error", success: false, message: "Contract link not found", statusCode: "", mid: "" };

            let preApproval = await PreApprovals.findOne({ partnerId: loan.partnerId, merchantCode: loan.merchantCode, productId: loan.productId });

            if (!preApproval)
                preApproval = await PreApprovals.findOne({ partnerId: loan.partnerId, merchantCode: loan.merchantCode, trxnId: loan.trxnId });

            let expiryDate = null;

            if (preApproval && preApproval.expiredOn)
                expiryDate = getDMYFormattedDate(preApproval.expiredOn, "/");
            else
                expiryDate = getDMYFormattedDate(new Date(loan.insertedOn.getFullYear(), loan.insertedOn.getMonth() + 1, 0), "/");

            const notificationTemplate = await NotificationTemplates.findOne({ partnerId: loan.partnerId, template: template, channel: "WhatsApp" });

            if (!notificationTemplate)
                return { data: "Error", success: false, message: "Notification template not found", statusCode: "", mid: "" };

            whatsAppTemplate = notificationTemplate.whatsAppTemplate;

            let txtParam5 = " ";

            const discount = await computeLoanCashback(loan._id, false);
            if (!discount.success)
                return { data: "Error", success: false, message: discount.message, statusCode: "", mid: "" };

            if (discount.discountAmount)
                txtParam5 = notificationTemplate.var5.replace("#discount", `₹${discount.discountAmount.toString()}`);

            let partnerLink = notificationTemplate.url1 || "";
            let bodyParameterValues = null;
            let media = null

            let templateConfig = await NotificationEventTemplates.findOne({ eventId: notificationTemplate.eventId, template: whatsAppTemplate })

            let payload = {
                name,
                amount,
                contractLink: `${contractLink}?c=wa`,
                expiryDate,
                discountAmount: txtParam5,
                ffSupportLink: config.get("karix.ffSupportLink")
            }

            let ctaPayload = {
                partnerLink: `${partnerLink}?c=wa`.replace(config.get("tinyUrl.urlPrefix"), "")
            }

            if (templateConfig && templateConfig.config) {
                bodyParameterValues = templateValueConfigEngine(templateConfig.config, payload, notificationTemplate)
            }

            if (!bodyParameterValues) return { data: "Error", success: false, message: "WhatsApp template not found", statusCode: "", mid: "" };

            let buttons = null

            if (templateConfig && templateConfig.ctaConfig && Object.keys(templateConfig.ctaConfig).length) {
                buttons = templateCTAConfigEngine(templateConfig.ctaConfig, ctaPayload, notificationTemplate)
            }

            if (templateConfig && templateConfig.isMedia) {

                let url = notificationTemplate.header && notificationTemplate.header.filePath ? `${config.get("awsSystemFilesCredentials.baseUrl")}${notificationTemplate.header.filePath}` : config.get("keys.defaultWhatsappMediaFile")
                let splittedUrl = url.split("/")

                media = {
                    "type": templateConfig.mediaType ? templateConfig.mediaType.toLowerCase() : "image",
                    "url": url,
                    "fileName": splittedUrl[splittedUrl.length - 1] || "fundfina-benefits.png"
                }
            }

            if (notificationTemplate.type === "MEDIA_TEMPLATE") {
                content = {
                    preview_url: false,
                    type: notificationTemplate.type,
                    mediaTemplate: {
                        templateId: whatsAppTemplate,
                        media: media || undefined,
                        bodyParameterValues: bodyParameterValues,
                        buttons: buttons || undefined
                    }
                }
            } else {
                content = {
                    preview_url: true,
                    shorten_url: true,
                    type: notificationTemplate.type,
                    template: {
                        templateId: whatsAppTemplate,
                        parameterValues: bodyParameterValues
                    }
                };
            }

        }
        else if (template === "kyc_reject_eng") {

            const loan = await LoanInfo.findById(loanId);

            if (!loan)
                return { data: "Error", success: false, message: "Loan not found", statusCode: "", mid: "" };

            const userPartnerInfo = await UserPartnerInfo.findOne({ userId: userId, partnerId: loan.partnerId, merchantCode: loan.merchantCode });

            if (!userPartnerInfo)
                return { data: "Error", success: false, message: "UserPartnerInfo not found ", statusCode: "", mid: "" };

            const reviewDoc = userPartnerInfo.incorrectReviewDocument ? userPartnerInfo.incorrectReviewDocument : "";

            if (!reviewDoc)
                return { data: "Error", success: false, message: "Review reason not found", statusCode: "", mid: "" };

            const partnerMaster = await PartnerMaster.findById(loan.partnerId);

            let partnerLink = "";

            const notificationTemplate = await NotificationTemplates.findOne({ partnerId: loan.partnerId, template: template, channel: "WhatsApp" });

            if (!notificationTemplate)
                return { data: "Error", success: false, message: "Notification template not found", statusCode: "", mid: "" };

            let preApproval = await PreApprovals.findOne({ partnerId: loan.partnerId, merchantCode: loan.merchantCode, productId: loan.productId });

            if (!preApproval)
                preApproval = await PreApprovals.findOne({ partnerId: loan.partnerId, merchantCode: loan.merchantCode, trxnId: loan.trxnId });

            if (userPartnerInfo && userPartnerInfo.reviewLink)
                partnerLink = userPartnerInfo.reviewLink;
            else {
                partnerLink = notificationTemplate && notificationTemplate.url1 ? notificationTemplate.url1 : "";

                const type = config.get("shortUrl.reviewCode");

                let longUrl = null;
                let txnId = "";

                if (partnerMaster.partnerCode.toLowerCase() === "pay")
                    txnId = preApproval.trxnId;
                else
                    txnId = preApproval.productId ? preApproval.productId : preApproval.trxnId;

                if (notificationTemplate.isInternalOfferLink)
                    longUrl = config.get("keys.webUrl") + `leadInfo/${preApproval.merchantCode}/${txnId}/${preApproval.approvedAmount}/${partnerMaster.shortauthKey ? partnerMaster.shortauthKey : partnerMaster.authKey}`;
                else
                    longUrl = partnerLink;

                const generateCustomUrlResponse = await generateCustomUrl(longUrl, type, loan.userId, loan._id, loan.loanNo, loan.merchantCode, partnerMaster.displayName, null);

                if (generateCustomUrlResponse.success)
                    partnerLink = generateCustomUrlResponse.data;
                else
                    return { data: "Error", success: false, message: generateCustomUrlResponse.message, statusCode: "", mid: "" };
            }

            if (!partnerLink)
                return { data: "Error", success: false, message: "Partner Link not found", statusCode: "", mid: "" };

            const reason = await DefinationDetailsMaster.findOne({ definationType: "review reasons", defination: reviewDoc });

            if (!reason)
                return { data: "Error", success: false, message: "Review reason not found", statusCode: "", mid: "" };

            let rejectReason = user.userDetails && user.userDetails.kycRemarks ? user.userDetails.kycRemarks : "Please provide missing details";

            rejectReason = rejectReason.replace("-- System", "");

            whatsAppTemplate = notificationTemplate.whatsAppTemplate;

            let txtParam5 = " ";

            const discount = await computeLoanCashback(loan._id, false);
            if (!discount.success)
                return { data: "Error", success: false, message: discount.message, statusCode: "", mid: "" };

            if (discount.discountAmount)
                txtParam5 = notificationTemplate.var5.replace("#discount", `₹${discount.discountAmount.toString()}`);

            let bodyParameterValues = null;
            let media = null

            let templateConfig = await NotificationEventTemplates.findOne({ eventId: notificationTemplate.eventId, template: whatsAppTemplate })

            let payload = {
                name: name,
                reason: reason.value ? reason.value : "",
                partnerLink: `${partnerLink}?c=wa`,
                rejectReason: rejectReason,
                discountAmount: txtParam5,
                ffSupportLink: config.get("karix.ffSupportLink")
            }

            let ctaPayload = {
                partnerLink: `${partnerLink}?c=wa`.replace(config.get("tinyUrl.urlPrefix"), "")
            }

            if (templateConfig && templateConfig.config) {
                bodyParameterValues = templateValueConfigEngine(templateConfig.config, payload, notificationTemplate)
            }

            if (!bodyParameterValues) return { data: "Error", success: false, message: "WhatsApp template not found", statusCode: "", mid: "" };

            let buttons = null

            if (templateConfig && templateConfig.ctaConfig && Object.keys(templateConfig.ctaConfig).length) {
                buttons = templateCTAConfigEngine(templateConfig.ctaConfig, ctaPayload, notificationTemplate)
            }

            if (templateConfig && templateConfig.isMedia) {

                let url = notificationTemplate.header && notificationTemplate.header.filePath ? `${config.get("awsSystemFilesCredentials.baseUrl")}${notificationTemplate.header.filePath}` : config.get("keys.defaultWhatsappMediaFile")
                let splittedUrl = url.split("/")

                media = {
                    "type": templateConfig.mediaType ? templateConfig.mediaType.toLowerCase() : "image",
                    "url": url,
                    "fileName": splittedUrl[splittedUrl.length - 1] || "fundfina-benefits.png"
                }
            }

            if (notificationTemplate.type === "MEDIA_TEMPLATE") {
                content = {
                    preview_url: false,
                    type: notificationTemplate.type,
                    mediaTemplate: {
                        templateId: whatsAppTemplate,
                        media: media || undefined,
                        bodyParameterValues: bodyParameterValues,
                        buttons: buttons || undefined
                    }
                }
            } else {
                content = {
                    preview_url: true,
                    shorten_url: true,
                    type: notificationTemplate.type,
                    template: {
                        templateId: whatsAppTemplate,
                        parameterValues: bodyParameterValues
                    }
                };
            }

            mailModel.rejectionreason = reason.value ? reason.value : "";
            mailModel.link = `${partnerLink}?c=e`;
            mailModel.link1 = config.get("karix.ffSupportLink");

            mailCategory = "Emails to Borrower";
            mailSubCategory = "KYC Review";
            mailUserType = "User";

            sendMailFlag = true;
        }
        else if (template === "incomplete__application_eng_1") {

            const loan = await LoanInfo.findById(loanId);

            if (!loan)
                return { data: "Error", success: false, message: "Loan not found", statusCode: "", mid: "" };

            const response = await getLoanCalculations(loan._id);

            const notificationTemplate = await NotificationTemplates.findOne({ partnerId: loan.partnerId, template: template, channel: "WhatsApp" });

            if (!notificationTemplate)
                return { data: "Error", success: false, message: "Notification template not found", statusCode: "", mid: "" };

            if (response.success) {

                const partnerMaster = await PartnerMaster.findById(loan.partnerId);

                if (!partnerMaster)
                    return { data: "Error", success: false, message: "Partner not found", statusCode: "", mid: "" };

                let partnerLink = notificationTemplate && notificationTemplate.url1 ? notificationTemplate.url1 : "";

                if (!partnerLink)
                    return { data: "Error", success: false, message: "Partner Link not found", statusCode: "", mid: "" };

                const type = config.get("shortUrl.incompleteApplicationCode");

                let preApproval = await PreApprovals.findOne({ partnerId: loan.partnerId, merchantCode: loan.merchantCode, productId: loan.productId });

                if (!preApproval)
                    preApproval = await PreApprovals.findOne({ partnerId: loan.partnerId, merchantCode: loan.merchantCode, trxnId: loan.trxnId });

                let longUrl = null;
                let txnId = "";

                if (partnerMaster.partnerCode.toLowerCase() === "pay")
                    txnId = preApproval.trxnId;
                else
                    txnId = preApproval.productId ? preApproval.productId : preApproval.trxnId;

                if (notificationTemplate.isInternalOfferLink)
                    longUrl = config.get("keys.webUrl") + `leadInfo/${preApproval.merchantCode}/${txnId}/${preApproval.approvedAmount}/${partnerMaster.shortauthKey ? partnerMaster.shortauthKey : partnerMaster.authKey}`;
                else
                    longUrl = partnerLink;

                const generateCustomUrlResponse = await generateCustomUrl(longUrl, type, loan.userId, loan._id, loan.loanNo, loan.merchantCode, partnerMaster.displayName, null);

                if (generateCustomUrlResponse.success)
                    partnerLink = generateCustomUrlResponse.data;
                else
                    return { data: "Error", success: false, message: generateCustomUrlResponse.message, statusCode: "", mid: "" };

                whatsAppTemplate = notificationTemplate.whatsAppTemplate;
                let bodyParameterValues = null;
                let media = null

                let templateConfig = await NotificationEventTemplates.findOne({ eventId: notificationTemplate.eventId, template: whatsAppTemplate })

                let payload = {
                    name,
                    partnerName: response.data.partnerName.toString(),
                    partnerLink: `${partnerLink}?c=wa`,
                    ffSupportLink: config.get("karix.ffSupportLink")
                }

                let ctaPayload = {
                    partnerLink: `${partnerLink}?c=wa`.replace(config.get("tinyUrl.urlPrefix"), "")
                }

                if (templateConfig && templateConfig.config) {
                    bodyParameterValues = templateValueConfigEngine(templateConfig.config, payload, notificationTemplate)
                }

                if (!bodyParameterValues) return { data: "Error", success: false, message: "WhatsApp template not found", statusCode: "", mid: "" };

                let buttons = null

                if (templateConfig && templateConfig.ctaConfig && Object.keys(templateConfig.ctaConfig).length) {
                    buttons = templateCTAConfigEngine(templateConfig.ctaConfig, ctaPayload, notificationTemplate)
                }

                if (templateConfig && templateConfig.isMedia) {

                    let url = notificationTemplate.header && notificationTemplate.header.filePath ? `${config.get("awsSystemFilesCredentials.baseUrl")}${notificationTemplate.header.filePath}` : config.get("keys.defaultWhatsappMediaFile")
                    let splittedUrl = url.split("/")

                    media = {
                        "type": templateConfig.mediaType ? templateConfig.mediaType.toLowerCase() : "image",
                        "url": url,
                        "fileName": splittedUrl[splittedUrl.length - 1] || "fundfina-benefits.png"
                    }
                }

                content = {
                    preview_url: true,
                    type: notificationTemplate.type,
                    mediaTemplate: {
                        templateId: whatsAppTemplate,
                        media: media || undefined,
                        bodyParameterValues: bodyParameterValues,
                        buttons: buttons || undefined
                    }
                };
            }
            else {
                return {
                    data: response.message, success: false, message: response.message, statusCode: "", mid: ""
                }
            }
        }
        else if (template === "renewal__notification_eng") {
            const loan = await LoanInfo.findById(loanId);

            if (!loan)
                return { data: "Error", success: false, message: "Loan not found", statusCode: "", mid: "" };

            const partner = await PartnerMaster.findById(notificationList.partnerId);

            if (!partner)
                return { data: "Error", success: false, message: "Partner not found", statusCode: "", mid: "" };

            const notificationTemplate = await NotificationTemplates.findOne({ partnerId: notificationList.partnerId, template: template, channel: "WhatsApp" });

            if (!notificationTemplate)
                return { data: "Error", success: false, message: "Notification template not found", statusCode: "", mid: "" };

            const preApproval = await PreApprovals.findById(notificationList.preApprovalId);

            if (!preApproval)
                return { data: "Error", success: false, message: "PreApproval record not found", statusCode: "", mid: "" };

            const installmentDetails = await installmentService.getInstallmentDetails(loanId);
            if (!installmentDetails.success)
                return { data: "Error", success: false, message: installmentDetails.message, statusCode: "", mid: "" };
            const loanExpiryDate = getDMYFormattedDate(installmentDetails.lastInstallmentDate, "-");

            let partnerLink = notificationTemplate && notificationTemplate.url1 ? notificationTemplate.url1 : "";

            const partnerName = notificationTemplate && notificationTemplate.partnerName ? notificationTemplate.partnerName : "";

            const type = config.get("shortUrl.renewalsCode");

            let longUrl = null;
            let txnId = "";

            if (partner.partnerCode.toLowerCase() === "pay")
                txnId = preApproval.trxnId;
            else
                txnId = preApproval.productId ? preApproval.productId : preApproval.trxnId;

            if (notificationTemplate.isInternalOfferLink)
                longUrl = config.get("keys.webUrl") + `leadInfo/${preApproval.merchantCode}/${txnId}/${preApproval.approvedAmount}/${partnerMaster.shortauthKey ? partnerMaster.shortauthKey : partnerMaster.authKey}`;
            else
                longUrl = partnerLink;

            const generateCustomUrlResponse = await generateCustomUrl(longUrl, type, loan.userId, loan._id, loan.loanNo, loan.merchantCode, partner.displayName, null);

            if (generateCustomUrlResponse.success)
                partnerLink = generateCustomUrlResponse.data;
            else
                return { data: "Error", success: false, message: generateCustomUrlResponse.message, statusCode: "", mid: "" };

            const loanAmount = preApproval.approvedAmount ? preApproval.approvedAmount.toString() : "";

            const emiAmount = preApproval.emiAmt ? preApproval.emiAmt.toString() : "";

            const tenureInDays = preApproval.tenureInDays ? `${preApproval.tenureInDays.toString()} days` : "";

            const disbursalAmount = preApproval.disbursedAmount ? preApproval.disbursedAmount.toString() : "";

            const charges = await loanService.getLoanCharges(loanAmount, loan._id, preApproval._id);
            if (charges.success) {
                const totalInterestAmount = preApproval.totalInterest ? (Math.round((preApproval.totalInterest + Number.EPSILON) * 100) / 100).toString() : "";

                if (!name || !loanExpiryDate || !loanAmount || !partnerLink || !partnerName || !emiAmount || !tenureInDays ||
                    !charges.processingChargeAmt || !disbursalAmount || !config.get("karix.ffSupportLink"))
                    return { data: "Error", success: false, message: "Insufficient message parameters", statusCode: "", mid: "" };

                whatsAppTemplate = notificationTemplate.whatsAppTemplate;

                content = {
                    preview_url: true,
                    shorten_url: true,
                    type: notificationTemplate.type,
                    template: {
                        templateId: whatsAppTemplate,
                        parameterValues: {
                            0: name,
                            1: loanExpiryDate,
                            2: loanAmount,
                            3: partnerName,
                            4: `${partnerLink}?c=wa`,
                            5: loanAmount,
                            6: emiAmount,
                            7: totalInterestAmount,
                            8: tenureInDays,
                            9: charges.processingChargeAmt.toString(),
                            10: charges.otherCharges.toString(),
                            11: disbursalAmount,
                            12: config.get("karix.ffSupportLink")
                        }
                    }
                };
            } else {
                return {
                    data: charges.message, success: false, message: charges.message, statusCode: "", mid: ""
                }
            }

        }
        else if (template === "contract_lc_sign_eng") {

            const loan = await LoanInfo.findById(loanId);

            if (!loan)
                return { data: "Error", success: false, message: "Loan not found", statusCode: "", mid: "" };

            const amount = loan.amount ? loan.amount.toString() : "";

            let contractLink = "";

            if (loan.primaryCollectionType === "eNach" || loan.secondaryCollectionType === "eNach")
                contractLink = loan.loanContractDetails && loan.loanContractDetails.eNachUrl ? loan.loanContractDetails.eNachUrl : "";
            else
                contractLink = loan.loanContractDetails && loan.loanContractDetails.contractUrl ? loan.loanContractDetails.contractUrl : "";

            if (!amount)
                return { data: "Error", success: false, message: "Loan amount not found", statusCode: "", mid: "" };

            if (!contractLink)
                return { data: "Error", success: false, message: "Contract link not found", statusCode: "", mid: "" };

            let preApproval = await PreApprovals.findOne({ partnerId: loan.partnerId, merchantCode: loan.merchantCode, productId: loan.productId });

            if (!preApproval)
                preApproval = await PreApprovals.findOne({ partnerId: loan.partnerId, merchantCode: loan.merchantCode, trxnId: loan.trxnId });

            let expiryDate = null;

            if (preApproval && preApproval.expiredOn)
                expiryDate = getDMYFormattedDate(preApproval.expiredOn, "/");
            else
                expiryDate = getDMYFormattedDate(new Date(loan.insertedOn.getFullYear(), loan.insertedOn.getMonth() + 1, 0), "/");

            const notificationTemplate = await NotificationTemplates.findOne({ partnerId: loan.partnerId, template: template, channel: "WhatsApp" });

            if (!notificationTemplate)
                return { data: "Error", success: false, message: "Notification template not found", statusCode: "", mid: "" };

            whatsAppTemplate = notificationTemplate.whatsAppTemplate;

            content = {
                preview_url: true,
                shorten_url: true,
                type: notificationTemplate.type,
                template: {
                    templateId: whatsAppTemplate,
                    parameterValues: {
                        0: name,
                        1: amount,
                        2: `${contractLink}?c=wa`,
                        3: expiryDate,
                        4: config.get("karix.ffSupportLink")
                    }
                }
            };
        }
        else if (template === "consent_auth_merchant_eng") {

            const loan = await LoanInfo.findById(loanId);

            if (!loan)
                return { data: "Error", success: false, message: "Loan not found", statusCode: "", mid: "" };

            const amount = loan.amount ? loan.amount.toString() : "";

            if (!amount)
                return { data: "Error", success: false, message: "Loan amount not found", statusCode: "", mid: "" };

            const creditLineBalance = await workflowService.getCreditLineBalance(loan.productId);

            if (!creditLineBalance.success)
                return { data: "Error", success: false, message: "Loan amount not found", statusCode: "", mid: "" };

            const notificationTemplate = await NotificationTemplates.findOne({ partnerId: loan.partnerId, template: template, channel: "WhatsApp" });

            if (!notificationTemplate)
                return { data: "Error", success: false, message: "Notification template not found", statusCode: "", mid: "" };

            whatsAppTemplate = notificationTemplate.whatsAppTemplate;

            content = {
                preview_url: true,
                shorten_url: true,
                type: notificationTemplate.type,
                template: {
                    templateId: whatsAppTemplate,
                    parameterValues: {
                        0: name,
                        1: amount,
                        2: creditLineBalance.availableCreditLimit ? creditLineBalance.availableCreditLimit.toString() : "",
                        3: config.get("karix.ffSupportLink")
                    }
                }
            };
        }
        else if (template === "consent_confirm_delivery_agent_eng") {

            const loan = await LoanInfo.findById(loanId);

            if (!loan)
                return { data: "Error", success: false, message: "Loan not found", statusCode: "", mid: "" };

            const amount = loan.amount ? loan.amount.toString() : "";

            if (!amount)
                return { data: "Error", success: false, message: "Loan amount not found", statusCode: "", mid: "" };

            if (!loan.invoiceRequestId)
                return { data: "Error", success: false, message: "Invoice Id not found", statusCode: "", mid: "" };

            const invoice = await InvoiceRequests.findById(loan.invoiceRequestId);

            if (!invoice)
                return { data: "Error", success: false, message: "Invoice record not found", statusCode: "", mid: "" };

            if (!invoice.deliveryAgent || !invoice.deliveryAgentMobile)
                return { data: "Error", success: false, message: "Agent Name/Mobile not found", statusCode: "", mid: "" };

            const notificationTemplate = await NotificationTemplates.findOne({ partnerId: loan.partnerId, template: template, channel: "WhatsApp" });

            if (!notificationTemplate)
                return { data: "Error", success: false, message: "Notification template not found", statusCode: "", mid: "" };

            whatsAppTemplate = notificationTemplate.whatsAppTemplate;

            content = {
                preview_url: true,
                shorten_url: true,
                type: notificationTemplate.type,
                template: {
                    templateId: whatsAppTemplate,
                    parameterValues: {
                        0: invoice.deliveryAgent ? capitalizeFirstLetter(invoice.deliveryAgent) : "Agent",
                        1: amount,
                        2: name,
                        3: loan.partner
                    }
                }
            };
        }
        else if (template === "autopay_setup_merchant_eng") {

            const loan = await LoanInfo.findById(loanId);

            if (!loan)
                return { data: "Error", success: false, message: "Loan not found", statusCode: "", mid: "" };

            const amount = loan.amount ? loan.amount.toString() : "";

            if (!amount)
                return { data: "Error", success: false, message: "Loan amount not found", statusCode: "", mid: "" };

            const firstInstallment = await installmentService.getSingleInstallmentDetails(loanId, 1);
            if (!firstInstallment.success)
                return { data: "Error", success: false, message: firstInstallment.message, statusCode: "", mid: "" };

            const installmentDetails = firstInstallment.success ? firstInstallment : null;

            if (!installmentDetails)
                return { data: "Error", success: false, message: "EMI details not found", statusCode: "", mid: "" };

            const paymentDate = installmentDetails.dueDate ? getDMYFormattedDate(installmentDetails.dueDate, "/") : "";

            if (!paymentDate)
                return { data: "Error", success: false, message: "Repayment Date not found", statusCode: "", mid: "" };

            const notificationTemplate = await NotificationTemplates.findOne({ partnerId: loan.partnerId, template: template, channel: "WhatsApp" });

            if (!notificationTemplate)
                return { data: "Error", success: false, message: "Notification template not found", statusCode: "", mid: "" };

            whatsAppTemplate = notificationTemplate.whatsAppTemplate;

            content = {
                preview_url: true,
                shorten_url: true,
                type: notificationTemplate.type,
                template: {
                    templateId: whatsAppTemplate,
                    parameterValues: {
                        0: name,
                        1: amount,
                        2: paymentDate,
                        3: config.get("karix.ffSupportLink")
                    }
                }
            };
        }
        else if (template === "bank_details_update_eng") {
            const loan = await LoanInfo.findById(loanId);

            if (!loan)
                return { data: "Error", success: false, message: "Loan not found", statusCode: "", mid: "" };

            const userPartnerInfo = await UserPartnerInfo.findOne({ userId: userId, partnerId: loan.partnerId, merchantCode: loan.merchantCode });

            if (!userPartnerInfo)
                return { data: "Error", success: false, message: "UserPartnerInfo not found ", statusCode: "", mid: "" };

            if (!userPartnerInfo.updateBankLink)
                return { data: "Error", success: false, message: "Bank link not found", statusCode: "", mid: "" };

            const notificationTemplate = await NotificationTemplates.findOne({ partnerId: loan.partnerId, template: template, channel: "WhatsApp" });

            if (!notificationTemplate)
                return { data: "Error", success: false, message: "Notification template not found", statusCode: "", mid: "" };

            let partnerLink = notificationTemplate.url1;

            whatsAppTemplate = notificationTemplate.whatsAppTemplate;

            let bodyParameterValues = null;
            let media = null

            let templateConfig = await NotificationEventTemplates.findOne({ eventId: notificationTemplate.eventId, template: whatsAppTemplate })

            let payload = {
                name,
                updateBankLink: `${userPartnerInfo.updateBankLink}?c=wa`,
                ffSupportLink: config.get("karix.ffSupportLink")
            }

            let ctaPayload = {
                partnerLink: `${partnerLink}?c=wa`.replace(config.get("tinyUrl.urlPrefix"), "")
            }

            if (templateConfig && templateConfig.config) {
                bodyParameterValues = templateValueConfigEngine(templateConfig.config, payload, notificationTemplate)
            }

            if (!bodyParameterValues) return { data: "Error", success: false, message: "WhatsApp template not found", statusCode: "", mid: "" };

            let buttons = null

            if (templateConfig && templateConfig.ctaConfig && Object.keys(templateConfig.ctaConfig).length) {
                buttons = templateCTAConfigEngine(templateConfig.ctaConfig, ctaPayload, notificationTemplate)
            }

            if (templateConfig && templateConfig.isMedia) {

                let url = notificationTemplate.header && notificationTemplate.header.filePath ? `${config.get("awsSystemFilesCredentials.baseUrl")}${notificationTemplate.header.filePath}` : config.get("keys.defaultWhatsappMediaFile")
                let splittedUrl = url.split("/")

                media = {
                    "type": templateConfig.mediaType ? templateConfig.mediaType.toLowerCase() : "image",
                    "url": url,
                    "fileName": splittedUrl[splittedUrl.length - 1] || "fundfina-benefits.png"
                }
            }

            if (notificationTemplate.type === "MEDIA_TEMPLATE") {
                content = {
                    preview_url: false,
                    type: notificationTemplate.type,
                    mediaTemplate: {
                        templateId: whatsAppTemplate,
                        media: media || undefined,
                        bodyParameterValues: bodyParameterValues,
                        buttons: buttons || undefined
                    }
                }
            } else {
                content = {
                    preview_url: true,
                    shorten_url: true,
                    type: notificationTemplate.type,
                    template: {
                        templateId: whatsAppTemplate,
                        parameterValues: bodyParameterValues
                    }
                };
            }

        }
        else if (template === "autopay_consent_request_eng") {
            const loan = await LoanInfo.findById(loanId);

            if (!loan)
                return { data: "Error", success: false, message: "Loan not found", statusCode: "", mid: "" };

            let contractLink = "";

            if (loan.primaryCollectionType === "eNach" || loan.secondaryCollectionType === "eNach")
                contractLink = loan.loanContractDetails && loan.loanContractDetails.eNachUrl ? loan.loanContractDetails.eNachUrl : "";
            else
                contractLink = loan.loanContractDetails && loan.loanContractDetails.contractUrl ? loan.loanContractDetails.contractUrl : "";

            if (!contractLink)
                return { data: "Error", success: false, message: "Contract link not found", statusCode: "", mid: "" };

            const notificationTemplate = await NotificationTemplates.findOne({ partnerId: loan.partnerId, template: template, channel: "WhatsApp" });

            if (!notificationTemplate)
                return { data: "Error", success: false, message: "Notification template not found", statusCode: "", mid: "" };

            whatsAppTemplate = notificationTemplate.whatsAppTemplate;

            content = {
                preview_url: true,
                shorten_url: true,
                type: notificationTemplate.type,
                template: {
                    templateId: whatsAppTemplate,
                    parameterValues: {
                        0: name,
                        1: `${contractLink}?c=wa`,
                        2: config.get("karix.ffSupportLink")
                    }
                }
            };
        }
        else if (template === "autopay_update_request_eng") {
            const loan = await LoanInfo.findById(loanId);

            if (!loan)
                return { data: "Error", success: false, message: "Loan not found", statusCode: "", mid: "" };

            let contractLink = "";

            if (loan.primaryCollectionType === "eNach" || loan.secondaryCollectionType === "eNach")
                contractLink = loan.loanContractDetails && loan.loanContractDetails.eNachUrl ? loan.loanContractDetails.eNachUrl : "";
            else
                contractLink = loan.loanContractDetails && loan.loanContractDetails.contractUrl ? loan.loanContractDetails.contractUrl : "";

            if (!contractLink)
                return { data: "Error", success: false, message: "Contract link not found", statusCode: "", mid: "" };

            const notificationTemplate = await NotificationTemplates.findOne({ partnerId: loan.partnerId, template: template, channel: "WhatsApp" });

            if (!notificationTemplate)
                return { data: "Error", success: false, message: "Notification template not found", statusCode: "", mid: "" };

            whatsAppTemplate = notificationTemplate.whatsAppTemplate;

            content = {
                preview_url: true,
                shorten_url: true,
                type: notificationTemplate.type,
                template: {
                    templateId: whatsAppTemplate,
                    parameterValues: {
                        0: name,
                        1: `${contractLink}?c=wa`,
                        2: config.get("karix.ffSupportLink")
                    }
                }
            };
        }
        else if (template === "Offer Notification-1") {
            const preApproval = await PreApprovals.findById(notificationList.preApprovalId);

            if (!preApproval)
                return { data: "Error", success: false, message: "PreApproval not found", statusCode: "", mid: "" };

            const notificationTemplate = await NotificationTemplates.findOne({ channel: "WhatsApp", template: template, partnerId: notificationList.partnerId, loanType: preApproval.loanType, productType: preApproval.productType });

            if (!notificationTemplate)
                return { data: "Error", success: false, message: "Notification Template not found", statusCode: "", mid: "" };

            const partnerMaster = await PartnerMaster.findById(notificationList.partnerId);

            let partnerLink = notificationTemplate.url1;

            let longUrl = null;
            let txnId = "";

            if (partnerMaster.partnerCode.toLowerCase() === "pay")
                txnId = preApproval.trxnId;
            else
                txnId = preApproval.productId ? preApproval.productId : preApproval.trxnId;

            if (notificationTemplate.isInternalOfferLink)
                longUrl = config.get("keys.webUrl") + `leadInfo/${preApproval.merchantCode}/${txnId}/${preApproval.approvedAmount}/${partnerMaster.shortauthKey ? partnerMaster.shortauthKey : partnerMaster.authKey}`;
            else
                longUrl = partnerLink;

            const generateCustomUrlResponse = await generateCustomUrl(longUrl, config.get("shortUrl.offerCode"), null, null, null, notificationList.merchantCode, partnerMaster.displayName, notificationList.preApprovalId, notificationList.partnerId, notificationList._id);

            if (generateCustomUrlResponse.success)
                partnerLink = generateCustomUrlResponse.data;
            else
                return { data: "Error", success: false, message: generateCustomUrlResponse.message, statusCode: "", mid: "" };

            whatsAppTemplate = notificationTemplate.whatsAppTemplate;
            let bodyParameterValues = null;
            let media = null

            let templateConfig = await NotificationEventTemplates.findOne({ eventId: notificationTemplate.eventId, template: whatsAppTemplate })

            let payload = {
                partnerName: notificationTemplate.partnerName,
                approvedAmount: preApproval.approvedAmount ? preApproval.approvedAmount.toString() : "",
                emiAmount: preApproval.emiAmt ? preApproval.emiAmt.toString() : "",
                totalInterest: preApproval.totalInterest ? preApproval.totalInterest.toString() : "",
                tenureInDays: preApproval.tenureInDays ? preApproval.tenureInDays.toString() : "",
                partnerLink: `${partnerLink}?c=wa`,
                ffSupportLink: config.get("karix.ffSupportLink")
            }

            let ctaPayload = {
                partnerLink: `${partnerLink}?c=wa`.replace(config.get("tinyUrl.urlPrefix"), "")
            }

            if (templateConfig && templateConfig.config) {
                bodyParameterValues = templateValueConfigEngine(templateConfig.config, payload, notificationTemplate)
            }

            if (!bodyParameterValues) return { data: "Error", success: false, message: "WhatsApp template not found", statusCode: "", mid: "" };

            let buttons = null

            if (templateConfig && templateConfig.ctaConfig && Object.keys(templateConfig.ctaConfig).length) {
                buttons = templateCTAConfigEngine(templateConfig.ctaConfig, ctaPayload, notificationTemplate)
            }

            if (templateConfig && templateConfig.isMedia) {

                let url = notificationTemplate.header && notificationTemplate.header.filePath ? `${config.get("awsSystemFilesCredentials.baseUrl")}${notificationTemplate.header.filePath}` : config.get("keys.defaultWhatsappMediaFile")
                let splittedUrl = url.split("/")

                media = {
                    "type": templateConfig.mediaType ? templateConfig.mediaType.toLowerCase() : "image",
                    "url": url,
                    "fileName": splittedUrl[splittedUrl.length - 1] || "fundfina-benefits.png"
                }
            }

            content = {
                preview_url: false,
                type: notificationTemplate.type,
                mediaTemplate: {
                    templateId: whatsAppTemplate,
                    media: media || undefined,
                    bodyParameterValues: bodyParameterValues,
                    buttons: buttons || undefined
                }
            };
        }
        else if (template === "loan_disbursal_eng") {
            const loan = await LoanInfo.findById(loanId);

            if (!loan)
                return { data: "Error", success: false, message: "Loan not found", statusCode: "", mid: "" };

            const partner = await PartnerMaster.findById(loan.partnerId);

            if (!partner)
                return { data: "Error", success: false, message: "Partner not found", statusCode: "", mid: "" };

            const amount = loan.amount ? loan.amount.toString() : "";

            if (!amount)
                return { data: "Error", success: false, message: "Loan amount not found", statusCode: "", mid: "" };

            const notificationTemplate = await NotificationTemplates.findOne({ partnerId: loan.partnerId, template: template, channel: "WhatsApp" });

            if (!notificationTemplate)
                return { data: "Error", success: false, message: "Notification template not found", statusCode: "", mid: "" };

            whatsAppTemplate = notificationTemplate.whatsAppTemplate;

            let txtParam4 = " ";

            const discount = await computeLoanCashback(loan._id, false);
            if (!discount.success)
                return { data: "Error", success: false, message: discount.message, statusCode: "", mid: "" };

            if (discount.discountAmount)
                txtParam4 = notificationTemplate.var4.replace("#discount", `₹${discount.discountAmount.toString()}`);


            let partnerLink = notificationTemplate.url1 | "";
            let bodyParameterValues = null;
            let media = null

            let templateConfig = await NotificationEventTemplates.findOne({ eventId: notificationTemplate.eventId, template: whatsAppTemplate })

            let payload = {
                name,
                amount,
                discountAmount: txtParam4,
                ffSupportLink: config.get("karix.ffSupportLink")
            }

            let ctaPayload = {
                partnerLink: `${partnerLink}?c=wa`.replace(config.get("tinyUrl.urlPrefix"), "")
            }

            if (templateConfig && templateConfig.config) {
                bodyParameterValues = templateValueConfigEngine(templateConfig.config, payload, notificationTemplate)
            }

            if (!bodyParameterValues) return { data: "Error", success: false, message: "WhatsApp template not found", statusCode: "", mid: "" };

            let buttons = null

            if (templateConfig && templateConfig.ctaConfig && Object.keys(templateConfig.ctaConfig).length) {
                buttons = templateCTAConfigEngine(templateConfig.ctaConfig, ctaPayload, notificationTemplate)
            }

            if (templateConfig && templateConfig.isMedia) {

                let url = notificationTemplate.header && notificationTemplate.header.filePath ? `${config.get("awsSystemFilesCredentials.baseUrl")}${notificationTemplate.header.filePath}` : config.get("keys.defaultWhatsappMediaFile")
                let splittedUrl = url.split("/")

                media = {
                    "type": templateConfig.mediaType ? templateConfig.mediaType.toLowerCase() : "image",
                    "url": url,
                    "fileName": splittedUrl[splittedUrl.length - 1] || "fundfina-benefits.png"
                }
            }

            if (notificationTemplate.type === "MEDIA_TEMPLATE") {
                content = {
                    preview_url: false,
                    type: notificationTemplate.type,
                    mediaTemplate: {
                        templateId: whatsAppTemplate,
                        media: media || undefined,
                        bodyParameterValues: bodyParameterValues,
                        buttons: buttons || undefined
                    }
                }
            } else {
                content = {
                    preview_url: true,
                    shorten_url: true,
                    type: notificationTemplate.type,
                    template: {
                        templateId: whatsAppTemplate,
                        parameterValues: bodyParameterValues
                    }
                };
            }

        }
        else if (template === "Offer Expiry Notification") {
            const preApproval = await PreApprovals.findById(notificationList.preApprovalId);

            if (!preApproval)
                return { data: "Error", success: false, message: "PreApproval not found", statusCode: "", mid: "" };

            const notificationTemplate = await NotificationTemplates.findOne({ channel: "WhatsApp", template: template, partnerId: notificationList.partnerId, loanType: preApproval.loanType, productType: preApproval.productType });

            if (!notificationTemplate)
                return { data: "Error", success: false, message: "Notification Template not found", statusCode: "", mid: "" };

            const partnerMaster = await PartnerMaster.findById(notificationList.partnerId);

            let partnerLink = notificationTemplate.url1;

            let longUrl = null;
            let txnId = "";

            if (partnerMaster.partnerCode.toLowerCase() === "pay")
                txnId = preApproval.trxnId;
            else
                txnId = preApproval.productId ? preApproval.productId : preApproval.trxnId;

            if (notificationTemplate.isInternalOfferLink)
                longUrl = config.get("keys.webUrl") + `leadInfo/${preApproval.merchantCode}/${txnId}/${preApproval.approvedAmount}/${partnerMaster.shortauthKey ? partnerMaster.shortauthKey : partnerMaster.authKey}`;
            else
                longUrl = partnerLink;

            const generateCustomUrlResponse = await generateCustomUrl(longUrl, config.get("shortUrl.offerCode"), null, null, null, notificationList.merchantCode, partnerMaster.displayName, notificationList.preApprovalId, notificationList.partnerId, notificationList._id);

            if (generateCustomUrlResponse.success)
                partnerLink = generateCustomUrlResponse.data;
            else
                return { data: "Error", success: false, message: generateCustomUrlResponse.message, statusCode: "", mid: "" };

            whatsAppTemplate = notificationTemplate.whatsAppTemplate;
            let bodyParameterValues = null;
            let media = null

            let templateConfig = await NotificationEventTemplates.findOne({ eventId: notificationTemplate.eventId, template: whatsAppTemplate })

            let payload = {
                name,
                expiryDate: getDMYFormattedDate(preApproval.expiredOn),
                partnerName: notificationTemplate.partnerName,
                partnerLink: `${partnerLink}?c=wa`,
                approvedAmount: preApproval.approvedAmount ? preApproval.approvedAmount.toString() : "",
                emiAmount: preApproval.emiAmt ? preApproval.emiAmt.toString() : "",
                totalInterest: preApproval.totalInterest ? preApproval.totalInterest.toString() : "",
                tenureInDays: preApproval.tenureInDays ? preApproval.tenureInDays.toString() : "",
                processingFee: preApproval.processingFeeAmount ? preApproval.processingFeeAmount.toString() : "",
                otherCharges: preApproval.stampingCharges ? preApproval.stampingCharges.toString() : "",
                disbursalAmount: preApproval.disbursedAmount ? preApproval.disbursedAmount.toString() : "",
                ffSupportLink: config.get("karix.ffSupportLink")
            }

            let ctaPayload = {
                partnerLink: `${partnerLink}?c=wa`.replace(config.get("tinyUrl.urlPrefix"), "")
            }

            if (templateConfig && templateConfig.config) {
                bodyParameterValues = templateValueConfigEngine(templateConfig.config, payload, notificationTemplate)
            }

            if (!bodyParameterValues) return { data: "Error", success: false, message: "WhatsApp template not found", statusCode: "", mid: "" };

            let buttons = null

            if (templateConfig && templateConfig.ctaConfig && Object.keys(templateConfig.ctaConfig).length) {
                buttons = templateCTAConfigEngine(templateConfig.ctaConfig, ctaPayload, notificationTemplate)
            }

            if (templateConfig && templateConfig.isMedia) {

                let url = notificationTemplate.header && notificationTemplate.header.filePath ? `${config.get("awsSystemFilesCredentials.baseUrl")}${notificationTemplate.header.filePath}` : config.get("keys.defaultWhatsappMediaFile")
                let splittedUrl = url.split("/")

                media = {
                    "type": templateConfig.mediaType ? templateConfig.mediaType.toLowerCase() : "image",
                    "url": url,
                    "fileName": splittedUrl[splittedUrl.length - 1] || "fundfina-benefits.png"
                }
            }

            content = {
                preview_url: false,
                type: notificationTemplate.type,
                mediaTemplate: {
                    templateId: whatsAppTemplate,
                    media: media || undefined,
                    bodyParameterValues: bodyParameterValues,
                    buttons: buttons || undefined
                }
            };
        }
        else if (template === "collections_loan_details_view") {

            const loan = await LoanInfo.findById(loanId);

            if (!loan) return { data: "Error", success: false, message: "Loan not found", statusCode: "", mid: "" };

            const notificationTemplate = await NotificationTemplates.findOne({ template: template, channel: "WhatsApp" });

            if (!notificationTemplate) return { data: "Error", success: false, message: "Notification template not found", statusCode: "", mid: "" };

            content = {
                preview_url: true,
                shorten_url: true,
                type: notificationTemplate.type,
                mediaTemplate: {
                    templateId: notificationTemplate.whatsAppTemplate,
                    bodyParameterValues: {
                        0: name,
                        1: loan.loanNo,
                    }
                }
            };

        }
        else if (template === "cashback_notification_eng") {
            const loanInfo = await LoanInfo.findById(loanId);
            const amount = loanInfo.discount.discountAmount;
            const notificationTemplate = await NotificationTemplates.findOne({ partnerId: loanInfo.partnerId, template: template, channel: "WhatsApp" });

            if (!notificationTemplate)
                return { data: "Error", success: false, message: "Notification template not found", statusCode: "", mid: "" };


            whatsAppTemplate = notificationTemplate.whatsAppTemplate;

            content = {
                preview_url: true,
                shorten_url: true,
                type: notificationTemplate.type,
                template: {
                    templateId: whatsAppTemplate,
                    parameterValues: {
                        0: name,
                        1: amount,
                        2: config.get("karix.ffSupportLink")
                    }
                }
            };

            template = notificationTemplate.smsTemplate;
        }
        else if (template === "otp_confirmation") {
            whatsAppTemplate = template;

            content = {
                preview_url: true,
                shorten_url: true,
                type: config.get("karix.type"),
                template: {
                    templateId: whatsAppTemplate,
                    parameterValues: {
                        0: notificationList.smsModel.VAR1,
                        1: notificationList.smsModel.VAR2
                    }
                }
            };
        }
        else if (template === "nach_failure") {

            const loan = await LoanInfo.findById(loanId);

            if (!loan) return { data: "Error", success: false, message: "Loan not found", statusCode: "", mid: "" };

            const notificationTemplate = await NotificationTemplates.findOne({ partnerId: loan.partnerId, template: template, channel: "WhatsApp" });

            if (!notificationTemplate)
                return { data: "Error", success: false, message: "Notification template not found", statusCode: "", mid: "" };

            whatsAppTemplate = notificationList.smsModel.TemplateName

            let bodyParameterValues = null;
            let media = null

            let templateConfig = await NotificationEventTemplates.findOne({ eventId: notificationTemplate.eventId, template: whatsAppTemplate })

            let payload = {
                name: notificationList.smsModel.VAR1,
                amount: notificationList.smsModel.VAR2,
                mode: notificationList.smsModel.VAR3,
                rejectReason: notificationList.smsModel.VAR4 === "" ? " " : notificationList.smsModel.VAR4,
                // : notificationList.smsModel.VAR5 === "" ? " " : notificationList.smsModel.VAR5,
                ffSupportLink: notificationList.smsModel.VAR6
            }

            let ctaPayload = {
                nachLink: notificationList.smsModel.CTA.replace(config.get("tinyUrl.urlPrefix"), "")
            }

            if (templateConfig && templateConfig.config) {
                bodyParameterValues = templateValueConfigEngine(templateConfig.config, payload, notificationTemplate)
            }

            if (!bodyParameterValues) return { data: "Error", success: false, message: "WhatsApp template not found", statusCode: "", mid: "" };

            let buttons = null

            if (templateConfig && templateConfig.ctaConfig && Object.keys(templateConfig.ctaConfig).length) {
                buttons = templateCTAConfigEngine(templateConfig.ctaConfig, ctaPayload, notificationTemplate)
            }

            if (templateConfig && templateConfig.isMedia) {

                let url = notificationTemplate.header && notificationTemplate.header.filePath ? `${config.get("awsSystemFilesCredentials.baseUrl")}${notificationTemplate.header.filePath}` : config.get("keys.defaultWhatsappMediaFile")
                let splittedUrl = url.split("/")

                media = {
                    "type": templateConfig.mediaType ? templateConfig.mediaType.toLowerCase() : "image",
                    "url": url,
                    "fileName": splittedUrl[splittedUrl.length - 1] || "fundfina-benefits.png"
                }
            }

            if (notificationList.smsModel.Type === "MEDIA_TEMPLATE") {
                content = {
                    preview_url: false,
                    type: notificationList.smsModel.Type,
                    mediaTemplate: {
                        templateId: whatsAppTemplate,
                        media: media || undefined,
                        bodyParameterValues: bodyParameterValues,
                        buttons: buttons || undefined
                    }
                }
            } else {
                content = {
                    preview_url: true,
                    shorten_url: true,
                    type: notificationList.smsModel.Type,
                    template: {
                        templateId: whatsAppTemplate,
                        parameterValues: bodyParameterValues
                    }
                };
            }

        }
        else if (template === "physical_nach_link") {

            const notificationTemplate = await NotificationTemplates.findOne({ channel: "WhatsApp", template: template, partnerId: notificationList.partnerId });

            if (!notificationTemplate)
                return { data: "Error", success: false, message: "Notification Template not found", statusCode: "", mid: "" };

            whatsAppTemplate = notificationTemplate.whatsAppTemplate;

            let bodyParameterValues = {
                0: notificationList.smsModel.VAR1,
                1: notificationList.smsModel.VAR2,
                2: notificationList.smsModel.VAR3,
                3: notificationList.smsModel.VAR4,
                4: notificationList.smsModel.VAR5
            }

            content = {
                preview_url: false,
                type: notificationTemplate.type,
                mediaTemplate: {
                    templateId: whatsAppTemplate,
                    bodyParameterValues: bodyParameterValues,
                    media: {
                        type: "document",
                        url: notificationList.smsModel.Attachment1,
                        fileName: "physicalNach.pdf"
                    },
                    buttons: {
                        actions: [
                            {
                                type: "url",
                                index: "0",
                                payload: notificationList.smsModel.CTA
                            }
                        ]
                    }
                }
            };
        }
        else if (template === "loan_review") {
            content = {
                preview_url: false,
                type: notificationList.smsModel.Type,
                mediaTemplate: {
                    templateId: notificationList.smsModel.TemplateName,
                    bodyParameterValues: {
                        0: notificationList.smsModel.VAR1,
                        1: notificationList.smsModel.VAR2,
                        2: notificationList.smsModel.VAR3,
                        3: notificationList.smsModel.VAR4,
                        4: notificationList.smsModel.VAR5
                    },
                    buttons: {
                        actions: [
                            {
                                type: "url",
                                index: "0",
                                payload: notificationList.smsModel.CTA.replace(config.get("tinyUrl.urlPrefix"), "")
                            }
                        ]
                    }
                }
            }
        }
        else if (template == "payment_link_v2" || template == "payment_link_reminder_v2") {

            let meta = notificationList.meta;
            if (!meta || (meta && !Object.keys(meta).length))
                return { data: "Error", success: false, message: "Meta not found", statusCode: "", mid: "" };

            const notificationTemplate = await NotificationTemplates.findOne({ template: template, channel: "WhatsApp" });

            if (!notificationTemplate)
                return { data: "Error", success: false, message: "Notification template not found", statusCode: "", mid: "" };

            //let linkSplit = meta.link.split('/');

            //let urlBuild = linkSplit[linkSplit.length - 1];
            //let urlBuild = linkSplit[linkSplit.length - 2] + '/' + linkSplit[linkSplit.length - 1];
            let ffSupportLink = config.get("karix.ffSupportNumber");
            content = {
                preview_url: false,
                type: "MEDIA_TEMPLATE",
                mediaTemplate: {
                    templateId: notificationTemplate.whatsAppTemplate,
                    bodyParameterValues: {
                        0: name,
                        1: meta.collectionAmount,
                        2: meta.totalPendingAmount,
                        3: meta.loanNo,
                        4: meta.partnerName,
                        5: ffSupportLink,
                        6: meta.fullLink
                    },
                    buttons: {
                        actions: [
                            {
                                "type": "url",
                                "index": "0",
                                "payload": meta.link
                            }
                        ]
                    }
                }

            }
        }
        else if (template == "payment_link_cf_v2" || template == "payment_link_cf_reminder_v1") {

            let meta = notificationList.meta;
            if (!meta || (meta && !Object.keys(meta).length))
                return { data: "Error", success: false, message: "Meta not found", statusCode: "", mid: "" };

            const notificationTemplate = await NotificationTemplates.findOne({ template: template, channel: "WhatsApp" });

            if (!notificationTemplate)
                return { data: "Error", success: false, message: "Notification template not found", statusCode: "", mid: "" };

            let ffSupportLink = config.get("karix.ffSupportNumber");
            content = {
                preview_url: false,
                type: "MEDIA_TEMPLATE",
                mediaTemplate: {
                    templateId: notificationTemplate.whatsAppTemplate,
                    bodyParameterValues: {
                        0: name,
                        1: meta.collectionAmount,
                        2: meta.totalPendingAmount,
                        3: meta.loanNo,
                        4: meta.partnerName,
                        5: ffSupportLink,
                        6: meta.fullLink
                    },
                    buttons: {
                        actions: [
                            {
                                "type": "url",
                                "index": "0",
                                "payload": meta.link
                            }
                        ]
                    }
                }

            }
        }
        else if (template == "payment_confirmation_v1") {

            let meta = notificationList.meta;
            if (!meta || (meta && !Object.keys(meta).length))
                return { data: "Error", success: false, message: "Meta not found", statusCode: "", mid: "" };

            const notificationTemplate = await NotificationTemplates.findOne({ template: template, channel: "WhatsApp" });

            if (!notificationTemplate)
                return { data: "Error", success: false, message: "Notification template not found", statusCode: "", mid: "" };

            let ffSupportLink = config.get("karix.ffSupportNumber");
            content = {
                preview_url: false,
                type: "MEDIA_TEMPLATE",
                mediaTemplate: {
                    templateId: notificationTemplate.whatsAppTemplate,
                    bodyParameterValues: {
                        0: meta.amount,
                        1: meta.loanNo,
                        2: meta.partnerName,
                        3: ffSupportLink
                    }
                }

            }
        }
        else if (template == "payment_link_agent_v1") {

            let meta = notificationList.meta;
            if (!meta || (meta && !Object.keys(meta).length))
                return { data: "Error", success: false, message: "Meta not found", statusCode: "", mid: "" };

            const notificationTemplate = await NotificationTemplates.findOne({ template: template, channel: "WhatsApp" });

            if (!notificationTemplate)
                return { data: "Error", success: false, message: "Notification template not found", statusCode: "", mid: "" };

            content = {
                preview_url: false,
                type: "MEDIA_TEMPLATE",
                mediaTemplate: {
                    templateId: notificationTemplate.whatsAppTemplate,
                    bodyParameterValues: {
                        0: meta.collectionAmount,
                        1: meta.totalPendingAmount,
                        2: meta.loanNo,
                        3: meta.partnerName,
                        4: meta.fullLink
                    }
                }

            }
        } else if (template == "agent_details") {
            let meta = notificationList.meta;
            content = {
                preview_url: false,
                type: "MEDIA_TEMPLATE",
                mediaTemplate: {
                    templateId: template,
                    bodyParameterValues: {
                        0: meta.merchantName,
                        1: meta.agentName,
                        2: meta.agentMobileNo,
                        3: meta.fundfinaContact
                    },
                    media: {
                        type: "image",
                        url: meta.fileUrl,
                        fileName: meta.fileName || "agent.jpeg"
                    },
                }
            }
            sender = config.get("karix.mobileNo2");
        }
        else if (template == "pre_legal_collection") {
            const notificationTemplate = await NotificationTemplates.findOne({ template: template, channel: "WhatsApp" });

            if (!notificationTemplate)
                return { data: "Error", success: false, message: "Notification template not found", statusCode: "", mid: "" };

            content = {
                preview_url: false,
                type: notificationList.smsModel.Type,
                mediaTemplate: {
                    templateId: notificationTemplate.whatsAppTemplate,
                    media: {
                        type: "document",
                        url: notificationList.smsModel.Attachment1,
                        fileName: notificationList.smsModel.AttachmentName1
                    },
                    bodyParameterValues: {
                        0: notificationList.smsModel.VAR1,
                        1: notificationList.smsModel.VAR2,
                        2: notificationList.smsModel.VAR3,
                        3: notificationList.smsModel.VAR4
                    }
                }
            }

            sender = config.get("karix.mobileNo2");
        } else if (template == "collection_case_notification") {
            let bodyParameterValues = {}

            for (let i = 0; i < 13; i++) {
                let key = `VAR${i + 1}`
                if (notificationList.smsModel[key]) {
                    bodyParameterValues[i] = notificationList.smsModel[key]
                }
            }

            content = {
                preview_url: true,
                type: notificationList.smsModel.Type,

                mediaTemplate: {
                    templateId: notificationList.meta.template,
                    media: {
                        type: "image",
                        url: notificationList.smsModel.Attachment1,
                        fileName: notificationList.smsModel.AttachmentName1
                    },
                    bodyParameterValues: bodyParameterValues
                }
            }

            sender = config.get("karix.mobileNo2");
        } else if (template == "enach_cancellation_request" || template == "enach_cancellation_confirmation") {
            let bodyParameterValues = {}

            for (let i = 0; i < 13; i++) {
                let key = `VAR${i + 1}`
                if (notificationList.smsModel[key]) {
                    bodyParameterValues[i] = notificationList.smsModel[key]
                }
            }

            content = {
                preview_url: false,
                type: "TEMPLATE",
                template: {
                    templateId: template,
                    parameterValues: bodyParameterValues
                }
            };
            sender = config.get("karix.mobileNo2");
        }
        else if (template == "noc_repayment") {
            const notificationTemplate = await NotificationTemplates.findOne({ template: template, channel: "WhatsApp" });

            if (!notificationTemplate)
                return { data: "Error", success: false, message: "Notification template not found", statusCode: "", mid: "" };

            const loan = await LoanInfo.findById(loanId).select({ _id: 1, userId: 1, loanNo: 1, merchantCode: 1, partner: 1 });

            if (!loan)
                return { data: "Error", success: false, message: "Loan not found", statusCode: "", mid: "" };

            // const type = config.get("shortUrl.documentLink");
            // const generateCustomUrlResponse = await generateCustomUrl(signUrl, type, loan.userId, loan._id, loan.loanNo, loan.merchantCode, loan.partner, null);

            // if (!generateCustomUrlResponse.success)
            //     return { data: "Error", success: false, message: generateCustomUrlResponse.message, statusCode: "", mid: "" };

            content = {
                preview_url: false,
                type: notificationList.smsModel.Type,
                mediaTemplate: {
                    templateId: notificationTemplate.whatsAppTemplate,
                    media: {
                        type: "document",
                        url: notificationList.smsModel.AttachmentPath1,
                        fileName: "noc.pdf"
                    },
                    bodyParameterValues: {
                        0: notificationList.smsModel.VAR1,
                        1: notificationList.smsModel.VAR2,
                        2: notificationList.smsModel.VAR3
                    }
                }
            }

            sender = config.get("karix.mobileNo2");
        }
        else if (template == "pay_online") {
            const notificationTemplate = await NotificationTemplates.findOne({ template: template, channel: "WhatsApp" });

            if (!notificationTemplate)
                return { data: "Error", success: false, message: "Notification template not found", statusCode: "", mid: "" };

            let templateConfig = await NotificationEventTemplates.findOne({ eventId: notificationTemplate.eventId, template: template });

            if (templateConfig && templateConfig.isMedia) {

                let url = smsModel.AttachmentPath1;
                let splittedUrl = url.split("/")

                media = {
                    "type": templateConfig.mediaType ? templateConfig.mediaType.toLowerCase() : "video",
                    "url": url,
                    "fileName": splittedUrl[splittedUrl.length - 1] || "file.mp4"
                }
            }

            content = {
                preview_url: false,
                type: notificationList.smsModel.Type,
                mediaTemplate: {
                    templateId: notificationTemplate.whatsAppTemplate,
                    media: media || undefined,
                    bodyParameterValues: {
                        0: notificationList.smsModel.VAR1,
                        1: notificationList.smsModel.VAR2,
                        2: notificationList.smsModel.VAR3,
                        3: notificationList.smsModel.VAR4,
                        4: notificationList.smsModel.VAR5
                    }
                }
            }

            sender = config.get("karix.mobileNo2");
        }

        const model = {
            message: {
                channel: config.get("karix.channel"),
                content: content,
                recipient: {
                    to: mobileNo,
                    recipient_type: config.get("karix.recipient_type")
                },
                sender: {
                    from: sender
                },
                preferences: {
                    webHookDNId: config.get("karix.webHookDNId")
                }
            },
            metaData: {
                version: config.get("karix.version")
            }
        }

        const response = await sendMessage(userId, loanId, model, merchantCode, trxnId, productId);

        if (response.success && sendMailFlag)
            await sendMail(userId, loanId, mailCategory, mailSubCategory, mailModel, mailUserType);

        return response;
    }
    catch (ex) {
        saveErrorLog("sendWhatsAppMessage", apiPath, { userId, loanId, template }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        return {
            data: "Error",
            success: false,
            message: ex.message,
            statusCode: "",
            mid: ""
        }
    }
}

const templateValueConfigEngine = (config, payload, notificationTemplate = {}) => {

    let values = {}
    try {

        let index = 0

        for (let key in config) {
            let obj = config[key]

            if (obj.type === "DYNAMIC") {
                values[index] = payload[obj.value] || " "
            } else {
                values[index] = notificationTemplate ? notificationTemplate[key] || " " : " "
            }

            index++
        }
        return values

    } catch (e) {
        saveErrorLog("templateValueConfigEngine", "templateValueConfigEngine", payload, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        return null
    }

}

const templateCTAConfigEngine = (config, payload, notificationTemplate = {}) => {

    let actions = []
    try {

        let index = 0

        for (let key in config) {
            let obj = config[key]

            let valueObj = {
                type: "url",
                index: `${index}`,
                payload: " "
            }

            if (obj.type === "DYNAMIC") {
                valueObj.payload = payload[obj.value] || " "
            } else {
                valueObj.payload = notificationTemplate ? notificationTemplate[key] || " " : " "
            }
            actions.push(valueObj)
            index++
        }
        return { actions }

    } catch (e) {
        saveErrorLog("templateValueConfigEngine", "templateValueConfigEngine", payload, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        return null
    }

}

async function sendMessage(userId, loanId, model, merchantCode, trxnId, productId) {

    let requestSentOn = Date.now();
    const url = config.get('karix.url');

    try {

        const response = await callKarixApi("POST", url, model);

        const dataModel = new WhatsAppLog({
            userId: userId ? userId : null,
            loanId: loanId ? loanId : null,
            merchantCode: merchantCode ? merchantCode : "",
            trxnId: trxnId ? trxnId : "",
            productId: productId ? productId : "",
            request: JSON.stringify(model),
            response: JSON.stringify(response),
            requestSentOn: requestSentOn,
            responseReceivedOn: Date.now(),
            mid: response.mid ? response.mid : "",
            currentStatus: response.statusDesc ? response.statusDesc : "",
            statusUpdatedOn: Date.now(),
            code: response.statusCode ? response.statusCode : "",
            mobileNo: model.message && model.message.recipient && model.message.recipient.to ? model.message.recipient.to : "",
            template: model.message && model.message.content && model.message.content.template && model.message.content.template.templateId ? model.message.content.template.templateId : "",
            type: model.message && model.message.content && model.message.content.type ? model.message.content.type : "",
            url: url,
            channel: model.message && model.message.channel ? model.message.channel : "",
            parameters: model.message && model.message.content && model.message.content.template && model.message.content.template.parameterValues ? JSON.stringify(model.message.content.template.parameterValues) : ""
        });

        await dataModel.save();

        return { data: response.statusDesc ? response.statusDesc : "", success: response.statusCode === "200" ? true : false, message: response.statusDesc ? response.statusDesc : "", statusCode: response.statusCode ? response.statusCode : "", mid: response.mid ? response.mid : "" };
    }
    catch (ex) {
        saveErrorLog("sendMessage", apiPath, { userId, loanId, model, merchantCode, trxnId, productId }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        const dataModel = new WhatsAppLog({
            request: JSON.stringify(model),
            response: JSON.stringify(ex),
            requestSentOn: requestSentOn,
            responseReceivedOn: Date.now(),
            mobileNo: model.message && model.message.recipient && model.message.recipient.to ? model.message.recipient.to : "",
            template: model.message && model.message.content && model.message.content.template && model.message.content.template.templateId ? model.message.content.template.templateId : "",
            type: model.message && model.message.content && model.message.content.type ? model.message.content.type : "",
            url: url,
            channel: model.message && model.message.channel ? model.message.channel : "",
            parameters: model.message && model.message.content && model.message.content.template && model.message.content.template.parameterValues ? JSON.stringify(model.message.content.template.parameterValues) : ""
        });

        await dataModel.save();

        return { data: "Error", success: false, message: ex.message, statusCode: 200, mid: "" };
    }
}

module.exports.whatsAppWebHook = async function (model) {
    try {
        const dataModel = new WAWebhookLog({
            response: JSON.stringify(model),
            insertedOn: Date.now(),
            eventType: model.events && model.events.eventType ? model.events.eventType : "",
            channel: model.channel ? model.channel : "",
            recipient: {
                to: model.recipient && model.recipient.to ? model.recipient.to : "",
                recipient_type: model.recipient && model.recipient.recipient_type ? model.recipient.recipient_type : "",
                reference: {
                    cust_ref: model.recipient && model.recipient.reference && model.recipient.reference.cust_ref ? model.recipient.reference.cust_ref : "",
                    messageTag1: model.recipient && model.recipient.reference && model.recipient.reference.messageTag1 ? model.recipient.reference.messageTag1 : "",
                    conversationId: model.recipient && model.recipient.reference && model.recipient.reference.conversationId ? model.recipient.reference.conversationId : ""
                }
            },
            events: {
                eventType: model.events && model.events.eventType ? model.events.eventType : "",
                timestamp: model.events && model.events.timestamp ? new Date(parseInt(model.events.timestamp)) : null,
                date: model.events && model.events.date ? model.events.date : "",
                mid: model.events && model.events.mid ? model.events.mid : ""
            },
            eventContent: {
                message: {
                    from: model.eventContent && model.eventContent.message && model.eventContent.message.from ? model.eventContent.message.from : "",
                    id: model.eventContent && model.eventContent.message && model.eventContent.message.id ? model.eventContent.message.id : "",
                    text: {
                        body: model.eventContent && model.eventContent.message && model.eventContent.message.text && model.eventContent.message.text.body ? model.eventContent.message.text.body : "",
                    },
                    document: {
                        caption: model.eventContent && model.eventContent.message && model.eventContent.message.document && model.eventContent.message.document.caption ? model.eventContent.message.document.caption : "",
                        filename: model.eventContent && model.eventContent.message && model.eventContent.message.document && model.eventContent.message.document.filename ? model.eventContent.message.document.filename : "",
                        mime_type: model.eventContent && model.eventContent.message && model.eventContent.message.document && model.eventContent.message.document.mime_type ? model.eventContent.message.document.mime_type : "",
                        sha5: model.eventContent && model.eventContent.message && model.eventContent.message.document && model.eventContent.message.document.sha5 ? model.eventContent.message.document.sha5 : "",
                        fileLink: model.eventContent && model.eventContent.message && model.eventContent.message.document && model.eventContent.message.document.fileLink ? model.eventContent.message.document.fileLink : ""
                    },
                    image: {
                        caption: model.eventContent && model.eventContent.message && model.eventContent.message.image && model.eventContent.message.image.caption ? model.eventContent.message.image.caption : "",
                        mime_type: model.eventContent && model.eventContent.message && model.eventContent.message.image && model.eventContent.message.image.mime_type ? model.eventContent.message.image.mime_type : "",
                        sha5: model.eventContent && model.eventContent.message && model.eventContent.message.image && model.eventContent.message.image.sha5 ? model.eventContent.message.image.sha5 : "",
                        fileLink: model.eventContent && model.eventContent.message && model.eventContent.message.image && model.eventContent.message.image.fileLink ? model.eventContent.message.image.fileLink : ""
                    },
                    location: {
                        latitude: model.eventContent && model.eventContent.message && model.eventContent.message.location && model.eventContent.message.location.latitude ? model.eventContent.message.location.latitude : "",
                        longitude: model.eventContent && model.eventContent.message && model.eventContent.message.location && model.eventContent.message.location.longitude ? model.eventContent.message.location.longitude : ""
                    },
                    video: {
                        mime_type: model.eventContent && model.eventContent.message && model.eventContent.message.video && model.eventContent.message.video.mime_type ? model.eventContent.message.video.mime_type : "",
                        sha5: model.eventContent && model.eventContent.message && model.eventContent.message.video && model.eventContent.message.video.sha5 ? model.eventContent.message.video.sha5 : "",
                        fileLink: model.eventContent && model.eventContent.message && model.eventContent.message.video && model.eventContent.message.video.fileLink ? model.eventContent.message.video.fileLink : ""
                    },
                    button: {
                        payload: model.eventContent && model.eventContent.message && model.eventContent.message.button && model.eventContent.message.button.payload ? model.eventContent.message.button.payload : "",
                        text: model.eventContent && model.eventContent.message && model.eventContent.message.button && model.eventContent.message.button.text ? model.eventContent.message.button.text : ""
                    },
                    to: model.eventContent && model.eventContent.message && model.eventContent.message.to ? model.eventContent.message.to : "",
                    contentType: model.eventContent && model.eventContent.message && model.eventContent.message.contentType ? model.eventContent.message.contentType : "",
                    contextmid: model.eventContent && model.eventContent.message && model.eventContent.message.contextmid ? model.eventContent.message.contextmid : "",
                    attachmentType: model.eventContent && model.eventContent.message && model.eventContent.message.attachmentType ? model.eventContent.message.attachmentType : ""
                }
            },
            notificationAttributes: {
                status: model.notificationAttributes && model.notificationAttributes.status ? model.notificationAttributes.status : "",
                reason: model.notificationAttributes && model.notificationAttributes.reason ? model.notificationAttributes.reason : "",
                code: model.notificationAttributes && model.notificationAttributes.code ? model.notificationAttributes.code : ""
            },
            smsFallback: {
                reason: model.smsFallback && model.smsFallback.reason ? model.smsFallback.reason : "",
                status: model.smsFallback && model.smsFallback.status ? model.smsFallback.status : ""
            }
        });

        if (model.contacts && model.contacts.length > 0) {
            for (const contact of model.contacts) {
                const item = {
                    addresses: contact.addresses ? contact.addresses : [],
                    emails: contact.emails ? contact.emails : [],
                    name: {
                        first_name: contact.name && contact.name.first_name ? contact.name.first_name : "",
                        formatted_name: contact.name && contact.name.formatted_name ? contact.name.formatted_name : ""
                    },
                    org: contact.org ? contact.org : {},
                    phones: contact.phones ? contact.phones : [],
                    urls: contact.urls ? contact.urls : []
                };

                dataModel.contacts.push(item);

            }
        }

        await dataModel.save();

        if (dataModel.eventType && dataModel.eventType.toLowerCase() === "delivery events") {

            const mid = dataModel.events && dataModel.events.mid ? dataModel.events.mid : "";

            if (mid) {

                const waLog = await WhatsAppLog.findOne({ mid: mid });

                if (waLog) {

                    const deliveryStatus = {
                        eventType: dataModel.eventType,
                        timestamp: model.events && model.events.timestamp ? model.events.timestamp.toString() : "",
                        date: model.events && model.events.date ? model.events.date : "",
                        mid: model.events && model.events.mid ? model.events.mid : "",
                        status: model.notificationAttributes && model.notificationAttributes.status ? model.notificationAttributes.status : "",
                        reason: model.notificationAttributes && model.notificationAttributes.reason ? model.notificationAttributes.reason : "",
                        code: model.notificationAttributes && model.notificationAttributes.code ? model.notificationAttributes.code : "",
                        insertedOn: Date.now()
                    };

                    if (!waLog.statusUpdatedOn || (waLog.statusUpdatedOn && new Date(Number(model.events.timestamp)) > waLog.statusUpdatedOn)) {
                        waLog.currentStatus = model.notificationAttributes && model.notificationAttributes.status ? model.notificationAttributes.status : "";
                        waLog.statusUpdatedOn = new Date(Number(model.events.timestamp));
                        waLog.code = model.notificationAttributes && model.notificationAttributes.code ? model.notificationAttributes.code : "";
                        waLog.reason = model.notificationAttributes && model.notificationAttributes.reason ? model.notificationAttributes.reason : "";
                    }
                    waLog.deliveryStatus.push(deliveryStatus);

                    await waLog.save();

                }

                const notificationCollection = await NotificationsList.findOne({ responseCode: mid, "channel": "WhatsApp" });

                if (notificationCollection) {

                    if (!notificationCollection.eventTimeStamp || (notificationCollection.eventTimeStamp && new Date(Number(model.events.timestamp)) > notificationCollection.eventTimeStamp)) {

                        notificationCollection.status = model.notificationAttributes && model.notificationAttributes.status ? model.notificationAttributes.status : "";
                        notificationCollection.reason = model.notificationAttributes && model.notificationAttributes.reason ? model.notificationAttributes.reason : "";

                        notificationCollection.eventTimeStamp = new Date(Number(model.events.timestamp));

                        if (notificationCollection.status && notificationCollection.status.toLowerCase() === "delivered")
                            notificationCollection.deliveredOn = new Date(Number(model.events.timestamp));

                        if (notificationCollection.status && notificationCollection.status.toLowerCase() === "read")
                            notificationCollection.readOn = new Date(Number(model.events.timestamp));


                        await notificationCollection.save();

                        if (notificationCollection.status.toLowerCase() === "delivered" || notificationCollection.status.toLowerCase() === "read") {
                            let updateUserPayload = {
                                userId: notificationCollection.userId,
                                mobileNumber: notificationCollection.mobileNo,
                                lastMessageSentOn: new Date(Number(model.events.timestamp))
                            }
                            updateMobileNumberChannelDetails("WHATSAPP", updateUserPayload)
                        }


                        //incase if enable this block, please take care of mobile number changes.
                        // if (notificationCollection.status && notificationCollection.status.toLowerCase() === "not sent") {

                        //     let mobileNo = notificationCollection.mobileNo;

                        //     let type = "SMS";

                        //     //Check if there is alternate mobile number
                        //     if (notificationCollection.userId) {
                        //         const user = await UserInfo.findById(notificationCollection.userId).select({
                        //             _id: 1,
                        //             userDetails: 1
                        //         });

                        //         const index = user.userDetails && user.userDetails.mobileNo && user.userDetails.mobileNo.length > 0 ? user.userDetails.mobileNo.indexOf(mobileNo) : -1;

                        //         let alternateNumber = null;

                        //         if (index > -1) {
                        //             alternateNumber = user.userDetails.mobileNo && user.userDetails.mobileNo[index + 1] ? user.userDetails.mobileNo[index + 1] : null;

                        //             if (alternateNumber) {
                        //                 mobileNo = alternateNumber;
                        //                 type = "WhatsApp";
                        //             }
                        //         }

                        //         //If SMS then send to primary number only
                        //         if (!alternateNumber && notificationCollection.numberType === "secondary")
                        //             mobileNo = user.userDetails.mobileNo && user.userDetails.mobileNo[0] ? user.userDetails.mobileNo[0] : notificationCollection.mobileNo;
                        //     }

                        //     //if alternate mobile number found then send WhatsApp else send SMS
                        //     const pushToNotification = await pushToNotifications(notificationCollection.partnerId, notificationCollection.merchantCode, notificationCollection.productId, notificationCollection.trxnId, notificationCollection.userId, notificationCollection.loanId,
                        //         type, mobileNo, notificationCollection.templateType, "loan", null, notificationCollection.preApprovalId, null, type === "SMS" ? true : false, type === "SMS" ? "TSMS" : null, type === "SMS" ? "primary" : "secondary");

                        //     //Call sendnotifications
                        //     if (pushToNotification.success) {
                        //         const sendNotifcation = await postDisbursal.sendNotifications(pushToNotification.data._id);

                        //         if (sendNotifcation.success) {
                        //             notificationCollection.fallbackMessageId = pushToNotification.data._id;
                        //             await notificationCollection.save();
                        //         }
                        //     }
                        // }

                    }

                }

            }
        }

        //Capture STOP text and update UserInfo
        if (dataModel.eventType && dataModel.eventType.toLowerCase() === "user initiated") {

            if (dataModel.eventContent && dataModel.eventContent.message && dataModel.eventContent.message.contentType && dataModel.eventContent.message.contentType.toLowerCase() === "text") {

                if (dataModel.eventContent.message.text && dataModel.eventContent.message.text.body && dataModel.eventContent.message.text.body.toLowerCase() === "stop") {
                    let mobileNo = model.eventContent && model.eventContent.message && model.eventContent.message.from ? model.eventContent.message.from : "";

                    let countryCode = "91";

                    if (mobileNo) {
                        if (mobileNo.length === 12) {
                            countryCode = mobileNo.substring(0, 2);
                            mobileNo = mobileNo.substring(2);
                        }

                        const dndRecord = await DND.findOne({ mobileNo: mobileNo, channel: "WhatsApp" });

                        if (dndRecord) {
                            dndRecord.isActive = true;
                            dndRecord.updatedOn = Date.now();
                            dndRecord.countryCode = countryCode;

                            await dndRecord.save();
                        }
                        else {
                            const dndRecord = new DND({
                                mobileNo: mobileNo,
                                countryCode: countryCode,
                                channel: "WhatsApp",
                                isActive: true,
                                insertedOn: Date.now(),
                                updatedOn: null
                            });

                            await dndRecord.save();
                        }
                    }
                }

            }

        }

        return { data: "", success: true, message: "Saved successfully" };
    }
    catch (ex) {
        saveErrorLog("whatsAppWebHook", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        const dataModel = new WAWebhookLog({
            response: JSON.stringify(model),
            insertedOn: Date.now(),
            exception: ex.message
        });

        await dataModel.save();

        return { data: "Error Occurred", success: false, message: ex.message };
    }
}

function capitalizeFirstLetter(string) {
    if (string)
        return string.charAt(0).toUpperCase() + string.slice(1).toLowerCase();
    else
        return "";
}

async function getLoanCalculations(loanId) {
    try {
        const loan = await LoanInfo.findById(loanId);

        let emiAmount = 0;
        let gstCharges = 0;
        let processingCharges = 0;
        let stampingCharges = 0;
        let disbursalAmount = 0;
        let chargeInPer = 0;
        let totalInterest = 0;
        const lateCharges = config.get("penaltyCharges");

        if (!loan)
            return {
                data: {
                    display: false
                },
                success: false,
                message: "Loan Id not found",
                status: 200
            }

        let borrowerPartner = await PartnerMaster.findById(
            loan.partnerId
        );

        if (!borrowerPartner)
            return {
                data: {
                    display: false
                },
                success: false,
                message: "Partner not found",
                status: 200
            }

        if (!loan.partnerId || !loan.interestRate || !loan.repaymentFrequency || !loan.tenure)
            return {
                data: {
                    display: false
                },
                success: false,
                message: "Loan Information not sufficient",
                status: 200
            }

        const charges = await loanService.getLoanCharges(loan.amount, loan._id, null);

        const today = new Date();
        const emiDate = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 5, 30, 1, 0);
        const disbursalDate = loan.productType == "DL_30" ? dateAdd("day", -1, emiDate) : emiDate;

        let installmentDetails = await loanService.emiSchedule(loan._id, disbursalDate, false);

        if (!IsNullEmpty(installmentDetails) && !IsNullEmpty(installmentDetails.data)) {
            if (installmentDetails.data.length > 0) {
                emiAmount = installmentDetails.data[0].rePaymentAmount;
                disbursalAmount = loan.productType == "DL_30" ? (charges.disbursalAmount - installmentDetails.data[0].rePaymentAmount) : charges.disbursalAmount;
            }
        }

        totalInterest = installmentDetails.data
            .reduce(function (prev, cur) {
                return prev + cur.interestAmount;
            }, 0);


        let tenure = "";

        if (loan.isMaturityMonth) tenure = loan.maturity.toString() + " Months";
        else if (loan.repaymentFrequency && loan.repaymentFrequency.toLowerCase() === "daily"
        ) {
            tenure = loan.maturity.toString() + " Days";
            emiAmount = emiAmount + " per day";
        } else if (loan.repaymentFrequency && loan.repaymentFrequency.toLowerCase() === "weekly") {
            tenure = loan.maturity.toString() + " Weeks";
            emiAmount = emiAmount + " per week";
        } else if (loan.repaymentFrequency && loan.repaymentFrequency.toLowerCase() === "monthly") {
            tenure = loan.maturity.toString() + " Months";
            emiAmount = emiAmount + " per month";
        } else if (loan.repaymentFrequency && loan.repaymentFrequency.toLowerCase() === "fortnightly") {
            tenure = loan.maturity.toString() + " Fortnights";
            emiAmount = emiAmount + " per fortnight";
        } else if (loan.repaymentFrequency && loan.repaymentFrequency.toLowerCase() === "adhoc") {
            tenure = loan.tenure.toString() + " Days";
            emiAmount = emiAmount + ` per ${loan.adhocDays} Days`;
        }

        const partnerConfig = await PartnerConfig.findOne({ partnerId: loan.partnerId });

        let LC_Tenure = "";
        let LC_ParentTenure = "";

        if (partnerConfig) {

            const LC_Days = partnerConfig.LC_InvoiceTenure ? partnerConfig.LC_InvoiceTenure.toString() : "";
            const LC_ParentDays = partnerConfig.LC_ParentTenure ? partnerConfig.LC_ParentTenure.toString() : "";
            const LC_InvoiceFrequency = partnerConfig.LC_InvoiceFrequency ? partnerConfig.LC_InvoiceFrequency.toString() : "";
            const LC_ParentFrequency = partnerConfig.LC_ParentFrequency ? partnerConfig.LC_ParentFrequency.toString() : "";

            LC_Tenure = `${LC_Days} ${LC_InvoiceFrequency}`;
            LC_ParentTenure = `${LC_ParentDays} ${LC_ParentFrequency}`;
        }


        return {
            data: {
                display: true,
                processingFeeCharge: charges.processingChargePer,
                emiAmount,
                disbursalAmount,
                processingCharges: charges.processingChargeAmt,
                otherCharges: charges.gstCharge + charges.stampingCharge,
                interestRate: loan.interestRate,
                bankWallet: partnerConfig && partnerConfig.collectionType && partnerConfig.collectionType === "eNach" ? "Bank Account" : "Wallet",
                frequency: loan.repaymentFrequency,
                lateCharges: lateCharges,
                totalInterest: Math.round((totalInterest + Number.EPSILON) * 100) / 100,
                lateDays: "DL_30" ? "4" : "2",
                partnerName: loan.partner ? loan.partner : "",
                loanAmount: loan.amount ? loan.amount : 0,
                tenure: tenure,
                brandName: borrowerPartner.brandName ? borrowerPartner.brandName : "",
                productType: charges.productType ? charges.productType : "",
                LC_Tenure: LC_Tenure,
                LC_ParentTenure: LC_ParentTenure
            },
            success: true,
            message: "Information fetched successfully",
            status: 200
        }
    }
    catch (ex) {
        saveErrorLog("getLoanCalculations", apiPath, loanId, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        return {
            data: {
                display: false
            },
            success: false,
            message: ex.message,
            status: 200
        }
    }

};

module.exports.migrateNotifications = async function () {
    try {
        const notifications = await Notifications.find();
        for (const item of notifications) {
            const messages = item.message;

            if (messages && messages.length > 0) {
                for (const message of messages) {
                    const newNotificationListItem = new NotificationsList({
                        partnerId: item.partnerId ? item.partnerId : null,
                        merchantCode: item.merchantCode ? item.merchantCode : "",
                        userId: null,
                        productId: item.productId ? item.productId : "",
                        trxnId: item.trxnId ? item.trxnId : "",
                        product: "loan",
                        loanId: null,
                        channel: message.channel ? message.channel : "",
                        mobileNo: message.mobileNo ? message.mobileNo : "",
                        templateType: message.type ? message.type : "",
                        status: message.status ? message.status : "",
                        responseCode: message.responseCode ? message.responseCode : "",
                        sentOn: message.sentOn ? message.sentOn : null,
                        deliveredOn: message.deliveredOn ? message.deliveredOn : null,
                        readOn: null,
                        reason: null,
                        statusCode: null,
                        insertedOn: message.sentOn ? message.sentOn : item.insertedOn,
                        modifiedOn: item.modifiedOn ? item.modifiedOn : null,
                        fallbackMessageId: null
                    });

                    await newNotificationListItem.save();

                }
            }

        }
        return { data: "", success: true, message: "Executed successfully" };
    } catch (ex) {
        saveErrorLog("getLoanCalculations", apiPath, loanId, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        return { data: "", success: false, message: ex.message };
    }


}

module.exports.insertConfigParameters = async function (model) {

    try {
        let startDate = null;
        let endDate = null;

        if (model.startDate) {
            let startParts = model.startDate.split("/");
            startDate = new Date(startParts[2], startParts[1] - 1, startParts[0]);
        }

        if (model.endDate) {
            let endParts = model.endDate.split("/");
            endDate = new Date(endParts[2], endParts[1] - 1, endParts[0]);
        }

        const configParams = new ConfigParameters({
            channel: model.channel,
            templateType: model.templateType,
            startDate: startDate,
            endDate: endDate,
            isActive: true,
            insertedOn: Date.now(),
            updatedOn: null
        });

        await configParams.save();

        return { data: "", success: true, message: "saved successfully" };
    } catch (ex) {
        saveErrorLog("insertConfigParameters", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        return { data: "", success: false, message: ex.message };
    }


}

module.exports.sendWhatsAppFallbackSMS = async function (userId = null, loanId, template, notificationListId = null) {
    try {
        let user = null;

        if (userId)
            user = await UserInfo.findById(userId);

        const notificationList = await NotificationsList.findById(notificationListId);

        if (!notificationList)
            return { data: "Error", success: false, message: "NotificationsList record not found", statusCode: "", mid: "" };

        let mobileNo = notificationList.mobileNo ? notificationList.mobileNo : "";

        if (!mobileNo)
            return { data: "Error", success: false, message: "Mobile No not found", statusCode: "", mid: "" };

        let smsModel = getSmsModel();
        smsModel.To = mobileNo;
        smsModel.Type = "TSMS";
        smsModel.From = config.get("keys.from");

        let name = "";

        if (user)
            name = capitalizeFirstLetter(user.userDetails.firstName) + " " + capitalizeFirstLetter(user.userDetails.lastName);

        if (mobileNo.length === 10)
            mobileNo = `91${mobileNo}`;

        const ffSupportLink = config.get("karix.ffSupportLink");

        if (template === "contract_sign_eng") {

            const loan = await LoanInfo.findById(loanId);

            if (!loan)
                return { data: "Error", success: false, message: "Loan not found", statusCode: "", mid: "" };

            const partner = await PartnerMaster.findById(loan.partnerId);

            if (!partner)
                return { data: "Error", success: false, message: "Partner not found", statusCode: "", mid: "" };

            let contractLink = "";

            if (loan.primaryCollectionType === "eNach" || loan.secondaryCollectionType === "eNach")
                contractLink = loan.loanContractDetails && loan.loanContractDetails.eNachUrl ? loan.loanContractDetails.eNachUrl : "";
            else
                contractLink = loan.loanContractDetails && loan.loanContractDetails.contractUrl ? loan.loanContractDetails.contractUrl : "";

            if (!contractLink)
                return { data: "Error", success: false, message: "Contract link not found", statusCode: "", mid: "" };

            let preApproval = await PreApprovals.findOne({ partnerId: loan.partnerId, merchantCode: loan.merchantCode, productId: loan.productId });

            if (!preApproval)
                preApproval = await PreApprovals.findOne({ partnerId: loan.partnerId, merchantCode: loan.merchantCode, trxnId: loan.trxnId });

            let expiryDate = null;

            if (preApproval && preApproval.expiredOn)
                expiryDate = getDMYFormattedDate(preApproval.expiredOn, "/");
            else
                expiryDate = getDMYFormattedDate(new Date(loan.insertedOn.getFullYear(), loan.insertedOn.getMonth() + 1, 0), "/");

            smsModel.TemplateName = "Contract_Signing2";
            smsModel.VAR1 = `${contractLink}?c=m`;
            smsModel.VAR2 = expiryDate;

        }
        else if (template === "kyc_reject_eng") {

            const loan = await LoanInfo.findById(loanId);

            if (!loan)
                return { data: "Error", success: false, message: "Loan not found", statusCode: "", mid: "" };

            const userPartnerInfo = await UserPartnerInfo.findOne({ userId: userId, partnerId: loan.partnerId, merchantCode: loan.merchantCode });

            if (!userPartnerInfo)
                return { data: "Error", success: false, message: "UserPartnerInfo not found ", statusCode: "", mid: "" };

            const partner = await PartnerMaster.findById(loan.partnerId);

            const reviewDoc = userPartnerInfo.incorrectReviewDocument ? userPartnerInfo.incorrectReviewDocument : "";

            if (!reviewDoc)
                return { data: "Error", success: false, message: "Review reason not found", statusCode: "", mid: "" };

            let partnerLink = "";

            if (userPartnerInfo && userPartnerInfo.reviewLink)
                partnerLink = userPartnerInfo.reviewLink;
            else {
                const notificationTemplate = await NotificationTemplates.findOne({ partnerId: loan.partnerId, template: template, channel: "WhatsApp" });

                if (!notificationTemplate)
                    return { data: "Error", success: false, message: "Notification template not found", statusCode: "", mid: "" };

                partnerLink = notificationTemplate && notificationTemplate.url1 ? notificationTemplate.url1 : "";

                let preApproval = await PreApprovals.findOne({ partnerId: loan.partnerId, merchantCode: loan.merchantCode, productId: loan.productId });

                if (!preApproval)
                    preApproval = await PreApprovals.findOne({ partnerId: loan.partnerId, merchantCode: loan.merchantCode, trxnId: loan.trxnId });

                let longUrl = null;
                let txnId = "";

                if (partner.partnerCode.toLowerCase() === "pay")
                    txnId = preApproval.trxnId;
                else
                    txnId = preApproval.productId ? preApproval.productId : preApproval.trxnId;

                if (notificationTemplate.isInternalOfferLink)
                    longUrl = config.get("keys.webUrl") + `leadInfo/${preApproval.merchantCode}/${txnId}/${preApproval.approvedAmount}/${partner.shortauthKey ? partner.shortauthKey : partner.authKey}`;
                else
                    longUrl = partnerLink;

                const generateCustomUrlResponse = await generateCustomUrl(longUrl, config.get("shortUrl.reviewCode"), loan.userId, loan._id, loan.loanNo, loan.merchantCode, partner.displayName, null);

                if (generateCustomUrlResponse.success)
                    partnerLink = generateCustomUrlResponse.data;
                else
                    return { data: "Error", success: false, message: generateCustomUrlResponse.message, statusCode: "", mid: "" };

            }

            if (!partnerLink)
                return { data: "Error", success: false, message: "Partner Link not found", statusCode: "", mid: "" };

            const reason = await DefinationDetailsMaster.findOne({ definationType: "review reasons", defination: reviewDoc });

            if (!reason)
                return { data: "Error", success: false, message: "Review reason not found", statusCode: "", mid: "" };

            smsModel.TemplateName = "KYC_Review2";
            smsModel.VAR1 = reason.value ? reason.value : "";
            smsModel.VAR2 = `${partnerLink}?c=m`;

        }
        else if (template === "incomplete__application_eng_1") {

            const loan = await LoanInfo.findById(loanId);

            if (!loan)
                return { data: "Error", success: false, message: "Loan not found", statusCode: "", mid: "" };

            const response = await getLoanCalculations(loan._id);

            const notificationTemplate = await NotificationTemplates.findOne({ partnerId: loan.partnerId, template: template, channel: "WhatsApp" });

            if (!notificationTemplate)
                return { data: "Error", success: false, message: "Notification template not found", statusCode: "", mid: "" };

            if (response.success) {

                const partnerMaster = await PartnerMaster.findById(loan.partnerId);

                if (!partnerMaster)
                    return { data: "Error", success: false, message: "Partner not found", statusCode: "", mid: "" };

                let partnerLink = notificationTemplate && notificationTemplate.url1 ? notificationTemplate.url1 : "";

                if (!partnerLink)
                    return { data: "Error", success: false, message: "Partner Link not found", statusCode: "", mid: "" };

                let preApproval = await PreApprovals.findOne({ partnerId: loan.partnerId, merchantCode: loan.merchantCode, productId: loan.productId });

                if (!preApproval)
                    preApproval = await PreApprovals.findOne({ partnerId: loan.partnerId, merchantCode: loan.merchantCode, trxnId: loan.trxnId });

                let longUrl = null;
                let txnId = "";

                if (partnerMaster.partnerCode.toLowerCase() === "pay")
                    txnId = preApproval.trxnId;
                else
                    txnId = preApproval.productId ? preApproval.productId : preApproval.trxnId;

                if (notificationTemplate.isInternalOfferLink)
                    longUrl = config.get("keys.webUrl") + `leadInfo/${preApproval.merchantCode}/${txnId}/${preApproval.approvedAmount}/${partnerMaster.shortauthKey ? partnerMaster.shortauthKey : partnerMaster.authKey}`;
                else
                    longUrl = partnerLink;

                const generateCustomUrlResponse = await generateCustomUrl(longUrl, config.get("shortUrl.incompleteApplicationCode"), loan.userId, loan._id, loan.loanNo, loan.merchantCode, partnerMaster.displayName, null);

                if (generateCustomUrlResponse.success)
                    partnerLink = generateCustomUrlResponse.data;
                else
                    return { data: "Error", success: false, message: generateCustomUrlResponse.message, statusCode: "", mid: "" };

                smsModel.TemplateName = "Incomplete Application 1";
                smsModel.VAR1 = name;
                smsModel.VAR2 = response.data.brandName ? response.data.brandName.toString() : response.data.partnerName.toString();
                smsModel.VAR3 = `${partnerLink}?c=m`;
                smsModel.VAR4 = response.data.loanAmount ? response.data.loanAmount.toString() : "";
                smsModel.VAR5 = response.data.emiAmount ? response.data.emiAmount.toString() : "";
                smsModel.VAR6 = response.data.totalInterest ? response.data.totalInterest.toString() : "";
                smsModel.VAR7 = response.data.tenure ? response.data.tenure.toString() : "";
                smsModel.VAR8 = response.data.processingCharges ? response.data.processingCharges.toString() : "";
                smsModel.VAR9 = response.data.otherCharges ? response.data.otherCharges.toString() : "";
                smsModel.VAR10 = response.data.disbursalAmount ? response.data.disbursalAmount.toString() : "";
                smsModel.VAR11 = ffSupportLink;
            }
            else {
                return {
                    data: response.message, success: false, message: response.message, statusCode: "", mid: ""
                }
            }
        }
        else if (template === "renewal__notification_eng") {
            const loan = await LoanInfo.findById(loanId);

            if (!loan)
                return { data: "Error", success: false, message: "Loan not found", statusCode: "", mid: "" };

            const partner = await PartnerMaster.findById(notificationList.partnerId);

            if (!partner)
                return { data: "Error", success: false, message: "Partner not found", statusCode: "", mid: "" };

            const notificationTemplate = await NotificationTemplates.findOne({ partnerId: notificationList.partnerId, template: template, channel: "WhatsApp" });

            if (!notificationTemplate)
                return { data: "Error", success: false, message: "Notification template not found", statusCode: "", mid: "" };

            const preApproval = await PreApprovals.findById(notificationList.preApprovalId);

            if (!preApproval)
                return { data: "Error", success: false, message: "PreApproval record not found", statusCode: "", mid: "" };

            // const loanInstallmentsList = await installmentService.getInstallments(loanId);
            // if (!loanInstallmentsList.success)
            //     return { data: "Error", success: false, message: "No Installments found", statusCode: "", mid: "" };

            // const lastInstallment = loanInstallmentsList.data.filter(x => x.installmentNumber == loanInstallmentsList.data.length);
            // const loanExpiryDate = getDMYFormattedDate(lastInstallment[0].dueDate, "-");

            const installmentDetails = await installmentService.getInstallmentDetails(loanId);
            if (!installmentDetails.success)
                return { data: "Error", success: false, message: installmentDetails.message, statusCode: "", mid: "" };
            const loanExpiryDate = getDMYFormattedDate(installmentDetails.lastInstallmentDate, "-");

            let partnerLink = notificationTemplate && notificationTemplate.url1 ? notificationTemplate.url1 : "";

            const partnerName = notificationTemplate && notificationTemplate.partnerName ? notificationTemplate.partnerName : "";

            let longUrl = null;
            let txnId = "";

            if (partner.partnerCode.toLowerCase() === "pay")
                txnId = preApproval.trxnId;
            else
                txnId = preApproval.productId ? preApproval.productId : preApproval.trxnId;

            if (notificationTemplate.isInternalOfferLink)
                longUrl = config.get("keys.webUrl") + `leadInfo/${preApproval.merchantCode}/${txnId}/${preApproval.approvedAmount}/${partnerMaster.shortauthKey ? partnerMaster.shortauthKey : partnerMaster.authKey}`;
            else
                longUrl = partnerLink;

            const generateCustomUrlResponse = await generateCustomUrl(longUrl, config.get("shortUrl.renewalsCode"), loan.userId, loan._id, loan.loanNo, loan.merchantCode, partner.displayName, null);

            if (generateCustomUrlResponse.success)
                partnerLink = generateCustomUrlResponse.data;
            else
                return { data: "Error", success: false, message: generateCustomUrlResponse.message, statusCode: "", mid: "" };

            const loanAmount = preApproval.approvedAmount ? preApproval.approvedAmount.toString() : "";

            const emiAmount = preApproval.emiAmt ? preApproval.emiAmt.toString() : "";

            const tenureInDays = preApproval.tenureInDays ? `${preApproval.tenureInDays.toString()} days` : "";

            const charges = await loanService.getLoanCharges(loanAmount, null, preApproval._id);

            if (!charges.success)
                return { data: "Error", success: false, message: charges.message, statusCode: "", mid: "" };

            const disbursalAmount = charges.disbursalAmount;
            const chargeInPer = charges.processingChargePer;
            const processingCharges = charges.processingChargeAmt;

            const totalInterestAmount = preApproval.totalInterest ? (Math.round((preApproval.totalInterest + Number.EPSILON) * 100) / 100).toString() : "";

            if (!name || !loanExpiryDate || !loanAmount || !partnerLink || !partnerName || !emiAmount || !tenureInDays ||
                !processingCharges || !disbursalAmount || !ffSupportLink)
                return { data: "Error", success: false, message: "Insufficient message parameters", statusCode: "", mid: "" };

            smsModel.TemplateName = "Loan Renewal 1";
            smsModel.VAR1 = name;
            smsModel.VAR2 = "expiring";
            smsModel.VAR3 = loanExpiryDate;
            smsModel.VAR4 = `You have a renewal offer of loan for Rs. ${loanAmount}`;
            smsModel.VAR5 = partnerName;
            smsModel.VAR6 = `${partnerLink}?c=m`;
            smsModel.VAR7 = loanAmount;
            smsModel.VAR8 = emiAmount;
            smsModel.VAR9 = totalInterestAmount;
            smsModel.VAR10 = tenureInDays;
            smsModel.VAR11 = processingCharges;
            smsModel.VAR12 = disbursalAmount;
        }
        else if (template === "contract_lc_sign_eng") {

            const loan = await LoanInfo.findById(loanId);

            if (!loan)
                return { data: "Error", success: false, message: "Loan not found", statusCode: "", mid: "" };

            let contractLink = "";

            if (loan.primaryCollectionType === "eNach" || loan.secondaryCollectionType === "eNach")
                contractLink = loan.loanContractDetails && loan.loanContractDetails.eNachUrl ? loan.loanContractDetails.eNachUrl : "";
            else
                contractLink = loan.loanContractDetails && loan.loanContractDetails.contractUrl ? loan.loanContractDetails.contractUrl : "";

            if (!contractLink)
                return { data: "Error", success: false, message: "Contract link not found", statusCode: "", mid: "" };

            let preApproval = await PreApprovals.findOne({ partnerId: loan.partnerId, merchantCode: loan.merchantCode, productId: loan.productId });

            if (!preApproval)
                preApproval = await PreApprovals.findOne({ partnerId: loan.partnerId, merchantCode: loan.merchantCode, trxnId: loan.trxnId });

            let expiryDate = null;

            if (preApproval && preApproval.expiredOn)
                expiryDate = getDMYFormattedDate(preApproval.expiredOn, "/");
            else
                expiryDate = getDMYFormattedDate(new Date(loan.insertedOn.getFullYear(), loan.insertedOn.getMonth() + 1, 0), "/");

            // smsModel.TemplateName = "Contract_LC_Sign_eng";
            // smsModel.VAR1 = name;
            // smsModel.VAR2 = amount;
            smsModel.TemplateName = "Loan_Contract_Sign";
            smsModel.VAR1 = `${contractLink}?c=m`;
            smsModel.VAR2 = expiryDate;
        }
        else if (template === "consent_auth_merchant_eng") {

            const loan = await LoanInfo.findById(loanId);

            if (!loan)
                return { data: "Error", success: false, message: "Loan not found", statusCode: "", mid: "" };

            const amount = loan.amount ? loan.amount.toString() : "";

            if (!amount)
                return { data: "Error", success: false, message: "Loan amount not found", statusCode: "", mid: "" };

            const creditLineBalance = await workflowService.getCreditLineBalance(loan.productId);

            if (!creditLineBalance.success)
                return { data: "Error", success: false, message: "Loan amount not found", statusCode: "", mid: "" };

            // smsModel.TemplateName = "Consent_Auth_Merchant_eng";
            // smsModel.VAR1 = name;
            // smsModel.VAR2 = amount;
            // smsModel.VAR3 = creditLineBalance.availableCreditLimit ? creditLineBalance.availableCreditLimit.toString() : "";

            smsModel.TemplateName = "Merchant_Consent_Auth_1";
            smsModel.VAR1 = amount;
            smsModel.VAR2 = creditLineBalance.availableCreditLimit ? creditLineBalance.availableCreditLimit.toString() : "";
        }
        else if (template === "consent_confirm_delivery_agent_eng") {

            const loan = await LoanInfo.findById(loanId);

            if (!loan)
                return { data: "Error", success: false, message: "Loan not found", statusCode: "", mid: "" };

            const amount = loan.amount ? loan.amount.toString() : "";

            if (!amount)
                return { data: "Error", success: false, message: "Loan amount not found", statusCode: "", mid: "" };

            if (!loan.invoiceRequestId)
                return { data: "Error", success: false, message: "Invoice Id not found", statusCode: "", mid: "" };

            const invoice = await InvoiceRequests.findById(loan.invoiceRequestId);

            if (!invoice)
                return { data: "Error", success: false, message: "Invoice record not found", statusCode: "", mid: "" };

            if (!invoice.deliveryAgent || !invoice.deliveryAgentMobile)
                return { data: "Error", success: false, message: "Agent Name/Mobile not found", statusCode: "", mid: "" };

            smsModel.TemplateName = "Consent_Confirm_Delivery_Agent_eng";
            smsModel.VAR1 = invoice.deliveryAgent ? capitalizeFirstLetter(invoice.deliveryAgent) : "Agent";
            smsModel.VAR2 = amount;
            smsModel.VAR3 = name;
            smsModel.VAR4 = loan.partner;
        }
        else if (template === "autopay_setup_merchant_eng") {

            const loan = await LoanInfo.findById(loanId);

            if (!loan)
                return { data: "Error", success: false, message: "Loan not found", statusCode: "", mid: "" };

            const amount = loan.amount ? loan.amount.toString() : "";

            if (!amount)
                return { data: "Error", success: false, message: "Loan amount not found", statusCode: "", mid: "" };

            const firstInstallment = await installmentService.getSingleInstallmentDetails(loanId, 1);
            if (!firstInstallment.success)
                return { data: "Error", success: false, message: firstInstallment.message, statusCode: "", mid: "" };

            const installmentDetails = firstInstallment.success ? firstInstallment : null;

            if (!installmentDetails)
                return { data: "Error", success: false, message: "EMI details not found", statusCode: "", mid: "" };

            const paymentDate = installmentDetails.dueDate ? getDMYFormattedDate(installmentDetails.dueDate, "/") : "";

            if (!paymentDate)
                return { data: "Error", success: false, message: "Repayment Date not found", statusCode: "", mid: "" };

            // smsModel.TemplateName = "AutoPay_Setup_Merchant_eng";
            // smsModel.VAR1 = name;
            // smsModel.VAR2 = amount;
            // smsModel.VAR3 = paymentDate;

            smsModel.TemplateName = "AutoPay_Setup_Merchant";
            smsModel.VAR1 = amount;
            smsModel.VAR2 = paymentDate;

        } else if (template === "bank_details_update_eng") {
            const loan = await LoanInfo.findById(loanId);

            if (!loan)
                return { data: "Error", success: false, message: "Loan not found", statusCode: "", mid: "" };

            const userPartnerInfo = await UserPartnerInfo.findOne({ userId: userId, partnerId: loan.partnerId, merchantCode: loan.merchantCode });

            if (!userPartnerInfo)
                return { data: "Error", success: false, message: "UserPartnerInfo not found ", statusCode: "", mid: "" };

            if (!userPartnerInfo.updateBankLink)
                return { data: "Error", success: false, message: "Bank link not found", statusCode: "", mid: "" };

            smsModel.TemplateName = "Bank_Details_Update_eng";
            smsModel.VAR1 = name;
            smsModel.VAR2 = `${userPartnerInfo.updateBankLink}?c=m`;
        }
        else if (template === "autopay_consent_request_eng") {
            const loan = await LoanInfo.findById(loanId);

            if (!loan)
                return { data: "Error", success: false, message: "Loan not found", statusCode: "", mid: "" };

            let contractLink = "";

            if (loan.primaryCollectionType === "eNach" || loan.secondaryCollectionType === "eNach")
                contractLink = loan.loanContractDetails && loan.loanContractDetails.eNachUrl ? loan.loanContractDetails.eNachUrl : "";
            else
                contractLink = loan.loanContractDetails && loan.loanContractDetails.contractUrl ? loan.loanContractDetails.contractUrl : "";

            if (!contractLink)
                return { data: "Error", success: false, message: "Contract link not found", statusCode: "", mid: "" };

            smsModel.TemplateName = "AutoPay_Consent_Request_eng";
            smsModel.VAR1 = name;
            smsModel.VAR2 = `${contractLink}?c=m`;
        }
        else if (template === "autopay_update_request_eng") {
            const loan = await LoanInfo.findById(loanId);

            if (!loan)
                return { data: "Error", success: false, message: "Loan not found", statusCode: "", mid: "" };

            let contractLink = "";

            if (loan.primaryCollectionType === "eNach" || loan.secondaryCollectionType === "eNach")
                contractLink = loan.loanContractDetails && loan.loanContractDetails.eNachUrl ? loan.loanContractDetails.eNachUrl : "";
            else
                contractLink = loan.loanContractDetails && loan.loanContractDetails.contractUrl ? loan.loanContractDetails.contractUrl : "";

            if (!contractLink)
                return { data: "Error", success: false, message: "Contract link not found", statusCode: "", mid: "" };

            smsModel.TemplateName = "AutoPay_Update_Request_eng";
            smsModel.VAR1 = name;
            smsModel.VAR2 = `${contractLink}?c=m`;
        } else if (template === "cashback_notification_eng") {
            const loanInfo = await LoanInfo.findById(loanId);
            const userInfo = await UserInfo.findById(userId).select({ userDetails: 1 });
            const name = getName(userInfo.userDetails.firstName, userInfo.userDetails.middleName, userInfo.userDetails.lastName);
            const amount = loanInfo.discount.discountAmount;

            smsModel.TemplateName = "Cashback_Notification_eng";
            smsModel.VAR1 = name;
            smsModel.VAR2 = amount;
        }
        else if (template === "loan_disbursal_eng") {
            const loan = await LoanInfo.findById(loanId);

            if (!loan)
                return { data: "Error", success: false, message: "Loan not found", statusCode: "", mid: "" };

            const partner = await PartnerMaster.findById(loan.partnerId);

            if (!partner)
                return { data: "Error", success: false, message: "Partner not found", statusCode: "", mid: "" };

            const amount = loan.amount ? loan.amount.toString() : "";

            if (!amount)
                return { data: "Error", success: false, message: "Loan amount not found", statusCode: "", mid: "" };

            smsModel.TemplateName = "Loan_Disbursal_eng";
            smsModel.VAR1 = name;
            smsModel.VAR2 = amount;
        }
        else if (template === "nach_failure") {

            // smsModel.TemplateName = "Nach Failure";
            // smsModel.VAR1 = notificationList.smsModel.VAR1;
            // smsModel.VAR2 = notificationList.smsModel.VAR2;
            // smsModel.VAR3 = notificationList.smsModel.VAR3;
            // smsModel.VAR4 = notificationList.smsModel.VAR4;
            // smsModel.VAR5 = notificationList.smsModel.CTA;

            smsModel.TemplateName = "Nach_Failure";
            smsModel.VAR1 = notificationList.smsModel.VAR4;
            smsModel.VAR2 = notificationList.smsModel.CTA;
        }
        else if (template === "loan_review") {
            // smsModel.TemplateName = "Loan Review 1";
            // smsModel.VAR1 = notificationList.smsModel.VAR1;
            // smsModel.VAR2 = notificationList.smsModel.VAR2;
            // smsModel.VAR3 = `${notificationList.smsModel.VAR3}?c=m`;

            smsModel.TemplateName = "Loan_Review1";
            smsModel.VAR1 = notificationList.smsModel.VAR2;
            smsModel.VAR2 = `${notificationList.smsModel.VAR3}?c=m`;
        }
        else if (template == "enach_cancellation_request" || template == "enach_cancellation_confirmation") {
            smsModel.TemplateName = template === "enach_cancellation_request" ? "eNach_Cancellation_Request" : "eNach_Cancellation_Confirmation";
            smsModel.VAR1 = notificationList.smsModel.VAR2;
            smsModel.VAR2 = notificationList.smsModel.VAR3;
        }
        else if (template === "pre_legal_collection") {
            const loan = await LoanInfo.findById(loanId);

            if (!loan)
                return { data: "Error", success: false, message: "Loan not found", statusCode: "", mid: "" };

            const signUrl = getSignedUrlToDownload({ url: notificationList.smsModel.AttachmentPath1 });

            const type = config.get("shortUrl.documentLink");
            const generateCustomUrlResponse = await generateCustomUrl(signUrl, type, loan.userId, loan.loanId, loan.loanNo, loan.merchantCode, loan.partner, null);

            if (generateCustomUrlResponse.success) {
                smsModel.TemplateName = "pre_legal_30_plus";
                smsModel.VAR1 = notificationList.smsModel.VAR4;
                smsModel.VAR2 = `${generateCustomUrlResponse.data}?c=m`;
            }
        }
        else if (template === "noc_repayment") {
            const loan = await LoanInfo.findById(loanId);

            if (!loan)
                return { data: "Error", success: false, message: "Loan not found", statusCode: "", mid: "" };

            const signUrl = getSignedUrlToDownload({ url: notificationList.smsModel.AttachmentPath1 });

            const type = config.get("shortUrl.documentLink");
            const generateCustomUrlResponse = await generateCustomUrl(signUrl, type, loan.userId, loan.loanId, loan.loanNo, loan.merchantCode, loan.partner, null);

            if (generateCustomUrlResponse.success) {
                smsModel.TemplateName = "noc_repayment";
                smsModel.VAR1 = notificationList.smsModel.VAR3;
                smsModel.VAR2 = `${generateCustomUrlResponse.data}?c=m`;
            }
        }
        else if (template === "pay_online") {
            const loan = await LoanInfo.findById(loanId);

            const url = notificationList.smsModel.AttachmentPath1;

            if (!loan)
                return { data: "Error", success: false, message: "Loan not found", statusCode: "", mid: "" };

            const signUrl = getSignedUrlToDownload({ url: url });

            const type = config.get("shortUrl.documentLink");
            const generateCustomUrlResponse = await generateCustomUrl(signUrl, type, loan.userId, loan.loanId, loan.loanNo, loan.merchantCode, loan.partner, null);

            if (generateCustomUrlResponse.success) {
                smsModel.TemplateName = "online_pay";
                smsModel.VAR1 = notificationList.smsModel.VAR4;
                smsModel.VAR2 = `${generateCustomUrlResponse.data}?c=m`;
            }
        }

        const response = await sendCustomSms(smsModel);

        return response;
    }
    catch (ex) {
        saveErrorLog("sendWhatsAppFallbackSMS", apiPath, { userId, loanId, template }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        return {
            data: "Error",
            success: false,
            message: ex.message,
            statusCode: "",
            mid: ""
        }
    }

}

// async function computeLoanCashback(loan) {

//     let discountText = "";
//     let bankText = "";

//     let discountAmount = 0;

//     try {
//         const partnerConfig = await PartnerConfig.findOne({ partnerId: loan.partnerId });

//         const today = Date.now();

//         let preApproval = await PreApprovals.findOne({ partnerId: loan.partnerId, merchantCode: loan.merchantCode, productId: loan.productId });

//         if (!preApproval)
//             preApproval = await PreApprovals.findOne({ partnerId: loan.partnerId, merchantCode: loan.merchantCode, trxnId: loan.trxnId });

//         if (partnerConfig && preApproval) {

//             const campaign = partnerConfig.campaign.filter((x) => x.type == "cashback" && x.isActive == true);

//             if (campaign.length > 0) {
//                 const startDate = new Date(campaign[0].from.getFullYear(), campaign[0].from.getMonth(), campaign[0].from.getDate(), 0, 0, 0);
//                 const endDate = new Date(campaign[0].to.getFullYear(), campaign[0].to.getMonth(), campaign[0].to.getDate(), 23, 59, 59);
//                 const loanType = campaign[0].loanType ? campaign[0].loanType : "";

//                 if (today >= startDate && today <= endDate) {

//                     if (loanType.toLowerCase() === "all" || loanType.toLowerCase() === preApproval.loanType.toLowerCase()) {

//                         if (campaign[0].discountAmount)
//                             discountAmount = campaign[0].discountAmount;
//                         else if (campaign[0].discountPercentage)
//                             discountAmount = Math.round((((loan.amount / 100) * campaign[0].discountPercentage) + Number.EPSILON) * 100) / 100;

//                     }

//                 }
//             }
//         }
//     }
//     catch {
//         saveErrorLog("computeLoanCashback", apiPath, loan._id, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex.toString());
//     }

//     return { discountText, bankText, discountAmount };
// }
