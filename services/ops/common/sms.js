const mongoose = require("mongoose");
const config = require("config");
const { SmsWebhookLog } = require("../../../models/ops/sms/sms");
const { saveErrorLog } = require("./masters");
const { NotificationsList } = require("../../../models/ops/partner/partner");
const { updateMobileNumberChannelDetails } = require("./common");
const apiPath = __dirname;

module.exports.smsWebhook = async function (payload, type) {
    try {
        await logResponse(payload, type);

        const sessionId = payload.SessionId ? payload.SessionId : "";

        if (sessionId) {

            const notification = await NotificationsList.findOne({ responseCode: sessionId, channel: "SMS" });

            if (notification) {

                if (type === "TSMS") {
                    const statusAt = payload.StatusAt ? new Date(payload.StatusAt) : null;

                    //if (!notification.modifiedOn || (notification.modifiedOn && statusAt > notification.modifiedOn)) {
                    notification.status = payload.SmsStatus ? payload.SmsStatus.toLowerCase() : "";
                    notification.modifiedOn = statusAt;
                    notification.deliveredOn = payload.SmsStatus && payload.SmsStatus.toLowerCase() === "delivered" ? new Date(payload.SentAt) : null;
                    notification.reason = payload.ErrorDescription ? payload.ErrorDescription : "";

                    await notification.save();

                    if (payload.SmsStatus.toLowerCase() === "delivered") {
                        let updateUserPayload = {
                            userId: notification.userId,
                            mobileNumber: notification.mobileNo,
                            lastMessageSentOn: new Date(payload.SentAt)
                        }
                        updateMobileNumberChannelDetails("SMS", updateUserPayload)
                    }
                    //}
                }
                else if (type === "OTP") {
                    notification.status = payload.Status ? payload.Status.toLowerCase() : "";
                    notification.modifiedOn = Date.now();
                    notification.deliveredOn = payload.Status && payload.Status.toLowerCase() === "delivered" ? Date.now() : null;
                    notification.reason = payload.Error ? payload.Error : "";

                    await notification.save();
                }
            }
        }

        return {
            data: "",
            success: true,
            message: "Saved successfully"
        }

    }
    catch (ex) {
        saveErrorLog("smsWebhook", apiPath, { payload, type }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        return {
            data: "",
            success: false,
            message: ex.message
        }
    }
}

async function logResponse(payload, type) {
    try {

        const model = new SmsWebhookLog({
            sessionId: payload.SessionId ? payload.SessionId : "",
            mobileNo: payload.SmsTo ? payload.SmsTo : payload.To,
            response: JSON.stringify(payload),
            type: type,
            insertedOn: Date.now()
        });

        await model.save();

        return 1;

    }
    catch (ex) {
        saveErrorLog("logResponse", apiPath, { payload, type }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        return 1;
    }
}