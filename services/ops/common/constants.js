module.exports.Constants = {

    PAYMENT_CHANNEL: {
        <PERSON>NACH: "<PERSON><PERSON><PERSON>",
        WALLET: "WALLET",
        CF_UPI: "CF_UPI", // Cashfree UPI
        FF_UPI: "FF_UPI", //Fundfina UPI
        VA : "VA"
    },
    PAYMENT_COLLECTION_TYPE: {
        SINGLE: "Single",
        BU<PERSON><PERSON>: "Bulk"
    },
    PAYMENT_SENDING_STATE: {
        PAID: "Paid",
        IN_ACTIVE: "InActive"
    },
    MIS_LOG_STAGING_STATUS: {
        PENDING: "PENDING",
        FAILED: "FAILED",
        COMPLETED: "COMPLETED",
        PROCESSING: "PROCESSING"
    },
    MIS_LOG_CHANNEL: {
        API: "API"
    },
    OTP_CHANNELS: {
        SMS: "SMS",
        WHATSAPP: "WHATSAPP",
        EMAIL: "EMAIL"
    },
    SEND_OTP_RESET_TIME: 15, /** MINUTES */
    MAX_SEND_OTP: 5,
    O<PERSON>_VALIDITY_TIME: 15, /** MINUTES */
    MAX_OTP_VALIDATION: 5,

    NOTIFICATION_TYPES: {
        MERCHANT_REGISTRATION: "MERCHANT_REGISTRATION",
        PARTNER_REGISTRATION: "PARTNER_REGISTRATION",
        FORGOT_PASSWORD: "FORGOT_PASSWORD",
        KYC_PAGE: "KYC_PAGE",
        MERCHANT_OTP: "MERCHANT_OTP",
        LOAN_CONTRACT: "LOAN_CONTRACT",
        MY_PROFILE: "MY_PROFILE"
    },
    OTP_CONFIG: {
        MERCHANT_REGISTRATION: {
            SEND_OTP_RESET_TIME: 15, /** MINUTES */
            MAX_SEND_OTP: 15,
            OTP_VALIDITY_TIME: 15, /** MINUTES */
            MAX_OTP_VALIDATION: 15,
        },
        PARTNER_REGISTRATION: {
            SEND_OTP_RESET_TIME: 15, /** MINUTES */
            MAX_SEND_OTP: 5,
            OTP_VALIDITY_TIME: 15, /** MINUTES */
            MAX_OTP_VALIDATION: 5,
        },
        FORGOT_PASSWORD: {
            SEND_OTP_RESET_TIME: 15, /** MINUTES */
            MAX_SEND_OTP: 5,
            OTP_VALIDITY_TIME: 15, /** MINUTES */
            MAX_OTP_VALIDATION: 5,
        },
        KYC_PAGE: {
            SEND_OTP_RESET_TIME: 5, /** MINUTES */
            MAX_SEND_OTP: 10,
            OTP_VALIDITY_TIME: 5, /** MINUTES */
            MAX_OTP_VALIDATION: 10,
        },
        MERCHANT_OTP: {
            SEND_OTP_RESET_TIME: 5, /** MINUTES */
            MAX_SEND_OTP: 10,
            OTP_VALIDITY_TIME: 5, /** MINUTES */
            MAX_OTP_VALIDATION: 10,
        },
        LOAN_CONTRACT: {
            SEND_OTP_RESET_TIME: 15, /** MINUTES */
            MAX_SEND_OTP: 5,
            OTP_VALIDITY_TIME: 15, /** MINUTES */
            MAX_OTP_VALIDATION: 5,
        },
        MY_PROFILE: {
            SEND_OTP_RESET_TIME: 15, /** MINUTES */
            MAX_SEND_OTP: 5,
            OTP_VALIDITY_TIME: 15, /** MINUTES - Reset time */
            MAX_OTP_VALIDATION: 5,
        },
        COLLECTION_OTP: {
            SEND_OTP_RESET_TIME: 15, /** MINUTES */
            MAX_SEND_OTP: 5,
            OTP_VALIDITY_TIME: 15, /** MINUTES - Reset time */
            MAX_OTP_VALIDATION: 5,
        },
        DEFAULT: {
            SEND_OTP_RESET_TIME: 15, /** MINUTES */
            MAX_SEND_OTP: 5,
            OTP_VALIDITY_TIME: 15, /** MINUTES */
            MAX_OTP_VALIDATION: 5,
        }
    },

    FILE_UPLOAD_CONFIG: {
        MERCHANT_KYC: {
            PAN: {
                ALLOWED_FILE_TYPES: ['.png', '.jpg', '.jpeg', '.pdf'],
                MAX_FILE_SIZE: 50 /** MB */
            },
            ADDRESS_PROOF_FRONT_IMAGE: {
                ALLOWED_FILE_TYPES: ['.png', '.jpg', '.jpeg', '.pdf'],
                MAX_FILE_SIZE: 50 /** MB */
            },
            ADDRESS_PROOF_BACK_IMAGE: {
                ALLOWED_FILE_TYPES: ['.png', '.jpg', '.jpeg', '.pdf'],
                MAX_FILE_SIZE: 50 /** MB */
            }
        },
        DISBURSAL: {
            UPLOAD_EXCEL: {
                ALLOWED_FILE_TYPES: ['.xls', '.xlsx'],
                MAX_FILE_SIZE: 50 /** MB */
            }
        },
        PARTNER_KYC: {
            PAN: {
                ALLOWED_FILE_TYPES: ['.png', '.jpg', '.jpeg', '.pdf'],
                MAX_FILE_SIZE: 50 /** MB */
            }
        },
        BANK_DETAILS_UPLOAD: {
            ENACH_IMAGE: {
                ALLOWED_FILE_TYPES: ['.png', '.jpg', '.jpeg', '.pdf'],
                MAX_FILE_SIZE: 50 /** MB */
            }
        }
    },
    FILE_UPLOAD_MODULES: {
        MERCHANT_KYC: "MERCHANT_KYC",
        DISBURSAL: "DISBURSAL",
        PARTNER_KYC: "PARTNER_KYC",
        BANK_DETAILS_UPLOAD: "BANK_DETAILS_UPLOAD"
    },
    FILE_UPLOAD_TYPES: {
        PAN: "PAN",
        ADDRESS_PROOF_FRONT_IMAGE: "ADDRESS_PROOF_FRONT_IMAGE",
        ADDRESS_PROOF_BACK_IMAGE: "ADDRESS_PROOF_BACK_IMAGE",
        UPLOAD_EXCEL: "UPLOAD_EXCEL",
        ENACH_IMAGE: "ENACH_IMAGE"
    },

    API_RATE_LIMIT_TYPES: {
        LOGIN: "LOGIN"
    },
    API_RATE_LIMIT: {
        LOGIN: {
            MAX: 10,
            RESET_TIME: 5 //MIN
        }
    },

    MAX_FILE_UPLOAD_LIMIT_IN_GET_SINGED_URL: ********, // Bytes (50MB)

    ROLES: {
        ADMIN: "Admin",
        BORROWING_PARTNER: "Borrowing Partner",
        LENDING_PARTNER: "Lending Partner",
        USER: "User",
        COLLECTION_AGENT: "Collection Agent"
    },

    MONTH_NAME_BY_INDEX: {
        1: 'jan', 2: 'feb', 3: 'mar', 4: 'apr', 5: 'may', 6: 'jun', 7: 'jul', 8: 'aug', 9: 'sep', 10: 'oct', 11: 'nov', 12: 'dec'
    },

    PRODUCT_MASTER_HISTORY_ACTIONS: {
        EDIT: "EDIT",
        DELETE: "DELETE"
    },

    NOC_EMAIL_EXTRACT: {
        ALLOWED_FILE_TYPES_TO_DOWNLOAD: ['pdf'],
        UPLOAD_STATUS: {
            PENDING: "PENDING",
            COMPLETED: "COMPLETED",
            FAILED: "FAILED"
        }
    }

}
