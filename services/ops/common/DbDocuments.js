const mongoose = require("mongoose");
const debug = require("debug")("app:DbDocuments");
const { saveErrorLog } = require("../../../services/ops/common/masters");
const IsNullEmpty = require("isnullemptyorwhitespace");
const apiPath = __dirname;
const { PartnerMaster, PartnerConfig } = require("../../../models/ops/partner/partner");
const { UserPartnerInfo } = require("../../../models/ops/registration/users");
const Logger = require("../../../middleware/logger");
const logger = new Logger();

module.exports.getLendingUserPartner = async function (lendingUserId) {
    let output = {
        data: null,
        success: false,
        message: "",
        status: 501,
    };
    try {
        const userPartnerInfo = await UserPartnerInfo.findOne({
            userId: mongoose.Types.ObjectId(lendingUserId),
            isPartnerUser: true
        });

        if (!IsNullEmpty(userPartnerInfo)) {
            output = {
                data: userPartnerInfo,
                success: true,
                message: "fetched successfully",
                status: 200,
            };
        } else {
            output.message = "Lending Partner User Not Found";
        }
    } catch (ex) {
        logger.logs("Error: ", ex);
        const model = { lendingUserId: lendingUserId };
        saveErrorLog("getlenderPartnerCode", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
}

module.exports.getPartner = async function (partnerCode, partnerId = null, partnerName = null) {
    let output = {
        data: null,
        success: false,
        message: "",
        status: 501,
    };
    try {
        let query = {};
        if (partnerCode) {
            query.partnerCode = partnerCode;
        }

        if (partnerId) {
            query._id = mongoose.Types.ObjectId(partnerId);
        }

        if (partnerName) {
            query.partnerName = { $regex: new RegExp(partnerName, "i") };
        }

        const partner = await PartnerMaster.findOne(query);
        if (partner) {
            output = {
                data: partner,
                success: true,
                message: "",
                status: 200,
            };
        } else {
            output.message = "No Partner Found";
        }
    }
    catch (ex) {
        logger.logs("Error :", ex);
        const model = {
            partnerCode: partnerCode,
            partnerId: partnerId,
            partnerName: partnerName
        };
        saveErrorLog("getPartnerByPartnerCode", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
}

module.exports.getPartnerConfig = async function (partnerCode, partnerId = null, partnerName = null) {
    let output = {
        data: null,
        success: false,
        message: "",
        status: 501,
    };
    try {
        let query = {};
        if (partnerCode) {
            query.partnerCode = partnerCode;
        }

        if (partnerId) {
            query._id = mongoose.Types.ObjectId(partnerId);
        }

        if (partnerName) {
            query.partnerName = { $regex: new RegExp(partnerName, "i") };
        }

        const partner = await PartnerMaster.findOne(query);
        if (partner) {
            const partneRecord = {
                _id: partner._id,
                partnerName: partner.partnerName,
                isloanDisabled: partner.isloanDisabled,
                isReviewDisabled: partner.isReviewDisabled,
                callBackUrl: partner.callBackUrl,
                displayLoanTabs: partner.displayLoanTabs,
                showHeaderMenu: partner.showHeaderMenu,
                showFooterMenu: partner.showFooterMenu,
                displayLogo: partner.displayLogo,
                logoFileName: partner.logoFileName,
                bankValidationAmount: null,
                eNachAmount: null,
                disbursalType: null,
                collectionType: null,
                shortauthKey: partner.shortauthKey,
                contractSuccessCallBackUrl: partner.contractSuccessCallBackUrl,
                eNachErrorCallBackUrl: partner.eNachErrorCallBackUrl,
                hideFFLogo: partner.hideFFLogo,
                questionnairre: null
            }
            const partnerConfig = await PartnerConfig.findOne({ partnerId: mongoose.Types.ObjectId(partner._id) });
            if (partnerConfig) {
                partneRecord.bankValidationAmount = partnerConfig.bankValidationAmount;
                partneRecord.eNachAmount = partnerConfig.eNachAmount;
                partneRecord.disbursalType = partnerConfig.disbursalType;
                partneRecord.collectionType = partnerConfig.collectionType;
                partneRecord.questionnairre = partnerConfig.questionnairre
            }

            output = {
                data: partneRecord,
                success: true,
                message: "",
                status: 200,
            };
        } else {
            output.message = "No Partner Found";
        }
    }
    catch (ex) {
        logger.logs("Error :", ex);
        const model = {
            partnerCode: partnerCode,
            partnerId: partnerId,
            partnerName: partnerName
        };
        saveErrorLog("getPartnerByPartnerCode", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
}
