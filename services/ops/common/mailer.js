"use strict";
const mongoose = require("mongoose");
const nodemailer = require("nodemailer");
const config = require("config");
const { UserInfo } = require("../../../models/ops/registration/users");
const { MailerFormatMaster } = require("../../../models/ops/common/mailer");
const IsNullEmpty = require("isnullemptyorwhitespace");
const { replaceAll, getCurrentTimeStamp, between } = require("../../../services/ops/common/common");
const { NotificationsList } = require("../../../models/ops/partner/partner");
const { LoanInfo } = require("../../../models/ops/loan/loan");
const { saveErrorLog } = require("../../../services/ops/common/masters");
const apiPath = __dirname;
// async..await is not allowed in global scope, must use a wrapper
module.exports.sendFinalMail = async function sendFinalMail(object, subject, body, messageTag) {
  // Generate test SMTP service account from ethereal.email
  // Only needed if you don't have a real mail account for testing
  // create reusable transporter object using the default SMTP transport

  const headers = {
    "X-SES-CONFIGURATION-SET": "snsConfiguration",
    "X-SES-MESSAGE-TAGS": `tagName=${messageTag}`
  }

  let transporter = nodemailer.createTransport({
    host: config.get("mailConfig.host"),
    port: config.get("mailConfig.port"),
    secure: false, // true for 465, false for other ports
    auth: {
      user: config.get("mailConfig.email"), // generated ethereal user
      pass: config.get("mailConfig.password"), // generated ethereal password
    },
  });

  if (object && object.isMultipleAttachments && object.attachments && object.attachments.length) {

    let attachments = object.attachments.map(item => ({
      filename: item.fileName,
      path: item.filePath,
      contentType: item.contentType
    }));

    let obj = {
      from: config.get("mailConfig.from"), // sender address
      to: object.to, // list of receiver
      bcc: object.bcc,
      subject: subject, // Subject line
      text: "", // plain text body
      html: body, // html body
      headers: headers,
      attachments: attachments
    }

    if (object.cc)
      obj['cc'] = object.cc;

    let info = await transporter.sendMail(obj);

    return {
      data: "",
      success: true,
      message: "Message sent: " + info.messageId,
      status: 200,
    }
  }


  if (object.attachmentName != "" && object.cc != "") {

    let attachments = [
      {
        filename: object.attachmentName,
        path: object.attachmentPath,
        contentType: object.contentType,
      }
    ]

    if (object.attachmentPathHref) {
      attachments = [
        {
          filename: object.attachmentName,
          href: object.attachmentPathHref,
          contentType: object.contentType,
        }
      ]
    }

    if (object.attachmentPathHref1)
      attachments.push(
        {
          filename: object.attachmentName1,
          href: object.attachmentPathHref1,
          contentType: object.contentType,
        }
      );

    let info = await transporter.sendMail({
      from: object.from ? object.from : config.get("mailConfig.from"), // sender address
      to: object.to, // list of receivers
      cc: object.cc,
      bcc: object.bcc,
      subject: subject, // Subject line
      text: "", // plain text body
      html: body, // html body
      headers: headers,
      attachments: attachments
    });

    return {
      data: "",
      success: true,
      message: "Message sent: " + info.messageId,
      status: 200,
    };
  }
  else if (object.attachmentName != "") {

    let attachments = [
      {
        filename: object.attachmentName,
        path: object.attachmentPath,
        contentType: object.contentType,
      }
    ]

    if (object.attachmentPathHref) {
      attachments = [
        {
          filename: object.attachmentName,
          href: object.attachmentPathHref,
          contentType: object.contentType,
        }
      ]
    }

    if (object.attachmentPathHref1)
      attachments.push(
        {
          filename: object.attachmentName1,
          href: object.attachmentPathHref1,
          contentType: object.contentType,
        }
      );

    let info = await transporter.sendMail({
      from: object.from ? object.from : config.get("mailConfig.from"), // sender address
      to: object.to, // list of receivers
      subject: subject, // Subject line
      text: "", // plain text body
      html: body, // html body
      headers: headers,
      attachments: attachments
    });

    return {
      data: "",
      success: true,
      message: "Message sent: " + info.messageId,
      status: 200,
    };
  } else if (object.cc != "") {
    let info = await transporter.sendMail({
      from: object.from ? object.from : config.get("mailConfig.from"), // sender address
      to: object.to, // list of receivers
      cc: object.cc,
      subject: subject, // Subject line
      text: "", // plain text body
      html: body, // html body
      headers: headers
    });

    return {
      data: "",
      success: true,
      message: "Message sent: " + info.messageId,
      status: 200,
    };
  } else {
    let info = await transporter.sendMail({
      from: config.get("mailConfig.from"), // sender address
      to: object.to, // list of receivers
      subject: subject, // Subject line
      text: "", // plain text body
      html: body, // html body
      headers: headers
    });

    return {
      data: "",
      success: true,
      message: "Message sent: " + info.messageId,
      status: 200,
    };
  }
  // send mail with defined transport object

  //console.log("Message sent: %s", info.messageId);
  // Message sent: <<EMAIL>>

  // Preview only available when sending through an Ethereal account
  //console.log("Preview URL: %s", nodemailer.getTestMessageUrl(info));
  // Preview URL: https://ethereal.email/message/WaQKMgKddxQDoou...
};

async function formatMailerContent(userId, loanId, object, category, subcategory, messageTag) {
  const mailer = await MailerFormatMaster.findOne({
    category: category,
    subCategory: subcategory,
  });

  if (mailer) {
    let salut = mailer.saluation;
    let subject = mailer.subject;
    let body = mailer.body;
    let header = mailer.header;
    let footer = mailer.footer;
    let regards = mailer.regards;

    salut = replaceAll(salut, "[salutation]", object.salutation);

    subject = replaceAll(subject, "[name]", object.name);
    subject = replaceAll(subject, "[number]", object.number);
    subject = replaceAll(subject, "[date]", object.date);

    body = replaceAll(body, "[amount]", object.amount);
    body = replaceAll(body, "[amount1]", object.amount1);
    body = replaceAll(body, "[amount2]", object.amount2);
    body = replaceAll(body, "[date]", object.date);
    body = replaceAll(body, "[date1]", object.date1);
    body = replaceAll(body, "[number]", object.number);
    body = replaceAll(body, "[ifsc]", object.ifsc);
    body = replaceAll(body, "[accountnumber]", object.accountnumber);
    body = replaceAll(body, "[accounttype]", object.accounttype);
    body = replaceAll(body, "[name]", object.name);
    body = replaceAll(body, "[link]", object.link);
    body = replaceAll(body, "[link1]", object.link1);
    body = replaceAll(body, "[mobile]", object.mobile);
    body = replaceAll(body, "[offer]", object.offer);
    body = replaceAll(body, "[nextdate]", object.nextdate);
    body = replaceAll(body, "[table]", object.table);
    body = replaceAll(body, "[rejectionreason]", object.rejectionreason);
    body = replaceAll(body, "[otp]", object.otp);
    body = replaceAll(body, "[mobile]", object.mobile);
    body = replaceAll(body, "[number1]", object.number1);
    body = replaceAll(body, "[number2]", object.number2);
    body = replaceAll(body, "[rejectCode]", object.rejectCode);
    body = replaceAll(body, "[event]", object.event);
    body = replaceAll(body, "[attempt]", object.attempt);
    body = replaceAll(body, "[comments]", object.comments);
    body = replaceAll(body, "[days]", object.days);
    body = replaceAll(body, "[email]", object.email);

    regards = replaceAll(regards, "[regards_name]", object.regards_name);

    let content = header + salut + body + regards + footer;

    const response = await sendFinalMail(object, subject, content, messageTag);

    if (response.success) {
      saveEmailNotifications(userId, loanId, object.to, object.cc, object.bcc, subcategory, messageTag, mailer._id);
    }

    return response;
  } else {
    return {
      success: false,
      message: "No Mail content found",
      status: 501,
      data: "",
    };
  }
};

module.exports.sendMail = async function (userId, loanId, category, subcategory, object, type) {
  if (type.toLowerCase() == "user") {
    const user = await UserInfo.findById(userId);

    if (!user) {
      return {
        success: false,
        message: "User not found for sending mail",
        status: 501,
        data: "",
      };
    } else {
      const emailId = user.userDetails && user.userDetails.emailId && user.userDetails.emailId[0] ? user.userDetails.emailId[0] : null;

      if (!emailId)
        return {
          success: false,
          message: "EmailId not found for sending mail",
          status: 501,
          data: "",
        };

      object.name = user.userDetails.firstName + " " + user.userDetails.lastName;
      object.salutation = user.userDetails.firstName + " " + user.userDetails.lastName;
      object.to = user.userDetails.emailId[0];
    }
  }
  else if (type.toLowerCase() === "lender"){
    object.salutation = "Team";
  }else if (type.toLowerCase() === "Admin1"){
    object.name = "Admin";
    object.salutation = "Admin";
    object.to = "<EMAIL>";
  }
  else {
    object.name = "Admin";
    object.salutation = "Admin";
    object.to = config.get("mailConfig.adminEmail");
  }


  const date = new Date();
  const stamping = getCurrentTimeStamp().toString() + between(1, 9999999).toString();
  let messageTag = category + "_" + subcategory + "_" + stamping.toString();
  messageTag = messageTag.split(" ").join("_");

  const response = await formatMailerContent(userId, loanId, object, category, subcategory, messageTag);

  return response;
};

module.exports.sendMailOnFailure = async function (subject, body, isFailureForPayout = false) {
  let transporter = nodemailer.createTransport({
    host: config.get("mailConfig.host"),
    port: config.get("mailConfig.port"),
    secure: false, // true for 465, false for other ports
    auth: {
      user: config.get("mailConfig.email"), // generated ethereal user
      pass: config.get("mailConfig.password"), // generated ethereal password
    },
  });
  let failureTo = config.get("keys.SystemFailMailTo");
  let failureCc = config.get("keys.systemFailMailCc");
  if (isFailureForPayout) {
    failureTo = config.get("payOut.toEmail");
    failureCc = config.get("payOut.ccEmail");
  }
  let info = await transporter.sendMail({
    from: config.get("mailConfig.from"), // sender address
    to: failureTo, // list of receivers
    cc: failureCc,
    subject: subject, // Subject line
    text: "", // plain text body
    html: body, // html body
  });

  return {
    data: info,
    success: true,
    message: "Message sent: " + info.messageId,
    status: 200,
  };
};

module.exports.sendExternalMail = async function (object, category, subcategory, customSubject = "") {
  const mailer = await MailerFormatMaster.findOne({
    category: category,
    subCategory: subcategory,
  });

  if (mailer) {
    let salut = mailer.saluation;
    let subject = customSubject != "" ? customSubject : mailer.subject;
    let body = mailer.body;
    let header = mailer.header;
    let footer = mailer.footer;
    let regards = mailer.regards;

    salut = replaceAll(salut, "[salutation]", object.salutation);

    subject = replaceAll(subject, "[name]", object.name);
    subject = replaceAll(subject, "[number]", object.number);
    subject = replaceAll(subject, "[date]", object.date);
    subject = replaceAll(subject, "[dateString]", object.dateString);

    body = replaceAll(body, "[amount]", object.amount);
    body = replaceAll(body, "[amount1]", object.amount1);
    body = replaceAll(body, "[amount2]", object.amount2);
    body = replaceAll(body, "[date]", object.date);
    body = replaceAll(body, "[fromDate]", object.fromDate);
    body = replaceAll(body, "[toDate]", object.toDate);
    body = replaceAll(body, "[number]", object.number);
    body = replaceAll(body, "[ifsc]", object.ifsc);
    body = replaceAll(body, "[accountnumber]", object.accountnumber);
    body = replaceAll(body, "[accounttype]", object.accounttype);
    body = replaceAll(body, "[name]", object.name);
    body = replaceAll(body, "[link]", object.link);
    body = replaceAll(body, "[link1]", object.link1);
    body = replaceAll(body, "[mobile]", object.mobile);
    body = replaceAll(body, "[offer]", object.offer);
    body = replaceAll(body, "[nextdate]", object.nextdate);
    body = replaceAll(body, "[table]", object.table);
    body = replaceAll(body, "[rejectionreason]", object.rejectionreason);
    body = replaceAll(body, "[otp]", object.otp);
    body = replaceAll(body, "[mobile]", object.mobile);
    body = replaceAll(body, "[trueScore]", object.trueScore);
    body = replaceAll(body, "[htmlBody]", object.htmlBody);
    body = replaceAll(body, "[number1]", object.number1);
    body = replaceAll(body, "[number2]", object.number2);
    body = replaceAll(body, "[pan]", object.pan);
    body = replaceAll(body, "[downloadLink]", object.downloadLink);

    regards = replaceAll(regards, "[regards_name]", object.regards_name);

    let content = header + salut + body + regards + footer;

    const date = new Date();
    const stamping = getCurrentTimeStamp().toString() + between(1, 9999999).toString();
    let messageTag = category + "_" + subcategory + "_" + stamping.toString();
    messageTag = messageTag.split(" ").join("_");

    object.userId = null;
    object.loanId = null;
    const response = await sendFinalMail(object, subject, content, messageTag);
    if (response.success) {
      saveEmailNotifications(null, null, object.to, object.cc, object.bcc, subcategory, messageTag, mailer._id);
    }
    return response;
  } else {
    return {
      success: false,
      message: "No Mail content found",
      status: 501,
      data: "",
    };
  }
};

module.exports.test = async function () {
  // console.log("Here, hello");
  return { data: "", success: true };
};

async function saveEmailNotifications(_userId, _loanId, to, cc, bcc, subcategory, messageTag, mailerId) {
  let output = {
    success: false,
    message: "User not found for sending mail",
    status: 501
  };
  try {
    const model = { _userId, _loanId, to, cc, bcc, subcategory, messageTag, mailerId };
    let userId = null;
    let loanId = null;
    let trxnId = null;
    let productId = null;
    let partnerId = null;
    let merchantCode = null;
    let emails = [];

    if (!IsNullEmpty(_userId)) {
      userId = _userId;
    }
    if (!IsNullEmpty(_loanId)) {
      const loanInfo = await LoanInfo.findById(_loanId);
      if (loanInfo) {
        loanId = loanInfo._id;
        trxnId = loanInfo.trxnId;
        productId = loanInfo.productId;
        merchantCode = loanInfo.merchantCode;
        partnerId = loanInfo.partnerId;
      }
    }

    if (!IsNullEmpty(to)) {
      const emailTo = to.split(";");
      emails = [...emailTo];
    }

    if (!IsNullEmpty(cc)) {
      const emailTo = cc.split(";");
      emails = [...emails, ...emailTo];
    }

    if (emails.length > 0) {
      for (let i = 0; i < emails.length; i++) {
        const notificationList = new NotificationsList({
          partnerId: partnerId ? mongoose.Types.ObjectId(partnerId) : null,
          merchantCode: merchantCode,
          userId: userId ? mongoose.Types.ObjectId(userId) : null,
          productId: productId,
          trxnId: trxnId,
          product: "loan",
          loanId: loanId ? mongoose.Types.ObjectId(loanId) : null,
          channel: "Email",
          mobileNo: null,
          email: emails[i],
          templateType: subcategory,
          status: "Sent",
          responseCode: null,
          sentOn: Date.now(),
          deliveredOn: null,
          readOn: null,
          reason: null,
          statusCode: null,
          insertedOn: Date.now(),
          modifiedOn: null,
          fallbackMessageId: null,
          mailerId: mongoose.Types.ObjectId(mailerId),
          messageTag: messageTag
        });
        await notificationList.save();
      }
    }

    output = {
      success: true,
      message: "Notifications Saved Successfully",
      status: 200
    };
  } catch (ex) {
    const model = { _userId, _loanId, to, cc, subcategory, messageTag, mailerId };
    saveErrorLog("saveEmailNotifications", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
  }
  return output;
};

module.exports.replaceMailImagePath = async function (value1, value2) {
  try {
    const list = await MailerFormatMaster.find();

    let i = 0;

    for (const item of list) {

      item.header = item.header.replace(value1, value2);
      await item.save();
      i += 1;

    }

    return {
      data: "",
      success: true,
      message: `${i} records updated`,
      status: 200
    }
  }
  catch (ex) {
    console.log(ex);
    return {
      data: "",
      success: false,
      message: ex.message,
      status: 200
    }
  }
}
