const mongoose = require("mongoose");
const config = require("config");
const axios = require("axios");
const https = require("https");
const http = require("http");
const { Stream } = require("winston/lib/winston/transports");
const fs = require("fs");
const CryptoJS = require("crypto-js");
const { NotificationsList, PartnerMaster } = require("../../../models/ops/partner/partner");
const { UserInfo, UserPartnerInfo, UserInfoHistory } = require("../../../models/ops/registration/users");
const { LoanInfo } = require("../../../models/ops/loan/loan");
const master = require("./masters");
const { exec } = require("child_process");
const IsNullEmpty = require("isnullemptyorwhitespace");
let crypto = require("crypto");
const { getSignedUrlToDownload } = require("../../../models/ops/aws/s3");
const { FILE_UPLOAD_CONFIG } = require('../../../services/ops/common/constants').Constants;
const { DigioMandateGeneration } = require("../../../models/ops/digio/digioApi");
const moment = require("moment");
const debug = require("debug")("app:admin");
const Master = require("./masters");
const { v4: uuidv4 } = require('uuid');
const Logger = require("../../../middleware/logger");
const logger = new Logger();

const apiPath = __dirname;

async function callApi(type, url, access_token, body) {
  let errorObj = "";
  if (type == "GET") {
    try {
      const response = await axios.get(url);
      //console.log(response);
      if (response) {
        return response.data;
      }
    } catch (ex) {
      return ex.response.data;
    }
  } else {
    const response = await axios.post(url, body);

    if (response) {
      let data = response.data;
      return data;
    }
  }
};

module.exports.callApi = callApi;

module.exports.fromDate = function (strDate) {
  strDate = strDate + "T00:00:00.001Z";
  return strDate;
};

module.exports.toDate = function (strDate) {
  strDate = strDate + "T23:59:59.999Z";
  return strDate;
};

module.exports.getName = function (firstName, middleName, lastName) {
  let name = "";

  if (firstName && firstName != null) name += firstName.trim();


  if (middleName && middleName != null) name += " " + middleName.trim();


  if (lastName && lastName != null) name += " " + lastName.trim();

  return name.trim();
};

module.exports.getAddress = function (add1, add2, add3) {
  let address = "";

  if (add1 && add1 != null) address += add1.trim();

  if (add2 && add2 != null) address += " " + add2.trim();

  if (add3 && add3 != null && add1 != add3) address += " " + add3.trim();

  return address.trim();
};

module.exports.getPreApprovedProductId = function (loanType, productType, partnerId, merchantCode) {
  let options = {
    timeZone: "Asia/Calcutta",
    year: "numeric",
    month: "numeric",
    day: "numeric",
    hour: "numeric",
    minute: "numeric",
    second: "numeric",
  };

  let formatter = new Intl.DateTimeFormat([], options);

  let newDate = formatter.format(new Date());
  let d = new Date(newDate);
  let month = "00";
  if (d.getMonth() < 9) {
    month = "0" + (d.getMonth() + 1).toString();
  } else {
    month = d.getMonth().toString();
  }

  let stamping = d.getFullYear().toString() + month + d.getDate().toString() + d.getHours().toString() + d.getMinutes().toString() + d.getSeconds().toString();

  let productId = String(loanType) + "-" + String(productType) + "-" + String(partnerId) + "-" + String(merchantCode) + "-" + stamping;

  let output = {
    data: productId,
    success: true,
    message: "ProductId genereated Successfully",
    status: 200,
  };

  return output;
};

module.exports.getProductId = function (trxnId, merchantCode, identity) {
  let options = {
    timeZone: "Asia/Calcutta",
    year: "numeric",
    month: "numeric",
    day: "numeric",
    hour: "numeric",
    minute: "numeric",
    second: "numeric",
  };

  let formatter = new Intl.DateTimeFormat([], options);

  let newDate = formatter.format(new Date());

  let d = new Date(newDate);
  if (d == "Invalid Date") d = new Date();
  let month = "00";

  if (d.getMonth() < 9) {
    month = "0" + (d.getMonth() + 1).toString();
  } else {
    month = d.getMonth().toString();
  }

  let stamping =
    d.getFullYear().toString() + month + d.getDate().toString() +
    d.getHours().toString() +
    d.getMinutes().toString() +
    d.getSeconds().toString();
  console.log("stamping", stamping);
  let productId =
    String(trxnId) +
    "-" +
    String(merchantCode) +
    "-" +
    String(identity) +
    "-" +
    stamping;

  let output = {
    data: productId,
    success: true,
    message: "ProductId genereated Successfully",
    status: 200,
  };
  return output;
};

function getDate(currentDate) {
  let options = {
    timeZone: "Asia/Calcutta",
    year: "numeric",
    month: "numeric",
    day: "numeric",
    hour: "numeric",
    minute: "numeric",
    second: "numeric",
  };

  let formatter = new Intl.DateTimeFormat([], options);

  let newDate = formatter.format(currentDate);
  return newDate;
}

module.exports.getDate = getDate;

module.exports.getFromDate = function (fromDate) {
  const fromParts = fromDate.split("/");
  fromDate = new Date(fromParts[2], fromParts[1] - 1, fromParts[0], 0, 0, 0);
  return fromDate;
};

module.exports.getToDate = function (toDate) {
  const toParts = toDate.split("/");
  toDate = new Date(toParts[2], toParts[1] - 1, toParts[0], 23, 59, 59);
  return toDate;
};

module.exports.getRandomNo = function (len) {
  try {
    const allCapsAlpha = [..."ABCDEFGHIJKLMNOPQRSTUVWXYZ"];
    const allLowerAlpha = [..."abcdefghijklmnopqrstuvwxyz"];
    const allUniqueChars = [..."~!@#$%()_"];
    const allNumbers = [..."0123456789"];

    const base = [
      ...allCapsAlpha,
      ...allNumbers,
      ...allLowerAlpha,
      ...allUniqueChars,
    ];

    const generator = (base, len) => {
      return [...Array(len)]
        .map((i) => base[(Math.random() * base.length) | 0])
        .join("");
    };

    return generator(base, len);
  }
  catch (ex) {
    master.saveErrorLog("getRandomNo", apiPath, len, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex.toString());
    return null;
  }
};

module.exports.getNewId = async function () {
  try {
    const allCapsAlpha = [..."ABCDEFGHIJKLMNOPQRSTUVWXYZ"];
    const allNumbers = [..."0123456789"];
    const base = [...allCapsAlpha, ...allNumbers];

    const generator = (base, len) => {
      return [...Array(len)]
        .map((i) => base[(Math.random() * base.length) | 0])
        .join("");
    };
    const newId =
      generator(base, 8) +
      "-" +
      generator(base, 4) +
      "-" +
      generator(base, 4) +
      "-" +
      generator(base, 4) +
      "-" +
      generator(base, 12);
    return newId;
  }
  catch (ex) {
    master.saveErrorLog("getNewId", apiPath, null, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex.toString());
    return null;
  }
};

module.exports.downloadImage = async function (url, imagePath) {
  return new Promise(async (resolve, reject) => {
    try {
      let files = imagePath.split("/");
      let maxLength = files.length - 1;
      let dirPath = config.get("parentDir");
      for (i = 0; i < maxLength; i++) {
        dirPath = dirPath + "/" + files[i];

        if (!fs.existsSync(dirPath)) {
          fs.mkdirSync(dirPath);
        }
      }
      imagePath = config.get("parentDir") + imagePath;
      const response = await axios({ url, responseType: "stream" });
      response.data.pipe(fs.createWriteStream(imagePath));
      resolve({
        status: 200,
        success: true,
        message: "Donwloaded Successfully",
      });
    } catch (ex) {
      reject({ status: 501, success: false, message: ex });
    }
  });

  // let imagePath=folderPath +fileName;
  // http.request(fileUrl,function(response){
  //   let data= new Stream();
  //   response.on('data',function(chunk){
  //     data.push(chunk);
  //   });

  //   response.on('end',function(){
  //     fs.writeFileSync(imagePath, data.read());
  //   })
  // }).end();
};

module.exports.downloadImageBuffer = async function (url) {

  try {

    const response = await axios.get(url, { decompress: false, responseType: 'arraybuffer' });

    return {
      data: response.data,
      status: 200,
      success: true,
      message: "",
    }
  } catch (e) {
    return {
      data: "",
      status: 200,
      success: false,
      message: "Download Failed",
    }
  }
};


module.exports.downloadImageFromS3 = async function (url, responseType = "base64") {

  try {
    if (url.startsWith("/")) url = url.substr(1)
    let signedUrl = await getSignedUrlToDownload({ url })

    const response = await axios.get(signedUrl, { decompress: false, responseType: 'arraybuffer' });

    let responseFile = response.data;

    if (responseType === "base64")
      responseFile = new Buffer.from(response.data, 'binary').toString('base64')

    return {
      data: responseFile,
      status: 200,
      success: true,
      message: "",
    }
  } catch (e) {
    return {
      data: "",
      status: 200,
      success: false,
      message: "Download Failed",
    }
  }
};

module.exports.stringToBase64 = function (data) {
  const buff = new Buffer.from(data);
  return buff.toString("base64");
};

module.exports.base64_encode = function (file) {
  // read binary data
  var fileData = fs.readFileSync(file);
  // convert binary data to base64 encoded string
  return new Buffer.from(fileData).toString('base64');
};

module.exports.base64ToString = function (data) {
  const buff = new Buffer.from(data, "base64");
  return buff.toString("ascii");
};

module.exports.getYMDFormattedDate = function (date, seperatedBy) {
  let d = new Date(date);
  let month = "" + (d.getMonth() + 1);
  let day = "" + d.getDate();
  const year = d.getFullYear();

  if (month.length < 2) month = "0" + month;
  if (day.length < 2) day = "0" + day;

  return [year, month, day].join(seperatedBy);
};

function getDMYFormattedDate(date, seperatedBy) {
  let d = new Date(date);
  let month = "" + (d.getMonth() + 1);
  let day = "" + d.getDate();
  const year = d.getFullYear();

  if (month.length < 2) month = "0" + month;
  if (day.length < 2) day = "0" + day;

  return [day, month, year].join(seperatedBy);
};

module.exports.getDMYFormattedDate = getDMYFormattedDate;

module.exports.getDMYFormattedDateWithTime = function (date, seperatedBy) {
  let d = new Date(date);
  let month = "" + (d.getMonth() + 1);
  let day = "" + d.getDate();
  const year = d.getFullYear();
  const hour = d.getHours();
  const minutes = d.getMinutes();
  const seconds = d.getSeconds();

  if (month.length < 2) month = "0" + month;
  if (day.length < 2) day = "0" + day;

  return [day, month, year].join(seperatedBy) + " " + [hour, minutes, seconds].join(":");
};

module.exports.getYMDFormattedDateWithTime = function (date, seperatedBy) {
  let d = new Date(date);
  let month = "" + (d.getMonth() + 1);
  let day = "" + d.getDate();
  const year = d.getFullYear();
  const hour = d.getHours();
  const minutes = d.getMinutes();
  const seconds = d.getSeconds();

  if (month.length < 2) month = "0" + month;
  if (day.length < 2) day = "0" + day;

  return [year, month, day].join(seperatedBy) + " " + [hour, minutes, seconds].join(":");
};

module.exports.getMDYFormattedDate = function (date, seperatedBy) {
  let d = new Date(date);
  let month = "" + (d.getMonth() + 1);
  let day = "" + d.getDate();
  const year = d.getFullYear();

  if (month.length < 2) month = "0" + month;
  if (day.length < 2) day = "0" + day;

  return [month, day, year].join(seperatedBy);
};

module.exports.getExcelFormattedDate = function (date) {
  let d = new Date(date);
  let month = "" + (d.getMonth() + 1);
  let day = "" + d.getDate();
  const year = d.getFullYear();

  if (month.length < 2) month = "0" + month;
  if (day.length < 2) day = "0" + day;

  let newDate = [day, month, year].join("-");
  newDate = newDate + " " + d.getHours() + ":" + d.getMinutes() + ":" + d.getSeconds() + ":" + d.getMilliseconds();
  return newDate;
};

function getMailModel() {
  return {
    from: "",
    to: "",
    cc: "",
    salutation: "",
    name: "",
    number: "",
    date: "",
    date1: "",
    amount: "",
    amount1: "",
    amount2: "",
    ifsc: "",
    accountnumber: "",
    link: "",
    offer: "",
    nextdate: "",
    table: "",
    rejectionreason: "",
    otp: "",
    regards_name: "Team Fundfina",
    attachmentName: "",
    attachmentPath: "",
    contentType: "application/pdf",
    link1: "",
    mobile: "",
    number1: "",
    rejectCode: "",
    event: "",
    attempt: "",
    fromDate: "",
    toDate: "",
    number1: "",
    number2: "",
    pan: "",
    attachmentName: "",
    attachmentPathHref: "",
    attachmentName1: "",
    attachmentPathHref1: "",
    email: ""
  };
};

module.exports.getMailModel = getMailModel;

module.exports.getSmsModel = function () {
  return {
    From: "",
    To: "",
    Msg: "",
    TemplateName: "",
    VAR1: "",
    VAR2: "",
    VAR3: "",
    VAR4: "",
    VAR5: "",
    VAR6: "",
    VAR7: "",
    VAR8: "",
    VAR9: "",
    VAR10: "",
    VAR11: "",
    VAR12: "",
    Type: "",
    CTA: "",
    AttachmentName1: "",
    Attachment1: "",
    AttachmentPath1: ""
  };
};

// Decimal round
module.exports.round10 = (value, exp) => decimalAdjust('round', value, exp);
// Decimal floor
module.exports.floor10 = (value, exp) => decimalAdjust('floor', value, exp);
// Decimal ceil
module.exports.ceil10 = (value, exp) => decimalAdjust('ceil', value, exp);
//Example:
// var num = 5.53389;
// var n = ceil10(num, -2);
//Ouput : 5.54

function decimalAdjust(type, value, exp) {
  // If the exp is undefined or zero...
  if (typeof exp === 'undefined' || +exp === 0) {
    return Math[type](value);
  }
  value = +value;
  exp = +exp;
  // If the value is not a number or the exp is not an integer...
  if (isNaN(value) || !(typeof exp === 'number' && exp % 1 === 0)) {
    return NaN;
  }
  // Shift
  value = value.toString().split('e');
  value = Math[type](+(value[0] + 'e' + (value[1] ? (+value[1] - exp) : -exp)));
  // Shift back
  value = value.toString().split('e');
  return +(value[0] + 'e' + (value[1] ? (+value[1] + exp) : exp));
};

module.exports.getExtension = function (path) {
  const lastIndex = path.lastIndexOf("?");
  if (lastIndex) {
    path = lastIndex == -1 ? path : path.substring(0, lastIndex);
  }
  const firstIndex = path.lastIndexOf(".");
  const extension = path.substring(firstIndex);
  return extension;
};

module.exports.callDigioApi = async function (type, urlString, body, responseType = null) {
  let errorObj = "";
  const url = config.get("digio.url") + urlString;
  const headers = {
    'Authorization': config.get("digio.token")
  }

  if (type == "GET") {
    try {
      const response = await axios.get(url, { headers });

      if (response) {
        return response.data;
      }
    } catch (ex) {

      return ex.response.data;
    }
  } else if (type == "POST") {

    let props = {};

    if (responseType)
      props = {
        headers,
        responseType
      }
    else
      props = {
        headers
      }

    const response = await axios.post(url, body, props);

    if (response) {
      let data = response.data;
      return data;
    }
  }
  else if (type == "DOC") {
    try {

      const response = await axios.request({
        method: 'GET',
        url: url,
        responseType: 'arraybuffer',
        headers: headers
      });

      if (response) {
        return response.data;
      }
    } catch (ex) {
      return ex.response.data;
    }
  }
};

module.exports.getOcrId = function (userId) {
  let options = {
    timeZone: "Asia/Calcutta",
    year: "numeric",
    month: "numeric",
    day: "numeric",
    hour: "numeric",
    minute: "numeric",
    second: "numeric",
  };

  let formatter = new Intl.DateTimeFormat([], options);

  let newDate = formatter.format(new Date());
  let d = new Date(newDate);
  let month = "00";
  if (d.getMonth() < 9) {
    month = "0" + (d.getMonth() + 1).toString();
  } else {
    month = d.getMonth().toString();
  }

  let stamping =
    d.getFullYear().toString() +
    month +
    d.getDate().toString() +
    d.getHours().toString() +
    d.getMinutes().toString() +
    d.getSeconds().toString();

  let ocrId = String(userId) + "-" + stamping + "-" + between(1, 99999);

  let output = {
    data: ocrId,
    success: true,
    message: "OcrId genereated Successfully",
    status: 200,
  };
  return output;
};

module.exports.sleep = function (ms) {
  return new Promise((resolve) => {
    setTimeout(resolve, ms);
  });
};

function between(min, max) {
  return Math.floor(
    Math.random() * (max - min) + min
  )
};
module.exports.between = between;

module.exports.getUniqueKey = function (loanNo) {
  let options = {
    timeZone: "Asia/Calcutta",
    year: "numeric",
    month: "numeric",
    day: "numeric",
    hour: "numeric",
    minute: "numeric",
    second: "numeric",
  };

  let d = new Date();
  let month = "00";
  if (d.getMonth() < 9) {
    month = "0" + (d.getMonth() + 1).toString();
  } else {
    month = d.getMonth().toString();
  }

  let stamping = d.getFullYear().toString() + month + d.getDate().toString() + d.getHours().toString() + d.getMinutes().toString() + d.getSeconds().toString();

  let keyId = String(loanNo) + "-" + stamping + "-" + between(1, 9999999);

  let output = {
    key: keyId,
    success: true,
    message: "key genereated Successfully",
    status: 200,
  };
  return output;
};

module.exports.encrypt = function (plainText) {
  let secret = config.get("keys.encryptionKey");

  let b64 = CryptoJS.AES.encrypt(plainText, secret).toString();
  let e64 = CryptoJS.enc.Base64.parse(b64);
  let eHex = e64.toString(CryptoJS.enc.Hex);
  return eHex;
}

module.exports.decrypt = function (cipherText) {
  let secret = config.get("keys.encryptionKey");

  let reb64 = CryptoJS.enc.Hex.parse(cipherText);
  let bytes = reb64.toString(CryptoJS.enc.Base64);
  let decrypt = CryptoJS.AES.decrypt(bytes, secret);
  let plain = decrypt.toString(CryptoJS.enc.Utf8);
  return plain;
}

module.exports.validateMobileNumber = function (input) {
  var phonenoformat = /^\d{10,12}$/;
  return phonenoformat.test(input);
};

module.exports.validateEmail = function (input) {
  var mailformat = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
  return mailformat.test(input.toLowerCase());
};

module.exports.validatePan = function (input) {
  var panformat = /([A-Z]){5}([0-9]){4}([A-Z]){1}$/;
  return panformat.test(input.toUpperCase());
};

module.exports.replaceAll = function (string, search, replace) {
  return string.split(search).join(replace);
};

module.exports.callKarixApi = async function (type, url, body) {
  let errorObj = "";

  const headers = {
    'Authentication': config.get("karix.token"),
    'Content-Type': 'application/json'
  }

  // console.log("headers", headers);
  // console.log("url", url);
  // console.log("body", body);

  if (type == "GET") {
    try {
      const response = await axios.get(url, { headers });

      if (response) {
        return response.data;
      }
    } catch (ex) {
      return ex.response.data;
    }
  } else {
    try {
      const response = await axios.post(url, body, { headers });

      if (response) {
        let data = response.data;
        return data;
      }
    }
    catch (ex) {
      return ex.response.data;
    }
  }
};

module.exports.callRebrandlyApi = async function (type, url, body) {

  const headers = {
    'apikey': config.get("rebrandly.apiKey"),
    'Content-Type': 'application/json'
  }

  if (type == "GET") {
    try {
      const response = await axios.get(url, { headers });

      if (response) {
        return response.data;
      }
    } catch (ex) {
      return ex.response.data;
    }
  } else {
    try {
      const response = await axios.post(url, body, { headers });

      if (response) {
        let data = response.data;
        return data;
      }
    }
    catch (ex) {
      return ex.response.data;
    }
  }
};

module.exports.callTinyUrlApi = async function (type, url, body, signature) {

  const headers = {
    'account-id': config.get("tinyUrl.account-id"),
    'Content-Type': 'application/json',
    'auth-signature': signature
  }

  if (type == "GET") {
    try {
      const response = await axios.get(url, { headers });

      if (response) {
        return response.data;
      }
    } catch (ex) {
      return ex.response.data;
    }
  } else {
    try {
      const response = await axios.post(url, body, { headers });

      if (response) {
        let data = response.data;
        return data;
      }
    }
    catch (ex) {
      console.log("ex", ex);
      return ex.message;
    }
  }
};

async function pushToNotifications(partnerId, merchantCode, productId, trxnId, userId, loanId, channel, mobileNo,
  templateType, product = "loan", fallbackMessageId = null, preApprovalId = null, smsModel = null, isFallbackMessage = false,
  smsType = null, numberType = "primary", url = null, primaryMobileNo = "", meta, ruleId) {

  try {
    const newNotification = new NotificationsList({
      partnerId: partnerId ? partnerId : null,
      merchantCode: merchantCode ? merchantCode : null,
      productId: productId ? productId : null,
      trxnId: trxnId ? trxnId : null,
      userId: userId ? userId : null,
      product: product,
      loanId: loanId ? loanId : null,
      channel: channel ? channel : null,
      mobileNo: mobileNo ? mobileNo : null,
      templateType: templateType ? templateType : null,
      status: null,
      responseCode: null,
      sentOn: null,
      deliveredOn: null,
      readOn: null,
      reason: null,
      statusCode: null,
      insertedOn: Date.now(),
      modifiedOn: null,
      fallbackMessageId: fallbackMessageId,
      preApprovalId: preApprovalId,
      smsModel: smsModel,
      isFallbackMessage: isFallbackMessage,
      smsType: smsType,
      numberType: numberType,
      url,
      primaryMobileNo,
      meta,
      ruleId
    });

    const save = await newNotification.save();

    return {
      data: save,
      success: true,
      message: "Saved successfully"
    }
  }
  catch (ex) {
    logger.logs(ex);
    return {
      data: "Error",
      success: false,
      message: ex.message
    }
  }
};

module.exports.pushToNotifications = pushToNotifications;

module.exports.getCurrentTimeStamp = function () {
  return new Date().valueOf().toString();
};

module.exports.convertToCamelCase = function (str) {
  var splitStr = str.toLowerCase().split(' ');
  for (var i = 0; i < splitStr.length; i++) {
    // You do not need to check if i is larger than splitStr length, as your for does that for you
    // Assign it back to the array
    splitStr[i] = splitStr[i].charAt(0).toUpperCase() + splitStr[i].substring(1);
  }
  // Directly return the joined string
  return splitStr.join(' ');
};

module.exports.updateUserAndLoanSteps = async function (loan, user, step, currentUserRole = null, currentUserId = null) {
  try {

    if (user.kycStepDetails && user.kycStepDetails.length > 0) {
      let foundIndex = user.kycStepDetails.findIndex(x => x.Step === step);

      if (foundIndex !== -1) {
        user.kycStepDetails[foundIndex].isCompleted = true;
        user.kycStepDetails[foundIndex].completedOn = Date.now();
      }
      else
        user.kycStepDetails.push({
          Step: step,
          isCompleted: true,
          insertedOn: Date.now(),
          completedOn: Date.now()
        });

    }
    else
      user.kycStepDetails.push({
        Step: step,
        isCompleted: true,
        insertedOn: Date.now(),
        completedOn: Date.now()
      });

    if (loan.loanStepDetails && loan.loanStepDetails.length > 0) {
      let foundIndex = loan.loanStepDetails.findIndex(x => x.Step === step);

      if (foundIndex !== -1) {
        loan.loanStepDetails[foundIndex].isCompleted = true;
        loan.loanStepDetails[foundIndex].completedOn = Date.now();
      }
      else
        loan.loanStepDetails.push({
          Step: step,
          isCompleted: true,
          insertedOn: Date.now(),
          completedOn: Date.now(),
        });

    }
    else
      loan.loanStepDetails.push({
        Step: step,
        isCompleted: true,
        insertedOn: Date.now(),
        completedOn: Date.now(),
      });

    loan.updatedOn = Date.now();
    loan.lastStepCompleted = step;
    loan.lastStepCompletedAt = Date.now();

    if (step === "KYC") {
      const partner = await PartnerMaster.findById(loan.partnerId);
      if (currentUserRole && currentUserRole.toLowerCase() === "user") {
        user.userDetails.loan.lastUpdatedByLoanId = loan && loan._id ? loan._id : null;
        user.userDetails.loan.lastUpdatedByLoanNo = loan && loan.loanNo ? loan.loanNo : null;
        user.userDetails.loan.lastUpdatedByPartner = partner && partner.partnerName ? partner.partnerName : null;
        user.userDetails.loan.lastUpdatedByPartnerId = partner && partner._id ? partner._id : null;
        user.userDetails.loan.updatedOn = Date.now();
        user.userDetails.kycUpdatedOn = Date.now();

        user.userDetails.documentConsent = true;
        user.userDetails.docConsentGivenOn = Date.now();
      }
      else if (currentUserRole && currentUserRole.toLowerCase() === "admin")
        user.userDetails.adminModifiedDate = Date.now();

      user.userDetails.kycUpdatedByRole = currentUserRole ? currentUserRole : "";
      user.userDetails.kycUpdatedBy = currentUserId ? currentUserId : null;
    }

    return { data: { loan: loan, user: user }, success: true, message: "Model updated" };

  }
  catch (ex) {
    master.saveErrorLog("updateUserAndLoanSteps", apiPath, { loan, user, step, currentUserRole }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex.toString());
    return { data: { loan: loan, user: user }, success: false, message: ex.message };
  }
}

module.exports.insertUpdatePartnerInfo = async function (userId, partnerId, merchantCode) {
  try {
    if (!IsNullEmpty(merchantCode)) {
      let userPartnerInfo = await UserPartnerInfo.findOne({ partnerId: mongoose.Types.ObjectId(partnerId), userId: mongoose.Types.ObjectId(userId), merchantCode: merchantCode, isRetailer: true });

      if (IsNullEmpty(userPartnerInfo)) {
        userPartnerInfo = await UserPartnerInfo.findOne({ partnerId: mongoose.Types.ObjectId(partnerId), userId: mongoose.Types.ObjectId(userId), merchantCode: null, isRetailer: true });
      }

      if (IsNullEmpty(userPartnerInfo)) {
        userPartnerInfo = new UserPartnerInfo({
          userId: mongoose.Types.ObjectId(userId),
          partnerId: mongoose.Types.ObjectId(partnerId),
          merchantCode: merchantCode,
          isRetailer: true,
          isPartnerUser: false,
          loanHistory: [],
          ledgerDetails: null,
          insertedOn: Date.now(),
          updatedOn: Date.now()
        });
      } else {
        userPartnerInfo.merchantCode = merchantCode;
        userPartnerInfo.updatedOn = Date.now();
      }

      await userPartnerInfo.save();

      return { data: "", success: true, message: "UserPartnerInfo updated successfully" };
    }
    else
      return { data: "", success: false, message: "Merchantcode not found" };
  }
  catch (ex) {
    master.saveErrorLog("insertUpdatePartnerInfo", apiPath, { userId, partnerId, merchantCode }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex.toString());
    return { data: "", success: false, message: ex.message };
  }
}

module.exports.insertStepsInLoanInfo = async function (loan) {
  try {

    if (!loan.loanStepDetails || loan.loanStepDetails.length === 0) {

      loan.loanStepDetails.push({
        Step: "Contact Information",
        isCompleted: false,
        insertedOn: Date.now(),
        completedOn: null
      });

      loan.loanStepDetails.push({
        Step: "OTP Screen",
        isCompleted: false,
        insertedOn: Date.now(),
        completedOn: null
      });

      loan.loanStepDetails.push({
        Step: "OTP Matched",
        isCompleted: false,
        insertedOn: Date.now(),
        completedOn: null
      });

      loan.loanStepDetails.push({
        Step: "Loan",
        isCompleted: false,
        insertedOn: Date.now(),
        completedOn: null
      });

      loan.loanStepDetails.push({
        Step: "KYC",
        isCompleted: false,
        insertedOn: Date.now(),
        completedOn: null
      });

      loan.loanStepDetails.push({
        Step: "Shop",
        isCompleted: false,
        insertedOn: Date.now(),
        completedOn: null
      });

      loan.loanStepDetails.push({
        Step: "Questionnairre",
        isCompleted: false,
        insertedOn: Date.now(),
        completedOn: null
      });

      loan.loanStepDetails.push({
        Step: "Review",
        isCompleted: false,
        insertedOn: Date.now(),
        completedOn: null
      });

      loan.loanStepDetails.push({
        Step: "Bank",
        isCompleted: false,
        insertedOn: Date.now(),
        completedOn: null
      });

      loan.loanStepDetails.push({
        Step: "eNach",
        isCompleted: false,
        insertedOn: Date.now(),
        completedOn: null
      });

      loan.loanStepDetails.push({
        Step: "Loan Contract",
        isCompleted: false,
        insertedOn: Date.now(),
        completedOn: null
      });
    }

    return { data: { loan }, success: true, message: "Steps added to model" }

  }
  catch (ex) {
    master.saveErrorLog("insertStepsInLoanInfo", apiPath, { loanId }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex.toString());

    return { data: null, success: false, message: ex.message }
  }
}
module.exports.getProductType = function (str) {
  let productType = str ? str.split("_") : null;
  productType = (productType && productType[0]) ? productType[0] : "";
  return productType;
};

function isObject(val) {
  return typeof val == 'object';
};

module.exports.convertArrayOfObjectToStringForHmac = function (objs) {
  let message = "";
  for (let i = 0; i < objs.length; i++) {
    message += convertObjectToStringForHmac(objs[i], "");
  }
  return '[' + message + ']';
};

function convertObjectToStringForHmac(obj, message = "") {
  if (isObject(obj)) {
    let keys = Object.keys(obj);
    keys.sort();
    message += "{";
    for (let key of keys) {
      let value = obj[key]
      if (!isObject(value) || value instanceof Date)
        message += `"${key}":"${typeof value == "string" ?
          value.replace(/\s/g, '') : value}",`
      else
        message += convertObjectToStringForHmac(value, `"${key}":`) + ",";
    }
    message = message.substr(0, message.length - 1) + '}';
    return message;
  }
  return "";
}
module.exports.convertObjectToStringForHmac = convertObjectToStringForHmac;

module.exports.awaitHandler = (promise) => {
  return promise
    .then(data => ([data, undefined]))
    .catch(error => Promise.resolve([undefined, error]));
}

module.exports.generateHmac = function (text, key, algo, returnType) {

  if (!text) {
    return text;
  }
  return crypto.createHmac(algo || 'sha256', key)
    .update(text)
    .digest(returnType || 'hex');
}

module.exports.generateHmacSha512 = function (text) {

  if (!text) {
    return text;
  }
  return crypto.createHash('sha512').update(text).digest('hex');
}


module.exports.simpleUniqueId = (length = 16) => {
  return parseInt(Math.ceil(Math.random() * Date.now()).toPrecision(length).toString().replace(".", "")) + ""
}

module.exports.getFileName = function (input) {
  return input.replace(/^.*[\\\/]/, '');
}

module.exports.getAge = function (birthDate) {
  let today = new Date();
  let age = today.getFullYear() - birthDate.getFullYear();
  let m = today.getMonth() - birthDate.getMonth();
  if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  return age;
}

module.exports.getMimeType = function (ext) {
  let mimeType = null;

  switch (ext.toLowerCase()) {
    case ".png":
      mimeType = "image/png";
      break;
    case ".jpg":
      mimeType = "image/jpeg";
      break;
    case ".jpeg":
      mimeType = "image/jpeg";
      break;
    case ".gif":
      mimeType = "image/gif";
      break;
    case ".pdf":
      mimeType = "application/pdf";
      break;
    default:
      mimeType = null;
      break;
  }

  return mimeType;
}

module.exports.maskNumber = function (value, unmaskedDigits) {
  if (!value) return value;

  value = value.toString();

  let returnValue = "";

  const maskedLength = value.length - unmaskedDigits;

  for (let i = 0; i < maskedLength; i++) {
    returnValue += "x";
  }

  return returnValue + value.substr(value.length - unmaskedDigits);
};

module.exports.deleteFile = async function (filePath, userId = null) {
  filePath = config.get("parentDir") + filePath;

  try {
    if (fs.existsSync(filePath)) {
      fs.unlink(filePath, (err) => {
        if (err) {
          console.log(err);
          return false;
        }
        else {
          return true;
        }

      });
    }
    else
      return false;
  }
  catch (ex) {
    master.saveErrorLog("deleteFile", apiPath, { filePath, userId }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex.toString());
    return false;
  }
}

module.exports.imagePathEncrypt = function (plainText) {
  let secret = config.get("keys.imagePathEncryptionKey");
  let b64 = CryptoJS.AES.encrypt(plainText, secret).toString();
  let e64 = CryptoJS.enc.Base64.parse(b64);
  let eHex = e64.toString(CryptoJS.enc.Hex);
  return eHex;
}

module.exports.imagePathDecrypt = function (cipherText) {
  let secret = config.get("keys.imagePathEncryptionKey");
  let reb64 = CryptoJS.enc.Hex.parse(cipherText);
  let bytes = reb64.toString(CryptoJS.enc.Base64);
  let decrypt = CryptoJS.AES.decrypt(bytes, secret);
  let plain = decrypt.toString(CryptoJS.enc.Utf8);
  return plain;
}

module.exports.removeSpecialChars = function (input) {
  return input.replace(/[^a-zA-Z0-9/, ]/g, '');
}

module.exports.Sort = function (field, reverse, primer) {
  const key = primer ?
    function (x) {
      return primer(x[field])
    } :
    function (x) {
      return x[field]
    };

  reverse = !reverse ? 1 : -1;

  return function (a, b) {
    return a = key(a), b = key(b), reverse * ((a > b) - (b > a));
  }
}


module.exports.genderValidation = function (poaDetails, gender) {
  if (!gender) return false
  gender = getGenderConstant(gender)
  if (poaDetails && poaDetails.docVerification) {
    if (poaDetails.docVerification.type === "Ocr") {
      if (poaDetails.docVerification.verificationType == "AADHAAR" && poaDetails.docVerification.uid && poaDetails.docVerification.uid.ocr) {
        if (!poaDetails.docVerification.uid.ocr.gender) poaDetails.docVerification.uid.ocr.gender = gender
        if (getGenderConstant(poaDetails.docVerification.uid.ocr.gender) === gender) return true
      } else if (poaDetails.docVerification.verificationType == "VOTER_ID" && poaDetails.docVerification.voterId && poaDetails.docVerification.voterId.ocr) {
        if (!poaDetails.docVerification.voterId.ocr.gender) poaDetails.docVerification.voterId.ocr.gender = gender
        if (getGenderConstant(poaDetails.docVerification.voterId.ocr.gender) === gender) return true
      }
    } else if (poaDetails.docVerification.type === "digilocker") {
      if (poaDetails.docVerification.verificationType == "AADHAAR" && poaDetails.docVerification.uid && poaDetails.docVerification.uid.details) {
        if (!poaDetails.docVerification.uid.details.gender) poaDetails.docVerification.uid.details.gender = gender
        if (getGenderConstant(poaDetails.docVerification.uid.details.gender) === gender) return true
      }
    }
  }

  return false
}

const getGenderConstant = function (string) {
  if (!string) return ""
  string = string.toLowerCase()
  if (string === "f" || string === "female") return "female"
  if (string === "m" || string === "male") return "male"
  return ""
}

module.exports.getGenderConstant = getGenderConstant

module.exports.getGenderFromPoa = function (poaDetails) {

  if (poaDetails && poaDetails.docVerification) {
    if (poaDetails.docVerification.type === "Ocr") {
      if (poaDetails.docVerification.verificationType == "AADHAAR" && poaDetails.docVerification.uid && poaDetails.docVerification.uid.ocr) {
        return poaDetails.docVerification.uid.ocr.gender
      } else if (poaDetails.docVerification.verificationType == "VOTER_ID" && poaDetails.docVerification.voterId && poaDetails.docVerification.voterId.ocr) {
        return poaDetails.docVerification.voterId.ocr.gender
      }
    } else if (poaDetails.docVerification.type === "digilocker") {
      if (poaDetails.docVerification.verificationType == "AADHAAR" && poaDetails.docVerification.uid && poaDetails.docVerification.uid.details) {
        return poaDetails.docVerification.uid.details.gender
      }
    }
  }

  return ""
}

module.exports.FormatCells = function (XLSX, ws, col) {
  for (let element in col) {
    let format = col[element]
    let colNum = XLSX.utils.decode_col(element);
    let range = XLSX.utils.decode_range(ws['!ref']);
    for (let i = range.s.r + 1; i <= range.e.r; ++i) {
      let ref = XLSX.utils.encode_cell({ r: i, c: colNum });
      if (!ws[ref]) continue;
      if (ws[ref].t != 'n') continue;
      ws[ref].z = format;
    }
  }
}

module.exports.generateOtp = function (numberOfDigits) {

  let zeros = "";

  if (numberOfDigits > 0) {
    for (let i = 1; i < numberOfDigits; i++) {
      zeros += "0"
    }
  } else {
    zeros = "00000"; /** DEFAULT 6 DIGIT */
  }

  let otp = Math.floor((+(1 + zeros)) + Math.random() * (+(9 + zeros)));
  return otp;
}

module.exports.addMinutes = function (mins) {
  let now = new Date();
  now.setMinutes(now.getMinutes() + mins);
  return now
}

module.exports.minutesDifference = function (date1, date2) {
  let msDifference = date2 - date1;
  let minutes = Math.floor(msDifference / 1000 / 60);
  return minutes
}

module.exports.validateFile = function (module, type, file, extension) {

  file = Buffer.from(file.replace(/^data:.+;base64,/, ""), "base64")

  const sizeINByte = Buffer.byteLength(file)
  const sizeInMB = sizeINByte / (1024 * 1024)
  const allowedMaxSize = FILE_UPLOAD_CONFIG[module] && FILE_UPLOAD_CONFIG[module][type] && FILE_UPLOAD_CONFIG[module][type].MAX_FILE_SIZE;
  const allowedFileTypes = FILE_UPLOAD_CONFIG[module] && FILE_UPLOAD_CONFIG[module][type] && FILE_UPLOAD_CONFIG[module][type].ALLOWED_FILE_TYPES;

  if (!allowedFileTypes || !allowedFileTypes.includes(extension))
    return { error: { message: "Invalid File Extension (" + extension + ")" } }


  if (!allowedMaxSize || allowedMaxSize < sizeInMB)
    return { error: { message: "Size Can't be More Than " + allowedMaxSize + 'MB' } }

  return { error: null, response: { fileSize: sizeInMB, extension } }

}

module.exports.checkPassword = function (str) {
  var re = /^(?=.*\d)(?=.*[!@#$%^&*])(?=.*[a-z])(?=.*[A-Z]).{8,}$/;
  return re.test(str);
}


module.exports.leadingZeroFormatExcel = function (value) {
  value = `${value}`

  if (value.length == 1) {
    value = `0${value}`
  }

  return `'${value}`
}


module.exports.simpleTemplateEngine = function (tpl, data) {
  var re = /{([^}]+)?}/g, match;
  while (match = re.exec(tpl)) {
    re = /{([^}]+)?}/g, match;
    tpl = tpl.replace(match[0], data[match[1]])
  }
  return tpl;
}

module.exports.getWindowTime = function (cutOffTimeConfig) {
  if (!cutOffTimeConfig) {
    return new Date()
  }

  let windowDate = moment(new Date()).add(cutOffTimeConfig.hours, "hours").toDate();
  windowDate = moment(windowDate).add(cutOffTimeConfig.minutes, "minutes").toDate();

  return windowDate;
}

module.exports.saveGstDetails = async (type, userId, value = null, state = null) => {
  try {
    const gstUrl = `${config.get("keys.dataIngestionApiUrl")}/api/job/gst/data/orchestrator`;

    let gstBody = null;

    if (type === "pantogst")
      gstBody = {
        "userId": userId.toString(),
        "type": "pantogst",
        "details": {
          "panNo": value,
          "state": state
        }
      }
    else
      gstBody = {
        "userId": userId.toString(),
        "type": "detailedgst",
        "details": {
          "gstin": value
        }
      }

    await callApi("POST", gstUrl, null, gstBody);

  }
  catch (ex) {
    master.saveErrorLog("saveGstDetails", apiPath, { type, userId, value, state }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex.toString());
    return false;
  }
};

module.exports.isPnachAvailable = async (loanId) => {
  try {
    const loan = await LoanInfo.findById(loanId);

    let isAvailable = false;

    if (loan.status === "Draft" || loan.status === "Loan Is Funding" || loan.status === "Under Review") {
      if (loan.loanContractDetails && loan.loanContractDetails.authMode === "physical") {
        const mandate = await DigioMandateGeneration.findOne({ loanIds: loanId, mode: "physical", status: "created" }).sort({ createdOn: -1 });

        if (mandate)
          isAvailable = true;
      }
    }

    return isAvailable;

  }
  catch (ex) {
    console.log(ex);
    master.saveErrorLog("isPnachAvailable", apiPath, loanId, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex.toString());
    return false;
  }
};

async function backupUserInfo(userId) {
  let output = {
    success: false,
    message: "Some Error Occurred",
    status: 501,
  };
  try {
    const userInfo = await UserInfo.findById(userId);
    if (userInfo) {
      const tempUserInfo = userInfo.toJSON();
      tempUserInfo.userId = mongoose.Types.ObjectId(tempUserInfo["_id"]);
      delete tempUserInfo["_id"];
      const userInfoHistory = new UserInfoHistory(tempUserInfo);
      userInfoHistory.deletedOn = Date.now();
      await userInfoHistory.save();

      output = {
        success: true,
        message: "Saved Successfully",
        status: 200,
      };
    } else {
      output.message = "No user found";
    }
  } catch (ex) {
    output.message = ex.message;
  }
  return output;
};

module.exports.backupUserInfo = backupUserInfo;

module.exports.getSessionExpireTime = () => {
  let expiryDate = new Date(Date.now() + 86400000); // 24 hours
  return expiryDate
}

module.exports.numberOfDaysDifferece = (date1, date2) => {
  date1 = new Date(date1);
  date2 = new Date(date2);
  const diffTime = Math.abs(date2 - date1);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays
}

module.exports.mathRandomNumber = () => {
  return Math.floor(1000 + Math.random() * 9000);
}

module.exports.removeFile = (filePath, callback) => {
  fs.unlink(filePath, function () { callback && callback() })
}

module.exports.getIpInfoByApi = async (ip) => {
  try {
    let url = `${config.get("keys.geoIPApi")}/${ip}/json/?key=${config.get("keys.geoIPApiKey")}`
    let ipInfo = await callApi("GET", url)

    if (ipInfo && ipInfo.error) {
      master.saveErrorLog("ipApiError", apiPath, "", ipInfo.reason);
      return null
    }

    return ipInfo
  } catch (ex) {
    console.log(ex);
    master.saveErrorLog("getIpInfoByApi", apiPath, "", ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex.toString());
    return null;
  }
}

module.exports.callInvoiceApi = async function (type, url, body, signature, contentType = "application/json") {

  const headers = {
    'Content-Type': contentType,
    'auth-signature': signature
  }

  if (type == "GET") {
    try {
      const response = await axios.get(url, { headers });

      if (response) {
        return response.data;
      }
    } catch (ex) {
      return ex.response.data;
    }
  } else if (type == "POST" && headers['Content-Type'] == "application/json") {
    try {
      const response = await axios.post(url, body, { headers });

      if (response) {
        let data = response.data;
        return data;
      }
    }
    catch (ex) {
      return {
        data: "",
        success: false,
        message: ex.response && ex.response.data && ex.response.data.message ? ex.response.data.message : ex.message,
        status: 200
      }
    }
  }
  else if (type == "POST" && headers['Content-Type'] == "multipart/form-data") {
    axios({
      method: "post",
      url: url,
      data: body,
      headers: { headers },
    })
      .then(function (response) {
        let data = response.data;
        return data;
      })
      .catch(function (ex) {
        return {
          data: "",
          success: false,
          message: ex.message,
          status: 200
        }
      });
  }
};
module.exports.generateUniqueFileName = () => {
  return uuidv4() + '_' + new Date().getTime();
}

module.exports.getMobileNumberArray = (mobileNumbersObj) => {
  let arr = []
  if (mobileNumbersObj && mobileNumbersObj.length) {
    mobileNumbersObj.forEach(element => {
      arr.push(element.mobileNumber)
    })
  }

  return arr
}

module.exports.buildMobileNumberObject = (mobileNumber, isPrimary = true, isVerified = true) => {
  let obj = null
  if (mobileNumber) {
    obj = { mobileNumber, isPrimary, isVerified }
  }

  return obj
}

module.exports.updateMobileNumberChannelDetails = async (type, payload) => {

  let { userId } = payload

  let user = await UserInfo.findById(userId)

  if (!user) return
  let { mobileNo } = user.userDetails

  mobileNo.forEach((element, index) => {

    if (element.mobileNumber === payload.mobileNumber) {
      if (type === "SMS") {

        if (!element.channel) element.channel = {}
        element.channel.sms = {
          isActive: true,
          lastMessageSentOn: payload.lastMessageSentOn
        }

      } else if (type === "WHATSAPP") {
        if (!element.channel) element.channel = {}
        element.channel.whatsapp = {
          isActive: true,
          lastMessageSentOn: payload.lastMessageSentOn
        }
      }
    }
  })

  await UserInfo.updateOne({ _id: userId }, { $set: { "userDetails.mobileNo": mobileNo } })

}

module.exports.getAge = (dateString) => {
  var today = new Date();
  var birthDate = new Date(dateString);
  var age = today.getFullYear() - birthDate.getFullYear();
  var m = today.getMonth() - birthDate.getMonth();
  if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  return age;
}



const templateValueConfigEngineForSmsModal = (config, payload, notificationTemplate = {}) => {

  let values = {}
  try {

    let index = 0

    for (let key in config) {
      let obj = config[key]

      if (obj.type === "DYNAMIC") {
        values[`VAR${index + 1}`] = payload[obj.value] || " "
      } else {
        values[`VAR${index + 1}`] = notificationTemplate ? notificationTemplate[key] || " " : " "
      }

      index++
    }
    return values

  } catch (e) {
    saveErrorLog("templateValueConfigEngine", "templateValueConfigEngine", payload, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    return null
  }

}

module.exports.templateValueConfigEngineForSmsModal = templateValueConfigEngineForSmsModal


module.exports.getSimpleReferenceNo = (loanNo) => {
  let uniqueId = uuidv4() + '_' + (loanNo)
  return uniqueId
}