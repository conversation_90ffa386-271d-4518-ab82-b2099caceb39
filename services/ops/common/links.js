const { Links, UrlShortnerApiResponse } = require("../../../models/ops/common/links");
const { callRebrandlyApi, callTinyUrlApi, convertObjectToStringForHmac } = require("../../../services/ops/common/common");
const ShortUniqueId = require('short-unique-id');
const config = require("config");
const { saveErrorLog } = require("../../../services/ops/common/masters");
const apiPath = __dirname;
let Crypto = require("crypto-js");

module.exports.generateCustomRebrandlyUrl = async function (longUrl, type, userId = null, loanId = null, loanNo = null, merchantCode = null) {
    try {
        const link = await Links.findOne({ longUrl });

        if (link)
            return {
                data: link.shortUrl, success: true, message: "Link already exists"
            }

        const baseUrl = config.get("shortUrl.baseUrl");

        let uniqueUrlCode = false;

        let urlCode = "";

        let title = "";

        const contractCode = config.get("shortUrl.contractCode");
        const eNachCode = config.get("shortUrl.eNachCode");
        const reviewCode = config.get("shortUrl.reviewCode");
        const offerCode = config.get("shortUrl.offerCode");
        const consentCode = config.get("shortUrl.consentCode");
        const bankLinkCode = config.get("shortUrl.bankLinkCode");
        const incompleteApplicationCode = config.get("shortUrl.incompleteApplicationCode");

        if (type === contractCode)
            title = loanNo ? `Loan Contract ${loanNo}` : "Loan Contract";
        else if (type === eNachCode)
            title = loanNo ? `Loan e-Mandate ${loanNo}` : "Loan e-Mandate";
        else if (type === reviewCode)
            title = loanNo ? `Review Link ${loanNo}` : "Review Link";
        else if (type === offerCode)
            title = merchantCode ? `Offer Link ${merchantCode}` : "Offer Link";
        else if (type === consentCode)
            title = loanNo ? `CreditLine Link ${loanNo}` : "CreditLine Link";
        else if (type === bankLinkCode)
            title = loanNo ? `Bank Link ${loanNo}` : "Bank Link";
        else if (type === incompleteApplicationCode)
            title = loanNo ? `Incomplete Application Link ${loanNo}` : "Incomplete Application Link";

        if (!title)
            title = type + " " + loanNo ? loanNo : "";

        while (uniqueUrlCode === false) {
            const uid = new ShortUniqueId({ length: 4 });

            const slashtag = uid();

            const shortYear = new Date().getFullYear().toString().substring(2);

            urlCode = shortYear + slashtag;

            const urlCodeExist = await Links.findOne({ type: type, urlCode: urlCode });

            if (!urlCodeExist)
                uniqueUrlCode = true;

        }

        const linkModel = new Links({
            userId: userId ? userId : null,
            loanId: loanId ? loanId : null,
            type: type ? type : "",
            baseUrl: baseUrl,
            urlCode: urlCode ? urlCode : "",
            longUrl: longUrl,
            shortUrl: `${baseUrl}/${type}/${urlCode}`,
            insertedOn: Date.now()
        });

        await linkModel.save();

        const rebrandlyResponse = createRebrandlyShortLink(baseUrl, longUrl, `${type}/${urlCode}`, userId, loanId, title);

        return rebrandlyResponse;

    }
    catch (ex) {
        saveErrorLog("generateCustomUrl", apiPath, { longUrl, type, userId, loanId, loanNo }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);

        return { data: "", success: false, message: "Error occurred" };

    }
}

async function createRebrandlyShortLink(domain, longUrl, urlCode, userId = null, loanId = null, title = null) {

    try {

        const url = `${config.get("rebrandly.baseUrl")}/v1/links`;

        const model = {
            destination: longUrl,
            title: title ? title : "New Link",
            slashtag: urlCode,
            domain: {
                fullName: domain
            }
        };

        const date = Date.now();

        const response = await callRebrandlyApi("POST", url, model);

        const urlModel = new UrlShortnerApiResponse({
            userId: userId ? userId : null,
            loanId: loanId ? loanId : null,
            url: url ? url : "",
            request: JSON.stringify(model),
            response: JSON.stringify(response),
            requestSentOn: date,
            responseReceivedOn: Date.now()
        });

        await urlModel.save();

        if (response && response.shortUrl)
            return { data: response.shortUrl, success: true, message: "Url created successfully" };
        else
            return { data: "", success: false, message: "Some error occurred" };
    }
    catch (ex) {

        saveErrorLog("createRebrandlyShortLink", apiPath, { domain, longUrl, urlCode, userId, loanId, title }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);

        const urlModel = new UrlShortnerApiResponse({
            userId: userId ? userId : null,
            loanId: loanId ? loanId : null,
            url: url ? url : "",
            request: JSON.stringify(model),
            response: JSON.stringify(response),
            requestSentOn: date,
            responseReceivedOn: Date.now()
        });

        await urlModel.save();

        return { data: "", success: false, message: "Some error occurred" };
    }
}

async function createFundfinaShortLink(longUrl, category, userId = null, loanId = null, meta = null) {

    try {

        const url = `${config.get("tinyUrl.url")}/api/shrink`;

        const model = {
            url: longUrl,
            category: category,
            meta: meta
        };

        const date = Date.now();

        const message = convertObjectToStringForHmac(model);

        const calculatedSignature = Crypto.HmacSHA256(message, config.get("tinyUrl.secretKey")).toString();

        const response = await callTinyUrlApi("POST", url, model, calculatedSignature);

        const urlModel = new UrlShortnerApiResponse({
            userId: userId ? userId : null,
            loanId: loanId ? loanId : null,
            url: url ? url : "",
            request: JSON.stringify(model),
            response: JSON.stringify(response),
            requestSentOn: date,
            responseReceivedOn: Date.now()
        });

        await urlModel.save();

        if (response && response.shortUrl)
            return { data: response.shortUrl, success: true, message: "Url created successfully" };
        else
            return { data: "", success: false, message: "Some error occurred" };
    }
    catch (ex) {
        console.log(ex);
        saveErrorLog("createFundfinaShortLink", apiPath, { longUrl, category, userId, loanId }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);

        const urlModel = new UrlShortnerApiResponse({
            userId: userId ? userId : null,
            loanId: loanId ? loanId : null,
            url: url ? url : "",
            request: JSON.stringify(model),
            response: JSON.stringify(response),
            requestSentOn: date,
            responseReceivedOn: Date.now()
        });

        await urlModel.save();

        return { data: "", success: false, message: "Some error occurred" };
    }
}
module.exports.createFundfinaShortLink = createFundfinaShortLink

module.exports.generateCustomUrl = async function (longUrl, type, userId = null, loanId = null, loanNo = null, merchantCode = null, partnerName = null, offerId = null, partnerId = null, notificationId = null) {
    try {
        let title = "";

        const contractCode = config.get("shortUrl.contractCode");
        const eNachCode = config.get("shortUrl.eNachCode");
        const reviewCode = config.get("shortUrl.reviewCode");
        const offerCode = config.get("shortUrl.offerCode");
        const consentCode = config.get("shortUrl.consentCode");
        const bankLinkCode = config.get("shortUrl.bankLinkCode");
        const incompleteApplicationCode = config.get("shortUrl.incompleteApplicationCode");

        let meta = {};

        if (type === contractCode)
            meta = {
                title: contractCode,
                loanId: loanId ? loanId.toString() : "",
                loanNo: loanNo ? loanNo : "",
                merchantCode: merchantCode ? merchantCode : "",
                partnerName: partnerName ? partnerName : ""
            };
        else if (type === eNachCode)
            meta = {
                title: eNachCode,
                loanId: loanId ? loanId.toString() : "",
                loanNo: loanNo ? loanNo : "",
                merchantCode: merchantCode ? merchantCode : "",
                partnerName: partnerName ? partnerName : ""
            };
        else if (type === reviewCode)
            meta = {
                title: reviewCode,
                merchantCode: merchantCode ? merchantCode : "",
                partnerName: partnerName ? partnerName : ""
            };
        else if (type === offerCode)
            meta = {
                title: offerCode,
                offerId: offerId ? offerId.toString() : "",
                merchantCode: merchantCode ? merchantCode : "",
                partnerName: partnerName ? partnerName : "",
                partnerId: partnerId ? partnerId.toString() : "",
                notificationId: notificationId ? notificationId.toString() : ""
            };
        else if (type === consentCode)
            meta = {
                title: consentCode,
                loanId: loanId ? loanId.toString() : "",
                loanNo: loanNo ? loanNo : "",
                merchantCode: merchantCode ? merchantCode : "",
                partnerName: partnerName ? partnerName : ""
            };
        else if (type === bankLinkCode)
            meta = {
                title: bankLinkCode,
                merchantCode: merchantCode ? merchantCode : "",
                partnerName: partnerName ? partnerName : ""
            };
        else if (type === incompleteApplicationCode)
            meta = {
                title: incompleteApplicationCode,
                loanId: loanId ? loanId.toString() : "",
                loanNo: loanNo ? loanNo : "",
                merchantCode: merchantCode ? merchantCode : "",
                partnerName: partnerName ? partnerName : ""
            };

        const rebrandlyResponse = createFundfinaShortLink(longUrl, type, userId, loanId, meta);

        return rebrandlyResponse;

    }
    catch (ex) {
        saveErrorLog("generateCustomUrl", apiPath, { longUrl, type, userId, loanId, loanNo }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);

        return { data: "", success: false, message: "Error occurred" };

    }
}
