const {
  BankBranchMaster,
  CityStateMaster,
  DefinationDetailsMaster,
  ErrorLog,
  OtpMatchLog,
  OtpInfo,
  UserDeviceInfo
} = require("../../../models/ops/common/master");
const { UserInfo } = require("../../../models/ops/registration/users");
const { LoanInfo } = require("../../../models/ops/loan/loan");
const { DigioBankList } = require("../../../models/ops/digio/digioApi")
const _ = require("lodash");
const mongoose = require("mongoose");
const { ConfigParameters } = require("../../../models/ops/partner/partner");
const config = require("config");
const { getIpInfoByApi } = require("./common");
const { InvoiceMaster } = require("../../../models/ops/invoice/invoice");
const apiPath = __dirname;

module.exports.getIfscCodes = async function (str) {
  try {
    if (!str) {
      let list = await BankBranchMaster.find().distinct("ifsc");

      list = list.slice(0, 10);

      return {
        data: resArr,
        success: true,
        message: "",
        status: 200,
      };
    } else {
      let regexp = new RegExp("^" + str);

      let list = await BankBranchMaster.find({
        ifsc: { $regex: regexp, $options: "i" },
      }).distinct("ifsc");

      list = list.slice(0, 10);

      return {
        data: list,
        success: true,
        message: "",
        status: 200,
      };
    }
  }
  catch (ex) {
    saveErrorLog("getIfscCodes", apiPath, str, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);

    return {
      data: null,
      success: false,
      message: ex.message ? ex.message.toString().replace(/\r?\n|\r/g, " ") : ex,
      status: 200
    }
  }
};

module.exports.getBankNames = async function (str) {
  try {
    if (!str || str == "") {
      const list = await BankBranchMaster.find().distinct("bank");

      let resArr = [];
      list.forEach(function (item) {
        let i = resArr.findIndex((x) => x.bank == item.bank);
        if (i <= -1) {
          resArr.push({ bank: item.bank });
        }
      });

      return {
        data: resArr,
        success: true,
        message: "",
        status: 200,
      };
    } else {
      let regexp = new RegExp("^" + str);

      const list = await BankBranchMaster.find({
        bank: { $regex: regexp, $options: "i" },
      }).distinct("bank");

      let resArr = [];
      list.forEach(function (item) {
        let i = resArr.findIndex((x) => x.bank == item.bank);
        if (i <= -1) {
          resArr.push({ bank: item.bank });
        }
      });

      return {
        data: list,
        success: true,
        message: "",
        status: 200,
      };
    }
  }
  catch (ex) {
    saveErrorLog("getBankNames", apiPath, str, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);

    return {
      data: null,
      success: false,
      message: ex.message ? ex.message.toString().replace(/\r?\n|\r/g, " ") : ex,
      status: 200
    }
  }
};

module.exports.getBranchNames = async function (bankName, branchName) {
  try {
    if (bankName == null || bankName == "") {
      return {
        data: "",
        success: false,
        message: "Please provide Bank Name",
        status: 400,
      };
    } else if (branchName == null || branchName == "") {
      const list = await BankBranchMaster.find({
        bank: { $regex: regexp, $options: "i" },
      })
        .select("branch -_id")
        .distinct("branch");

      return {
        data: list,
        success: true,
        message: "",
        status: 200,
      };
    } else {
      branchName = new RegExp("^" + branchName);

      const list = await BankBranchMaster.find({
        bank: bankName,
        branch: { $regex: branchName, $options: "i" },
      })
        .select("branch -_id")
        .distinct("branch");

      return {
        data: list,
        success: true,
        message: "",
        status: 200,
      };
    }
  }
  catch (ex) {
    saveErrorLog("getBranchNames", apiPath, { bankName, branchName }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);

    return {
      data: null,
      success: false,
      message: ex.message ? ex.message.toString().replace(/\r?\n|\r/g, " ") : ex,
      status: 200
    }
  }
};

module.exports.getBankfromBranch = async function (bank, branch) {
  try {
    const list = await BankBranchMaster.find({
      bank: bank,
      branch: branch,
    }).select("-_id");

    return {
      data: list,
      success: true,
      message: "",
      status: 200,
    };
  }
  catch (ex) {
    saveErrorLog("getBankfromBranch", apiPath, { bank, branch }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);

    return {
      data: null,
      success: false,
      message: ex.message ? ex.message.toString().replace(/\r?\n|\r/g, " ") : ex,
      status: 200
    }
  }
};

module.exports.getStatesByPincode = async function (pincode) {
  try {
    if (pincode) {

      let queryObj = {
        pincode: pincode
      }
      const list = await CityStateMaster.distinct("state", queryObj);

      return {
        data: list,
        success: true,
        message: "",
        status: 200,
      };

    } else {

      return {
        data: [],
        success: true,
        message: "",
        status: 200,
      };
    }
  }
  catch (ex) {
    saveErrorLog("getStates", apiPath, state, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);

    return {
      data: null,
      success: false,
      message: ex.message ? ex.message.toString().replace(/\r?\n|\r/g, " ") : ex,
      status: 200
    }
  }
};


module.exports.getStates = async function (state, pincode) {
  try {

    let queryObj = {}
    if (pincode) queryObj.pincode = pincode
    let list = await CityStateMaster.find(queryObj).distinct("state");

    if (state) {
      list = localSearchFromArray(list, state)
    }

    return {
      data: list,
      success: true,
      message: "",
      status: 200,
    };
  }
  catch (ex) {
    saveErrorLog("getStates", apiPath, state, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);

    return {
      data: null,
      success: false,
      message: ex.message ? ex.message.toString().replace(/\r?\n|\r/g, " ") : ex,
      status: 200
    }
  }
};

module.exports.getCities = async function (state, city, pincode) {
  try {
    let queryObj = {}
    if (pincode) queryObj.pincode = pincode
    if (state) queryObj.state = state.toUpperCase()
    let list = await CityStateMaster.distinct("city", queryObj);

    if (city) {
      list = localSearchFromArray(list, city)
    }

    return {
      data: list,
      success: true,
      message: "",
      status: 200,
    };
  }
  catch (ex) {
    saveErrorLog("getCities", apiPath, { state, city }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);

    return {
      data: null,
      success: false,
      message: ex.message ? ex.message.toString().replace(/\r?\n|\r/g, " ") : ex,
      status: 200
    }
  }
};

module.exports.getDefinitionMaster = async function (model) {
  try {
    const definitionDetails = await DefinationDetailsMaster.find({
      definationType: {
        $regex: new RegExp("^" + model.definitionType + "$", "i"),
      },
      isActive: 1,
    }).sort("sortingOrder");

    return {
      data: definitionDetails,
      success: true,
      message: "",
      status: 200,
    };
  }
  catch (ex) {
    saveErrorLog("getDefinitionMaster", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);

    return {
      data: null,
      success: false,
      message: ex.message ? ex.message.toString().replace(/\r?\n|\r/g, " ") : ex,
      status: 200
    }
  }
};

module.exports.isValidState = async function (stateValue, pincode) {
  try {

    let query = {}

    if (pincode) query.pincode = pincode

    const list = await CityStateMaster.find(query)
      .select("state -_id")
      .distinct("state");

    const state = _.filter(
      list,
      (item) => item.toLowerCase() === stateValue.toLowerCase()
    );

    if (state.length > 0) {
      return {
        data: "",
        success: true,
        message: "State is Valid",
        status: 200,
      };
    } else {
      return {
        data: "",
        success: false,
        message: "State is Invalid",
        status: 200,
      };
    }
  }
  catch (ex) {
    saveErrorLog("isValidState", apiPath, stateValue, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);

    return {
      data: null,
      success: false,
      message: ex.message ? ex.message.toString().replace(/\r?\n|\r/g, " ") : ex,
      status: 200
    }
  }
};

module.exports.isValidCity = async function (stateValue, cityValue) {
  try {

    let queryObj = {}

    if (stateValue) queryObj.state = stateValue.toUpperCase()

    const cities = await CityStateMaster.distinct("city", queryObj)

    const city = localSearchFromArray(cities, cityValue)

    if (city.length > 0) {
      return {
        data: "",
        success: true,
        message: "City is Valid",
        status: 200,
      };
    } else {
      return {
        data: "",
        success: false,
        message: "City is InValid",
        status: 200,
      };
    }
  }
  catch (ex) {
    saveErrorLog("isValidCity", apiPath, { stateValue, cityValue }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);

    return {
      data: null,
      success: false,
      message: ex.message ? ex.message.toString().replace(/\r?\n|\r/g, " ") : ex,
      status: 200
    }
  }

};

module.exports.isValidIfscCode = async function (ifscCode) {
  try {
    const ifsc = await BankBranchMaster.find({
      ifsc: {
        $regex: new RegExp("^" + ifscCode + "$", "i"),
      },
    });

    if (ifsc.length > 0) {
      return {
        data: "",
        success: true,
        message: "IFSC Code is Valid",
        status: 200,
      };
    } else {
      return {
        data: "",
        success: false,
        message: "IFSC Code is Invalid",
        status: 200,
      };
    }
  }
  catch (ex) {
    saveErrorLog("isValidIfscCode", apiPath, ifscCode, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);

    return {
      data: null,
      success: false,
      message: ex.message ? ex.message.toString().replace(/\r?\n|\r/g, " ") : ex,
      status: 200
    }
  }
};

module.exports.isValidPAN = async function (userId, panNo) {
  try {
    const pan = await UserInfo.findOne({
      panNo: { $regex: panNo, $options: "i" },
    });

    if (pan && !pan._id.equals(mongoose.Types.ObjectId(userId))) {
      return {
        data: "",
        success: false,
        message: "PAN Number already exists",
        status: 200,
      };
    } else {
      return {
        data: "",
        success: true,
        message: "allow",
        status: 200,
      };
    }
  }
  catch (ex) {
    saveErrorLog("isValidPAN", apiPath, { userId, panNo }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);

    return {
      data: null,
      success: false,
      message: ex.message ? ex.message.toString().replace(/\r?\n|\r/g, " ") : ex,
      status: 200
    }
  }
};

module.exports.isValidPOA = async function (userId, poaNo) {
  try {
    const poa = await UserInfo.findOne({
      "poaDetails.poaNo": { $regex: poaNo, $options: "i" },
    });

    if (poa && !poa._id.equals(mongoose.Types.ObjectId(userId))) {
      return {
        data: "",
        success: false,
        message: "POA Number already exists",
        status: 200,
      };
    } else {
      return {
        data: "",
        success: true,
        message: "allow",
        status: 200,
      };
    }
  }
  catch (ex) {
    saveErrorLog("isValidPOA", apiPath, { userId, poaNo }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);

    return {
      data: null,
      success: false,
      message: ex.message ? ex.message.toString().replace(/\r?\n|\r/g, " ") : ex,
      status: 200
    }
  }
};

module.exports.isValidUdyogAadhaar = async function (userId, udyogAadhaarNo) {
  try {
    const aadhaar = await UserInfo.findOne({
      "udyogAadhaar.aadhaarNo": { $regex: udyogAadhaarNo, $options: "i" },
    });

    if (aadhaar && !aadhaar._id.equals(mongoose.Types.ObjectId(userId))) {
      return {
        data: "",
        success: false,
        message: "Udyog Aadhaar Number already exists",
        status: 200,
      };
    } else {
      return {
        data: "",
        success: true,
        message: "allow",
        status: 200,
      };
    }
  }
  catch (ex) {
    saveErrorLog("isValidUdyogAadhaar", apiPath, { userId, udyogAadhaarNo }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);

    return {
      data: null,
      success: false,
      message: ex.message ? ex.message.toString().replace(/\r?\n|\r/g, " ") : ex,
      status: 200
    }
  }
};

module.exports.isValidGst = async function (userId, gstNo) {
  try {
    const gst = await UserInfo.findOne({
      "gstDetails.gstNo": { $regex: gstNo, $options: "i" },
    });

    if (gst && !gst._id.equals(mongoose.Types.ObjectId(userId))) {
      return {
        data: "",
        success: false,
        message: "GST Number already exists",
        status: 200,
      };
    } else {
      return {
        data: "",
        success: true,
        message: "allow",
        status: 200,
      };
    }
  }
  catch (ex) {
    saveErrorLog("isValidGst", apiPath, { userId, gstNo }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);

    return {
      data: null,
      success: false,
      message: ex.message ? ex.message.toString().replace(/\r?\n|\r/g, " ") : ex,
      status: 200
    }
  }
};

module.exports.checkEmail = async function (userId, emailId) {
  try {
    const email = await UserInfo.findOne({
      "userDetails.emailId": { $regex: emailId, $options: "i" },
    });

    const user = await UserInfo.findById(userId);

    if (email && !email._id.equals(mongoose.Types.ObjectId(userId))) {
      return {
        data: "",
        success: false,
        message: "Please Enter Valid Email",
        status: 200,
      };
    } else {
      return {
        data: "",
        success: true,
        message: "allow",
        status: 200,
      };
    }
  }
  catch (ex) {
    saveErrorLog("checkEmail", apiPath, { userId, emailId }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);

    return {
      data: null,
      success: false,
      message: ex.message ? ex.message.toString().replace(/\r?\n|\r/g, " ") : ex,
      status: 200
    }
  }
};

module.exports.checkPrimaryEmail = async function (userId, emailId) {
  try {
    const email = await UserInfo.findOne({
      "userDetails.emailId": { $regex: emailId, $options: "i" },
    });

    const user = await UserInfo.findById(userId);

    if (email && !email._id.equals(mongoose.Types.ObjectId(userId))) {
      return {
        data: "",
        success: false,
        message: "Please Enter Valid Email",
        status: 200,
      };
    } else {
      return {
        data: "",
        success: true,
        message: "allow",
        status: 200,
      };
    }
  }
  catch (ex) {
    saveErrorLog("checkPrimaryEmail", apiPath, { userId, emailId }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);

    return {
      data: null,
      success: false,
      message: ex.message ? ex.message.toString().replace(/\r?\n|\r/g, " ") : ex,
      status: 200
    }
  }
};

module.exports.checkMobile = async function (userId, mobileNo) {
  try {
    if (mobileNo.length != 10 || isNaN(mobileNo)) {
      return {
        data: "",
        success: false,
        message: "Invalid Mobile Number",
        status: 200,
      };
    }

    const mobile = await UserInfo.findOne({
      "userDetails.mobileNo.mobileNumber": { $regex: mobileNo, $options: "i" },
    });

    const user = await UserInfo.findById(userId);

    // if (user.userDetails.mobileNo[0] && user.userDetails.mobileNo[0].toLowerCase() === mobileNo.toLowerCase()) {
    //   return {
    //     data: "",
    //     success: false,
    //     message: "Mobile No cannot be same as primary mobile",
    //     status: 200,
    //   };
    // }

    if (mobile && !mobile._id.equals(mongoose.Types.ObjectId(userId))) {
      return {
        data: "",
        success: false,
        message: "Please Enter Valid Mobile",
        status: 200,
      };
    } else {
      return {
        data: "",
        success: true,
        message: "allow",
        status: 200,
      };
    }
  }
  catch (ex) {
    saveErrorLog("checkMobile", apiPath, { userId, mobileNo }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);

    return {
      data: null,
      success: false,
      message: ex.message ? ex.message.toString().replace(/\r?\n|\r/g, " ") : ex,
      status: 200
    }
  }
};

module.exports.checkPrimaryMobile = async function (userId, mobileNo) {
  try {
    if (mobileNo.length != 10 || isNaN(mobileNo)) {
      return {
        data: "",
        success: false,
        message: "Invalid Mobile Number",
        status: 200,
      };
    }

    const mobile = await UserInfo.findOne({
      "userDetails.mobileNo.mobileNumber": mobileNo
    });

    //const user = await UserInfo.findById(userId);

    // if (user.userDetails.mobileNo[1] && user.userDetails.mobileNo[1].toLowerCase() === mobileNo.toLowerCase()) {
    //   return {
    //     data: "",
    //     success: false,
    //     message: "Mobile No cannot be same as secondary mobile",
    //     status: 200,
    //   };
    // }

    if (mobile && !mobile._id.equals(mongoose.Types.ObjectId(userId))) {
      return {
        data: "",
        success: false,
        message: "Please Enter Valid Mobile",
        status: 200,
      };
    } else {
      return {
        data: "",
        success: true,
        message: "allow",
        status: 200,
      };
    }
  }
  catch (ex) {
    saveErrorLog("checkPrimaryMobile", apiPath, { userId, mobileNo }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);

    return {
      data: null,
      success: false,
      message: ex.message ? ex.message.toString().replace(/\r?\n|\r/g, " ") : ex,
      status: 200
    }
  }
};

async function saveErrorLog(method, apiPath, request, errorMsg) {
  try {
    const errorlog = new ErrorLog({
      method: method,
      path: apiPath,
      request: request,
      errorMsg: errorMsg,
      insertedOn: Date.now(),
    });
    //console.log(errorlog);
    await errorlog.save();
  } catch (ex) {
    //console.log("Error saveErrorLog", ex);
  }
}

module.exports.saveErrorLog = saveErrorLog;

module.exports.saveOtpFailureLog = async function (loanId = null, otp = null, otpMatch = null, otpType = null, otpAutoFill = false, channel = "SMS") {
  try {
    let loan = await LoanInfo.findById(loanId);

    const record = await OtpInfo.findOne({
      userId: loan.userId,
      loanId: loanId,
      isActive: true,
      purpose: otpType,
      channel: channel
    }).sort(
      {
        insertedOn: -1,
      }
    );

    await logUserOtp(loan.userId, loanId, loan && loan.partnerId ? loan.partnerId : null, loan && loan.merchantCode ? loan.merchantCode : null, otp, record && record.mobileNo ? record.mobileNo : null, otpMatch, otpType, otpAutoFill);

    return {
      data: "",
      success: true,
      message: "saved successfully"
    }
  }
  catch (ex) {
    saveErrorLog("saveOtpFailureLog", apiPath, { loanId }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);

    return {
      data: "",
      success: false,
      message: ex.message ? ex.message.toString().replace(/\r?\n|\r/g, " ") : ex,
      status: 200
    }
  }
}

async function logUserOtp(userId = null, loanId = null, partnerId = null, merchantCode = null, otpEntered = null, mobileNo = null, otpMatch = null, otpType = null, otpAutoFill = false, channel = "SMS") {
  try {
    const otpLog = new OtpMatchLog({
      userId: userId,
      loanId: loanId,
      partnerId: partnerId,
      merchantCode: merchantCode,
      otpEntered: otpEntered,
      mobileNo: mobileNo,
      otpMatch: otpMatch,
      otpType: otpType,
      insertedOn: Date.now(),
      otpAutoFill: otpAutoFill,
      channel: channel
    });

    await otpLog.save();


    return {
      data: "",
      success: true,
      message: "Saved successfully"
    }
  }
  catch (ex) {
    saveErrorLog("logUserOtp", apiPath, { loanId }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);

    return {
      data: "",
      success: false,
      message: ex.message ? ex.message.toString().replace(/\r?\n|\r/g, " ") : ex,
      status: 200
    }
  }
}

module.exports.logUserOtp = logUserOtp;

async function saveDeviceInfo(model, ip) {
  try {
    const deviceInfo = new UserDeviceInfo({
      partnerId: model.partnerId,
      merchantCode: model.merchantCode ? model.merchantCode : null,
      browserName: model.browserName ? model.browserName : null,
      browserVersion: model.browserVersion ? model.browserVersion : null,
      fullBrowserVersion: model.fullBrowserVersion ? model.fullBrowserVersion : null,
      mobileVendor: model.mobileVendor ? model.mobileVendor : null,
      mobileModel: model.mobileModel ? model.mobileModel : null,
      osName: model.osName ? model.osName : null,
      osVersion: model.osVersion ? model.osVersion : null,
      insertedOn: Date.now()
    });

    if (ip) {

      const ipInfo = await getIpInfoByApi(ip)
      if (ipInfo && ipInfo.latitude && ipInfo.longitude && !ipInfo.error) {
        deviceInfo.ipInfo = ipInfo
      }
    }

    await deviceInfo.save();

    return {
      data: "",
      success: true,
      message: "Saved successfully",
      status: 200
    }
  }
  catch (ex) {
    saveErrorLog("saveDeviceInfo", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);

    return {
      data: "",
      success: false,
      message: ex.message ? ex.message.toString().replace(/\r?\n|\r/g, " ") : ex,
      status: 200
    }
  }
}

module.exports.getRoleWiseDefinitionList = async function (model, userId) {
  try {
    const user = await UserInfo.findById(userId);

    const role = user.userDetails.role;

    let definitionDetails = [];

    if (role.toLowerCase() === "admin")
      definitionDetails = await DefinationDetailsMaster.find({
        definationType: {
          $regex: new RegExp("^" + model.definitionType + "$", "i"),
        },
        isActive: 1
      }).sort({ defination: 1 });
    else if (role.toLowerCase() === "lending partner")
      definitionDetails = await DefinationDetailsMaster.find({
        definationType: {
          $regex: new RegExp("^" + model.definitionType + "$", "i"),
        },
        isActiveForLendingPartner: 1
      }).sort({ defination: 1 });
    else if (role.toLowerCase() === "borrowing partner")
      definitionDetails = await DefinationDetailsMaster.find({
        definationType: {
          $regex: new RegExp("^" + model.definitionType + "$", "i"),
        },
        isActiveForLendingPartner: 1
      }).sort({ defination: 1 });

    return {
      data: definitionDetails,
      success: true,
      message: "",
      status: 200,
    };
  }
  catch (ex) {
    saveErrorLog("getRoleWiseDefinitionList", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);

    return {
      data: "",
      success: false,
      message: ex.message ? ex.message.toString().replace(/\r?\n|\r/g, " ") : ex,
      status: 200
    }
  }
};

module.exports.saveDeviceInfo = saveDeviceInfo;

module.exports.isValidPOANumber = async function (userId, poaNo, poaType = null) {
  try {
    let poa = null;

    if (poaType === "Aadhaar Card")
      return {
        data: "",
        success: true,
        message: "allow",
        status: 200,
      };

    if (poaType)
      poa = await UserInfo.findOne({
        "poaDetails.poaNo": { $regex: poaNo, $options: "i" },
        "poaDetails.poaType": poaType
      });
    else
      poa = await UserInfo.findOne({
        "poaDetails.poaNo": { $regex: poaNo, $options: "i" }
      });

    if (poa && !poa._id.equals(mongoose.Types.ObjectId(userId)))
      return {
        data: "",
        success: false,
        message: "POA Number already exists",
        status: 200,
      };
    else
      return {
        data: "",
        success: true,
        message: "allow",
        status: 200,
      };
  }
  catch (ex) {
    saveErrorLog("isValidPOANumber", apiPath, { userId, poaNo, poaType }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);

    return {
      data: "",
      success: false,
      message: ex.message ? ex.message.toString().replace(/\r?\n|\r/g, " ") : ex,
      status: 200
    }
  }
};


module.exports.getSupportedMandateMethod = async function (ifsc) {
  try {
    const bank = await BankBranchMaster.findOne({ ifsc });

    if (!bank) {
      return {
        data: "",
        success: false,
        message: "",
        status: 500
      }
    }

    const bankName = bank.bank

    const digioBank = await DigioBankList.findOne({
      systemBankName: {
        $regex: new RegExp("^" + bankName + "$", "i")
      }
    });

    if (!digioBank) {
      return {
        data: "",
        success: false,
        message: "Digio Bank not Found",
        status: 500
      }
    }
    return {
      data: {
        "netBanking": digioBank.netBanking,
        "debitCard": digioBank.debitCard,
        "esign": digioBank.esign,
        "physical": (config.get("digio.eNach.physicalMandate") && !digioBank.netBanking && !digioBank.debitCard && !digioBank.esign) ? true : false
      },
      success: false,
      message: "success",
      status: 200
    };


  } catch (ex) {
    saveErrorLog("getSupportedMandateMethod", apiPath, ifsc, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);

    return {
      data: "",
      success: false,
      message: ex.message ? ex.message.toString().replace(/\r?\n|\r/g, " ") : ex,
      status: 200
    }
  }
}


module.exports.getConfiguration = async function (channel) {
  try {
    const configuration = await ConfigParameters.findOne({ channel });

    if (!configuration) {
      return {
        data: "",
        success: false,
        message: "",
        status: 500
      }
    }

    return {
      data: configuration,
      success: false,
      message: "success",
      status: 200
    };
  } catch (ex) {
    saveErrorLog("getConfiguration", apiPath, channel, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);

    return {
      data: "",
      success: false,
      message: ex.message ? ex.message.toString().replace(/\r?\n|\r/g, " ") : ex,
      status: 200
    }
  }
}


const localSearchFromArray = (arr, value) => {
  try {
    if (!arr && !arr.length) return []

    let search = arr.filter(element => element.toLowerCase().includes(value.toLowerCase()))
    return search
  } catch (ex) {
    saveErrorLog("localSearch", "localSearch", { arr, value }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex.toString());
    return []
  }

}

module.exports.getInvoiceTypeList = async function (userId) {
  try {
    const user = await UserInfo.findById(userId);

    const role = user.userDetails.role;

    let invoiceTypeList = [];

    if (role.toLowerCase() === "admin")
      invoiceTypeList = await InvoiceMaster.find({
        isActive: true
      }).select({ invoiceType: 1, invoiceText: 1 }).sort({ invoiceType: 1 });
    else if (role.toLowerCase() === "lending partner")
      invoiceTypeList = await InvoiceMaster.find({
        isActive: true, invoiceFor: role
      }).select({ invoiceType: 1, invoiceText: 1 }).sort({ invoiceType: 1 });
    else if (role.toLowerCase() === "borrowing partner")
      invoiceTypeList = await InvoiceMaster.find({
        isActive: true, invoiceFor: role
      }).select({ invoiceType: 1, invoiceText: 1 }).sort({ invoiceType: 1 });

    return {
      data: invoiceTypeList,
      success: true,
      message: "",
      status: 200,
    };
  }
  catch (ex) {
    saveErrorLog("getInvoiceTypeList", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);

    return {
      data: "",
      success: false,
      message: ex.message ? ex.message.toString().replace(/\r?\n|\r/g, " ") : ex,
      status: 200
    }
  }
};
