const mongoose = require("mongoose");
const { PartnerMaster, PartnerTransactions } = require("../../../models/ops/partner/partner");
const { MerchantShopType } = require("../../../models/ops/user/userCentricData");
const { saveErrorLog } = require("../common/masters");

// module.exports.validateMerchantPartnerMapping = async function (model) {
//     let output = {
//         success: false,
//         message: "Server Error",
//         status: 200,
//     };
//     try {
//         const { error: validateError, response: validateResponse } = validateShopTypeData(model);

//         if (validateError) {
//             output.message = validateError
//         } else {
//             const { merchantCode, partnerCode } = model;
//             const partnerRecord = await PartnerMaster.findOne({ partnerCode }).select({ _id: 1, partnerCode: 1 });
//             if (!partnerRecord) {
//                 output.message = "Invalid partner";
//             } else {

//                 const { _id: partnerId } = partnerRecord;

//                 const today = new Date();
//                 const currentMonth = today.getMonth() + 1;
//                 const currentYear = today.getFullYear();

//                 const merchantRecord = await PartnerTransactions.find({ transactionMonth: currentMonth, transactionYear: currentYear });

//                 if (!merchantRecord) {
//                     output.message = "Invalid merchant";
//                 } else {
//                     output.success = true;
//                     output.message = "Merchant Partner Mapping validation successfully";
//                 }
//             }

//         }

//     } catch (ex) {
//         saveErrorLog("validateMerchantPartnerMapping", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
//         // logger.logs(ex);
//         // output.message = ex.message;
//     }
//     return output;
// }

module.exports.checkIfShopDataExists = async function (model) {
    let output = {
        success: false,
        message: "Server Error",
        status: 200,
    };
    try {
        const { error: validateError, response: validateResponse } = validateShopTypeData(model);

        if (validateError) {
            output.message = validateError
        } else {
            const { merchantCode, partnerCode } = model;
            if (!merchantCode || !partnerCode) {
                output.message = 'Invalid input'
            } else {
                const today = new Date();
                const currentMonth = today.getMonth() + 1;
                const currentYear = today.getFullYear();

                const merchantShopTypeRecord = await MerchantShopType.findOne({ merchantCode, partnerCode, month: currentMonth, year: currentYear });

                if (!merchantShopTypeRecord) {
                    output.message = "Merchant shop type data does not exist";
                } else {
                    output.success = true;

                    output.message = "Merchant shop type data exists";
                }

            }

        }

    } catch (ex) {
        saveErrorLog("checkIfShopDataExists", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        // logger.logs(ex);
        // output.message = ex.message;
    }
    return output;
}

module.exports.saveMerchantShopTypeData = async function (model) {
    let output = {
        success: false,
        message: "Server Error",
        status: 200,
    };
    try {
        const { error: validateError, response: validateResponse } = validateShopTypeData(model);

        if (validateError) {
            output.message = validateError
        } else {
            const { merchantCode, partnerCode, shopType } = model;

            if (!merchantCode || !partnerCode || !shopType) {
                output.message = 'Invalid input'
            } else {
                const today = new Date();
                const currentMonth = today.getMonth() + 1;
                const currentYear = today.getFullYear();

                const merchantShopTypeRecord = await MerchantShopType.findOne({ merchantCode, partnerCode, month: currentMonth, year: currentYear });

                if (merchantShopTypeRecord) {
                    output.message = "Merchant shop type data already exists";
                } else {

                    const partnerRecord = await PartnerMaster.findOne({ partnerCode }).select({ _id: 1, partnerCode: 1 });
                    if (!partnerRecord) {
                        output.message = "Invalid partner";
                    } else {
                        const { _id: partnerId } = partnerRecord;

                        const newMerchantShopTypeRecord = new MerchantShopType({
                            merchantCode, partnerCode, month: currentMonth, year: currentYear, shopType, partnerId
                        });

                        await newMerchantShopTypeRecord.save();

                        output.success = true;
                        output.message = 'Merchant shop type save successfully'
                    }
                }


            }
        }

    } catch (ex) {
        saveErrorLog("checkIfShopDataExists", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        // logger.logs(ex);
        // output.message = ex.message;
    }
    return output;
}

function validateShopTypeData(model) {

    if (model.merchantCode && (typeof model.merchantCode !== 'string'))
        return { error: "Invalid merchant code", response: null }

    if (model.partnerCode && (typeof model.partnerCode !== 'string'))
        return { error: "Invalid partner code", response: null }

    if (model.shopType && (typeof model.shopType !== 'string'))
        return { error: "Invalid shop type", response: null }


    return { error: null, response: {} };
}