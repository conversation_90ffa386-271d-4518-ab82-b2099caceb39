
var json2xls = require('json2xls');
const fs = require("fs");
const moment = require('moment');
const config = require("config");
const debug = require("debug")("app:reports");
const { keyBy, groupBy, orderBy } = require("lodash");
const xlsx = require('xlsx');



const { LoanInfo, LoanInstallments } = require('../../../models/ops/loan/loan');
const { PartnerMaster, PreApprovals } = require('../../../models/ops/partner/partner')
const { MailerConfig } = require("../../../models/ops/common/mailer");
const { UserInfo, UserPartnerInfo } = require("../../../models/ops/registration/users");
const { LoanDisbursmentTransactions } = require("../../../models/ops/loan/disbursal");

const { saveErrorLog } = require("../common/masters");
const { getDMYFormattedDate, round10, getMailModel } = require("../common/common");
const { sendExternalMail } = require("../common/mailer");
const installmentService = require("../loan/installments");
const { DigioMandateGeneration, DigioCollectionSchedule } = require("../../../models/ops/digio/digioApi");

const apiPath = __dirname;
const Excel = require('exceljs');
const Logger = require('../../../middleware/logger');
const logger = new Logger();


async function generateCollectionCasesExcel(body) {

    try {
        generateExcel(body);
    } catch (error) {
        saveErrorLog("generateCollectionCasesExcel", apiPath, {}, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }
    return {
        success: false,
        data: {},
        message: "Generate collection cases excel",
        status: 200
    }
}

async function generateExcel(body) {
    try {

        let overdueBy = body.overdueBy;
        let files = [];
        if (overdueBy && overdueBy.length) {
            for (let i = 0; i < overdueBy.length; i++) {
                let { error, response } = await prepareExcelData(overdueBy[i]);

                if (error) {
                    return { error, response: null }
                }

                if (response)
                    files.push(response);
            }
        }

        if (files.length) {

            const mailerConfig = await MailerConfig.findOne({ name: "collection cases" });
            if (mailerConfig) {
                let mailModel = getMailModel();

                mailModel.isMultipleAttachments = true;
                mailModel.attachments = files;
                mailModel.to = mailerConfig.to;
                mailModel.cc = mailerConfig.cc;
                mailModel.date = moment().format("DD/MM/YYYY")

                if (body && body.to) {
                    mailModel.to = body.to
                    mailModel.cc = null;
                }

                const mailResponse = await sendExternalMail(mailModel, "Collection Cases", "Collection Cases");

                if (mailResponse.success) {
                    files.map(item => {
                        fs.unlink(item.filePath, function () {
                        });
                    })

                }
            } else {
                files.map(item => {
                    fs.unlink(item.filePath, function () {
                    });
                })
            }
        }

    } catch (ex) {
        saveErrorLog("generateExcel", apiPath, {}, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }
}

async function prepareExcelData(overdueBy) {
    try {
        let query1 = {
            status: { $in: ["Loan In Progress", "Written-Off"] },
            isDraft: false
        };
        const defaultDays = config.get("digio.eNach.defaultDays");
        let query2 = {};
        const todayDate = new Date();
        const today = new Date(
            todayDate.getFullYear(),
            todayDate.getMonth(),
            todayDate.getDate(), 5, 30, 0, 0
        );

        let daysFrom = overdueBy.start;
        let daysTo = overdueBy.end;

        let overDueByDays = {}

        if (daysFrom) {
            overDueByDays["$gte"] = daysFrom
        }

        if (daysTo) {
            overDueByDays["$lte"] = daysTo
        }

        let overdueByMatchQuery = {
            overDueByDays
        }

        let aggregateQuery = [{
            $match: {
                ...query1
            }
        },
        {
            $addFields: {
                overDueByDays: {
                    $dateDiff: {
                        startDate: "$nextEmiRepaymentDate",
                        endDate: today,
                        unit: 'day'
                    }
                }
            }
        }, {
            $match: {
                $and: [{
                    overDueByDays: {
                        $gte: defaultDays
                    }
                }, overdueByMatchQuery]
            }
        },
        {
            $project: {
                loanNo: 1,
                userId: 1,
                _id: 1,
                amount: 1,
                purpose: 1,
                maturity: 1,
                interestRate: 1,
                loanType: 1,
                isMaturityMonth: 1,
                insertedOn: 1,
                adminRemarks: 1,
                rejectDescription: 1,
                repaymentFrequency: 1,
                postDisbursalStatus: 1,
                clientRemarks: 1,
                status: 1,
                primaryCollectionType: 1,
                secondaryCollectionType: 1,
                disbursalDetails: 1,
                merchantCode: 1,
                insertedOn: 1,
                updatedOn: 1,
                lenderDetails: 1,
                partnerId: 1,
                overDueByDays: 1,
                pendingList: 1,
                offerId: 1,
                moratoriumInterestOutstanding: 1,
                feeAmountOutstanding: 1,
                totalOutstanding: 1,
                principalOutstandingTillDate: 1,
                interestOutstandingTillDate: 1,
                moratoriumInterestOutstandingTillDate: 1,
                feeAmountOutstandingTillDate: 1,
                pendingInstallmentsTillDate: 1,
                totalMoratoriumInterest: 1,
                totalInstallmentAmount: 1,
                totalOutstandingAmountTillDate: 1,
                pendingInstallments: 1,
                interestAmount: 1,
                principalAmount: 1,
                nextEmiRepaymentDate: 1,
                lastEmiPaidDate: 1,
                flowDetails: 1,
                arrears: 1
            },
        }
        ]

        let batchSize = 1000;

        let aggreateQueryForTotalCount = [...aggregateQuery];
        aggreateQueryForTotalCount[aggreateQueryForTotalCount.length - 1] = { $count: "totalCount" }
        let totalCountRes = await LoanInfo.aggregate(aggreateQueryForTotalCount).allowDiskUse(true)

        if (!totalCountRes || !totalCountRes.length || !totalCountRes[0].totalCount)
            return { error: null, response: null }

        totalCountRes = totalCountRes[0].totalCount

        let totalBatch = Math.ceil(totalCountRes / batchSize);

        /** GENERATE FILE */
        let overdueBucketName = `${overdueBy.start}${overdueBy.end ? '_' + overdueBy.end + ' days' : "and above days"}`;
        const fileName = `collection_cases_${moment().format("DD_MM_YYYY")}_bucket_${overdueBucketName}.xlsx`;
        const filePath = config.get("exportToExcelPath.generalReports") + fileName;
        const options = {
            filename: filePath,
            useStyles: true,
            useSharedStrings: true
        };
        const workbook = new Excel.stream.xlsx.WorkbookWriter(options);
        const worksheet = workbook.addWorksheet('Sheet1');

        worksheet.columns = [
            { header: 'Loan No.', key: 'loanNo' },
            { header: 'Name', key: 'name' },
            { header: 'Mobile Numbers', key: 'mobileNumbers' },
            { header: 'Partner', key: 'partnerName' },
            { header: 'Merchant Code', key: 'merchantCode' },
            { header: 'Status', key: 'status' },
            { header: 'Disbursal Date', key: 'disbursalDate' },
            { header: 'Last Payment Date', key: 'lastPaymentReceivedDate' },
            { header: 'Loan Amount', key: 'amount' },
            { header: 'Principal Outstanding', key: 'totalPrincipalOutstanding' },
            { header: 'Principal Pending', key: 'principalOutstandingTillDate' },
            { header: 'Interest Outstanding', key: 'totalInterestOutstanding' },
            { header: 'Interest Pending', key: 'interestOutstandingTillDate' },
            { header: 'Others Outstanding', key: 'otherCharges' },
            { header: 'Total Outstanding', key: 'totalOutstandingAmount' },
            { header: 'Total Pending', key: 'totalOutstandingAmountTillDate' },
            { header: 'Reason for failure', key: 'failure_reason' },
            { header: 'Bucket', key: 'bucket' },
            { header: 'Lending Partner', key: 'lendingPartner' },
            { header: 'Disbursal Month', key: 'disbursalShortDate' },
            { header: 'No of EMIs pending', key: 'numberOfEmisPending' },
            { header: 'City', key: 'city' },
            { header: 'State', key: 'state' },
            { header: 'Pincode', key: 'pincode' },
            { header: 'Home Address', key: 'homeAddress' },
            { header: 'Shop Address', key: 'shopAddress' },
            { header: 'TrueScore At Disbursal', key: 'trueScoreDisbursal' },
            { header: 'Last Transaction Period', key: 'lastTransactionPeriod' },
            { header: 'Last Transaction Count', key: 'lastTransactionCount' },
            { header: 'Last Transaction Value', key: 'lastTransactionVolume' },
            { header: 'Last Active Days', key: 'lastActiveDays' },
            { header: 'Earliest Pending Emi', key: 'earliestPendingEmi' },
            { header: 'DPD', key: 'dpd' },
            { header: 'Total Interest Amount', key: 'totalInterestAmount' },
            { header: 'Loan Expiry Date', key: 'loanExpiryDate' },
            { header: 'Written-Off Date', key: 'writtenOffDate' },
            { header: 'Principal Outstadning As On WrittenOff', key: 'principalOutstandingOnWrittenOff' }
        ]


        for (let i = 0; i < totalBatch; i++) {
            let { error: dataError, response: data } = await prepareData(aggregateQuery, batchSize, i);

            if (dataError) {
                return { error: dataError, response: null }
            }

            for (let j = 0; j < data.length; j++) {
                worksheet.addRow(data[j]).commit();
            }
        }
        console.log("test")
        await workbook.commit();
        return { error: null, response: { fileName, filePath } }
    }
    catch (ex) {
        logger.logs(ex);
        logger.logs("Error : ", ex);
        saveErrorLog("getPendingEmisLoanWise", apiPath, "", ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        return { error: ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex, response: null }
    }
};

async function prepareData(aggregateQuery, limit, page) {
    try {
        let isAllLoans = true;
        let loans = await LoanInfo.aggregate(aggregateQuery).sort({ insertedOn: -1 }).skip(page * limit).limit(limit).allowDiskUse(true)

        let userIds = []
        let partnerIds = []
        let userPartnerInfoQuery = []
        let offerIdsList = []
        let loanIds = []
        const today = Date.now();

        for (let i = 0; i < loans.length; i++) {
            let loan = loans[i];
            userIds.push(loan.userId)
            partnerIds.push(loan.partnerId)
            userPartnerInfoQuery.push({ userId: loan.userId, partnerId: loan.partnerId })
            offerIdsList.push(loan.offerId)
            loanIds.push(loan._id)
        }

        let usersList = await UserInfo.find({ _id: { $in: userIds } }).lean();
        let partnerMaster = await PartnerMaster.find({ _id: { $in: partnerIds } }).lean();

        let userPartnerInfo = userPartnerInfoQuery && userPartnerInfoQuery.length ? await UserPartnerInfo.find({ $or: userPartnerInfoQuery }).lean() : [];
        let preApprovalList = null;
        if (offerIdsList.length)
            preApprovalList = await PreApprovals.find({ _id: { $in: offerIdsList } });


        let usersHash = {}
        let partnerHash = {}
        let userPartnerInfoHash = {}
        let preApprovalHash = {}


        for (let i = 0; i < usersList.length; i++) {
            let user = usersList[i]
            usersHash[user._id.toString()] = user
        }

        for (let i = 0; i < partnerMaster.length; i++) {
            let partner = partnerMaster[i]
            partnerHash[partner._id.toString()] = partner
        }

        for (let i = 0; i < userPartnerInfo.length; i++) {
            userPartnerInfoHash[userPartnerInfo[i].userId.toString() + '_' + userPartnerInfo[i].partnerId.toString()] = userPartnerInfo[i]
        }

        if (preApprovalList) {
            for (let i = 0; i < preApprovalList.length; i++) {
                preApprovalHash[preApprovalList[i]._id.toString()] = preApprovalList[i]
            }
        }

        let allInstallments = null;

        // if (!isAllLoans)
        allInstallments = await installmentService.getInstallmentsPending(loanIds, null, null, null, true, true, today, null, null, null);

        let allInstallmentsHash = {}
        let installmentHashLoanWise = {}

        if (allInstallments) {

            allInstallmentsHash = groupBy(allInstallments.installments, "loanId");
            installmentHashLoanWise = groupBy(allInstallments.pendingInstallments, "loanId");
            // for (let i = 0; i < allInstallments.data.length; i++) {
            //     allInstallmentsHash[allInstallments.data[i].loanId.toString()] = allInstallments.data[i];
            // }

            // if (allInstallments && allInstallments.pendingInstallments) {
            //     for (let i = 0; i < allInstallments.pendingInstallments.length; i++) {

            //         if (installmentHashLoanWise[allInstallments.pendingInstallments[i].loanId.toString()]) {
            //             installmentHashLoanWise[allInstallments.pendingInstallments[i].loanId.toString()].push(allInstallments.pendingInstallments[i])
            //         } else {
            //             installmentHashLoanWise[allInstallments.pendingInstallments[i].loanId.toString()] = [allInstallments.pendingInstallments[i]]
            //         }
            //     }
            // }
        }

        let loanList = [];


        let mandateHash = {}

        if (!isAllLoans) {
            const mandateRes = await DigioMandateGeneration.find({ loanIds: { $in: loanIds }, isActive: true, status: "register_success" }).lean();

            for (let i = 0; i < mandateRes.length; i++) {

                for (let j = 0; j < mandateRes[i].loanIds.length; j++) {
                    mandateHash[mandateRes[i].loanIds[j].toString()] = mandateRes[i]
                }
            }
        }

        let digioAggregateQuery = [{
            $match: {
                collectionType: 'Bulk',
                loanId: { $in: loanIds }
            }
        }, {
            $sort: {
                insertedOn: -1
            }
        }, {
            $group: {
                _id: '$loanId',
                schedule: {
                    $first: '$$ROOT'
                }
            }
        }]

        let digioScheduleResHash = {}

        const digioScheduleRes = await DigioCollectionSchedule.aggregate(digioAggregateQuery).allowDiskUse(true)

        if (digioScheduleRes) {
            for (let i = 0; i < digioScheduleRes.length; i++) {
                digioScheduleResHash[digioScheduleRes[i]._id.toString()] = digioScheduleRes[i]
            }
        }

        let digioCollectionSchedule = [{
            $match: {
                loanId: { $in: loanIds },
                sendingState: "Send"
            }
        },
        {
            $group: {
                _id: '$loanId',
                count: {
                    $sum: 1
                }
            }
        }]

        let checkExistingResHash = {}

        if (!isAllLoans) {
            const checkExistingRes = await DigioCollectionSchedule.aggregate(digioCollectionSchedule).allowDiskUse(true)

            if (checkExistingRes) {
                for (let i = 0; i < checkExistingRes.length; i++) {
                    checkExistingResHash[checkExistingRes[i]._id.toString()] = checkExistingRes[i]
                }
            }
        }

        for (const loan of loans) {

            let pendingList = installmentHashLoanWise[loan._id.toString()] && installmentHashLoanWise[loan._id.toString()].length ? installmentHashLoanWise[loan._id.toString()] : []
            loan.userDetails = usersHash[loan.userId.toString()] ? usersHash[loan.userId.toString()].userDetails : {}
            loan.user = usersHash[loan.userId.toString()] ? usersHash[loan.userId.toString()] : {}
            loan.partnerMaster = partnerHash[loan.partnerId.toString()] ? partnerHash[loan.partnerId.toString()] : {}
            loan.addressDetails = usersHash[loan.userId.toString()] ? usersHash[loan.userId.toString()].addressDetails : {}
            loan.partnerName = loan.partnerMaster && loan.partnerMaster.displayName;

            const userPartnerInfo = userPartnerInfoHash[loan.userId.toString() + '_' + loan.partnerId.toString()];
            const preApproval = loan.offerId ? preApprovalHash[loan.offerId.toString()] : ""

            if (userPartnerInfo && userPartnerInfo.userMetrices) {

                const userMetrics = userPartnerInfo.userMetrices;

                if (userMetrics) {
                    loan.lastTransactionPeriod = (userMetrics.transactionMonth && userMetrics.transactionYear) ? (userMetrics.transactionMonth < 10 ? ('0' + userMetrics.transactionMonth) : userMetrics.transactionMonth) + '/' + userMetrics.transactionYear : "";
                    loan.lastTransactionCount = userMetrics.transactionCount;
                    loan.lastTransactionVolume = userMetrics.transactionValue;
                    loan.lastActiveDays = userMetrics.activeDays;
                }
            }

            if (preApproval) {
                loan.trueScoreDisbursal = preApproval.trueScore ? preApproval.trueScore : ""
            }

            let holdFlag = true;

            if (pendingList.length > 0 || isAllLoans) {

                loan.earliestPendingEmi = loan.nextEmiRepaymentDate || pendingList.length && pendingList[0].dueDate;

                const overDueByDays = loan.overDueByDays;

                const lenderId = loan.lenderDetails && loan.lenderDetails[0] && loan.lenderDetails[0].lenderPartnerId;
                let loanMeta = allInstallmentsHash[loan._id.toString()];

                if (!loanMeta && !isAllLoans) {
                    return {
                        data: [],
                        success: false,
                        message: "",
                        totalPages: 0
                    }
                }

                loanMeta = orderBy(loanMeta, ["installmentNumber"], ["desc"]);

                let loanExpiryDate = loanMeta[0].dueDate;
                loanExpiryDate = getDMYFormattedDate(loanExpiryDate, "/");

                let writtenOffDate = "";
                let principalOutstandingOnWrittenOff = "";

                if (loan.status == "Written-Off") {
                    const flowdetails = loan.flowDetails.filter((x) => x.Step == "Written-Off");
                    if (flowdetails != null && flowdetails.length > 0) {
                        writtenOffDate = getDMYFormattedDate(flowdetails[0].insertedOn, "/");
                    }

                    const arrears = loan.arrears.filter((x) => x.delinquency == "Write-Off");
                    if (arrears != null && arrears.length > 0) {
                        principalOutstandingOnWrittenOff = arrears[0].amount;
                    }
                }

                let bucket = "";

                if (overDueByDays > 0 && overDueByDays <= 7)
                    bucket = "1 - 7 days";
                if (overDueByDays > 7 && overDueByDays <= 15)
                    bucket = "8 - 15 days";
                if (overDueByDays > 15 && overDueByDays <= 30)
                    bucket = "16 - 30 days";
                if (overDueByDays > 30 && overDueByDays <= 60)
                    bucket = "31 - 60 days";
                if (overDueByDays > 60 && overDueByDays <= 90)
                    bucket = "61 - 90 days";
                if (overDueByDays > 90 && overDueByDays <= 120)
                    bucket = "91 - 120 days";
                if (overDueByDays > 120)
                    bucket = "> 120 days";

                let lendingPartner = "";

                if (lenderId) {
                    const lendingPartnerMaster = await PartnerMaster.findById(lenderId);

                    if (lendingPartnerMaster)
                        lendingPartner = lendingPartnerMaster.partnerName ? lendingPartnerMaster.partnerName : "";
                }

                let fromEmi = 0;
                let toEmi = 0;

                let emiList = [];
                let emiNumberList = [];

                let i = 1;

                for (const pendingItem of pendingList) {
                    if (i === 1)
                        fromEmi = pendingItem.installmentNumber;

                    emiList.push(i);
                    emiNumberList.push(pendingItem.installmentNumber);
                    toEmi = pendingItem.installmentNumber;

                    if (!pendingItem.holdCollectionViaWallet)
                        holdFlag = false;

                    i++;
                }

                let nachActionDisabled = false;
                let failure_reason = "";

                let mandateStatus = "";

                if (loan.primaryCollectionType === "eNach" || loan.secondaryCollectionType === "eNach") {

                    const mandate = mandateHash[loan._id.toString()]

                    if (mandate)
                        mandateStatus = mandate.status;

                    const schedule = digioScheduleResHash[loan._id.toString()]

                    if (schedule && schedule.schedule && schedule.schedule.sendingState !== "Paid")
                        failure_reason = schedule.schedule.failure_description ? schedule.schedule.failure_description : "";

                    const checkExisting = checkExistingResHash[loan._id.toString()];

                    if (checkExisting && checkExisting.count)
                        nachActionDisabled = true;

                }

                const disbursalDate = loan.disbursalDetails && loan.disbursalDetails.disbursedOn ? getDMYFormattedDate(loan.disbursalDetails.disbursedOn, "/") : "";

                const disbursalShortDate = loan.disbursalDetails && loan.disbursalDetails.disbursedOn ? moment(loan.disbursalDetails.disbursedOn).format('MMM YY') : "";

                let mobileNumberList = loan && loan.userDetails && loan.userDetails.mobileNo && loan.userDetails.mobileNo.map(item => item.mobileNumber);
                if (mobileNumberList)
                    mobileNumberList = mobileNumberList.join();

                let homeAddress = "";
                let shopAddress = "";

                if (loan.user && loan.user.addressDetails && loan.user.addressDetails.length) {
                    let addressDetails = loan.user.addressDetails[0];

                    if (addressDetails.add1)
                        homeAddress += `${addressDetails.add1},`;

                    if (addressDetails.add2)
                        homeAddress += ` ${addressDetails.add2},`;

                    if (addressDetails.add3)
                        homeAddress += ` ${addressDetails.add3},`;

                    if (addressDetails.city)
                        homeAddress += ` ${addressDetails.city},`;

                    if (addressDetails.state)
                        homeAddress += ` ${addressDetails.state},`;

                    if (addressDetails.country)
                        homeAddress += ` ${addressDetails.country},`;

                    if (addressDetails.pincode)
                        homeAddress += ` ${addressDetails.pincode}`;

                }

                if (loan.user && loan.user.shopDetails && loan.user.shopDetails.length) {
                    let shopDetails = loan.user.shopDetails[0];

                    if (shopDetails.shopAddress)
                        shopAddress += ` ${shopDetails.shopAddress},`;

                    if (shopDetails.city)
                        shopAddress += ` ${shopDetails.city},`;

                    if (shopDetails.state)
                        shopAddress += ` ${shopDetails.state},`;

                    if (shopDetails.pincode)
                        shopAddress += ` ${shopDetails.pincode}`;
                }

                homeAddress = homeAddress.trim()
                shopAddress = shopAddress.trim()

                let obj = {
                    loanId: loan._id,
                    loanNo: loan.loanNo,
                    amount: loan.amount,
                    name: loan.userDetails.firstName + " " + loan.userDetails.lastName,
                    failure_reason: failure_reason,
                    disabled: true,
                    eNachStatus: "",
                    nachActionDisabled: nachActionDisabled,
                    nachBtnDisabled: false,
                    preLegalNoticeBtnDisabled: false,
                    primaryCollectionType: loan.primaryCollectionType,
                    secondaryCollectionType: loan.secondaryCollectionType,
                    totalPrincipalOutstanding: Math.round(((loan.principalAmount && loan.principalAmount.outstanding) + Number.EPSILON) * 100) / 100,
                    totalInterestOutstanding: Math.round(((loan.interestAmount && loan.interestAmount.outstanding) + Number.EPSILON) * 100) / 100,
                    totalLateFeeOutstanding: Math.round(((loan.feeAmountOutstanding && loan.feeAmountOutstanding) + Number.EPSILON) * 100) / 100,
                    totalOutstandingAmount: Math.round((loan.totalOutstanding + Number.EPSILON) * 100) / 100,
                    merchantCode: loan.merchantCode ? loan.merchantCode : "",
                    partnerName: loan.partnerName ? loan.partnerName : "",
                    mobileNo: loan.userDetails.mobileNo && loan.userDetails.mobileNo[0] ? loan.userDetails.mobileNo[0].mobileNumber : "", //mobile number change
                    disbursalDate,
                    lastPaymentReceivedDate: loan.lastEmiPaidDate ? getDMYFormattedDate(loan.lastEmiPaidDate, "/") : "",
                    otherCharges: Math.round((loan.feeAmountOutstanding + Number.EPSILON) * 100) / 100 + Math.round((loan.moratoriumInterestOutstanding + Number.EPSILON) * 100) / 100,
                    insertedOn: loan.insertedOn,
                    updatedOn: loan.updatedOn,
                    fromEmi,
                    toEmi,
                    emiList,
                    holdWalletCollection: holdFlag,
                    emiCount: "",
                    isMandateRegistered: mandateStatus === "register_success" ? true : false,
                    lendingPartner,
                    bucket,
                    disbursalShortDate,
                    status: loan.status,
                    principalOutstandingTillDate: Math.round(((loan && loan.principalOutstandingTillDate) + Number.EPSILON) * 100) / 100,
                    interestOutstandingTillDate: Math.round(((loan && loan.interestOutstandingTillDate) + Number.EPSILON) * 100) / 100,
                    totalOutstandingAmountTillDate: Math.round((loan && loan.totalOutstandingAmountTillDate + Number.EPSILON) * 100) / 100,
                    numberOfEmisPending: loan && loan.pendingInstallments,
                    mobileNumbers: mobileNumberList,
                    lastTransactionPeriod: loan.lastTransactionPeriod || "-",
                    lastTransactionCount: loan.lastTransactionCount || "-",
                    lastTransactionVolume: loan.lastTransactionVolume || "-",
                    lastActiveDays: loan.lastActiveDays || "-",
                    trueScoreDisbursal: loan.trueScoreDisbursal || "-",
                    earliestPendingEmi: loan.earliestPendingEmi,
                    homeAddress,
                    shopAddress,
                    totalInterestAmount: Math.round(((loan.interestAmount && loan.interestAmount.outstanding) + (loan.interestAmount && loan.interestAmount.collected) + Number.EPSILON) * 100) / 100,
                    loanExpiryDate,
                    writtenOffDate,
                    principalOutstandingOnWrittenOff
                }

                if (obj.lastPaymentReceivedDate) {
                    let daysSplit = obj.lastPaymentReceivedDate.split('/')
                    let dpd = getNumberOfDaysDifference(daysSplit[1] + '/' + daysSplit[0] + '/' + daysSplit[2])
                    obj["dpd"] = dpd;
                }

                if (loan.addressDetails && loan.addressDetails.length) {
                    obj = {
                        ...obj,
                        pincode: loan.addressDetails[0].pincode,
                        city: loan.addressDetails[0].city,
                        state: loan.addressDetails[0].state
                    }
                }

                loanList.push(obj);
            }
        }

        return { error: null, response: loanList };
    } catch (ex) {
        console.log(ex);
        //saveErrorLog("prepareData", apiPath, "", ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        return { error: ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex, response: null }
    }
}

getNumberOfDaysDifference = (date) => {
    let date1 = new Date(date);
    let date2 = new Date(new Date().toDateString());
    let differenceInTime = date2.getTime() - date1.getTime();
    let differenceInDays = differenceInTime / (1000 * 3600 * 24);

    return '' + differenceInDays
}

module.exports = {
    generateCollectionCasesExcel
}