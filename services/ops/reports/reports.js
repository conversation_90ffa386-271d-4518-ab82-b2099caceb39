
var json2xls = require('json2xls');
const fs = require("fs");
const moment = require('moment');
const config = require("config");
const debug = require("debug")("app:reports");
const { keyBy, groupBy } = require("lodash");
const mongoose = require("mongoose");
const { LoanInfo, LoanInstallments } = require('../../../models/ops/loan/loan');
const { PartnerMaster } = require('../../../models/ops/partner/partner')
const { MailerConfig } = require("../../../models/ops/common/mailer");
const { UserInfo } = require("../../../models/ops/registration/users");
const { LoanDisbursmentTransactions } = require("../../../models/ops/loan/disbursal");
const { saveErrorLog } = require("../common/masters");
const { getDMYFormattedDate, round10, getMailModel } = require("../common/common");
const { sendExternalMail } = require("../common/mailer");
const installmentService = require("../loan/installments");
const { mapper } = require('../disbursal/mappers/mapper');
const { Constants } = require('../common/constants');
const XLSX = require('xlsx');
const apiPath = __dirname;
const { dateAdd } = require("dateadd");
const Logger = require('../../../middleware/logger');
const logger = new Logger();

async function generateReport(date) {

    try {
        let monthStart = new Date(moment(date).startOf('month'))
        let monthEnd = new Date(moment(date).endOf('month'))

        const todayDate = new Date();

        const today = new Date(
            todayDate.getFullYear(),
            todayDate.getMonth(),
            todayDate.getDate(), 5, 30, 0, 0
        );

        let aggregateQuery = [{
            $match: {
                'disbursalDetails.disbursedOn': {
                    $gt: monthStart,
                    $lt: monthEnd
                }
            }
        }, {
            $group: {
                _id: '$partnerId',
                totalAmount: {
                    $sum: '$amount'
                },
                totalCount: {
                    $sum: 1
                }
            }
        }]

        let loanInfoMeta = await LoanInfo.aggregate(aggregateQuery); /** NO LOAN STATUS CONSIDRED - DATA BASED ON ONLY DISBUSED ON DATE - (ALL PARTNERS) */

        let loanMetaHash = {}

        for (let i = 0; i < loanInfoMeta.length; i++) {
            loanMetaHash[loanInfoMeta[i]._id.toString()] = loanInfoMeta[i]
        }

        let partners = await PartnerMaster.find({ partnerType: "Borrowing Partner", isActive: true, partnerCode: { $nin: ["FUND", "PEassy"] } }).lean()

        let partnerData = []

        let totalPreviousPrincipleOutStanding = 0;

        let totalRow = {
            "Month": "Total",
            "Partner": "",
            "Event Default Rate": 0,
            "Event Default Percentage": 0,
            "Total Principal Default Rate": 0,
            "Total Principal Default Percentage": 0,
            "Outstanding Default Rate": 0,
            "Outstanding Default Percentage": 0,
            "Total Disbursement Count": 0,
            "Total Disbursement Amount": 0,
            "Change in Total Outstanding from last Week": 0,
            "Percentage Change compared to last week": 0,
            "Percentage Change compared to total disbursement": 0,
            "Num Delinq": 0,
            "Amt Delinq": 0,
            "Prin Delinq": 0,
            "Num Def": 0,
            "Amt Def": 0,
            "Prin Def": 0,
            "Num Writ": 0,
            "Amt Writ": 0,
            "Prin Writ": 0
        }

        for (let i = 0; i < partners.length; i++) {

            let columns = {
                "Month": moment(date).format('MMMM-YY'),
                "Partner": "",
                "Event Default Rate": 0,
                "Event Default Percentage": 0,
                "Total Principal Default Rate": 0,
                "Total Principal Default Percentage": 0,
                "Outstanding Default Rate": 0,
                "Outstanding Default Percentage": 0,
                "Total Disbursement Count": 0,
                "Total Disbursement Amount": 0,
                "Change in Total Outstanding from last Week": 0,
                "Percentage Change compared to last week": "0",
                "Percentage Change compared to total disbursement": 0,
                "Num Delinq": 0,
                "Amt Delinq": 0,
                "Prin Delinq": 0,
                "Num Def": 0,
                "Amt Def": 0,
                "Prin Def": 0,
                "Num Writ": 0,
                "Amt Writ": 0,
                "Prin Writ": 0
            }


            let result = await getPartnerLevelData(partners[i]._id, monthStart, monthEnd, today);
            let arreasResult = await getArrearsPartnerLevelData(partners[i]._id, monthStart, monthEnd);

            columns["Partner"] = partners[i].displayName;

            if (loanMetaHash[partners[i]._id]) {
                columns["Total Disbursement Count"] = loanMetaHash[partners[i]._id].totalCount;
                columns["Total Disbursement Amount"] = loanMetaHash[partners[i]._id].totalAmount;

                //Total
                totalRow["Total Disbursement Count"] += +loanMetaHash[partners[i]._id].totalCount;
                totalRow["Total Disbursement Amount"] += +loanMetaHash[partners[i]._id].totalAmount
            }

            if (result) {

                columns["Event Default Rate"] = result.outStandingLoanCount;
                columns["Total Principal Default Rate"] = result.totalAmount;
                columns["Outstanding Default Rate"] = result.totalPrincipleOutStanding;
                columns["Change in Total Outstanding from last Week"] = toFixedCustom(result.principleDifference);

                //Total
                totalRow["Event Default Rate"] += +result.outStandingLoanCount;
                totalRow["Total Principal Default Rate"] += +result.totalAmount;
                totalRow["Outstanding Default Rate"] += result.totalPrincipleOutStanding;
                totalRow["Change in Total Outstanding from last Week"] += toFixedCustom(result.principleDifference);

                if (result.principleDifference && result.previousPrincipleOutStanding) {
                    columns["Percentage Change compared to last week"] = toFixedCustom((result.principleDifference / result.previousPrincipleOutStanding) * 100) + '%';
                }

                if (result.previousPrincipleOutStanding)
                    totalPreviousPrincipleOutStanding += result.previousPrincipleOutStanding;

                if (result.principleDifference && columns["Total Disbursement Amount"]) {
                    columns["Percentage Change compared to total disbursement"] = toFixedCustom((result.principleDifference / columns["Total Disbursement Amount"]) * 100) + '%'
                }

                columns["Event Default Percentage"] = toFixedCustom(((result.outStandingLoanCount / columns["Total Disbursement Count"]) * 100) || 0) + '%';
                columns["Total Principal Default Percentage"] = toFixedCustom(((result.totalAmount / columns["Total Disbursement Amount"]) * 100) || 0) + '%';
                columns["Outstanding Default Percentage"] = toFixedCustom(((result.totalPrincipleOutStanding / columns["Total Disbursement Amount"]) * 100) || 0) + '%';
            }

            if (arreasResult) {
                columns["Num Delinq"] = arreasResult.numDelinq
                columns["Amt Delinq"] = toFixedCustom(arreasResult.amountDelinq)
                columns["Prin Delinq"] = toFixedCustom(arreasResult.prinDelinq)
                columns["Num Def"] = arreasResult.numDefault
                columns["Amt Def"] = toFixedCustom(arreasResult.amountDefault)
                columns["Prin Def"] = toFixedCustom(arreasResult.prinDefault)
                columns["Num Writ"] = arreasResult.numWriteOff
                columns["Amt Writ"] = toFixedCustom(arreasResult.amountWriteOff)
                columns["Prin Writ"] = toFixedCustom(arreasResult.prinWriteOff)

                //Total
                totalRow["Num Delinq"] += arreasResult.numDelinq
                totalRow["Amt Delinq"] += toFixedCustom(arreasResult.amountDelinq)
                totalRow["Prin Delinq"] += toFixedCustom(arreasResult.prinDelinq)
                totalRow["Num Def"] += arreasResult.numDefault
                totalRow["Amt Def"] += toFixedCustom(arreasResult.amountDefault)
                totalRow["Prin Def"] += toFixedCustom(arreasResult.prinDefault)
                totalRow["Num Writ"] += arreasResult.numWriteOff
                totalRow["Amt Writ"] += toFixedCustom(arreasResult.amountWriteOff)
                totalRow["Prin Writ"] += toFixedCustom(arreasResult.prinWriteOff)

            }

            //Total
            totalRow["Percentage Change compared to last week"] = toFixedCustom(totalPreviousPrincipleOutStanding && ((totalRow["Change in Total Outstanding from last Week"] / totalPreviousPrincipleOutStanding) * 100) || 0) + '%';
            totalRow["Percentage Change compared to total disbursement"] = toFixedCustom(((totalRow["Change in Total Outstanding from last Week"] / totalRow["Total Disbursement Amount"]) * 100) || 0) + '%'
            totalRow["Event Default Percentage"] = toFixedCustom(((totalRow["Event Default Rate"] / totalRow["Total Disbursement Count"]) * 100) || 0) + '%';
            totalRow["Total Principal Default Percentage"] = toFixedCustom(((totalRow["Total Principal Default Rate"] / totalRow["Total Disbursement Amount"]) * 100) || 0) + '%';
            totalRow["Outstanding Default Percentage"] = toFixedCustom(((totalRow["Outstanding Default Rate"] / totalRow["Total Disbursement Amount"]) * 100) || 0) + '%';

            if (result || arreasResult || loanMetaHash[partners[i]._id])
                partnerData.push(columns)
        }

        partnerData.push(totalRow)


        return partnerData;

    } catch (ex) {
        saveErrorLog("generateReport", apiPath, {}, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }
};

async function getPartnerLevelData(partnerId, startDate, endDate, today) {

    try {
        let defaultLoansAggregate = [{
            $match: {
                partnerId: partnerId,
                status: {
                    $in: [
                        'Loan In Progress',
                        'Written-Off'
                    ]
                },
                'disbursalDetails.disbursedOn': {
                    $gt: startDate,
                    $lt: endDate
                }
            }
        }, {
            $addFields: {
                daysDifference: {
                    $dateDiff: {
                        startDate: '$nextEmiRepaymentDate',
                        endDate: today,
                        unit: 'day'
                    }
                }
            }
        }, {
            $match: {
                daysDifference: {
                    $gte: 7
                }
            }
        }, {
            $group: {
                _id: '$partnerId',
                principleOutStanding: {
                    $sum: "$principalAmount.outstanding"
                },
                totalAmount: {
                    $sum: '$amount'
                },
                loanIds: {
                    $push: '$_id'
                }
            }
        }]

        let result = await LoanInfo.aggregate(defaultLoansAggregate)

        if (!result.length)
            return null;

        let dateBefore = new Date(today)
        dateBefore = new Date(dateBefore.setDate(new Date().getDate() - 7))

        let loanInstallments = await LoanInstallments.aggregate([
            {
                $match: {
                    loanId: { $in: result[0].loanIds },
                    $or: [{ status: "Pending" }, { transactionDate: { $gte: dateBefore } }]
                }
            },
            {
                $group: {
                    _id: null,
                    totalPrincipleOutStanding: {
                        $sum: "$installmentPrincipal"
                    }
                }
            }
        ])

        let resultObj = {
            outStandingLoanCount: result.length ? result[0].loanIds.length : 0,
            totalAmount: result.length ? result[0].totalAmount : 0,
            totalPrincipleOutStanding: result.length ? result[0].principleOutStanding : 0,
            principleDifference: (loanInstallments.length ? loanInstallments[0].totalPrincipleOutStanding : 0) - (result.length ? result[0].principleOutStanding : 0),
            previousPrincipleOutStanding: (loanInstallments.length ? loanInstallments[0].totalPrincipleOutStanding : 0),
            firstPercentageValue: 0,
            secondPercentageValue: 0
        }

        return resultObj;
    } catch (ex) {
        saveErrorLog("getPartnerLevelData", apiPath, {}, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }

};

async function getArrearsPartnerLevelData(partnerId, startDate, endDate) {

    try {
        let loans = await LoanInfo.aggregate([
            {
                $match: {
                    partnerId,
                    'disbursalDetails.disbursedOn': {
                        $gt: startDate,
                        $lt: endDate
                    },
                    "arrears.0": { $exists: true },
                    "arrears.delinquency": { $in: ["Delinquent", "Default", "Write-Off"] }
                }
            },
            {
                $project: {
                    arrears: 1,
                    interestRate: 1,
                    amount: 1,
                    tenure: 1
                }
            }
        ])

        if (!loans.length)
            return null

        let resultObj = {
            numDelinq: 0,
            amountDelinq: 0,
            prinDelinq: 0,
            numDefault: 0,
            amountDefault: 0,
            prinDefault: 0,
            numWriteOff: 0,
            amountWriteOff: 0,
            prinWriteOff: 0
        }

        for (let i = 0; i < loans.length; i++) {

            let loan = loans[i]
            let isDelinquentAccurs = false;
            let isDefaultAccurs = false;
            let isWriteOffAccurs = false;

            for (let j = 0; j < loan.arrears.length; j++) {

                let arrear = loan.arrears[j]

                if (arrear.delinquency == "Delinquent" && !isDelinquentAccurs) {
                    resultObj.numDelinq += 1;
                    resultObj.amountDelinq += arrear.amount * (1 + (loan.interestRate / 100) * loan.tenure / 365);
                    resultObj.prinDelinq += arrear.amount;
                    isDelinquentAccurs = true
                }

                if (arrear.delinquency == "Default" && !isDefaultAccurs) {
                    resultObj.numDefault += 1;
                    resultObj.amountDefault += arrear.amount * (1 + (loan.interestRate / 100) * loan.tenure / 365);
                    resultObj.prinDefault += arrear.amount;
                    isDefaultAccurs = true
                }

                if (arrear.delinquency == "Write-Off" && !isWriteOffAccurs) {
                    resultObj.numWriteOff += 1;
                    resultObj.amountWriteOff += arrear.amount * (1 + (loan.interestRate / 100) * loan.tenure / 365);
                    resultObj.prinWriteOff += arrear.amount;
                    isWriteOffAccurs = true
                }
            }
        }

        return resultObj;
    } catch (ex) {
        saveErrorLog("getArrearsPartnerLevelData", apiPath, {}, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }
};

async function generateCollectionAnalysis(body) {

    try {

        let monthsToAddYear = ["January", "February", "March"]
        let currentMonth = moment().format('MMMM')

        let date = new Date()
        let lastYear = date.getFullYear() - (monthsToAddYear.includes(currentMonth) ? 2 : 1)

        let startDate = new Date(`04/01/${lastYear}`);
        let endDate = new Date();

        let noOfMonthDifference = monthDiff(startDate, endDate) + 1;
        let reportData = []

        for (let i = 0; i < noOfMonthDifference; i++) {

            let date = new Date(new Date(startDate).setMonth(startDate.getMonth() + i))

            let result = await generateReport(date)

            if (result) {
                reportData = reportData.concat(result);
                reportData = reportData.concat([{}, {}])
            }

        }

        var xls = json2xls(reportData);

        const fileName = "weekly_collections_analysis_" + moment().format("DD_MM_YYYY") + ".xlsx";
        const filePath = config.get("exportToExcelPath.generalReports") + fileName;
        fs.writeFileSync(filePath, xls, 'binary');

        const mailerConfig = await MailerConfig.findOne({ name: "Weekly Collection Analysis Report" })

        if (mailerConfig) {
            let mailModel = getMailModel();

            mailModel.attachmentName = fileName
            mailModel.attachmentPath = filePath
            mailModel.to = mailerConfig.to
            mailModel.cc = mailerConfig.cc
            mailModel.date = moment().format("DD/MM/YYYY")

            if (body && body.to) {
                mailModel.to = body.to
            }

            const mailResponse = await sendExternalMail(mailModel, "Weekly Collection Analysis", "Weekly Collection Analysis Report");

            if (mailResponse.success) {
                fs.unlink(filePath, function () {
                });
            }
        }

        return {
            success: true,
            data: [],
            message: "Success",
            status: 200
        }
    } catch (ex) {
        saveErrorLog("generateCollectionAnalysis", apiPath, {}, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        return {
            success: false,
            data: {},
            message: "Some Error Occured",
            status: 200
        }

    }
};

function monthDiff(dateFrom, dateTo) {
    return dateTo.getMonth() - dateFrom.getMonth() +
        (12 * (dateTo.getFullYear() - dateFrom.getFullYear()))
};

function toFixedCustom(n) {
    if (Number(n) === n && n % 1 !== 0) { // Is float
        return +n.toFixed(2)
    } else {
        return n
    }
};

async function setDates(model) {
    let output = {
        data: null,
        success: false,
        message: "Some Error Occurred",
        status: 200,
    };
    try {
        let fromDate = new Date(Date.now());
        let toDate = new Date(Date.now());
        let isEmailSend = model.isEmailSend;
        const millisecondsIn7Days = 604800000;

        const currentYear = fromDate.getFullYear();
        const currentMonth = fromDate.getMonth();
        const previousMonth = currentMonth == 0 ? 11 : currentMonth - 1;
        const previousYear = currentMonth == 0 ? currentYear - 1 : currentYear;

        if (model.type) {
            switch (model.type.toLowerCase()) {
                case "w1":
                    fromDate = new Date(currentYear, currentMonth, 1, 0, 0, 0, 0);
                    toDate = new Date(currentYear, currentMonth, 7, 23, 59, 59, 459);
                    break;
                case "w2":
                    fromDate = new Date(currentYear, currentMonth, 8, 0, 0, 0, 0);
                    toDate = new Date(currentYear, currentMonth, 15, 23, 59, 59, 459);
                    break;
                case "w3":
                    fromDate = new Date(currentYear, currentMonth, 16, 0, 0, 0, 0);
                    toDate = new Date(currentYear, currentMonth, 23, 23, 59, 59, 459);
                    break;
                case "w4":
                    fromDate = new Date(previousYear, previousMonth, 24, 0, 0, 0, 0);
                    toDate = new Date(previousYear, previousMonth + 1, 0, 23, 59, 59, 459);
                    break;
                case "weekly":
                    fromDate = new Date(fromDate.getTime() - millisecondsIn7Days);
                    fromDate = new Date(fromDate.getFullYear(), fromDate.getMonth(), fromDate.getDate(), 0, 0, 0, 0);
                    toDate = new Date(toDate.getFullYear(), toDate.getMonth(), toDate.getDate(), 23, 59, 59, 459);
                    break;
                case "daily":
                    toDate = new Date(currentYear, currentMonth, fromDate.getDate(), 0, 0, 0, 0);
                    fromDate = dateAdd("day", -1, toDate);
                    break;
                default://Monthly
                    fromDate = new Date(previousYear, previousMonth, 1, 0, 0, 0, 0);
                    toDate = new Date(previousYear, previousMonth + 1, 0, 23, 59, 59, 459);
                    break;
            }
        }

        if (model.fromDate) {
            fromDate = new Date(model.fromDate);
            fromDate = new Date(fromDate.getFullYear(), fromDate.getMonth(), fromDate.getDate(), 0, 0, 0, 0);
        }

        if (model.toDate) {
            toDate = new Date(model.toDate);
            toDate = new Date(toDate.getFullYear(), toDate.getMonth(), toDate.getDate(), 23, 59, 59, 459);
        }

        if (isEmailSend == undefined) isEmailSend = true;

        output = {
            data: {
                fromDate,
                toDate,
                isEmailSend
            },
            success: true,
            message: "Fetehced Successfully",
            status: 200,
        };

    } catch (ex) {
        logger.logs(ex);
        saveErrorLog("setDates", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }

    return output;
};

module.exports = {
    generateCollectionAnalysis
};

module.exports.exportWrittenOffCollectionReport = async function (model) {
    let output = {
        success: false,
        message: "Some Error Occurred",
        status: 200,
    };

    try {
        const limit = 100;
        let fromDate = new Date(Date.now());
        let toDate = new Date(Date.now());
        let isEmailSend = model.isEmailSend;

        const currentYear = fromDate.getFullYear();
        const currentMonth = fromDate.getMonth();
        const previousMonth = currentMonth == 0 ? 11 : currentMonth - 1;
        const previousYear = currentMonth == 0 ? currentYear - 1 : currentYear;

        if (model.fromDate) {
            fromDate = new Date(model.fromDate);
            fromDate = new Date(fromDate.getFullYear(), fromDate.getMonth(), fromDate.getDate(), 0, 0, 0, 0);
        } else {
            fromDate = new Date(previousYear, previousMonth, 1, 0, 0, 0, 0);
        }

        if (model.toDate) {
            toDate = new Date(model.toDate);
            toDate = new Date(toDate.getFullYear(), toDate.getMonth(), toDate.getDate(), 23, 59, 59, 459);
        } else {
            toDate = new Date(previousYear, previousMonth + 1, 0, 23, 59, 59, 459);
        }

        let query = { status: { $in: ["Written-Off", "WrittenOff Paid"] } };

        const totalLoans = await LoanInfo.countDocuments(query);
        let totalPages = parseInt(totalLoans / limit);
        let skip = 0;
        let records = [];

        for (let page = 0; page < (totalPages + 1); page++) {
            skip = page * limit;

            const loans = await LoanInfo.aggregate([{ $match: query },
            { $unwind: "$flowDetails" },
            { $match: { "flowDetails.Step": "Written-Off" } },
            { $project: { loanNo: 1, "writtenOffDate": "$flowDetails.insertedOn" } },
            { $sort: { loanNo: 1 } }, { $skip: skip }, { $limit: limit }
            ]).allowDiskUse(true);

            //logger.logs("loans", loans);
            const uniqueLoans = loans.map((x) => x.loanNo);

            const loanDetails = await LoanInfo.find({ loanNo: { $in: uniqueLoans } }).select({ _id: 1, loanNo: 1, lenderDetails: 1 });

            const uniqueLenders = loanDetails.map((x) => x.lenderDetails[0].lenderPartnerId);

            const partners = await PartnerMaster.find({ _id: { $in: uniqueLenders } }).select({ _id: 1, displayName: 1 });

            const lenderPartnerHash = groupBy(partners, "_id");
            const loanDetailsHash = groupBy(loanDetails, "loanNo");

            for (const loan of loans) {
                //logger.logs("loan", loan);
                const lenderDetails = loanDetailsHash[loan.loanNo][0];
                //logger.logs("lenderDetails", lenderDetails);
                const loanLendingPartner = lenderPartnerHash[lenderDetails.lenderDetails[0].lenderPartnerId][0];
                //logger.logs("loanLendingPartner", loanLendingPartner);

                const checkFromDate = fromDate > loan.writtenOffDate ? fromDate : loan.writtenOffDate;
                // logger.logs("fromDate", fromDate);
                // logger.logs("loan.writtenOffDate", loan.writtenOffDate);
                // logger.logs("checkFromDate", checkFromDate);
                const installments = await installmentService.getPeriodicInstallmentsRecovered(null, [loan.loanNo], checkFromDate, toDate, true, true, true, true, false, true);

                //logger.logs("installments", installments);

                if (installments && installments.success) {
                    for (let i = 0; i < installments.data.length; i++) {
                        let feesAmount = 0;
                        if (installments.fees.length > 0) {
                            const fees = installments.fees.filter((x) => x.installmentNumber == installments.data[i].installmentNumber);

                            if (fees.length > 0) {
                                for (const fee of fees) {
                                    feesAmount += fee.feeAmount;
                                }
                            }
                        }
                        const totalAmount = round10(installments.data[i].installmentAmount + installments.data[i].moratoriumInterestReceived + feesAmount, -2);

                        const record = {
                            "Loan No": installments.data[i].loanNo,
                            "Installment Number": installments.data[i].installmentNumber,
                            "Total Amount": totalAmount,
                            "Principal": installments.data[i].installmentPrincipal,
                            "Interest": installments.data[i].installmentInterest,
                            "Late Fees": feesAmount,
                            "channel": installments.data[i].channel,
                            "Date Collected": getDMYFormattedDate(installments.data[i].transactionDate, '/'),
                            "lendingPartnerId": loanLendingPartner._id,
                            "lendingPartner": loanLendingPartner.displayName
                        }

                        records.push(record);
                    }
                }
            }
        }
        //logger.logs("records", records);
        if (records.length > 0) {
            const currentMonth = fromDate.getMonth() + 1;
            const fileName = "WrittenOffCollectedAfter" + fromDate.getDate() + "_" + currentMonth + "_" + fromDate.getFullYear().toString().substring(2, 4) + ".xlsx";
            let xls = json2xls(records);
            const filePath = config.get("exportToExcelPath.generalReports") + fileName;
            console.log(filePath);
            fs.writeFile(filePath, xls, "binary", async () => {
                let excelFromDate = getDMYFormattedDate(fromDate, "_");
                let mailModel = getMailModel();
                mailModel.attachmentName = fileName;
                mailModel.attachmentPath = filePath;
                mailModel.name = "Write Off Loans Report";
                mailModel.salutation = "Team";
                mailModel.date = excelFromDate;
                const mailerConfig = await MailerConfig.findOne({ name: "Write Off Loans Collection Report" });
                if (mailerConfig && mailerConfig != null) {
                    mailModel.to = mailerConfig.to;
                    mailModel.cc = mailerConfig.cc;
                    let mailResponse = await sendExternalMail(mailModel, "Write Off Loans Collection Report", "Write Off Loans Collection Report For Fundfina");
                }

                fs.unlink(filePath, function () {
                    //logger.logs("file deleted successfully");
                });
            });
        }
        output = {
            success: true,
            message: "Fetched Successfully",
            status: 200,
        };
    } catch (ex) {
        logger.logs(ex);
        saveErrorLog("exportWrittenOffCollectionReport", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};

module.exports.exportWrittenOffLoans = async function (model) {
    let output = {
        success: false,
        message: "Some Error Occurred",
        status: 200,
    };

    try {
        const limit = 100;

        const setDatesResponse = await setDates(model);

        if (setDatesResponse.success) {
            let fromDate = setDatesResponse.data.fromDate;
            let toDate = setDatesResponse.data.toDate;
            let isEmailSend = setDatesResponse.data.isEmailSend;

            logger.logs("fromDate", fromDate);
            logger.logs("toDate", toDate);
            logger.logs("isEmailSend", isEmailSend);
            logger.logs("FromDate", getDMYFormattedDate(fromDate, "_"));
            logger.logs("ToDate", getDMYFormattedDate(toDate, "_"));

            let query = {
                status: { $in: ["Written-Off", "WrittenOff Paid"] }, "flowDetails": {
                    $elemMatch: {
                        "Step": "Written-Off", insertedOn: {
                            $exists: true, $gte: fromDate, $lte: toDate
                        }
                    }
                }
            };

            const pendingStatus = ["Pending", "Partial"];
            const totalLoans = await LoanInfo.countDocuments(query);
            let totalPages = parseInt(totalLoans / limit);
            let skip = 0;
            let records = [];

            for (let page = 0; page < (totalPages + 1); page++) {
                skip = page * limit;

                const loans = await LoanInfo.aggregate([{ $match: query },
                { $unwind: "$flowDetails" },
                { $match: { "flowDetails.Step": "Written-Off" } },
                { $project: { loanNo: 1, "writtenOffDate": "$flowDetails.insertedOn" } },
                { $sort: { loanNo: 1 } }, { $skip: skip }, { $limit: limit }
                ]).allowDiskUse(true);

                //logger.logs("loans", loans);
                const uniqueLoans = loans.map((x) => x.loanNo);

                const loanDetails = await LoanInfo.find({ loanNo: { $in: uniqueLoans } }).select({ _id: 1, loanNo: 1, lenderDetails: 1, partnerId: 1, userId: 1, totalOutstanding: 1, principalAmount: 1, interestAmount: 1, feeAmountOutstanding: 1, disbursalDetails: 1, amount: 1, arrears: 1 });

                const uniqueLenders = [];
                const uniqueBorrowingPartners = [];
                const uniqueUsers = [];

                for (let i = 0; i < loanDetails.length; i++) {
                    uniqueLenders.push(loanDetails[i].lenderDetails[0].lenderPartnerId);
                    uniqueBorrowingPartners.push(loanDetails[i].partnerId);
                    uniqueUsers.push(loanDetails[i].userId);
                }

                const lendingPartners = await PartnerMaster.find({ _id: { $in: uniqueLenders } }).select({ _id: 1, displayName: 1 });
                const borrowingPartners = await PartnerMaster.find({ _id: { $in: uniqueBorrowingPartners } }).select({ _id: 1, displayName: 1 });
                const users = await UserInfo.find({ _id: { $in: uniqueUsers } }).select({ _id: 1, userDetails: 1, panNo: 1, addressDetails: 1 });

                const lenderPartnerHash = groupBy(lendingPartners, "_id");
                const borrowerPartnerHash = groupBy(borrowingPartners, "_id");
                const loanDetailsHash = groupBy(loanDetails, "loanNo");
                const userDetailsHash = groupBy(users, "_id");

                for (const loan of loans) {
                    //logger.logs("loan", loan);
                    const loanInfo = loanDetailsHash[loan.loanNo][0];
                    //logger.logs("lenderDetails", lenderDetails);
                    const loanLendingPartner = lenderPartnerHash[loanInfo.lenderDetails[0].lenderPartnerId][0];
                    const loanBorrowingPartner = borrowerPartnerHash[loanInfo.partnerId][0];
                    const loanUser = userDetailsHash[loanInfo.userId][0];

                    //logger.logs("loanLendingPartner", loanLendingPartner);

                    const checkFromDate = fromDate > loan.writtenOffDate ? fromDate : loan.writtenOffDate;

                    const installments = await installmentService.getPeriodicInstallmentsRecovered(null, [loan.loanNo], checkFromDate, toDate, true, true, true, true, false, true);

                    //logger.logs("installments", installments);
                    let feesAmount = 0;

                    if (installments && installments.success && installments.fees.length > 0) {
                        feesAmount = installments.fees.filter((x) => x.feeAmount > 0).reduce(function (prev, cur) {
                            return prev + cur.feeAmount;
                        }, 0).toFixed(2);

                        // if (fees.length > 0) {
                        //     for (const fee of fees) {
                        //         feesAmount += fee.feeAmount;
                        //     }
                        // }
                    }

                    let outstandingAmountWriteOff = 0;
                    let collectedPriorDefaultDate = 0;
                    let collectedPostWrittenOff = 0;
                    let PrincipalWrittenOff = 0;


                    let arrears = loanInfo.arrears.filter(x => x.delinquency == "Write-Off");
                    if (arrears && arrears.length > 0) {
                        outstandingAmountWriteOff = arrears[0].amount;

                        const installlmentsWrittenOff = await installmentService.getPeriodicInstallmentsRecovered(null, [loan.loanNo], arrears[0].insertedOn, toDate, false, true, true, true, false, true, true);

                        if (installlmentsWrittenOff && installlmentsWrittenOff.success && installlmentsWrittenOff.data.length > 0) {
                            collectedPostWrittenOff = installlmentsWrittenOff.data.length;
                            for (let i = 0; i < installlmentsWrittenOff.data.length; i++) {
                                PrincipalWrittenOff += installlmentsWrittenOff.data[i].installmentPrincipal;
                            }
                        }
                    }

                    arrears = loanInfo.arrears.filter(x => x.delinquency == "Default");

                    if (arrears && arrears.length > 0) {
                        const installmentsDefault = await installmentService.getPeriodicInstallmentsRecovered(null, [loan.loanNo], null, arrears[0].insertedOn, false, true, true, true, false, true, true);
                        if (installmentsDefault && installmentsDefault.success && installmentsDefault.data.length > 0) {
                            collectedPriorDefaultDate = installmentsDefault.data.length;
                        }
                    }

                    const pendingInstallments = await installmentService.getInstallments(null, [loan.loanNo], pendingStatus, null);
                    if (pendingInstallments && pendingInstallments.success && pendingInstallments.data.length > 0) {
                        for (let i = 0; i < pendingInstallments.data.length; i++) {
                            PrincipalWrittenOff += pendingInstallments.data[i].installmentPrincipal;
                        }
                    }

                    const record = {
                        "Loan No": loan.loanNo,
                        "Disbursal Date": getDMYFormattedDate(loanInfo.disbursalDetails.disbursedOn, "-"),
                        "Loan Amount": loanInfo.amount,
                        "Disbursal Amount": loanInfo.disbursalDetails.disbursalAmount ? loanInfo.disbursalDetails.disbursalAmount : null,
                        "PAN": loanUser.panNo,
                        "State": loanUser.addressDetails[loanUser.addressDetails.length - 1].state,
                        "Borrowing Partner": loanBorrowingPartner.displayName,
                        "Lending Partner": loanLendingPartner.displayName,
                        "Written Off Date": getDMYFormattedDate(loan.writtenOffDate, '-'),
                        "Total Outstanding Amount": loanInfo.totalOutstanding,
                        "Total Principal Pending": loanInfo.principalAmount.outstanding,
                        "Total Interest Pending": loanInfo.interestAmount.outstanding,
                        "Total Late Fees Pending": loanInfo.feeAmountOutstanding,
                        "Principal Collected": loanInfo.principalAmount.collected,
                        "Interest Collected": loanInfo.interestAmount.collected,
                        "Late Fees Collected": feesAmount,
                        "Outstanding amount as on date of write off": outstandingAmountWriteOff,
                        "No of installments collected prior to default date": collectedPriorDefaultDate,
                        "No of instalments recovered by post WriteOff ": collectedPostWrittenOff,
                        "Principal Pending at the time of WriteOff": PrincipalWrittenOff

                    };

                    records.push(record);
                }
            }
            //logger.logs("records", records);
            if (records.length > 0) {
                let excelFromDate = getDMYFormattedDate(fromDate, "_");
                let excelToDate = getDMYFormattedDate(toDate, "_");
                const currentMonth = fromDate.getMonth() + 1;
                const fileName = "WrittenOffLoans_" + excelFromDate + "_To_" + excelToDate + ".xlsx";
                let xls = json2xls(records);
                const filePath = config.get("exportToExcelPath.generalReports") + fileName;

                console.log(filePath);

                fs.writeFile(filePath, xls, "binary", async () => {
                    let mailModel = getMailModel();
                    mailModel.attachmentName = fileName;
                    mailModel.attachmentPath = filePath;
                    mailModel.name = "Write Off Loans Report";
                    mailModel.salutation = "Team";
                    mailModel.date = excelFromDate;
                    const mailerConfig = await MailerConfig.findOne({ name: "Write Off Loans Report" });
                    if (mailerConfig && mailerConfig != null) {
                        mailModel.to = mailerConfig.to;
                        mailModel.cc = mailerConfig.cc;
                        let mailResponse = await sendExternalMail(mailModel, "Write Off Loans Report", "Write Off Loans Report For Fundfina");
                    }

                    fs.unlink(filePath, function () {
                        //logger.logs("file deleted successfully");
                    });
                });
            }
            output = {
                success: true,
                message: "Fetched Successfully",
                status: 200,
            };
        } else {
            output.message = setDatesResponse.message;
        }
    } catch (ex) {
        logger.logs(ex);
        saveErrorLog("exportWrittenOffLoans", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};

module.exports.exportProcessingFeesReport = async function (model) {
    let output = {
        success: false,
        message: "Some Error Occurred",
        status: 200,
    };

    try {
        const limit = 1000;

        const setDatesResponse = await setDates(model);

        if (setDatesResponse.success) {
            let fromDate = setDatesResponse.data.fromDate;
            let toDate = setDatesResponse.data.toDate;
            let isEmailSend = setDatesResponse.data.isEmailSend;

            logger.logs("fromDate", fromDate);
            logger.logs("toDate", toDate);
            logger.logs("isEmailSend", isEmailSend);
            logger.logs("FromDate", getDMYFormattedDate(fromDate, "_"));
            logger.logs("ToDate", getDMYFormattedDate(toDate, "_"));

            let query = { "disbursalDetails.disbursedOn": { $gte: fromDate, $lte: toDate }, status: { $ne: "Loan Cancelled" } };

            const totalLoans = await LoanInfo.countDocuments(query);
            let totalPages = parseInt(totalLoans / limit);
            let skip = 0;
            let records = [];

            for (let page = 0; page < (totalPages + 1); page++) {
                skip = page * limit;

                const loans = await LoanInfo.find(query).select({ _id: 1, loanNo: 1, partnerId: 1, userId: 1, lenderDetails: 1, disbursalDetails: 1, amount: 1 }).skip(skip).limit(limit);

                const uniqueLenders = [];
                const uniqueBorrowingPartners = [];
                const uniqueUsers = [];
                const uniqueLoans = [];

                for (let i = 0; i < loans.length; i++) {
                    uniqueLenders.push(loans[i].lenderDetails[0].lenderPartnerId);
                    uniqueBorrowingPartners.push(loans[i].partnerId);
                    uniqueUsers.push(loans[i].userId);
                    uniqueLoans.push(loans[i].loanNo);
                }

                const lendingPartners = await PartnerMaster.find({ _id: { $in: uniqueLenders } }).select({ _id: 1, displayName: 1 });

                const borrowingPartners = await PartnerMaster.find({ _id: { $in: uniqueBorrowingPartners } }).select({ _id: 1, displayName: 1 });

                const users = await UserInfo.find({ _id: { $in: uniqueUsers } }).select({ _id: 1, userDetails: 1, panNo: 1, addressDetails: 1 });

                const disbursementTransactions = await LoanDisbursmentTransactions.find({ loanNo: { $in: uniqueLoans }, disbursalType: "CHARGES", disbursalStatus: "Payment Completed" }).select({ loanNo: 1, disbursalSteps: 1, updatedOn: 1 });

                const lenderPartnerHash = groupBy(lendingPartners, "_id");
                const borrowerPartnerHash = groupBy(borrowingPartners, "_id");
                const userDetailsHash = groupBy(users, "_id");
                const transactionsHash = groupBy(disbursementTransactions, "loanNo");

                for (let i = 0; i < loans.length; i++) {
                    const disbursedOn = new Date(loans[i].disbursalDetails.disbursedOn);
                    const transactionMonth = disbursedOn.getMonth();
                    const transactionYear = disbursedOn.getFullYear();
                    const loanLendingPartner = lenderPartnerHash[loans[i].lenderDetails[0].lenderPartnerId][0];
                    const loanBorrowingPartner = borrowerPartnerHash[loans[i].partnerId][0];
                    const loanUser = userDetailsHash[loans[i].userId][0];
                    const transactions = transactionsHash[loans[i].loanNo];

                    // const tran = transactions[0];
                    // logger.logs("tran", tran);
                    // logger.logs("tran", tran.loanNo);
                    // logger.logs("tran.disbursalSteps", tran.disbursalSteps);
                    // logger.logs("tran.disbursalSteps.LOAN_PAYMENT_CONFIRMATION", tran.disbursalSteps.LOAN_PAYMENT_CONFIRMATION);
                    // logger.logs("transactions.disbursalSteps.LOAN_PAYMENT_CONFIRMATION.response.dot", tran.disbursalSteps);

                    const record = {
                        "Loan No": loans[i].loanNo,
                        "Disbursal Date": getDMYFormattedDate(loans[i].disbursalDetails.disbursedOn, "-"),
                        "Loan Amount": loans[i].amount,
                        "Disbursal Amount": loans[i].disbursalDetails.disbursalAmount,
                        "Processing Fees": loans[i].disbursalDetails.processingFee,
                        "Stamp paper charges": loans[i].disbursalDetails.stampingCharges,
                        "GST Charges": loans[i].disbursalDetails.gstCharges,
                        "PAN": loanUser.panNo,
                        "State": loanUser.addressDetails[loanUser.addressDetails.length - 1].state,
                        "Borrowing Partner": loanBorrowingPartner.displayName,
                        "Lending Partner": loanLendingPartner.displayName,
                        "Transaction - UTR": loans[i].disbursalDetails.bankReferenceNo,
                        "Processing Fees Posting Date": transactions && transactions.length > 0 && transactions[0].updatedOn ? getDMYFormattedDate(transactions[0].updatedOn, "-") : null,
                        "Month": transactionMonth + 1,
                        "Year": transactionYear
                    }

                    records.push(record);
                }
            }

            if (records.length > 0) {
                const excelFromDate = getDMYFormattedDate(fromDate, "_");
                const excelToDate = getDMYFormattedDate(toDate, "_");
                const fileName = "ProcessingFeesReport_" + excelFromDate + "_To_" + excelToDate + ".xlsx";
                let xls = json2xls(records);
                const filePath = config.get("exportToExcelPath.generalReports") + fileName;

                logger.logs(filePath);

                fs.writeFile(filePath, xls, "binary", async () => {
                    if (isEmailSend) {
                        let mailModel = getMailModel();
                        mailModel.attachmentName = fileName;
                        mailModel.attachmentPath = filePath;
                        mailModel.name = "Processing Fees Report";
                        mailModel.salutation = "Team";
                        mailModel.date = excelFromDate;
                        const mailerConfig = await MailerConfig.findOne({ name: "Processing Fees Report" });
                        if (mailerConfig && mailerConfig != null) {
                            mailModel.to = mailerConfig.to;
                            mailModel.cc = mailerConfig.cc;
                            let mailResponse = await sendExternalMail(mailModel, "Processing Fees Report", "Processing Fees Report For Fundfina");
                            logger.logs("mailResponse", mailResponse);
                        }

                        fs.unlink(filePath, function () {
                            //logger.logs("file deleted successfully");
                        });
                    }
                });
            }

            output = {
                success: true,
                message: "Fetched Successfully",
                status: 200,
            };
        } else {
            output.message = setDatesResponse.message;
        }
    } catch (ex) {
        logger.logs(ex);
        saveErrorLog("exportProcessingFeesReport", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};

module.exports.exportLateFeesCollectionReport = async function (model) {
    let output = {
        success: false,
        message: "Some Error Occurred",
        status: 200,
    };

    try {
        const setDatesResponse = await setDates(model);

        if (setDatesResponse.success) {
            let fromDate = setDatesResponse.data.fromDate;
            let toDate = setDatesResponse.data.toDate;
            let isEmailSend = setDatesResponse.data.isEmailSend;
            let loanNos = null;

            logger.logs("fromDate", fromDate);
            logger.logs("toDate", toDate);
            logger.logs("isEmailSend", isEmailSend);
            logger.logs("FromDate", getDMYFormattedDate(fromDate, "_"));
            logger.logs("ToDate", getDMYFormattedDate(toDate, "_"));

            if (model.partnerId && model.partnerId != null) {
                const loans = await LoanInfo.find({ "lenderDetails.lenderPartnerId": mongoose.Types.ObjectId(model.partnerId) }).select({ _id: 1, loanNo: 1 });
                loanNos = loans.map((x) => x.loanNo);
            }

            const records = [];

            const installments = await installmentService.getPeriodicInstallmentsRecovered(null, loanNos, fromDate, toDate, true, true, true, true, false, true, false);

            if (installments && installments.success && installments.fees.length > 0) {
                logger.logs("installments.fees.length", installments.fees.length);
                let uniqueLoans = installments.fees.map((x) => x.loanNo);
                uniqueLoans = [...new Set(uniqueLoans)];

                const loanDetails = await LoanInfo.find({ loanNo: { $in: uniqueLoans } }).select({ _id: 1, loanNo: 1, disbursalDetails: 1, userId: 1, partnerId: 1, lenderDetails: 1, status: 1 });

                const uniqueLenders = [];
                const uniquePartners = [];
                const uniqueUsers = [];

                for (let i = 0; i < loanDetails.length; i++) {
                    uniqueLenders.push(loanDetails[i].lenderDetails[0].lenderPartnerId);
                    uniquePartners.push(loanDetails[i].partnerId);
                    uniqueUsers.push(loanDetails[i].userId);
                }

                const lendingPartners = await PartnerMaster.find({ _id: { $in: uniqueLenders } }).select({ _id: 1, displayName: 1 });

                const partners = await PartnerMaster.find({ _id: { $in: uniquePartners } }).select({ _id: 1, displayName: 1 });

                const users = await UserInfo.find({ _id: { $in: uniqueUsers } }).select({ panNo: 1, addressDetails: 1, });

                const loanDetailsHash = groupBy(loanDetails, "loanNo");
                const lenderPartnerHash = groupBy(lendingPartners, "_id");
                const borrowingPartnerHash = groupBy(partners, "_id");
                const usersHash = groupBy(users, "_id");

                for (let i = 0; i < installments.fees.length; i++) {
                    const loan = loanDetailsHash[installments.fees[i].loanNo][0];
                    const lendingPartner = lenderPartnerHash[loan.lenderDetails[0].lenderPartnerId][0];
                    const borrowerPartner = borrowingPartnerHash[loan.partnerId][0];
                    const user = usersHash[loan.userId][0];
                    const collectionDate = new Date(installments.fees[i].feeCollectedDate);
                    const transactionMonth = collectionDate.getMonth();
                    const transactionYear = collectionDate.getFullYear();
                    let record = {
                        "Loan No": installments.fees[i].loanNo,
                        "Loan Status": loan.status,
                        "Disbursal Date": getDMYFormattedDate(loan.disbursalDetails.disbursedOn, "-"),
                        "Late Fees Collected": installments.fees[i].feeAmount,
                        "PAN": user.panNo,
                        "State": user.addressDetails[user.addressDetails.length - 1].state,
                        "Borrowing Partner": borrowerPartner.displayName,
                        "Lending Partner": lendingPartner.displayName,
                        "Transaction - UTR": null,
                        "EDI collection date": getDMYFormattedDate(collectionDate, "-"),
                        "Month": transactionMonth + 1,
                        "Year": transactionYear
                    }
                    records.push(record);
                }
                // feesAmount = installments.fees.filter((x) => x.feeAmount > 0).reduce(function (prev, cur) {
                //     return prev + cur.feeAmount;
                // }, 0).toFixed(2);
            }

            if (records.length > 0) {
                const excelFromDate = getDMYFormattedDate(fromDate, "_");
                const excelToDate = getDMYFormattedDate(toDate, "_");
                const fileName = "LateFeesCollectionReport_" + excelFromDate + "_To_" + excelToDate + ".xlsx";
                let xls = json2xls(records);
                const filePath = config.get("exportToExcelPath.generalReports") + fileName;

                logger.logs(filePath);

                fs.writeFile(filePath, xls, "binary", async () => {
                    if (isEmailSend) {
                        let mailModel = getMailModel();
                        mailModel.attachmentName = fileName;
                        mailModel.attachmentPath = filePath;
                        mailModel.name = "Late Fees Collection Report";
                        mailModel.salutation = "Team";
                        mailModel.date = excelFromDate;
                        const mailerConfig = await MailerConfig.findOne({ name: "Late Fees Collection Report" });
                        if (mailerConfig && mailerConfig != null) {
                            mailModel.to = mailerConfig.to;
                            mailModel.cc = mailerConfig.cc;
                            let mailResponse = await sendExternalMail(mailModel, "Late Fees Collection Report", "Late Fees Collection Report For Fundfina");
                            logger.logs("mailResponse", mailResponse);
                        }

                        fs.unlink(filePath, function () {
                            //logger.logs("file deleted successfully");
                        });
                    }
                });
            }

            output = {
                success: true,
                message: "Fetched Successfully",
                status: 200,
            };
        } else {
            output.message = setDatesResponse.message;
        }
    } catch (ex) {
        logger.logs(ex);
        saveErrorLog("exportLateFeesCollectionReport", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};

module.exports.exportWrittenOffRecoveryReport = async function (model) {
    let output = {
        success: false,
        message: "Some Error Occurred",
        status: 200,
    };

    try {
        const limit = 100;
        const setDatesResponse = await setDates(model);

        if (setDatesResponse.success) {
            let fromDate = setDatesResponse.data.fromDate;
            let toDate = setDatesResponse.data.toDate;
            let isEmailSend = setDatesResponse.data.isEmailSend;

            logger.logs("fromDate", fromDate);
            logger.logs("toDate", toDate);
            logger.logs("isEmailSend", isEmailSend);
            logger.logs("FromDate", getDMYFormattedDate(fromDate, "_"));
            logger.logs("ToDate", getDMYFormattedDate(toDate, "_"));
            let query = { status: { $in: ["Written-Off", "WrittenOff Paid"] } };

            const totalLoans = await LoanInfo.countDocuments(query);
            let totalPages = parseInt(totalLoans / limit);
            let skip = 0;
            let records = [];

            for (let page = 0; page < (totalPages + 1); page++) {
                skip = page * limit;

                const loans = await LoanInfo.aggregate([{ $match: query },
                { $unwind: "$flowDetails" },
                { $match: { "flowDetails.Step": "Written-Off" } },
                { $project: { loanNo: 1, "writtenOffDate": "$flowDetails.insertedOn", disbursalDetails: 1, amount: 1, userId: 1, lenderDetails: 1, partnerId: 1 } },
                { $sort: { loanNo: 1 } }, { $skip: skip }, { $limit: limit }
                ]).allowDiskUse(true);

                //logger.logs("loans", loans);
                let uniqueLoans = [];
                let uniqueUserIds = [];
                let uniqueLenders = [];
                let uniqueBorrowers = [];

                for (let i = 0; i < loans.length; i++) {
                    uniqueLoans.push(loans[i].loanNo);
                    uniqueUserIds.push(loans[i].userId);
                    uniqueLenders.push(loans[i].lenderDetails[0].lenderPartnerId);
                    uniqueBorrowers.push(loans[i].partnerId);
                }

                uniqueLoans = [...new Set(uniqueLoans)];
                uniqueUserIds = [...new Set(uniqueUserIds)];
                uniqueLenders = [...new Set(uniqueLenders)];
                uniqueBorrowers = [...new Set(uniqueBorrowers)];

                //const loanDetails = await LoanInfo.find({ loanNo: { $in: uniqueLoans } }).select({ _id: 1, loanNo: 1, lenderDetails: 1 });

                //const uniqueLenders = loanDetails.map((x) => x.lenderDetails[0].lenderPartnerId);

                const lendingPartners = await PartnerMaster.find({ _id: { $in: uniqueLenders } }).select({ _id: 1, displayName: 1 });
                const borrowingPartners = await PartnerMaster.find({ _id: { $in: uniqueBorrowers } }).select({ _id: 1, displayName: 1 });
                const userInfo = await UserInfo.find({ _id: { $in: uniqueUserIds } }).select({ _id: 1, panNo: 1 });

                const usersHash = groupBy(userInfo, "_id");
                const lenderPartnerHash = groupBy(lendingPartners, "_id");
                const borrowingPartnerHash = groupBy(borrowingPartners, "_id");

                for (const loan of loans) {

                    const userDetails = usersHash[loan.userId][0];
                    const loanLendingPartner = lenderPartnerHash[loan.lenderDetails[0].lenderPartnerId][0];
                    const loanBorrowingPartner = borrowingPartnerHash[loan.partnerId][0];


                    //logger.logs("loanLendingPartner", loanLendingPartner);

                    const checkFromDate = fromDate > loan.writtenOffDate ? fromDate : loan.writtenOffDate;
                    // logger.logs("fromDate", fromDate);
                    // logger.logs("loan.writtenOffDate", loan.writtenOffDate);
                    // logger.logs("checkFromDate", checkFromDate);
                    const installments = await installmentService.getPeriodicInstallmentsRecovered(null, [loan.loanNo], checkFromDate, toDate, true, true, true, true, false, true);

                    //logger.logs("installments", installments);

                    if (installments && installments.success) {
                        for (let i = 0; i < installments.data.length; i++) {
                            let feesAmount = 0;
                            if (installments.fees.length > 0) {
                                const fees = installments.fees.filter((x) => x.installmentNumber == installments.data[i].installmentNumber);

                                if (fees.length > 0) {
                                    for (const fee of fees) {
                                        feesAmount += fee.feeAmount;
                                    }
                                }
                            }
                            const totalAmount = round10(installments.data[i].installmentAmount + installments.data[i].moratoriumInterestReceived + feesAmount, -2);

                            const record = {
                                "Loan No": installments.data[i].loanNo,
                                "Disbursal Date": getDMYFormattedDate(loan.disbursalDetails.disbursedOn, '/'),
                                "Loan Write Off Date": getDMYFormattedDate(loan.writtenOffDate, '/'),
                                "Loan Amount": loan.amount,
                                "Disbursal Amount": loan.disbursalDetails.disbursalAmount ? loan.disbursalDetails.disbursalAmount : "NA",
                                "Installment Number": installments.data[i].installmentNumber,
                                "Total Amount": totalAmount,
                                "Principal": installments.data[i].installmentPrincipal,
                                "Interest": installments.data[i].installmentInterest,
                                "Late Fees": feesAmount,
                                "Date Collected": getDMYFormattedDate(installments.data[i].transactionDate, '/'),
                                "EDI Collection Posting Date": getDMYFormattedDate(installments.data[i].transactionDate, '/'),
                                "Transaction - UTR": installments.data[i].enterprisePartnerSettlementInfo && installments.data[i].enterprisePartnerSettlementInfo.utrNo ? installments.data[i].enterprisePartnerSettlementInfo.utrNo : "NA",
                                "Channel": installments.data[i].channel,
                                "Pan": userDetails.panNo,
                                "Borrowing Partner": loanBorrowingPartner.displayName,
                                "Lending Partner": loanLendingPartner.displayName
                            };

                            records.push(record);
                        }
                    }
                }
            }
            //logger.logs("records", records);
            if (records.length > 0) {
                const currentMonth = fromDate.getMonth() + 1;
                const fileName = "WrittenOffRecoveryReport" + fromDate.getDate() + "_" + currentMonth + "_" + fromDate.getFullYear().toString().substring(2, 4) + ".xlsx";
                let xls = json2xls(records);
                const filePath = config.get("exportToExcelPath.generalReports") + fileName;
                console.log(filePath);
                fs.writeFile(filePath, xls, "binary", async () => {
                    if (isEmailSend) {
                        const excelFromDate = getDMYFormattedDate(fromDate, "_");
                        const excelToDate = getDMYFormattedDate(toDate, "_");

                        let mailModel = getMailModel();
                        mailModel.attachmentName = fileName;
                        mailModel.attachmentPath = filePath;
                        mailModel.name = "Write Off Loans Report";
                        mailModel.salutation = "Team";
                        mailModel.fromDate = excelFromDate;
                        mailModel.toDate = excelToDate;

                        const mailerConfig = await MailerConfig.findOne({ name: "WrittenOff Recovery Report" });
                        if (mailerConfig && mailerConfig != null) {
                            mailModel.to = mailerConfig.to;
                            mailModel.cc = mailerConfig.cc;

                            const mailResponse = await sendExternalMail(mailModel, "WrittenOff Recovery Report", "WrittenOff Recovery Report For Fundfina");
                            logger.logs("mailResponse", mailResponse);
                        }

                        fs.unlink(filePath, function () {
                            logger.logs("file deleted successfully");
                        });
                    }
                });
            }
            output = {
                success: true,
                message: "Fetched Successfully",
                status: 200,
            };
        } else {
            output.message = setDatesResponse.message;
        }
    } catch (ex) {
        logger.logs(ex);
        saveErrorLog("exportWrittenOffCollectionReport", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};

generateReportExcelAndSendMailToPartner = async function (dataArray, fileName, dateString, excelExportConfig, emailTo, emailCc, mailerCategory, mailerSubCategory) {
    let output = {
        success: false,
        message: "Some Error Occured"
    }
    try {

        const excelPayload = [];

        for (const data of dataArray) {
            let dataTransformed = mapper(excelExportConfig, data, {});
            excelPayload.push(dataTransformed);
        }

        const dirPath = config.get("keys.tempPath");

        if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath);
        }

        const filePath = dirPath + "/" + fileName;

        let workbook = XLSX.utils.book_new()
        let worksheet = XLSX.utils.json_to_sheet(excelPayload);
        XLSX.utils.book_append_sheet(workbook, worksheet, "Loans")
        XLSX.writeFile(workbook, filePath);

        let mailModel = getMailModel();
        mailModel.attachmentName = fileName;
        mailModel.attachmentPath = filePath;
        mailModel.name = "Team";
        mailModel.dateString = dateString;

        let mailResponse = null;

        mailModel.salutation = "Team";
        mailModel.to = emailTo;
        mailModel.cc = emailCc;

        // mailModel.to = '<EMAIL>';
        // mailModel.cc = '<EMAIL>';
        // mailModel.cc = '<EMAIL>';
        // mailModel.date = getDMYFormattedDateWithTime(Date.now(), "-");

        mailResponse = await sendExternalMail(mailModel, mailerCategory, mailerSubCategory);


        if (mailResponse.success) {
            fs.unlink(filePath, function () {
                //logger.logs("file deleted successfully");
            });

            output = {
                success: true,
                message: `${mailerCategory} Report Mailed Successfully`,
            };
        } else {
            output = {
                success: false,
                message: mailResponse.message,

            };
        }

    } catch (ex) {
        saveErrorLog("generateExcelAndSendMailToPartner", apiPath, { dataArray, fileName, dateString, excelExportConfig, emailTo, emailCc, mailerCategory, mailerSubCategory }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.error = ex.message;
    }

    return output;
}

module.exports.loanClosureReportForBorrowingPartners = async function (model) {
    let output = {
        success: false,
        message: "Some Error Occurred",
        status: 200,
    };

    try {

        // let partnerIdList = await PartnerMaster.find({ partnerType: "Borrowing Partner", "reports.loanClosureReport": true }, { _id: 1 }).lean();
        // partnerIdList = partnerIdList.map(partnerRecord => partnerRecord['_id']);


        // let today = new Date(2022, 03, 01, 0, 0, 0);
        let today = new Date();

        const previousMonthDayCount = new Date(today.getFullYear(), today.getMonth(), 0).getDate();

        let startDate = new Date(today.getMonth() === 0 ? today.getFullYear() - 1 : today.getFullYear(), today.getMonth() === 0 ? 11 : today.getMonth() - 1, 1, 0, 0, 0, 0)

        let endDate = new Date(startDate.getFullYear(), startDate.getMonth(), previousMonthDayCount, 23, 59, 59, 999);

        // const startOfTheYear = new Date(2023, 0, 1, 0, 0, 0, 0);
        // $gte: new Date(2023, 3, 1, 0, 0, 0, 0),
        // $lte: new Date(2023, 3, 30, 23, 59, 59, 999),



        // const query = [
        //     {
        //         '$match': {
        //             'partnerId': { '$in': partnerIdList },
        //             'status': {
        //                 '$in': [
        //                     'Close Repaid', 'Close Prepaid'
        //                 ]
        //             },
        //             'disbursalDetails.disbursedOn': {
        //                 '$gte': startOfTheYear
        //             },
        //             'lastEmiPaidDate': {
        //                 '$gte': startDate,
        //                 '$lte': endDate,
        //             },
        //         }
        //     }, {
        //         '$project': {
        //             '_id': 0,
        //             'loanNo': 1,
        //             'merchantCode': 1,
        //             'amount': 1,
        //             'partnerId': 1,
        //             'disbursalDate': '$disbursalDetails.disbursedOn',
        //             'lastEmiPaidDate': 1,
        //             // 'loanClosureDate': {
        //             //     '$filter': {
        //             //         'input': '$flowDetails',
        //             //         'as': 'item',
        //             //         'cond': {
        //             //             '$and': [
        //             //                 {
        //             //                     '$in': [
        //             //                         '$$item.Step', [
        //             //                             'Close Repaid', 'Close Prepaid'
        //             //                         ]
        //             //                     ]
        //             //                 }, {
        //             //                     '$gte': [
        //             //                         '$$item.insertedOn', startDate
        //             //                     ]
        //             //                 }, {
        //             //                     '$lte': [
        //             //                         '$$item.insertedOn', endDate
        //             //                     ]
        //             //                 }
        //             //             ]
        //             //         }
        //             //     }
        //             // }
        //             'loanClosureDate': {
        //                 '$last': '$flowDetails'
        //             }
        //         }
        //     },
        //     //  {
        //     //     '$unwind': {
        //     //         'path': '$loanClosureDate'
        //     //     }
        //     // }
        //     // ,
        //     {
        //         '$project': {
        //             'loanNo': 1,
        //             'merchantCode': 1,
        //             'amount': 1,
        //             'partnerId': 1,
        //             'disbursalDate': 1,
        //             'lastEmiPaidDate': 1,
        //             'loanClosureDate': '$loanClosureDate.insertedOn'
        //         }
        //     }, {
        //         '$group': {
        //             '_id': '$partnerId',
        //             'partnerDataArray': {
        //                 '$push': {
        //                     'loanNo': '$loanNo',
        //                     'merchantCode': '$merchantCode',
        //                     'loanAmount': '$amount',
        //                     'disbursalDate': '$disbursalDate',
        //                     'lastEmiPaidDate': '$lastEmiPaidDate',
        //                     'loanClosureDate': '$loanClosureDate'
        //                 }
        //             }
        //         }
        //     }
        // ]

        const query = [
            {
                $match: {
                    status: {
                        $in: ["Close Repaid", "Close Prepaid"],
                    },
                    lastEmiPaidDate: {
                        $gte: startDate,
                        $lte: endDate,
                    },
                },
            },
            {
                $lookup: {
                    from: "PartnerMaster",
                    localField: "partnerId",
                    foreignField: "_id",
                    as: "partnerMasterRecords",
                },
            },
            {
                $unwind: {
                    path: "$partnerMasterRecords",
                },
            },
            {
                $match: {
                    "partnerMasterRecords.partnerType":
                        "Borrowing Partner",
                    "partnerMasterRecords.reports.loanClosureReport": true,
                    $expr: {
                        $gte: [
                            "$disbursalDetails.disbursedOn",
                            "$partnerMasterRecords.reports.loanClosureReportStartDate",
                        ],
                    },
                },
            },
            {
                $project: {
                    _id: 0,
                    loanNo: 1,
                    merchantCode: 1,
                    amount: 1,
                    partnerId: 1,
                    disbursalDate:
                        "$disbursalDetails.disbursedOn",
                    lastEmiPaidDate: 1,
                    loanClosureDate: {
                        $last: "$flowDetails",
                    },
                },
            },
            {
                $project: {
                    loanNo: 1,
                    merchantCode: 1,
                    amount: 1,
                    partnerId: 1,
                    disbursalDate: 1,
                    lastEmiPaidDate: 1,
                    loanClosureDate:
                        "$loanClosureDate.insertedOn",
                },
            },
            {
                $group: {
                    _id: "$partnerId",
                    partnerDataArray: {
                        $push: {
                            loanNo: "$loanNo",
                            merchantCode: "$merchantCode",
                            loanAmount: "$amount",
                            disbursalDate: "$disbursalDate",
                            lastEmiPaidDate: "$lastEmiPaidDate",
                            loanClosureDate: "$loanClosureDate",
                        },
                    },
                },
            },
        ]

        const partnerData = await LoanInfo.aggregate(query);

        const partnerDataHash = {}
        for (const data of partnerData) {
            partnerDataHash[data['_id']] = {
                ...data
            }
        }

        const partnerEmails = await PartnerMaster.find({ partnerType: "Borrowing Partner", "reports.loanClosureReport": true, emails: { $exists: true } }, { partnerCode: 1, emailTo: '$emails.opsReportEmailTo', emailCc: '$emails.opsReportEmailCc' }).lean();

        for (const email of partnerEmails) {
            partnerDataHash[email['_id']] = {
                ...partnerDataHash[email['_id']],
                ...email
            }
        }

        const promiseArray = [];

        const excelExportConfig = {
            'Loan Number': 'loanNo',
            'Merchant Code': 'merchantCode',
            'Loan Amount': 'loanAmount',
            'Disbursal Date': 'disbursalDate',
            'Last EMI Paid Date': 'lastEmiPaidDate',
            'Loan Closure Date': 'loanClosureDate'
        }

        const month = Constants.MONTH_NAME_BY_INDEX[startDate.getMonth() + 1];
        const year = startDate.getFullYear();


        for (const partnerData in partnerDataHash) {
            const fileName = "LoanClosureReport_" + partnerDataHash[partnerData]['partnerCode'] + "_" + month + "_" + year + ".xlsx";
            const dateString = `${month} ${year}`

            promiseArray.push(generateReportExcelAndSendMailToPartner(partnerDataHash[partnerData]['partnerDataArray'], fileName, dateString, excelExportConfig, partnerDataHash[partnerData]['emailTo'], partnerDataHash[partnerData]['emailCc'], "Loan Closure Report", "Loans in Close Repaid and Close Prepaid Report"));
        }

        await Promise.all(promiseArray);

        output.success = true;
        output.message = "Loan Closure Report generated and sent successfully"

    } catch (ex) {
        logger.logs(ex);
        saveErrorLog("loanClosureReportForBorrowingPartners", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};

module.exports.loanDisbursalReportForBorrowingPartners = async function (model) {
    let output = {
        success: false,
        message: "Some Error Occurred",
        status: 200,
    };

    try {
        let today = new Date();

        const previousMonthDayCount = new Date(today.getFullYear(), today.getMonth(), 0).getDate();

        let startDate = new Date(today.getMonth() === 0 ? today.getFullYear() - 1 : today.getFullYear(), today.getMonth() === 0 ? 11 : today.getMonth() - 1, 1, 0, 0, 0, 0)

        let endDate = new Date(startDate.getFullYear(), startDate.getMonth(), previousMonthDayCount, 23, 59, 59, 999)

        const query = [
            {
                '$match': {
                    'disbursalDetails.disbursedOn': {
                        '$gte': startDate,
                        '$lte': endDate
                    }
                }
            }, {
                '$project': {
                    '_id': 0,
                    'loanNo': 1,
                    'merchantCode': 1,
                    'amount': 1,
                    'partnerId': 1,
                    'disbursalDate': '$disbursalDetails.disbursedOn'
                }
            }, {
                '$group': {
                    '_id': '$partnerId',
                    'partnerDataArray': {
                        '$push': {
                            'loanNo': '$loanNo',
                            'merchantCode': '$merchantCode',
                            'loanAmount': '$amount',
                            'disbursalDate': '$disbursalDate'
                        }
                    }
                }
            }
        ]

        const partnerIdList = []
        const partnerData = await LoanInfo.aggregate(query);

        const partnerDataHash = {}
        for (const data of partnerData) {
            partnerIdList.push(data['_id']);
            partnerDataHash[data['_id']] = {
                ...data
            }
        }

        const partnerEmails = await PartnerMaster.find({ _id: { $in: partnerIdList }, emails: { $exists: true } }, { partnerCode: 1, emailTo: '$emails.opsReportEmailTo', emailCc: '$emails.opsReportEmailCc' }).lean();

        for (const email of partnerEmails) {
            partnerDataHash[email['_id']] = {
                ...partnerDataHash[email['_id']],
                ...email
            }
        }

        const promiseArray = [];

        const excelExportConfig = {
            'Loan Number': 'loanNo',
            'Merchant Code': 'merchantCode',
            'Loan Amount': 'loanAmount',
            'Disbursal Date': 'disbursalDate'
        }

        const month = Constants.MONTH_NAME_BY_INDEX[startDate.getMonth() + 1];
        const year = startDate.getFullYear();


        for (const partnerData in partnerDataHash) {
            const fileName = "LoanDisbursalReport_" + partnerDataHash[partnerData]['partnerCode'] + "_" + month + "_" + year + ".xlsx";
            const dateString = `${month} ${year}`

            promiseArray.push(generateReportExcelAndSendMailToPartner(partnerDataHash[partnerData]['partnerDataArray'], fileName, dateString, excelExportConfig, partnerDataHash[partnerData]['emailTo'], partnerDataHash[partnerData]['emailCc'], "Loan Disbursal Report", "Loans Disbursed Report"));
        }

        await Promise.all(promiseArray);

        output.success = true;
        output.message = "Loan Disbursal Report generated and sent successfully"

    } catch (ex) {
        logger.logs(ex);
        saveErrorLog("loanDisbursalReportForBorrowingPartners", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};

module.exports.loanClosureReportForFinanceAndOps = async function (model) {
    let output = {
        success: false,
        message: "Some Error Occurred",
        status: 200,
    };

    try {

        let partnerIdList = await PartnerMaster.find({ partnerType: "Borrowing Partner", "reports.loanClosureReport": true }, { _id: 1 }).lean();
        partnerIdList = partnerIdList.map(partnerRecord => partnerRecord['_id']);

        // let today = new Date(2022, 03, 01, 0, 0, 0);
        let today = new Date();

        const previousMonthDayCount = new Date(today.getFullYear(), today.getMonth(), 0).getDate();

        let startDate = new Date(today.getMonth() === 0 ? today.getFullYear() - 1 : today.getFullYear(), today.getMonth() === 0 ? 11 : today.getMonth() - 1, 1, 0, 0, 0, 0)

        const startOfTheYear = new Date(2023, 0, 1, 0, 0, 0, 0);

        let endDate = new Date(startDate.getFullYear(), startDate.getMonth(), previousMonthDayCount, 23, 59, 59, 999)

        const query = [
            {
                '$match': {
                    'partnerId': { '$in': partnerIdList },
                    'status': {
                        '$in': [
                            'Close Repaid', 'Close Prepaid'
                        ]
                    },
                    'disbursalDetails.disbursedOn': {
                        '$gte': startOfTheYear
                    },
                    'lastEmiPaidDate': {
                        '$gte': startDate,
                        '$lte': endDate
                    }
                }
            }, {
                '$project': {
                    '_id': 0,
                    'loanNo': 1,
                    'merchantCode': 1,
                    'amount': 1,
                    'borrowingPartnerId': '$partnerId',
                    'lenderDetails': {
                        '$last': '$lenderDetails'
                    },
                    'disbursalDate': '$disbursalDetails.disbursedOn',
                    'lastEmiPaidDate': 1,
                    'loanClosureDate': {
                        '$last': '$flowDetails'
                    }
                }
            }, {
                '$project': {
                    'loanNo': 1,
                    'merchantCode': 1,
                    'amount': 1,
                    'borrowingPartnerId': 1,
                    'lendingPartnerId': '$lenderDetails.lenderPartnerId',
                    'disbursalDate': 1,
                    'lastEmiPaidDate': 1,
                    'loanClosureDate': '$loanClosureDate.insertedOn'
                }
            }, {
                '$project': {
                    'loanNo': 1,
                    'merchantCode': 1,
                    'loanAmount': '$amount',
                    'borrowingPartnerId': 1,
                    'lendingPartnerId': 1,
                    'disbursalDate': 1,
                    'lastEmiPaidDate': 1,
                    'loanClosureDate': 1
                }
            },
            {
                '$group': {
                    '_id': '$borrowingPartnerId',
                    'partnerDataArray': {
                        '$push': {
                            'loanNo': '$loanNo',
                            'merchantCode': '$merchantCode',
                            'loanAmount': '$loanAmount',
                            'disbursalDate': '$disbursalDate',
                            'lastEmiPaidDate': '$lastEmiPaidDate',
                            'loanClosureDate': '$loanClosureDate',
                            'lendingPartnerId': '$lendingPartnerId',
                            'borrowingPartnerId': '$borrowingPartnerId'
                        }
                    }
                }
            }
        ]


        let partnerData = await LoanInfo.aggregate(query);


        let lendingBorrowingPartnerData = await PartnerMaster.find({ partnerType: { $in: ['Borrowing Partner', 'Lending Partner'] }, isActive: true }, { partnerCode: 1, _id: 1 }).lean();


        let lendingBorrowingPartnerDataHash = {}

        for (const partner of lendingBorrowingPartnerData) {
            lendingBorrowingPartnerDataHash[partner['_id']] = partner;
        }
        lendingBorrowingPartnerData = null;

        let partnerDataArray = [];

        for (const data of partnerData) {
            partnerDataArray = [
                ...partnerDataArray,
                ...data['partnerDataArray']
            ]
        }
        partnerData = null;

        for (let index = 0; index < partnerDataArray.length; index++) {

            partnerDataArray[index] = {
                ...partnerDataArray[index],
                borrowingPartnerCode: lendingBorrowingPartnerDataHash[partnerDataArray[index]['borrowingPartnerId']]['partnerCode'],
                lendingPartnerCode: lendingBorrowingPartnerDataHash[partnerDataArray[index]['lendingPartnerId']]['partnerCode']
            }

            delete partnerDataArray[index]['lendingPartnerId']
            delete partnerDataArray[index]['borrowingPartnerId']
        }

        lendingBorrowingPartnerDataHash = null;

        const excelExportConfig = {
            'Loan Number': 'loanNo',
            'Merchant Code': 'merchantCode',
            'Loan Amount': 'loanAmount',
            'Disbursal Date': 'disbursalDate',
            'Last EMI Paid Date': 'lastEmiPaidDate',
            'Loan Closure Date': 'loanClosureDate',
            'Borrowing Partner Code': 'borrowingPartnerCode',
            'Lending Partner Code': 'lendingPartnerCode'
        }

        const month = Constants.MONTH_NAME_BY_INDEX[startDate.getMonth() + 1];
        const year = startDate.getFullYear();

        const mailerConfigRecord = await MailerConfig.findOne({ name: "Loan Closure Report" }, { _id: 0, to: 1, cc: 1 }).lean();
        const { to: emailTo, cc: emailCc } = mailerConfigRecord;

        const fileName = "LoanClosureReport_" + month + "_" + year + ".xlsx";
        const dateString = `${month} ${year}`

        await generateReportExcelAndSendMailToPartner(partnerDataArray, fileName, dateString, excelExportConfig, emailTo, emailCc, "Loan Closure Report", "Loans in Close Repaid and Close Prepaid Report");


        output.success = true;
        output.message = "Loan Closure Report generated and sent successfully"

    } catch (ex) {
        logger.logs(ex);
        saveErrorLog("loanClosureReportForFinanceAndOps", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};