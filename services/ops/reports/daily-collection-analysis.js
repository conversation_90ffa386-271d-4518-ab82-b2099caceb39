
var json2xls = require('json2xls');
const fs = require("fs");
const moment = require('moment');
const config = require("config");
const debug = require("debug")("app:reports");
const { keyBy, groupBy } = require("lodash");
const xlsx = require('xlsx');



const { LoanInfo, LoanInstallments } = require('../../../models/ops/loan/loan');
const { PartnerMaster } = require('../../../models/ops/partner/partner')
const { MailerConfig } = require("../../../models/ops/common/mailer");
const { UserInfo } = require("../../../models/ops/registration/users");
const { LoanDisbursmentTransactions } = require("../../../models/ops/loan/disbursal");

const { saveErrorLog } = require("../common/masters");
const { getDMYFormattedDate, round10, getMailModel } = require("../common/common");
const { sendExternalMail } = require("../common/mailer");
const installmentService = require("../loan/installments");
const Logger = require('../../../middleware/logger');
const logger = new Logger();
const apiPath = __dirname;

async function generateReport(date, isLendingPartner) {

    try {
        let monthStart = new Date(moment(date).startOf('month'))
        let monthEnd = new Date(moment(date).endOf('month'))

        const todayDate = new Date();

        const today = new Date(
            todayDate.getFullYear(),
            todayDate.getMonth(),
            todayDate.getDate(), 5, 30, 0, 0
        );

        let aggregateQuery = [{
            $match: {
                'disbursalDetails.disbursedOn': {
                    $gt: monthStart,
                    $lt: monthEnd
                }
            }
        }, {
            $group: {
                _id: '$partnerId',
                totalAmount: {
                    $sum: '$amount'
                },
                totalCount: {
                    $sum: 1
                }
            }
        }]

        if (isLendingPartner) {
            aggregateQuery = [{
                $match: {
                    'disbursalDetails.disbursedOn': {
                        $gt: monthStart,
                        $lt: monthEnd
                    }
                }
            }, {
                $group: {
                    _id: '$lenderDetails.lenderPartnerId',
                    totalAmount: {
                        $sum: '$amount'
                    },
                    totalCount: {
                        $sum: 1
                    }
                }
            }]

        }

        let loanInfoMeta = await LoanInfo.aggregate(aggregateQuery); /** NO LOAN STATUS CONSIDRED - DATA BASED ON ONLY DISBUSED ON DATE - (ALL PARTNERS) */

        let loanMetaHash = {}

        for (let i = 0; i < loanInfoMeta.length; i++) {
            loanMetaHash[loanInfoMeta[i]._id.toString()] = loanInfoMeta[i]
        }

        let partners = await PartnerMaster.find({ partnerType: isLendingPartner ? "Lending Partner" : "Borrowing Partner", isActive: true, partnerCode: { $nin: ["FUND", "PEassy"] } }).lean()

        let partnerData = []

        let totalPreviousPrincipleOutStanding = 0;

        let totalRow = {
            "Month": "Total",
            "Partner": "",
            "Event Default Rate": 0,
            "Event Default Percentage": 0,
            "Total Principal Default Rate": 0,
            "Total Principal Default Percentage": 0,
            "Outstanding Default Rate": 0,
            "Outstanding Default Percentage": 0,
            "Total Disbursement Count": 0,
            "Total Disbursement Amount": 0,
            "Change in Total Outstanding from last day": 0,
            "Percentage Change compared to last day": 0,
            "Percentage Change compared to total disbursement": 0,
            "Num Delinq": 0,
            "Amt Delinq": 0,
            "Prin Delinq": 0,
            "Num Def": 0,
            "Amt Def": 0,
            "Prin Def": 0,
            "Num Writ": 0,
            "Amt Writ": 0,
            "Prin Writ": 0
        }

        for (let i = 0; i < partners.length; i++) {

            let columns = {
                "Month": moment(date).format('MMMM-YY'),
                "Partner": "",
                "Event Default Rate": 0,
                "Event Default Percentage": 0,
                "Total Principal Default Rate": 0,
                "Total Principal Default Percentage": 0,
                "Outstanding Default Rate": 0,
                "Outstanding Default Percentage": 0,
                "Total Disbursement Count": 0,
                "Total Disbursement Amount": 0,
                "Change in Total Outstanding from last day": 0,
                "Percentage Change compared to last day": "0",
                "Percentage Change compared to total disbursement": 0,
                "Num Delinq": 0,
                "Amt Delinq": 0,
                "Prin Delinq": 0,
                "Num Def": 0,
                "Amt Def": 0,
                "Prin Def": 0,
                "Num Writ": 0,
                "Amt Writ": 0,
                "Prin Writ": 0
            }


            let result = await getPartnerLevelData(partners[i]._id, monthStart, monthEnd, today, isLendingPartner);
            let arreasResult = await getArrearsPartnerLevelData(partners[i]._id, monthStart, monthEnd, isLendingPartner);

            columns["Partner"] = partners[i].displayName;

            if (loanMetaHash[partners[i]._id]) {
                columns["Total Disbursement Count"] = loanMetaHash[partners[i]._id].totalCount;
                columns["Total Disbursement Amount"] = loanMetaHash[partners[i]._id].totalAmount;

                //Total
                totalRow["Total Disbursement Count"] += +loanMetaHash[partners[i]._id].totalCount;
                totalRow["Total Disbursement Amount"] += +loanMetaHash[partners[i]._id].totalAmount
            }

            if (result) {

                columns["Event Default Rate"] = result.outStandingLoanCount;
                columns["Total Principal Default Rate"] = result.totalAmount;
                columns["Outstanding Default Rate"] = result.totalPrincipleOutStanding;
                columns["Change in Total Outstanding from last day"] = toFixedCustom(result.principleDifference);

                //Total
                totalRow["Event Default Rate"] += +result.outStandingLoanCount;
                totalRow["Total Principal Default Rate"] += +result.totalAmount;
                totalRow["Outstanding Default Rate"] += result.totalPrincipleOutStanding;
                totalRow["Change in Total Outstanding from last day"] += toFixedCustom(result.principleDifference);

                if (result.principleDifference && result.previousPrincipleOutStanding) {
                    columns["Percentage Change compared to last day"] = toFixedCustom((result.principleDifference / result.previousPrincipleOutStanding) * 100) + '%';
                }

                if (result.previousPrincipleOutStanding)
                    totalPreviousPrincipleOutStanding += result.previousPrincipleOutStanding;

                if (result.principleDifference && columns["Total Disbursement Amount"]) {
                    columns["Percentage Change compared to total disbursement"] = toFixedCustom((result.principleDifference / columns["Total Disbursement Amount"]) * 100) + '%'
                }

                columns["Event Default Percentage"] = toFixedCustom(((result.outStandingLoanCount / columns["Total Disbursement Count"]) * 100) || 0) + '%';
                columns["Total Principal Default Percentage"] = toFixedCustom(((result.totalAmount / columns["Total Disbursement Amount"]) * 100) || 0) + '%';
                columns["Outstanding Default Percentage"] = toFixedCustom(((result.totalPrincipleOutStanding / columns["Total Disbursement Amount"]) * 100) || 0) + '%';
            }

            if (arreasResult) {
                columns["Num Delinq"] = arreasResult.numDelinq
                columns["Amt Delinq"] = toFixedCustom(arreasResult.amountDelinq)
                columns["Prin Delinq"] = toFixedCustom(arreasResult.prinDelinq)
                columns["Num Def"] = arreasResult.numDefault
                columns["Amt Def"] = toFixedCustom(arreasResult.amountDefault)
                columns["Prin Def"] = toFixedCustom(arreasResult.prinDefault)
                columns["Num Writ"] = arreasResult.numWriteOff
                columns["Amt Writ"] = toFixedCustom(arreasResult.amountWriteOff)
                columns["Prin Writ"] = toFixedCustom(arreasResult.prinWriteOff)

                //Total
                totalRow["Num Delinq"] += arreasResult.numDelinq
                totalRow["Amt Delinq"] += toFixedCustom(arreasResult.amountDelinq)
                totalRow["Prin Delinq"] += toFixedCustom(arreasResult.prinDelinq)
                totalRow["Num Def"] += arreasResult.numDefault
                totalRow["Amt Def"] += toFixedCustom(arreasResult.amountDefault)
                totalRow["Prin Def"] += toFixedCustom(arreasResult.prinDefault)
                totalRow["Num Writ"] += arreasResult.numWriteOff
                totalRow["Amt Writ"] += toFixedCustom(arreasResult.amountWriteOff)
                totalRow["Prin Writ"] += toFixedCustom(arreasResult.prinWriteOff)

            }

            //Total
            totalRow["Percentage Change compared to last day"] = toFixedCustom(totalPreviousPrincipleOutStanding && ((totalRow["Change in Total Outstanding from last day"] / totalPreviousPrincipleOutStanding) * 100) || 0) + '%';
            totalRow["Percentage Change compared to total disbursement"] = toFixedCustom(((totalRow["Change in Total Outstanding from last day"] / totalRow["Total Disbursement Amount"]) * 100) || 0) + '%'
            totalRow["Event Default Percentage"] = toFixedCustom(((totalRow["Event Default Rate"] / totalRow["Total Disbursement Count"]) * 100) || 0) + '%';
            totalRow["Total Principal Default Percentage"] = toFixedCustom(((totalRow["Total Principal Default Rate"] / totalRow["Total Disbursement Amount"]) * 100) || 0) + '%';
            totalRow["Outstanding Default Percentage"] = toFixedCustom(((totalRow["Outstanding Default Rate"] / totalRow["Total Disbursement Amount"]) * 100) || 0) + '%';

            if (result || arreasResult || loanMetaHash[partners[i]._id])
                partnerData.push(columns)
        }

        partnerData.push(totalRow)


        return partnerData;

    } catch (ex) {
        saveErrorLog("generateReport", apiPath, {}, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }
}

async function getPartnerLevelData(partnerId, startDate, endDate, today, isLendingPartner) {

    try {

        let defaultLoansAggregate = [{
            $match: {
                partnerId: partnerId,
                status: {
                    $in: [
                        'Loan In Progress',
                        'Written-Off'
                    ]
                },
                'disbursalDetails.disbursedOn': {
                    $gt: startDate,
                    $lt: endDate
                }
            }
        }, {
            $addFields: {
                daysDifference: {
                    $dateDiff: {
                        startDate: '$nextEmiRepaymentDate',
                        endDate: today,
                        unit: 'day'
                    }
                }
            }
        }, {
            $match: {
                daysDifference: {
                    $gte: 7
                }
            }
        }, {
            $group: {
                _id: '$partnerId',
                principleOutStanding: {
                    $sum: "$principalAmount.outstanding"
                },
                totalAmount: {
                    $sum: '$amount'
                },
                loanIds: {
                    $push: '$_id'
                }
            }
        }]


        if (isLendingPartner) {
            defaultLoansAggregate = [{
                $match: {
                    "lenderDetails.lenderPartnerId": partnerId,
                    status: {
                        $in: [
                            'Loan In Progress',
                            'Written-Off'
                        ]
                    },
                    'disbursalDetails.disbursedOn': {
                        $gt: startDate,
                        $lt: endDate
                    }
                }
            }, {
                $addFields: {
                    daysDifference: {
                        $dateDiff: {
                            startDate: '$nextEmiRepaymentDate',
                            endDate: today,
                            unit: 'day'
                        }
                    }
                }
            }, {
                $match: {
                    daysDifference: {
                        $gte: 7
                    }
                }
            }, {
                $group: {
                    _id: '$lenderDetails.lenderPartnerId',
                    principleOutStanding: {
                        $sum: "$principalAmount.outstanding"
                    },
                    totalAmount: {
                        $sum: '$amount'
                    },
                    loanIds: {
                        $push: '$_id'
                    }
                }
            }]
        }


        let result = await LoanInfo.aggregate(defaultLoansAggregate)

        if (!result.length)
            return null;

        let dateBefore = new Date(today)
        dateBefore = new Date(dateBefore.setDate(new Date().getDate() - 1))
        dateBefore.setHours(0, 0, 0, 0);


        let loanInfoIdsQuery = {
            partnerId: partnerId,
            'disbursalDetails.disbursedOn': {
                $gt: startDate,
                $lt: endDate
            }
        }

        if (isLendingPartner) {
            loanInfoIdsQuery = {
                "lenderDetails.lenderPartnerId": partnerId,
                'disbursalDetails.disbursedOn': {
                    $gt: startDate,
                    $lt: endDate
                }
            }
        }
        let loanDisbursedParticularMonth = await LoanInfo.find(loanInfoIdsQuery, { _id: 1 })

        let loanInstallments = []

        if (loanDisbursedParticularMonth && loanDisbursedParticularMonth.length) {

            let previousOutstandingQuery = [
                [{
                    $match: {
                        loanId: { $in: loanDisbursedParticularMonth.map(item => item._id) },
                        partnerId,
                        $or: [
                            {
                                status: 'Pending'
                            },
                            {
                                status: 'Received',
                                transactionDate: {
                                    $gte: dateBefore
                                }
                            }
                        ]
                    }
                }, {
                    $sort: {
                        dueDate: 1
                    }
                }, {
                    $group: {
                        _id: '$loanId',
                        installement: {
                            $first: '$$ROOT'
                        },
                        totalPrincipleOutStanding: {
                            $sum: "$installmentPrincipal"
                        }
                    }
                }, {
                    $addFields: {
                        daysDifference: {
                            $dateDiff: {
                                startDate: '$installement.dueDate',
                                endDate: today,
                                unit: 'day'
                            }
                        }
                    }
                }, {
                    $match: {
                        daysDifference: {
                            $gte: 8
                        }
                    }
                }, {
                    $group: {
                        _id: null,
                        totalPrincipleOutStanding: {
                            $sum: "$totalPrincipleOutStanding"
                        }
                    }
                }
                ]
            ]

            if (isLendingPartner) {
                previousOutstandingQuery = [
                    [{
                        $match: {
                            loanId: { $in: loanDisbursedParticularMonth.map(item => item._id) },
                            $or: [
                                {
                                    status: 'Pending'
                                },
                                {
                                    status: 'Received',
                                    transactionDate: {
                                        $gte: dateBefore
                                    }
                                }
                            ]
                        }
                    }, {
                        $sort: {
                            dueDate: 1
                        }
                    }, {
                        $group: {
                            _id: '$loanId',
                            installement: {
                                $first: '$$ROOT'
                            },
                            totalPrincipleOutStanding: {
                                $sum: "$installmentPrincipal"
                            }
                        }
                    }, {
                        $addFields: {
                            daysDifference: {
                                $dateDiff: {
                                    startDate: '$installement.dueDate',
                                    endDate: today,
                                    unit: 'day'
                                }
                            }
                        }
                    }, {
                        $match: {
                            daysDifference: {
                                $gte: 8
                            }
                        }
                    }, {
                        $group: {
                            _id: null,
                            totalPrincipleOutStanding: {
                                $sum: "$totalPrincipleOutStanding"
                            }
                        }
                    }
                    ]
                ]
            }

            loanInstallments = await LoanInstallments.aggregate(previousOutstandingQuery)
        }

        // let loanInstallments = await LoanInstallments.aggregate([
        //     {
        //         $match: {
        //             loanId: { $in: result[0].loanIds },
        //             $or: [{ status: "Pending" }, { transactionDate: { $gte: dateBefore } }]
        //         }
        //     },
        //     {
        //         $group: {
        //             _id: null,
        //             totalPrincipleOutStanding: {
        //                 $sum: "$installmentPrincipal"
        //             }
        //         }
        //     }
        // ])

        let resultObj = {
            outStandingLoanCount: result.length ? result[0].loanIds.length : 0,
            totalAmount: result.length ? result[0].totalAmount : 0,
            totalPrincipleOutStanding: result.length ? result[0].principleOutStanding : 0,
            principleDifference: (result.length ? result[0].principleOutStanding : 0) - (loanInstallments.length ? loanInstallments[0].totalPrincipleOutStanding : 0),
            previousPrincipleOutStanding: (loanInstallments.length ? loanInstallments[0].totalPrincipleOutStanding : 0),
            firstPercentageValue: 0,
            secondPercentageValue: 0
        }

        return resultObj;
    } catch (ex) {
        saveErrorLog("getPartnerLevelData", apiPath, {}, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }

}

async function getArrearsPartnerLevelData(partnerId, startDate, endDate, isLendingPartner) {

    try {

        let loanInfoAggregateQuery = [
            {
                $match: {
                    partnerId,
                    'disbursalDetails.disbursedOn': {
                        $gt: startDate,
                        $lt: endDate
                    },
                    "arrears.0": { $exists: true },
                    "arrears.delinquency": { $in: ["Delinquent", "Default", "Write-Off"] }
                }
            },
            {
                $project: {
                    arrears: 1,
                    interestRate: 1,
                    amount: 1,
                    tenure: 1
                }
            }
        ]

        if (isLendingPartner) {
            loanInfoAggregateQuery = [
                {
                    $match: {
                        "lenderDetails.lenderPartnerId": partnerId,
                        'disbursalDetails.disbursedOn': {
                            $gt: startDate,
                            $lt: endDate
                        },
                        "arrears.0": { $exists: true },
                        "arrears.delinquency": { $in: ["Delinquent", "Default", "Write-Off"] }
                    }
                },
                {
                    $project: {
                        arrears: 1,
                        interestRate: 1,
                        amount: 1,
                        tenure: 1
                    }
                }
            ]
        }

        let loans = await LoanInfo.aggregate(loanInfoAggregateQuery)

        if (!loans.length)
            return null

        let resultObj = {
            numDelinq: 0,
            amountDelinq: 0,
            prinDelinq: 0,
            numDefault: 0,
            amountDefault: 0,
            prinDefault: 0,
            numWriteOff: 0,
            amountWriteOff: 0,
            prinWriteOff: 0
        }

        for (let i = 0; i < loans.length; i++) {

            let loan = loans[i]
            let isDelinquentAccurs = false;
            let isDefaultAccurs = false;
            let isWriteOffAccurs = false;

            for (let j = 0; j < loan.arrears.length; j++) {

                let arrear = loan.arrears[j]

                if (arrear.delinquency == "Delinquent" && !isDelinquentAccurs) {
                    resultObj.numDelinq += 1;
                    resultObj.amountDelinq += arrear.amount * (1 + (loan.interestRate / 100) * loan.tenure / 365);
                    resultObj.prinDelinq += arrear.amount;
                    isDelinquentAccurs = true
                }

                if (arrear.delinquency == "Default" && !isDefaultAccurs) {
                    resultObj.numDefault += 1;
                    resultObj.amountDefault += arrear.amount * (1 + (loan.interestRate / 100) * loan.tenure / 365);
                    resultObj.prinDefault += arrear.amount;
                    isDefaultAccurs = true
                }

                if (arrear.delinquency == "Write-Off" && !isWriteOffAccurs) {
                    resultObj.numWriteOff += 1;
                    resultObj.amountWriteOff += arrear.amount * (1 + (loan.interestRate / 100) * loan.tenure / 365);
                    resultObj.prinWriteOff += arrear.amount;
                    isWriteOffAccurs = true
                }
            }
        }

        return resultObj;
    } catch (ex) {
        saveErrorLog("getArrearsPartnerLevelData", apiPath, {}, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }
}

async function generateDailyCollectionAnalysisOld(body) {

    try {

        let monthsToAddYear = ["January", "February", "March"]
        let currentMonth = moment().format('MMMM')

        let date = new Date()
        let lastYear = date.getFullYear() - (monthsToAddYear.includes(currentMonth) ? 2 : 1)

        let startDate = new Date(`04/01/${lastYear}`);
        let endDate = new Date();

        let noOfMonthDifference = monthDiff(startDate, endDate) + 1;
        let reportData = []

        for (let i = 0; i < noOfMonthDifference; i++) {

            let date = new Date(new Date(startDate).setMonth(startDate.getMonth() + i))

            let result = await generateReport(date)

            if (result) {
                reportData = reportData.concat(result);
                reportData = reportData.concat([{}, {}])
            }

        }

        var xls = json2xls(reportData);

        const fileName = "daily_collections_analysis_" + moment().format("DD_MM_YYYY") + ".xlsx";
        const filePath = config.get("exportToExcelPath.generalReports") + fileName;
        fs.writeFileSync(filePath, xls, 'binary');

        const mailerConfig = await MailerConfig.findOne({ name: "Daily Collection Analysis Report" })

        if (mailerConfig) {
            let mailModel = getMailModel();

            mailModel.attachmentName = fileName
            mailModel.attachmentPath = filePath
            mailModel.to = mailerConfig.to
            mailModel.cc = mailerConfig.cc
            mailModel.date = moment().format("DD/MM/YYYY")

            if (body && body.to) {
                mailModel.to = body.to
            }

            const mailResponse = await sendExternalMail(mailModel, "Daily Collection Analysis", "Daily Collection Analysis Report");

            if (mailResponse.success) {
                fs.unlink(filePath, function () {
                });
            }
        }

        return {
            success: true,
            data: [],
            message: "Success",
            status: 200
        }
    } catch (ex) {
        saveErrorLog("generateDailyCollectionAnalysis", apiPath, {}, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        return {
            success: false,
            data: {},
            message: "Some Error Occured",
            status: 200
        }

    }
}

async function generateDailyCollectionAnalysis(body) {

    try {

        let monthsToAddYear = ["January", "February", "March"]
        let currentMonth = moment().format('MMMM')

        let date = new Date()
        let lastYear = date.getFullYear() - (monthsToAddYear.includes(currentMonth) ? 2 : 1)

        let startDate = new Date(`04/01/${lastYear}`);
        let endDate = new Date();

        let noOfMonthDifference = monthDiff(startDate, endDate) + 1;
        let reportData = []
        let lendingPartnerReportData = []

        for (let i = 0; i < noOfMonthDifference; i++) {

            let date = new Date(new Date(startDate).setMonth(startDate.getMonth() + i))

            let result = await generateReport(date)

            if (result && result.length) {

                for (let k = 0; k < result.length; k++) {

                    let item = result[k]

                    let keys = Object.keys(item);

                    if (i == 0 && !reportData.length)
                        reportData.push(keys);

                    if (keys.length) {
                        let row = []
                        keys.map(key => {
                            row.push(item[key])
                        })
                        reportData.push(row)
                    }
                }

                if (reportData && reportData.length) {
                    let emptyRow = reportData[0].map(item => "")
                    reportData.push(emptyRow)
                }
            }
        }

        for (let i = 0; i < noOfMonthDifference; i++) {

            let date = new Date(new Date(startDate).setMonth(startDate.getMonth() + i))

            let result = await generateReport(date, true)

            if (result && result.length) {

                for (let k = 0; k < result.length; k++) {

                    let item = result[k]

                    let keys = Object.keys(item);

                    if (i == 0 && !lendingPartnerReportData.length)
                        lendingPartnerReportData.push(keys);

                    if (keys.length) {
                        let row = []
                        keys.map(key => {
                            row.push(item[key])
                        })

                        lendingPartnerReportData.push(row)
                    }
                }

                if (lendingPartnerReportData && lendingPartnerReportData.length) {
                    let emptyRow = lendingPartnerReportData[0].map(item => "")
                    lendingPartnerReportData.push(emptyRow)
                }
            }

        }
        const fileName = "daily_collections_analysis_" + moment().format("DD_MM_YYYY") + ".xlsx";
        const filePath = config.get("exportToExcelPath.generalReports") + fileName;

        const workbook = xlsx.utils.book_new();
        const sheet1 = xlsx.utils.aoa_to_sheet(reportData);
        const sheet2 = xlsx.utils.aoa_to_sheet(lendingPartnerReportData);
        xlsx.utils.book_append_sheet(workbook, sheet1, 'Borrowing Partner');
        xlsx.utils.book_append_sheet(workbook, sheet2, 'Lending Partner');
        // Save the workbook to a file
        xlsx.writeFile(workbook, filePath);

        const mailerConfig = await MailerConfig.findOne({ name: "Daily Collection Analysis Report" })

        if (mailerConfig) {
            let mailModel = getMailModel();

            mailModel.attachmentName = fileName
            mailModel.attachmentPath = filePath
            mailModel.to = mailerConfig.to
            mailModel.cc = mailerConfig.cc
            mailModel.date = moment().format("DD/MM/YYYY")

            if (body && body.to) {
                mailModel.to = body.to
            }

            const mailResponse = await sendExternalMail(mailModel, "Daily Collection Analysis", "Daily Collection Analysis Report");

            if (mailResponse.success) {
                fs.unlink(filePath, function () {
                });
            }
        }

        return {
            success: true,
            data: [],
            message: "Success",
            status: 200
        }
    } catch (ex) {
        saveErrorLog("generateDailyCollectionAnalysis", apiPath, {}, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        return {
            success: false,
            data: {},
            message: "Some Error Occured",
            status: 200
        }

    }
}

function monthDiff(dateFrom, dateTo) {
    return dateTo.getMonth() - dateFrom.getMonth() +
        (12 * (dateTo.getFullYear() - dateFrom.getFullYear()))
}

function toFixedCustom(n) {
    if (Number(n) === n && n % 1 !== 0) { // Is float
        return +n.toFixed(2)
    } else {
        return n
    }
}

async function setDates(model) {
    let output = {
        data: null,
        success: false,
        message: "Some Error Occurred",
        status: 200,
    };
    try {
        let fromDate = new Date(Date.now());
        let toDate = new Date(Date.now());
        let isEmailSend = model.isEmailSend;
        const millisecondsIn7Days = 604800000;

        const currentYear = fromDate.getFullYear();
        const currentMonth = fromDate.getMonth();
        const previousMonth = currentMonth == 0 ? 11 : currentMonth - 1;
        const previousYear = currentMonth == 0 ? currentYear - 1 : currentYear;

        if (model.type) {
            switch (model.type.toLowerCase()) {
                case "w1":
                    fromDate = new Date(currentYear, currentMonth, 1, 0, 0, 0, 0);
                    toDate = new Date(currentYear, currentMonth, 7, 23, 59, 59, 459);
                    break;
                case "w2":
                    fromDate = new Date(currentYear, currentMonth, 8, 0, 0, 0, 0);
                    toDate = new Date(currentYear, currentMonth, 15, 23, 59, 59, 459);
                    break;
                case "w3":
                    fromDate = new Date(currentYear, currentMonth, 16, 0, 0, 0, 0);
                    toDate = new Date(currentYear, currentMonth, 23, 23, 59, 59, 459);
                    break;
                case "w4":
                    fromDate = new Date(previousYear, previousMonth, 24, 0, 0, 0, 0);
                    toDate = new Date(previousYear, previousMonth + 1, 0, 23, 59, 59, 459);
                    break;
                case "weekly":
                    fromDate = new Date(fromDate.getTime() - millisecondsIn7Days);
                    fromDate = new Date(fromDate.getFullYear(), fromDate.getMonth(), fromDate.getDate(), 0, 0, 0, 0);
                    toDate = new Date(toDate.getFullYear(), toDate.getMonth(), toDate.getDate(), 23, 59, 59, 459);
                    break;
                default://Monthly
                    fromDate = new Date(previousYear, previousMonth, 1, 0, 0, 0, 0);
                    toDate = new Date(previousYear, previousMonth + 1, 0, 23, 59, 59, 459);
                    break;
            }
        }

        if (model.fromDate) {
            fromDate = new Date(model.fromDate);
            fromDate = new Date(fromDate.getFullYear(), fromDate.getMonth(), fromDate.getDate(), 0, 0, 0, 0);
        }

        if (model.toDate) {
            toDate = new Date(model.toDate);
            toDate = new Date(toDate.getFullYear(), toDate.getMonth(), toDate.getDate(), 23, 59, 59, 459);
        }

        if (isEmailSend == undefined) isEmailSend = true;

        output = {
            data: {
                fromDate,
                toDate,
                isEmailSend
            },
            success: true,
            message: "Fetehced Successfully",
            status: 200,
        };

    } catch (ex) {
        logger.logs(ex);
        saveErrorLog("setDates", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }

    return output;
}

module.exports = {
    generateDailyCollectionAnalysis
}