const XAmbit = require('../../../partnerApiCalls/XAmbit');
const { BankStatementAnalysis } = require("../../../models/ops/bank-statement/BankStatementAnalysis");
const { PDFS3LocationURL } = require("../../../models/ops/bank-statement/PDFS3LocationURL");
const uploadToAws = require("../../../models/ops/aws/aws-s3");
const fs = require('fs');



class Bankstatement {

    //create new analysis
    static async createNewAnalysis(pdfFile, userId, res, fromUpload = false) {
        try {
            const fileName = pdfFile.originalname;
            const filePath = pdfFile.path;
            //upload to AWS-S3 Bucket
            const s3Url = await uploadToAws.uploadBankStatementsToAws(fileName, filePath, userId);
            //storing the s3url into db
            const Source = "Request coming from Xambit";
            s3Url.userId = userId;
            s3Url.Source = Source;
            console.log("db creting now for pdf location url")
            const result = await PDFS3LocationURL.create(s3Url);
            //fetching s3url from db
            /*
            let isDocumentAlreadyExist = await PDFS3LocationURL.findOne({ userId: userId });
            if (isDocumentAlreadyExist) {
                let collectionId = await new XAmbit().createNewAnalysis();
                const uniqueId = collectionId.data.id;
                let response = await new XAmbit().uploadFile(fileName, filePath, uniqueId);
                const collectionID = response.data.collection;
                PDFS3LocationURL.updateOne(
                    { _id: result._id },
                    { $set: { 'data.file': response.data.file, 'collectionID': collectionID, 'data.time': response.data.time } },
                    (err, result) => {
                        if (err) {
                            console.error(err);
                        } else {
                            console.log(result);
                        }
                    }
                );
                */
            fs.unlinkSync(filePath);
            console.log('Local file removed:', filePath);


            if (fromUpload) {
                // Return the S3 URL if the request is from uploadInvoice or uploadBankStatement
                return { s3url: result.response.Location };  // Assuming `file` contains the URL in `s3Url`
            }
            return res.status(200).send('Success: File uploaded and stored in database.');
            //return { data: response.data };
            //  };
        } catch (uploadError) {
            console.error('Error uploading file to S3:', uploadError);
            return res.status(500).send('Internal Server Error');
        }
    };




    //bankstatement getdatanalysis
    static async getDataAnalysis(collectionid) {
        try {
            let isDocumentAlreadyExistinDB = await BankStatementAnalysis.findOne({ collectionID: collectionid });
            console.log("isDocumentAlreadyExistinDB", isDocumentAlreadyExistinDB)
            if (isDocumentAlreadyExistinDB) {
                return {
                    status: 200,
                    data: isDocumentAlreadyExistinDB,
                    message: 'Bank statement details fetched successfully'
                };
            } else if (collectionid) {
                return {
                    status: 404,
                    message: 'No analysis found in the database'
                };
            }
            /*  } else if (collectionid) {
                  let response;
                  response = await new XAmbit().getDataAnalysis(collectionid);
                  return {
                      data: response
                  };
                   }
                     */
        } catch (error) {
            return {
                status: 500,
                error: 'Internal Server Error'
            };
        }
    };

}

module.exports = Bankstatement
