const mongoose = require("mongoose");
const config = require("config");
const Crypto = require("crypto-js");
const { UserInfo, UserPartnerInfo } = require("../../../models/ops/registration/users");
const { PartnerMaster, PartnerConfig } = require("../../../models/ops/partner/partner");
const { LoanInfo } = require("../../../models/ops/loan/loan");

module.exports.loanOfferSTP = async function (req) {

    let output = {
        data: null,
        success: false,
        message: "Internal Error",
        status: 400
    }
    try {

        const signature = req.body['signature'];
        const shortAuthKey = req.body['shortAuthKey'];
        const merchantCode = req.body["merchantCode"];
        const offerId = req.body["offerId"];
        const amount = req.body["amount"];

        if (!signature || !shortAuthKey || !merchantCode || !offerId || !amount) {
            output.message = "Invalid Input"
            return output;
        }

        let { error, response } = await validateHmac(req);

        if (error) {
            output.message = error.message
            return output;
        }

        let userPartnerInfo = await UserPartnerInfo.findOne({ merchantCode, partnerId: response.partner._id.toString() });

        if (!userPartnerInfo) {
            output.message = "Invalid merchantCode";
            return output;
        }

        let userInfo = await UserInfo.findOne({ _id: userPartnerInfo.userId });
        const token = userInfo.generateAuthToken({ loggedInFrom: "RedirectAuthLink", partnerId: userPartnerInfo.partnerId });

        let partnerBuild = await preparePartnerRes(response.partner);

        if (!partnerBuild) {
            output.message = "Partner Info Not Found";
            return output;
        }

        let loan = await LoanInfo.findOne({ trxnId: offerId });

        if (loan && loan.status === "Initiated" && loan.isDraft) {
            loan.status = "Draft";
            loan.updatedOn = new Date()

            loan.flowDetails.push({
                Step: "Draft",
                changedBy: userInfo._id,
                changedByRole: "User",
                insertedOn: new Date()
            });

            await loan.save();
        }


        output.data = {
            user: {
                userId: userInfo._id,
                name: userInfo.userDetails.firstName + " " + userInfo.userDetails.lastName,
                token: token,
                loggedInFrom: ""
            },
            partner: partnerBuild
        }
        output.success = true;
        output.message = "Success";
        output.status = 200
        return output;

    } catch (error) {
        return output;
    }
}

async function preparePartnerRes(partner) {

    try {
        const partneRecord = {
            _id: partner._id,
            partnerName: partner.partnerName,
            isloanDisabled: partner.isloanDisabled,
            isReviewDisabled: partner.isReviewDisabled,
            callBackUrl: partner.callBackUrl,
            displayLoanTabs: partner.displayLoanTabs,
            showHeaderMenu: partner.showHeaderMenu,
            showFooterMenu: partner.showFooterMenu,
            displayLogo: partner.displayLogo,
            logoFileName: partner.logoFileName,
            bankValidationAmount: null,
            eNachAmount: null,
            disbursalType: null,
            collectionType: null,
            shortauthKey: partner.shortauthKey,
            contractSuccessCallBackUrl: partner.contractSuccessCallBackUrl,
            eNachErrorCallBackUrl: partner.eNachErrorCallBackUrl,
            hideFFLogo: partner.hideFFLogo,
            questionnairre: null
        }

        const partnerConfig = await PartnerConfig.findOne({ partnerId: mongoose.Types.ObjectId(partner._id) });
        if (partnerConfig) {
            partneRecord.bankValidationAmount = partnerConfig.bankValidationAmount;
            partneRecord.eNachAmount = partnerConfig.eNachAmount;
            partneRecord.disbursalType = partnerConfig.disbursalType;
            partneRecord.collectionType = partnerConfig.collectionType;
            partneRecord.questionnairre = partnerConfig.questionnairre
        }

        return partneRecord
    } catch (error) {
        return null
    }
}

async function validateHmac(req) {

    try {

        let { signature, shortAuthKey, merchantCode, offerId, amount } = req.body;

        const pMaster = await PartnerMaster.findOne({ shortauthKey: shortAuthKey });

        if (!pMaster)
            return { error: { message: "UnAuthorized" }, response: null }

        const message = `${merchantCode}|${offerId}|${amount}|${shortAuthKey}`;

        const calculatedSignature = Crypto.HmacSHA256(message, pMaster.secretKey).toString();

        if (calculatedSignature !== signature)
            return { error: { message: "UnAuthorized" }, response: null }

        return { error: null, response: { partner: pMaster } }

    } catch (error) {
        return { error: { message: "UnAuthorized" }, response: null }
    }

}