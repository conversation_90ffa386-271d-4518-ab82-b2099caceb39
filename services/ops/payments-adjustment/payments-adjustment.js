const config = require("config");
const { saveErrorLog } = require("../../../services/ops/common/masters");
const { PaymentCollection } = require("../../../models/ops/loan/postDisbursal");
const { AgentCollections } = require("../../../models/ops/agent/agent");
const fetch = require("node-fetch");
const AuthSignature = require('../../auth-signature/auth-signature');
const apiPath = __dirname;

async function paymentAdjustments(model, req) {
    let output = {
        success: false,
        message: "Error Occurred",
        status: 500
    }
    try {

        let oldEmis = model.oldEmis
        let newEmis = model.newEmis
        let referenceNo = model.referenceNo

        if (!oldEmis ||
            !oldEmis.length ||
            !newEmis ||
            !newEmis.length ||
            oldEmis.length != newEmis.length) {

            output.message = "oldEmis / newEmis Missing"
            return output;
        }

        if (!referenceNo) {
            output.message = "Reference No Missing"
            return output
        }

        //Fundfina database
        await AgentCollections.updateOne({ paymentReferenceNumber: referenceNo }, {
            $set: {
                emis: newEmis
            }
        })

        let paymentRes = await PaymentCollection.find({ referenceNo, emiNumber: { $in: oldEmis } });

        if (!paymentRes || paymentRes.length != oldEmis.length) {
            output.message = "Invalid referenceNo"
            return output
        }

        let emiMapper = {}

        for (let i = 0; i < newEmis.length; i++) {
            emiMapper[+oldEmis[i]] = +newEmis[i];
        }

        let bulkWriteObj = []

        for (let i = 0; i < paymentRes.length; i++) {
            let newEmi = emiMapper[paymentRes[i].emiNumber];
            bulkWriteObj.push({
                updateOne: {
                    filter: { referenceNo: referenceNo, emiNumber: paymentRes[i].emiNumber },
                    update: {
                        $set: {
                            emiNumber: newEmi,
                            isActive: true
                        }
                    }
                }
            })
        }

        console.log(bulkWriteObj)

        await PaymentCollection.bulkWrite(bulkWriteObj);

        let { error, response } = await updatePaymentLinkEdis(referenceNo, newEmis);
        if (error) {
            output.message = error;
            return output
        }

        output = {
            data: {},
            success: true,
            message: "Success",
            status: 200
        }

        return output;
    } catch (error) {
        saveErrorLog("sendNotificationPaymentSuccess", apiPath, {}, error.stack ? error.stack.toString().replace(/\r?\n|\r/g, " ") : error);
        return output
    }
}

async function updatePaymentLinkEdis(referenceNo, emis) {
    try {
        let model = {
            referenceNo,
            emis
        }
        let paymentService = config.get("paymentService");
        const signature = AuthSignature.generateAuthSignature(model, paymentService.authToken)
        let headers = {
            'Content-Type': 'application/json',
            "auth-signature": signature,
            "account-id": paymentService.accountId
        }
        let url = `${paymentService.baseUrl}${paymentService.paymentsAdjustments}`;
        let response = await fetch(url, { method: 'POST', headers, body: JSON.stringify(model) })
        let data = await response.json();
        if (data && data.success) {
            return { error: null, response: data.data || {} }
        } else
            return { error: "Update Payment failed", response: null }

    } catch (error) {
        saveErrorLog("updatePaymentLinkEdis", apiPath, {}, error.stack ? error.stack.toString().replace(/\r?\n|\r/g, " ") : error);
        return { error: error.stack ? error.stack.toString().replace(/\r?\n|\r/g, " ") : error, response: null }

    }
}


module.exports = {
    paymentAdjustments
}