const mongoose = require("mongoose");
const config = require("config");
const { callApi, updateUserAndLoanSteps, buildMobileNumberObject } = require("../common/common");
const { logUserOtp } = require("../common/masters");
const { smsLog } = require("../../../models/ops/registration/verification");
const { OtpInfo } = require("../../../models/ops/common/master");
const { UserInfo } = require("../../../models/ops/registration/users");
const { LoanInfo } = require("../../../models/ops/loan/loan");
const { DND, PartnerConfig } = require("../../../models/ops/partner/partner");
const apiPath = __dirname;
const { saveErrorLog } = require("../../../services/ops/common/masters");
const { verifyOtp } = require('../otp-service/otp-service');
const { OTP_CHANNELS, NOTIFICATION_TYPES } = require('../../../services/ops/common/constants').Constants
module.exports.sendMobileOTP = async function (mobileNo) {
  const url = config.get("keys.2Factor") + "V1/" + config.get("keys.2FactorApiKey") + "/SMS/" + mobileNo + "/AUTOGEN/SMS OTP";

  try {

    const response = await callApi("GET", url, "", "");

    if (response) {
      const smsLogs = new smsLog({
        url: url,
        methodName: "AUTOGEN",
        mobileNo: mobileNo,
        status: response.Status,
        response: response.Details,
        responseBody: JSON.stringify(response)
      });

      await smsLogs.save();

      if (response.Status.toLowerCase() == "success") {
        return {
          success: true,
          data: response.Details,
          message: "OTP sent successfully",
          status: 200,
        };
      } else {
        return {
          success: false,
          data: response.Details,
          message: "Some error occurred",
          status: 500,
        };
      }
    } else {
      return {
        success: false,
        data: response.Details,
        message: "Some error occurred",
        status: 500,
      };
    }
  }
  catch (ex) {
    const smsLogs = new smsLog({
      url: url,
      methodName: "AUTOGEN",
      mobileNo: mobileNo,
      status: "Error",
      response: "",
      responseBody: "",
      exception: ex.message
    });

    await smsLogs.save();

    return {
      success: false,
      data: "",
      message: "Some error occurred",
      status: 500,
    };
  }
};

module.exports.verifyOTP = async function (mobileNo, otp) {
  const detailId = await smsLog
    .findOne({ mobileNo: mobileNo, methodName: "AUTOGEN" })
    .sort("-creationDate")
    .select("response");

  try {

    const url =
      config.get("keys.2Factor") + "V1/" + config.get("keys.2FactorApiKey") + "/SMS/VERIFY/" + detailId.response + "/" + otp;

    const response = await callApi("GET", url, "", "");

    if (response) {
      const smsLogs = new smsLog({
        url: url,
        methodName: "VERIFY",
        mobileNo: mobileNo,
        status: response.Status,
        response: response.Details,
        responseBody: JSON.stringify(response)
      });

      await smsLogs.save();

      if (response.Status.toLowerCase() == "success") {
        return {
          success: true,
          data: "",
          message: response.Details,
          status: 200,
        };
      } else {
        return {
          success: false,
          data: "",
          message: response.Details,
          status: 400,
        };
      }
    } else {
      return {
        success: false,
        data: response.Details,
        message: "Some error occurred",
        status: 500,
      };
    }
  }
  catch (ex) {
    const smsLogs = new smsLog({
      url: url,
      methodName: "VERIFY",
      mobileNo: mobileNo,
      status: "Error",
      response: "",
      responseBody: "",
      exception: ex.message
    });

    await smsLogs.save();

    return {
      success: false,
      data: "",
      message: "Some error occurred",
      status: 500,
    };
  }
};

module.exports.sendCustomSms = async function (smsRequest) {
  let response = null;

  try {
    const mobileNo = smsRequest.To && smsRequest.To.length === 10 ? "91" + smsRequest.To : smsRequest.To;

    let url = `${config.get("keys.2Factor")}R1/?module=TRANS_SMS&apikey=${config.get("keys.2FactorApiKey")}&to=${mobileNo}&from=${config.get("keys.from")}&templatename=${encodeURIComponent(smsRequest.TemplateName)}`;

    if (smsRequest.VAR1)
      url += `&var1=${encodeURIComponent(smsRequest.VAR1)}`;

    if (smsRequest.VAR2)
      url += `&var2=${encodeURIComponent(smsRequest.VAR2)}`;

    if (smsRequest.VAR3)
      url += `&var3=${encodeURIComponent(smsRequest.VAR3)}`;

    if (smsRequest.VAR4)
      url += `&var4=${encodeURIComponent(smsRequest.VAR4)}`;

    if (smsRequest.VAR5)
      url += `&var5=${encodeURIComponent(smsRequest.VAR5)}`;

    if (smsRequest.VAR6)
      url += `&var6=${encodeURIComponent(smsRequest.VAR6)}`;

    if (smsRequest.VAR7)
      url += `&var7=${encodeURIComponent(smsRequest.VAR7)}`;

    if (smsRequest.VAR8)
      url += `&var8=${encodeURIComponent(smsRequest.VAR8)}`;

    if (smsRequest.VAR9)
      url += `&var9=${encodeURIComponent(smsRequest.VAR9)}`;

    if (smsRequest.VAR10)
      url += `&var10=${encodeURIComponent(smsRequest.VAR10)}`;

    if (smsRequest.VAR11)
      url += `&var11=${encodeURIComponent(smsRequest.VAR11)}`;

    if (smsRequest.VAR12)
      url += `&var12=${encodeURIComponent(smsRequest.VAR12)}`;

    response = await callApi("GET", url, "", "");

    if (response) {
      const smsLogs = new smsLog({
        url: url,
        methodName: smsRequest.Type,
        mobileNo: smsRequest.To,
        status: response.Status,
        response: response.Details,
        responseBody: JSON.stringify(response)
      });

      await smsLogs.save();

      url = null;

      if (response.Status.toLowerCase() == "success") {
        return {
          success: true,
          data: response.Details,
          message: `OTP sent sucessfully`,
          status: 200,
        };
      } else {
        return {
          success: false,
          data: response.Details,
          message: "Some error occurred",
          status: 500,
        };
      }
    } else {
      return {
        success: false,
        data: response.Details,
        message: "Some error occurred",
        status: 500,
      };
    }
  } catch (exSMS) {
    saveErrorLog("sendCustomSms", apiPath, smsRequest, "sendCustomSms failed due to error " + exSMS.message + JSON.stringify(response));

    url = null;

    const smsLogs = new smsLog({
      url: url,
      methodName: smsRequest.Type,
      mobileNo: smsRequest.To,
      status: "Error",
      response: "",
      responseBody: "",
      exception: exSMS.message
    });

    await smsLogs.save();

    return {
      success: false,
      data: null,
      message: "Some error occurred",
      status: 500,
    };
  }

};

module.exports.getMobileFromUserName = async function (value) {
  let user = await UserInfo.findOne({
    "userDetails.userName": { $regex: new RegExp("^" + value + "$", "i") },
  });

  if (!user)
    user = await UserInfo.findOne({
      "userDetails.emailId": { $regex: new RegExp("^" + value + "$", "i") },
    });

  if (!user) {
    return {
      success: false,
      data: user,
      message: "No such username or email Id",
      status: 400,
    };
  }

  const mobileNo = user.userDetails.mobileNo[0] ? user.userDetails.mobileNo[0].mobileNumber : "";
  const emailId = user.userDetails.emailId[0];

  if (!mobileNo && !emailId) {
    return {
      success: false,
      data: user,
      message: "No email Id / mobile no mapped to username / email",
      status: 400,
    };
  } else {
    return {
      success: true,
      data: user,
      message: "data found",
      status: 200,
    };
  }
};

module.exports.getMobileFromUserId = async function (userId) {
  let user = await UserInfo.findById(userId);

  if (!user)
    return {
      success: false,
      data: user,
      message: "Invalid User",
      status: 400,
    };

  const mobile = user.userDetails && user.userDetails.mobileNo && user.userDetails.mobileNo[0] ? user.userDetails.mobileNo[0].mobileNumber : ""; //mobile number change

  if (!mobile) {
    return {
      success: false,
      data: user,
      message: "No mobile number mapped to this account",
      status: 400,
    };
  } else {

    return {
      success: true,
      data: user,
      message: "data found",
      status: 200,
    };
  }
};

module.exports.getMobileNoFromLoanId = async function (loanId) {
  try {
    const loan = await LoanInfo.findById(loanId);
    const user = await UserInfo.findById(loan.userId);

    if (!user)
      return {
        success: false,
        data: null,
        message: "Invalid User",
        status: 200
      };

    const mobile = user.userDetails && user.userDetails.mobileNo && user.userDetails.mobileNo[0] && user.userDetails.mobileNo[0].mobileNumber ? user.userDetails.mobileNo[0].mobileNumber : "";

    if (!mobile)
      return {
        success: false,
        data: null,
        message: "No mobile number mapped to this account",
        status: 200
      };
    else
      return {
        success: true,
        data: {
          userId: user._id,
          mobile: user.userDetails.mobileNo,
          partnerId: loan.partnerId,
          maskedMobileNo: getMaskedMobileNo(mobile),
          merchantCode: loan.merchantCode
        },
        message: "data found",
        status: 200,
      };
  }
  catch (ex) {
    saveErrorLog("getMobileNoFromLoanId", apiPath, loanId, ex.message);

    return {
      success: false,
      data: null,
      message: ex.message,
      status: 200
    };
  }

};

module.exports.getPartnerId = async function (loanId) {
  try {

    const loan = await LoanInfo.findById(loanId);

    if (!loan)
      return {
        success: false,
        data: user,
        message: "Invalid Details",
        status: 200
      };

    return {
      success: true,
      data: loan.partnerId,
      message: "data found",
      status: 200,
    };
  }
  catch (ex) {
    saveErrorLog("getPartnerId", apiPath, loanId, ex.message);
    console.log(ex);
    return {
      success: false,
      data: null,
      message: ex.message,
      status: 200
    };
  }

};

async function updateMobile(loanId, mobileNo) {
  try {
    const loan = await LoanInfo.findById(mongoose.Types.ObjectId(loanId));

    if (!loan)
      return {
        success: false,
        data: null,
        message: "Invalid Loan",
        status: 200
      };

    const user = await UserInfo.findById(mongoose.Types.ObjectId(loan.userId));

    if (!user)
      return {
        success: false,
        data: user,
        message: "Invalid User",
        status: 200
      };

    const partnerConfig = await PartnerConfig.findOne({ partnerId: loan.partnerId });

    if (!partnerConfig || !partnerConfig.allowMobileEdit)
      return {
        success: false,
        data: user,
        message: "Mobile edit not allowed",
        status: 200
      };

    const existingMobileNo = user.userDetails.mobileNo && user.userDetails.mobileNo[0] && user.userDetails.mobileNo[0].mobileNumber ? user.userDetails.mobileNo[0].mobileNumber : null;

    let mobileNumberObj = buildMobileNumberObject(mobileNo)

    if (existingMobileNo) {
      if (existingMobileNo !== mobileNo) {
        user.userDetails.mobileNo.splice(0, 1, mobileNumberObj);
        user.userDetails.primaryMobileUpdatedOn = Date.now();
        await user.save();
      }
    }
    else {
      user.userDetails.mobileNo.push(mobileNumberObj);
      user.userDetails.primaryMobileUpdatedOn = Date.now();
      await user.save();
    }

    return {
      success: true,
      data: null,
      message: "updated successfully",
      status: 200
    };
  }
  catch (ex) {
    console.log("ex");
    saveErrorLog("updateMobile", apiPath, { loanId, mobileNo }, ex.message);

    return {
      success: false,
      data: null,
      message: ex.message,
      status: 200
    };
  }
};

module.exports.updateMobile = updateMobile;

module.exports.updateWhatsAppConsent = async function (userId, mobileNo, isWhatsApp) {
  try {
    const user = await UserInfo.findById(mongoose.Types.ObjectId(userId));

    if (!user)
      return {
        success: false,
        data: user,
        message: "Invalid User",
        status: 400,
      };

    if (isWhatsApp)
      user.userDetails.whatsApp = {
        whatsAppNumber: mobileNo,
        whatsAppConsent: true,
        consentGivenOn: Date.now()
      };
    else
      user.userDetails.whatsApp = {};

    if (isWhatsApp) {
      const dndRecord = await DND.findOne({ mobileNo: mobileNo, channel: "WhatsApp", isActive: true });

      if (dndRecord) {
        dndRecord.isActive = false;
        dndRecord.updatedOn = Date.now();
        await dndRecord.save();
      }
    }

    await user.save();

    return {
      success: true,
      data: null,
      message: "updated successfully"
    };
  }
  catch (ex) {
    saveErrorLog("updateWhatsAppConsent", apiPath, { userId, mobileNo, isWhatsApp }, ex.message);

    return {
      success: false,
      data: user,
      message: ex.message,
      status: 400,
    };
  }
};

module.exports.generateOtpManually = async function (userId, purpose, mobileNo = null, channel = "SMS") {
  let otp = Math.floor(100000 + Math.random() * 900000);

  await OtpInfo.updateMany(
    { userId: userId, channel: channel },
    { $set: { isActive: false } }
  );

  const item = new OtpInfo({
    userId: userId,
    loanId: null,
    otp: otp,
    isActive: true,
    purpose: purpose,
    insertedOn: Date.now(),
    mobileNo: [mobileNo],
    channel: channel
  });

  const response = item.save();

  return {
    data: response,
    otp: otp,
    success: true,
    message: "OTP generated",
    status: 200,
  };
};

module.exports.verifyCustomOtp = async function (userId, otp, purpose, channel = "SMS") {
  const record = await OtpInfo.findOne({
    userId: userId,
    isActive: true,
    purpose: purpose,
    channel: channel
  }).sort({
    insertedOn: -1,
  });

  if (!record)
    return {
      data: "",
      success: false,
      message: "No record found for userId Id",
      status: 400,
    };

  if (record.otp != otp)
    return {
      data: "",
      success: false,
      message: "OTP does not match",
      status: 200,
    };

  record.isActive = false;
  const response = await record.save();

  return {
    data: response,
    success: true,
    message: "OTP matched!",
    status: 200,
  };
};

module.exports.generateOtpForMerchant = async function (
  userId,
  purpose,
  loanId,
  mobileNo = null,
  channel = "SMS"
) {
  let otp = Math.floor(1000 + Math.random() * 9000);

  await OtpInfo.updateMany(
    { userId: userId, loanId: loanId, channel: channel },
    { $set: { isActive: false } }
  );

  const item = new OtpInfo({
    userId: userId,
    loanId: loanId,
    otp: otp,
    isActive: true,
    purpose: purpose,
    channel: channel,
    insertedOn: Date.now(),
    mobileNo: mobileNo
  });

  const response = await item.save();

  return {
    data: response,
    otp: otp,
    success: true,
    message: "OTP generated",
    status: 200,
  };
};

module.exports.verifyCustomOtpForMerchant = async function (
  userId,
  otp,
  purpose,
  loanId,
  otpAutoFill = false,
  channel = "SMS",
  mobileNo,
  checkIsExist,
  isVerifyMobileNumber
) {
  let channelConst = channel

  if (channel == "WhatsApp") {
    channelConst = OTP_CHANNELS.WHATSAPP;
  }

  let verifyPayload = {
    notifyTo: mobileNo,
    channel: channelConst,
    notificationType: NOTIFICATION_TYPES.MERCHANT_OTP,
    otp
  }

  let { error: otpVerifyError, response: record } = await verifyOtp(verifyPayload)

  if (otpVerifyError) {
    return {
      data: "",
      success: false,
      message: otpVerifyError && otpVerifyError.message || "Invalid OTP",
      status: 200,
    };
  }

  let loan = null;

  if (loanId)
    loan = await LoanInfo.findById(loanId);

  if (!record)
    return {
      data: "",
      success: false,
      message: "No record found for userId Id",
      status: 400,
    };

  record.mobileNo = [record.notifyTo]

  if (record.otp != otp) {
    //log user otp
    await logUserOtp(userId, loanId ? loanId : null, loan && loan.partnerId ? loan.partnerId : null, loan && loan.merchantCode ? loan.merchantCode : null,
      otp, record && record.mobileNo ? record.mobileNo : null, false, purpose, otpAutoFill, channel);

    return {
      data: "",
      success: false,
      message: "OTP does not match",
      status: 200,
    };
  }

  let stepName = "OTP Matched"

  if (isVerifyMobileNumber) stepName = "Mobile Verified Successfully"

  const foundIndex = loan.loanStepDetails.findIndex(x => x.Step === stepName && x.isCompleted === true);

  if (foundIndex === -1) {

    let user = await UserInfo.findById(userId);

    const otpScreenStep = await updateUserAndLoanSteps(loan, user, stepName, null, null);

    loan = otpScreenStep.data.loan;

    user = otpScreenStep.data.user;

    await loan.save();

    await user.save();
  }

  if (loan && loan.status === "Initiated" && loan.isDraft) {
    loan = await LoanInfo.findById(loanId);
    loan.status = "Draft";
    loan.updatedOn = Date.now();

    loan.flowDetails.push({
      Step: "Draft",
      changedBy: userId,
      changedByRole: "User",
      insertedOn: Date.now()
    });

    await loan.save();

  }

  if (checkIsExist) await updateMobile(loanId, mobileNo, true);

  //log user otp
  await logUserOtp(userId, loanId ? loanId : null, loan && loan.partnerId ? loan.partnerId : null, loan && loan.merchantCode ? loan.merchantCode : null,
    otp, record && record.mobileNo ? record.mobileNo : null, true, purpose, otpAutoFill, channel);

  return {
    data: "",
    success: true,
    message: "OTP matched!",
    status: 200,
  };
};

module.exports.confirmMerchantMobileNumber = async function (userId, loanId) {
  try {
    if (!loanId || !userId) {
      return {
        data: "",
        success: false,
        message: "Invalid Data",
        status: 400,
      }
    }

    let loan = await LoanInfo.findOne({ _id: loanId, userId })
    loan.isMobileNumberConfirmed = true

    loan.loanStepDetails.forEach(element => {
      if (element.Step == "Verify Mobile Number" && !element.isCompleted) {
        element.isCompleted = true
        element.completedOn = new Date()
      }
      if (element.Step == "Mobile Verified Successfully" && !element.isCompleted) {
        element.isCompleted = true
        element.completedOn = new Date()
      }
    })

    await loan.save()

    return { data: "", success: true, message: "success", status: 200 }
  } catch (e) {
    saveErrorLog("confirmMerchantMobileNumber", "/confirmMerchantMobileNumber", { loanId }, ex.message);

    return {
      data: "",
      success: false,
      message: "No record found for userId Id",
      status: 500,
    }
  }
}


function getMaskedMobileNo(mobileNo) {
  if (!mobileNo) return "";

  mobileNo = mobileNo.toString();

  return "XXXXXX" + mobileNo.substr(mobileNo.length - 4);
};

module.exports.getMaskedMobileNo = getMaskedMobileNo;

async function getMaskedEmailId(email) {
  if (!email) return "";

  email = email.toString();

  if (email.indexOf("@") > -1) {
    let parts = email.split("@");
    let name = parts[0];
    let result = name.charAt(0);
    for (let i = 1; i < name.length; i++) {
      result += "*";
    }
    result += name.charAt(name.length - 1);
    result += "@";
    let domain = parts[1];
    result += domain.charAt(0);
    let dot = domain.indexOf(".");
    for (let i = 1; i < dot; i++) {
      result += "*";
    }
    result += domain.substring(dot);

    return result;
  } else {
    let result = "";
    for (let i = 1; i < email.length - 4; i++) {
      result += "*";
    }
    return result + email.substr(email.length - 4);
  }
};

module.exports.getMaskedEmailId = getMaskedEmailId;

module.exports.validateOtpRequest = async function (loanId, mobile) {
  try {
    const loan = await LoanInfo.findById(loanId);
    const user = await UserInfo.findById(loan.userId);

    if (!user) {
      return {
        success: false,
        data: user,
        message: "Invalid User",
        status: 200
      };
    }

    const partnerConfig = await PartnerConfig.findOne({ partnerId: loan.partnerId });

    if (!partnerConfig || !partnerConfig.allowMobileEdit)
      return {
        success: false,
        data: null,
        message: "Mobile number edit not allowed",
        status: 200
      };

    if (!mobile || mobile.length !== 10 || isNaN(parseFloat(mobile)))
      return {
        success: false,
        data: null,
        message: "Invalid mobile number",
        status: 200
      };

    const existingUser = await UserInfo.findOne({
      "userDetails.mobileNo.mobileNumber": mobile
    });

    if (existingUser && !existingUser._id.equals(user._id))
      return {
        data: null,
        success: false,
        message: "Please Enter Valid Mobile",
        status: 200
      };

    return {
      success: true,
      data: {
        userId: user._id,
        mobile: [{ mobileNumber: mobile }],
        partnerId: loan.partnerId,
        maskedMobileNo: getMaskedMobileNo(mobile),
        merchantCode: loan.merchantCode
      },
      message: "data found",
      status: 200,
    };
  }
  catch (ex) {
    saveErrorLog("validateOtpRequest", apiPath, loanId, ex.message);

    return {
      success: false,
      data: null,
      message: ex.message,
      status: 200
    };
  }

};

module.exports.allowMobileEdit = async function (loanId) {
  try {
    const loan = await LoanInfo.findById(loanId);

    if (!loan)
      return {
        success: false,
        data: null,
        message: "Invalid loan",
        status: 200
      };

    const partnerConfig = await PartnerConfig.findOne({ partnerId: loan.partnerId });

    return {
      success: partnerConfig && partnerConfig.allowMobileEdit ? true : false,
      status: 200
    };
  }
  catch (ex) {
    saveErrorLog("allowMobileEdit", apiPath, loanId, ex.message);

    return {
      success: false,
      data: null,
      message: ex.message,
      status: 200
    };
  }

};