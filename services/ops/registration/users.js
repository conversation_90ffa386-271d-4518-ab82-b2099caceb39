const mongoose = require("mongoose");
const { dateAdd } = require("dateadd");
const bycrypt = require("bcrypt");
const IsNullEmpty = require("isnullemptyorwhitespace");
const debug = require("debug")("app:users");
const DateDiff = require("date-diff");
const { UserInfo, UserInfoHistory, UserPartnerInfo, ClientCodeRepository, PayInPayOut, UserPartnerInfoHistory } = require("../../../models/ops/registration/users");
const { PartnerRoles } = require("../../../models/ops/registration/partner-roles");
const { LoanInfo } = require("../../../models/ops/loan/loan");
const { CityStateMaster, MenuMaster, RoleMenuMaster } = require("../../../models/ops/common/master");
const { <PERSON>Loans, PartnerMaster, PartnerConfig, PreApprovals } = require("../../../models/ops/partner/partner");
const { verifyGstData, ocrVerification, verifyPanData, verifyDlData, verifyVoterIdData, validateAndSaveBankDetails } = require("../../../services/ops/verification/digioApi");
const { getPartner } = require("../../../services/ops/common/DbDocuments");
const { saveErrorLog } = require("../../../services/ops/common/masters");
const { getName, getDMYFormattedDate, getExtension, getCurrentTimeStamp, insertUpdatePartnerInfo, updateUserAndLoanSteps, maskNumber, base64_encode, downloadImageFromS3, genderValidation, getGenderFromPoa, saveGstDetails, buildMobileNumberObject } = require("../../../services/ops/common/common");
const AWS = require("../../../models/ops/aws/s3");

const moment = require('moment');
const config = require("config");
const fs = require("fs");
const mkdirp = require("mkdirp");
const _ = require("lodash");
const { ObjectId } = require("mongoose");
const installmentService = require("../loan/installments");
const { verifyOtp } = require('../../../services/ops/otp-service/otp-service');
const { FILE_UPLOAD_MODULES, FILE_UPLOAD_TYPES, ROLES } = require('../common/constants').Constants;
const { validateFile, backupUserInfo } = require('../common/common');
const Logger = require("../../../middleware/logger");
const logger = new Logger();
const apiPath = __dirname;

module.exports.updateQuestionnairre = async function (model, userId) {

  try {
    let user = await UserInfo.findById(userId).select("-userDetails.password");

    let loanId = model.loanId ? model.loanId : "";

    if (!loanId) {
      const loan = await LoanInfo.findOne({
        userId: userId
      }).sort({ updatedOn: -1 });

      if (loan)
        loanId = loan._id;
    }

    if (user.length == 0) {
      return {
        data: "",
        success: false,
        message: "Invalid UserId",
        status: 501,
      };
    } else {
      user.questionnairreDetails = {
        recommended: model.recommended,
        whatsApp: model.whatsApp,
        gender: model.gender,
        maritalStatus: model.maritalStatus,
        children: model.children,
        degree: model.degree,
        monthlyIncome: model.monthlyIncome,
        monthlyOutgoing: model.monthlyOutgoing,
        jobType: model.jobType,
        homeOwnershipType: model.homeOwnershipType,
        vehicle: model.vehicle,
        purchasedInOneYear: model.purchasedInOneYear,
        recommendedBy: model.recommendedBy,
        modifiedOn: Date.now(),
      };

      let loanInfo = await LoanInfo.findById(loanId);

      const completeStep = await updateUserAndLoanSteps(loanInfo, user, "Questionnairre", null, null);

      if (!completeStep.success)
        return {
          data: "",
          success: false,
          message: updateStep.message,
          status: 400
        };

      loanInfo = completeStep.data.loan;

      user = completeStep.data.user;

      user.modifiedOn = Date.now();

      loanInfo.updatedOn = Date.now();

      await user.save();

      await loanInfo.save();

      user = null;
      loanInfo = null;

      return {
        data: "",
        success: true,
        message: "Questionnairre Saved Successfully",
        status: 200,
      };
    }
  } catch (error) {
    saveErrorLog("updateQuestionnairre", apiPath, { model, userId }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    return {
      data: null,
      success: false,
      message: "Error Occurred",
      status: 400,
    };
  }
};

module.exports.insertUser = async function (model) {

  /** CHANGES START */
  let payload = {
    notifyTo: model.mobileNo,
    notificationType: "MERCHANT_REGISTRATION",
    channel: "SMS",
    otp: model.otp
  };
  let { error: verifyOtpError, response: verifyOtpResponse } = await verifyOtp(payload);

  if (verifyOtpError) {
    return {
      data: "",
      success: false,
      message: verifyOtpError && verifyOtpError.message || "Invalid OTP",
      status: 400,
    };
  }
  /** CHANGES END */
  let partner = await PartnerMaster.findOne({
    partnerName: { $regex: new RegExp(model.partnerName, "i") },
  });

  if (!partner) {
    return {
      data: "",
      success: false,
      message: "Invalid Partner Details",
      status: 400,
    };
  }

  let user = await UserInfo.find()
    .or([
      { "userDetails.emailId": { $regex: new RegExp(model.emailId, "i") } },
      { "userDetails.mobileNo.mobileNumber": model.mobileNo }, //mobile number change
    ])
    .select("-userDetails.password");

  if (user.length != 0) {
    return {
      data: "",
      success: false,
      message: "Please Enter Valid Email/Mobile",
      status: 400,
    };
  }

  // let parts = model.dob.split("/");
  // // Please pay attention to the month (parts[1]); JavaScript counts months from 0:
  // // January - 0, Februar.y - 1, etc.
  // let dob = new Date(parts[2], parts[1] - 1, parts[0]);

  user = new UserInfo({
    userDetails: {
      userName: model.userName,
      firstName: model.firstName.toUpperCase(),
      middleName: model.middleName,
      lastName: model.lastName.toUpperCase(),
      role: "User",
      isMobileValidated: true,
      isEmailValidated: false,
      dob: "",
      passwordModifiedOn: Date.now(),
    },
    ipAddress: model.ipAddress,
    insertedOn: Date.now(),
    tnc: "Y",
  });

  if (model.emailId)
    model.emailId = model.emailId.trim().toLowerCase();

  user.userDetails.emailId.push(model.emailId);
  user.userDetails.mobileNo.push(buildMobileNumberObject(model.mobileNo));

  // user.partnerDetails = {
  //   partnerId: partner._id,
  //   partnerName: partner.partnerName,
  //   partnerType: partner.partnerType,
  // };

  // user.isPartnerUser = false;
  // user.isRetailer = true;

  user.kycStepDetails.push({
    Step: "Registration",
    isCompleted: true,
    insertedOn: Date.now(),
    completedOn: Date.now(),
  });

  user.kycStepDetails.push({
    Step: "Loan",
    isCompleted: false,
    insertedOn: Date.now(),
    completedOn: null,
  });

  user.kycStepDetails.push({
    Step: "KYC",
    isCompleted: false,
    insertedOn: Date.now(),
    completedOn: null,
  });

  user.kycStepDetails.push({
    Step: "Shop",
    isCompleted: false,
    insertedOn: Date.now(),
    completedOn: null,
  });

  user.kycStepDetails.push({
    Step: "Questionnairre",
    isCompleted: false,
    insertedOn: Date.now(),
    completedOn: null,
  });

  //Hashing the password
  const salt = await bycrypt.genSalt(10);
  user.userDetails.password = await bycrypt.hash(model.password, salt);

  const result = await user.save();

  const userPartnerInfo = new UserPartnerInfo({
    userId: mongoose.Types.ObjectId(user._id),
    partnerId: mongoose.Types.ObjectId(partner._id),
    merchantCode: null,
    isRetailer: true,
    isPartnerUser: false,
    loanHistory: [],
    insertedOn: Date.now(),
    updatedOn: Date.now()
  });
  await userPartnerInfo.save();

  return {
    data: _.omit(result, ["userDetails.password"]),
    success: true,
    message: "User saved successfully",
    status: 200,
  };
};

module.exports.updateUser = async function (model, userId) {
  let partner = await PartnerMaster.findOne({
    partnerName: { $regex: new RegExp(model.partnerName, "i") },
  });

  if (!partner) {
    return {
      data: "",
      success: false,
      message: "Invalid Partner Details",
      status: 400,
    };
  }

  const user = await UserInfo.findById(userId);

  if (user.length == 0)
    return {
      data: "",
      success: false,
      message: "User not found",
      status: 501,
    };

  user.userDetails.firstName = model.firstName;
  user.userDetails.middleName = model.middleName;
  user.userDetails.lastName = model.lastName;
  user.userDetails.password = model.password;

  user.userDetails.isMobileValidated = true;
  user.userDetails.isEmailValidated = false;

  let parts = model.dob.split("/");
  // Please pay attention to the month (parts[1]); JavaScript counts months from 0:
  // January - 0, Februar.y - 1, etc.
  let dob = new Date(parts[2], parts[1] - 1, parts[0]);

  user.userDetails.dob = dob;
  // user.partnerDetails = {
  //   partnerId: partner._id,
  //   partnerName: partner.partnerName,
  //   partnerType: partner.partnerType,
  // };

  user.userDetails.emailId[0] = model.emailId;
  user.userDetails.emailId[0] = model.mobileNo;
  user.ipAddress = model.ipAddress;
  user.modifiedOn = Date.now();
  user.tnc = "Y";

  if (user.kycStepDetails.length == 0) {
    user.kycStepDetails.push({
      Step: "Registration",
      isCompleted: true,
      insertedOn: Date.now(),
      completedOn: Date.now(),
    });

    user.kycStepDetails.push({
      Step: "Loan",
      isCompleted: false,
      insertedOn: Date.now(),
      completedOn: null,
    });

    user.kycStepDetails.push({
      Step: "KYC",
      isCompleted: false,
      insertedOn: Date.now(),
      completedOn: null,
    });

    user.kycStepDetails.push({
      Step: "Shop",
      isCompleted: false,
      insertedOn: Date.now(),
      completedOn: null,
    });

    user.kycStepDetails.push({
      Step: "Questionnairre",
      isCompleted: false,
      insertedOn: Date.now(),
      completedOn: null,
    });
  }

  const result = await user.save();


  if (!IsNullEmpty(model.merchantCode)) {
    let userPartnerInfo = await UserPartnerInfo.findOne({ partnerId: mongoose.Types.ObjectId(partner._id) });

    if (IsNullEmpty(userPartnerInfo)) {
      userPartnerInfo = new UserPartnerInfo({
        userId: mongoose.Types.ObjectId(user._id),
        partnerId: mongoose.Types.ObjectId(loan.partnerId),
        merchantCode: null,
        isRetailer: true,
        isPartnerUser: false,
        loanHistory: [],
        insertedOn: Date.now(),
        updatedOn: Date.now()
      });
      await userPartnerInfo.save();
    }
  }

  return {
    data: _.omit(result, ["userDetails.password"]),
    success: true,
    message: "User saved successfully",
    status: 200,
  };
};

module.exports.deleteUser = async function (model, userId) {
  const user = await UserInfo.findById(userId).select("-userDetails.password");

  if (user.length == 0) {
    return {
      data: "",
      success: false,
      message: "User not found",
      status: 501,
    };
  } else {
    const result = await user.remove();

    return {
      data: _.omit(result, ["userDetails.password"]),
      success: true,
      message: "User deleted successfully",
      status: 200,
    };
  }
};

module.exports.insertUpdateKycFromPlatform = async function (model, userId) {
  if (model.otp) {
    /** CHANGES START */
    let payload = {
      notifyTo: model.mobileNo,
      notificationType: "KYC_PAGE",
      channel: "SMS",
      otp: model.otp
    };
    let { error: verifyOtpError, response: verifyOtpResponse } = await verifyOtp(payload);

    if (verifyOtpError) {
      return {
        data: "",
        success: false,
        message: verifyOtpError && verifyOtpError.message || "Invalid OTP",
        status: 400,
      };
    }
    /** CHANGES END */
  }

  let user = await UserInfo.findById(userId).select("-userDetails.password");
  let result = null;
  try {

    const staticPath = "ProofImages/" + userId;

    if (!user)
      return {
        data: "",
        success: false,
        message: "Invalid User Id",
        status: 400
      };

    let pan = await UserInfo.findOne({
      "userDetails.panNo": {
        $regex: new RegExp("^" + model.panNo + "$", "i"),
      }
    });

    if (pan && !pan._id.equals(mongoose.Types.ObjectId(userId)))
      return {
        data: "",
        success: false,
        message: "PAN number already exists",
        status: 400,
      };

    let partner = {};
    let loan = {};

    if (!model.loanId) {

      const loanId = user.userDetails && user.userDetails.loan && user.userDetails.loan.lastUpdatedByLoanId ? user.userDetails.loan.lastUpdatedByLoanId : null;

      if (loanId)
        loan = await LoanInfo.findById(loanId);
      else
        loan = await LoanInfo.findOne({
          userId: userId
        }).sort({ updatedOn: -1 });

      partner = await PartnerMaster.findById(loan.partnerId);
    }
    else {
      loan = await LoanInfo.findById(model.loanId);
      if(loan.amount>200000 && model.gstNo===""){
        return {
          data: "",
          success: false,
          message: "Gst Number is required for Loan Amount more than 2 Lakhs",
          status: 400
        };
      }
      partner = await PartnerMaster.findById(loan.partnerId);
      console.log("partner",partner)
    }

    const partnerConfig = await PartnerConfig.findOne({ partnerId: partner._id });
    console.log("partnerConfig",partnerConfig)
    if (!partnerConfig)
      return {
        data: "",
        success: false,
        message: "Partner Config not defined",
        status: 400
      };

    const currentRole = model.currentUserRole ? model.currentUserRole.toLowerCase() : "";
    const oldGstNumber = user.gstDetails && user.gstDetails[0] && user.gstDetails[0].gstNo ? user.gstDetails[0].gstNo : "";

    if (model.panFileStream) {

      let { error: validateError, response: validateRes } = validateFile(FILE_UPLOAD_MODULES.MERCHANT_KYC, FILE_UPLOAD_TYPES.PAN, model.panFileStream, getExtension(model.panFile));

      if (validateError) {
        return {
          data: "",
          success: false,
          message: validateError.message,
          status: 400
        };
      }

      let panFileName = `panFile_${getCurrentTimeStamp()}${getExtension(model.panFile)}`;

      let panPath = `${staticPath}/PAN/${panFileName}`
      // let uploadFile = model.panFileStream.replace(/^data:.+;base64,/, "");
      await AWS.uploadToS3(panPath, model.panFileStream)

      user.panDocDetails = [];

      let panDoc_Details = {
        fileName: panFileName,
        filePath: panPath,
        isActive: true,
        insertedOn: Date.now()
      };

      user.panDocDetails.push(panDoc_Details);
    }

    //PAN Number change allowed only if KYC not previously approved
    if (model.currentUserRole && model.currentUserRole.toLowerCase() === "user" && user.userDetails.clientCode)
      model.panNo = user.panNo;

    user.panNo = model.panNo.toUpperCase();

    if (model.poaType !== "Aadhaar Card") {
      let poa = await UserInfo.findOne({
        "poaDetails.poaNo": {
          $regex: new RegExp("^" + model.poaNo + "$", "i"),
        },
        "poaDetails.poaType": model.poaType
      });
      if (poa && !poa._id.equals(mongoose.Types.ObjectId(userId)))
        return {
          data: "",
          success: false,
          message: "POA number already exists",
          status: 400,
        };
    }

    if (model.poaFileStream) {

      let { error: validateError, response: validateRes } = validateFile(FILE_UPLOAD_MODULES.MERCHANT_KYC, FILE_UPLOAD_TYPES.ADDRESS_PROOF_FRONT_IMAGE, model.poaFileStream, getExtension(model.poaFile));

      if (validateError) {
        return {
          data: "",
          success: false,
          message: validateError.message,
          status: 400
        };
      }


      let poaBack = {};
      let docDetails = [];
      let backExists = false

      if (user.poaDetails && user.poaDetails[0] && user.poaDetails[0].docDetails[1]) {
        poaBack = user.poaDetails[0].docDetails[1];
        backExists = true;
      }

      let poaFileName = `poaFront_${getCurrentTimeStamp()}${getExtension(model.poaFile)}`;

      let poaPath = `wom/${staticPath}/POA/${poaFileName}`
      await AWS.uploadToS3(poaPath, model.poaFileStream)

      let poaDoc_Details = {
        fileName: poaFileName,
        filePath: poaPath,
        isActive: true,
        insertedOn: Date.now(),
        documentPassword: model.poaPassword ? model.poaPassword : "",
        position: "front"
      };

      docDetails.push(poaDoc_Details);

      if (backExists)
        docDetails.push(poaBack);

      let poa_Details = {
        poaNo: model.poaNo.toUpperCase(),
        poaType: model.poaType,
        docDetails: docDetails,
        insertedOn: Date.now(),
        poaVerificationDocType: model.poaVerificationDocType ? model.poaVerificationDocType : model.poaType
      };

      if (user.poaDetails && user.poaDetails[0])
        user.poaDetails.splice(0, 1, poa_Details);
      else
        user.poaDetails.push(poa_Details);

    } else {
      user.poaDetails[0].poaNo = model.poaType === "Aadhaar Card" ? maskNumber(model.poaNo.toUpperCase(), 4) : model.poaNo.toUpperCase();
      user.poaDetails[0].poaType = model.poaType;
      user.poaDetails[0].poaVerificationDocType = model.poaVerificationDocType ? model.poaVerificationDocType : model.poaType;

      if (model.poaPassword && user.poaDetails && user.poaDetails[0] && user.poaDetails[0].docDetails && user.poaDetails[0].docDetails[0])
        user.poaDetails[0].docDetails[0].documentPassword = model.poaPassword ? model.poaPassword : "";

      if (model.poaPassword && user.poaDetails && user.poaDetails[0] && user.poaDetails[0].docDetails && user.poaDetails[0].docDetails[1])
        user.poaDetails[0].docDetails[1].documentPassword = model.poaPassword ? model.poaPassword : "";

      if (user.poaDetails && user.poaDetails[0] && user.poaDetails[0].docDetails && user.poaDetails[0].docDetails[1])
        user.poaDetails[0].docDetails[1].position = "back";
    }

    if (model.poaBackFileStream) {

      let { error: validateError, response: validateRes } = validateFile(FILE_UPLOAD_MODULES.MERCHANT_KYC, FILE_UPLOAD_TYPES.ADDRESS_PROOF_BACK_IMAGE, model.poaBackFileStream, getExtension(model.poaBackFile));

      if (validateError) {
        return {
          data: "",
          success: false,
          message: validateError.message,
          status: 400
        };
      }

      let poaBackFileName = `poaBack_${getCurrentTimeStamp()}${getExtension(model.poaBackFile)}`;

      let poaPath = `wom/${staticPath}/POA/${poaBackFileName}`
      let uploadFile = model.poaBackFileStream.replace(/^data:.+;base64,/, "");
      await AWS.uploadToS3(poaPath, uploadFile)

      let poaDoc_Details = {
        fileName: poaBackFileName,
        filePath: poaPath,
        isActive: true,
        insertedOn: Date.now(),
        documentPassword: model.poaPassword ? model.poaPassword : "",
        position: "back"
      };

      user.poaDetails[0].docDetails[1] = poaDoc_Details;

    } else if (!model.poaBackFile && !model.poaBackFileStream) {
      if (user.poaDetails[0].docDetails[1]) user.poaDetails[0].docDetails.splice(1, 1);
    }

    let gst = await UserInfo.findOne({
      "gstDetails.gstNo": {
        $regex: new RegExp("^" + model.gstNo + "$", "i"),
      },
    });

    if (gst && !gst._id.equals(mongoose.Types.ObjectId(userId)))
      return {
        data: "",
        success: false,
        message: "GST number already exists",
        status: 400,
      };

    if (model.gstFileStream) {
      user.gstDetails = [];

      let [base, filePath] = model.gstFileStream.split(".com/")

      let gstFileName = filePath.split("/")
      gstFileName = gstFileName[gstFileName.length - 1]

      let gstDoc_Details = {
        fileName: gstFileName,
        filePath: filePath,
        isActive: true,
        insertedOn: Date.now(),
        position: "front"
      };

      let gst_Details = {
        gstNo: model.gstNo.toUpperCase(),
        docDetails: gstDoc_Details,
        insertedOn: Date.now()
      };

      user.gstDetails.push(gst_Details);

    }
    // else if (user.gstDetails && user.gstDetails[0] && model.gstNo) {
    //   user.gstDetails[0].gstNo = model.gstNo.toUpperCase();
    // }
    else if ((!user.gstDetails || (user.gstDetails && !user.gstDetails[0])) && model.gstNo) {
      user.gstDetails = [];

      let gst_Details = {
        gstNo: model.gstNo.toUpperCase(),
        docDetails: [],
        insertedOn: Date.now()
      };

      user.gstDetails.push(gst_Details);
    }

    const gstVerfied = user.gstDetails && user.gstDetails[0] && user.gstDetails[0].docVerification && user.gstDetails[0].docVerification.isGstVerified ? true : false;

    let additionalFileCount = user.additionalDocuments ? user.additionalDocuments.length : 0;
    if (model.additionalDocuments && model.additionalDocuments.length) {

      let additionalDocuments = user.additionalDocuments

      let docsHash = {}
      additionalDocuments.forEach(element => {
        docsHash[element.docDetails[0].fileName] = element
      });

      let newDocuments = []

      for (const item of model.additionalDocuments) {

        if (item.fileStream && item.fileStream != "") {

          additionalFileCount += 1;

          let [base, filePath] = item.fileStream.split(".com/")
          let additionalFileName = filePath.split("/");
          additionalFileName = additionalFileName[additionalFileName.length - 1]

          if (!docsHash[additionalFileName]) {
            let additionalDoc_Details = {
              fileName: additionalFileName,
              filePath: filePath,
              isActive: true,
              insertedOn: Date.now(),
              position: "front"
            };

            newDocuments.push({
              docNo: "",
              docType: "",
              docDetails: additionalDoc_Details,
            });
          }

          if (item.isDeleted) {
            delete docsHash[additionalFileName]
          }
        }


      }
      let alreadyUploadedDoc = Object.values(docsHash)
      user.additionalDocuments = [...alreadyUploadedDoc, ...newDocuments]

    }
    //Name change allowed only if KYC not previously approved
    if (model.currentUserRole && model.currentUserRole.toLowerCase() === "user" && user.userDetails.clientCode) {
      model.firstName = user.userDetails.firstName ? user.userDetails.firstName : "";
      model.middleName = user.userDetails.middleName ? user.userDetails.middleName : "";
      model.lastName = user.userDetails.lastName ? user.userDetails.lastName : "";
    }

    user.userDetails.firstName = model.firstName.toUpperCase();
    user.userDetails.middleName = model.middleName ? model.middleName.toUpperCase() : "";
    user.userDetails.lastName = model.lastName.toUpperCase();

    user.userDetails.employment = model.employment;

    if (model.email) {
      if (user.userDetails.emailId[0]) {
        const oldEmailID = user.userDetails.emailId[0] ? user.userDetails.emailId[0].toLowerCase() : "";
        const userName = user.userDetails.userName ? user.userDetails.userName.toLowerCase() : "";

        user.userDetails.emailId.splice(0, 1, model.email);

        if (oldEmailID === userName)
          user.userDetails.userName = model.email;

      } else
        user.userDetails.emailId.push(model.email);
    }

    if (model.alternateEmail) {
      if (user.userDetails.emailId[1])
        user.userDetails.emailId.splice(1, 1, model.alternateEmail);
      else
        user.userDetails.emailId.push(model.alternateEmail);
    }

    if (model.mobileNo !== user.userDetails.mobileNo[0].mobileNumber)
      user.userDetails.whatsApp = {};


    if (model.mobileNo) {
      let mobileNumberObj = buildMobileNumberObject(model.mobileNo)

      if (user.userDetails.mobileNo[0])
        user.userDetails.mobileNo.splice(0, 1, mobileNumberObj);
      else
        user.userDetails.mobileNo.push(mobileNumberObj);
    }

    if (model.alternateMobile) {
      let mobileNumberObj = buildMobileNumberObject(model.alternateMobile, false, false)

      if (user.userDetails.mobileNo[1])
        user.userDetails.mobileNo.splice(1, 1, mobileNumberObj);
      else
        user.userDetails.mobileNo.push(mobileNumberObj);
    }

    if (model.gender && currentRole === "admin") {
      user.questionnairreDetails.gender = model.gender

      let poaDetails = user.poaDetails[0]

      let isValidGender = genderValidation(poaDetails, model.gender)

      let genderFromDoc = getGenderFromPoa(poaDetails)

      if (isValidGender || !genderFromDoc) {

        if (user.userDetails.kycRemarks == "Gender Mismatch") user.userDetails.kycRemarks = ""

        if (user.issues) {
          let issues = user.issues.filter(element => element.code !== config.get("errorCode.genderMismatch"));
          user.issues = issues
        }

      } else {
        let errorObj = {
          code: config.get("errorCode.genderMismatch"),
          description: "Gender Mismatch"
        }
        if (user.issues) {
          user.issues.push(errorObj)
        } else {
          user.issues = [errorObj]

        }
      }

    }

    if (loan.merchantCode) model.merchantCode = loan.merchantCode;

    const savePartnerInfo = await insertUpdatePartnerInfo(userId, loan.partnerId, model.merchantCode);

    if (!savePartnerInfo.success)
      return {
        data: "",
        success: false,
        message: savePartnerInfo.message,
        status: 400
      };

    if (model.dob) {
      let parts = model.dob.split("/");
      // Please pay attention to the month (parts[1]); JavaScript counts months from 0:
      // January - 0, Februar.y - 1, etc.
      let dob = new Date(parts[2], parts[1] - 1, parts[0]);
      user.userDetails.dob = dob;
    }

    const updateStep = await updateUserAndLoanSteps(loan, user, "KYC", model.currentUserRole, model.currentUserId);

    if (!updateStep.success)
      return {
        data: "",
        success: false,
        message: updateStep.message,
        status: 400
      };

    loan = updateStep.data.loan;

    user = updateStep.data.user;

    loan.merchantCode = model.merchantCode;

    await loan.save();

    // const otherAddress = user.addressDetails.filter(function (obj) {
    //   return obj.type.toLowerCase() !== "correspondence";
    // });

    user.addressDetails = [];

    let userCity = "";
    let userState = "";

    const getKycStatus = await getKycUpdatedStatus(userId, model, loan._id, partner._id, currentRole, model.merchantCode);
    if (getKycStatus !== "Approved")
      user.userDetails.kycStatus = getKycStatus;

    userCity = model.city ? model.city.toUpperCase() : "";
    userState = model.state ? model.state.toUpperCase() : "";

    let add1 = "";
    let add2 = "";
    let add3 = "";

    if (model.add1) {
      add1 = model.add1.replace("#", "");
      add1 = add1.replace("@", "");
    }

    if (model.add2) {
      add2 = model.add2.replace("#", "");
      add2 = add2.replace("@", "");
    }

    if (model.add3) {
      add3 = model.add3.replace("#", "");
      add3 = add3.replace("@", "");
    }
    
    let perAdd1 = "";
    let perAdd2 = "";
    let perAdd3 = "";

    if (model.perAdd1) {
      perAdd1 = model.perAdd1.replace("#", "");
      perAdd1 = perAdd1.replace("@", "");
    }

    if (model.perAdd2) {
      perAdd2 = model.perAdd2.replace("#", "");
      perAdd2 = perAdd2.replace("@", "");
    }

    if (model.perAdd3) {
      perAdd3 = model.perAdd3.replace("#", "");
      perAdd3 = perAdd3.replace("@", "");
    }

    user.addressDetails.push({
      add1: add1,
      add2: add2,
      add3: add3,
      type: "correspondence",
      city: userCity,
      state: userState,
      country: model.country.toUpperCase(),
      pincode: model.pincode,
      insertedOn: Date.now(),
      perAdd1: perAdd1,
      perAdd2: perAdd2,
      perAdd3: perAdd3,
    });

    // if (otherAddress && otherAddress.length > 0) {
    //   for (const address of otherAddress) {
    //     user.addressDetails.push({
    //       add1: address.add1,
    //       add2: address.add2,
    //       add3: address.add3,
    //       type: address.type,
    //       city: address.city,
    //       state: address.state,
    //       country: address.country,
    //       pincode: address.pincode,
    //       insertedOn: Date.now()
    //     });
    //   }
    // }

    //validate and save bank details
    if (currentRole === "admin") {
      if (loan.amount >= partnerConfig.bankValidationAmount) {
        if (model.accountNo && model.ifsc && model.accountType && model.accountName) {
          //validate bank details
          const saveBankDetails = await validateAndSaveBankDetails(userId, partner._id, model.accountNo.toUpperCase(), model.ifsc.toUpperCase(), model.accountType, model.accountName, null, null, model.merchantCode, loan._id, false);

          if (!saveBankDetails.success)
            return saveBankDetails;
        }
        else
          return {
            data: "",
            success: false,
            message: "Please provide bank details",
            status: 400
          };
      }
      else if (model.accountNo && model.ifsc && model.accountType && model.accountName) {
        //validate bank details
        const saveBankDetails = await validateAndSaveBankDetails(userId, partner._id, model.accountNo.toUpperCase(), model.ifsc.toUpperCase(), model.accountType, model.accountName, null, null, model.merchantCode, loan._id, false);

        if (!saveBankDetails.success)
          return saveBankDetails;
      }
    }

    user.modifiedOn = Date.now();

    result = await user.save();

    if (result) {

      // validateDocuments(result, model, oldPanNumber, oldPoaNumber, oldDob, oldGstNumber, gstVerfied);
      validateDocumentsV2(result, model, oldGstNumber, gstVerfied);

      user = null;
      loan = null;

      return {
        // data: result,
        data: {}, /** VAPT ISSUE FIX */
        success: true,
        message: "User updated successfully",
        status: 200,
      };
    }
    else {
      user = null;
      loan = null;

      return {
        // data: result,
        data: {}, /** VAPT ISSUE FIX */
        success: false,
        message: "Error Occurred",
        status: 400,
      };
    }
  }
  catch (ex) {
    saveErrorLog("insertUpdateKycFromPlatform", apiPath, { model, userId }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    return {
      data: null,
      success: false,
      message: "Error Occurred",
      status: 400,
    };
  }

};

module.exports.insertUpdatePartnerKYC = async function (model, userId) {
  let user = await UserInfo.findById(userId).select("-userDetails.password");

  if (!user) {
    return {
      data: "",
      success: false,
      message: "Invalid User Id",
      status: 400,
    };
  }

  const oldGstNumber = user.gstDetails && user.gstDetails[0] && user.gstDetails[0].gstNo ? user.gstDetails[0].gstNo : "";

  let partnerMaster = await PartnerMaster.findById(model.partnerId);

  let pan = await UserInfo.findOne({
    "userDetails.panNo": {
      $regex: new RegExp("^" + model.panNo + "$", "i"),
    },
  });

  if (pan && !pan._id.equals(mongoose.Types.ObjectId(userId))) {
    return {
      data: "",
      success: false,
      message: "PAN number already exists",
      status: 400,
    };
  }
  const staticPath = "ProofImages/" + userId;
  if (model.panFileStream) {

    let { error: validateError, response: validateRes } = validateFile(FILE_UPLOAD_MODULES.PARTNER_KYC, FILE_UPLOAD_TYPES.PAN, model.panFileStream, getExtension(model.panFile));

    if (validateError) {
      return {
        data: "",
        success: false,
        message: validateError.message,
        status: 400
      };
    }

    let panFileName = `panFile_${getCurrentTimeStamp()}${getExtension(model.panFile)}`;
    let filePath = staticPath + "/PAN/" + panFileName
    await AWS.uploadToS3(filePath, model.panFileStream)

    let panDoc_Details = {
      fileName: panFileName,
      filePath: filePath,
      isActive: true,
      insertedOn: Date.now(),
      position: "front"
    };

    user.panDocDetails = [panDoc_Details];

  }

  let oldPanNumber = user.panNo;

  user.panNo = model.panNo.toUpperCase();

  if (model.udyogAadhaarNo) {
    let udyogAadhaar = await UserInfo.findOne({
      "udyogAadhaar.aadhaarNo": {
        $regex: new RegExp("^" + model.udyogAadhaarNo + "$", "i"),
      },
    });

    if (
      udyogAadhaar &&
      !udyogAadhaar._id.equals(mongoose.Types.ObjectId(userId))
    ) {
      return {
        data: "",
        success: false,
        message: "Udyog Aadhaar number already exists",
        status: 400,
      };
    }
  }

  if (model.udyogAadhaarFileStream) {

    user.udyogAadhaar = {};

    let [base, filePath] = model.udyogAadhaarFileStream.split(".com/")

    let udyodAadhaarFileName = filePath.split("/")
    udyodAadhaarFileName = udyodAadhaarFileName[udyodAadhaarFileName.length - 1]

    let udyogAadhaarDoc_Details = {
      fileName: udyodAadhaarFileName,
      filePath: filePath,
      isActive: true,
      insertedOn: Date.now(),
      position: "front"
    };

    let udyogAadhaar_Details = {
      aadhaarNo: model.udyogAadhaarNo.toUpperCase(),
      docDetails: udyogAadhaarDoc_Details,
      insertedOn: Date.now(),
    };

    user.udyogAadhaar = udyogAadhaar_Details;
  } else if (!model.udyogAadhaarFileStream && !model.udyogAadhaarFile) {
    user.udyogAadhaar = {};
  } else {
    user.udyogAadhaar.aadhaarNo = model.udyogAadhaarNo
      ? model.udyogAadhaarNo.toUpperCase()
      : "";
  }

  let gst = await UserInfo.findOne({
    "gstDetails.gstNo": {
      $regex: new RegExp("^" + model.gstNo + "$", "i"),
    },
  });

  if (gst && !gst._id.equals(mongoose.Types.ObjectId(userId))) {
    return {
      data: "",
      success: false,
      message: "GST number already exists",
      status: 400,
    };
  }
  if (model.gstFileStream) {

    let [base, filePath] = model.gstFileStream.split(".com/")

    let gstFileName = filePath.split("/")
    gstFileName = gstFileName[gstFileName.length - 1]

    let gstDoc_Details = {
      fileName: gstFileName,
      filePath: filePath,
      isActive: true,
      insertedOn: Date.now(),
      position: "front"
    };

    let gst_Details = {
      gstNo: model.gstNo.toUpperCase(),
      docDetails: gstDoc_Details,
      insertedOn: Date.now(),
    };

    user.gstDetails.push(gst_Details);
  }
  else if ((!user.gstDetails || (user.gstDetails && !user.gstDetails[0])) && model.gstNo) {
    user.gstDetails = [];
    let gst_Details = {
      gstNo: model.gstNo.toUpperCase(),
      docDetails: [],
      insertedOn: Date.now(),
    };

    user.gstDetails.push(gst_Details);
  }

  user.userDetails.kycStatus =
    user.userDetails.kycStatus !== "Approved"
      ? "Under Review"
      : user.userDetails.kycStatus;

  if (user.kycStepDetails.length != 0) {
    user.kycStepDetails[0].isCompleted = true;
    user.kycStepDetails[0].completedOn = Date.now();
  } else {
    user.kycStepDetails.push({
      Step: "KYC",
      isCompleted: true,
      insertedOn: Date.now(),
      completedOn: Date.now(),
    });
  }

  user.addressDetails = user.addressDetails.filter(function (obj) {
    return obj.type.toLowerCase() !== "correspondence";
  });

  // let city = await CityStateMaster.findOne({
  //   state: model.state,
  //   city: model.city
  // });

  const city = await CityStateMaster.findOne({
    state: {
      $regex: new RegExp("^" + model.state + "$", "i"),
    },
    city: {
      $regex: new RegExp("^" + model.city + "$", "i"),
    }
  });

  if (!city) {
    return {
      data: "",
      success: false,
      message: "Invalid City/State",
      status: 400,
    };
  }

  user.addressDetails.push({
    add1: model.add1 ? model.add1.toUpperCase() : "",
    add2: model.add2 ? model.add2.toUpperCase() : "",
    add3: model.add3 ? model.add3.toUpperCase() : "",
    type: "correspondence",
    city: city.city.toUpperCase(),
    state: city.state.toUpperCase(),
    country: model.country.toUpperCase(),
    pincode: model.pincode,
    insertedOn: Date.now(),
  });

  //validate bank details
  const saveBankDetails = await validateAndSaveBankDetails(userId, model.partnerId, model.accountNo, model.ifsc, model.accountType, model.accountName);

  if (!saveBankDetails.success)
    return saveBankDetails;

  user.modifiedOn = Date.now();

  const result = await user.save();

  //Digio Verification
  //Digio PAN OCR Verification

  if (model.panFileStream)
    await ocrVerification(userId, model.panFileStream, "", true, model.panNo, "PAN", 0);
  else {
    //In-Case on number is received, check if number is different to previous number. If different call dataApi
    if (oldPanNumber.toLowerCase() != model.panNo.toLowerCase()) {
      let partnerName = partnerMaster.partnerName;
      let doiValue = "";
      let doiDate = partnerMaster.doi;

      if (doiDate) {
        doiValue = moment(doiDate).format("yyyy-MM-DD")
      }

      await verifyPanData(userId, model.panNo, partnerName, doiValue);
    }
  }

  //Verify GST Number if KYC is not approved
  // if (model.gstNo)
  //   await verifyGstData(userId, model.gstNo.toUpperCase(), 0);

  const gstVerfied = user.gstDetails && user.gstDetails[0] && user.gstDetails[0].docVerification && user.gstDetails[0].docVerification.isGstVerified ? true : false;

  if (!gstVerfied) {
    if (model.gstNo)
      await saveGstDetails("detailedgst", user._id, model.gstNo.toUpperCase(), null);
    else if (user.panNo && user.addressDetails && user.addressDetails[0] && user.addressDetails[0].state)
      await saveGstDetails("pantogst", user._id, user.panNo.trim().toUpperCase(), user.addressDetails[0].state.trim().toUpperCase());
  }

  return {
    data: {},
    success: true,
    message: "User updated successfully",
    status: 200,
  };
};

async function createDirectoryAndSaveFile(path, imageStream, pathWithFileName) {
  if (!fs.existsSync(path)) fs.mkdirSync(path);

  // let data = imageStream.replace(/^data:image\/\w+;base64,/, "");
  // data = imageStream.replace(/^data:applicationpdf\/\w+;base64,/, "");

  let data = imageStream.replace(/^data:.+;base64,/, "");

  fs.writeFileSync(pathWithFileName, data, { encoding: "base64" }, function (
    err
  ) {
    if (err) return false;

    return true;
  });
};

module.exports.createDirectoryAndSaveFile = createDirectoryAndSaveFile;

module.exports.getMenuList = async function (userId) {
  try {
    if (!userId)
      return {
        data: [],
        success: true,
        message: "",
        status: 200,
      };

    const userInfo = await UserInfo.findById(userId);

    if (!userInfo)
      return {
        data: [],
        success: true,
        message: "",
        status: 200,
      };


    const roleMenuMaster = await RoleMenuMaster.find({
      roleName: userInfo.userDetails.role
    });

    let array = [];

    for (const item of roleMenuMaster) {
      array.push(item.menuName);
    }

    const menuMaster = await MenuMaster.find({
      menu: { $in: array },
      isActive: true,
    }).sort({ ordering: 1 });

    return {
      data: menuMaster,
      success: true,
      message: "",
      status: 200,
    };
  }
  catch (ex) {
    saveErrorLog("getMenuList", apiPath, userId, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    return {
      data: [],
      success: false,
      message: ex.message,
      status: 200,
    };
  }
};

module.exports.getUser = async function (userId, isPartnerUser = false, partnerId = null, merchantCode = null) {
  try {
    const user1 = await UserInfo.findById(userId).select(
      "_id userDetails questionnairreDetails udyogAadhaar udyamAadhaar panNo addressDetails panDocDetails poaDetails gstDetails additionalDocuments profileDocDetails panDocVerification shopDetails"
    );

    const user = JSON.parse(JSON.stringify(user1));

    if (user.userDetails.password)
      delete user.userDetails.password;

    if (user.userDetails.passwordModifiedOn)
      delete user.userDetails.passwordModifiedOn;

    // partnerDetails
    // merchantCode
    //isRetailer 
    //isPartnerUser

    let doi = "";
    let typeOfEntity = "";
    let isRetailerUser = true;
    let partner = null;

    if (user) {
      let query = { userId: mongoose.Types.ObjectId(userId) };

      if (isPartnerUser != false) query["isPartnerUser"] = isPartnerUser;

      if (partnerId) query.partnerId = mongoose.Types.ObjectId(partnerId);
      else if (user.loan && user.loan.lastUpdatedByPartnerId) query.partnerId = mongoose.Types.ObjectId(user.loan.lastUpdatedByPartnerId);

      if (merchantCode)
        query["merchantCode"] = merchantCode;
      else if (user.loan && user.loan.lastUpdatedByLoanNo) {
        const loanInfo = await LoanInfo.findOne({ loanNo: user.loan.lastUpdatedByLoanNo }).select({ merchantCode: 1 });
        query["merchantCode"] = loanInfo.merchantCode;
      }

      const userPartnerInfo = await UserPartnerInfo.findOne(query);

      if (userPartnerInfo) {
        user.bankDetails = userPartnerInfo.bankDetails;
        user.isRetailer = userPartnerInfo.isRetailer;
        user.isPartnerUser = userPartnerInfo.isPartnerUser;
        user.merchantCode = userPartnerInfo.merchantCode;
        user.partnerDetails = {
          partnerId: userPartnerInfo.partnerId
        }

        partner = await PartnerMaster.findById(userPartnerInfo.partnerId).select({ authKey: 0, secretKey: 0 });
        if (partner) {
          user.partnerDetails = {
            partnerId: partner._id,
            partnerName: partner.partnerName,
            partnerType: partner.partnerType
          }

          if (userPartnerInfo.isPartnerUser) {
            typeOfEntity = partner.typeOfEntity;
            doi = partner.doi;
          }
          isRetailerUser = partner.isRetailer ? true : false;
        }
      }
    }
    partner = partner == null ? {} : partner;

    let loanPartner = null;
    if (user1.userDetails.loan.lastUpdatedByPartnerId)
      loanPartner = await PartnerMaster.findById(user1.userDetails.loan.lastUpdatedByPartnerId);

    return {
      data: { user, doi, typeOfEntity, isRetailerUser, partner, loanPartner },
      success: true,
      message: "",
      status: 200
    };
  } catch (ex) {
    saveErrorLog("getUser", apiPath, {}, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    return {
      data: [],
      success: false,
      message: "Internal Error",
      status: 200,
    };
  }
};

async function getClientCode(model) {
  if (model != null) {
    const isPartnerUser = await UserPartnerInfo.find({ userId: mongoose.Types.ObjectId(model._id), isPartnerUser: true }).countDocuments();

    if (model.panNo != null) {
      let prefix;
      const userPartnerInfo = await UserPartnerInfo.findOne({ userId: mongoose.Types.ObjectId(model._id) });

      const partner = await PartnerMaster.findById(userPartnerInfo.partnerId);
      if (partner != null) {
        if (partner.partnerName != null) {
          if (isPartnerUser > 0 && model.userDetails.role.toLowerCase() != "user") {
            prefix = "FFMP";
          } else {
            prefix = partner.partnerCode;
          }

          if (prefix != null) return generateClientCode(prefix, model.panNo);
          else
            return {
              data: "",
              success: false,
              message: "prefix not found",
              status: 501,
            };
        } else {
          return {
            data: "",
            success: false,
            message: "partner Name is mandatory field",
            status: 400,
          };
        }
      } else {
        return {
          data: "",
          success: false,
          message: "partner not found",
          status: 501,
        };
      }
    } else {
      if (model.panNo == null) {
        return {
          data: "",
          success: false,
          message: "user panno could not be null",
          status: 400,
        };
      } else {
        return {
          data: "",
          success: false,
          message: "user isPartnerUser could not be null",
          status: 400,
        };
      }
    }
  } else {
    return {
      data: "",
      success: false,
      message: "user schema could not be null",
      status: 400,
    };
  }
};

module.exports.getClientCode = getClientCode;

async function generateClientCode(prefix, panNo) {
  //console.log('inside generateClientCode');
  let client_Code;
  let _code;
  let adjuster;
  let partyCodeGenerated = false;
  let output = {
    data: client_Code,
    success: false,
    message: "Some Error Occurred",
    status: 501,
  };

  do {
    const repository = await ClientCodeRepository.findOne({
      panNumber: { $regex: panNo, $options: "i" },
    });

    if (repository != null) {
      client_Code = repository.clientCode;
      partyCodeGenerated = true;
      return {
        data: client_Code,
        success: true,
        message: "party code already exist",
        status: 200,
      };
    } else {
      const repository1 = await ClientCodeRepository.find({
        prefix: { $regex: prefix, $options: "i" },
      })
        .sort({ code: -1 })
        .limit(1);

      if (repository1.length > 0) {
        _code = repository1[0].code;
      } else {
        _code = 0;
      }

      _code = _code + 1;

      switch (_code.toString().length) {
        case 1:
          adjuster = "00000";
          break;
        case 2:
          adjuster = "0000";
          break;
        case 3:
          adjuster = "000";
          break;
        case 4:
          adjuster = "00";
          break;
        case 5:
          adjuster = "0";
          break;
        default:
          adjuster = "";
          break;
      }

      if (adjuster == "") {
        client_Code = prefix + String(_code);
      } else {
        client_Code = prefix + adjuster + String(_code);
      }

      const clientCodeRepository = new ClientCodeRepository({
        panNumber: panNo,
        clientCode: client_Code,
        prefix: prefix,
        code: _code,
      });

      await clientCodeRepository
        .save()
        .then((product) => {
          partyCodeGenerated = true;

          output.data = client_Code;
          output.success = true;
          output.message = "party code generated successfully";
          output.status = 200;
        })
        .catch((err) => {
          const duplicate = err.toString().includes("duplicate key", 0);
          if (!duplicate) {
            partyCodeGenerated = true;

            output.data = null;
            output.success = true;
            output.message = err.toString();
            output.status = 501;
          }
        });
    }
  } while (!partyCodeGenerated);

  return output;
};

module.exports.updatePassword = async function (userId, password, model) {

  let user = await UserInfo.findOne({ "userDetails.userName": { $regex: new RegExp("^" + model.value && model.value.toLowerCase() + "$", "i") } });

  if (!user) {
    user = await UserInfo.findOne({ "userDetails.emailId": model.value && model.value.toLowerCase() });
  }

  if (!user) {
    return {
      data: "",
      success: false,
      message: "Invalid User",
      status: 400,
    };
  }

  if (user.userDetails.emailId && user.userDetails.emailId.length)
    model.value = user.userDetails.emailId[0]

  /** CHANGES START */
  let payload = {
    notifyTo: model.value,
    notificationType: "FORGOT_PASSWORD",
    channel: "EMAIL",
    otp: model.otp
  };
  let { error: verifyOtpError, response: verifyOtpResponse } = await verifyOtp(payload);

  if (verifyOtpError) {
    return {
      data: "",
      success: false,
      message: verifyOtpError && verifyOtpError.message || "Invalid OTP",
      status: 400,
    };
  }
  /** CHANGES END */

  //  let user = await UserInfo.findOne({ "userDetails.emailId": model.value && model.value.toLowerCase() });

  // if (!user) {
  //   return {
  //     data: "",
  //     success: false,
  //     message: "Invalid User",
  //     status: 400,
  //   };
  // }

  //Hashing the password
  const salt = await bycrypt.genSalt(10);
  user.userDetails.password = await bycrypt.hash(password, salt);
  user.modifiedOn = Date.now();
  user.userDetails.passwordModifiedOn = Date.now();

  const result = await user.save();

  return {
    data: {},
    success: true,
    message: "User saved successfully",
    status: 200,
  };
};

module.exports.updateDetails = async function (userId, value, type) {
  let user = await UserInfo.findById(userId);

  if (!user) {
    return {
      data: "",
      success: false,
      message: "Invalid User",
      status: 400,
    };
  }

  if (type.toLowerCase() == "alternateemail") {
    if (user.userDetails.emailId[1]) {
      user.userDetails["emailId"].set(1, value);
    } else {
      user.userDetails["emailId"].set(1, value);
    }
  }
  if (type.toLowerCase() == "alternatemobile") {
    let obj = {
      mobileNumber: value,
      isPrimary: false
    }
    if (user.userDetails.mobileNo[1]) {
      user.userDetails["mobileNo"].set(1, obj);
    } else {
      user.userDetails["mobileNo"].set(1, obj);
    }
  }

  if (type.toLowerCase() == "mobile") {
    let obj = {
      mobileNumber: value,
      isPrimary: true
    }
    if (user.userDetails.mobileNo[0]) {
      user.userDetails["mobileNo"].set(0, obj);
    } else {
      user.userDetails["mobileNo"].set(0, obj);
    }
  }

  const result = await user.save();

  return {
    data: {},
    success: true,
    message: "User saved successfully",
    status: 200,
  };
};

module.exports.uploadProfilePic = async function (
  userId,
  fileName,
  fileStream
) {
  let user = await UserInfo.findById(userId).select("-userDetails.password");


  if (!user) {
    return {
      data: "",
      success: false,
      message: "Invalid User Id",
      status: 400,
    };
  }

  if (fileStream) {

    let [base, filePath] = fileStream.split(".com/")

    fileName = filePath.split("/")
    fileName = fileName[fileName.length - 1]

    let profileDoc_Details = {
      fileName: fileName,
      filePath: filePath,
      isActive: true,
      insertedOn: Date.now(),
    };

    user.profileDocDetails.push(profileDoc_Details);

    const result = await user.save();

    return {
      data: {},
      success: true,
      message: "User updated successfully",
      status: 200,
    };
  } else {
    return {
      data: "",
      success: false,
      message: "No Image provided",
      status: 200,
    };
  }
};

module.exports.getPortFolioDetailsForBorrowingPartner = async function (model) {
  try {
    let output = {
      data: null,
      success: false,
      message: "",
      status: 501,
    };

    let query = { status: { $in: config.get("keys.postDisbursalStatus") } };
    // "lenderDetails.lenderId": mongoose.Types.ObjectId(model.userId),

    if (!IsNullEmpty(model.userId)) {
      let userInfo = await UserInfo.findById(model.userId);

      if (!IsNullEmpty(userInfo)) {
        let borrowingPartner = await UserPartnerInfo.aggregate([
          { $match: { userId: mongoose.Types.ObjectId(userInfo._id) } },
          {
            $lookup: {
              from: "PartnerMaster",
              foreignField: "_id",
              localField: "partnerId",
              as: "borrowingPartnerMaster"
            }
          },
          {
            $unwind: "$borrowingPartnerMaster"
          },
          {
            $match: { "borrowingPartnerMaster.partnerType": "Borrowing Partner" }
          },
          {
            $project: {
              _id: 1,
              userId: 1,
              partnerId: "$borrowingPartnerMaster._id",
              partnerCode: "$borrowingPartnerMaster.partnerCode",
              isApiCall: "$borrowingPartnerMaster.isApiCall",
              displayName: "$borrowingPartnerMaster.displayName",
              partnerName: "$borrowingPartnerMaster.partnerName",
            }
          }
        ]);

        query["partnerId"] = mongoose.Types.ObjectId(borrowingPartner[0].partnerId);


        if (!IsNullEmpty(model.loanNo)) {
          query["loanNo"] = model.loanNo;
        }

        // if (!IsNullEmpty(model.fromDate)) {
        //   let fromParts = model.fromDate.split("/");
        //   let fromDate = new Date(
        //     fromParts[2],
        //     fromParts[1] - 1,
        //     fromParts[0],
        //     0,
        //     0,
        //     0
        //   );
        //   query["insertedOn"] = { $gte: fromDate };
        // }

        // if (!IsNullEmpty(model.toDate)) {
        //   let toParts = model.toDate.split("/");
        //   let toDate = new Date(toParts[2], toParts[1] - 1, toParts[0], 23, 59, 0);
        //   query["insertedOn"] = { $lte: toDate };
        // }

        let fromParts = model.fromDate.split("/");
        let fromDate = new Date(fromParts[2], fromParts[1] - 1, fromParts[0], 0, 0, 0);
        let toParts = model.toDate.split("/");
        let toDate = new Date(toParts[2], toParts[1] - 1, toParts[0], 23, 59, 59);

        if (!IsNullEmpty(model.productId)) {
          query["productId"] = model.productId;
        }

        if (!IsNullEmpty(model.merchantCode)) {
          query["merchantCode"] = model.merchantCode;
        }

        let query1 = {
          "userDetails": { $exists: true, $ne: [] },
        };

        if (!IsNullEmpty(model.name)) {
          query1["$or"] = [
            {
              "userDetails.firstName": {
                $regex: model.name,
                $options: "i",
              },
            },
            {
              "userDetails.lastName": {
                $regex: model.name,
                $options: "i",
              },
            },
          ];
        }

        if (!model.merchantCode && !model.productId && !model.loanNo && !model.name)
          query["insertedOn"] = { $gte: fromDate, $lte: toDate };

        let sortBy = model.sortBy;

        let loanInfo = await LoanInfo.find(query).select({
          _id: 1,
          loanNo: 1,
          amount: 1,
          status: 1,
          maturity: 1,
          interestRate: 1,
          repaymentFrequency: 1,
          lenderDetails: 1,
          merchantCode: 1,
          productId: 1,
          trxnId: 1,
          disbursalDetails: 1,
          partnerLoans: 1,
          insertedOn: 1,
          userId: 1,
          principalAmount: 1,
          interestAmount: 1
        }).sort(sortBy);

        const userIds = loanInfo.map(x => x.userId);
        query1["_id"] = { $in: userIds };

        const userDetails = await UserInfo.find(query1).select({ _id: 1, userDetails: 1 }).sort(sortBy);
        let userHash = {};
        for (let i = 0; i < userDetails.length; i++) {
          userHash[userDetails[i]._id.toString()] = userDetails[i];
        }

        const productIds = loanInfo.map(x => x.productId);
        const partnerLoans = await PartnerLoans.find({ productId: { $in: productIds } }).sort(sortBy);

        const loanIds = loanInfo.map(x => x._id);

        if (!IsNullEmpty(loanInfo)) {
          let reports = [];
          for (const itemLoanInfo of loanInfo) {

            let disbursalAmount = itemLoanInfo.disbursalDetails.disbursalAmount ? itemLoanInfo.disbursalDetails.disbursalAmount : 0;
            if (disbursalAmount === 0) {

              const partnerLoan = partnerLoans.filter(x => x.productId == itemLoanInfo.productId);

              if (partnerLoan && partnerLoan[0] && partnerLoan[0].notifications && partnerLoan[0].notifications.length > 0) {

                let notification = _.filter(partnerLoan[0].notifications, x => x.type === '5');

                if (notification && notification.length > 0)
                  disbursalAmount = notification[notification.length - 1].amountDisbursed ? notification[notification.length - 1].amountDisbursed : 0;
              }

              if (disbursalAmount === 0)
                if (partnerLoan && partnerLoan[0] && partnerLoan[0].disbursal && partnerLoan[0].disbursal.length > 0) {

                  const length = partnerLoan[0].disbursal.length - 1;

                  disbursalAmount = partnerLoan[0].disbursal[length] && partnerLoan[0].disbursal[length].amount ? partnerLoan[0].disbursal[length].amount : 0;
                }
            }
            const loanInfoUserDetails = userHash[itemLoanInfo.userId.toString()];

            let loanDetails = {
              loanId: itemLoanInfo._id,
              loanNo: itemLoanInfo.loanNo,
              amount: itemLoanInfo.amount,
              merchantCode: itemLoanInfo.merchantCode,
              productId: itemLoanInfo.productId,
              trxnId: itemLoanInfo.trxnId,
              name: getName(loanInfoUserDetails.userDetails.firstName, loanInfoUserDetails.userDetails.middleName, loanInfoUserDetails.userDetails.lastName),
              disbursedOn: itemLoanInfo.disbursalDetails && itemLoanInfo.disbursalDetails.disbursedOn ? getDMYFormattedDate(itemLoanInfo.disbursalDetails.disbursedOn, "/") : "",
              disbursalAmount: disbursalAmount,
              insertedOn: itemLoanInfo.insertedOn ? getDMYFormattedDate(itemLoanInfo.insertedOn, "/") : ""
            };
            // const installments = installmentDetails.data.filter(x => x.loanNo.toString() == itemLoanInfo.loanNo.toString());

            let totalPrincipleRecovered = itemLoanInfo.principalAmount.collected;

            let totalInterestRecovered = itemLoanInfo.interestAmount.collected;

            let totalRePaymentAmount = itemLoanInfo.principalAmount.collected + itemLoanInfo.interestAmount.collected;

            let totalLenderInvested = itemLoanInfo.amount;

            //Number.EPSILON is used to round to 2 digit
            if (totalPrincipleRecovered > itemLoanInfo.amount - 10)
              totalPrincipleRecovered = itemLoanInfo.amount;

            totalPrincipleRecovered = Math.round((totalPrincipleRecovered + Number.EPSILON) * 100) / 100;
            totalInterestRecovered = Math.round((totalInterestRecovered + Number.EPSILON) * 100) / 100;
            totalRePaymentAmount = Math.round((totalRePaymentAmount + Number.EPSILON) * 100) / 100;

            let currentInvestment = itemLoanInfo.amount - (itemLoanInfo.amount / totalLenderInvested) * totalPrincipleRecovered;

            // if (totalPrincipleRecovered > itemLoanInfo.amount - 10)
            //   totalPrincipleRecovered = itemLoanInfo.amount;

            if (currentInvestment < 10) currentInvestment = 0;

            currentInvestment = Math.round((currentInvestment + Number.EPSILON) * 100) / 100;

            let principleRecovered = (itemLoanInfo.amount / totalLenderInvested) * totalPrincipleRecovered;
            principleRecovered = Math.round((principleRecovered + Number.EPSILON) * 100) / 100;

            let intersetRecovered = (itemLoanInfo.amount / totalLenderInvested) * totalInterestRecovered;
            intersetRecovered = Math.round((intersetRecovered + Number.EPSILON) * 100) / 100;

            loanDetails.currentInvestment = currentInvestment;
            loanDetails.intersetRecovered = intersetRecovered;
            loanDetails.principalRecovered = principleRecovered;

            reports.push(loanDetails);
          }
          logger.logs("reports", reports);
          return {
            data: reports,
            success: true,
            message: "details fetched successfully",
            status: 200,
          };
        } else {
          return {
            data: loanInfo,
            success: true,
            message: "No data found",
            status: 200,
          };
        }
      } else {
        output.message = "user Id does not exists";
        return output;
      }
    }
  } catch (ex) {
    logger.logs(ex);
    saveErrorLog("getPortFolioDetailsForBorrowingPartner", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    return {
      success: false,
      message: ex.message,
      status: 501,
    };
  }
};


module.exports.getPortFolioDetailsForBorrowingPartnerV2 = async function (model) {
  try {
    let output = {
      data: null,
      success: false,
      message: "",
      status: 501,
    };

    const limit = 10;
    const skip = model.page ? (+model.page - 1) * limit : 0

    let query = { status: { $in: config.get("keys.postDisbursalStatus") } };

    if (!IsNullEmpty(model.userId)) {
      let userInfo = await UserInfo.findById(model.userId)

      if (!IsNullEmpty(userInfo)) {

        let userPartner = await UserPartnerInfo.findOne({ userId: userInfo._id }).lean()

        if (!userPartner) {
          output.message = "user partner does not exists";
          return output;
        }

        let borrowingPartner = await PartnerMaster.findById(userPartner.partnerId).lean()

        if (!borrowingPartner) {
          output.message = "borrowing partner does not exists";
          return output;
        }

        query["partnerId"] = borrowingPartner._id


        if (model.loanNo) {
          query["loanNo"] = model.loanNo;
        }

        let fromParts = model.fromDate.split("/");
        let fromDate = new Date(fromParts[2], fromParts[1] - 1, fromParts[0], 0, 0, 0);
        let toParts = model.toDate.split("/");
        let toDate = new Date(toParts[2], toParts[1] - 1, toParts[0], 23, 59, 59);

        if (!IsNullEmpty(model.productId)) {
          query["productId"] = model.productId;
        }

        if (!IsNullEmpty(model.merchantCode)) {
          query["merchantCode"] = model.merchantCode;
        }

        let query1 = {
          "userDetails": { $exists: true, $ne: [] },
        };

        if (!IsNullEmpty(model.name)) {
          query1["$or"] = [
            {
              "userDetails.firstName": {
                $regex: model.name,
                $options: "i",
              },
            },
            {
              "userDetails.lastName": {
                $regex: model.name,
                $options: "i",
              },
            },
          ];
        }

        if (!model.merchantCode && !model.productId && !model.loanNo && !model.name)
          query["disbursalDetails.disbursedOn"] = { $gte: fromDate, $lte: toDate };

        let sortBy = model.sortBy;

        const totalLoans = await LoanInfo.countDocuments(query)

        if (!totalLoans) {
          return {
            data: [],
            success: true,
            message: "No data found",
            status: 200,
          };
        }

        let project = {
          _id: 1,
          loanNo: 1,
          amount: 1,
          status: 1,
          maturity: 1,
          interestRate: 1,
          repaymentFrequency: 1,
          lenderDetails: 1,
          merchantCode: 1,
          productId: 1,
          trxnId: 1,
          disbursalDetails: 1,
          partnerLoans: 1,
          insertedOn: 1,
          userId: 1,
          principalAmount: 1,
          interestAmount: 1
        }

        let loanInfo = await LoanInfo.find(query).select(project).sort(sortBy).skip(skip).limit(limit).lean();

        let userIds = [], productIds = [], loanIds = []

        loanInfo.forEach(element => {
          loanIds.push(element._id)
          userIds.push(element.userId)
          productIds.push(element.productId)
        });

        query1["_id"] = { $in: userIds };

        const userDetails = await UserInfo.find(query1).select({ _id: 1, userDetails: 1 })

        let userHash = _.keyBy(userDetails, "_id")

        let reports = [];

        for (const itemLoanInfo of loanInfo) {

          let disbursalAmount = itemLoanInfo.disbursalDetails.disbursalAmount ? itemLoanInfo.disbursalDetails.disbursalAmount : 0;

          const loanInfoUserDetails = userHash[itemLoanInfo.userId.toString()];

          let loanDetails = {
            loanId: itemLoanInfo._id,
            loanNo: itemLoanInfo.loanNo,
            amount: itemLoanInfo.amount,
            merchantCode: itemLoanInfo.merchantCode,
            productId: itemLoanInfo.productId,
            trxnId: itemLoanInfo.trxnId,
            name: getName(loanInfoUserDetails.userDetails.firstName, loanInfoUserDetails.userDetails.middleName, loanInfoUserDetails.userDetails.lastName),
            disbursedOn: itemLoanInfo.disbursalDetails && itemLoanInfo.disbursalDetails.disbursedOn ? getDMYFormattedDate(itemLoanInfo.disbursalDetails.disbursedOn, "/") : "",
            disbursalAmount: disbursalAmount,
            insertedOn: itemLoanInfo.insertedOn ? getDMYFormattedDate(itemLoanInfo.insertedOn, "/") : "",
            currentInvestment: itemLoanInfo.principalAmount ? itemLoanInfo.principalAmount.outstanding : ""
          };

          let totalPrincipleRecovered = itemLoanInfo.principalAmount.collected;

          let totalInterestRecovered = itemLoanInfo.interestAmount.collected;

          let totalRePaymentAmount = itemLoanInfo.principalAmount.collected + itemLoanInfo.interestAmount.collected;

          let totalLenderInvested = itemLoanInfo.amount;

          //Number.EPSILON is used to round to 2 digit
          if (totalPrincipleRecovered > itemLoanInfo.amount - 10)
            totalPrincipleRecovered = itemLoanInfo.amount;

          totalPrincipleRecovered = Math.round((totalPrincipleRecovered + Number.EPSILON) * 100) / 100;
          totalInterestRecovered = Math.round((totalInterestRecovered + Number.EPSILON) * 100) / 100;
          totalRePaymentAmount = Math.round((totalRePaymentAmount + Number.EPSILON) * 100) / 100;

          // let currentInvestment = itemLoanInfo.amount - (itemLoanInfo.amount / totalLenderInvested) * totalPrincipleRecovered;

          // if (currentInvestment < 10) currentInvestment = 0;

          // currentInvestment = Math.round((currentInvestment + Number.EPSILON) * 100) / 100;

          let principleRecovered = (itemLoanInfo.amount / totalLenderInvested) * totalPrincipleRecovered;
          principleRecovered = Math.round((principleRecovered + Number.EPSILON) * 100) / 100;

          let intersetRecovered = (itemLoanInfo.amount / totalLenderInvested) * totalInterestRecovered;
          intersetRecovered = Math.round((intersetRecovered + Number.EPSILON) * 100) / 100;

          // loanDetails.currentInvestment = currentInvestment;
          loanDetails.intersetRecovered = intersetRecovered;
          loanDetails.principalRecovered = principleRecovered;

          reports.push(loanDetails);
        }
        logger.logs("reports", reports);
        return {
          data: reports,
          success: true,
          message: "details fetched successfully",
          status: 200,
          totalPages: Math.ceil(totalLoans / limit)
        };

      } else {
        output.message = "user Id does not exists";
        return output;
      }

    }

  } catch (ex) {
    logger.logs(ex);
    saveErrorLog("getPortFolioDetailsForBorrowingPartner", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    return {
      success: false,
      message: ex.message,
      status: 501,
    };
  }
};

module.exports.getBorrowingPartnerPortFolioDetailsForDownload = async function (model) {
  try {
    let output = {
      data: null,
      success: false,
      message: "",
      status: 501,
    };

    let query = { status: { $in: config.get("keys.postDisbursalStatus") } };

    if (!IsNullEmpty(model.userId)) {
      let userInfo = await UserInfo.findById(model.userId)

      if (!IsNullEmpty(userInfo)) {

        let userPartner = await UserPartnerInfo.findOne({ userId: userInfo._id }).lean()

        if (!userPartner) {
          output.message = "user partner does not exists";
          return output;
        }

        let borrowingPartner = await PartnerMaster.findById(userPartner.partnerId).lean()

        if (!borrowingPartner) {
          output.message = "borrowing partner does not exists";
          return output;
        }

        query[model.isLender ? "lenderDetails.lenderPartnerId" : 'partnerId'] = borrowingPartner._id

        if (model.loanNo) {
          query["loanNo"] = model.loanNo;
        }


        let fromParts = model.fromDate.split("/");
        let fromDate = new Date(fromParts[2], fromParts[1] - 1, fromParts[0], 0, 0, 0);
        let toParts = model.toDate.split("/");
        let toDate = new Date(toParts[2], toParts[1] - 1, toParts[0], 23, 59, 59);

        if (!IsNullEmpty(model.productId)) {
          query["productId"] = model.productId;
        }

        if (!IsNullEmpty(model.merchantCode)) {
          query["merchantCode"] = model.merchantCode;
        }

        let query1 = {
          "userDetails": { $exists: true, $ne: [] },
        };

        if (!IsNullEmpty(model.name)) {
          query1["$or"] = [
            {
              "userDetails.firstName": {
                $regex: model.name,
                $options: "i",
              },
            },
            {
              "userDetails.lastName": {
                $regex: model.name,
                $options: "i",
              },
            },
          ];
        }

        if (!model.merchantCode && !model.productId && !model.loanNo && !model.name)
          query["disbursalDetails.disbursedOn"] = { $gte: fromDate, $lte: toDate };

        let project = {
          _id: 1,
          loanNo: 1,
          amount: 1,
          status: 1,
          maturity: 1,
          interestRate: 1,
          repaymentFrequency: 1,
          lenderDetails: 1,
          merchantCode: 1,
          productId: 1,
          trxnId: 1,
          disbursalDetails: 1,
          partnerLoans: 1,
          insertedOn: 1,
          userId: 1,
          principalAmount: 1,
          interestAmount: 1
        }

        let loanInfo = await LoanInfo.find(query).select(project).lean();

        let userIds = [], loanIds = []

        loanInfo.forEach(element => {
          loanIds.push(element._id)
          userIds.push(element.userId)
        });

        query1["_id"] = { $in: userIds };

        const userDetails = await UserInfo.find(query1).select({ _id: 1, userDetails: 1, panNo: 1 }).lean()

        let userHash = _.keyBy(userDetails, "_id")

        let reports = [];

        for (const itemLoanInfo of loanInfo) {

          let disbursalAmount = itemLoanInfo.disbursalDetails.disbursalAmount ? itemLoanInfo.disbursalDetails.disbursalAmount : 0;

          const loanInfoUserDetails = userHash[itemLoanInfo.userId.toString()];

          let loanDetails = {
            loanId: itemLoanInfo._id,
            loanNo: itemLoanInfo.loanNo,
            amount: itemLoanInfo.amount,
            merchantCode: itemLoanInfo.merchantCode,
            productId: itemLoanInfo.productId,
            trxnId: itemLoanInfo.trxnId,
            name: getName(loanInfoUserDetails.userDetails.firstName, loanInfoUserDetails.userDetails.middleName, loanInfoUserDetails.userDetails.lastName),
            disbursedOn: itemLoanInfo.disbursalDetails && itemLoanInfo.disbursalDetails.disbursedOn ? getDMYFormattedDate(itemLoanInfo.disbursalDetails.disbursedOn, "/") : "",
            disbursalAmount: disbursalAmount,
            insertedOn: itemLoanInfo.insertedOn ? getDMYFormattedDate(itemLoanInfo.insertedOn, "/") : ""
          };

          if (model.isLender) {
            loanDetails.pan = loanInfoUserDetails.panNo
            loanDetails.lenderLoanId = itemLoanInfo.lenderDetails[0].lenderLoanId || ""
          }

          let totalPrincipleRecovered = itemLoanInfo.principalAmount.collected;

          let totalInterestRecovered = itemLoanInfo.interestAmount.collected;

          let totalRePaymentAmount = itemLoanInfo.principalAmount.collected + itemLoanInfo.interestAmount.collected;

          let totalLenderInvested = itemLoanInfo.amount;

          //Number.EPSILON is used to round to 2 digit
          if (totalPrincipleRecovered > itemLoanInfo.amount - 10)
            totalPrincipleRecovered = itemLoanInfo.amount;

          totalPrincipleRecovered = Math.round((totalPrincipleRecovered + Number.EPSILON) * 100) / 100;
          totalInterestRecovered = Math.round((totalInterestRecovered + Number.EPSILON) * 100) / 100;
          totalRePaymentAmount = Math.round((totalRePaymentAmount + Number.EPSILON) * 100) / 100;

          let currentInvestment = itemLoanInfo.amount - (itemLoanInfo.amount / totalLenderInvested) * totalPrincipleRecovered;

          if (currentInvestment < 10) currentInvestment = 0;

          currentInvestment = Math.round((currentInvestment + Number.EPSILON) * 100) / 100;

          let principleRecovered = (itemLoanInfo.amount / totalLenderInvested) * totalPrincipleRecovered;
          principleRecovered = Math.round((principleRecovered + Number.EPSILON) * 100) / 100;

          let intersetRecovered = (itemLoanInfo.amount / totalLenderInvested) * totalInterestRecovered;
          intersetRecovered = Math.round((intersetRecovered + Number.EPSILON) * 100) / 100;

          loanDetails.currentInvestment = currentInvestment;
          loanDetails.intersetRecovered = intersetRecovered;
          loanDetails.principalRecovered = principleRecovered;

          reports.push(loanDetails);
        }
        logger.logs("reports", reports);
        return {
          data: reports,
          success: true,
          message: "details fetched successfully",
          status: 200,
        };

      } else {
        output.message = "user Id does not exists";
        return output;
      }

    }

  } catch (ex) {
    logger.logs(ex);
    saveErrorLog("getPortFolioDetailsForBorrowingPartner", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    return {
      success: false,
      message: ex.message,
      status: 501,
    };
  }
};


module.exports.getRepaymentDetails = async function (model, req) {

  const showPaymentOptions = model.showPaymentOptions; // Used for collection queue tele calling.
  let output = {
    data: null,
    success: false,
    message: "",
    status: 200,
  };
  try {
    if (!IsNullEmpty(model.loanId) || model.loanNo) {

      // let loanInfo = await LoanInfo.findById(model.loanId).select({ _id: 1, loanNo: 1, loanContractDetails: 1 });

      if (req && (!req.user.role || (req.user.role != ROLES.ADMIN && !req.user.partnerId))) {
        output.message = "Invalid Role/Missing PartnerId"
        return output;
      }

      let loanInfoQuery = {
        _id: model.loanId
      }

      if (model.loanNo) {
        loanInfoQuery = {
          loanNo: model.loanNo
        }
      }

      if (req) {

        if (req.user.role && req.user.role == ROLES.LENDING_PARTNER) {
          loanInfoQuery["lenderDetails.lenderPartnerId"] = req.user.partnerId;
        } else if (req.user.role && (req.user.role == ROLES.BORROWING_PARTNER) || (req.user.role == ROLES.USER)) {
          loanInfoQuery["partnerId"] = req.user.partnerId;
        }

      }

      let loanInfo = await LoanInfo.findOne(loanInfoQuery).select({ _id: 1, loanNo: 1, loanContractDetails: 1 });

      const loanInstallments = await installmentService.convertEmiDetailsFormat(loanInfo._id);

      let emiList = [];

      let summary = {
        pendingEmiCount: 0,
        pendingPricipalAmount: 0,
        pendingInterestAmount: 0,
        pendingEmiAmount: 0,
        pendingOtherCharges: 0,
        pendingTotalAmount: 0
      };

      const todayDate = new Date();

      const today = new Date(todayDate.getFullYear(), todayDate.getMonth(), todayDate.getDate(), 5, 30, 0, 0);

      if (loanInstallments.data && loanInstallments.data.length > 0) {

        for (const emi of loanInstallments.data) {
          const obj = {
            emiNumber: emi.emiNumber,
            rePaymentAmount: emi.rePaymentAmount,
            rePaymentStatus: emi.rePaymentStatus,
            rePaymentReceivedDate: emi.rePaymentReceivedDate,
            interestAmount: emi.interestAmount,
            principalOutstanding: emi.principalOutstanding,
            principalRecovered: emi.principalRecovered,
            otherCharges: emi.penalty + emi.moratoriumInterest,
            totalEmiAmount: emi.rePaymentAmount + emi.penalty + emi.moratoriumInterest,
            rePaymentDate: emi.rePaymentDate
          };

          if (showPaymentOptions) {
            obj.holdWalletCollection = emi.holdCollection ? true : false
          }

          if (emi.rePaymentStatus.toLowerCase() === "pending" && emi.rePaymentDate < today) {
            summary.pendingEmiCount += 1;
            summary.pendingPricipalAmount += emi.principalRecovered;
            summary.pendingInterestAmount += emi.interestAmount;
            summary.pendingEmiAmount += emi.rePaymentAmount;
            summary.pendingOtherCharges += emi.penalty + emi.moratoriumInterest;
            summary.pendingTotalAmount += emi.rePaymentAmount + emi.penalty + emi.moratoriumInterest;

            if (showPaymentOptions) {
              obj.showPaymentOptions = true;
            }
          }
          emiList.push(obj);
        }
      }

      summary.pendingPricipalAmount = Math.round((summary.pendingPricipalAmount + Number.EPSILON) * 100) / 100;
      summary.pendingInterestAmount = Math.round((summary.pendingInterestAmount + Number.EPSILON) * 100) / 100;
      summary.pendingEmiAmount = Math.round((summary.pendingEmiAmount + Number.EPSILON) * 100) / 100;
      summary.pendingOtherCharges = Math.round((summary.pendingOtherCharges + Number.EPSILON) * 100) / 100;
      summary.pendingTotalAmount = Math.round((summary.pendingTotalAmount + Number.EPSILON) * 100) / 100;

      output = {
        data: { loanNo: loanInfo.loanNo, loanContractDetails: loanInfo.loanContractDetails, emiDetails: emiList, summary, _id: loanInfo._id },
        success: true,
        message: "fetched successfully",
        status: 200,
      };

    } else {
      output.message = "loanId is mandatory";
    }
  } catch (ex) {
    logger.logs(ex);
    output.message = ex.message;
  }
  return output;
};

module.exports.getPortFolioDetails = async function (model) {
  let output = {
    data: null,
    success: false,
    message: "",
    status: 501,
  };
  try {
    const userPartnerInfo = await UserPartnerInfo.findOne({ userId: mongoose.Types.ObjectId(model.userId) }).select({ partnerId: 1 });

    let query = {
      status: { $in: config.get("keys.postDisbursalStatus") },
      "lenderDetails.lenderPartnerId": mongoose.Types.ObjectId(userPartnerInfo.partnerId),
    };

    let loanInfo = await LoanInfo.aggregate([
      {
        $match: query,
      },
      {
        $group: {
          _id: "$_id",
          invested: { $sum: "$lenderDetails.amount" },
          loanNo: { $first: "$loanNo" },
          amount: { $first: "$amount" },
          status: { $first: "$status" },
          maturity: { $first: "$maturity" },
          interestRate: { $first: "$interestRate" },
          repaymentFrequency: { $first: "$repaymentFrequency" },
          lenderDetails: { $first: "$lenderDetails" },
        },
      }
    ]);

    const today = new Date();

    let tpr = 0;
    let tir = 0;
    let rd30 = 0;
    let rd60 = 0;
    let rd90 = 0;
    let rd120 = 0;
    let lenderInvested = 0;
    let emiRecovered = 0;
    let totalLoanAmt = 0;
    let payout = 0;
    let totalVACredit = 0;
    let daysInvested = 0;

    const loanIds = loanInfo.map(x => x._id);

    const installmentDetails = await installmentService.getMultipleLoansInstallments(loanIds);

    if (installmentDetails.success) {
      for (itemLoanInfo of loanInfo) {
        logger.logs("loanNo", itemLoanInfo.loanNo);
        const installments = installmentDetails.data.filter(x => x.loanId.toString() == itemLoanInfo._id.toString());

        let totalPrincipleRecovered = installments.filter((x) => x.status == "Received").reduce(function (prev, cur) {
          return prev + cur.installmentPrincipal;
        }, 0);

        if (totalPrincipleRecovered > itemLoanInfo.amount - 10)
          totalPrincipleRecovered = itemLoanInfo.amount;

        tpr += totalPrincipleRecovered ? totalPrincipleRecovered : 0;

        let totalInterestRecovered = installments
          .filter((x) => x.status == "Received")
          .reduce(function (prev, cur) {
            return prev + cur.installmentInterest;
          }, 0);

        tir += totalInterestRecovered ? totalInterestRecovered : 0;

        let totalEmiRecovered = installments
          .filter((x) => x.status == "Received")
          .reduce(function (prev, cur) {
            return prev + cur.installmentAmount;
          }, 0);

        emiRecovered += totalEmiRecovered ? totalEmiRecovered : 0;

        const day30 = dateAdd("day", -30, today);
        let repaymentDelay30Days = installments
          .filter(
            (x) =>
              x.status == "Pending" &&
              x.dueDate >= day30 &&
              x.dueDate <= today
          )
          .reduce(function (prev, cur) {
            return prev + cur.installmentAmount;
          }, 0);

        rd30 += repaymentDelay30Days ? repaymentDelay30Days : 0;

        const day31 = dateAdd("day", -31, today);
        const day60 = dateAdd("day", -60, today);
        let repaymentDelay60Days = installments
          .filter(
            (x) =>
              x.status == "Pending" &&
              x.dueDate >= day60 &&
              x.dueDate <= day31
          )
          .reduce(function (prev, cur) {
            return prev + cur.installmentAmount;
          }, 0);

        rd60 += repaymentDelay60Days ? repaymentDelay60Days : 0;

        const day61 = dateAdd("day", -61, today);
        const day90 = dateAdd("day", -90, today);
        let repaymentDelay90Days = installments
          .filter(
            (x) =>
              x.status == "Pending" &&
              x.dueDate >= day90 &&
              x.dueDate <= day61
          )
          .reduce(function (prev, cur) {
            return prev + cur.installmentAmount;
          }, 0);

        rd90 += repaymentDelay90Days ? repaymentDelay90Days : 0;

        let repaymentDelayGreaterThan90Days = installments
          .filter(
            (x) => x.status == "Pending" && x.dueDate < day90
          )
          .reduce(function (prev, cur) {
            return prev + cur.installmentAmount;
          }, 0);

        rd120 += repaymentDelayGreaterThan90Days ? repaymentDelayGreaterThan90Days : 0;

        let totalLenderInvested = itemLoanInfo.lenderDetails.amount ? itemLoanInfo.lenderDetails.amount : 0;

        totalLoanAmt += itemLoanInfo.amount ? itemLoanInfo.amount : 0;

        lenderInvested += totalLenderInvested ? totalLenderInvested : 0;

      }

      const payOutModel = await PayInPayOut.find({ type: "Payout", partnerId: mongoose.Types.ObjectId(userPartnerInfo.partnerId), isActive: true });

      const payInModel = await PayInPayOut.find({ type: "Payin", partnerId: mongoose.Types.ObjectId(userPartnerInfo.partnerId), isActive: true });

      let get_DaysInvested = await getDaysInvested(userPartnerInfo.partnerId, null);

      let payOutAmount = payOutModel
        .reduce(function (prev, cur) {
          return prev + cur.amount;
        }, 0);

      payout += payOutAmount;

      let payInAmount = payInModel
        .reduce(function (prev, cur) {
          return prev + cur.amount;
        }, 0);

      totalVACredit += payInAmount;

      daysInvested += parseFloat(get_DaysInvested.daysInvested);

      // let ledger = loanInfo[0].ledgerDetails[0];

      // //Payout
      // let payout = ledger.payOut ? ledger.payOut : 0;

      let activeInvestment = totalVACredit - payout;

      let totalAmtInVA = activeInvestment - (lenderInvested - (lenderInvested / totalLoanAmt) * emiRecovered);

      if (totalAmtInVA < 0) totalAmtInVA = 0;

      let currentInvestment = lenderInvested - (lenderInvested / totalLoanAmt) * tpr;

      let roi = 0;
      if (totalVACredit == 0 || daysInvested == 0) {
        roi = 0;
      } else {
        let intByTotalVA = tir / totalVACredit;
        intByTotalVA = 1 + intByTotalVA;
        let power = 365 / daysInvested;

        roi = (Math.pow(intByTotalVA, power) - 1) * 100;
      }

      data = {
        totalPrincipleRecovered: Math.round((tpr + Number.EPSILON) * 100) / 100,
        totalInterestRecovered: Math.round((tir + Number.EPSILON) * 100) / 100,
        repaymentDelay30Days: Math.round((rd30 + Number.EPSILON) * 100) / 100,
        repaymentDelay60Days: Math.round((rd90 + Number.EPSILON) * 100) / 100,
        repaymentDelay90Days: Math.round((rd60 + Number.EPSILON) * 100) / 100,
        repaymentDelay120Days: Math.round((rd120 + Number.EPSILON) * 100) / 100,
        payout: Math.round((payout + Number.EPSILON) * 100) / 100,
        totalAmtInVA: Math.round((totalAmtInVA < 0 ? 0 : totalAmtInVA + Number.EPSILON) * 100) / 100,
        totalLifeTimeInvestment: Math.round((totalVACredit + Number.EPSILON) * 100) / 100,
        daysInvested: 0,
        totalInvestment: Math.round((lenderInvested + Number.EPSILON) * 100) / 100,
        emiRecovered: Math.round((emiRecovered + Number.EPSILON) * 100) / 100,
        activeInvestment: Math.round((activeInvestment + Number.EPSILON) * 100) / 100,
        roi: Math.round((roi + Number.EPSILON) * 100) / 100,
        currentInvestment: Math.round((currentInvestment + Number.EPSILON) * 100) / 100,
        lenderInvested: lenderInvested,
        totalLoanAmt: totalLoanAmt
      };

      output = {
        data,
        success: true,
        message: "data fetched successfully",
        status: 200,
      };
    } else output.message = installmentDetails.messsage;
  } catch (ex) {
    logger.logs(ex);
    output.message = ex.message;
  }
  return output;
};

module.exports.getLenderPortFolioDetails = async function (model) {
  let output = {
    data: null,
    success: false,
    message: "",
    status: 501,
  };
  try {
    const endDate = new Date(Date.now());
    const startDate = new Date(dateAdd("day", -31, endDate));
    logger.logs("startDate", startDate);
    logger.logs("endDate", endDate);
    const userPartnerInfo = await UserPartnerInfo.findOne({ userId: mongoose.Types.ObjectId(model.userId) }).select({ partnerId: 1 });

    let query = { status: { $in: config.get("keys.postDisbursalStatus") }, "lenderDetails.lenderPartnerId": mongoose.Types.ObjectId(userPartnerInfo.partnerId), insertedOn: { $gt: startDate, $lte: endDate } };

    let sortBy = model.sortBy;

    const limit = 200;

    const totalLoans = await LoanInfo.find(query).countDocuments();
    logger.logs("query", query);
    logger.logs("totalLoans", totalLoans);
    let totalPages = parseInt(totalLoans / limit);
    totalPages = 1;
    let skip = 0;

    let records = [];

    for (let page = 0; page < totalPages + 1; page++) {
      skip = page * limit;
      // let loanInfo = await LoanInfo.aggregate([
      //   {
      //     $match: query,
      //   },
      //   { $unwind: "$lenderDetails" },
      //   {
      //     $group: {
      //       _id: "$_id",
      //       loanNo: { $first: "$loanNo" },
      //       amount: { $first: "$amount" },
      //       status: { $first: "$status" },
      //       maturity: { $first: "$maturity" },
      //       interestRate: { $first: "$interestRate" },
      //       repaymentFrequency: { $first: "$repaymentFrequency" },
      //       lenderDetails: { $first: "$lenderDetails" },
      //       principalAmount: { $first: "$principalAmount" },
      //       interestAmount: { $first: "$interestAmount" },
      //       insertedOn: { $first: "$interestAmount" }
      //     },
      //   },
      // ]).allowDiskUse(true).sort(sortBy);


      let loanInfo = await LoanInfo.find(query).select({
        _id: 1, loanNo: 1, amount: 1, status: 1, maturity: 1,
        interestRate: 1,
        lenderDetails: 1,
        repaymentFrequency: 1,
        principalAmount: 1,
        interestAmount: 1,
        insertedOn: 1,
        userId: 1
      }).sort(sortBy).skip(skip).limit(limit);

      const uniqueUsers = [...new Set(loanInfo.map(item => item.userId))];
      const userInfo = await UserInfo.find({ _id: { $in: uniqueUsers } }).select({ _id: 1, userDetails: 1 });
      let userHash = {};
      for (let i = 0; i < userInfo.length; i++) {
        userHash[userInfo[i]._id.toString()] = userInfo[i];
      }

      const loanIds = loanInfo.map(x => x._id);

      //const loanInstallments = await installmentService.getInstallmentsCollected(loanIds);

      //  if (loanInstallments.success) {
      for (const itemLoanInfo of loanInfo) {
        const user = userHash[itemLoanInfo.userId.toString()];

        const record = {
          _id: itemLoanInfo._id,
          loanNo: itemLoanInfo.loanNo,
          amount: itemLoanInfo.amount,
          status: itemLoanInfo.status,
          maturity: itemLoanInfo.maturity,
          interestRate: itemLoanInfo.interestRate,
          lenderDetails: itemLoanInfo.lenderDetails[0],
          repaymentFrequency: itemLoanInfo.repaymentFrequency,
          principalAmount: itemLoanInfo.principalAmount,
          interestAmount: itemLoanInfo.interestAmount,
          insertedOn: itemLoanInfo.insertedOn,
          userDetails: user.userDetails
        };

        record.investedAmount = Math.round((record.amount + Number.EPSILON) * 100) / 100;
        let a = record.investedAmount / record.amount;

        //const installments = loanInstallments.data.filter(x => x.loanId.toString() == record._id.toString());

        record.principalRecovered = record.principalAmount && record.principalAmount.collected ? record.principalAmount.collected : 0;

        record.interestRecovered = record.interestAmount && record.interestAmount.collected ? record.interestAmount.collected : 0

        let b = a * record.principalRecovered;

        let currentInvestment = record.amount - b;

        if (currentInvestment < 10) currentInvestment = 0;

        if (record.principalRecovered > record.amount - 10)
          record.principalRecovered = record.amount;

        record.currentInvestment = Math.round((currentInvestment + Number.EPSILON) * 100) / 100;

        record.principalRecovered = Math.round((record.principalRecovered + Number.EPSILON) * 100) / 100;

        record.interestRecovered = Math.round((record.interestRecovered + Number.EPSILON) * 100) / 100;

        records.push(record);
      }
      // } else output.message = loanInstallments.message;
    }

    output = {
      data: records,
      success: true,
      message: "data fetched successfully",
      status: 200,
    };
    //logger.logs("output", output);
  } catch (ex) {
    logger.logs(ex);
    saveErrorLog("getLenderPortFolioDetails", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    output.message = ex.message;
  }
  return output;
};

module.exports.getLenderPortFolioDetailsV2 = async function (model) {
  let output = {
    data: null,
    success: false,
    message: "",
    status: 501,
  };
  try {
    const userPartnerInfo = await UserPartnerInfo.findOne({ userId: mongoose.Types.ObjectId(model.userId) }).select({ partnerId: 1 });

    let query = { status: { $in: config.get("keys.postDisbursalStatus") }, "lenderDetails.lenderPartnerId": mongoose.Types.ObjectId(userPartnerInfo.partnerId) };

    if (model.fromDate && model.toDate) {
      query["disbursalDetails.disbursedOn"] = { $gt: new Date(model.fromDate), $lte: new Date(model.toDate) }
    }

    let sortBy = model.sortBy;

    const limit = 10;
    const skip = model.page ? (+model.page - 1) * limit : 0

    const totalLoans = await LoanInfo.countDocuments(query)

    let loanInfo = await LoanInfo.find(query).select({
      _id: 1, loanNo: 1, amount: 1, status: 1, maturity: 1,
      interestRate: 1,
      lenderDetails: 1,
      repaymentFrequency: 1,
      principalAmount: 1,
      interestAmount: 1,
      insertedOn: 1,
      userId: 1,
      disbursalDetails: 1
    }).sort(sortBy).skip(skip).limit(limit);

    const uniqueUsers = [...new Set(loanInfo.map(item => item.userId))];

    const userInfo = await UserInfo.find({ _id: { $in: uniqueUsers } }).select({ _id: 1, userDetails: 1, panNo: 1 });

    let userHash = {};

    for (let i = 0; i < userInfo.length; i++) {
      userHash[userInfo[i]._id.toString()] = userInfo[i];
    }

    let records = []

    for (const itemLoanInfo of loanInfo) {
      const user = userHash[itemLoanInfo.userId.toString()];
      let disbursalAmount = itemLoanInfo.disbursalDetails.disbursalAmount ? itemLoanInfo.disbursalDetails.disbursalAmount : 0;
      const record = {
        _id: itemLoanInfo._id,
        loanNo: itemLoanInfo.loanNo,
        amount: itemLoanInfo.amount,
        name: getName(user.userDetails.firstName, user.userDetails.middleName, user.userDetails.lastName),
        disbursedOn: itemLoanInfo.disbursalDetails && itemLoanInfo.disbursalDetails.disbursedOn ? getDMYFormattedDate(itemLoanInfo.disbursalDetails.disbursedOn, "/") : "",
        disbursalAmount: disbursalAmount,
        status: itemLoanInfo.status,
        maturity: itemLoanInfo.maturity,
        interestRate: itemLoanInfo.interestRate,
        lenderDetails: itemLoanInfo.lenderDetails[0],
        repaymentFrequency: itemLoanInfo.repaymentFrequency,
        principalAmount: itemLoanInfo.principalAmount,
        interestAmount: itemLoanInfo.interestAmount,
        insertedOn: itemLoanInfo.insertedOn ? getDMYFormattedDate(itemLoanInfo.insertedOn, "/") : "",
        userDetails: user ? user.userDetails : {},
        pan: user.panNo
      };

      record.investedAmount = Math.round((record.amount + Number.EPSILON) * 100) / 100;

      let a = record.investedAmount / record.amount;

      record.principalRecovered = record.principalAmount && record.principalAmount.collected ? record.principalAmount.collected : 0;

      record.interestRecovered = record.interestAmount && record.interestAmount.collected ? record.interestAmount.collected : 0

      let b = a * record.principalRecovered;

      let currentInvestment = record.amount - b;

      if (currentInvestment < 10) currentInvestment = 0;

      if (record.principalRecovered > record.amount - 10)
        record.principalRecovered = record.amount;

      record.currentInvestment = Math.round((currentInvestment + Number.EPSILON) * 100) / 100;

      record.principalRecovered = Math.round((record.principalRecovered + Number.EPSILON) * 100) / 100;

      record.interestRecovered = Math.round((record.interestRecovered + Number.EPSILON) * 100) / 100;

      records.push(record);
    }


    output = {
      data: records,
      success: true,
      message: "data fetched successfully",
      status: 200,
      totalPages: Math.ceil(totalLoans / limit)
    };

  } catch (ex) {
    logger.logs(ex);
    saveErrorLog("getLenderPortFolioDetails", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    output.message = ex.message;
  }
  return output;
};

module.exports.socialMediaRegistration = async function (model) {
  let output = {
    data: null,
    partnerId: null,
    success: false,
    message: "",
    status: 501,
  };
  try {
    let partner = await getPartner(null, null, model.partnerName);

    if (partner.success) {

      let user = await UserInfo.find({
        "userDetails.emailId": { $regex: new RegExp(model.emailId, "i") },
      }).select("-userDetails.password");

      if (user.length != 0) {
        return {
          data: "",
          success: false,
          message: "Please Enter Valid Email/Mobile",
          status: 400,
        };
      }

      user = new UserInfo({
        userDetails: {
          userName: model.emailId,
          firstName: model.firstName ? model.firstName.toUpperCase() : "",
          middleName: "",
          lastName: model.lastName ? model.lastName.toUpperCase() : "",
          role: "User",
          isMobileValidated: false,
          isEmailValidated: false,
          dob: "",
          passwordModifiedOn: null,
          registeredFrom: model.registeredFrom,
          imageUrl: model.imageUrl,
          socialMediaId: model.socialMediaId,
        },
        ipAddress: model.ipAddress,
        insertedOn: Date.now(),
        tnc: "Y",
      });

      user.userDetails.emailId.push(model.emailId);

      // user.partnerDetails = {
      //   partnerId: partner.data._id,
      //   partnerName: partner.data.partnerName,
      //   partnerType: partner.data.partnerType,
      // };

      // user.isPartnerUser = false;
      // user.isRetailer = true;

      user.kycStepDetails.push({
        Step: "KYC",
        isCompleted: false,
        insertedOn: Date.now(),
        completedOn: null,
      });

      user.kycStepDetails.push({
        Step: "Questionnairre",
        isCompleted: false,
        insertedOn: Date.now(),
        completedOn: null,
      });

      //Hashing the password
      const salt = await bycrypt.genSalt(10);
      user.userDetails.password = await bycrypt.hash(model.emailId, salt);

      const result = await user.save();

      const userPartnerInfo = new UserPartnerInfo({
        userId: mongoose.Types.ObjectId(user._id),
        partnerId: mongoose.Types.ObjectId(partner.data._id),
        merchantCode: null,
        isRetailer: true,
        isPartnerUser: false,
        loanHistory: [],
        ledgerDetails: null,
        insertedOn: Date.now(),
        updatedOn: Date.now()
      });

      await userPartnerInfo.save();

      output = {
        partnerId: mongoose.Types.ObjectId(partner.data._id),
        data: _.omit(result, ["userDetails.password"]),
        success: true,
        message: "User saved successfully",
        status: 200,
      };

    } else {
      output.message = partner.message;
    }
  } catch (ex) {
    saveErrorLog("socialMediaRegistration", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    output.message = ex.message;
  }
  return output;
};

async function getDaysInvested(partnerId, endDate) {
  let output = {
    daysInvested: 0,
    success: false,
    message: "",
    status: 501,
  };
  try {
    if (!IsNullEmpty(partnerId)) {
      if (IsNullEmpty(endDate)) {
        endDate = Date.now();
      } else if (typeof endDate == "string") {
        const toParts = endDate.split("/");
        endDate = new Date(toParts[2], toParts[1] - 1, toParts[0], 23, 59, 59);
      }

      let payIn = await PayInPayOut.find({
        partnerId: mongoose.Types.ObjectId(partnerId),
        type: "Payin",
        depositDate: { $lt: endDate },
      });
      let payOut = await PayInPayOut.find({
        partnerId: mongoose.Types.ObjectId(partnerId),
        type: "Payout",
        depositDate: { $lt: endDate },
      });

      let PayinWeightedAmt = 0;
      let PayOutWeightedAmt = 0;
      let PayinAmt = 0;

      if (!IsNullEmpty(payIn)) {
        PayinWeightedAmt = payIn
          .filter((x) => x.depositDate != null)
          .reduce(function (prev, cur) {
            const diff = new DateDiff(endDate, cur.depositDate);
            return prev + cur.amount * diff.days();
          }, 0);

        PayinAmt = payIn
          .filter((x) => x.depositDate != null)
          .reduce(function (prev, cur) {
            return prev + cur.amount;
          }, 0);
      }

      if (!IsNullEmpty(payOut)) {
        PayOutWeightedAmt = payOut
          .filter((x) => x.depositDate != null)
          .reduce(function (prev, cur) {
            const diff = new DateDiff(endDate, cur.depositDate);
            return prev + cur.amount * diff.days();
          }, 0);
      }

      if (PayinAmt != 0) {
        output.daysInvested = parseFloat((PayinWeightedAmt - PayOutWeightedAmt) / PayinAmt).toFixed(2);
        output.success = true;
        output.status = 200;
        output.message = "days invested calculated successfully";
      }
    } else {
      output.message = "Please provide partnerId";
      return output;
    }
  } catch (ex) {
    //console.log(ex);
    logger.logs(ex);
    saveErrorLog("getDaysInvested", apiPath, { partnerId, endDate }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    output.message = ex.message;
  }
  return output;
};

// module.exports.getTrueScorePartnerList = async function (userId) {

//   const user = await UserInfo.findById(userId);

//   if (!user)
//     return {
//       partnerList: []
//     }

//   if (!user.panNo)
//     return {
//       partnerList: []
//     }

//   const query = {
//     merchantCode: { $exists: true },
//     panNo: {
//       $regex: new RegExp("^" + user.panNo + "$", "i")
//     }
//   };

//   const userPartnerList = await UserInfo.aggregate([
//     {
//       $match: query,
//     },
//     {
//       $lookup: {
//         from: "PartnerMaster",
//         let: { partnerId: "$_id", displayTrueScore: "$displayTrueScore" },
//         pipeline: [
//           {
//             $match: {
//               $expr: {
//                 $and: [{ $eq: ["$$partnerId", "$partnerDetails.partnerId"] }, { $eq: ["$$displayTrueScore", true] }
//                 ],
//                 as: "PartnerMaster",
//               }
//             },
//     {
//             $project: {
//               merchanCode: 1,
//               panNo: 1,
//               insertedOn: 1,
//               userName: "$userDetails.userName",
//               role: "$userDetails.role",
//               emailId: "$userDetails.emailId",
//               mobileNo: "$userDetails.mobileNo",
//               firstName: "$userDetails.firstName",
//               middleName: "$userDetails.middleName",
//               lastName: "$userDetails.lastName",
//               partnerType: "$partnerDetails.partnerType",
//               partnerName: "$partnerDetails.partnerName",
//               balance: "$ledgerDetails.balance",
//               // lendingLoans: { $size: "$loans" },
//               // PartnerLoansCount: { $size: "$PartnerLoans" },
//               PartnerLoans: "$PartnerLoans",
//               PartnerMaster: "$PartnerMaster",
//               kycStatus: "$userDetails.kycStatus",
//             },
//           },
//           //{ $match: { PartnerLoansCount: { $gt: 1 } } },
//         ]);
// }

module.exports.updateShopDetails = async function (model) {
  let user = await UserInfo.findById(model.userId);
  if (!user)
    return {
      data: "",
      success: false,
      message: "User not found",
      status: 501
    }

  let partnerName = "";

  let loanId = model.loanId ? model.loanId : "";

  if (!loanId) {
    const loan = await LoanInfo.findOne({
      userId: model.userId
    }).sort({ updatedOn: -1 });

    if (loan)
      loanId = loan._id;
  }

  let loanInfo = await LoanInfo.findById(loanId);

  let shopDetails = [];

  for (const item of model.users) {

    if (item.add) {
      item.add = item.add.replace("#", "");
      item.add = item.add.replace("@", "");
    }

    if (item.shopType) {
      item.shopType = item.shopType.replace("#", "");
      item.shopType = item.shopType.replace("@", "");
    }

    const shop = {
      shopName: item.shopName ? item.shopName.toUpperCase() : "",
      shopType: item.shopType ? item.shopType.toUpperCase() : "",
      shopOwnershipType: item.shopOwnershipType ? item.shopOwnershipType : "",
      shopSector: item.shopSector ? item.shopSector :"",
      natureOfBussiness : item.natureOfBussiness ? item.natureOfBussiness : "",
      shopAddress: item.add ? item.add.toUpperCase() : "",
      city: item.city ? item.city.toUpperCase() : "",
      state: item.state ? item.state.toUpperCase() : "",
      pincode: item.pincode ? item.pincode.toUpperCase() : "",
      partner: item.partner ? item.partner : "",
      merchantCode: item.merchantCode ? item.merchantCode : "",
      trxnId: item.trxnId ? item.trxnId : "",
      productId: item.productId ? item.productId : "",
      shopLocation: item.shopLocation ? item.shopLocation : "",
      shopVintage: item.shopVintage ? item.shopVintage : "",
      companyType: item.companyType ? item.companyType : "",
      businessVintage: item.businessVintage ? item.businessVintage : "",
      contactNo: item.contactNo ? item.contactNo : "",
      partner: item.partner ? item.partner : partnerName,
      modifiedByPartner: item.partner && partnerName ? partnerName : "",
      insertedOn: item.insertedOn ? item.insertedOn : Date.now(),
      modifiedOn: item.partner && partnerName ? Date.now() : null,
      lastUpdatedBy: model.currentUserId ? model.currentUserId : null,
      lastUpdatedByRole: model.currentUserRole ? model.currentUserRole : null
    };

    shopDetails.push(shop);

  }

  user.shopDetails = shopDetails;
  user.shopDetailsUpdatedOn = Date.now();
  user.modifiedOn = Date.now();

  const completeStep = await updateUserAndLoanSteps(loanInfo, user, "Shop", null, null);

  if (!completeStep.success)
    return {
      data: "",
      success: false,
      message: updateStep.message,
      status: 400
    };

  loanInfo = completeStep.data.loan;

  user = completeStep.data.user;

  user.modifiedOn = Date.now();
  loanInfo.updatedOn = Date.now();

  await user.save();
  await loanInfo.save();

  user = null;
  loanInfo = null;

  return {
    data: "",
    success: true,
    message: "Shop details saved successfully",
    status: 200
  }

};

async function getKycUpdatedStatus(userId, model, loanId, partnerId, currentUserRole, partnerMerchantCode = null) {

  const user = await UserInfo.findById(userId);

  let kycStatus = user.userDetails.kycStatus ? user.userDetails.kycStatus : "Initiated";

  if (kycStatus !== "Approved")
    return kycStatus;

  let statusToUpdate = "";

  if (currentUserRole === "user") {
    const loan = await LoanInfo.find({ userId: userId, _id: { $ne: loanId }, status: { $nin: ["Draft", "Initiated"] } });

    if (loan.length > 0)
      statusToUpdate = "ReInitiated";
    else
      statusToUpdate = "Initiated";
  }
  else if (currentUserRole === "admin")
    statusToUpdate = "Under Review";

  //Check if any file is uploaded. If uploaded put it under review
  if (model.panFileStream || model.poaFileStream || model.poaBackFileStream || model.gstFileStream) {
    kycStatus = statusToUpdate;
    await backupUserInfo(userId);
    return kycStatus;
  }

  let partnerUserInfo = null;

  if (partnerMerchantCode)
    partnerUserInfo = await UserPartnerInfo.findOne({ userId: userId, partnerId: partnerId, merchantCode: partnerMerchantCode });
  else
    partnerUserInfo = await UserPartnerInfo.findOne({ userId: userId, partnerId: partnerId });

  const firstName = user.userDetails.firstName ? user.userDetails.firstName.trim().toLowerCase() : "";
  const middleName = user.userDetails.middleName ? user.userDetails.middleName.trim().toLowerCase() : "";
  const lastName = user.userDetails.lastName ? user.userDetails.lastName.trim().toLowerCase() : "";
  const mobileNo = user.userDetails.mobileNo && user.userDetails.mobileNo[0] && user.userDetails.mobileNo[0].mobileNumber ? user.userDetails.mobileNo[0].mobileNumber.trim().toLowerCase() : ""; //mobile number change
  const alternateMobile = user.userDetails.mobileNo && user.userDetails.mobileNo[1] && user.userDetails.mobileNo[1].mobileNumber ? user.userDetails.mobileNo[1].mobileNumber.trim().toLowerCase() : ""; //mobile number change
  const alternateEmailId = user.userDetails.emailId && user.userDetails.emailId[1] ? user.userDetails.emailId[1].trim().toLowerCase() : "";
  const merchantCode = partnerUserInfo.merchantCode ? partnerUserInfo.merchantCode.trim().toLowerCase() : "";
  const employment = user.userDetails.employment ? user.userDetails.employment.trim().toLowerCase() : "";
  const add1 = user.addressDetails && user.addressDetails[0] && user.addressDetails[0].add1 ? user.addressDetails[0].add1.trim().toLowerCase() : "";
  const add2 = user.addressDetails && user.addressDetails[0] && user.addressDetails[0].add2 ? user.addressDetails[0].add2.trim().toLowerCase() : "";
  const add3 = user.addressDetails && user.addressDetails[0] && user.addressDetails[0].add3 ? user.addressDetails[0].add3.trim().toLowerCase() : "";
  const city = user.addressDetails && user.addressDetails[0] && user.addressDetails[0].city ? user.addressDetails[0].city.trim().toLowerCase() : "";
  const state = user.addressDetails && user.addressDetails[0] && user.addressDetails[0].state ? user.addressDetails[0].state.trim().toLowerCase() : "";
  const pincode = user.addressDetails && user.addressDetails[0] && user.addressDetails[0].pincode ? user.addressDetails[0].pincode.trim().toLowerCase() : "";
  const panNo = user.panNo ? user.panNo.trim().toLowerCase() : "";
  const poaNumber = user.poaDetails && user.poaDetails[0] && user.poaDetails[0].poaNo ? user.poaDetails[0].poaNo.trim().toLowerCase() : "";
  const poaType = user.poaDetails && user.poaDetails[0] && user.poaDetails[0].poaType ? user.poaDetails[0].poaType.trim().toLowerCase() : "";
  const poaVerificationDocType = user.poaDetails && user.poaDetails[0] && user.poaDetails[0].poaVerificationDocType ? user.poaDetails[0].poaVerificationDocType.trim().toLowerCase() : "";
  const gstNumber = user.gstDetails && user.gstDetails[0] && user.gstDetails[0].gstNo ? user.gstDetails[0].gstNo.trim().toLowerCase() : "";
  const accountNumber = partnerUserInfo.bankDetails && partnerUserInfo.bankDetails[0] && partnerUserInfo.bankDetails[0].accountNo ? partnerUserInfo.bankDetails[0].accountNo.trim().toLowerCase() : "";
  const ifsc = partnerUserInfo.bankDetails && partnerUserInfo.bankDetails[0] && partnerUserInfo.bankDetails[0].ifsc ? partnerUserInfo.bankDetails[0].ifsc.trim().toLowerCase() : "";
  const accountType = partnerUserInfo.bankDetails && partnerUserInfo.bankDetails[0] && partnerUserInfo.bankDetails[0].accountType ? partnerUserInfo.bankDetails[0].accountType.trim().toLowerCase() : "";
  const accountName = partnerUserInfo.bankDetails && partnerUserInfo.bankDetails[0] && partnerUserInfo.bankDetails[0].accountName ? partnerUserInfo.bankDetails[0].accountName.trim().toLowerCase() : "";
  const dob = user.userDetails.dob ? getDMYFormattedDate(user.userDetails.dob, "/") : "";

  const currentFirstName = model.firstName ? model.firstName.trim().toLowerCase() : "";
  const currentMiddleName = model.middleName ? model.middleName.trim().toLowerCase() : "";
  const currentLastName = model.lastName ? model.lastName.trim().toLowerCase() : "";
  const currentMobileNo = model.mobileNo ? model.mobileNo.trim().toLowerCase() : "";
  const currentAlternateMobileNo = model.alternateMobile ? model.alternateMobile.trim().toLowerCase() : "";
  const currentAlternateEmailId = model.alternateEmail ? model.alternateEmail.trim().toLowerCase() : "";
  const currentMerchantCode = model.merchantCode ? model.merchantCode.trim().toLowerCase() : "";
  const currentEmployment = model.employment ? model.employment.trim().toLowerCase() : "";
  const currentAdd1 = model.add1 ? model.add1.trim().toLowerCase() : "";
  const currentAdd2 = model.add2 ? model.add2.trim().toLowerCase() : "";
  const currentAdd3 = model.add3 ? model.add3.trim().toLowerCase() : "";
  const currentCity = model.city ? model.city.trim().toLowerCase() : "";
  const currentState = model.state ? model.state.trim().toLowerCase() : "";
  const currentPincode = model.pincode ? model.pincode.trim().toLowerCase() : "";
  const currentPanNo = model.panNo ? model.panNo.trim().toLowerCase() : "";
  const currentPoaNo = model.poaNo ? model.poaNo.trim().toLowerCase() : "";
  const currentPoaType = model.poaType ? model.poaType.trim().toLowerCase() : "";
  const currentPoaVerificationDocType = model.poaVerificationDocType ? model.poaVerificationDocType.trim().toLowerCase() : "";
  const currentGstNo = model.gstNo ? model.gstNo.trim().toLowerCase() : "";
  const currentAccountNo = model.accountNo ? model.accountNo.trim().toLowerCase() : "";
  const currentIfsc = model.ifsc ? model.ifsc.trim().toLowerCase() : "";
  const currentAccountType = model.accountType ? model.accountType.trim().toLowerCase() : "";
  const currentDob = model.dob ? model.dob.trim().toLowerCase() : "";
  const currentAccountName = model.accountName ? model.accountName.trim().toLowerCase() : "";

  if (firstName !== currentFirstName)
    kycStatus = statusToUpdate;
  if (middleName !== currentMiddleName)
    kycStatus = statusToUpdate;
  if (lastName !== currentLastName)
    kycStatus = statusToUpdate;
  if (mobileNo !== currentMobileNo)
    kycStatus = statusToUpdate;
  if (alternateMobile !== currentAlternateMobileNo)
    kycStatus = statusToUpdate;
  if (alternateEmailId !== currentAlternateEmailId)
    kycStatus = statusToUpdate;
  if (merchantCode !== currentMerchantCode)
    kycStatus = statusToUpdate;
  if (employment !== currentEmployment)
    kycStatus = statusToUpdate;
  if (add1 !== currentAdd1)
    kycStatus = statusToUpdate;
  if (add2 !== currentAdd2)
    kycStatus = statusToUpdate;
  if (add3 !== currentAdd3)
    kycStatus = statusToUpdate;
  if (city !== currentCity)
    kycStatus = statusToUpdate;
  if (state !== currentState)
    kycStatus = statusToUpdate;
  if (pincode !== currentPincode)
    kycStatus = statusToUpdate;
  if (panNo !== currentPanNo)
    kycStatus = statusToUpdate;
  if (poaNumber !== currentPoaNo)
    kycStatus = statusToUpdate;
  if (poaType !== currentPoaType)
    kycStatus = statusToUpdate;
  if (gstNumber !== currentGstNo)
    kycStatus = statusToUpdate;
  if (accountNumber !== currentAccountNo)
    kycStatus = statusToUpdate;
  if (ifsc !== currentIfsc)
    kycStatus = statusToUpdate;
  if (accountType !== currentAccountType)
    kycStatus = statusToUpdate;
  if (dob !== currentDob)
    kycStatus = statusToUpdate;
  if (accountName !== currentAccountName)
    kycStatus = statusToUpdate;

  if (kycStatus != "Approved") await backupUserInfo(userId);
  return kycStatus;

};

async function bulkUpdateKycStatus() {

  const nullUsers = await UserInfo.find({ "userDetails.kycStatus": null, isPartnerUser: false });

  const underReviewUsers = await UserInfo.find({ "userDetails.kycStatus": "Under Review", isPartnerUser: false });

  let activeLoanUserCount = 0;
  let inactiveLoanUserCount = 0;
  let reInitiatedCount = 0;
  let noChangeCount = 0;

  //Change for users with KYC Status = null
  for (const nullUserItem of nullUsers) {

    nullUserItem.userDetails.kycStatus = "Initiated";
    await nullUserItem.save();

  }

  for (const user of underReviewUsers) {

    const activeLoans = await LoanInfo.find({ userId: user._id, status: { $nin: ['Draft', 'Initiated'] } });

    if (activeLoans.length > 0) {

      const lastLoan = await LoanInfo.findOne({ userId: user._id }).sort({ insertedOn: -1 });

      if (lastLoan && (lastLoan.status === "Draft" || lastLoan.status === "Initiated")) {
        user.userDetails.kycStatus = "ReInitiated";
        await user.save();

        reInitiatedCount += 1;
      }
      else {
        noChangeCount += 1;
      }

      activeLoanUserCount += 1;

    }
    else {
      //users which dont have a active loan
      //update kyc status as Initiated
      user.userDetails.kycStatus = "Initiated";
      await user.save();

      inactiveLoanUserCount += 1;
    }
  }

  return {
    data: {
      nullUsersCount: nullUsers.length,
      underReviewCount: underReviewUsers.length,
      activeLoanUserCount: activeLoanUserCount,
      reInitiatedCount: reInitiatedCount,
      inactiveLoanUserCount: inactiveLoanUserCount,
      noChangeCount: noChangeCount
    },
    status: 200,
    message: "Fetched successfully"
  }
};
module.exports.bulkUpdateKycStatus = bulkUpdateKycStatus;

async function backupUserPartnerInfo(userPartnerInfoId) {
  let output = {
    success: false,
    message: "Some Error Occurred",
    status: 501,
  };
  try {
    const userPartnerInfo = await UserPartnerInfo.findById(userPartnerInfoId);

    if (userPartnerInfo) {
      const tempUserPartnerInfo = userPartnerInfo.toJSON();
      tempUserPartnerInfo.userPartnerInfoId = tempUserPartnerInfo["_id"];
      delete tempUserPartnerInfo["_id"];
      const userPartnerInfoRepository = new UserPartnerInfoHistory(tempUserPartnerInfo);
      userPartnerInfoRepository.deletedOn = Date.now();
      await userPartnerInfoRepository.save();

      output = {
        success: true,
        message: "Saved Successfully",
        status: 200,
      };
    } else {
      output.message = "No record found";
    }
  } catch (ex) {
    output.message = ex.message;
  }
  return output;
};
module.exports.backupUserPartnerInfo = backupUserPartnerInfo;

// async function validateDocuments(user, model, oldPanNumber = null, oldPoaNumber = null, oldDob = null, oldGstNumber = null, gstVerfied = false) {
//   try {

//     //Set pan and poa file stream for cases which are uploaded but no verification has been done
//     if (!model.panFileStream && (!user.panDocVerification ||
//       (user.panDocVerification.type !== "Ocr" && user.panDocVerification.type !== "digilocker"))) {
//       if (user.panDocDetails && user.panDocDetails[0] && user.panDocDetails[0].filePath) {

//         const panFilePath = await downloadImageFromS3(user.panDocDetails[0].filePath);

//         model.panFileStream = base64_encode(panFilePath);
//       }
//     }

//     if (!model.poaFileStream && user.poaDetails && user.poaDetails[0]) {
//       if ((!user.poaDetails[0].docVerification) ||
//         (user.poaDetails[0].docVerification &&
//           (user.poaDetails[0].docVerification.type !== "Ocr" || user.poaDetails[0].docVerification.type !== "digilocker")
//         )) {

//         if (user.poaDetails[0].docDetails && user.poaDetails[0].docDetails[0] && user.poaDetails[0].docDetails[0].filePath) {

//           const poaFilePath = await downloadImageFromS3(user.poaDetails[0].docDetails[0].filePath);

//           model.poaFileStream = base64_encode(poaFilePath);
//         }
//       }
//     }

//     if (!model.poaBackFileStream && user.poaDetails && user.poaDetails[0]) {
//       if ((!user.poaDetails[0].docVerification) ||
//         (user.poaDetails[0].docVerification &&
//           (user.poaDetails[0].docVerification.type !== "Ocr" || user.poaDetails[0].docVerification.type !== "digilocker")
//         )) {

//         if (user.poaDetails[0].docDetails && user.poaDetails[0].docDetails[1] && user.poaDetails[0].docDetails[1].filePath) {

//           const poaBackFilePath = await downloadImageFromS3(user.poaDetails[0].docDetails[1].filePath);

//           model.poaBackFileStream = base64_encode(poaBackFilePath);
//         }
//       }
//     }

//     //Digio Verification
//     //Digio PAN OCR Verification
//     if (model.panFileStream)
//       await ocrVerification(user._id, model.panFileStream, "", true, model.panNo, "PAN", 0);

//     else if (user.panDocVerification && user.panDocVerification.type !== "digilocker") {
//       //In-Case on number is received, check if number is different to previous number. If different call dataApi
//       if (oldPanNumber.toLowerCase() != model.panNo.toLowerCase() || oldDob != model.dob) {
//         const middleName = model.middleName ? " " + model.middleName.toUpperCase() + " " : " ";
//         const fullName = model.firstName.toUpperCase() + middleName + model.lastName.toUpperCase();
//         let dobValue = "";

//         if (model.dob) {
//           let dobParts = model.dob.split("/");
//           // Please pay attention to the month (parts[1]); JavaScript counts months from 0:
//           // January - 0, Februar.y - 1, etc.
//           let year = dobParts[2].toString();
//           let month = dobParts[1].toString();
//           let day = dobParts[0].toString();
//           dobValue = year + "-" + month + "-" + day;
//         }
//         await verifyPanData(user._id, model.panNo, fullName, dobValue);
//       }
//     }

//     //Digio POA OCR Verification
//     if (model.poaFileStream) {
//       if (model.poaType == "Voter Id" || model.poaType == "Driving Licence" || model.poaType == "Others" || model.poaType == "Aadhaar Card")
//         await ocrVerification(user._id, model.poaFileStream, model.poaBackFileStream, true, model.poaNo, "POA", 0);
//       else {
//         let docVerification = {
//           isDocMatch: false,
//           isDocVerified: user.poaDetails && user.poaDetails[defaultIndex] && user.poaDetails[defaultIndex].docVerification && user.poaDetails[defaultIndex].docVerification.isDocVerified ? user.poaDetails[defaultIndex].docVerification.isDocVerified : false,
//           isNumberVerified: false,
//           createdOn: Date.now(),
//           type: "N.A"
//         }

//         let updatePayload = {
//           [`poaDetails.${defaultIndex}.docVerification`]: docVerification
//         }

//         await UserInfo.updateOne({ _id: user._id }, { $set: { ...updatePayload } });

//       }
//     } else {
//       //If user has changed number without uploading file
//       if (oldPoaNumber.toLowerCase() != model.poaNo.toLowerCase()) {
//         //Voting Id
//         if (model.poaType == "Voter Id")
//           await verifyVoterIdData(user._id, model.poaNo);
//         else if (model.poaType == "Driving Licence")  //Driving License
//           await verifyDlData(user._id, model.poaNo, model.dob);
//         else {
//           let docVerification = {
//             isDocMatch: false,
//             isDocVerified: user.poaDetails && user.poaDetails[0] && user.poaDetails[0].docVerification && user.poaDetails[0].docVerification.isDocVerified ? user.poaDetails[0].docVerification.isDocVerified : false,
//             isNumberVerified: false,
//             createdOn: Date.now(),
//             type: "N.A"
//           }

//           let updatePayload = {
//             [`poaDetails.${defaultIndex}.docVerification`]: docVerification
//           }

//           await UserInfo.updateOne({ _id: user._id }, { $set: { ...updatePayload } });

//         }
//       }
//     }


//     //Verify GST Number if gst not previously verified or gst number changed
//     // if (model.gstNo && (!gstVerfied || (model.gstNo !== oldGstNumber)))
//     //   await verifyGstData(user._id, model.gstNo.toUpperCase(), 0);

//     if (!gstVerfied || (model.gstNo !== oldGstNumber)) {
//       if (model.gstNo)
//         await saveGstDetails("detailedgst", user._id, model.gstNo.toUpperCase(), null);
//       else if (user.panNo && user.addressDetails && user.addressDetails[0] && user.addressDetails[0].state)
//         await saveGstDetails("pantogst", user._id, user.panNo.trim().toUpperCase(), user.addressDetails[0].state.trim().toUpperCase());

//     }

//     user = null;

//     return {
//       data: "",
//       success: true,
//       message: "Updated successfully"
//     }
//   }
//   catch (ex) {
//     saveErrorLog("validateDocuments", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);

//     return {
//       data: "",
//       success: false,
//       message: ex.message
//     }
//   }
// };

async function validateDocumentsV2(userInfo, model, oldGstNumber = null, gstVerfied = false) {
  try {

    //Verify Pan Details
    if (IsNullEmpty(userInfo.panDocVerification.isDocVerified) || model.panFileStream) {
      if (!IsNullEmpty(userInfo.panDocDetails) && userInfo.panDocDetails.length > 0 && !IsNullEmpty(userInfo.panNo)) {
        const filePath = userInfo.panDocDetails[0].filePath
        const panFileStream = await downloadImageFromS3(filePath);

        if (panFileStream.data)
          await ocrVerification(userInfo._id, panFileStream.data, "", true, userInfo.panNo, "PAN", 0);

      } else if (!IsNullEmpty(userInfo.panNo) && !IsNullEmpty(userInfo.userDetails.dob)) {
        const dob = new Date(userInfo.userDetails.dob);
        let month = dob.getMonth() + 1;
        month = month < 10 ? ("0" + month) : month;
        let day = dob.getDate();
        day = day < 10 ? ("0" + day) : day;
        const dobValue = dob.getFullYear() + "-" + month + "-" + day;
        const fullName = getName(userInfo.userDetails.firstName, userInfo.userDetails.middleName, userInfo.userDetails.lastName);
        await verifyPanData(userInfo._id, userInfo.panNo, fullName, dobValue);
      }
    }

    //Verify POA Details
    if (!IsNullEmpty(userInfo.poaDetails) && userInfo.poaDetails.length > 0) {

      for (let i = 0; i < userInfo.poaDetails.length; i++) {

        const isDocVerified = userInfo.poaDetails[i] && userInfo.poaDetails[i].docVerification && userInfo.poaDetails[i].docVerification.isDocVerified ? true : false;
        const errorDetails = userInfo.poaDetails[i] && userInfo.poaDetails[i].errorDetails && userInfo.poaDetails[i].errorDetails.ocr && userInfo.poaDetails[i].errorDetails.ocr.details ? userInfo.poaDetails[i].errorDetails.ocr.details : null;

        if ((!isDocVerified && !errorDetails) || (i === 0 && (model.poaFileStream || model.poaBackFileStream))) {
          if (!IsNullEmpty(userInfo.poaDetails[i].docDetails) && userInfo.poaDetails[i].docDetails.length > 0 && (userInfo.poaDetails[i].poaType.toLowerCase() == "voter id" || userInfo.poaDetails[i].poaType.toLowerCase() == "driving licence" || userInfo.poaDetails[i].poaType.toLowerCase() == "aadhaar card" || userInfo.poaDetails[i].poaType.toLowerCase() == "others")) {

            let filePath = "";
            let front_part = "";
            let back_part = "";

            if (userInfo.poaDetails[i].docDetails.length > 1) {
              for (let j = 0; j < userInfo.poaDetails[i].docDetails.length; j++) {
                if (!IsNullEmpty(userInfo.poaDetails[i].docDetails[j].position)) {
                  if (userInfo.poaDetails[i].docDetails[j].position == "front") {
                    filePath = userInfo.poaDetails[i].docDetails[j].filePath

                    let downloadedImage = await downloadImageFromS3(filePath);
                    front_part = downloadedImage.data
                  } else if (userInfo.poaDetails[i].docDetails[j].position == "back") {
                    filePath = userInfo.poaDetails[i].docDetails[j].filePath

                    let downloadedImage = await downloadImageFromS3(filePath);
                    back_part = downloadedImage.data
                  } else if (j == 0) {
                    filePath = userInfo.poaDetails[i].docDetails[j].filePath

                    let downloadedImage = await downloadImageFromS3(filePath);
                    front_part = downloadedImage.data
                  } else if (j > 0) {
                    filePath = userInfo.poaDetails[i].docDetails[j].filePath

                    let downloadedImage = await downloadImageFromS3(filePath);
                    back_part = downloadedImage.data
                  }
                }
              }
            } else if (userInfo.poaDetails[i].docDetails.length > 0) {
              filePath = userInfo.poaDetails[i].docDetails[0].filePath
              let downloadedImage = await downloadImageFromS3(filePath);
              front_part = downloadedImage.data
            }

            if (front_part || back_part)
              await ocrVerification(userInfo._id, front_part, back_part, true, userInfo.poaDetails[i].poaNo, "POA", i);

          } else {
            if (userInfo.poaDetails[i].poaType.toLowerCase() == "voter id") {
              await verifyVoterIdData(userInfo._id, userInfo.poaDetails[i].poaNo, i);
            } else if (userInfo.poaDetails[i].poaType.toLowerCase() == "driving licence") {
              const dob = new Date(userInfo.userDetails.dob);
              let month = dob.getMonth() + 1;
              month = month < 10 ? ("0" + month) : month;
              let day = dob.getDate();
              day = day < 10 ? ("0" + day) : day;
              const dobValue = dob.getDate() + "/" + month + "/" + day;
              await verifyDlData(userInfo._id, userInfo.poaDetails[i].poaNo, dobValue, i);
            } else {
              const docVerification = {
                isDocMatch: false,
                isDocVerified: userInfo.poaDetails && userInfo.poaDetails[i] && userInfo.poaDetails[0].docVerification && userInfo.poaDetails[i].docVerification.isDocVerified ? userInfo.poaDetails[i].docVerification.isDocVerified : false,
                isNumberVerified: false,
                createdOn: Date.now(),
                type: "N.A"
              }

              const updatePayload = {
                [`poaDetails.${defaultIndex}.docVerification`]: docVerification
              }

              await UserInfo.updateOne({ _id: userInfo._id }, { $set: { ...updatePayload } });
            }
          }
        }
      }
    }

    //Verify GST Number if gst not previously verified or gst number changed
    // if (model.gstNo && (!gstVerfied || (model.gstNo !== oldGstNumber)))
    //   await verifyGstData(userInfo._id, model.gstNo.toUpperCase(), 0);

    if (!gstVerfied) {
      if (model.gstNo)
        await saveGstDetails("detailedgst", userInfo._id, model.gstNo.toUpperCase(), null);
      else if (userInfo.panNo && userInfo.addressDetails && userInfo.addressDetails[0] && userInfo.addressDetails[0].state)
        await saveGstDetails("pantogst", userInfo._id, userInfo.panNo.trim().toUpperCase(), userInfo.addressDetails[0].state.trim().toUpperCase());
    }

    user = null;

    return {
      data: "",
      success: true,
      message: "Updated successfully"
    }
  }
  catch (ex) {
    console.log(ex);
    saveErrorLog("validateDocumentsV2", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);

    return {
      data: "",
      success: false,
      message: ex.message
    }
  }
};

module.exports.updatePanDocVerification = async function () {
  try {
    const users = await UserInfo.find({ "panDocVerification.ocr": { $ne: null } }).select({ _id: 1 });

    let i = 0;

    for (const id of users) {

      const user = await UserInfo.findById(id);

      if (user) {

        console.log(`${i} Updating userid ${id}`);

        user.panDocVerification.details = {
          name: user.panDocVerification.ocr && user.panDocVerification.ocr.name ? user.panDocVerification.ocr.name : "",
          fathers_name: user.panDocVerification.ocr && user.panDocVerification.ocr.fathers_name ? user.panDocVerification.ocr.fathers_name : "",
          docNo: user.panDocVerification.ocr && user.panDocVerification.ocr.id_no ? user.panDocVerification.ocr.id_no : "",
          dob: user.panDocVerification.ocr && user.panDocVerification.ocr.dob ? user.panDocVerification.ocr.dob : "",
          document_type: "PAN",
          id_proof_type: "ID_PROOF",
          photoPath: user.panDocVerification.ocr && user.panDocVerification.ocr.photoPath ? user.panDocVerification.ocr.photoPath : ""
        }

        await user.save();

        i++;
      }
    }
    console.log("complete");
    console.log(`${i} records updated`);

    return {
      data: "",
      success: true,
      message: `${i} records updated`
    }
  }
  catch (ex) {
    console.log(ex);
    return {
      data: "",
      success: false,
      message: ex.message
    }
  }
};

module.exports.updateAadhaarDocVerification = async function () {
  try {
    const users = await UserInfo.find({ "poaDetails.poaType": "Aadhaar Card", "poaDetails.docVerification": { $ne: null } }).select({ _id: 1 });

    let count = 0;

    for (const id of users) {

      const user = await UserInfo.findById(id);

      let isUpdated = false;

      if (user) {

        for (const poa of user.poaDetails) {
          let d = 0;

          if (poa.poaType === "Aadhaar Card" && poa.docVerification && poa.docVerification.uid && poa.docVerification.uid.ocr) {
            console.log(`${count} Updating POA ${id}`);
            user.poaDetails[d].docVerification.uid.details = {
              docNo: poa.docVerification.uid.ocr.id_no ? poa.docVerification.uid.ocr.id_no : "",
              document_type: "aadhaar",
              id_proof_type: "ID_AND_ADDRESS_PROOF",
              gender: poa.docVerification.uid.ocr.gender ? poa.docVerification.uid.ocr.gender : "",
              name: poa.docVerification.uid.ocr.name ? poa.docVerification.uid.ocr.name : "",
              dob: poa.docVerification.uid.ocr.dob ? poa.docVerification.uid.ocr.dob : "",
              current_address: poa.docVerification.uid.ocr.address ? poa.docVerification.uid.ocr.address : "",
              current_address_details: poa.docVerification.uid.ocr.address_information ? poa.docVerification.uid.ocr.address_information : {},
              photoPath: poa.docVerification.uid.ocr.photoPath ? poa.docVerification.uid.ocr.photoPath : "",
            };

            isUpdated = true;
          }

          d += 1;
        }

        if (isUpdated) {
          await user.save();
          count++;
        }

      }

    }

    console.log(`${count} records updated`);

    return {
      data: "",
      success: true,
      message: `${count} records updated`
    }

  }
  catch (ex) {
    console.log(ex);
  }
};

module.exports.updateKYCInfo = async function (userId) {
  let output = {
    success: false,
    message: "Some Error Occurred",
    status: 501,
  };
  try {
    let DML = null;
    const userInfo = await UserInfo.findById(userId);
    let kycInfo = await KYCInfo.findOne({ userId: mongoose.Types.ObjectId(userId), panNo: userInfo.panNo, isActive: true });
    if (kycInfo && kycInfo != null) {
      logger.logs("update");
      //Update KYCInfo
      DML = "Updated";
      kycInfo.firstName = userInfo.userDetails.firstName;
      kycInfo.middleName = userInfo.userDetails.middleName;
      kycInfo.lastName = userInfo.userDetails.lastName;
      kycInfo.addressDetails = userInfo.addressDetails;
      kycInfo.panDocDetails = userInfo.panDocDetails;
      kycInfo.panDocVerification = userInfo.panDocVerification;
      kycInfo.poaDetails = userInfo.poaDetails;
      kycInfo.updatedOn = Date.now();
    } else {
      //await KYCInfo.updateMany({ userId: mongoose.Types.ObjectId(userId) }, { isActive: false });
      logger.logs("insert");
      //Insert KYCInfo
      DML = "Inserted";
      kycInfo = new KYCInfo({
        userId: mongoose.Types.ObjectId(userId),
        existingUserId: null,
        panNo: userInfo.panNo,
        firstName: userInfo.userDetails.firstName,
        middleName: userInfo.userDetails.middleName,
        lastName: userInfo.userDetails.lastName,
        addressDetails: userInfo.addressDetails,
        panDocDetails: userInfo.panDocDetails,
        panDocVerification: userInfo.panDocVerification,
        poaDetails: userInfo.poaDetails,
        isActive: true,
        insertedOn: Date.now(),
        updatedOn: null
      });
    }
    await kycInfo.save();

    await LoanInfo.updateMany({ userId: mongoose.Types.ObjectId(userId), kycId: null }, { kycId: mongoose.Types.ObjectId(kycInfo._id) });

    output = {
      success: true,
      message: `${DML} Successfully`,
      status: 200,
    };
  } catch (ex) {
    logger.logs("Error : ", ex);
    output.message = ex.message;
    saveErrorLog("udpateKYCInfo", apiPath, { userId }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
  }
  return output;
};