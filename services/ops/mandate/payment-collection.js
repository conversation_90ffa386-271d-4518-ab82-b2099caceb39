const config = require("config");
const { getYMDFormattedDate } = require("../common/common");
const { saveErrorLog } = require("../common/masters");
const { PaymentCollection } = require("../../../models/ops/loan/postDisbursal");
const apiPath = __dirname;
const { PAYMENT_COLLECTION_TYPE, PAYMENT_SENDING_STATE, PAYMENT_CHANNEL } = require('../common/constants').Constants;
const { PartnerMaster } = require("../../../models/ops/partner/partner");

module.exports.handleInsertENachForPaymentCollection = async function (payload) {

    try {
        let now = new Date();
        let repaymentCollectionAccount = config.get("repaymentCollectionAccount");
        let docPush = [];

        let partnerHash = await getPartnerCode(payload);

        for (let j = 0; j < payload.length; j++) {

            let {
                loan,
                fees,
                installment,
                partnerResponse,
                uniqueKey,
                presentationFine,
                collectionType } = payload[j];

            let totalFeeAmount = null;

            if (fees) {
                for (let k = 0; k < fees.length; k++) {
                    totalFeeAmount += fees[k].feeAmount
                }
            }

            let totalRepayAmount = (installment.installmentAmount || 0) + (totalFeeAmount || 0) + (installment.moratoriumInterest || 0);

            let doc = {
                isExisting: getYMDFormattedDate(installment.rePaymentDate, "-") == getYMDFormattedDate(Date.now(), "-") ? false : true,
                responseStatus: true,
                isPaid: false,
                isActive: true,
                loanId: loan._id,
                loanNo: loan.loanNo,
                loanStatus: loan.status,
                productId: loan.productId,
                productType: loan.productType,
                partnerId: loan.partnerId,
                partner: loan.partner,
                partnerCode: loan.partnerId && partnerHash[loan.partnerId.toString()] && partnerHash[loan.partnerId.toString()].partnerCode || "",
                merchantCode: loan.merchantCode,
                trxnId: loan.trxnId,
                emiNumber: installment.installmentNumber,
                referenceNo: uniqueKey,
                disbursedAmount: loan.amount,
                disbursementDate: loan.disbursalDetails.disbursedOn,
                dueDate: now,
                repayAmount: installment.installmentNumber,
                penalty: totalFeeAmount,
                moratoriumInterest: installment.moratoriumInterest,
                totalRepayAmount,
                accountName: repaymentCollectionAccount.accountName,
                accountNo: repaymentCollectionAccount.accountNo,
                ifsc: repaymentCollectionAccount.ifsc,
                source: null,
                rePaymentDate: installment.dueDate,
                scheduledDueDate: installment.actualDueDate,
                expiryDate: null,
                interestAmount: installment.interestAmount,
                principleRecovered: loan.principalAmount.collected,
                responseDescription: "",
                responseReceivedOn: now,
                paidOn: null,
                insertedOn: now,
                updatedOn: now,
                meta: {
                    umrn: partnerResponse && partnerResponse.data.mandate && partnerResponse.data.mandate.umrn || "",
                    lastEventCalled: "",
                    presentationFine,
                    collectionType,
                    transactionId: partnerResponse && partnerResponse.data && partnerResponse.data.upcoming_transaction && partnerResponse.data.upcoming_transaction.id
                },
                channel: PAYMENT_CHANNEL.ENACH
            }

            docPush.push(doc)
        }
        await PaymentCollection.insertMany(docPush);

        return { error: null, response: { message: "PaymentCollection insert successfully completed" } }
    } catch (error) {
        saveErrorLog("handleInsertENachForPaymentCollection", apiPath, { payload }, error.stack ? error.stack.toString().replace(/\r?\n|\r/g, " ") : error);
        return { error, response: null }
    }

}

module.exports.updatePaymentCollection = async function (payload) {

    try {

        let { query, update } = payload;

        if (!query || !update) {
            saveErrorLog("updatePaymentCollection", apiPath, { payload }, "No Query / Update Found");
            return { error: "No Query / Update Found", response: null }
        }

        let updateQuery = { referenceNo: query.referenceNo, channel: PAYMENT_CHANNEL.ENACH }
        let updateObj = {};

        if (update && update.paymentStatus && update.sendingState == PAYMENT_SENDING_STATE.PAID) {
            updateObj.isPaid = true;
            updateObj.paidOn = update.settlementDate;
            updateObj.isActive = false;
        }

        if (update && update.sendingState && update.sendingState == PAYMENT_SENDING_STATE.IN_ACTIVE)
            updateObj.isActive = false;

        if (update && update.lastEventCalled)
            updateObj["meta.lastEventCalled"] = update.lastEventCalled;

        if (update && update.paymentStatus)
            updateObj["meta.paymentStatus"] = update.paymentStatus;

        let updateManyRes = await PaymentCollection.updateMany(updateQuery, { $set: updateObj, updatedOn: new Date() });
        return { error: null, response: { message: "Successfully updated" } }

    } catch (error) {
        saveErrorLog("updatePaymentCollection", apiPath, { payload }, error.stack ? error.stack.toString().replace(/\r?\n|\r/g, " ") : error);
        return { error, response: null }
    }
}

async function getPartnerCode(payload) {

    try {

        let partnerIds = [];

        for (let i = 0; i < payload.length; i++) {
            let { loan } = payload[i];
            if (loan && loan.partnerId)
                partnerIds.push(loan.partnerId);
        }
        let partnerHash = {};

        let partnersRes = await PartnerMaster.find({ _id: { $in: partnerIds } }).lean();

        if (partnersRes && partnersRes.length) {
            for (let i = 0; i < partnersRes.length; i++) {
                partnerHash[partnersRes[i]._id.toString()] = partnersRes[i]
            }
        }

        return partnerHash
    } catch (error) {
        saveErrorLog("getPartnerCode", apiPath, { payload }, error.stack ? error.stack.toString().replace(/\r?\n|\r/g, " ") : error);
        return null
    }
}