const mongoose = require("mongoose");
const _ = require("lodash");
const moment = require("moment");
const config = require("config");
const debug = require("debug")("app:eNach");
const fs = require("fs");

const { DigioMandateGeneration, DigioMandateLog, DigioBankList, DigioNachResponseLog,
    DigioNachWebHookResponse, DigioScheduleLog, DigioDebitResponseLog, DigioDebitWebHookResponse,
    DigioCollectionSchedule, DigioSchedulerLog, DigioMandateCancellationLog,
    DigioMandateCancellationWebHook, EmiRequest, MandateErrorCodes } = require("../../../models/ops/digio/digioApi");
const { UserInfo, UserPartnerInfo, EngagementSession } = require("../../../models/ops/registration/users");
const { LoanInfo } = require("../../../models/ops/loan/loan");

const { callDigioApi, getName, getYMDFormattedDate, getMailModel, getUniqueKey, getDMYFormattedDate, getSmsModel, sleep, pushToNotifications, getProductType } = require("../common/common");
const { sendMail } = require("../common/mailer");

const { sendNotifications } = require("../loan/postDisbursal");
const { PartnerMaster } = require("../../../models/ops/partner/partner");
const { validateAndSaveBankDetails, checkMandateCurrentBankDetails } = require("../verification/digioApi");
const { isDate, dateAdd } = require("dateadd");
const { sendCustomSms } = require("../registration/verificationService");
const { generateCustomUrl } = require("../../../services/ops/common/links");
const { saveErrorLog } = require("../common/masters");
const { callBorrowingPartnerAPI } = require("../../ops/partner/borrowingPartner");
const installmentService = require("../../../services/ops/loan/installments");
const workflowService = require("../../../services/ops/loan/workflow");
const { updatePaymentCollection, handleInsertENachForPaymentCollection } = require('./payment-collection')
const { PAYMENT_COLLECTION_TYPE, PAYMENT_SENDING_STATE, PAYMENT_CHANNEL } = require('../common/constants').Constants
const { getExtension, isPnachAvailable } = require("../common/common");
const { getSignedUrlToDownload } = require("../../../models/ops/aws/s3");
const DateDiff = require("date-diff");
const apiPath = __dirname;
const onlyCharacters = /^[a-zA-Z ]+$/;

const AWS = require("../../../models/ops/aws/s3");
const { NotificationTemplates } = require("../../../models/ops/common/master");
const { FILE_UPLOAD_MODULES, FILE_UPLOAD_TYPES, ROLES } = require('../common/constants').Constants;
const { validateFile } = require('../common/common');
const Logger = require("../../../middleware/logger");
const logger = new Logger();


module.exports.sendEnachRequest = async function (loanId, userId, authMode) {
    try {
        const user = await UserInfo.findById(userId);
        const loan = await LoanInfo.findById(loanId);

        const userPartnerInfo = await UserPartnerInfo.findOne({
            userId: mongoose.Types.ObjectId(userId),
            partnerId: mongoose.Types.ObjectId(loan.partnerId),
            merchantCode: loan.merchantCode
        });

        const partnerMaster = await PartnerMaster.findById(loan.partnerId);

        const mandateGeneration = await DigioMandateGeneration.findOne({ loanIds: loanId, userId: loan.userId, partnerId: loan.partnerId, mode: authMode, status: { $nin: ["dest_register_failed", "npci_nack_received"] } }).sort({ createdOn: -1 });

        const isMandateDetailsMatch = await checkMandateCurrentBankDetails(mandateGeneration, loanId, authMode);

        if (isMandateDetailsMatch.success && loan.amount === mandateGeneration.mandateDetails.mandate_data.maximum_amount) {

            let longUrl = "";

            // if (loan.loanContractDetails && loan.loanContractDetails.eNachUrl && loan.loanContractDetails.authMode === authMode) {
            //     url = loan.loanContractDetails.eNachUrl;
            //     longUrl = loan.loanContractDetails.redirectionLongUrl ? loan.loanContractDetails.redirectionLongUrl : "";
            // }
            // else {
            let url = config.get("digio.eNach.nachLinkUrl");

            let redirectUrl = "?redirect_url=" + `${config.get("digio.eNach.redirectUrl")}?mode=${authMode}` + `&logo=${config.get("digio.eNach.logoLink")}`;

            if (mandateGeneration.access_token && mandateGeneration.access_token.id)
                redirectUrl += `&token_id=${mandateGeneration.access_token.id}`;

            const mandateId = mandateGeneration.mandateDetails ? mandateGeneration.mandate_id : "";

            const clientCode = mandateGeneration.mandateDetails && mandateGeneration.mandateDetails.mandate_data && mandateGeneration.mandateDetails.mandate_data.customer_ref_number ? mandateGeneration.mandateDetails.mandate_data.customer_ref_number : "";

            const mobileNo = mandateGeneration.mandateDetails && mandateGeneration.mandateDetails.customer_identifier ? mandateGeneration.mandateDetails.customer_identifier : "";

            url += mandateId + "/" + clientCode + "/" + mobileNo + redirectUrl;

            const eNachCode = config.get("shortUrl.eNachCode");

            const shortUrlResponse = await generateCustomUrl(url, eNachCode, loan.userId, loan._id, loan.loanNo, loan.merchantCode, partnerMaster.displayName, null);

            longUrl = url;

            if (shortUrlResponse.success)
                url = shortUrlResponse.data;
            // }

            return {
                data: "",
                success: true,
                message: "Mandate already generated for this loan",
                status: 200,
                eNachUrl: url,
                longUrl: longUrl,
                isMandateSigned: (mandateGeneration.status === "success" || mandateGeneration.status === "register_success") ? true : false,
                authMode: mandateGeneration.mode
            }
        }

        if (user && userPartnerInfo) {

            if (userPartnerInfo.bankDetails && userPartnerInfo.bankDetails[0]) {

                const digioBank = await DigioBankList.findOne({
                    systemBankName: {
                        $regex: new RegExp("^" + userPartnerInfo.bankDetails[0].bankName + "$", "i")
                    }
                });

                // if (!digioBank || (digioBank && !digioBank.netBanking && !digioBank.debitCard && !digioBank.esign))
                //     return {
                //         data: "",
                //         success: false,
                //         message: "e-Nach option not available for selected bank. Please select different bank.",
                //         status: 200
                //     };

                let beneficiary_name_with_bank = "";

                if (userPartnerInfo.bankDetails[0].docVerification && userPartnerInfo.bankDetails[0].docVerification.isBankVerified) {

                    beneficiary_name_with_bank = userPartnerInfo.bankDetails[0].accountName ? userPartnerInfo.bankDetails[0].accountName : "";

                    if (!beneficiary_name_with_bank || !onlyCharacters.test(beneficiary_name_with_bank))
                        return {
                            data: "",
                            success: false,
                            message: "Invalid Bank Account Name. Bank Account Name can only contain characters",
                            status: 200
                        };

                    //call e-mandate
                    const createMandateResponse = await createMandate(user, loan._id, digioBank, beneficiary_name_with_bank, true, mandateGeneration, authMode);

                    return createMandateResponse;

                } else {
                    //call bank verification
                    const bankresponse = await validateAndSaveBankDetails(userId, userPartnerInfo.partnerId, userPartnerInfo.bankDetails[0].accountNo.toUpperCase(), userPartnerInfo.bankDetails[0].ifsc.toUpperCase(), userPartnerInfo.bankDetails[0].accountType, userPartnerInfo.bankDetails[0].accountName, null, null, loan.merchantCode, loan._id, true);
                    //call e-mandate

                    if (bankresponse.success) {

                        userPartnerInfo = await UserPartnerInfo.findOne({
                            userId: mongoose.Types.ObjectId(userId),
                            partnerId: mongoose.Types.ObjectId(loan.partnerId),
                            merchantCode: loan.merchantCode
                        });

                        if (userPartnerInfo.bankDetails[0].docVerification && userPartnerInfo.bankDetails[0].docVerification.isBankVerified)
                            beneficiary_name_with_bank = userPartnerInfo.bankDetails[0].accountName ? userPartnerInfo.bankDetails[0].accountName : "";

                        if (!beneficiary_name_with_bank || !onlyCharacters.test(beneficiary_name_with_bank))
                            return {
                                data: "",
                                success: false,
                                message: "Invalid Bank Account Name. Bank Account Name can only contain characters",
                                status: 200
                            };

                        const createMandateResponse = await createMandate(user, loan._id, digioBank, beneficiary_name_with_bank, true, mandateGeneration);

                        return createMandateResponse;

                    } else
                        return {
                            data: "",
                            success: false,
                            message: bankresponse.message,
                            status: 200,
                            eNachUrl: "",
                            longUrl: ""
                        }
                }
            }
            else
                return {
                    data: "",
                    success: false,
                    message: "bank details not found",
                    status: 200,
                    eNachUrl: "",
                    longUrl: ""
                }
        } else if (!user)
            return {
                data: "",
                success: false,
                message: "No user found",
                status: 200,
                eNachUrl: "",
                longUrl: ""
            }
        else if (!userPartnerInfo)
            return {
                data: "",
                success: false,
                message: "No User Partner Info found",
                status: 200,
                eNachUrl: "",
                longUrl: ""
            }
    }
    catch (ex) {
        saveErrorLog("sendEnachRequest", apiPath, { loanId, userId }, { error: ex.message, stackTrace: ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex });
        return { data: ex.message, success: false, message: ex.message, status: 200 };
    }

}

async function createMandate(user, loanId, digioBank, beneficiary_name_with_bank, isRedirect = true, existingMandate = null, authMode = "api") {
    try {
        const loan = await LoanInfo.findById(loanId);
        const userPartnerInfo = await UserPartnerInfo.findOne({
            userId: mongoose.Types.ObjectId(loan.userId),
            partnerId: mongoose.Types.ObjectId(loan.partnerId),
            merchantCode: loan.merchantCode
        });

        const partnerMaster = await PartnerMaster.findById(loan.partnerId);

        let mobileNo = user.userDetails.mobileNo[0] ? user.userDetails.mobileNo[0].mobileNumber : ""; // mobile number change 
        // let customer_ref_number = loan.merchantCode ? loan.merchantCode : "";
        let customer_ref_number = user.panNo ? user.panNo : "";
        let scheme_ref_number = loan.loanNo;
        let customer_account_number = userPartnerInfo.bankDetails[0] && userPartnerInfo.bankDetails[0].accountNo ? userPartnerInfo.bankDetails[0].accountNo : "";
        let customer_account_type = userPartnerInfo.bankDetails[0] && userPartnerInfo.bankDetails[0].accountType ? userPartnerInfo.bankDetails[0].accountType : "";
        let destination_bank_id = userPartnerInfo.bankDetails[0] && userPartnerInfo.bankDetails[0].ifsc ? userPartnerInfo.bankDetails[0].ifsc : "";
        let destination_bank_name = digioBank && digioBank.digioBankName ? digioBank.digioBankName : digioBank.systemBankName;
        let customer_name = beneficiary_name_with_bank ? beneficiary_name_with_bank : getName(user.userDetails.firstName, user.userDetails.middleName, user.userDetails.lastName);
        let first_collection_date = moment(new Date()).format("yyyy-MM-DD");

        let frequency = config.get("digio.eNach.frequency");

        let model = {
            "customer_identifier": mobileNo,
            "auth_mode": authMode || config.get("digio.eNach.auth_mode"),
            "mandate_type": config.get("digio.eNach.mandateType"),
            "corporate_config_id": config.get("digio.eNach.corporate_config_id"),
            "generate_access_token": config.get("digio.eNach.generate_access_token"),
            "expire_in_days": config.get("digio.eNach.expire_in_days"),
            "mandate_data": {
                "maximum_amount": loan.amount ? loan.amount : 0,
                "instrument_type": config.get("digio.eNach.instrument_type"),
                "first_collection_date": first_collection_date,
                // "final_collection_date": last_collection_date,
                "is_recurring": config.get("digio.eNach.isRecurring"),
                "frequency": frequency,
                "management_category": config.get("digio.eNach.management_category"),
                "customer_name": customer_name,
                "customer_account_number": customer_account_number,
                "destination_bank_id": destination_bank_id,
                "destination_bank_name": destination_bank_name,
                "customer_account_type": customer_account_type,
                "customer_ref_number": customer_ref_number,
                "scheme_ref_number": scheme_ref_number,
                "customer_email": user.userDetails.emailId[0] ? user.userDetails.emailId[0] : "",
                "customer_mobile": user.userDetails.mobileNo[0] ? user.userDetails.mobileNo[0].mobileNumber : ""// mobile number change 
            }
        }

        let { data, success, message } = await mandateCreation(model, user, existingMandate && existingMandate.loanIds ? existingMandate.loanIds : [loan._id]);

        if (success) {

            let startDateParts = first_collection_date.split("-");
            // let endDateParts = last_collection_date.split("-");

            let mandateCreation = new DigioMandateGeneration({
                id: data.id ? data.id : "",
                mandate_id: data.mandate_id ? data.mandate_id : "",
                state: data.state ? data.state : "",
                type: data.type ? data.type : "",
                status: "created",
                statusModifiedAt: Date.now(),
                rejectCode: "",
                rejectReason: "",
                bank_details: {
                    shared_with_bank: data.bank_details && data.bank_details.shared_with_bank ? data.bank_details.shared_with_bank : "",
                    bank_name: data.bank_details && data.bank_details.bank_name ? data.bank_details.bank_name : "",
                    state: data.bank_details && data.bank_details.state ? data.bank_details.state : "",
                },
                created_at: data.created_at ? data.created_at : "",
                mode: data.mode ? data.mode : "",
                service_provider_details: {
                    service_provider_name: data.service_provider_details && data.service_provider_details.service_provider_name ? data.service_provider_details.service_provider_name : "",
                    service_provider_utility_code: data.service_provider_details && data.service_provider_details.service_provider_utility_code ? data.service_provider_details.service_provider_utility_code : "",
                    support_email: data.service_provider_details && data.service_provider_details.support_email ? data.service_provider_details.support_email : "",
                    support_mobile: data.service_provider_details && data.service_provider_details.support_mobile ? data.service_provider_details.support_mobile : "",
                },
                userId: user._id,
                partnerId: loan.partnerId,
                isActive: false,
                startDate: new Date(startDateParts[0], startDateParts[1] - 1, startDateParts[2]),
                inActiveFrom: null,
                inActiveBy: null,
                // endDate: new Date(endDateParts[0], endDateParts[1] - 1, endDateParts[2]),
                endDate: null,
                createdOn: Date.now(),
                mandateDetails: model,
                ackResponse: [],
                access_token: {
                    created_at: data.access_token && data.access_token.created_at ? new Date(data.access_token.created_at) : null,
                    id: data.access_token && data.access_token.id ? data.access_token.id : "",
                    entity_id: data.access_token && data.access_token.entity_id ? data.access_token.entity_id : "",
                    valid_till: data.access_token && data.access_token.valid_till ? new Date(data.access_token.valid_till) : null,
                },
                loanIds: existingMandate && existingMandate.loanIds ? existingMandate.loanIds : [loan._id],
                creationType: isRedirect ? "new" : "added"
            });

            await mandateCreation.save();

            // if (existingMandate) {
            //     existingMandate.isActive = false;
            //     existingMandate.inActiveFrom = Date.now();
            //     existingMandate.cancellationErrorMessage = "New mandate created - System";
            //     await existingMandate.save();
            // }

            let url = config.get("digio.eNach.nachLinkUrl");

            let redirectUrl = "";
            if (isRedirect)
                redirectUrl = "?redirect_url=" + config.get("digio.eNach.redirectUrl") + `&logo=${config.get("digio.eNach.logoLink")}`;
            else
                redirectUrl = `?logo=${config.get("digio.eNach.logoLink")}`;

            if (data.access_token && data.access_token.id)
                redirectUrl += `&token_id=${data.access_token.id}`;

            let mandateId = data.mandate_id ? data.mandate_id : "";

            let clientCode = loan.merchantCode ? loan.merchantCode : "";

            url += mandateId + "/" + clientCode + "/" + mobileNo + redirectUrl;

            const eNachCode = config.get("shortUrl.eNachCode");

            const shortUrlResponse = await generateCustomUrl(url, eNachCode, loan.userId, loan._id, loan.loanNo, loan.merchantCode, partnerMaster.displayName, null);

            const longUrl = url;

            if (shortUrlResponse.success)
                url = shortUrlResponse.data;

            return { data, success, message, eNachUrl: url, longUrl: longUrl, isMandateSigned: false, authMode };
        }
        else {
            return { data, success, message, eNachUrl: "", longUrl: "", isMandateSigned: false, authMode };
        }
    }
    catch (ex) {
        saveErrorLog("createMandate", apiPath, { userId: user._id, loanId, digioBank, beneficiary_name_with_bank }, { error: ex.message, stackTrace: ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex });
        return { data: ex.response.data.code, success: false, message: ex.response.data.message, eNachUrl: "", longUrl: "", isMandateSigned: false, authMode };
    }
}

async function mandateCreation(model, user, loanIds) {
    let requestSentOn = Date.now();

    const url = config.get("digio.eNach.createFormUrl");

    try {
        const response = await callDigioApi("POST", url, model);

        let digioMandateLogModel = {
            request: model,
            response: response,
            url: url,
            requestSentOn: requestSentOn,
            responseReceivedOn: Date.now(),
            userId: user._id ? user._id : null,
            loanIds: loanIds ? loanIds : null
        }

        await log(digioMandateLogModel);

        return { data: response, success: true, message: "" };
    }
    catch (ex) {
        saveErrorLog("mandateCreation", apiPath, { model, userId: user._id, loanIds: loanIds }, { error: ex.message, stackTrace: ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex });

        let digioMandateLogModel = {
            request: model,
            response: ex.response.data,
            url: url,
            requestSentOn: requestSentOn,
            responseReceivedOn: Date.now(),
            userId: user._id ? user._id : null,
            loanIds: loanIds ? loanIds : null
        }

        await log(digioMandateLogModel);

        return { data: ex.response.data.code, success: false, message: ex.response.data.message };
    }
}

async function log(model) {

    let log = new DigioMandateLog({
        request: JSON.stringify(model.request),
        response: JSON.stringify(model.response),
        url: model.url,
        requestSentOn: model.requestSentOn,
        responseReceivedOn: model.responseReceivedOn,
        userId: model.userId ? model.userId : null,
        loanIds: model.loanIds ? model.loanIds : null,
        mandateId: model.response && model.response.id ? model.response.id : ""
    });

    let response = await log.save();

    return response;

}

module.exports.updateEnachResponse = async function (model) {
    let entitity = model.entities && model.entities[0];

    let mandate_id = model.payload && model.payload[entitity] && model.payload[entitity].id ? model.payload[entitity].id : "";
    let userId = null;
    let loanIds = null;

    try {
        if (mandate_id) {
            let mandateCreation = await DigioMandateGeneration.findOne({ mandate_id: mandate_id });

            if (mandateCreation) {
                userId = mandateCreation.userId ? mandateCreation.userId : null;
                loanIds = mandateCreation.loanIds ? mandateCreation.loanIds : null;
            }

            await eNachResponse(userId, loanIds, mandate_id, model);

            if (mandateCreation) {

                let nachResponse = new DigioNachWebHookResponse({
                    entities: model.entities ? model.entities : [],
                    api_mandate: {
                        id: model.payload && model.payload.api_mandate && model.payload.api_mandate.id ? model.payload.api_mandate.id : "",
                        message_id: model.payload && model.payload.api_mandate && model.payload.api_mandate.message_id ? model.payload.api_mandate.message_id : "",
                        npci_txn_id: model.payload && model.payload.api_mandate && model.payload.api_mandate.npci_txn_id ? model.payload.api_mandate.npci_txn_id : "",
                        current_status: model.payload && model.payload.api_mandate && model.payload.api_mandate.current_status ? model.payload.api_mandate.current_status : "",
                        txn_timestamp: model.payload && model.payload.api_mandate && model.payload.api_mandate.txn_timestamp ? new Date(model.payload.api_mandate.txn_timestamp) : null,
                        txn_reject_code: model.payload && model.payload.api_mandate && model.payload.api_mandate.txn_reject_code ? model.payload.api_mandate.txn_reject_code : "",
                        umrn: model.payload && model.payload.api_mandate && model.payload.api_mandate.umrn ? model.payload.api_mandate.umrn : "",
                        txn_reject_reason: model.payload && model.payload.api_mandate && model.payload.api_mandate.txn_reject_reason ? model.payload.api_mandate.txn_reject_reason : "",
                        auth_sub_mode: model.payload && model.payload.api_mandate && model.payload.api_mandate.auth_sub_mode ? model.payload.api_mandate.auth_sub_mode : "",
                        others: {
                            scheme_ref_number: model.payload && model.payload.api_mandate && model.payload.api_mandate.others && model.payload.api_mandate.others.scheme_ref_number ? model.payload.api_mandate.others.scheme_ref_number : "",
                            customer_ref_number: model.payload && model.payload.api_mandate && model.payload.api_mandate.others && model.payload.api_mandate.others.customer_ref_number ? model.payload.api_mandate.others.customer_ref_number : "",
                        }
                    },
                    id: model.id ? model.id : "",
                    created_at: model.created_at ? new Date(model.created_at) : null,
                    event: model.event ? model.event : "",
                    responseReceivedOn: Date.now(),
                    userId: userId,
                    loanIds: loanIds
                });
                //update
                await nachResponse.save();

                const loanId = mandateCreation.loanIds && mandateCreation.loanIds[0] ? mandateCreation.loanIds[0] : null;
                //mandate signed with current bank details
                const checkMandate = await checkMandateCurrentBankDetails(mandateCreation, loanId);

                mandateCreation.lastEventCalled = model.event ? model.event : "";
                mandateCreation.status = model.payload && model.payload.api_mandate && model.payload.api_mandate.current_status ? model.payload.api_mandate.current_status : "";
                mandateCreation.rejectCode = model.payload && model.payload.api_mandate && model.payload.api_mandate.txn_reject_code ? model.payload.api_mandate.txn_reject_code : "";
                mandateCreation.rejectReason = model.payload && model.payload.api_mandate && model.payload.api_mandate.txn_reject_reason ? model.payload.api_mandate.txn_reject_reason : "";
                mandateCreation.statusModifiedAt = Date.now();
                mandateCreation.npci_txn_id = model.payload && model.payload.api_mandate && model.payload.api_mandate.npci_txn_id ? model.payload.api_mandate.npci_txn_id : "";
                mandateCreation.umrn = model.payload && model.payload.api_mandate && model.payload.api_mandate.umrn ? model.payload.api_mandate.umrn : "";
                mandateCreation.auth_sub_mode = model.payload && model.payload.api_mandate && model.payload.api_mandate.auth_sub_mode ? model.payload.api_mandate.auth_sub_mode : mandateCreation.auth_sub_mode;
                mandateCreation.mandateRegisteredOn = model.payload && model.payload.api_mandate && model.payload.api_mandate.current_status && model.payload.api_mandate.current_status === "register_success" ? Date.now() : null;

                await mandateCreation.save();

                //Set oldMandate is inActive
                if (checkMandate.success && mandateCreation.status === "register_success") {
                    mandateCreation = await DigioMandateGeneration.findOne({ mandate_id: mandate_id });
                    mandateCreation.isActive = true;

                    const oldMandates = await DigioMandateGeneration.find({ userId: mandateCreation.userId, partnerId: mandateCreation.partnerId, isActive: true, mandate_id: { $ne: mandate_id } });

                    for (const oldMandate of oldMandates) {
                        //move old mandate loans to current mandate
                        for (const id of oldMandate.loanIds) {
                            if (!mandateCreation.loanIds.includes(id)) {
                                mandateCreation.loanIds.push(id);
                                mandateCreation.updatedOn = Date.now();
                                updateMandateLogs(mandateCreation, id);
                            }

                            const loan = await LoanInfo.findById(id);

                            if (loan) {
                                loan.loanContractDetails.authMode = mandateCreation.mode;
                                await loan.save();
                            }
                        }

                        //set old mandate as inactive
                        oldMandate.isActive = false;
                        oldMandate.inActiveFrom = Date.now();
                        oldMandate.cancellationErrorMessage = "Cancelled as new mandate created due to change in bank details.";
                        oldMandate.inActiveBy = null;

                        await oldMandate.save();
                    }

                    await mandateCreation.save();

                    //check if any other loans dont have mandate and assign this mandate to them
                    mandateCreation = await DigioMandateGeneration.findOne({ mandate_id: mandate_id });

                    const loanList = await LoanInfo.find({
                        userId: mandateCreation.userId,
                        partnerId: mandateCreation.partnerId,
                        primaryCollectionType: "Wallet",
                        secondaryCollectionType: "",
                        status: "Loan In Progress"
                    });

                    for (const item of loanList) {

                        const mandate = await DigioMandateGeneration.findOne({ loanIds: item._id, isActive: true });

                        if (!mandate) {
                            if (mandateCreation && !mandateCreation.loanIds.includes(item._id)) {
                                mandateCreation.loanIds.push(item._id);
                                mandateCreation.updatedOn = Date.now();
                                await mandateCreation.save();
                            }

                            item.secondaryCollectionType = "eNach";
                            item.loanContractDetails.eNachUrl = item.loanContractDetails.contractUrl;
                            item.loanContractDetails.authMode = mandateCreation.mode;
                            await item.save();

                            updateMandateLogs(mandateCreation, item._id);
                        }
                    }
                }

                const isExists = await MandateErrorCodes.findOne({ code: mandateCreation.rejectCode, isActive: true }).countDocuments();
                //console.log("isExists", isExists);
                //check if error code exists in exclusion list
                if (isExists === 0) {
                    const partnerMaster = await PartnerMaster.findById(mandateCreation.partnerId).select({ partnerCode: 1, displayName: 1 });
                    const loanInfo = await LoanInfo.findById(loanId).select({ productType: 1 });
                    const productType = getProductType(loanInfo.productType);
                    const conf = config.get(partnerMaster.partnerCode.toLowerCase());
                    //console.log("conf", conf);
                    let isPartnerNotified = true;

                    if (conf && conf.eNachFailureApis && conf.eNachFailureApis != null) {
                        let apis = conf.eNachFailureApis;
                        if (apis) {
                            apis = apis.filter((x) => x.productType == productType);
                            //console.log("apis", apis);
                            if (apis.length > 0) {
                                for (let i = 0; i < apis.length; i++) {
                                    const borrowingParterRequestModel = apis[i].model != null ? JSON.parse(JSON.stringify(apis[i].model)) : {};
                                    borrowingParterRequestModel.source = "application";
                                    borrowingParterRequestModel.module = "eNachFailureApis";
                                    borrowingParterRequestModel.loanId = loanInfo._id;
                                    borrowingParterRequestModel.partnerId = partnerMaster._id;
                                    borrowingParterRequestModel.partnerCode = partnerMaster.partnerCode;
                                    borrowingParterRequestModel.partnerDisplayName = partnerMaster.displayName;
                                    borrowingParterRequestModel.apiType = apis[i].apiType;
                                    borrowingParterRequestModel.isRetryOffline = apis[i].isRetryOffline;
                                    borrowingParterRequestModel.url = apis[i].url;
                                    borrowingParterRequestModel.rejectCode = mandateCreation.rejectCode;
                                    borrowingParterRequestModel.rejectReason = mandateCreation.rejectReason;

                                    if (apis[i].isWaitForResponse) {
                                        const borrowerApiResponse = await callBorrowingPartnerAPI(borrowingParterRequestModel);
                                        if (!borrowerApiResponse.success) {
                                            i = apis.length;
                                            output.message = borrowerApiResponse.message;
                                            return output;
                                        }
                                    } else {
                                        logger.logs("borrowingParterRequestModel", borrowingParterRequestModel);
                                        callBorrowingPartnerAPI(borrowingParterRequestModel);
                                    }
                                }
                                isPartnerNotified = true;
                            }
                        } else {
                            isPartnerNotified = true;
                        }
                    }
                }


                return {
                    data: "",
                    success: true,
                    message: "Saved successfully"
                }
            }
            else {
                await eNachResponse(userId, loanIds, mandate_id, model);

                return {
                    data: "",
                    success: false,
                    message: "Mandate Id not found"
                }
            }
        }
        else {

            return {
                data: "",
                success: false,
                message: "Mandate Id not received"
            }
        }
    }
    catch (ex) {
        saveErrorLog("updateEnachResponse", apiPath, { model }, { error: ex.message, stackTrace: ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex });
        await eNachResponse(userId, loanIds, mandate_id, model);
        return { data: ex.message, success: false, message: ex.message };
    }
}


module.exports.updateAadhaarEnachResponse = async function (model) {
    let entitity = model.entities && model.entities[0];

    let mandate_id = model.payload && model.payload[entitity] && model.payload[entitity].id ? model.payload[entitity].id : "";

    //console.log("updateEnachResponse called", mandate_id, new Date());

    let userId = null;
    let loanIds = null;

    try {
        if (mandate_id) {
            let mandateCreation = await DigioMandateGeneration.findOne({ mandate_id: mandate_id });

            if (model.event == "mndt.cancelinit" || model.event == "mndt.bankcancel") {
                let umrn = model.payload && model.payload[entitity] && model.payload[entitity].umrn ? model.payload[entitity].umrn : "";
                mandateCreation = await DigioMandateGeneration.findOne({ umrn });
                mandate_id = mandateCreation.id
            }

            if (mandateCreation) {
                userId = mandateCreation.userId ? mandateCreation.userId : null;
                loanIds = mandateCreation.loanIds ? mandateCreation.loanIds : null;
            }

            await eNachResponse(userId, loanIds, mandate_id, model);

            if (mandateCreation) {

                syncAadhaarPayloadToApiPayload(model.payload, model, mandateCreation.mode);

                let nachResponse = new DigioNachWebHookResponse({
                    entities: model.entities ? model.entities : [],
                    api_mandate: {
                        id: model.payload && model.payload.mandate && model.payload.mandate.id ? model.payload.mandate.id : "",
                        file_name: model.payload && model.payload.mandate && model.payload.mandate.file_name ? model.payload.mandate.file_name : "",
                        partner_entity: model.payload && model.payload.mandate && model.payload.mandate.partner_entity ? model.payload.mandate.partner_entity : "",
                        pki_signature_details: model.payload && model.payload.mandate && model.payload.mandate.pki_signature_details ? model.payload.mandate.pki_signature_details : "",
                        message_id: model.payload && model.payload.mandate && model.payload.mandate.message_id ? model.payload.mandate.message_id : "",
                        npci_txn_id: model.payload && model.payload.mandate && model.payload.mandate.npci_txn_id ? model.payload.mandate.npci_txn_id : "",
                        current_status: model.payload && model.payload.mandate && model.payload.mandate.current_status ? model.payload.mandate.current_status : "",
                        txn_timestamp: model.payload && model.payload.mandate && model.payload.mandate.txn_timestamp ? new Date(model.payload.mandate.txn_timestamp) : null,
                        txn_reject_code: model.payload && model.payload.mandate && model.payload.mandate.txn_reject_code ? model.payload.mandate.txn_reject_code : "",
                        umrn: model.payload && model.payload.mandate && model.payload.mandate.umrn ? model.payload.mandate.umrn : "",
                        txn_reject_reason: model.payload && model.payload.mandate && model.payload.mandate.txn_reject_reason ? model.payload.mandate.txn_reject_reason : "",
                        auth_sub_mode: model.payload && model.payload.mandate && model.payload.mandate.auth_sub_mode ? model.payload.mandate.auth_sub_mode : "",
                        others: {
                            scheme_ref_number: model.payload && model.payload.mandate && model.payload.mandate.scheme_ref_number ? model.payload.mandate.scheme_ref_number : "",
                            customer_ref_number: model.payload && model.payload.mandate && model.payload.mandate.customer_ref_number ? model.payload.mandate.customer_ref_number : "",
                        }
                    },
                    id: model.id ? model.id : "",
                    created_at: model.created_at ? new Date(model.created_at) : null,
                    event: model.event ? model.event : "",
                    responseReceivedOn: Date.now(),
                    userId: userId,
                    loanIds: loanIds
                });
                //update
                await nachResponse.save();

                const loanId = mandateCreation.loanIds && mandateCreation.loanIds[0] ? mandateCreation.loanIds[0] : null;

                const loanInfo = await LoanInfo.findById(loanId);
                //mandate signed with current bank details
                const checkMandate = await checkMandateCurrentBankDetails(mandateCreation, loanId);

                mandateCreation.lastEventCalled = model.event ? model.event : "";
                mandateCreation.status = model.payload && model.payload.mandate && model.payload.mandate.current_status ? model.payload.mandate.current_status : "";
                mandateCreation.rejectCode = model.payload && model.payload.mandate && model.payload.mandate.txn_reject_code ? model.payload.mandate.txn_reject_code : "";
                mandateCreation.rejectReason = model.payload && model.payload.mandate && model.payload.mandate.txn_reject_reason ? model.payload.mandate.txn_reject_reason : "";
                mandateCreation.statusModifiedAt = Date.now();
                mandateCreation.npci_txn_id = model.payload && model.payload.mandate && model.payload.mandate.npci_txn_id ? model.payload.mandate.npci_txn_id : "";
                mandateCreation.umrn = model.payload && model.payload.mandate && model.payload.mandate.umrn ? model.payload.mandate.umrn : "";
                mandateCreation.auth_sub_mode = model.payload && model.payload.mandate && model.payload.mandate.auth_sub_mode ? model.payload.mandate.auth_sub_mode : mandateCreation.auth_sub_mode;
                mandateCreation.mandateRegisteredOn = model.payload && model.payload.mandate && model.payload.mandate.current_status && model.payload.mandate.current_status === "register_success" ? Date.now() : null;

                //If failure, mark mandate as inActive
                if (model.payload && model.payload.mandate && (model.payload.mandate.current_status === "dest_register_failed"
                    || model.payload.mandate.current_status === "npci_nack_received"))
                    mandateCreation.isActive = false;

                await mandateCreation.save();

                //send update enach notification
                if (model.payload && model.payload.mandate && (model.payload.mandate.current_status === "dest_register_failed"
                    || model.payload.mandate.current_status === "npci_nack_received")) {

                    const loanInfo = await LoanInfo.findById(loanId);

                    await LoanInfo.updateOne(
                        {
                            _id: loanId
                        },
                        {
                            $set: {
                                isForceMandate: true
                            }
                        }
                    );

                    const userInfo = await UserInfo.findById(loanInfo.userId);

                    const eNachUrl = loanInfo.loanContractDetails && loanInfo.loanContractDetails.eNachUrl ? loanInfo.loanContractDetails.eNachUrl : "";

                    if (eNachUrl) {
                        let mailModel = getMailModel();

                        mailModel.link = `<a href=${eNachUrl}?c=e>e-Nach Link</a>`;
                        await sendMail(userInfo._id, loanId, "Emails to Borrower", "Mandate Link", mailModel, "User");

                        const notificationTemplate = await NotificationTemplates.findOne({ partnerId: loanInfo.partnerId, template: "nach_failure" });

                        let smsModel = getSmsModel();

                        smsModel.To = userInfo.userDetails.mobileNo[0] ? userInfo.userDetails.mobileNo[0].mobileNumber : ""; // mobile number change 
                        smsModel.Type = notificationTemplate.type;
                        smsModel.From = config.get("keys.from");

                        smsModel.TemplateName = notificationTemplate.whatsAppTemplate;
                        smsModel.VAR1 = `${userInfo.userDetails.firstName} ${userInfo.userDetails.lastName}`;
                        smsModel.VAR2 = `${loanInfo.amount}`;
                        smsModel.VAR3 = mandateCreation.mode === "esign" ? "aadhaar" : "physical";
                        smsModel.VAR4 = mandateCreation.rejectReason;
                        smsModel.VAR5 = notificationTemplate.var5;
                        smsModel.VAR6 = config.get("karix.ffSupportLink");
                        smsModel.CTA = eNachUrl;

                        const whatsAppResponse = await pushToNotifications(loanInfo.partnerId, loanInfo.merchantCode, loanInfo.productId, loanInfo.trxnId,
                            loanInfo.userId, loanInfo._id, "WhatsApp", userInfo.userDetails.mobileNo[0].mobileNumber, "nach_failure", null, null, null, smsModel, false, null, "primary", eNachUrl);// mobile number change 

                        if (whatsAppResponse.success)
                            await sendNotifications(whatsAppResponse.data._id);
                    }
                }

                //Set oldMandate is inActive
                if (checkMandate.success && mandateCreation.status === "register_success") {
                    mandateCreation = await DigioMandateGeneration.findOne({ mandate_id: mandate_id });
                    mandateCreation.isActive = true;

                    const oldMandates = await DigioMandateGeneration.find({ userId: mandateCreation.userId, partnerId: mandateCreation.partnerId, isActive: true, mandate_id: { $ne: mandate_id } });

                    for (const oldMandate of oldMandates) {
                        //move old mandate loans to current mandate
                        for (const id of oldMandate.loanIds) {
                            if (!mandateCreation.loanIds.includes(id)) {
                                mandateCreation.loanIds.push(id);
                                mandateCreation.updatedOn = Date.now();
                                updateMandateLogs(mandateCreation, id);

                                const loan = await LoanInfo.findById(id);

                                if (loan) {
                                    loan.loanContractDetails.authMode = mandateCreation.mode;
                                    await loan.save();
                                }
                            }

                        }

                        //set old mandate as inactive
                        oldMandate.isActive = false;
                        oldMandate.inActiveFrom = Date.now();
                        oldMandate.cancellationErrorMessage = "Cancelled as new mandate created due to change in bank details.";
                        oldMandate.inActiveBy = null;

                        await oldMandate.save();

                    }

                    await mandateCreation.save();

                    //check if any other loans dont have mandate and assign this mandate to them
                    mandateCreation = await DigioMandateGeneration.findOne({ mandate_id: mandate_id });

                    const loanList = await LoanInfo.find({
                        userId: mandateCreation.userId,
                        partnerId: mandateCreation.partnerId,
                        primaryCollectionType: "Wallet",
                        secondaryCollectionType: "",
                        status: "Loan In Progress"
                    });

                    for (const item of loanList) {

                        const mandate = await DigioMandateGeneration.findOne({ loanIds: item._id, isActive: true });

                        if (!mandate) {
                            if (mandateCreation && !mandateCreation.loanIds.includes(item._id)) {
                                mandateCreation.loanIds.push(item._id);
                                mandateCreation.updatedOn = Date.now();
                                await mandateCreation.save();
                            }

                            item.secondaryCollectionType = "eNach";
                            item.loanContractDetails.eNachUrl = item.loanContractDetails.contractUrl;
                            item.loanContractDetails.authMode = mandateCreation.mode;
                            await item.save();

                            updateMandateLogs(mandateCreation, item._id);

                        }
                    }

                    if (loanInfo.status.toLowerCase() === "recourse pending") {
                        const errorCode = config.get("errorCode.pendingForMandateRegistration");

                        let issues = loanInfo.issues || [];

                        const filteredIssue = issues.filter(element => element.code != errorCode);
                        issues = filteredIssue;

                        await LoanInfo.updateOne(
                            {
                                _id: loanId
                            },
                            {
                                $set: {
                                    issues
                                }
                            }
                        );

                        await workflowService.loanContractAccepted(loanId, null, true);
                    }

                }


                if (model.payload.mandate.current_status === "revoked" || (model.payload.mandate.current_status === "success" && model.event == "mndt.cancelinit")) {
                    mandateCreation = await DigioMandateGeneration.findOne({ _id: mandateCreation._id });
                    mandateCreation.cancelledOn = new Date()
                    mandateCreation.cancellationConfirmed = true
                    mandateCreation.isActive = false;
                    mandateCreation.inActiveFrom = Date.now();
                    mandateCreation.cancellationErrorMessage = "User Cancelled";
                    mandateCreation.inActiveBy = mandateCreation.userId;
                    mandateCreation.save()

                    let user = await UserInfo.findById(userId)
                    let mobileNo = user.userDetails && user.userDetails.mobileNo && user.userDetails.mobileNo[0] ? user.userDetails.mobileNo[0].mobileNumber : ""; // mobile number change 
                    const bankName = mandateCreation.mandateDetails && mandateCreation.mandateDetails.mandate_data && mandateCreation.mandateDetails.mandate_data.destination_bank_name ? mandateCreation.mandateDetails.mandate_data.destination_bank_name : " ";
                    const mandateAmount = mandateCreation.mandateDetails && mandateCreation.mandateDetails.mandate_data && mandateCreation.mandateDetails.mandate_data.maximum_amount ? mandateCreation.mandateDetails.mandate_data.maximum_amount : "";

                    let smsModel = getSmsModel();
                    smsModel.To = mobileNo;
                    smsModel.TemplateName = "enach_cancellation_confirmation";
                    smsModel.VAR1 = user.userDetails.firstName;
                    smsModel.VAR2 = bankName;
                    smsModel.VAR3 = mandateAmount;
                    smsModel.Type = "TEMPLATE";
                    smsModel.From = config.get("keys.from");

                    // const sendSms = await pushToNotifications(null, null, null, null,
                    //     mandateCreation.userId, null, "WhatsApp", smsModel.To, smsModel.TemplateName, "loan", null, null, smsModel, false, null);
                    // if (sendSms.success)
                    //     response = await sendNotifications(sendSms.data._id);

                    // let mailModel = getMailModel();

                    // mailModel.accountnumber = bankName
                    // mailModel.amount = mandateAmount
                    // sendMail(user._id, "", "Loan Application Emails to Borrower", "Mandate Cancellation Confirmation", mailModel, "User");

                }


                const isExists = await MandateErrorCodes.findOne({ code: mandateCreation.rejectCode, isActive: true }).countDocuments();
                //console.log("isExists", isExists);
                //check if error code exists in exclusion list
                if (isExists === 0) {
                    const partnerMaster = await PartnerMaster.findById(mandateCreation.partnerId).select({ partnerCode: 1, displayName: 1 });
                    const loanInfo = await LoanInfo.findById(loanId).select({ productType: 1 });
                    const productType = getProductType(loanInfo.productType);
                    const conf = config.get(partnerMaster.partnerCode.toLowerCase());
                    //console.log("conf", conf);
                    let isPartnerNotified = true;

                    if (conf && conf.eNachFailureApis && conf.eNachFailureApis != null) {
                        let apis = conf.eNachFailureApis;
                        if (apis) {
                            apis = apis.filter((x) => x.productType == productType);
                            //console.log("apis", apis);
                            if (apis.length > 0) {
                                for (let i = 0; i < apis.length; i++) {
                                    const borrowingParterRequestModel = apis[i].model != null ? JSON.parse(JSON.stringify(apis[i].model)) : {};
                                    borrowingParterRequestModel.source = "application";
                                    borrowingParterRequestModel.module = "eNachFailureApis";
                                    borrowingParterRequestModel.loanId = loanInfo._id;
                                    borrowingParterRequestModel.partnerId = partnerMaster._id;
                                    borrowingParterRequestModel.partnerCode = partnerMaster.partnerCode;
                                    borrowingParterRequestModel.partnerDisplayName = partnerMaster.displayName;
                                    borrowingParterRequestModel.apiType = apis[i].apiType;
                                    borrowingParterRequestModel.isRetryOffline = apis[i].isRetryOffline;
                                    borrowingParterRequestModel.url = apis[i].url;
                                    borrowingParterRequestModel.rejectCode = mandateCreation.rejectCode;
                                    borrowingParterRequestModel.rejectReason = mandateCreation.rejectReason;

                                    if (apis[i].isWaitForResponse) {
                                        const borrowerApiResponse = await callBorrowingPartnerAPI(borrowingParterRequestModel);
                                        if (!borrowerApiResponse.success) {
                                            i = apis.length;
                                            output.message = borrowerApiResponse.message;
                                            return output;
                                        }
                                    } else {
                                        logger.logs("borrowingParterRequestModel", borrowingParterRequestModel);
                                        callBorrowingPartnerAPI(borrowingParterRequestModel);
                                    }
                                }
                                isPartnerNotified = true;
                            }
                        } else {
                            isPartnerNotified = true;
                        }
                    }
                }


                return {
                    data: "",
                    success: true,
                    message: "Saved successfully"
                }
            }
            else {
                await eNachResponse(userId, loanIds, mandate_id, model);

                return {
                    data: "",
                    success: false,
                    message: "Mandate Id not found"
                }
            }
        }
        else {

            return {
                data: "",
                success: false,
                message: "Mandate Id not received"
            }
        }
    }
    catch (ex) {
        saveErrorLog("updateAadhaarEnachResponse", apiPath, { model }, { error: ex.message, stackTrace: ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex });
        await eNachResponse(userId, loanIds, mandate_id, model);
        return { data: ex.message, success: false, message: ex.message };
    }
}



function syncAadhaarPayloadToApiPayload(payload, modal, mode) {


    if (payload.mandate.current_status === "signed" || payload.mandate.current_status === "transfer_success"
        || payload.mandate.current_status === "spo_accepted" || payload.mandate.current_status === "npci_ack_received")
        payload.mandate.current_status = "success"
    if (payload.mandate.current_status === "dest_register_success") payload.mandate.current_status = "register_success"

    payload.mandate.message_id = modal.id
    payload.mandate.npci_txn_id = ""
    payload.mandate.txn_timestamp = payload.mandate.transfer_timestamp
    payload.mandate.txn_reject_code = payload.mandate.dest_bank_reject_code
    payload.mandate.umrn = payload.mandate.umrn
    payload.mandate.txn_reject_reason = payload.mandate.transfer_fail_reason || payload.mandate.spo_bank_reject_reason || payload.mandate.npci_reject_reason || payload.mandate.dest_bank_reject_reason
    payload.mandate.pki_signature_details = payload.mandate.pki_signature_details
    payload.mandate.auth_sub_mode = mode;
    payload.others = {
        scheme_ref_number: payload.mandate.scheme_ref_number,
        customer_ref_number: payload.mandate.customer_ref_number
    }
}

async function eNachResponse(userId, loanIds, mandate_id, model) {

    try {

        let log = new DigioNachResponseLog({
            userId: userId ? userId : null,
            loanIds: loanIds ? loanIds : null,
            mandate_id: mandate_id,
            response: JSON.stringify(model),
            responseReceivedOn: Date.now()
        });

        let response = await log.save();

        return { data: response, success: true };
    }
    catch (ex) {
        saveErrorLog("updateEnachResponse", apiPath, { userId, loanIds, mandate_id, model }, { error: ex.message, stackTrace: ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex });
        let log = new DigioNachResponseLog({
            userId: userId ? userId : null,
            loanIds: loanIds ? loanIds : null,
            mandate_id: mandate_id,
            response: JSON.stringify(model),
            responseReceivedOn: Date.now()
        });

        let response = await log.save();

        return { data: ex.message, success: false, message: ex.message };
    }
}

module.exports.updateSigningStatus = async function (mandate_id, status, message, npci_txn_id) {
    const mandate = await DigioMandateGeneration.findOne({ mandate_id: mandate_id });

    try {
        if (!mandate)
            return {
                data: "",
                success: false,
                message: "No record found for mandate Id"
            }
        else if (status.toLowerCase() == "success") {
            const loanId = mandate.loanIds && mandate.loanIds[0] ? mandate.loanIds[0] : null;

            const checkMandate = await checkMandateCurrentBankDetails(mandate, loanId);

            if (npci_txn_id)
                mandate.npci_txn_id = npci_txn_id;

            mandate.status = mandate.status.toLowerCase() == "register_success" ? mandate.status : status;

            mandate.ackResponse.push({
                status: status,
                message: message,
                npci_txn_id: npci_txn_id,
                signedOn: Date.now(),
                responseReceivedOn: Date.now()
            });

            mandate.rejectCode = "";
            mandate.rejectReason = "";

            await mandate.save();

            if (checkMandate.success) {

                const loan = await LoanInfo.findById(loanId);

                loan.loanContractDetails.authMode = mandate.mode ? mandate.mode : loan.loanContractDetails.authMode;

                loan.isForceMandate = false;

                await loan.save();

                if (loan.status.toLowerCase() === "recourse pending" && mandate.mode === "api") {
                    const errorCode = config.get("errorCode.pendingForMandateRegistration");

                    let issues = loan.issues || [];

                    const filteredIssue = issues.filter(element => element.code != errorCode);
                    issues = filteredIssue;

                    await LoanInfo.updateOne(
                        {
                            _id: loanId
                        },
                        {
                            $set: {
                                issues
                            }
                        }
                    );

                    await workflowService.loanContractAccepted(loanId, null, true);
                }

                return {
                    userId: mandate.userId,
                    loanId: loanId,
                    success: true,
                    message: message,
                    isStp: loan.isStp ? true : false,
                    redirectUrl: null,
                    partnerId: loan.partnerId,
                    eNachErrorCallBackUrl: loan.loanContractDetails && loan.loanContractDetails.eNachErrorCallBackUrl ? loan.loanContractDetails.eNachErrorCallBackUrl : null
                }
            }
            else {
                let nachUrl = null;

                const loan = await LoanInfo.findById(loanId);

                const authMode = loan.loanContractDetails && loan.loanContractDetails.authMode ? loan.loanContractDetails.authMode : "";

                const mandateGeneration = await DigioMandateGeneration.findOne({ loanIds: loanId, userId: loan.userId, partnerId: loan.partnerId, mode: authMode }).sort({ createdOn: -1 });

                let message = null;

                if (mandateGeneration.id !== mandate.id) {
                    const user = await UserInfo.findById(mandate.userId);

                    nachUrl = config.get("digio.eNach.nachLinkUrl");

                    redirectUrl = "?redirect_url=" + config.get("digio.eNach.redirectUrl") + `&logo=${config.get("digio.eNach.logoLink")}`;

                    if (mandateGeneration.access_token && mandateGeneration.access_token.id)
                        redirectUrl += `&token_id=${mandateGeneration.access_token.id}`;

                    const mandateId = mandateGeneration.mandate_id ? mandateGeneration.mandate_id : "";

                    const clientCode = loan.merchantCode ? loan.merchantCode : "";

                    const mobileNo = mandateGeneration.mandateDetails && mandateGeneration.mandateDetails.customer_identifier ? mandateGeneration.mandateDetails.customer_identifier : "";

                    nachUrl += mandateId + "/" + clientCode + "/" + mobileNo + redirectUrl;

                    message = "Mandate is no longer active as bank details have been changed. Please use latest mandate link";
                }
                else
                    message = "Mandate is no longer active as bank details have been changed. No new mandate found with updated bank details";

                return {
                    userId: mandate.userId,
                    loanId: mandate.loanIds && mandate.loanIds[0] ? mandate.loanIds[0] : null,
                    success: true,
                    message: message,
                    isStp: loan && loan.isStp ? true : false,
                    redirectUrl: nachUrl,
                    partnerId: loan.partnerId,
                    eNachErrorCallBackUrl: loan.loanContractDetails && loan.loanContractDetails.eNachErrorCallBackUrl ? loan.loanContractDetails.eNachErrorCallBackUrl : null
                }
            }
        }
        else {
            if (npci_txn_id)
                mandate.npci_txn_id = npci_txn_id;

            mandate.status = (mandate.status.toLowerCase() == "success" || mandate.status.toLowerCase() == "register_success") ? mandate.status : status;

            const loanId = mandate.loanIds && mandate.loanIds[0] ? mandate.loanIds[0] : null;

            let loan = null;

            if (loanId)
                loan = await LoanInfo.findById(loanId);

            mandate.ackResponse.push({
                status: status,
                message: message,
                signedOn: null,
                npci_txn_id: npci_txn_id,
                responseReceivedOn: Date.now()
            });

            await mandate.save();

            return {
                userId: mandate.userId,
                loanId: loanId,
                success: true,
                message: message,
                isStp: loan && loan.isStp ? true : false,
                redirectUrl: null,
                partnerId: loan.partnerId,
                eNachErrorCallBackUrl: loan.loanContractDetails && loan.loanContractDetails.eNachErrorCallBackUrl ? loan.loanContractDetails.eNachErrorCallBackUrl : null
            }
        }

    }
    catch (ex) {
        saveErrorLog("updateSigningStatus", apiPath, { mandate_id, status, message, npci_txn_id }, { error: ex.message, stackTrace: ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex });
        return {
            userId: mandate.userId,
            loanId: mandate.loanIds && mandate.loanIds[0] ? mandate.loanIds[0] : null,
            success: false,
            message: ex.message
        }
    }
}

async function scheduleLog(model) {
    let log = new DigioScheduleLog({
        request: JSON.stringify(model.request),
        response: JSON.stringify(model.response),
        url: model.url,
        sentOn: Date.now(),
        userId: model.userId ? model.userId : null,
        loanId: model.loanId ? model.loanId : null
    });

    let response = await log.save();

    return response;

}

async function scheduleCreation(model, userId, loanId) {

    const url = config.get("digio.eNach.createSchedule");

    try {

        const response = await callDigioApi("POST", url, model);

        let digioScheduleLog = {
            request: model,
            response: response,
            url: url,
            sentOn: Date.now(),
            userId: userId ? userId : null,
            loanId: loanId ? loanId : null
        }

        await scheduleLog(digioScheduleLog);

        return { data: response, success: true, message: "Schedule created" };
    }
    catch (ex) {
        saveErrorLog("scheduleCreation", apiPath, { model, userId, loanId }, { error: ex.message, stackTrace: ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex });

        let digioScheduleLog = {
            request: model,
            response: ex.response.data,
            url: url,
            sentOn: Date.now(),
            userId: userId ? userId : null,
            loanId: loanId ? loanId : null
        }

        await scheduleLog(digioScheduleLog);

        return { data: ex.response.data.code, success: false, message: ex.response.data.message };
    }
}

//Debit WebHook
module.exports.debitWebHook = async function (model) {

    let entitity = model.entities && model.entities[0];

    let nspId = model.payload && model.payload[entitity] && model.payload[entitity].id ? model.payload[entitity].id : "";
    let userId = null;
    let loanId = null;

    try {
        if (nspId) {
            let schedule = await DigioCollectionSchedule.findOne({ id: nspId });

            if (schedule) {
                userId = schedule.userId ? schedule.userId : null;
                loanId = schedule.loanId ? schedule.loanId : null;
            }

            await debitResponseLog(userId, loanId, nspId, model);

            if (schedule) {
                let debitResponse = new DigioDebitWebHookResponse({
                    entities: model.entities ? model.entities : [],
                    payload: {
                        [entitity]: {
                            id: model.payload && model.payload[entitity] && model.payload[entitity].id ? model.payload[entitity].id : "",
                            settlement_date: model.payload && model.payload[entitity] && model.payload[entitity].settlement_date ? new Date(model.payload[entitity].settlement_date) : null,
                            user_name: model.payload && model.payload[entitity] && model.payload[entitity].user_name ? model.payload[entitity].user_name : "",
                            amount: model.payload && model.payload[entitity] && model.payload[entitity].amount_in_paise ? model.payload[entitity].amount_in_paise / 10 : 0,
                            amount_in_paise: model.payload && model.payload[entitity] && model.payload[entitity].amount_in_paise ? model.payload[entitity].amount_in_paise : 0,
                            txn_reference: model.payload && model.payload[entitity] && model.payload[entitity].txn_reference ? model.payload[entitity].txn_reference : "",
                            umrn: model.payload && model.payload[entitity] && model.payload[entitity].umrn ? model.payload[entitity].umrn : "",
                            scheduled_payment_id: model.payload && model.payload[entitity] && model.payload[entitity].scheduled_payment_id ? model.payload[entitity].scheduled_payment_id : "",
                            failure_reason: model.payload && model.payload[entitity] && model.payload[entitity].failure_reason ? model.payload[entitity].failure_reason : "",
                            failure_description: model.payload && model.payload[entitity] && model.payload[entitity].failure_description ? model.payload[entitity].failure_description : "",
                            status: model.payload && model.payload[entitity] && model.payload[entitity].status ? model.payload[entitity].status : "",
                            transaction_id: model.payload && model.payload[entitity] && model.payload[entitity].transaction_id ? model.payload[entitity].transaction_id : "",
                            total_payment_count: model.payload && model.payload[entitity] && model.payload[entitity].total_payment_count ? model.payload[entitity].total_payment_count : 0,
                            processed_payment_count: model.payload && model.payload[entitity] && model.payload[entitity].processed_payment_count ? model.payload[entitity].processed_payment_count : 0,
                            next_scheduled_settlement_date: model.payload && model.payload[entitity] && model.payload[entitity].next_scheduled_settlement_date ? new Date(model.payload[entitity].next_scheduled_settlement_date) : null,
                            others: {
                                client_ref_id: model.payload && model.payload[entitity] && model.payload[entitity].others && model.payload[entitity].others.client_ref_id ? model.payload[entitity].others.client_ref_id : "",
                                customer_ref_number: model.payload && model.payload[entitity] && model.payload[entitity].others && model.payload[entitity].others.customer_ref_number ? model.payload[entitity].others.customer_ref_number : "",
                            },
                            service_provider_name: model.payload && model.payload[entitity] && model.payload[entitity].service_provider_name ? model.payload[entitity].service_provider_name : "",
                            sponsor_bank_name: model.payload && model.payload[entitity] && model.payload[entitity].sponsor_bank_name ? model.payload[entitity].sponsor_bank_name : "",
                            transaction_type: model.payload && model.payload[entitity] && model.payload[entitity].transaction_type ? model.payload[entitity].transaction_type : "",
                        },
                    },
                    id: model.id ? model.id : "",
                    created_at: model.created_at ? new Date(model.created_at) : null,
                    event: model.event ? model.event : "",
                    responseReceivedOn: Date.now(),
                    userId: userId,
                    loanId: loanId
                });

                const settlementDate = model.created_at ? new Date(model.created_at) : Date.now();
                //update
                await debitResponse.save();

                const oldStatus = schedule.payment_status;

                schedule.lastEventCalled = model.event ? model.event : "";
                schedule.payment_status = model.payload && model.payload[entitity] && model.payload[entitity].status ? model.payload[entitity].status : "";
                schedule.failure_reason = model.payload && model.payload[entitity] && model.payload[entitity].failure_reason ? model.payload[entitity].failure_reason : "";
                schedule.failure_description = model.payload && model.payload[entitity] && model.payload[entitity].failure_description ? model.payload[entitity].failure_description : "";
                schedule.lastEventCalledAt = Date.now();
                schedule.transaction_id = model.payload && model.payload[entitity] && model.payload[entitity].transaction_id ? model.payload[entitity].transaction_id : "";

                // PAYMENT COLLECTION
                let paymentCollectionUpdateObj = {
                    lastEventCalled: model.event,
                    paymentStatus: schedule.payment_status,
                    paidOn: settlementDate
                };

                if (model.event == "nach.debit.failed" || model.event == "nach.debit.spobankreject" || model.event == "nach.debit.tranfail" || model.event == "nach.debit.txn.skipped" || model.event == "nach.debit.success")
                    schedule.debitResponseReceived = true;

                if (schedule.payment_status.toLowerCase() == "payment_success") {
                    schedule.settlementDoneOn = settlementDate;
                    schedule.sendingState = "Paid";

                    // PAYMENT COLLECTION
                    paymentCollectionUpdateObj.sendingState = PAYMENT_SENDING_STATE.PAID

                }
                else
                    schedule.sendingState = "Send";

                await schedule.save();

                //PAYMENT COLLECTION
                let ffUniqueKey = model.payload && model.payload[entitity] && model.payload[entitity].others && model.payload[entitity].others.client_ref_id ? model.payload[entitity].others.client_ref_id : "";

                if (ffUniqueKey) {
                    let { error: paymentCollectionUpdateError, response: paymentCollectionUpdateResponse } = await updatePaymentCollection({ query: { referenceNo: ffUniqueKey }, update: paymentCollectionUpdateObj })

                    if (paymentCollectionUpdateError) {
                        return {
                            data: "",
                            success: false,
                            message: `Payment Collection Update Error`
                        }
                    }
                } else {
                    //Need to send mail
                    saveErrorLog("debitWebHook", apiPath, {}, { error: "Payment Collection Update Failed - ffUniqueKey not found" });
                    return {
                        data: "",
                        success: false,
                        message: `Payment Collection Update Failed - ffUniqueKey not found`
                    }
                }

                if (schedule.payment_status.toLowerCase() == "payment_success" && oldStatus.toLowerCase() != "payment_success") {

                    const emiRequest = await EmiRequest.find({ refId: nspId });

                    for (const emi of emiRequest) {
                        await installmentService.updateEmiPaidStatus(loanId, emi.emiNumber, emi.totalAmount, settlementDate, "Enach API", "System", ffUniqueKey);
                    }

                    await unholdWalletCollections(loanId);

                    return {
                        data: "",
                        success: true,
                        message: "Saved successfully"
                    }

                }
                else if (schedule.lastEventCalled.toLowerCase() == "nach.debit.txn.skipped" || schedule.lastEventCalled.toLowerCase() == "nach.debit.tranfail" || schedule.lastEventCalled.toLowerCase() == "nach.debit.tranfail" || schedule.lastEventCalled.toLowerCase() == "nach.debit.spobankreject" || schedule.lastEventCalled.toLowerCase() == "nach.debit.failed") {
                    let mailModel = getMailModel();

                    let loan = await LoanInfo.findById(loanId);

                    let emiList = "";

                    for (const emiNo of schedule.emis)
                        emiList += emiNo + ", ";

                    if (emiList)
                        emiList = emiList.substring(0, emiList.length - 2);

                    mailModel.number = loan.loanNo;
                    mailModel.number1 = emiList ? emiList.toString() : "";
                    mailModel.date = moment(settlementDate).format("DD/MM/yyyy");
                    mailModel.rejectCode = schedule.failure_reason;
                    mailModel.rejectionreason = schedule.failure_description;
                    mailModel.event = schedule.lastEventCalled;
                    mailModel.amount = schedule.totalAmount.toString();
                    mailModel.attempt = schedule.retryCount.toString();


                    //Send Email to Admin
                    if (loan.status !== 'Written-Off') {
                        await sendMail("", schedule.loanId, "Loan Application Emails to Admin", "Debit Collection Failure", mailModel, "Admin");
                    }

                    return {
                        data: "",
                        success: true,
                        message: "Saved successfully"
                    }

                }
                else {
                    await debitResponseLog(userId, loanId, nspId, model);
                    return {
                        data: "",
                        success: false,
                        message: `${nspId} in processing with status ${schedule.payment_status}`
                    }
                }
            }
            else {

                return {
                    data: "",
                    success: false,
                    message: "NSP Id not received"
                }
            }
        }
    }
    catch (ex) {
        saveErrorLog("debitWebHook", apiPath, { model }, { error: ex.message, stackTrace: ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex });

        await debitResponseLog(userId, loanId, nspId, model);
        return { data: ex.message, success: false, message: ex.message };
    }
}

async function debitResponseLog(userId, loanId, nspId, model) {

    try {

        let log = new DigioDebitResponseLog({
            userId: userId ? userId : null,
            loanId: loanId ? loanId : null,
            nspId: nspId,
            response: JSON.stringify(model),
            responseReceivedOn: Date.now()
        });

        let response = await log.save();

        return { data: response, success: true };
    }
    catch (ex) {
        saveErrorLog("debitResponseLog", apiPath, { userId, loanId, nspId, model }, { error: ex.message, stackTrace: ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex });

        let log = new DigioDebitResponseLog({
            userId: userId ? userId : null,
            loanId: loanId ? loanId : null,
            nspId: nspId,
            response: JSON.stringify(model),
            responseReceivedOn: Date.now()
        });

        let response = await log.save();

        return { data: ex.message, success: false, message: ex.message };
    }
}

module.exports.scheduleNextDayRepayments = async function () {
    try {
        let query1 = {};

        query1["status"] = "Loan In Progress";

        query1["isDraft"] = false;

        query1["primaryCollectionType"] = "eNach";

        let loans = await LoanInfo.find(query1).select({
            loanNo: 1,
            userId: 1,
            _id: 1,
            amount: 1,
            purpose: 1,
            maturity: 1,
            tenure: 1,
            interestRate: 1,
            loanType: 1,
            isMaturityMonth: 1,
            insertedOn: 1,
            adminRemarks: 1,
            rejectDescription: 1,
            repaymentFrequency: 1,
            postDisbursalStatus: 1,
            clientRemarks: 1,
            status: 1,
        });

        const loanIds = loans.map(x => x._id);

        const today = new Date();
        let emiDate = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 5, 30, 0, 0);
        emiDate = dateAdd("day", 1, emiDate);

        if (loanIds.length === 0)
            return {
                data: "",
                success: true,
                message: "No loans found",
                status: 200
            }

        const loanInstallments = await installmentService.getMultipleLoansInstallments(loanIds, null, ["Pending", "Partial"]);

        if (!loanInstallments.success)
            return loanInstallments;

        const loanFees = await installmentService.getMultipleLoanFees(loanIds, null, ["Pending", "Partial"]);

        if (!loanFees.success)
            return loanFees;
        ////const uniqueUserIds = [...new Set(loans.map(loan => loan.userId))];

        // const userInfo = await UserInfo.find({ _id: { $in: uniqueUserIds } }).select({ _id: 1, userDetails: 1 });
        // let userHash = {};
        // for (let i = 0; i < userInfo.length; i++) {
        //     userHash[userInfo[i]._id.toString()] = userInfo[i];
        // }

        for (const loan of loans) {
            const installmentDetails = loanInstallments.data.filter((x) => x.loanId.toString() == loan._id.toString() && getYMDFormattedDate(x.dueDate, "-") == getYMDFormattedDate(emiDate, "-"));
            if (installmentDetails != null && installmentDetails.length > 0) {
                for (const installment of installmentDetails) {
                    let fees = [];
                    if (loanFees.data != null && loanFees.data.length > 0) {
                        fees = loanFees.data.filter((x) => x.loanId.toString() == loan._id.toString() && x.installmentNumber == installment.installmentNumber);
                    }
                    await sendScheduleRequest(loan, installment, loan.partnerId, fees);
                }
            }
        }

        return {
            data: "",
            success: true,
            message: "Scheduler run complete",
            status: 200,
        };

    }
    catch (ex) {
        saveErrorLog("scheduleNextDayRepayments", apiPath, {}, { error: ex.message, stackTrace: ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex });

        let errorModel = new DigioSchedulerLog({
            userId: null,
            loanId: null,
            event: "error",
            error: ex.message,
            insertedOn: Date.now()
        });

        await errorModel.save();

        let endModel = new DigioSchedulerLog({
            userId: null,
            loanId: null,
            event: "info",
            error: "Schedular ended",
            insertedOn: Date.now()
        });

        await endModel.save();

        return {
            data: "",
            success: false,
            message: ex.message
        }
    }
}

module.exports.scheduleUnsentRepayments = async function () {
    try {

        let query1 = {};

        query1["status"] = "Loan In Progress";

        query1["isDraft"] = false;

        query1["primaryCollectionType"] = "eNach";

        let loans = await LoanInfo.find(query1).select({
            loanNo: 1,
            userId: 1,
            _id: 1,
            amount: 1,
            purpose: 1,
            maturity: 1,
            tenure: 1,
            interestRate: 1,
            loanType: 1,
            isMaturityMonth: 1,
            insertedOn: 1,
            adminRemarks: 1,
            rejectDescription: 1,
            repaymentFrequency: 1,
            postDisbursalStatus: 1,
            clientRemarks: 1,
            status: 1,
            partnerId: 1,
            productType: 1
        });

        const loanIds = loans.map(x => x._id);

        const today = new Date();

        let emiDate = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 5, 30, 0, 0);
        emiDate = dateAdd("day", 1, emiDate);

        const loanInstallments = await installmentService.getMultipleLoansInstallments(loanIds, null, ["Pending", "Partial"]);

        if (!loanInstallments.success)
            return loanInstallments;

        const loanFees = await installmentService.getMultipleLoanFees(loanIds, null, ["Pending", "Partial"]);
        if (!loanFees.success)
            return loanFees;


        // const uniqueUserIds = [...new Set(loans.map(loan => loan.userId))];

        // const userInfo = await UserInfo.find({ _id: { $in: uniqueUserIds } }).select({ _id: 1, userDetails: 1 });
        // let userHash = {};
        // for (let i = 0; i < userInfo.length; i++) {
        //     userHash[userInfo[i]._id.toString()] = userInfo[i];
        // }

        for (const loan of loans) {
            const penaltyResponse = await installmentService.applyLateFee(loan.partnerId, loan.productType, loan._id, true);

            if (penaltyResponse.success) {
                // loan.userInfo.userDetails = userHash[loan.userId].userDetails;

                let installmentDetails = loanInstallments.data.filter((x) => x.loanId == loan._id && x.dueDate < emiDate);
                if (installmentDetails != null && installmentDetails.length > 0) {
                    for (const installment of installmentDetails) {
                        let fees = [];
                        if (loanFees.data != null && loanFees.data.length > 0) {
                            fees = loanFees.data.filter((x) => x.loanId == loan._id && x.installmentNumber == installment.installmentNumber);
                        }
                        await sendUnsentScheduleRequest(loan, installment, loan.partnerId, fees);
                    }
                }
            }
        }

        let endModel = new DigioSchedulerLog({
            userId: null,
            loanId: null,
            event: "info",
            error: "Schedular ended",
            insertedOn: Date.now()
        });

        await endModel.save();

        return {
            data: "",
            success: true,
            message: "Scheduler run complete",
            status: 200,
        };

    }
    catch (ex) {
        saveErrorLog("scheduleUnsentRepayments", apiPath, {}, { error: ex.message, stackTrace: ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex });

        let errorModel = new DigioSchedulerLog({
            userId: null,
            loanId: null,
            event: "error",
            error: ex.message,
            insertedOn: Date.now()
        });

        await errorModel.save();

        let endModel = new DigioSchedulerLog({
            userId: null,
            loanId: null,
            event: "info",
            error: "Schedular ended",
            insertedOn: Date.now()
        });

        await endModel.save();

        return {
            data: "",
            success: false,
            message: ex.message
        }
    }
}

async function sendScheduleRequest(loan, emi, partnerId, fees) {
    try {
        const schedule = await DigioCollectionSchedule.findOne({ loanId: loan._id, emis: emi.installmentNumber, sendingState: "Send" });

        if (schedule)
            return {
                data: "",
                success: true,
                message: "Schedule already exists"
            }

        const mandate = await DigioMandateGeneration.findOne({ loanIds: loan._id, isActive: true, status: "register_success" });

        if (!mandate)
            return {
                data: "",
                success: false,
                message: "No active mandate found for this loan"
            }

        let installmentFees = 0;
        if (fees != null && fees.length > 0) {
            for (let i = 0; i < fees.length; i++) {
                penalty += fees[i].feeAmount;
                installmentFees += fees[i].feeAmount;
            }
        }

        let { key } = getUniqueKey(loan.loanNo);

        let dueDate = moment(emi.dueDate).format("yyyy-MM-DD");

        let amount = emi.installmentAmount + installmentFees + emi.moratoriumInterest;

        let emiModel = {
            "umrn": mandate.umrn ? mandate.umrn : "",
            "amount": amount,
            "settlement_date": dueDate,
            "corporate_account_number": config.get("digio.eNach.corporate_account_number"),
            "corporate_config_id": config.get("digio.eNach.corporate_config_id"),
            "destination_bank_id": mandate.mandateDetails && mandate.mandateDetails.mandate_data && mandate.mandateDetails.mandate_data.destination_bank_id ? mandate.mandateDetails.mandate_data.destination_bank_id : "",
            "customer_account_number": mandate.mandateDetails && mandate.mandateDetails.mandate_data && mandate.mandateDetails.mandate_data.customer_account_number ? mandate.mandateDetails.mandate_data.customer_account_number : "",
            "customer_name": mandate.mandateDetails && mandate.mandateDetails.mandate_data && mandate.mandateDetails.mandate_data.customer_name ? mandate.mandateDetails.mandate_data.customer_name : "",
            "frequency": mandate.mandateDetails && mandate.mandateDetails.mandate_data && mandate.mandateDetails.mandate_data.frequency ? mandate.mandateDetails.mandate_data.frequency : "",
            "payment_count": 1,
            "unique_key": key
        };

        let sendCreactionRequest = await scheduleCreation(emiModel, loan.userId, loan._id);

        if (sendCreactionRequest.success) {

            let emiSaveData = {
                "id": sendCreactionRequest.data && sendCreactionRequest.data.id ? sendCreactionRequest.data.id : "",
                "umrn": sendCreactionRequest.data && sendCreactionRequest.data.umrn ? sendCreactionRequest.data.umrn : "",
                "status": sendCreactionRequest.data && sendCreactionRequest.data.status ? sendCreactionRequest.data.status : "",
                "upcoming_transaction": {
                    "id": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.id ? sendCreactionRequest.data.upcoming_transaction.id : "",
                    "umrn": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.umrn ? sendCreactionRequest.data.upcoming_transaction.umrn : "",
                    "state": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.state ? sendCreactionRequest.data.upcoming_transaction.state : "",
                    "created_at": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.created_at ? new Date(sendCreactionRequest.data.upcoming_transaction.created_at) : null,
                    "present_at": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.present_at ? new Date(sendCreactionRequest.data.upcoming_transaction.present_at) : null,
                    "scheduled_settlement_date": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.scheduled_settlement_date ? new Date(sendCreactionRequest.data.upcoming_transaction.scheduled_settlement_date) : null,
                    "customer": {
                        "name": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.customer && sendCreactionRequest.data.upcoming_transaction.customer.name ? sendCreactionRequest.data.upcoming_transaction.customer.name : "",
                    },
                    "details": {
                        "status": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.details && sendCreactionRequest.data.upcoming_transaction.details.status ? sendCreactionRequest.data.upcoming_transaction.details.status : "",
                        "amount": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.details && sendCreactionRequest.data.upcoming_transaction.details.amount ? sendCreactionRequest.data.upcoming_transaction.details.amount : 0,
                        "amount_in_paise": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.details && sendCreactionRequest.data.upcoming_transaction.details.amount_in_paise ? sendCreactionRequest.data.upcoming_transaction.details.amount_in_paise : 0,
                        "sponsor_bank_id": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.details && sendCreactionRequest.data.upcoming_transaction.details.sponsor_bank_id ? sendCreactionRequest.data.upcoming_transaction.details.sponsor_bank_id : "",
                        "ready_to_process_at": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.details && sendCreactionRequest.data.upcoming_transaction.details.ready_to_process_at ? new Date(sendCreactionRequest.data.upcoming_transaction.details.ready_to_process_at) : null,
                        "type": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.details && sendCreactionRequest.data.upcoming_transaction.details.type ? sendCreactionRequest.data.upcoming_transaction.details.type : "",
                        "txn_ref": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.details && sendCreactionRequest.data.upcoming_transaction.details.txn_ref ? sendCreactionRequest.data.upcoming_transaction.details.txn_ref : "",
                    }
                },
                "mandate": {
                    "id": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.id ? sendCreactionRequest.data.mandate.id : "",
                    "mandate_id": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_id ? sendCreactionRequest.data.mandate.mandate_id : "",
                    "state": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.state ? sendCreactionRequest.data.mandate.state : "",
                    "type": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.type ? sendCreactionRequest.data.mandate.type : "",
                    "mandate_details": {
                        "file_name": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.file_name ? sendCreactionRequest.data.mandate.mandate_details.file_name : "",
                        "customer_identifier": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.customer_identifier ? sendCreactionRequest.data.mandate.mandate_details.customer_identifier : "",
                        "customer_name": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.customer_name ? sendCreactionRequest.data.mandate.mandate_details.customer_name : "",
                        "customer_ref_number": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.customer_ref_number ? sendCreactionRequest.data.mandate.mandate_details.customer_ref_number : "",
                        "scheme_ref_number": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.scheme_ref_number ? sendCreactionRequest.data.mandate.mandate_details.scheme_ref_number : "",
                        "customer_mobile": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.customer_mobile ? sendCreactionRequest.data.mandate.mandate_details.customer_mobile : "",
                        "auth_type": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.auth_type ? sendCreactionRequest.data.mandate.mandate_details.auth_type : "",
                        "authentication_time": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.authentication_time ? new Date(sendCreactionRequest.data.mandate.mandate_details.authentication_time) : null,
                        "is_recurring": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.is_recurring ? sendCreactionRequest.data.mandate.mandate_details.is_recurring : false,
                        "frequency": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.frequency ? sendCreactionRequest.data.mandate.mandate_details.frequency : "",
                        "first_collection_date": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.first_collection_date ? new Date(sendCreactionRequest.data.mandate.mandate_details.first_collection_date) : null,
                        "final_collection_date": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.final_collection_date ? new Date(sendCreactionRequest.data.mandate.mandate_details.final_collection_date) : null,
                        "maximum_amount": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.maximum_amount ? sendCreactionRequest.data.mandate.mandate_details.maximum_amount : 0,
                        "customer_account_number": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.customer_account_number ? sendCreactionRequest.data.mandate.mandate_details.customer_account_number : "",
                        "customer_account_type": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.customer_account_type ? sendCreactionRequest.data.mandate.mandate_details.customer_account_type : "",
                        "destination_bank_id": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.destination_bank_id ? sendCreactionRequest.data.mandate.mandate_details.destination_bank_id : "",
                        "destination_bank_name": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.destination_bank_name ? sendCreactionRequest.data.mandate.mandate_details.destination_bank_name : "",
                        "sponsor_bank_name": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.sponsor_bank_name ? sendCreactionRequest.data.mandate.mandate_details.sponsor_bank_name : "",
                        "npci_txn_id": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.npci_txn_id ? sendCreactionRequest.data.mandate.mandate_details.npci_txn_id : "",
                        "account_validation": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.account_validation ? sendCreactionRequest.data.mandate.mandate_details.account_validation : "",
                        "block_fund": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.block_fund ? sendCreactionRequest.data.mandate.mandate_details.block_fund : false,
                        "revocable": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.revocable ? sendCreactionRequest.data.mandate.mandate_details.revocable : false,
                    },
                    "umrn": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.umrn ? sendCreactionRequest.data.mandate.umrn : "",
                    "created_at": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.created_at ? new Date(sendCreactionRequest.data.mandate.created_at) : null,
                    "updated_at": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.updated_at ? new Date(sendCreactionRequest.data.mandate.updated_at) : null,
                    "mode": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mode ? sendCreactionRequest.data.mandate.mode : "",
                    "auth_sub_mode": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.auth_sub_mode ? sendCreactionRequest.data.mandate.auth_sub_mode : "",
                },
                "customer": {
                    "name": sendCreactionRequest.data && sendCreactionRequest.data.customer && sendCreactionRequest.data.customer.name ? sendCreactionRequest.data.customer.name : "",
                    "destination_account_type": sendCreactionRequest.data && sendCreactionRequest.data.customer && sendCreactionRequest.data.customer.destination_account_type ? sendCreactionRequest.data.customer.destination_account_type : "",
                    "destination_ifsc": sendCreactionRequest.data && sendCreactionRequest.data.customer && sendCreactionRequest.data.customer.destination_ifsc ? sendCreactionRequest.data.customer.destination_ifsc : "",
                    "user_account_number": sendCreactionRequest.data && sendCreactionRequest.data.customer && sendCreactionRequest.data.customer.user_account_number ? sendCreactionRequest.data.customer.user_account_number : "",
                },
                "corporate": {
                    "name": sendCreactionRequest.data && sendCreactionRequest.data.corporate && sendCreactionRequest.data.corporate.name ? sendCreactionRequest.data.corporate.name : "",
                    "utility_code": sendCreactionRequest.data && sendCreactionRequest.data.corporate && sendCreactionRequest.data.corporate.utility_code ? sendCreactionRequest.data.corporate.utility_code : "",
                    "corporate_reference": sendCreactionRequest.data && sendCreactionRequest.data.corporate && sendCreactionRequest.data.corporate.corporate_reference ? sendCreactionRequest.data.corporate.corporate_reference : "",
                    "sponsor_ifsc": sendCreactionRequest.data && sendCreactionRequest.data.corporate && sendCreactionRequest.data.corporate.sponsor_ifsc ? sendCreactionRequest.data.corporate.sponsor_ifsc : "",
                    "sponsor_bank_code": sendCreactionRequest.data && sendCreactionRequest.data.corporate && sendCreactionRequest.data.corporate.sponsor_bank_code ? sendCreactionRequest.data.corporate.sponsor_bank_code : "",
                    "corporate_account_number": sendCreactionRequest.data && sendCreactionRequest.data.corporate && sendCreactionRequest.data.corporate.corporate_account_number ? sendCreactionRequest.data.corporate.corporate_account_number : "",
                },
                "partner": {
                    "name": sendCreactionRequest.data && sendCreactionRequest.data.partner && sendCreactionRequest.data.partner.name ? sendCreactionRequest.data.partner.name : "",
                    "identifier": sendCreactionRequest.data && sendCreactionRequest.data.partner && sendCreactionRequest.data.partner.identifier ? sendCreactionRequest.data.partner.identifier : "",
                },
                "details": {
                    "first_proposed_settlement_date": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.first_proposed_settlement_date ? new Date(sendCreactionRequest.data.details.first_proposed_settlement_date) : null,
                    "payment_count": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.payment_count ? sendCreactionRequest.data.details.payment_count : 0,
                    "paid_count": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.paid_count ? sendCreactionRequest.data.details.paid_count : 0,
                    "last_presented_at": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.last_presented_at ? new Date(sendCreactionRequest.data.details.last_presented_at) : null,
                    "last_presented_settlement_date": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.last_presented_settlement_date ? new Date(sendCreactionRequest.data.details.last_presented_settlement_date) : null,
                    "frequency": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.frequency ? sendCreactionRequest.data.details.frequency : "",
                    "present_settlement_before_days": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.present_settlement_before_days ? sendCreactionRequest.data.details.present_settlement_before_days : 0,
                    "narration": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.narration ? sendCreactionRequest.data.details.narration : "",
                    "amount_in_paise": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.amount_in_paise ? sendCreactionRequest.data.details.amount_in_paise : 0,
                    "ended_at": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.ended_at ? new Date(sendCreactionRequest.data.details.ended_at) : null,
                    "mandate_id": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.mandate_id ? sendCreactionRequest.data.details.mandate_id : "",
                    "client_ref_id": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.client_ref_id ? sendCreactionRequest.data.details.client_ref_id : "",
                },
                "created_at": sendCreactionRequest.data && sendCreactionRequest.data.created_at ? new Date(sendCreactionRequest.data.created_at) : null,
                "sponsor_bank_name": sendCreactionRequest.data && sendCreactionRequest.data.sponsor_bank_name ? sendCreactionRequest.data.sponsor_bank_name : "",
                "payment_status": "new",
                "settlementDoneOn": null,
                "failure_reason": null,
                "failure_description": null,
                "total_payment_count": null,
                "processed_payment_count": null,
                "lastEventCalled": null,
                "lastEventCalledAt": null,
                "userId": loan.userId,
                "loanId": loan._id,
                "partnerId": partnerId,
                "retryCount": 0,
                "sendingState": "Send",
                "unique_key": key,
                "insertedOn": Date.now(),
                "transaction_id": null,
                "emiAmount": emi.installmentAmount ? emi.installmentAmount : "",
                "penalty": installmentFees,
                "moratoriumInterest": emi.moratoriumInterest ? emi.moratoriumInterest : 0,
                "presentationFine": 0,
                "totalAmount": amount,
                "settlementDate": emi.dueDate,
                "debitResponseReceived": false,
                "emis": [emi.installmentNumber],
                "collectionType": "Single"
            }

            let saveCollection = new DigioCollectionSchedule(emiSaveData);

            await saveCollection.save();

            const emiRequest = new EmiRequest({
                userId: loan.userId,
                loanId: loan._id,
                refId: sendCreactionRequest.data && sendCreactionRequest.data.id ? sendCreactionRequest.data.id : "",
                emiNumber: emi.installmentNumber,
                totalAmount: amount,
                emiAmount: emi.installmentAmount,
                penalty: installmentFees,
                monotoriumInterest: emi.moratoriumInterest ? emi.moratoriumInterest : 0,
                insertedOn: Date.now()
            });

            await emiRequest.save();

            // PAYMENT COLLECTION
            let paymentCollection = {
                loan,
                installment: emi,
                fees,
                partnerResponse: sendCreactionRequest,
                uniqueKey: key,
                presentationFine: 0,
                collectionType: PAYMENT_COLLECTION_TYPE.SINGLE
            }

            let { error: paymentCollectionError, response: paymentCollectionResponse } = await handleInsertENachForPaymentCollection([paymentCollection])

            if (paymentCollectionError) {
                return { data: "", success: false, message: "PaymentCollection Insert Failed" };
            }

            return { data: "", success: true, message: "Collection saved" };

        }
        else {
            return sendCreactionRequest;
        }
    }
    catch (ex) {
        logger.logs(ex);
        saveErrorLog("sendScheduleRequest", apiPath, { loanId: loan._id, emi, partnerId }, { error: ex.message, stackTrace: ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex });

        let log = new DigioSchedulerLog({
            userId: loan.userId,
            loanId: loan._id,
            event: "error",
            error: ex.message,
            insertedOn: Date.now()
        });

        await log.save();

        return { data: ex.message, success: false, message: ex.message };

    }

}

async function sendUnsentScheduleRequest(loan, emi, partnerId, fees) {
    try {
        const schedule = await DigioCollectionSchedule.findOne({ loanId: loan._id, emis: emi.installmentNumber });

        if (schedule)
            return {
                data: "",
                success: true,
                message: "Schedule already exists"
            }

        const mandate = await DigioMandateGeneration.findOne({ loanIds: loan._id, isActive: true, status: "register_success" });

        if (!mandate)
            return {
                data: "",
                success: false,
                message: "No active mandate found for this loan"
            }

        let { key } = getUniqueKey(loan.loanNo);

        const today = new Date();

        let todayDate = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 5, 30, 0, 0);
        todayDate = dateAdd("day", 1, todayDate);

        let repaymentDate = moment(todayDate).format("yyyy-MM-DD");

        let installmentFees = 0;
        if (fees != null && fees.length > 0) {
            for (let i = 0; i < fees.length; i++) {
                installmentFees += fees[i].feeAmount;
            }
        }


        let amount = emi[0].installmentAmount + installmentFees + emi[0].moratoriumInterest;

        let emiModel = {
            "umrn": mandate.umrn ? mandate.umrn : "",
            "amount": amount,
            "settlement_date": repaymentDate,
            "corporate_account_number": config.get("digio.eNach.corporate_account_number"),
            "corporate_config_id": config.get("digio.eNach.corporate_config_id"),
            "destination_bank_id": mandate.mandateDetails && mandate.mandateDetails.mandate_data && mandate.mandateDetails.mandate_data.destination_bank_id ? mandate.mandateDetails.mandate_data.destination_bank_id : "",
            "customer_account_number": mandate.mandateDetails && mandate.mandateDetails.mandate_data && mandate.mandateDetails.mandate_data.customer_account_number ? mandate.mandateDetails.mandate_data.customer_account_number : "",
            "customer_name": mandate.mandateDetails && mandate.mandateDetails.mandate_data && mandate.mandateDetails.mandate_data.customer_name ? mandate.mandateDetails.mandate_data.customer_name : "",
            "frequency": mandate.mandateDetails && mandate.mandateDetails.mandate_data && mandate.mandateDetails.mandate_data.frequency ? mandate.mandateDetails.mandate_data.frequency : "",
            "payment_count": 1,
            "unique_key": key
        };

        let sendCreactionRequest = await scheduleCreation(emiModel, loan.userId, loan._id);

        if (sendCreactionRequest.success) {

            let emiSaveData = {
                "id": sendCreactionRequest.data && sendCreactionRequest.data.id ? sendCreactionRequest.data.id : "",
                "umrn": sendCreactionRequest.data && sendCreactionRequest.data.umrn ? sendCreactionRequest.data.umrn : "",
                "status": sendCreactionRequest.data && sendCreactionRequest.data.status ? sendCreactionRequest.data.status : "",
                "upcoming_transaction": {
                    "id": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.id ? sendCreactionRequest.data.upcoming_transaction.id : "",
                    "umrn": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.umrn ? sendCreactionRequest.data.upcoming_transaction.umrn : "",
                    "state": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.state ? sendCreactionRequest.data.upcoming_transaction.state : "",
                    "created_at": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.created_at ? new Date(sendCreactionRequest.data.upcoming_transaction.created_at) : null,
                    "present_at": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.present_at ? new Date(sendCreactionRequest.data.upcoming_transaction.present_at) : null,
                    "scheduled_settlement_date": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.scheduled_settlement_date ? new Date(sendCreactionRequest.data.upcoming_transaction.scheduled_settlement_date) : null,
                    "customer": {
                        "name": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.customer && sendCreactionRequest.data.upcoming_transaction.customer.name ? sendCreactionRequest.data.upcoming_transaction.customer.name : "",
                    },
                    "details": {
                        "status": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.details && sendCreactionRequest.data.upcoming_transaction.details.status ? sendCreactionRequest.data.upcoming_transaction.details.status : "",
                        "amount": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.details && sendCreactionRequest.data.upcoming_transaction.details.amount ? sendCreactionRequest.data.upcoming_transaction.details.amount : 0,
                        "amount_in_paise": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.details && sendCreactionRequest.data.upcoming_transaction.details.amount_in_paise ? sendCreactionRequest.data.upcoming_transaction.details.amount_in_paise : 0,
                        "sponsor_bank_id": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.details && sendCreactionRequest.data.upcoming_transaction.details.sponsor_bank_id ? sendCreactionRequest.data.upcoming_transaction.details.sponsor_bank_id : "",
                        "ready_to_process_at": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.details && sendCreactionRequest.data.upcoming_transaction.details.ready_to_process_at ? new Date(sendCreactionRequest.data.upcoming_transaction.details.ready_to_process_at) : null,
                        "type": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.details && sendCreactionRequest.data.upcoming_transaction.details.type ? sendCreactionRequest.data.upcoming_transaction.details.type : "",
                        "txn_ref": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.details && sendCreactionRequest.data.upcoming_transaction.details.txn_ref ? sendCreactionRequest.data.upcoming_transaction.details.txn_ref : "",
                    }
                },
                "mandate": {
                    "id": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.id ? sendCreactionRequest.data.mandate.id : "",
                    "mandate_id": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_id ? sendCreactionRequest.data.mandate.mandate_id : "",
                    "state": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.state ? sendCreactionRequest.data.mandate.state : "",
                    "type": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.type ? sendCreactionRequest.data.mandate.type : "",
                    "mandate_details": {
                        "file_name": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.file_name ? sendCreactionRequest.data.mandate.mandate_details.file_name : "",
                        "customer_identifier": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.customer_identifier ? sendCreactionRequest.data.mandate.mandate_details.customer_identifier : "",
                        "customer_name": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.customer_name ? sendCreactionRequest.data.mandate.mandate_details.customer_name : "",
                        "customer_ref_number": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.customer_ref_number ? sendCreactionRequest.data.mandate.mandate_details.customer_ref_number : "",
                        "scheme_ref_number": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.scheme_ref_number ? sendCreactionRequest.data.mandate.mandate_details.scheme_ref_number : "",
                        "customer_mobile": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.customer_mobile ? sendCreactionRequest.data.mandate.mandate_details.customer_mobile : "",
                        "auth_type": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.auth_type ? sendCreactionRequest.data.mandate.mandate_details.auth_type : "",
                        "authentication_time": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.authentication_time ? new Date(sendCreactionRequest.data.mandate.mandate_details.authentication_time) : null,
                        "is_recurring": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.is_recurring ? sendCreactionRequest.data.mandate.mandate_details.is_recurring : false,
                        "frequency": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.frequency ? sendCreactionRequest.data.mandate.mandate_details.frequency : "",
                        "first_collection_date": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.first_collection_date ? new Date(sendCreactionRequest.data.mandate.mandate_details.first_collection_date) : null,
                        "final_collection_date": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.final_collection_date ? new Date(sendCreactionRequest.data.mandate.mandate_details.final_collection_date) : null,
                        "maximum_amount": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.maximum_amount ? sendCreactionRequest.data.mandate.mandate_details.maximum_amount : 0,
                        "customer_account_number": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.customer_account_number ? sendCreactionRequest.data.mandate.mandate_details.customer_account_number : "",
                        "customer_account_type": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.customer_account_type ? sendCreactionRequest.data.mandate.mandate_details.customer_account_type : "",
                        "destination_bank_id": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.destination_bank_id ? sendCreactionRequest.data.mandate.mandate_details.destination_bank_id : "",
                        "destination_bank_name": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.destination_bank_name ? sendCreactionRequest.data.mandate.mandate_details.destination_bank_name : "",
                        "sponsor_bank_name": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.sponsor_bank_name ? sendCreactionRequest.data.mandate.mandate_details.sponsor_bank_name : "",
                        "npci_txn_id": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.npci_txn_id ? sendCreactionRequest.data.mandate.mandate_details.npci_txn_id : "",
                        "account_validation": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.account_validation ? sendCreactionRequest.data.mandate.mandate_details.account_validation : "",
                        "block_fund": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.block_fund ? sendCreactionRequest.data.mandate.mandate_details.block_fund : false,
                        "revocable": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.revocable ? sendCreactionRequest.data.mandate.mandate_details.revocable : false,
                    },
                    "umrn": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.umrn ? sendCreactionRequest.data.mandate.umrn : "",
                    "created_at": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.created_at ? new Date(sendCreactionRequest.data.mandate.created_at) : null,
                    "updated_at": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.updated_at ? new Date(sendCreactionRequest.data.mandate.updated_at) : null,
                    "mode": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mode ? sendCreactionRequest.data.mandate.mode : "",
                    "auth_sub_mode": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.auth_sub_mode ? sendCreactionRequest.data.mandate.auth_sub_mode : "",
                },
                "customer": {
                    "name": sendCreactionRequest.data && sendCreactionRequest.data.customer && sendCreactionRequest.data.customer.name ? sendCreactionRequest.data.customer.name : "",
                    "destination_account_type": sendCreactionRequest.data && sendCreactionRequest.data.customer && sendCreactionRequest.data.customer.destination_account_type ? sendCreactionRequest.data.customer.destination_account_type : "",
                    "destination_ifsc": sendCreactionRequest.data && sendCreactionRequest.data.customer && sendCreactionRequest.data.customer.destination_ifsc ? sendCreactionRequest.data.customer.destination_ifsc : "",
                    "user_account_number": sendCreactionRequest.data && sendCreactionRequest.data.customer && sendCreactionRequest.data.customer.user_account_number ? sendCreactionRequest.data.customer.user_account_number : "",
                },
                "corporate": {
                    "name": sendCreactionRequest.data && sendCreactionRequest.data.corporate && sendCreactionRequest.data.corporate.name ? sendCreactionRequest.data.corporate.name : "",
                    "utility_code": sendCreactionRequest.data && sendCreactionRequest.data.corporate && sendCreactionRequest.data.corporate.utility_code ? sendCreactionRequest.data.corporate.utility_code : "",
                    "corporate_reference": sendCreactionRequest.data && sendCreactionRequest.data.corporate && sendCreactionRequest.data.corporate.corporate_reference ? sendCreactionRequest.data.corporate.corporate_reference : "",
                    "sponsor_ifsc": sendCreactionRequest.data && sendCreactionRequest.data.corporate && sendCreactionRequest.data.corporate.sponsor_ifsc ? sendCreactionRequest.data.corporate.sponsor_ifsc : "",
                    "sponsor_bank_code": sendCreactionRequest.data && sendCreactionRequest.data.corporate && sendCreactionRequest.data.corporate.sponsor_bank_code ? sendCreactionRequest.data.corporate.sponsor_bank_code : "",
                    "corporate_account_number": sendCreactionRequest.data && sendCreactionRequest.data.corporate && sendCreactionRequest.data.corporate.corporate_account_number ? sendCreactionRequest.data.corporate.corporate_account_number : "",
                },
                "partner": {
                    "name": sendCreactionRequest.data && sendCreactionRequest.data.partner && sendCreactionRequest.data.partner.name ? sendCreactionRequest.data.partner.name : "",
                    "identifier": sendCreactionRequest.data && sendCreactionRequest.data.partner && sendCreactionRequest.data.partner.identifier ? sendCreactionRequest.data.partner.identifier : "",
                },
                "details": {
                    "first_proposed_settlement_date": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.first_proposed_settlement_date ? new Date(sendCreactionRequest.data.details.first_proposed_settlement_date) : null,
                    "payment_count": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.payment_count ? sendCreactionRequest.data.details.payment_count : 0,
                    "paid_count": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.paid_count ? sendCreactionRequest.data.details.paid_count : 0,
                    "last_presented_at": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.last_presented_at ? new Date(sendCreactionRequest.data.details.last_presented_at) : null,
                    "last_presented_settlement_date": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.last_presented_settlement_date ? new Date(sendCreactionRequest.data.details.last_presented_settlement_date) : null,
                    "frequency": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.frequency ? sendCreactionRequest.data.details.frequency : "",
                    "present_settlement_before_days": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.present_settlement_before_days ? sendCreactionRequest.data.details.present_settlement_before_days : 0,
                    "narration": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.narration ? sendCreactionRequest.data.details.narration : "",
                    "amount_in_paise": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.amount_in_paise ? sendCreactionRequest.data.details.amount_in_paise : 0,
                    "ended_at": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.ended_at ? new Date(sendCreactionRequest.data.details.ended_at) : null,
                    "mandate_id": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.mandate_id ? sendCreactionRequest.data.details.mandate_id : "",
                    "client_ref_id": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.client_ref_id ? sendCreactionRequest.data.details.client_ref_id : "",
                },
                "created_at": sendCreactionRequest.data && sendCreactionRequest.data.created_at ? new Date(sendCreactionRequest.data.created_at) : null,
                "sponsor_bank_name": sendCreactionRequest.data && sendCreactionRequest.data.sponsor_bank_name ? sendCreactionRequest.data.sponsor_bank_name : "",
                "payment_status": "new",
                "settlementDoneOn": null,
                "failure_reason": null,
                "failure_description": null,
                "total_payment_count": null,
                "processed_payment_count": null,
                "lastEventCalled": null,
                "lastEventCalledAt": null,
                "userId": loan.userId,
                "loanId": loan._id,
                "partnerId": partnerId,
                "retryCount": 0,
                "sendingState": "Send",
                "unique_key": key,
                "insertedOn": Date.now(),
                "transaction_id": null,
                "emiAmount": emi[0].installmentAmount ? emi[0].installmentAmount : "",
                "penalty": installmentFees,
                "moratoriumInterest": emi[0].moratoriumInterest,
                "presentationFine": 0,
                "totalAmount": amount,
                "settlementDate": todayDate,
                "debitResponseReceived": false,
                "emis": [emi[0].installmentNumber],
                "collectionType": "Single"
            }

            let saveCollection = new DigioCollectionSchedule(emiSaveData);

            await saveCollection.save();

            //PAYMENT COLLECTION
            let paymentCollection = {
                loan,
                installment: emi,
                fees,
                partnerResponse: sendCreactionRequest,
                uniqueKey: key,
                presentationFine: 0,
                collectionType: PAYMENT_COLLECTION_TYPE.SINGLE
            }

            let { error: paymentCollectionError, response: paymentCollectionResponse } = await handleInsertENachForPaymentCollection([paymentCollection])

            if (paymentCollectionError) {
                return { data: "", success: false, message: "Payment collection create failed" };
            }

            const emiRequest = new EmiRequest({
                userId: loan.userId,
                loanId: loan._id,
                refId: sendCreactionRequest.data && sendCreactionRequest.data.id ? sendCreactionRequest.data.id : "",
                emiNumber: emi[0].installmentNumber,
                totalAmount: amount,
                emiAmount: emi[0].installmentAmount,
                penalty: installmentFees,
                monotoriumInterest: emi[0].moratoriumInterest ? emi[0].moratoriumInterest : 0,
                insertedOn: Date.now()
            });

            await emiRequest.save();

            return { data: "", success: true, message: "Collection saved" };
        }
        else {
            return sendCreactionRequest;
        }
    }
    catch (ex) {
        saveErrorLog("sendUnsentScheduleRequest", apiPath, { loanId: loan._id, emi, partnerId }, { error: ex.message, stackTrace: ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex });

        let log = new DigioSchedulerLog({
            userId: loan.userId,
            loanId: loan._id,
            event: "error",
            error: ex.message,
            insertedOn: Date.now()
        });

        await log.save();

        return { data: ex.message, success: false, message: ex.message };

    }

}

module.exports.scheduleFailedRepayments = async function () {
    try {
        const failedList = await DigioCollectionSchedule.find({ sendingState: "Send", debitResponseReceived: true, payment_status: { $ne: "payment_success" } });

        for (const item of failedList) {

            if (item.failure_reason == "04" || item.failure_reason == "06" || item.failure_reason == "57" || item.failure_reason == "58" || item.failure_reason == "59") {
                const penaltyResponse = await installmentService.applyLateFee(item.partnerId, null, item.loanId, true);

                if (penaltyResponse.success) {
                    await reSendFailedCollection(item.id);
                } else {
                    saveErrorLog("scheduleFailedRepayments", apiPath, item, { message: "apply penalty failure" });
                }
            } else {
                item.sendingState = "InActive";
                item.systemErrorMessage = "InActive due to failure reason";
                await item.save();

                //PAYMENT COLLECTION
                let { error: paymentCollectionUpdateError, response: paymentCollectionUpdateRes } = await updatePaymentCollection({ query: { referenceNo: item.unique_key }, update: { sendingState: PAYMENT_SENDING_STATE.IN_ACTIVE } })

                if (paymentCollectionUpdateError) {
                    return {
                        data: "",
                        success: false,
                        message: "Payment collection update failed",
                        status: 200,
                    };
                }

            }
        }

        return {
            data: "",
            success: true,
            message: "Failed cases scheduled",
            status: 200,
        };
    }
    catch (ex) {
        saveErrorLog("scheduleFailedRepayments", apiPath, {}, { error: ex.message, stackTrace: ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex });

        let errorModel = new DigioSchedulerLog({
            userId: null,
            loanId: null,
            event: "error",
            error: ex.message,
            insertedOn: Date.now()
        });

        await errorModel.save();

        let endModel = new DigioSchedulerLog({
            userId: null,
            loanId: null,
            event: "info",
            error: "Schedular ended",
            insertedOn: Date.now()
        });

        await endModel.save();

        return {
            data: "",
            success: false,
            message: ex.message,
            status: 400,
        };
    }


}

async function reSendFailedCollection(id) {
    const schedule = await DigioCollectionSchedule.findOne({ id: id });

    const loan = await LoanInfo.findById(schedule.loanId);

    if (!loan)
        return {
            data: "",
            success: false,
            message: "Loan not found",
            status: 200
        }

    try {
        if (!schedule)
            return {
                data: "",
                success: false,
                message: "No active schedule found"
            }

        const mandate = await DigioMandateGeneration.findOne({ loanIds: loan._id, isActive: true, status: "register_success" });

        if (!mandate)
            return {
                data: "",
                success: false,
                message: "No active mandate found for this loan"
            }

        const emiRequest = await EmiRequest.find({ refId: id });

        const resentCheck = await DigioCollectionSchedule.find({ loanId: schedule.loanId, sendingState: "Send", id: { $ne: id } });

        //PAYMENT COLLECTION
        let paymentCollectionUpdateObj = {}
        let paymentCollectionQuery = {
            referenceNo: schedule.unique_key
        }

        //Check if collection is already scheduled for any of the emis
        for (const emi of emiRequest) {

            for (const sentItem of resentCheck) {

                const emiRequest2 = await EmiRequest.find({ refId: sentItem.id });

                for (const emi2 of emiRequest2) {

                    if (emi2.emiNumber === emi.emiNumber) {
                        schedule.sendingState = "InActive";
                        schedule.systemErrorMessage = "Collection already scheduled for this emi";
                        await schedule.save();

                        //PAYMENT COLLECTION
                        paymentCollectionUpdateObj.sendingState = PAYMENT_SENDING_STATE.IN_ACTIVE;
                        let { error: paymentCollectionError, response: paymentCollectionRes } = await updatePaymentCollection({ query: paymentCollectionQuery, update: paymentCollectionUpdateObj });

                        if (paymentCollectionError) {
                            return {
                                data: "",
                                success: false,
                                message: "Payment collection update Failed"
                            }
                        }

                        return {
                            data: "",
                            success: false,
                            message: "Collection already scheduled for this emi"
                        }
                    }

                }
            }
        }

        let maxRetryCount = config.get("digio.eNach.maxRetryCount");

        if (schedule.retryCount >= maxRetryCount) {
            schedule.sendingState = "InActive";
            schedule.systemErrorMessage = "Re-try limit reached";
            await schedule.save();

            //PAYMENT COLLECTION
            paymentCollectionUpdateObj.sendingState = PAYMENT_SENDING_STATE.IN_ACTIVE
            let { error: paymentCollectionError, response: paymentCollectionRes } = await updatePaymentCollection({ query: paymentCollectionQuery, update: paymentCollectionUpdateObj });

            if (paymentCollectionError) {
                return {
                    data: "",
                    success: false,
                    message: "Payment collection update Failed"
                }
            }


            return {
                data: "",
                success: false,
                message: "Re-try limit reached"
            }
        }

        let installmentAmount = 0;
        let penalty = 0;
        let moratoriumInterest = 0;
        let totalCollectionAmount = 0;
        let emiRequestList = [];


        const installmentDetails = await installmentService.getInstallments(loan._id);
        if (!installmentDetails.success) {
            return {
                data: "",
                success: false,
                message: installmentDetails.message,
                status: 200
            }
        }

        const emiFees = await installmentService.getLoanFees(loan._id);
        if (!emiFees.success) {
            return {
                data: "",
                success: false,
                message: emiFees.message,
                status: 200
            }
        }

        for (const emi of emiRequest) {
            const installment = installmentDetails.data.filter((x) => x.installmentNumber === emi.emiNumber);
            const fees = emiFees.data.filter((x) => x.installmentNumber === emi.emiNumber);
            if (installment && installment[0]) {
                if (!installment || installment.length === 0) {
                    schedule.sendingState = "InActive";
                    schedule.systemErrorMessage = "EMI does not exist in loan Info";
                    await schedule.save();

                    //PAYMENT COLLECTION
                    paymentCollectionUpdateObj.sendingState = PAYMENT_SENDING_STATE.IN_ACTIVE
                    let { error: paymentCollectionError, response: paymentCollectionRes } = await updatePaymentCollection({ query: paymentCollectionQuery, update: paymentCollectionUpdateObj });

                    if (paymentCollectionError) {
                        return {
                            data: "",
                            success: false,
                            message: "Payment collection update Failed"
                        }
                    }

                    return {
                        data: "",
                        success: false,
                        message: "EMI does not exist in loan Info"
                    }
                }


                if (installment[0].status.toLowerCase() !== "pending") {
                    schedule.sendingState = "InActive";
                    schedule.systemErrorMessage = "EMI is not in pending state";
                    await schedule.save();

                    //PAYMENT COLLECTION
                    paymentCollectionUpdateObj.sendingState = PAYMENT_SENDING_STATE.IN_ACTIVE
                    let { error: paymentCollectionError, response: paymentCollectionRes } = await updatePaymentCollection({ query: paymentCollectionQuery, update: paymentCollectionUpdateObj });

                    if (paymentCollectionError) {
                        return {
                            data: "",
                            success: false,
                            message: "Payment collection update Failed"
                        }
                    }

                    return {
                        data: "",
                        success: false,
                        message: "EMI is not in pending state"
                    }
                }

                let installmentFees = 0;
                if (fees != null && fees.length > 0) {
                    for (let i = 0; i < fees.length; i++) {
                        penalty += fees[i].feeAmount;
                        installmentFees += fees[i].feeAmount;
                    }
                }

                installmentAmount += installment[0].installmentAmount;
                moratoriumInterest += installment[0].moratoriumInterest;

                const totalAmount = installment[0].installmentAmount + installmentFees + installment[0].moratoriumInterest;

                totalCollectionAmount += totalAmount;

                const emiRequest = {
                    "userId": loan.userId,
                    "loanId": loan._id,
                    "refId": "",
                    "emiNumber": emi.installmentNumber,
                    "totalAmount": totalAmount,
                    "emiAmount": installment[0].installmentAmount ? installment[0].installmentAmount : 0,
                    "penalty": installment[0].penalty ? installment[0].penalty : 0,
                    "monotoriumInterest": installment[0].moratoriumInterest ? installment[0].moratoriumInterest : 0,
                    "insertedOn": Date.now()
                };

                emiRequestList.push(emiRequest);
            }
        }

        let { key } = getUniqueKey(loan.loanNo);

        let retryCount = schedule.retryCount + 1;

        // let presentationFine = 0;

        // if (schedule.failure_reason == "04" || schedule.failure_reason == "06" || schedule.failure_reason == "57" || schedule.failure_reason == "58")
        //     presentationFine = config.get("digio.eNach.presentationFine") * retryCount;

        const today = new Date();

        const todayDate = new Date(
            today.getFullYear(),
            today.getMonth(),
            today.getDate(), 5, 30, 0, 0
        );

        let oldSettlementDate = dateAdd("day", config.get("digio.eNach.dayDelay"), todayDate);

        let repaymentDate = moment(oldSettlementDate).format("yyyy-MM-DD");

        let emiModel = {
            "umrn": mandate.umrn ? mandate.umrn : "",
            "amount": totalCollectionAmount,
            "settlement_date": repaymentDate,
            "corporate_account_number": config.get("digio.eNach.corporate_account_number"),
            "corporate_config_id": config.get("digio.eNach.corporate_config_id"),
            "destination_bank_id": mandate.mandateDetails && mandate.mandateDetails.mandate_data && mandate.mandateDetails.mandate_data.destination_bank_id ? mandate.mandateDetails.mandate_data.destination_bank_id : "",
            "customer_account_number": mandate.mandateDetails && mandate.mandateDetails.mandate_data && mandate.mandateDetails.mandate_data.customer_account_number ? mandate.mandateDetails.mandate_data.customer_account_number : "",
            "customer_name": mandate.mandateDetails && mandate.mandateDetails.mandate_data && mandate.mandateDetails.mandate_data.customer_name ? mandate.mandateDetails.mandate_data.customer_name : "",
            "frequency": mandate.mandateDetails && mandate.mandateDetails.mandate_data && mandate.mandateDetails.mandate_data.frequency ? mandate.mandateDetails.mandate_data.frequency : "",
            "payment_count": 1,
            "unique_key": key
        };

        let sendCreactionRequest = await scheduleCreation(emiModel, loan.userId, loan._id);

        if (sendCreactionRequest.success) {

            let emiSaveData = {
                "id": sendCreactionRequest.data && sendCreactionRequest.data.id ? sendCreactionRequest.data.id : "",
                "umrn": sendCreactionRequest.data && sendCreactionRequest.data.umrn ? sendCreactionRequest.data.umrn : "",
                "status": sendCreactionRequest.data && sendCreactionRequest.data.status ? sendCreactionRequest.data.status : "",
                "upcoming_transaction": {
                    "id": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.id ? sendCreactionRequest.data.upcoming_transaction.id : "",
                    "umrn": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.umrn ? sendCreactionRequest.data.upcoming_transaction.umrn : "",
                    "state": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.state ? sendCreactionRequest.data.upcoming_transaction.state : "",
                    "created_at": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.created_at ? new Date(sendCreactionRequest.data.upcoming_transaction.created_at) : null,
                    "present_at": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.present_at ? new Date(sendCreactionRequest.data.upcoming_transaction.present_at) : null,
                    "scheduled_settlement_date": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.scheduled_settlement_date ? new Date(sendCreactionRequest.data.upcoming_transaction.scheduled_settlement_date) : null,
                    "customer": {
                        "name": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.customer && sendCreactionRequest.data.upcoming_transaction.customer.name ? sendCreactionRequest.data.upcoming_transaction.customer.name : "",
                    },
                    "details": {
                        "status": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.details && sendCreactionRequest.data.upcoming_transaction.details.status ? sendCreactionRequest.data.upcoming_transaction.details.status : "",
                        "amount": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.details && sendCreactionRequest.data.upcoming_transaction.details.amount ? sendCreactionRequest.data.upcoming_transaction.details.amount : 0,
                        "amount_in_paise": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.details && sendCreactionRequest.data.upcoming_transaction.details.amount_in_paise ? sendCreactionRequest.data.upcoming_transaction.details.amount_in_paise : 0,
                        "sponsor_bank_id": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.details && sendCreactionRequest.data.upcoming_transaction.details.sponsor_bank_id ? sendCreactionRequest.data.upcoming_transaction.details.sponsor_bank_id : "",
                        "ready_to_process_at": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.details && sendCreactionRequest.data.upcoming_transaction.details.ready_to_process_at ? new Date(sendCreactionRequest.data.upcoming_transaction.details.ready_to_process_at) : null,
                        "type": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.details && sendCreactionRequest.data.upcoming_transaction.details.type ? sendCreactionRequest.data.upcoming_transaction.details.type : "",
                        "txn_ref": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.details && sendCreactionRequest.data.upcoming_transaction.details.txn_ref ? sendCreactionRequest.data.upcoming_transaction.details.txn_ref : "",
                    }
                },
                "mandate": {
                    "id": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.id ? sendCreactionRequest.data.mandate.id : "",
                    "mandate_id": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_id ? sendCreactionRequest.data.mandate.mandate_id : "",
                    "state": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.state ? sendCreactionRequest.data.mandate.state : "",
                    "type": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.type ? sendCreactionRequest.data.mandate.type : "",
                    "mandate_details": {
                        "file_name": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.file_name ? sendCreactionRequest.data.mandate.mandate_details.file_name : "",
                        "customer_identifier": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.customer_identifier ? sendCreactionRequest.data.mandate.mandate_details.customer_identifier : "",
                        "customer_name": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.customer_name ? sendCreactionRequest.data.mandate.mandate_details.customer_name : "",
                        "customer_ref_number": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.customer_ref_number ? sendCreactionRequest.data.mandate.mandate_details.customer_ref_number : "",
                        "scheme_ref_number": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.scheme_ref_number ? sendCreactionRequest.data.mandate.mandate_details.scheme_ref_number : "",
                        "customer_mobile": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.customer_mobile ? sendCreactionRequest.data.mandate.mandate_details.customer_mobile : "",
                        "auth_type": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.auth_type ? sendCreactionRequest.data.mandate.mandate_details.auth_type : "",
                        "authentication_time": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.authentication_time ? new Date(sendCreactionRequest.data.mandate.mandate_details.authentication_time) : null,
                        "is_recurring": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.is_recurring ? sendCreactionRequest.data.mandate.mandate_details.is_recurring : false,
                        "frequency": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.frequency ? sendCreactionRequest.data.mandate.mandate_details.frequency : "",
                        "first_collection_date": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.first_collection_date ? new Date(sendCreactionRequest.data.mandate.mandate_details.first_collection_date) : null,
                        "final_collection_date": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.final_collection_date ? new Date(sendCreactionRequest.data.mandate.mandate_details.final_collection_date) : null,
                        "maximum_amount": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.maximum_amount ? sendCreactionRequest.data.mandate.mandate_details.maximum_amount : 0,
                        "customer_account_number": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.customer_account_number ? sendCreactionRequest.data.mandate.mandate_details.customer_account_number : "",
                        "customer_account_type": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.customer_account_type ? sendCreactionRequest.data.mandate.mandate_details.customer_account_type : "",
                        "destination_bank_id": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.destination_bank_id ? sendCreactionRequest.data.mandate.mandate_details.destination_bank_id : "",
                        "destination_bank_name": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.destination_bank_name ? sendCreactionRequest.data.mandate.mandate_details.destination_bank_name : "",
                        "sponsor_bank_name": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.sponsor_bank_name ? sendCreactionRequest.data.mandate.mandate_details.sponsor_bank_name : "",
                        "npci_txn_id": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.npci_txn_id ? sendCreactionRequest.data.mandate.mandate_details.npci_txn_id : "",
                        "account_validation": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.account_validation ? sendCreactionRequest.data.mandate.mandate_details.account_validation : "",
                        "block_fund": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.block_fund ? sendCreactionRequest.data.mandate.mandate_details.block_fund : false,
                        "revocable": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.revocable ? sendCreactionRequest.data.mandate.mandate_details.revocable : false,
                    },
                    "umrn": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.umrn ? sendCreactionRequest.data.mandate.umrn : "",
                    "created_at": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.created_at ? new Date(sendCreactionRequest.data.mandate.created_at) : null,
                    "updated_at": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.updated_at ? new Date(sendCreactionRequest.data.mandate.updated_at) : null,
                    "mode": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mode ? sendCreactionRequest.data.mandate.mode : "",
                    "auth_sub_mode": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.auth_sub_mode ? sendCreactionRequest.data.mandate.auth_sub_mode : "",
                },
                "customer": {
                    "name": sendCreactionRequest.data && sendCreactionRequest.data.customer && sendCreactionRequest.data.customer.name ? sendCreactionRequest.data.customer.name : "",
                    "destination_account_type": sendCreactionRequest.data && sendCreactionRequest.data.customer && sendCreactionRequest.data.customer.destination_account_type ? sendCreactionRequest.data.customer.destination_account_type : "",
                    "destination_ifsc": sendCreactionRequest.data && sendCreactionRequest.data.customer && sendCreactionRequest.data.customer.destination_ifsc ? sendCreactionRequest.data.customer.destination_ifsc : "",
                    "user_account_number": sendCreactionRequest.data && sendCreactionRequest.data.customer && sendCreactionRequest.data.customer.user_account_number ? sendCreactionRequest.data.customer.user_account_number : "",
                },
                "corporate": {
                    "name": sendCreactionRequest.data && sendCreactionRequest.data.corporate && sendCreactionRequest.data.corporate.name ? sendCreactionRequest.data.corporate.name : "",
                    "utility_code": sendCreactionRequest.data && sendCreactionRequest.data.corporate && sendCreactionRequest.data.corporate.utility_code ? sendCreactionRequest.data.corporate.utility_code : "",
                    "corporate_reference": sendCreactionRequest.data && sendCreactionRequest.data.corporate && sendCreactionRequest.data.corporate.corporate_reference ? sendCreactionRequest.data.corporate.corporate_reference : "",
                    "sponsor_ifsc": sendCreactionRequest.data && sendCreactionRequest.data.corporate && sendCreactionRequest.data.corporate.sponsor_ifsc ? sendCreactionRequest.data.corporate.sponsor_ifsc : "",
                    "sponsor_bank_code": sendCreactionRequest.data && sendCreactionRequest.data.corporate && sendCreactionRequest.data.corporate.sponsor_bank_code ? sendCreactionRequest.data.corporate.sponsor_bank_code : "",
                    "corporate_account_number": sendCreactionRequest.data && sendCreactionRequest.data.corporate && sendCreactionRequest.data.corporate.corporate_account_number ? sendCreactionRequest.data.corporate.corporate_account_number : "",
                },
                "partner": {
                    "name": sendCreactionRequest.data && sendCreactionRequest.data.partner && sendCreactionRequest.data.partner.name ? sendCreactionRequest.data.partner.name : "",
                    "identifier": sendCreactionRequest.data && sendCreactionRequest.data.partner && sendCreactionRequest.data.partner.identifier ? sendCreactionRequest.data.partner.identifier : "",
                },
                "details": {
                    "first_proposed_settlement_date": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.first_proposed_settlement_date ? new Date(sendCreactionRequest.data.details.first_proposed_settlement_date) : null,
                    "payment_count": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.payment_count ? sendCreactionRequest.data.details.payment_count : 0,
                    "paid_count": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.paid_count ? sendCreactionRequest.data.details.paid_count : 0,
                    "last_presented_at": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.last_presented_at ? new Date(sendCreactionRequest.data.details.last_presented_at) : null,
                    "last_presented_settlement_date": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.last_presented_settlement_date ? new Date(sendCreactionRequest.data.details.last_presented_settlement_date) : null,
                    "frequency": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.frequency ? sendCreactionRequest.data.details.frequency : "",
                    "present_settlement_before_days": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.present_settlement_before_days ? sendCreactionRequest.data.details.present_settlement_before_days : 0,
                    "narration": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.narration ? sendCreactionRequest.data.details.narration : "",
                    "amount_in_paise": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.amount_in_paise ? sendCreactionRequest.data.details.amount_in_paise : 0,
                    "ended_at": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.ended_at ? new Date(sendCreactionRequest.data.details.ended_at) : null,
                    "mandate_id": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.mandate_id ? sendCreactionRequest.data.details.mandate_id : "",
                    "client_ref_id": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.client_ref_id ? sendCreactionRequest.data.details.client_ref_id : "",
                },
                "created_at": sendCreactionRequest.data && sendCreactionRequest.data.created_at ? new Date(sendCreactionRequest.data.created_at) : null,
                "sponsor_bank_name": sendCreactionRequest.data && sendCreactionRequest.data.sponsor_bank_name ? sendCreactionRequest.data.sponsor_bank_name : "",
                "payment_status": "new",
                "settlementDoneOn": null,
                "failure_reason": null,
                "failure_description": null,
                "total_payment_count": null,
                "processed_payment_count": null,
                "lastEventCalled": null,
                "lastEventCalledAt": null,
                "userId": schedule.userId,
                "loanId": schedule.loanId,
                "partnerId": schedule.partnerId,
                "retryCount": retryCount,
                "sendingState": "Send",
                "unique_key": key,
                "insertedOn": Date.now(),
                "transaction_id": null,
                "emiAmount": installmentAmount,
                "penalty": penalty,
                "moratoriumInterest": moratoriumInterest,
                "presentationFine": 0,
                "totalAmount": totalCollectionAmount,
                "settlementDate": oldSettlementDate,
                "debitResponseReceived": false,
                "emis": schedule.emis,
                "collectionType": schedule.collectionType
            }

            let saveCollection = new DigioCollectionSchedule(emiSaveData);

            await saveCollection.save();

            //PAYMENT COLLECTION
            let paymentCollection = {
                loan,
                installment: emi,
                fees,
                partnerResponse: sendCreactionRequest,
                uniqueKey: key,
                presentationFine: 0,
                collectionType: PAYMENT_COLLECTION.SINGLE
            }

            let { error: paymentCollectionError, response: paymentCollectionResponse } = await handleInsertENachForPaymentCollection([paymentCollection])

            if (paymentCollectionError) {
                return { data: "", success: false, message: "Payment collection creation failed" };
            }

            for (const emiItem of emiRequestList) {

                emiItem.refId = sendCreactionRequest.data && sendCreactionRequest.data.id ? sendCreactionRequest.data.id : "";

                const emiRequest = new EmiRequest(emiItem);

                await emiRequest.save();

            }

            schedule.sendingState = "InActive";
            schedule.systemErrorMessage = "New collection request is scheduled";

            await schedule.save();

            //PAYMENT COLLECTION
            let { error: paymentCollectionUpdateError, response: paymentCollectionUpdateRes } = await updatePaymentCollection({ query: { referenceNo: schedule.unique_key }, update: { sendingState: PAYMENT.SENDING_STATE.IN_ACTIVE } })

            if (paymentCollectionUpdateError) {
                return { data: "", success: false, message: "Payment Collection update failed" };
            }

            return { data: "", success: true, message: "Collection saved" };

        }
        else {
            return sendCreactionRequest;
        }
    }
    catch (ex) {
        saveErrorLog("reSendFailedCollection", apiPath, { id: id }, { error: ex.message, stackTrace: ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex });

        let log = new DigioSchedulerLog({
            userId: loan.userId,
            loanId: loan._id,
            event: "error",
            error: ex.message,
            insertedOn: Date.now()
        });

        await log.save();

        return { data: ex.message, success: false, message: ex.message };

    }

}

async function getDebitResponse(modal) {

    let { isTrigerByAWS } = modal;

    let startModel = new DigioSchedulerLog({
        userId: null,
        loanId: null,
        event: "info",
        error: "Schedular started",
        insertedOn: Date.now()
    });

    await startModel.save();

    let query = {};

    query["settlementDate"] = { $lt: Date.now() };

    query["debitResponseReceived"] = false;

    const schedule = await DigioCollectionSchedule.find(query);

    for (const item of schedule)
        await updateDebitResponse(item.id, isTrigerByAWS);

    return { data: "Update completed", success: true, message: "" };

}

module.exports.getDebitResponse = getDebitResponse;

async function updateDebitResponse(nspId, isTrigerByAWS = false) {
    try {
        let schedule = await DigioCollectionSchedule.findOne({ id: nspId });

        const url = `${config.get("digio.eNach.getDebitStatus")}${nspId}`;

        const response = await callDigioApi("GET", url, {});

        let oldStatus = schedule.payment_status;

        let maxRetryCount = config.get("digio.eNach.maxRetryCount");

        schedule.status = response && response.status ? response.status : "";
        schedule.lastEventCalled = "getDebitResponse";
        schedule.payment_status = response && response.last_transaction && response.last_transaction.details && response.last_transaction.details.status ? response.last_transaction.details.status : "";
        schedule.failure_reason = response && response.last_transaction && response.last_transaction.details && response.last_transaction.details.failure_code ? response.last_transaction.details.failure_code : "";
        schedule.failure_description = response && response.last_transaction && response.last_transaction.details && response.last_transaction.details.failure_reason ? response.last_transaction.details.failure_reason : "";
        schedule.lastEventCalledAt = Date.now();
        schedule.transaction_id = response && response.last_transaction && response.last_transaction.details && response.last_transaction.id ? response.last_transaction.id : "";

        let settlementDate = response && response.details && response.details.last_presented_settlement_date ? new Date(response.details.last_presented_settlement_date) : null;

        let ffUniqueId = response && response.details.client_ref_id;

        // PAYMENT COLLECTION
        let paymentCollectionUpdateObj = {
            lastEventCalled: "getDebitResponse",
            paymentStatus: schedule.payment_status
        };

        if (schedule.payment_status.toLowerCase() == "payment_success") {
            schedule.settlementDoneOn = settlementDate;
            schedule.sendingState = "Paid";
            schedule.paidOn = settlementDate;

            // PAYMENT COLLECTION
            paymentCollectionUpdateObj.sendingState = PAYMENT_SENDING_STATE.PAID
        }
        else {
            if (schedule.retryCount >= maxRetryCount) {
                schedule.sendingState = "InActive";
                schedule.systemErrorMessage = "Retry count exceeded";

                // PAYMENT COLLECTION
                paymentCollectionUpdateObj.sendingState = PAYMENT_SENDING_STATE.IN_ACTIVE
            }
            else
                schedule.sendingState = "Send";
        }

        if (schedule.payment_status.toLowerCase() == "payment_success" || schedule.payment_status.toLowerCase() == "payment_failed")
            schedule.debitResponseReceived = true;


        await schedule.save();

        //PAYMENT COLLECTION

        if (ffUniqueId) {
            let { error: paymentCollectionUpdateError, response: paymentCollectionUpdateResponse } = await updatePaymentCollection({ query: { referenceNo: ffUniqueId }, update: paymentCollectionUpdateObj })

            if (paymentCollectionUpdateError) {
                return {
                    data: "",
                    success: false,
                    message: `Payment Collection Update Error`
                }
            }
        } else {
            //Need to send mail
            saveErrorLog("updateDebitResponse", apiPath, {}, { error: "Payment Collection update Failed - Missing ffUniqueId" });
            return {
                data: "",
                success: false,
                message: `Payment Collection update Failed - Missing ffUniqueId`
            }

        }

        if (schedule.payment_status.toLowerCase() == "payment_success" && oldStatus.toLowerCase() != "payment_success") {

            const emiRequest = await EmiRequest.find({ refId: nspId });

            for (const emi of emiRequest)
                await installmentService.updateEmiPaidStatus(loanId, emi.emiNumber, emi.totalAmount, settlementDate, "Enach API", "System", ffUniqueId);

            return {
                data: "",
                success: true,
                message: "Saved successfully"
            }

        }
        else if (schedule.failure_reason && schedule.failure_description) {
            let mailModel = getMailModel();

            let loan = await LoanInfo.findById(schedule.loanId);

            let emiList = "";

            for (const emiNo of schedule.emis)
                emiList += emiNo + ", ";

            if (emiList)
                emiList = emiList.substring(0, emiList.length - 2);

            mailModel.number = loan.loanNo;
            mailModel.number1 = emiList ? emiList.toString() : "";
            mailModel.date = moment(settlementDate).format("DD/MM/yyyy");
            mailModel.rejectCode = schedule.failure_reason;
            mailModel.rejectionreason = schedule.failure_description;
            mailModel.event = schedule.lastEventCalled;

            //Send Email to Admin
            if (isTrigerByAWS) {
                if (loan.status !== 'Written-Off') {
                    await sendMail("", schedule.loanId, "Loan Application Emails to Admin", "Debit Collection Failure", mailModel, "Admin");
                }
            } else {
                await sendMail("", schedule.loanId, "Loan Application Emails to Admin", "Debit Collection Failure", mailModel, "Admin");
            } 


            return {
                data: "",
                success: true,
                message: "Saved successfully"
            }

        }
        else {
            return {
                data: "",
                success: false,
                message: `${nspId} in process with status: ${schedule.payment_status} `
            }
        }
    }
    catch (ex) {
        saveErrorLog("updateDebitResponse", apiPath, { nspId: nspId }, { error: ex.message, stackTrace: ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex });
        let log = new DigioSchedulerLog({
            userId: schedule.userId,
            loanId: schedule.loanId,
            event: "getDebitResponse",
            error: ex.message,
            insertedOn: Date.now()
        });

        await log.save();

        return {
            data: "",
            success: false,
            message: `${nspId} failed with message ${ex.message} `
        }
    }

}

module.exports.sendEnachReassignmentRequest = async function (loanId, mailModel, adminId) {
    try {

        const loan = await LoanInfo.findById(loanId);

        const user = await UserInfo.findById(loan.userId);

        let userPartnerInfo = await UserPartnerInfo.findOne({
            userId: mongoose.Types.ObjectId(loan.userId),
            partnerId: mongoose.Types.ObjectId(loan.partnerId),
            merchantCode: loan.merchantCode
        });

        let oldMandate = await DigioMandateGeneration.findOne({ loanIds: loan._id, isActive: true, status: "register_success" });

        let createMandateResponse = {};

        if (user) {

            if (userPartnerInfo.bankDetails && userPartnerInfo.bankDetails[0]) {

                let digioBank = await DigioBankList.findOne({
                    systemBankName: {
                        $regex: new RegExp("^" + userPartnerInfo.bankDetails[0].bankName + "$", "i")
                    }
                });

                // if (!digioBank || (digioBank && !digioBank.netBanking && !digioBank.debitCard && !digioBank.esign)) {
                //     return {
                //         data: "",
                //         success: false,
                //         message: "e-Nach option not available for selected bank. Please select different bank.",
                //         status: 200
                //     };
                // }

                let beneficiary_name_with_bank = "";

                if (userPartnerInfo.bankDetails[0].docVerification && userPartnerInfo.bankDetails[0].docVerification.isBankVerified) {

                    beneficiary_name_with_bank = userPartnerInfo.bankDetails[0].accountName ? userPartnerInfo.bankDetails[0].accountName : "";

                    //call e-mandate
                    if (oldMandate) {
                        createMandateResponse = await createNewMandate(user, userPartnerInfo, loan._id, digioBank, mailModel, adminId, beneficiary_name_with_bank);

                        return createMandateResponse;
                    }
                    else {
                        const existingMandate = await DigioMandateGeneration.findOne({
                            userId: loan.userId,
                            partnerId: loan.partnerId,
                            isActive: true,
                            status: "register_success"
                        })
                            .sort({ "mandateDetails.mandate_data.maximum_amount": -1, createdOn: -1 });

                        const checkMandateDetails = await checkMandateCurrentBankDetails(existingMandate, loan._id);

                        if (checkMandateDetails.success && existingMandate) {
                            if (!existingMandate.loanIds.includes(loan._id)) {
                                existingMandate.loanIds.push(loan._id);
                                existingMandate.updatedOn = Date.now();
                                await existingMandate.save();
                            }

                            loan.secondaryCollectionType = "eNach";

                            loan.loanContractDetails.eNachUrl = loan.loanContractDetails.contractUrl;
                            await loan.save();

                            updateMandateLogs(existingMandate, loan._id);

                            return {
                                data: "",
                                success: true,
                                existingMandate: true,
                                message: "Loan has been assigned to existing mandate",
                                status: 200
                            }
                        }
                        else {
                            createMandateResponse = await createMandate(user, loan._id, digioBank, beneficiary_name_with_bank, false, null);

                            if (createMandateResponse.success) {
                                if (loan.primaryCollectionType === "Wallet" && loan.secondaryCollectionType === "")
                                    loan.secondaryCollectionType = "eNach";

                                loan.loanContractDetails.contractUrl = createMandateResponse.eNachUrl;
                                loan.loanContractDetails.eNachUrl = createMandateResponse.eNachUrl;

                                await loan.save();

                                mailModel.link =
                                    "<a href='" +
                                    createMandateResponse.eNachUrl +
                                    "'>Link</a>";

                                //Send email
                                await sendMail(user._id, loanId, "Emails to Borrower", "AutoPay Consent Request", mailModel, "User");

                                return createMandateResponse;
                            }
                        }
                    }
                } else {
                    //call bank verification
                    const bankresponse = await validateAndSaveBankDetails(loan.userId, userPartnerInfo.partnerId, userPartnerInfo.bankDetails[0].accountNo.toUpperCase(), userPartnerInfo.bankDetails[0].ifsc.toUpperCase(), userPartnerInfo.bankDetails[0].accountType, userPartnerInfo.bankDetails[0].accountName, null, null, loan.merchantCode, loan._id, true);
                    //call e-mandate

                    if (bankresponse.success) {

                        userPartnerInfo = await UserPartnerInfo.findOne({
                            userId: mongoose.Types.ObjectId(loan.userId),
                            partnerId: mongoose.Types.ObjectId(loan.partnerId),
                            merchantCode: loan.merchantCode
                        });

                        if (userPartnerInfo.bankDetails[0].docVerification && userPartnerInfo.bankDetails[0].docVerification.isBankVerified) {
                            beneficiary_name_with_bank = userPartnerInfo.bankDetails[0].accountName ? userPartnerInfo.bankDetails[0].accountName : "";
                        }

                        if (oldMandate) {
                            createMandateResponse = await createNewMandate(user, userPartnerInfo, loan._id, digioBank, mailModel, adminId, beneficiary_name_with_bank);
                            return createMandateResponse;
                        }
                        else {
                            const existingMandate = await DigioMandateGeneration.findOne({
                                userId: loan.userId,
                                partnerId: loan.partnerId,
                                isActive: true,
                                status: "register_success"
                            })
                                .sort({ "mandateDetails.mandate_data.maximum_amount": -1, createdOn: -1 });

                            const checkMandateDetails = await checkMandateCurrentBankDetails(existingMandate, loan._id);

                            if (checkMandateDetails.success && existingMandate) {
                                if (!existingMandate.loanIds.includes(loan._id)) {
                                    existingMandate.loanIds.push(loan._id);
                                    existingMandate.updatedOn = Date.now();
                                    await existingMandate.save();
                                }

                                loan.secondaryCollectionType = "eNach";

                                loan.loanContractDetails.eNachUrl = loan.loanContractDetails.contractUrl;
                                await loan.save();

                                updateMandateLogs(existingMandate, loan._id);

                                return {
                                    data: "",
                                    success: true,
                                    existingMandate: true,
                                    message: "Loan has been assigned to existing mandate",
                                    status: 200
                                }
                            }
                            else {
                                createMandateResponse = await createMandate(user, loan._id, digioBank, mailModel, beneficiary_name_with_bank, false, null);

                                if (createMandateResponse.success) {
                                    if (loan.primaryCollectionType === "Wallet" && loan.secondaryCollectionType === "")
                                        loan.secondaryCollectionType = "eNach";

                                    loan.loanContractDetails.contractUrl = createMandateResponse.eNachUrl;
                                    loan.loanContractDetails.eNachUrl = createMandateResponse.eNachUrl;

                                    await loan.save();

                                    mailModel.link =
                                        "<a href='" +
                                        createMandateResponse.eNachUrl +
                                        "'>Link</a>";

                                    //Send email
                                    await sendMail(user._id, loanId, "Emails to Borrower", "AutoPay Consent Request", mailModel, "User");

                                }

                                return createMandateResponse;
                            }
                        }
                    } else {
                        return {
                            data: "",
                            success: false,
                            message: bankresponse.message,
                            status: 200
                        }
                    }
                }
            } else {
                return {
                    data: "",
                    success: false,
                    message: "User Bank details not found",
                    status: 200
                }
            }
        } else {
            return {
                data: "",
                success: false,
                message: "No user found",
                status: 200
            }
        }
    }
    catch (ex) {
        saveErrorLog("sendEnachReassignmentRequest", apiPath, { loanId, mailModel, adminId }, { error: ex.message, stackTrace: ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex });

        return { data: ex.message, success: false, message: ex.message, status: 200 };
    }

}

async function createNewMandate(user, userPartnerInfo, loanId, digioBank, mailModel, adminId, beneficiary_name_with_bank) {

    const loan = await LoanInfo.findById(loanId);

    const partnerMaster = await PartnerMaster.findById(loan.partnerId);

    try {
        let oldMandate = await DigioMandateGeneration.findOne({ loanIds: loan._id, isActive: true, status: "register_success" });

        let mobileNo = user.userDetails.mobileNo[0] ? user.userDetails.mobileNo[0].mobileNumber : user.userDetails.emailId[0] ? user.userDetails.emailId[0] : "";// mobile number change 
        // let customer_ref_number = loan.merchantCode ? loan.merchantCode : "";
        let customer_ref_number = user.panNo ? user.panNo : "";
        let scheme_ref_number = loan.loanNo;
        let customer_account_number = userPartnerInfo.bankDetails[0] && userPartnerInfo.bankDetails[0].accountNo ? userPartnerInfo.bankDetails[0].accountNo : "";
        let customer_account_type = userPartnerInfo.bankDetails[0] && userPartnerInfo.bankDetails[0].accountType ? userPartnerInfo.bankDetails[0].accountType : "";
        let destination_bank_id = userPartnerInfo.bankDetails[0] && userPartnerInfo.bankDetails[0].ifsc ? userPartnerInfo.bankDetails[0].ifsc : "";
        let destination_bank_name = digioBank && digioBank.digioBankName ? digioBank.digioBankName : digioBank.systemBankName;
        let customer_name = beneficiary_name_with_bank ? beneficiary_name_with_bank : getName(user.userDetails.firstName, user.userDetails.middleName, user.userDetails.lastName);
        let first_collection_date = moment(new Date()).format("yyyy-MM-DD");

        let frequency = config.get("digio.eNach.frequency");

        let model = {
            "customer_identifier": mobileNo,
            "auth_mode": config.get("digio.eNach.auth_mode"),
            "mandate_type": config.get("digio.eNach.mandateType"),
            "corporate_config_id": config.get("digio.eNach.corporate_config_id"),
            "generate_access_token": config.get("digio.eNach.generate_access_token"),
            "expire_in_days": config.get("digio.eNach.expire_in_days"),
            "mandate_data": {
                "maximum_amount": oldMandate.mandateDetails && oldMandate.mandateDetails.mandate_data && oldMandate.mandateDetails.mandate_data.maximum_amount ? oldMandate.mandateDetails.mandate_data.maximum_amount : loan.amount,
                "instrument_type": config.get("digio.eNach.instrument_type"),
                "first_collection_date": first_collection_date,
                "is_recurring": config.get("digio.eNach.isRecurring"),
                "frequency": frequency,
                "management_category": config.get("digio.eNach.management_category"),
                "customer_name": customer_name,
                "customer_account_number": customer_account_number,
                "destination_bank_id": destination_bank_id,
                "destination_bank_name": destination_bank_name,
                "customer_account_type": customer_account_type,
                "customer_ref_number": customer_ref_number,
                "scheme_ref_number": scheme_ref_number,
                "customer_email": user.userDetails.emailId[0] ? user.userDetails.emailId[0] : "",
                "customer_mobile": user.userDetails.mobileNo[0] ? user.userDetails.mobileNo[0].mobileNumber : "" // mobile number change 
            }
        }

        let mandateResponse = await mandateCreation(model, user, oldMandate.loanIds);

        if (mandateResponse.success) {

            let startDateParts = first_collection_date.split("-");

            let mandateCreation = new DigioMandateGeneration({
                id: mandateResponse.data.id ? mandateResponse.data.id : "",
                mandate_id: mandateResponse.data.mandate_id ? mandateResponse.data.mandate_id : "",
                state: mandateResponse.data.state ? mandateResponse.data.state : "",
                type: mandateResponse.data.type ? mandateResponse.data.type : "",
                status: "created",
                statusModifiedAt: Date.now(),
                rejectCode: "",
                rejectReason: "",
                bank_details: {
                    shared_with_bank: mandateResponse.data.bank_details && mandateResponse.data.bank_details.shared_with_bank ? mandateResponse.data.bank_details.shared_with_bank : "",
                    bank_name: mandateResponse.data.bank_details && mandateResponse.data.bank_details.bank_name ? mandateResponse.data.bank_details.bank_name : "",
                    state: mandateResponse.data.bank_details && mandateResponse.data.bank_details.state ? mandateResponse.data.bank_details.state : "",
                },
                created_at: mandateResponse.data.created_at ? mandateResponse.data.created_at : "",
                mode: mandateResponse.data.mode ? mandateResponse.data.mode : "",
                service_provider_details: {
                    service_provider_name: mandateResponse.data.service_provider_details && mandateResponse.data.service_provider_details.service_provider_name ? mandateResponse.data.service_provider_details.service_provider_name : "",
                    service_provider_utility_code: mandateResponse.data.service_provider_details && mandateResponse.data.service_provider_details.service_provider_utility_code ? mandateResponse.data.service_provider_details.service_provider_utility_code : "",
                    support_email: mandateResponse.data.service_provider_details && mandateResponse.data.service_provider_details.support_email ? mandateResponse.data.service_provider_details.support_email : "",
                    support_mobile: mandateResponse.data.service_provider_details && mandateResponse.data.service_provider_details.support_mobile ? mandateResponse.data.service_provider_details.support_mobile : "",
                },
                userId: user._id,
                partnerId: loan.partnerId,
                isActive: false,
                startDate: new Date(startDateParts[0], startDateParts[1] - 1, startDateParts[2]),
                endDate: null,
                createdOn: Date.now(),
                mandateDetails: model,
                ackResponse: [],
                access_token: {
                    created_at: mandateResponse.data.access_token && mandateResponse.data.access_token.created_at ? new Date(mandateResponse.data.access_token.created_at) : null,
                    id: mandateResponse.data.access_token && mandateResponse.data.access_token.id ? mandateResponse.data.access_token.id : "",
                    entity_id: mandateResponse.data.access_token && mandateResponse.data.access_token.entity_id ? mandateResponse.data.access_token.entity_id : "",
                    valid_till: mandateResponse.data.access_token && mandateResponse.data.access_token.valid_till ? new Date(mandateResponse.data.access_token.valid_till) : null,
                },
                loanIds: oldMandate.loanIds,
                creationType: "revised"
            });

            await mandateCreation.save();

            // oldMandate.isActive = false;
            // oldMandate.inActiveFrom = Date.now();
            // oldMandate.cancellationErrorMessage = "New mandate created - Assign/Collections Queue";
            // oldMandate.inActiveBy = adminId;

            // await oldMandate.save();

            let url = config.get("digio.eNach.nachLinkUrl");

            let mandateId = mandateResponse.data.mandate_id ? mandateResponse.data.mandate_id : "";

            let clientCode = loan.merchantCode ? loan.merchantCode : "";

            // let email = user.userDetails.emailId[0] ? user.userDetails.emailId[0] : "";

            let redirectUrl = `?logo=${config.get("digio.eNach.logoLink")}`;

            if (mandateResponse.data.access_token && mandateResponse.data.access_token.id)
                redirectUrl += `&token_id=${mandateResponse.data.access_token.id}`;

            url += mandateId + "/" + clientCode + "/" + mobileNo + redirectUrl;

            const eNachCode = config.get("shortUrl.eNachCode");

            const shortUrlResponse = await generateCustomUrl(url, eNachCode, loan.userId, loan._id, loan.loanNo, loan.merchantCode, partnerMaster.displayName, null);

            const longUrl = url;

            if (shortUrlResponse.success)
                url = shortUrlResponse.data;

            loan.loanContractDetails.contractUrl = url;
            loan.loanContractDetails.eNachUrl = url;
            loan.loanContractDetails.redirectionLongUrl = longUrl;

            await loan.save();

            mailModel.link =
                "<a href='" +
                `${url}?c=e` +
                "'>Link</a>";

            //Send email
            await sendMail(
                user._id,
                loanId,
                "Emails to Borrower",
                "Mandate Re-assignment",
                mailModel,
                "User"
            );

            return mandateResponse;
        }
        else {
            return mandateResponse;
        }
    }
    catch (ex) {
        saveErrorLog("createNewMandate", apiPath, { userId: user._id, userPartnerInfo, loanId, digioBank, mailModel, adminId, beneficiary_name_with_bank }, { error: ex.message, stackTrace: ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex });

        return { data: ex.message, success: false, message: ex.message, status: 200 };
    }
}

module.exports.resendEmiRequest = async function (id, loan, adminId) {

    try {

        const schedule = await DigioCollectionSchedule.findById(id);

        if (!schedule.debitResponseReceived)
            return {
                data: "",
                success: false,
                message: "This EMI has already been scheduled"
            }

        const mandate = await DigioMandateGeneration.findOne({ loanIds: schedule.loanId, isActive: true, status: "register_success" });

        if (!mandate)
            return {
                data: "",
                success: false,
                message: "No active mandate found for this loan",
                status: 501
            }

        let { key } = getUniqueKey(loan.loanNo);

        const today = new Date();

        let todayDate = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 5, 30, 0, 0);
        todayDate = dateAdd("day", 1, todayDate);

        let installmentAmount = 0;
        let penalty = 0;
        let moratoriumInterest = 0;

        let emiRequestList = [];

        const requestEmis = await EmiRequest.find({ refId: schedule.id });

        // let presentationFine = 0;
        // if (schedule.failure_reason == "04" || schedule.failure_reason == "06" || schedule.failure_reason == "57" || schedule.failure_reason == "58")
        //     presentationFine = config.get("digio.eNach.presentationFine") * retryCount;

        let totalCollectionAmount = 0;

        const emiList = await installmentService.getInstallments(loan._id, loan.loanNo);
        if (!emiList.success) {
            return {
                data: "",
                success: false,
                message: emiList.message,
                status: 200
            }
        }

        const emiFees = await installmentService.getLoanFees(loan._id);
        if (!emiFees.success) {
            return {
                data: "",
                success: false,
                message: emiFees.message,
                status: 200
            }
        }

        for (const emi of requestEmis) {
            const installment = emiList.data.filter((x) => x.installmentNumber === emi.emiNumber);
            const fees = emiFees.data.filter((x) => x.installmentNumber === emi.emiNumber);

            let installmentFees = 0;
            if (fees != null && fees.length > 0) {
                for (let i = 0; i < fees.length; i++) {
                    penalty += fees[i].feeAmount;
                    installmentFees += fees[i].feeAmount;
                }
            }

            if (installment && installment[0]) {
                installmentAmount += installment[0].installmentAmount;
                moratoriumInterest += installment[0].moratoriumInterest;

                const totalAmount = installment[0].installmentAmount + installmentFees + installment[0].moratoriumInterest;

                totalCollectionAmount += totalAmount;

                const emiRequest = {
                    "userId": loan.userId,
                    "loanId": loan._id,
                    "refId": "",
                    "emiNumber": emi.emiNumber,
                    "totalAmount": totalAmount,
                    "emiAmount": installment[0].installmentAmount ? installment[0].installmentAmount : 0,
                    "penalty": installmentFees,
                    "monotoriumInterest": installment[0].moratoriumInterest ? installment[0].moratoriumInterest : 0,
                    "insertedOn": Date.now()
                };

                emiRequestList.push(emiRequest);
            }
        }

        const repaymentDate = moment(todayDate).format("yyyy-MM-DD");

        const emiModel = {
            "umrn": mandate.umrn ? mandate.umrn : "",
            "amount": totalCollectionAmount,
            "settlement_date": repaymentDate,
            "corporate_account_number": config.get("digio.eNach.corporate_account_number"),
            "corporate_config_id": config.get("digio.eNach.corporate_config_id"),
            "destination_bank_id": mandate.mandateDetails && mandate.mandateDetails.mandate_data && mandate.mandateDetails.mandate_data.destination_bank_id ? mandate.mandateDetails.mandate_data.destination_bank_id : "",
            "customer_account_number": mandate.mandateDetails && mandate.mandateDetails.mandate_data && mandate.mandateDetails.mandate_data.customer_account_number ? mandate.mandateDetails.mandate_data.customer_account_number : "",
            "customer_name": mandate.mandateDetails && mandate.mandateDetails.mandate_data && mandate.mandateDetails.mandate_data.customer_name ? mandate.mandateDetails.mandate_data.customer_name : "",
            "frequency": mandate.mandateDetails && mandate.mandateDetails.mandate_data && mandate.mandateDetails.mandate_data.frequency ? mandate.mandateDetails.mandate_data.frequency : "",
            "payment_count": 1,
            "unique_key": key
        };

        const sendCreactionRequest = await scheduleCreation(emiModel, loan.userId, loan._id);

        if (sendCreactionRequest.success) {

            const emiSaveData = {
                "id": sendCreactionRequest.data && sendCreactionRequest.data.id ? sendCreactionRequest.data.id : "",
                "umrn": sendCreactionRequest.data && sendCreactionRequest.data.umrn ? sendCreactionRequest.data.umrn : "",
                "status": sendCreactionRequest.data && sendCreactionRequest.data.status ? sendCreactionRequest.data.status : "",
                "upcoming_transaction": {
                    "id": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.id ? sendCreactionRequest.data.upcoming_transaction.id : "",
                    "umrn": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.umrn ? sendCreactionRequest.data.upcoming_transaction.umrn : "",
                    "state": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.state ? sendCreactionRequest.data.upcoming_transaction.state : "",
                    "created_at": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.created_at ? new Date(sendCreactionRequest.data.upcoming_transaction.created_at) : null,
                    "present_at": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.present_at ? new Date(sendCreactionRequest.data.upcoming_transaction.present_at) : null,
                    "scheduled_settlement_date": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.scheduled_settlement_date ? new Date(sendCreactionRequest.data.upcoming_transaction.scheduled_settlement_date) : null,
                    "customer": {
                        "name": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.customer && sendCreactionRequest.data.upcoming_transaction.customer.name ? sendCreactionRequest.data.upcoming_transaction.customer.name : "",
                    },
                    "details": {
                        "status": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.details && sendCreactionRequest.data.upcoming_transaction.details.status ? sendCreactionRequest.data.upcoming_transaction.details.status : "",
                        "amount": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.details && sendCreactionRequest.data.upcoming_transaction.details.amount ? sendCreactionRequest.data.upcoming_transaction.details.amount : 0,
                        "amount_in_paise": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.details && sendCreactionRequest.data.upcoming_transaction.details.amount_in_paise ? sendCreactionRequest.data.upcoming_transaction.details.amount_in_paise : 0,
                        "sponsor_bank_id": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.details && sendCreactionRequest.data.upcoming_transaction.details.sponsor_bank_id ? sendCreactionRequest.data.upcoming_transaction.details.sponsor_bank_id : "",
                        "ready_to_process_at": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.details && sendCreactionRequest.data.upcoming_transaction.details.ready_to_process_at ? new Date(sendCreactionRequest.data.upcoming_transaction.details.ready_to_process_at) : null,
                        "type": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.details && sendCreactionRequest.data.upcoming_transaction.details.type ? sendCreactionRequest.data.upcoming_transaction.details.type : "",
                        "txn_ref": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.details && sendCreactionRequest.data.upcoming_transaction.details.txn_ref ? sendCreactionRequest.data.upcoming_transaction.details.txn_ref : "",
                    }
                },
                "mandate": {
                    "id": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.id ? sendCreactionRequest.data.mandate.id : "",
                    "mandate_id": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_id ? sendCreactionRequest.data.mandate.mandate_id : "",
                    "state": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.state ? sendCreactionRequest.data.mandate.state : "",
                    "type": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.type ? sendCreactionRequest.data.mandate.type : "",
                    "mandate_details": {
                        "file_name": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.file_name ? sendCreactionRequest.data.mandate.mandate_details.file_name : "",
                        "customer_identifier": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.customer_identifier ? sendCreactionRequest.data.mandate.mandate_details.customer_identifier : "",
                        "customer_name": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.customer_name ? sendCreactionRequest.data.mandate.mandate_details.customer_name : "",
                        "customer_ref_number": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.customer_ref_number ? sendCreactionRequest.data.mandate.mandate_details.customer_ref_number : "",
                        "scheme_ref_number": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.scheme_ref_number ? sendCreactionRequest.data.mandate.mandate_details.scheme_ref_number : "",
                        "customer_mobile": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.customer_mobile ? sendCreactionRequest.data.mandate.mandate_details.customer_mobile : "",
                        "auth_type": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.auth_type ? sendCreactionRequest.data.mandate.mandate_details.auth_type : "",
                        "authentication_time": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.authentication_time ? new Date(sendCreactionRequest.data.mandate.mandate_details.authentication_time) : null,
                        "is_recurring": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.is_recurring ? sendCreactionRequest.data.mandate.mandate_details.is_recurring : false,
                        "frequency": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.frequency ? sendCreactionRequest.data.mandate.mandate_details.frequency : "",
                        "first_collection_date": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.first_collection_date ? new Date(sendCreactionRequest.data.mandate.mandate_details.first_collection_date) : null,
                        "final_collection_date": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.final_collection_date ? new Date(sendCreactionRequest.data.mandate.mandate_details.final_collection_date) : null,
                        "maximum_amount": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.maximum_amount ? sendCreactionRequest.data.mandate.mandate_details.maximum_amount : 0,
                        "customer_account_number": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.customer_account_number ? sendCreactionRequest.data.mandate.mandate_details.customer_account_number : "",
                        "customer_account_type": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.customer_account_type ? sendCreactionRequest.data.mandate.mandate_details.customer_account_type : "",
                        "destination_bank_id": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.destination_bank_id ? sendCreactionRequest.data.mandate.mandate_details.destination_bank_id : "",
                        "destination_bank_name": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.destination_bank_name ? sendCreactionRequest.data.mandate.mandate_details.destination_bank_name : "",
                        "sponsor_bank_name": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.sponsor_bank_name ? sendCreactionRequest.data.mandate.mandate_details.sponsor_bank_name : "",
                        "npci_txn_id": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.npci_txn_id ? sendCreactionRequest.data.mandate.mandate_details.npci_txn_id : "",
                        "account_validation": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.account_validation ? sendCreactionRequest.data.mandate.mandate_details.account_validation : "",
                        "block_fund": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.block_fund ? sendCreactionRequest.data.mandate.mandate_details.block_fund : false,
                        "revocable": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.revocable ? sendCreactionRequest.data.mandate.mandate_details.revocable : false,
                    },
                    "umrn": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.umrn ? sendCreactionRequest.data.mandate.umrn : "",
                    "created_at": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.created_at ? new Date(sendCreactionRequest.data.mandate.created_at) : null,
                    "updated_at": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.updated_at ? new Date(sendCreactionRequest.data.mandate.updated_at) : null,
                    "mode": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mode ? sendCreactionRequest.data.mandate.mode : "",
                    "auth_sub_mode": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.auth_sub_mode ? sendCreactionRequest.data.mandate.auth_sub_mode : "",
                },
                "customer": {
                    "name": sendCreactionRequest.data && sendCreactionRequest.data.customer && sendCreactionRequest.data.customer.name ? sendCreactionRequest.data.customer.name : "",
                    "destination_account_type": sendCreactionRequest.data && sendCreactionRequest.data.customer && sendCreactionRequest.data.customer.destination_account_type ? sendCreactionRequest.data.customer.destination_account_type : "",
                    "destination_ifsc": sendCreactionRequest.data && sendCreactionRequest.data.customer && sendCreactionRequest.data.customer.destination_ifsc ? sendCreactionRequest.data.customer.destination_ifsc : "",
                    "user_account_number": sendCreactionRequest.data && sendCreactionRequest.data.customer && sendCreactionRequest.data.customer.user_account_number ? sendCreactionRequest.data.customer.user_account_number : "",
                },
                "corporate": {
                    "name": sendCreactionRequest.data && sendCreactionRequest.data.corporate && sendCreactionRequest.data.corporate.name ? sendCreactionRequest.data.corporate.name : "",
                    "utility_code": sendCreactionRequest.data && sendCreactionRequest.data.corporate && sendCreactionRequest.data.corporate.utility_code ? sendCreactionRequest.data.corporate.utility_code : "",
                    "corporate_reference": sendCreactionRequest.data && sendCreactionRequest.data.corporate && sendCreactionRequest.data.corporate.corporate_reference ? sendCreactionRequest.data.corporate.corporate_reference : "",
                    "sponsor_ifsc": sendCreactionRequest.data && sendCreactionRequest.data.corporate && sendCreactionRequest.data.corporate.sponsor_ifsc ? sendCreactionRequest.data.corporate.sponsor_ifsc : "",
                    "sponsor_bank_code": sendCreactionRequest.data && sendCreactionRequest.data.corporate && sendCreactionRequest.data.corporate.sponsor_bank_code ? sendCreactionRequest.data.corporate.sponsor_bank_code : "",
                    "corporate_account_number": sendCreactionRequest.data && sendCreactionRequest.data.corporate && sendCreactionRequest.data.corporate.corporate_account_number ? sendCreactionRequest.data.corporate.corporate_account_number : "",
                },
                "partner": {
                    "name": sendCreactionRequest.data && sendCreactionRequest.data.partner && sendCreactionRequest.data.partner.name ? sendCreactionRequest.data.partner.name : "",
                    "identifier": sendCreactionRequest.data && sendCreactionRequest.data.partner && sendCreactionRequest.data.partner.identifier ? sendCreactionRequest.data.partner.identifier : "",
                },
                "details": {
                    "first_proposed_settlement_date": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.first_proposed_settlement_date ? new Date(sendCreactionRequest.data.details.first_proposed_settlement_date) : null,
                    "payment_count": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.payment_count ? sendCreactionRequest.data.details.payment_count : 0,
                    "paid_count": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.paid_count ? sendCreactionRequest.data.details.paid_count : 0,
                    "last_presented_at": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.last_presented_at ? new Date(sendCreactionRequest.data.details.last_presented_at) : null,
                    "last_presented_settlement_date": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.last_presented_settlement_date ? new Date(sendCreactionRequest.data.details.last_presented_settlement_date) : null,
                    "frequency": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.frequency ? sendCreactionRequest.data.details.frequency : "",
                    "present_settlement_before_days": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.present_settlement_before_days ? sendCreactionRequest.data.details.present_settlement_before_days : 0,
                    "narration": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.narration ? sendCreactionRequest.data.details.narration : "",
                    "amount_in_paise": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.amount_in_paise ? sendCreactionRequest.data.details.amount_in_paise : 0,
                    "ended_at": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.ended_at ? new Date(sendCreactionRequest.data.details.ended_at) : null,
                    "mandate_id": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.mandate_id ? sendCreactionRequest.data.details.mandate_id : "",
                    "client_ref_id": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.client_ref_id ? sendCreactionRequest.data.details.client_ref_id : "",
                },
                "created_at": sendCreactionRequest.data && sendCreactionRequest.data.created_at ? new Date(sendCreactionRequest.data.created_at) : null,
                "sponsor_bank_name": sendCreactionRequest.data && sendCreactionRequest.data.sponsor_bank_name ? sendCreactionRequest.data.sponsor_bank_name : "",
                "payment_status": "new",
                "settlementDoneOn": null,
                "failure_reason": null,
                "failure_description": null,
                "total_payment_count": null,
                "processed_payment_count": null,
                "lastEventCalled": null,
                "lastEventCalledAt": null,
                "userId": loan.userId,
                "loanId": loan._id,
                "partnerId": schedule.partnerId,
                "retryCount": 0,
                "sendingState": "Send",
                "unique_key": key,
                "insertedOn": Date.now(),
                "transaction_id": null,
                "emiAmount": installmentAmount,
                "penalty": penalty,
                "moratoriumInterest": moratoriumInterest,
                "presentationFine": 0,
                "totalAmount": totalCollectionAmount,
                "settlementDate": todayDate,
                "debitResponseReceived": false,
                "emis": schedule.emis,
                "collectionType": "Single",
                "insertedBy": adminId
            };

            const saveCollection = new DigioCollectionSchedule(emiSaveData);

            await saveCollection.save();

            for (const emiItem of emiRequestList) {

                emiItem.refId = sendCreactionRequest.data && sendCreactionRequest.data.id ? sendCreactionRequest.data.id : "";

                const emiRequest = new EmiRequest(emiItem);

                await emiRequest.save();

            }

            schedule.sendingState = "InActive";
            schedule.systemErrorMessage = "New collection request has been scheduled";

            await schedule.save();

            return { data: "", success: true, message: "Scheduled successfully", status: 200 };

        }
        else {
            return sendCreactionRequest;
        }
    }
    catch (ex) {
        saveErrorLog("resendEmiRequest", apiPath, { id, loanId: loan._id, adminId }, { error: ex.message, stackTrace: ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex });

        const log = new DigioSchedulerLog({
            userId: loan.userId,
            loanId: loan._id,
            event: "error",
            error: ex.message,
            insertedOn: Date.now()
        });

        await log.save();

        return { data: ex.message, success: false, message: ex.message, status: 500 };

    }

}

const cancelMandate = async function (model, skipSession, skipNotification) {
    try {

        if (!skipSession) {
            if (!model.token) {
                return {
                    data: "",
                    success: false,
                    message: "Token Missing",
                    status: 500
                }
            }

            let token = model.token
            const session = await EngagementSession.findOne({ sessionToken: token }).lean()

            if (!session) {
                return {
                    data: "",
                    success: false,
                    message: "Unauthorized",
                    status: 500
                }
            }


            let sessionExpired = session.expiryTime > new Date() ? false : true

            if (sessionExpired) {
                return {
                    data: "",
                    success: false,
                    message: "Token Expired",
                    status: 500
                }
            }
        }

        const mandate = await DigioMandateGeneration.findOne({ umrn: model.umrn });

        if (!mandate)
            return {
                data: "",
                success: false,
                message: "No mandate found for this umrn number",
                status: 200
            }

        if (mandate.cancellationRequestSubmitted)
            return {
                data: "",
                success: false,
                message: "Cancellation request already submitted for this mandate",
                status: 200
            }

        if (mandate.cancellationConfirmed)
            return {
                data: "",
                success: false,
                message: "This mandate is already cancelled",
                status: 200
            }

        let loanNo = "";

        const today = new Date();

        let emiDate = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 5, 30, 0, 0);
        emiDate = dateAdd("day", 1, emiDate);

        const loanInstallments = await installmentService.getInstallmentsPending(mandate.loanIds, null, ["Pending", "Partial"], null, false, false, emiDate);

        if (loanInstallments.installments && loanInstallments.installments.length) {
            return {
                data: "",
                success: false,
                message: "Repayments are pending for this loan, hence cancellation is not allowed",
                status: 500
            }
        }

        const user = await UserInfo.findById(mandate.userId);

        if (!user)
            return {
                data: "",
                success: false,
                message: "No user found for this loan",
                status: 200
            }

        for (const loanId of mandate.loanIds) {

            const loan = await LoanInfo.findById(loanId);

            if (!loan)
                return {
                    data: "",
                    success: false,
                    message: "Loan belonging to this umrn doesn't exist",
                    status: 200
                }

            loanNo += `${loan.loanNo}, `;



            // let installmentDetails = await loanInstallments.data.filter((x) => x.loanId.toString() == loanId.toString());

            // if (installmentDetails.length > 0 && mandate.isActive)
            //     return {
            //         data: "",
            //         success: false,
            //         message: "Repayments are pending for this loan, hence cancellation is not allowed",
            //         status: 200
            //     }
        }


        let mobileNo = "";

        const accountNo = mandate.mandateDetails && mandate.mandateDetails.mandate_data && mandate.mandateDetails.mandate_data.customer_account_number ? mandate.mandateDetails.mandate_data.customer_account_number : "";
        const bankName = mandate.mandateDetails && mandate.mandateDetails.mandate_data && mandate.mandateDetails.mandate_data.destination_bank_name ? mandate.mandateDetails.mandate_data.destination_bank_name : "";
        const mandateAmount = mandate.mandateDetails && mandate.mandateDetails.mandate_data && mandate.mandateDetails.mandate_data.maximum_amount ? mandate.mandateDetails.mandate_data.maximum_amount : "";

        if (user)
            mobileNo = user.userDetails && user.userDetails.mobileNo && user.userDetails.mobileNo[0] ? user.userDetails.mobileNo[0].mobileNumber : ""; // mobile number change 


        let cancellationModel = {
            "umrn": mandate.umrn ? mandate.umrn : "",
            "dest_ifsc": mandate.mandateDetails && mandate.mandateDetails.mandate_data && mandate.mandateDetails.mandate_data.destination_bank_id ? mandate.mandateDetails.mandate_data.destination_bank_id : ""
        };

        let sendCancellationRequest = await sendMandateCancelRequest(cancellationModel, mandate.userId, mandate.loanIds);

        if (sendCancellationRequest.success) {

            mandate.cancellationRequestSubmitted = true;
            mandate.cancelledBy = mandate.userId;
            mandate.cancelledOn = null;
            mandate.cancellIntiatedOn = Date.now();
            mandate.cancellationConfirmed = false;
            mandate.cancellationErrorMessage = "";

            await mandate.save();

            if (mobileNo && !skipNotification) {
                let smsModel = getSmsModel();
                smsModel.To = mobileNo;
                smsModel.TemplateName = "enach_cancellation_request";
                smsModel.VAR1 = user.userDetails.firstName;
                smsModel.VAR2 = bankName;
                smsModel.VAR3 = mandateAmount;
                smsModel.Type = "TEMPLATE";
                smsModel.From = config.get("keys.from");

                const sendSms = await pushToNotifications(null, null, null, null,
                    mandate.userId, null, "WhatsApp", smsModel.To, smsModel.TemplateName, "loan", null, null, smsModel, false, null);
                if (sendSms.success)
                    response = await sendNotifications(sendSms.data._id);

                let mailModel = getMailModel();

                mailModel.accountnumber = bankName
                mailModel.amount = mandateAmount
                sendMail(user._id, "", "Loan Application Emails to Borrower", "Mandate Cancellation Initiation", mailModel, "User");

                return {
                    data: "",
                    success: true,
                    message: sendCancellationRequest.message,
                    status: 200
                }
            }
            else {
                return {
                    data: "",
                    success: true,
                    message: sendCancellationRequest.message,
                    status: 200
                }
            }

        }
        else {
            return {
                data: "",
                success: false,
                message: sendCancellationRequest.message,
                status: 200
            }
        }

    }
    catch (ex) {
        saveErrorLog("cancelMandate", apiPath, { model }, { error: ex.message, stackTrace: ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex });

        return { data: ex.message, success: false, message: ex.message, status: 200 };
    }
}

module.exports.cancelMandate = cancelMandate

async function sendMandateCancelRequest(model, userId, loanIds) {

    const url = config.get("digio.eNach.cancelMandate");

    try {

        const response = await callDigioApi("POST", url, model);

        let digioCancellationLog = {
            request: model,
            response: response,
            url: url,
            sentOn: Date.now(),
            userId: userId ? userId : null,
            loanIds: loanIds ? loanIds : null
        }

        await cancellationLog(digioCancellationLog);

        return { data: response, success: true, message: "Request Initiated" };
    }
    catch (ex) {
        saveErrorLog("sendMandateCancelRequest", apiPath, { model, userId, loanIds }, { error: ex.message, stackTrace: ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex });

        let digioCancellationLog = {
            request: model,
            response: ex.response.data,
            url: url,
            sentOn: Date.now(),
            userId: userId ? userId : null,
            loanIds: loanIds ? loanIds : null
        }

        await cancellationLog(digioCancellationLog);

        return { data: ex.response.data.code, success: false, message: ex.response.data.message };
    }
}

async function cancellationLog(model) {
    let log = new DigioMandateCancellationLog({
        request: JSON.stringify(model.request),
        response: JSON.stringify(model.response),
        url: model.url,
        sentOn: Date.now(),
        userId: model.userId ? model.userId : null,
        loanIds: model.loanIds ? model.loanIds : null
    });

    let response = await log.save();

    return response;

}

module.exports.getMandateList = async function (userId) {
    try {
        const user = await UserInfo.findById(userId);

        let userList = [];

        if (!user)
            return {
                data: userList,
                success: false,
                message: "No user found"
            }

        let query1 = {};

        query1["userId"] = user._id;

        query1["status"] = "register_success";

        let userDigioMandateRecords = await DigioMandateGeneration.findOne(query1).select({
            mandate_id: 1,
            status: 1,
            mandateDetails: 1,
            umrn: 1,
            loanId: 1,
            mandateRegisteredOn: 1,
            isActive: 1,
            cancellationConfirmed: 1,
        });

        const loanIds = await userDigioMandateRecords.map(x => x.loanId.toString());

        const loans = await LoanInfo.find({ _id: { $in: loanIds } }).select({
            loanNo: 1,
            amount: 1,
            status: 1,
        });

        const installmentDetails = await installmentService.getMultipleLoansInstallments(loanIds);
        if (!installmentDetails.success) {
            return {
                data: "",
                success: false,
                message: installmentDetails.message,
                status: 200
            }
        }

        const today = new Date();
        // const emiDate = new Date(
        //     today.getFullYear(),
        //     today.getMonth(),
        //     today.getDate() + 1, 5, 30, 0, 0
        // );

        // const allPendingEmis = await installmentService.getInstallmentsPending(loanIds = mandate.loanIds, installmentStatus = ["Pending"], tillDate = emiDate);
        // if (!allPendingEmis.success) {
        //     return {
        //         data: "",
        //         success: false,
        //         message: allPendingEmis.message
        //     }
        // }

        for (const loan of loans) {
            const totalEmis = installmentDetails.data.length > 0 ? installmentDetails.data.filter(x => x.loanId.toString() == loan._id.toString()).length : 0;
            const pendingEmis = installmentDetails.data.length > 0 && installmentDetails.data.filter((x) => x.loanId.toString() == loan._id.toString() && status == "Pending");

            const totalPendingEmis = pendingEmis.length;

            let isCancelButtonDisabled = false;

            // if (!loan.isActive || totalPendingEmis === 0)
            //     isCancelButtonDisabled = false;
            const loanMandateDetails = userDigioMandateRecords.filter(x => x.loanId.toString() == loan._id.toString());

            const item = {
                loanNo: loan.loanNo ? loan.loanNo : "",
                amount: loan.amount ? loan.amount : 0,
                loanStatus: loan.status ? loan.status : "",
                accountNo: loanMandateDetails[0].mandateDetails.mandate_data && loanMandateDetails[0].mandateDetails.mandate_data.customer_account_number ? loanMandateDetails[0].mandateDetails.mandate_data.customer_account_number : "",
                bank: loanMandateDetails[0].mandateDetails.mandate_data && loanMandateDetails[0].mandateDetails.mandate_data.destination_bank_name ? loanMandateDetails[0].mandateDetails.mandate_data.destination_bank_name : "",
                umrn: loanMandateDetails[0].umrn ? loanMandateDetails[0].umrn : "",
                mandateId: loanMandateDetails[0].mandate_id ? loanMandateDetails[0].mandate_id : "",
                mandateRegisteredOn: loanMandateDetails[0].mandateRegisteredOn ? getDMYFormattedDate(loanMandateDetails[0].mandateRegisteredOn) : "",
                totalEmis: totalEmis,
                pendingEmis: totalPendingEmis,
                isActive: loanMandateDetails[0].isActive ? true : false,
                isCancelButtonDisabled: isCancelButtonDisabled,
                cancellationConfirmed: loanMandateDetails[0].cancellationConfirmed ? true : false
            }

            userList.push(item);

        }

        return {
            data: userList,
            success: true,
            message: "",
            status: 200
        }
    } catch (ex) {
        saveErrorLog("getMandateList", apiPath, userId, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);

        return {
            data: "",
            success: false,
            message: ex.message,
            status: 200
        }
    }

}

module.exports.mandateCancellationWebHook = async function (model) {

    await mandateCancellationWebHook(model, "", "");

    return {
        data: "",
        success: true,
        message: ""
    }

    //update mandate
    //send sms


}

async function mandateCancellationWebHook(model, userId, loanIds) {
    let log = new DigioMandateCancellationWebHook({
        request: JSON.stringify(model),
        url: "",
        sentOn: Date.now(),
        userId: userId ? userId : null,
        loanIds: loanIds ? loanIds : null
    });

    let response = await log.save();

    return response;

}

module.exports.applyEnachPenalty = async function () {
    let isPenaltySuccess = true;
    let penaltyResponse = null;

    try {

        let query1 = {
            isDraft: false,
            status: { $in: config.get("keys.postDisbursalStatus") }
        };

        query1["$or"] = [
            {
                primaryCollectionType: "eNach"
            },
            {
                secondaryCollectionType: "eNach",
            },
        ];

        const loans = await LoanInfo.aggregate([
            {
                $match: query1,
            },
            {
                $lookup: {
                    from: "UserInfo",
                    localField: "userId",
                    foreignField: "_id",
                    as: "userInfo",
                },
            },
            {
                $project: {
                    "userInfo.userDetails": 1,
                    loanNo: 1,
                    userId: 1,
                    _id: 1,
                    amount: 1,
                    status: 1,
                    partnerId: 1,
                    productType: 1
                },
            },
        ]);

        for (const loan of loans) {

            penaltyResponse = await installmentService.applyLateFee(loan.partnerId, loan.productType, loan._id, true);

            if (!penaltyResponse.success) {
                isPenaltySuccess = false;
                break;
            }
        }

        return {
            data: "",
            success: isPenaltySuccess,
            message: penaltyResponse.message,
            status: 200,
        };

    }
    catch (ex) {
        saveErrorLog("applyEnachPenalty", apiPath, {}, { error: ex.message, stackTrace: ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex });
        return {
            data: "",
            success: false,
            message: ex.message
        }
    }
}

module.exports.sendScheduleRequestLoanWise = async function (loan, emiList, adminId) {

    try {
        const schedule = await DigioCollectionSchedule.findOne({ loanId: loan._id, sendingState: "Send" });

        if (schedule)
            return {
                data: "",
                success: true,
                message: "Active e-Nach request already exists for this loan"
            }

        const mandate = await DigioMandateGeneration.findOne({ loanIds: loan._id, isActive: true, status: "register_success" });

        if (!mandate)
            return {
                data: "",
                success: false,
                message: "No active mandate found for this loan"
            }

        let { key } = getUniqueKey(loan.loanNo);

        const today = new Date();

        let emiDate = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 5, 30, 0, 0);
        emiDate = dateAdd("day", 1, emiDate);

        let repaymentDate = moment(emiDate).format("yyyy-MM-DD");

        let totalEmiAmount = 0;
        let totalPenalty = 0;
        let totalMonotoriumInterest = 0;
        let totalCollectionAmount = 0;

        let emiRequestList = [];

        const installmentDetailsList = await installmentService.getInstallments(loan._id);
        if (!installmentDetailsList.success) {
            return {
                data: "",
                success: false,
                message: installmentDetailsList.message
            }
        }

        const emiFees = await installmentService.getLoanFees(loan._id);
        if (!emiFees.success) {
            return {
                data: "",
                success: false,
                message: emiFees.message,
                status: 200
            }
        }

        for (const emi of emiList) {
            const installmentDetails = installmentDetailsList.data.filter(x => x.installmentNumber === emi);
            const fees = emiFees.data.filter((x) => x.installmentNumber === emi);

            let installmentFees = 0;
            if (fees != null && fees.length > 0) {
                for (let i = 0; i < fees.length; i++) {
                    totalPenalty += fees[i].feeAmount;
                    installmentFees += fees[i].feeAmount;
                }
            }

            totalEmiAmount += installmentDetails[0].installmentAmount;
            totalMonotoriumInterest += installmentDetails[0].moratoriumInterest;

            const totalAmount = installmentDetails[0].installmentAmount + installmentFees + installmentDetails[0].moratoriumInterest;

            totalCollectionAmount += totalAmount;

            const emiRequest = {
                "userId": loan.userId,
                "loanId": loan._id,
                "refId": "",
                "emiNumber": emi,
                "totalAmount": totalAmount,
                "emiAmount": installmentDetails[0].installmentAmount ? installmentDetails[0].installmentAmount : 0,
                "penalty": installmentFees,
                "monotoriumInterest": installmentDetails[0].moratoriumInterest ? installmentDetails[0].moratoriumInterest : 0,
                "insertedOn": Date.now()
            };

            emiRequestList.push(emiRequest);
        }

        let emiModel = {
            "umrn": mandate.umrn ? mandate.umrn : "",
            "amount": totalCollectionAmount,
            "settlement_date": repaymentDate,
            "corporate_account_number": config.get("digio.eNach.corporate_account_number"),
            "corporate_config_id": config.get("digio.eNach.corporate_config_id"),
            "destination_bank_id": mandate.mandateDetails && mandate.mandateDetails.mandate_data && mandate.mandateDetails.mandate_data.destination_bank_id ? mandate.mandateDetails.mandate_data.destination_bank_id : "",
            "customer_account_number": mandate.mandateDetails && mandate.mandateDetails.mandate_data && mandate.mandateDetails.mandate_data.customer_account_number ? mandate.mandateDetails.mandate_data.customer_account_number : "",
            "customer_name": mandate.mandateDetails && mandate.mandateDetails.mandate_data && mandate.mandateDetails.mandate_data.customer_name ? mandate.mandateDetails.mandate_data.customer_name : "",
            "frequency": mandate.mandateDetails && mandate.mandateDetails.mandate_data && mandate.mandateDetails.mandate_data.frequency ? mandate.mandateDetails.mandate_data.frequency : "",
            "payment_count": 1,
            "unique_key": key
        };

        let sendCreactionRequest = await scheduleCreation(emiModel, loan.userId, loan._id);

        if (sendCreactionRequest.success) {

            let emiSaveData = {
                "id": sendCreactionRequest.data && sendCreactionRequest.data.id ? sendCreactionRequest.data.id : "",
                "umrn": sendCreactionRequest.data && sendCreactionRequest.data.umrn ? sendCreactionRequest.data.umrn : "",
                "status": sendCreactionRequest.data && sendCreactionRequest.data.status ? sendCreactionRequest.data.status : "",
                "upcoming_transaction": {
                    "id": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.id ? sendCreactionRequest.data.upcoming_transaction.id : "",
                    "umrn": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.umrn ? sendCreactionRequest.data.upcoming_transaction.umrn : "",
                    "state": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.state ? sendCreactionRequest.data.upcoming_transaction.state : "",
                    "created_at": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.created_at ? new Date(sendCreactionRequest.data.upcoming_transaction.created_at) : null,
                    "present_at": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.present_at ? new Date(sendCreactionRequest.data.upcoming_transaction.present_at) : null,
                    "scheduled_settlement_date": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.scheduled_settlement_date ? new Date(sendCreactionRequest.data.upcoming_transaction.scheduled_settlement_date) : null,
                    "customer": {
                        "name": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.customer && sendCreactionRequest.data.upcoming_transaction.customer.name ? sendCreactionRequest.data.upcoming_transaction.customer.name : "",
                    },
                    "details": {
                        "status": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.details && sendCreactionRequest.data.upcoming_transaction.details.status ? sendCreactionRequest.data.upcoming_transaction.details.status : "",
                        "amount": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.details && sendCreactionRequest.data.upcoming_transaction.details.amount ? sendCreactionRequest.data.upcoming_transaction.details.amount : 0,
                        "amount_in_paise": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.details && sendCreactionRequest.data.upcoming_transaction.details.amount_in_paise ? sendCreactionRequest.data.upcoming_transaction.details.amount_in_paise : 0,
                        "sponsor_bank_id": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.details && sendCreactionRequest.data.upcoming_transaction.details.sponsor_bank_id ? sendCreactionRequest.data.upcoming_transaction.details.sponsor_bank_id : "",
                        "ready_to_process_at": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.details && sendCreactionRequest.data.upcoming_transaction.details.ready_to_process_at ? new Date(sendCreactionRequest.data.upcoming_transaction.details.ready_to_process_at) : null,
                        "type": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.details && sendCreactionRequest.data.upcoming_transaction.details.type ? sendCreactionRequest.data.upcoming_transaction.details.type : "",
                        "txn_ref": sendCreactionRequest.data && sendCreactionRequest.data.upcoming_transaction && sendCreactionRequest.data.upcoming_transaction.details && sendCreactionRequest.data.upcoming_transaction.details.txn_ref ? sendCreactionRequest.data.upcoming_transaction.details.txn_ref : "",
                    }
                },
                "mandate": {
                    "id": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.id ? sendCreactionRequest.data.mandate.id : "",
                    "mandate_id": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_id ? sendCreactionRequest.data.mandate.mandate_id : "",
                    "state": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.state ? sendCreactionRequest.data.mandate.state : "",
                    "type": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.type ? sendCreactionRequest.data.mandate.type : "",
                    "mandate_details": {
                        "file_name": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.file_name ? sendCreactionRequest.data.mandate.mandate_details.file_name : "",
                        "customer_identifier": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.customer_identifier ? sendCreactionRequest.data.mandate.mandate_details.customer_identifier : "",
                        "customer_name": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.customer_name ? sendCreactionRequest.data.mandate.mandate_details.customer_name : "",
                        "customer_ref_number": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.customer_ref_number ? sendCreactionRequest.data.mandate.mandate_details.customer_ref_number : "",
                        "scheme_ref_number": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.scheme_ref_number ? sendCreactionRequest.data.mandate.mandate_details.scheme_ref_number : "",
                        "customer_mobile": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.customer_mobile ? sendCreactionRequest.data.mandate.mandate_details.customer_mobile : "",
                        "auth_type": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.auth_type ? sendCreactionRequest.data.mandate.mandate_details.auth_type : "",
                        "authentication_time": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.authentication_time ? new Date(sendCreactionRequest.data.mandate.mandate_details.authentication_time) : null,
                        "is_recurring": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.is_recurring ? sendCreactionRequest.data.mandate.mandate_details.is_recurring : false,
                        "frequency": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.frequency ? sendCreactionRequest.data.mandate.mandate_details.frequency : "",
                        "first_collection_date": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.first_collection_date ? new Date(sendCreactionRequest.data.mandate.mandate_details.first_collection_date) : null,
                        "final_collection_date": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.final_collection_date ? new Date(sendCreactionRequest.data.mandate.mandate_details.final_collection_date) : null,
                        "maximum_amount": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.maximum_amount ? sendCreactionRequest.data.mandate.mandate_details.maximum_amount : 0,
                        "customer_account_number": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.customer_account_number ? sendCreactionRequest.data.mandate.mandate_details.customer_account_number : "",
                        "customer_account_type": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.customer_account_type ? sendCreactionRequest.data.mandate.mandate_details.customer_account_type : "",
                        "destination_bank_id": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.destination_bank_id ? sendCreactionRequest.data.mandate.mandate_details.destination_bank_id : "",
                        "destination_bank_name": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.destination_bank_name ? sendCreactionRequest.data.mandate.mandate_details.destination_bank_name : "",
                        "sponsor_bank_name": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.sponsor_bank_name ? sendCreactionRequest.data.mandate.mandate_details.sponsor_bank_name : "",
                        "npci_txn_id": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.npci_txn_id ? sendCreactionRequest.data.mandate.mandate_details.npci_txn_id : "",
                        "account_validation": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.account_validation ? sendCreactionRequest.data.mandate.mandate_details.account_validation : "",
                        "block_fund": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.block_fund ? sendCreactionRequest.data.mandate.mandate_details.block_fund : false,
                        "revocable": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mandate_details && sendCreactionRequest.data.mandate.mandate_details.revocable ? sendCreactionRequest.data.mandate.mandate_details.revocable : false,
                    },
                    "umrn": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.umrn ? sendCreactionRequest.data.mandate.umrn : "",
                    "created_at": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.created_at ? new Date(sendCreactionRequest.data.mandate.created_at) : null,
                    "updated_at": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.updated_at ? new Date(sendCreactionRequest.data.mandate.updated_at) : null,
                    "mode": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.mode ? sendCreactionRequest.data.mandate.mode : "",
                    "auth_sub_mode": sendCreactionRequest.data && sendCreactionRequest.data.mandate && sendCreactionRequest.data.mandate.auth_sub_mode ? sendCreactionRequest.data.mandate.auth_sub_mode : "",
                },
                "customer": {
                    "name": sendCreactionRequest.data && sendCreactionRequest.data.customer && sendCreactionRequest.data.customer.name ? sendCreactionRequest.data.customer.name : "",
                    "destination_account_type": sendCreactionRequest.data && sendCreactionRequest.data.customer && sendCreactionRequest.data.customer.destination_account_type ? sendCreactionRequest.data.customer.destination_account_type : "",
                    "destination_ifsc": sendCreactionRequest.data && sendCreactionRequest.data.customer && sendCreactionRequest.data.customer.destination_ifsc ? sendCreactionRequest.data.customer.destination_ifsc : "",
                    "user_account_number": sendCreactionRequest.data && sendCreactionRequest.data.customer && sendCreactionRequest.data.customer.user_account_number ? sendCreactionRequest.data.customer.user_account_number : "",
                },
                "corporate": {
                    "name": sendCreactionRequest.data && sendCreactionRequest.data.corporate && sendCreactionRequest.data.corporate.name ? sendCreactionRequest.data.corporate.name : "",
                    "utility_code": sendCreactionRequest.data && sendCreactionRequest.data.corporate && sendCreactionRequest.data.corporate.utility_code ? sendCreactionRequest.data.corporate.utility_code : "",
                    "corporate_reference": sendCreactionRequest.data && sendCreactionRequest.data.corporate && sendCreactionRequest.data.corporate.corporate_reference ? sendCreactionRequest.data.corporate.corporate_reference : "",
                    "sponsor_ifsc": sendCreactionRequest.data && sendCreactionRequest.data.corporate && sendCreactionRequest.data.corporate.sponsor_ifsc ? sendCreactionRequest.data.corporate.sponsor_ifsc : "",
                    "sponsor_bank_code": sendCreactionRequest.data && sendCreactionRequest.data.corporate && sendCreactionRequest.data.corporate.sponsor_bank_code ? sendCreactionRequest.data.corporate.sponsor_bank_code : "",
                    "corporate_account_number": sendCreactionRequest.data && sendCreactionRequest.data.corporate && sendCreactionRequest.data.corporate.corporate_account_number ? sendCreactionRequest.data.corporate.corporate_account_number : "",
                },
                "partner": {
                    "name": sendCreactionRequest.data && sendCreactionRequest.data.partner && sendCreactionRequest.data.partner.name ? sendCreactionRequest.data.partner.name : "",
                    "identifier": sendCreactionRequest.data && sendCreactionRequest.data.partner && sendCreactionRequest.data.partner.identifier ? sendCreactionRequest.data.partner.identifier : "",
                },
                "details": {
                    "first_proposed_settlement_date": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.first_proposed_settlement_date ? new Date(sendCreactionRequest.data.details.first_proposed_settlement_date) : null,
                    "payment_count": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.payment_count ? sendCreactionRequest.data.details.payment_count : 0,
                    "paid_count": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.paid_count ? sendCreactionRequest.data.details.paid_count : 0,
                    "last_presented_at": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.last_presented_at ? new Date(sendCreactionRequest.data.details.last_presented_at) : null,
                    "last_presented_settlement_date": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.last_presented_settlement_date ? new Date(sendCreactionRequest.data.details.last_presented_settlement_date) : null,
                    "frequency": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.frequency ? sendCreactionRequest.data.details.frequency : "",
                    "present_settlement_before_days": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.present_settlement_before_days ? sendCreactionRequest.data.details.present_settlement_before_days : 0,
                    "narration": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.narration ? sendCreactionRequest.data.details.narration : "",
                    "amount_in_paise": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.amount_in_paise ? sendCreactionRequest.data.details.amount_in_paise : 0,
                    "ended_at": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.ended_at ? new Date(sendCreactionRequest.data.details.ended_at) : null,
                    "mandate_id": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.mandate_id ? sendCreactionRequest.data.details.mandate_id : "",
                    "client_ref_id": sendCreactionRequest.data && sendCreactionRequest.data.details && sendCreactionRequest.data.details.client_ref_id ? sendCreactionRequest.data.details.client_ref_id : "",
                },
                "created_at": sendCreactionRequest.data && sendCreactionRequest.data.created_at ? new Date(sendCreactionRequest.data.created_at) : null,
                "sponsor_bank_name": sendCreactionRequest.data && sendCreactionRequest.data.sponsor_bank_name ? sendCreactionRequest.data.sponsor_bank_name : "",
                "payment_status": "new",
                "settlementDoneOn": null,
                "failure_reason": null,
                "failure_description": null,
                "total_payment_count": null,
                "processed_payment_count": null,
                "lastEventCalled": null,
                "lastEventCalledAt": null,
                "userId": loan.userId,
                "loanId": loan._id,
                "partnerId": loan.partnerId,
                "retryCount": 0,
                "sendingState": "Send",
                "unique_key": key,
                "insertedOn": Date.now(),
                "transaction_id": null,
                "emiAmount": totalEmiAmount ? totalEmiAmount : 0,
                "penalty": totalPenalty ? totalPenalty : 0,
                "moratoriumInterest": totalMonotoriumInterest ? totalMonotoriumInterest : 0,
                "presentationFine": 0,
                "totalAmount": totalCollectionAmount,
                "settlementDate": emiDate,
                "debitResponseReceived": false,
                "emis": emiList,
                "collectionType": "Bulk",
                "insertedBy": adminId
            }

            let saveCollection = new DigioCollectionSchedule(emiSaveData);

            await saveCollection.save();

            //PAYMENT COLLECTION
            let installmentDetailsListHash = {};
            let emiFeesHash = {};

            if (installmentDetailsList && installmentDetailsList.data) {
                for (let i = 0; i < installmentDetailsList.data.length; i++) {
                    installmentDetailsListHash[installmentDetailsList.data[i].installmentNumber] = installmentDetailsList.data[i]
                }
            }

            if (emiFees && emiFees.data) {
                for (let i = 0; i < emiFees.data.length; i++) {

                    if (emiFeesHash[emiFees.data[i].installmentNumber]) {
                        emiFeesHash[emiFees.data[i].installmentNumber].push(emiFees.data[i]);
                    } else
                        emiFeesHash[emiFees.data[i].installmentNumber] = [emiFees.data[i]]
                }
            }

            let paymentCollection = []
            for (let i = 0; i < emiList.length; i++) {
                paymentCollection.push({
                    loan,
                    installment: installmentDetailsListHash[emiList[i]],
                    fees: emiFeesHash[emiList[i]],
                    partnerResponse: sendCreactionRequest,
                    uniqueKey: key,
                    presentationFine: 0,
                    collectionType: PAYMENT_COLLECTION_TYPE.BULK
                })
            }

            let { error: paymentCollectionError, response: paymentCollectionResponse } = await handleInsertENachForPaymentCollection(paymentCollection)

            if (paymentCollectionError) {
                return { data: "", success: false, message: "Create payment collection failed" };
            }

            for (const emiItem of emiRequestList) {

                emiItem.refId = sendCreactionRequest.data && sendCreactionRequest.data.id ? sendCreactionRequest.data.id : "";

                const emiRequest = new EmiRequest(emiItem);

                await emiRequest.save();

            }

            return { data: "", success: true, message: "Collection saved" };

        }
        else {
            return sendCreactionRequest;
        }
    }
    catch (ex) {
        saveErrorLog("sendScheduleRequestLoanWise", apiPath, { loanId: loan._id, emiList, adminId }, { error: ex.message, stackTrace: ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex });

        let log = new DigioSchedulerLog({
            userId: loan.userId,
            loanId: loan._id,
            event: "error",
            error: ex.message,
            insertedOn: Date.now()
        });

        await log.save();

        return { data: ex.message, success: false, message: ex.message };

    }

}

module.exports.bulkUpdateExistingSchedule = async function () {
    try {
        const schedule = await DigioCollectionSchedule.find({ emis: null });

        let scheduleCount = 0;
        let emiRequestCount = 0;

        for (const item of schedule) {

            item.emis = [item.emiNumber];

            const emi = await EmiRequest.findOne({ userId: item.userId, loanId: item.loanId, refId: item.refId, emiNumber: item.emiNumber });

            if (!emi) {
                const emiRequest = new EmiRequest({
                    userId: item.userId,
                    loanId: item.loanId,
                    refId: item.id,
                    emiNumber: item.emiNumber,
                    totalAmount: item.totalAmount,
                    emiAmount: item.emiAmount,
                    penalty: item.penalty,
                    monotoriumInterest: item.moratoriumInterest,
                    insertedOn: Date.now()

                });

                await emiRequest.save();
                emiRequestCount += 1;
            }

            await item.save();
            scheduleCount += 1;

        }

        return {
            data: "",
            success: true,
            message: `${scheduleCount} Digio Collection Schedule records and ${emiRequestCount} Emi Request records updated`,
            status: 200
        }

    }
    catch (ex) {
        console.log(ex);
        return {
            data: "",
            success: false,
            message: ex.message,
            status: 200
        }
    }
}

module.exports.cancelExistingMandate = async function (mandate) {
    try {

        if (!mandate)
            return {
                data: "",
                success: false,
                message: "No mandate found for this umrn number",
                status: 200
            }

        let mobileNo = "";

        if (user)
            mobileNo = user.userDetails && user.userDetails.mobileNo && user.userDetails.mobileNo[0] ? user.userDetails.mobileNo[0].mobileNumber : ""; // mobile number change 


        let cancellationModel = {
            "umrn": mandate.umrn ? mandate.umrn : "",
            "dest_ifsc": mandate.mandateDetails && mandate.mandateDetails.mandate_data && mandate.mandateDetails.mandate_data.destination_bank_id ? mandate.mandateDetails.mandate_data.destination_bank_id : ""
        };

        let sendCancellationRequest = await sendMandateCancelRequest(cancellationModel, mandate.userId, mandate.loanIds);

        if (sendCancellationRequest.success) {

            mandate.cancellationRequestSubmitted = true;
            mandate.cancelledBy = "";
            mandate.cancelledOn = null;
            mandate.cancellIntiatedOn = Date.now();
            mandate.cancellationConfirmed = false;
            mandate.cancellationErrorMessage = "";

            await mandate.save();

            return {
                data: "",
                success: true,
                message: sendCancellationRequest.message,
                status: 200
            }
        }
        else {
            return {
                data: "",
                success: false,
                message: sendCancellationRequest.message,
                status: 200
            }
        }

    }
    catch (ex) {
        saveErrorLog("cancelMandate", apiPath, { model }, { error: ex.message, stackTrace: ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex });

        return { data: ex.message, success: false, message: ex.message, status: 200 };
    }
}

async function updateMandateLogs(mandate, loanId) {
    try {
        const loan = await LoanInfo.findById(loanId);

        if (!loan) {
            return {
                data: "",
                success: false,
                message: "Loan info not found",
                status: 200,
            }
        }

        const nachResponseLog = await DigioNachResponseLog.findOne({ userId: loan.userId, mandate_id: mandate.id });

        if (nachResponseLog) {
            if (!nachResponseLog.loanIds.includes(loan._id)) {
                nachResponseLog.loanIds.push(loan._id);
                await nachResponseLog.save();
            }
        }

        const nachWebhookResponseLog = await DigioNachWebHookResponse.findOne({ userId: loan.userId, "api_mandate.id": mandate.id });

        if (nachWebhookResponseLog) {
            if (!nachWebhookResponseLog.loanIds.includes(loan._id)) {
                nachWebhookResponseLog.loanIds.push(loan._id);
                await nachWebhookResponseLog.save();
            }
        }

        const nachMandateLog = await DigioMandateLog.findOne({ userId: loan.userId, mandateId: mandate.id });

        if (nachMandateLog) {
            if (!nachMandateLog.loanIds.includes(loan._id)) {
                nachMandateLog.loanIds.push(loan._id);
                await nachMandateLog.save();
            }
        }

        return {
            data: "",
            success: true,
            message: "Saved successfully",
            status: 200,
        };
    }
    catch (ex) {
        saveErrorLog("updateMandateLogs", apiPath, { mandate, loanId }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        return {
            data: "",
            success: false,
            message: ex.message,
            status: 200,
        };
    }

}

async function unholdWalletCollections(loanId) {
    try {
        const loan = await LoanInfo.findById(loanId);

        const loanInstallments = await installmentService.getInstallments(loanId, null, "Pending");
        if (!loanInstallments.success) {
            return {
                data: "",
                success: false,
                message: loanInstallments.message
            }
        } else if (loanInstallments.data == null) {
            return {
                data: "",
                success: false,
                message: "No Pending Installments Found"
            }
        } else if (loanInstallments.data != null || loanInstallments.data.length > 0) {
            const uniquePendingInstallmentNos = [...new Set(loanInstallments.data.map(item => item.installmentNumber))];
            const minInstallment = Math.min(...uniquePendingInstallmentNos);

            const todayDate = new Date();

            const today = new Date(
                todayDate.getFullYear(),
                todayDate.getMonth(),
                todayDate.getDate(), 5, 30, 0, 0
            );

            const defaultDays = config.get("digio.eNach.defaultDays");

            let unholdCollection = false;
            const leastPendingInstallment = loanInstallments.data.filter((x) => x.installmentNumber == minInstallment);

            const overDueByDays = new DateDiff(today, leastPendingInstallment[0].dueDate).days();

            // if (overDueByDays >= defaultDays)
            //     unholdCollection = false;

            let holdInstallments = [];
            for (const installment of loanInstallments.data) {
                if (installment.holdCollectionViaWallet === true) holdInstallments.push(installment.installmentNumber);
            }

            if (holdInstallments.length > 0) {
                const responseHoldCollection = await installmentService.holdCollectionViaWallet([loanId], unholdCollection, holdInstallments, "System");

                if (responseHoldCollection.success) {
                    return {
                        data: "",
                        success: true,
                        message: "Updated successfully"
                    }
                } else {
                    return {
                        data: "",
                        success: false,
                        message: responseHoldCollection.message
                    }
                }
            }
        }
    }
    catch (ex) {
        saveErrorLog("unholdWalletCollections", apiPath, { loanId }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        return {
            data: "",
            success: false,
            message: ex.message
        }
    }
}

module.exports.updateMandateLogs = updateMandateLogs;

module.exports.sendEnachReassignmentRequestV2 = async function (loanId, mailModel, adminId) {
    try {

        const loan = await LoanInfo.findById(loanId);

        const user = await UserInfo.findById(loan.userId);

        const partnerMaster = await PartnerMaster.findById(loan.partnerId);

        let oldMandate = await DigioMandateGeneration.findOne({ loanIds: loan._id, isActive: true, status: "register_success" });

        if (user) {

            //call e-mandate
            if (oldMandate) {
                let longUrl = "", shortUrl = "";

                let internalLink = `${config.get("keys.editBankLink")}/${loan._id.toString()}?isEdit=true`

                longUrl = internalLink;

                const eNachCode = config.get("shortUrl.eNachCode");

                const shortUrlResponse = await generateCustomUrl(internalLink, eNachCode, loan.userId, loan._id, loan.loanNo, loan.merchantCode, partnerMaster.displayName);

                if (shortUrlResponse.success) {
                    internalLink = shortUrlResponse.data
                    shortUrl = shortUrlResponse.data;
                }

                mailModel.link = "<a href='" + shortUrl + "'>Link</a>";

                //Send email
                await sendMail(user._id, loanId, "Emails to Borrower", "AutoPay Consent Request", mailModel, "User");

                loan.loanContractDetails.contractUrl = shortUrl;
                loan.loanContractDetails.eNachUrl = shortUrl;
                loan.loanContractDetails.redirectionLongUrl = longUrl;

                loan.isForceMandate = true;

                await loan.save();

                return {
                    data: "",
                    success: true,
                    existingMandate: false,
                    message: "Mandate reassignment link sent",
                    status: 200
                }
            }
            else {
                const existingMandate = await DigioMandateGeneration.findOne({
                    userId: loan.userId,
                    partnerId: loan.partnerId,
                    isActive: true,
                    status: "register_success"
                })
                    .sort({ "mandateDetails.mandate_data.maximum_amount": -1, createdOn: -1 });

                const checkMandateDetails = await checkMandateCurrentBankDetails(existingMandate, loan._id);

                if (checkMandateDetails.success && existingMandate) {
                    if (!existingMandate.loanIds.includes(loan._id)) {
                        existingMandate.loanIds.push(loan._id);
                        existingMandate.updatedOn = Date.now();
                        await existingMandate.save();
                    }

                    loan.secondaryCollectionType = "eNach";
                    loan.loanContractDetails.eNachUrl = loan.loanContractDetails.contractUrl;

                    await loan.save();

                    updateMandateLogs(existingMandate, loan._id);

                    return {
                        data: "",
                        success: true,
                        existingMandate: true,
                        message: "Loan has been assigned to existing mandate",
                        status: 200
                    }
                }
                else {

                    let longUrl = "", shortUrl = "";

                    let internalLink = `${config.get("keys.editBankLink")}/${loan._id.toString()}?isEdit=true`

                    longUrl = internalLink;

                    const eNachCode = config.get("shortUrl.eNachCode");
                    const shortUrlResponse = await generateCustomUrl(internalLink, eNachCode, loan.userId, loan._id, loan.loanNo, loan.merchantCode, partnerMaster.displayName);

                    if (shortUrlResponse.success) {
                        internalLink = shortUrlResponse.data
                        shortUrl = shortUrlResponse.data;
                    }

                    mailModel.link = "<a href='" + shortUrl + "'>Link</a>";

                    //Send email
                    await sendMail(user._id, loanId, "Emails to Borrower", "AutoPay Consent Request", mailModel, "User");

                    loan.loanContractDetails.contractUrl = shortUrl;
                    loan.loanContractDetails.eNachUrl = shortUrl;
                    loan.loanContractDetails.redirectionLongUrl = longUrl;

                    loan.isForceMandate = true;

                    await loan.save();

                    return {
                        data: "",
                        success: true,
                        existingMandate: false,
                        message: "Mandate reassignment link sent",
                        status: 200
                    }
                }
            }

        } else {
            return {
                data: "",
                success: false,
                message: "No user found",
                status: 200
            }
        }
    }
    catch (ex) {
        saveErrorLog("sendEnachReassignmentRequest", apiPath, { loanId, mailModel, adminId }, { error: ex.message, stackTrace: ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex });

        return { data: ex.message, success: false, message: ex.message, status: 200 };
    }

}

async function generatePhysicalNachFile(loanId, req) {
    try {
        let loan = await LoanInfo.findById(loanId);

        if (!loan) return { data: null, success: false, message: "No loan found", status: 200 };

        if (req && req.user.role != ROLES.ADMIN && loan.partnerId.toString() != req.user.partnerId) {
            return { data: null, success: false, message: "Access Denied", status: 400 };
        }

        const staticPath = "ProofImages/" + loan.userId;

        const authMode = loan.loanContractDetails.authMode;

        const mandate = await DigioMandateGeneration.findOne({ loanIds: loanId, userId: loan.userId, partnerId: loan.partnerId, mode: authMode }).sort({ createdOn: -1 });

        if (!mandate) return { data: null, success: false, message: "No mandate found", status: 200 };

        if (mandate.status === "success" || mandate.status === "register_success")
            return { data: null, success: true, message: "Mandate already signed", status: 200 };

        const url = `v3/client/mandate/${mandate.id}/generate_file`;

        const response = await callDigioApi("POST", url, {}, "arraybuffer");

        //fs.writeFileSync(config.get("exportToExcelPath.generalReports") + pNachFileName, response);

        const pNachPath = `${staticPath}/Mandate/pNach_${loan.loanNo}.pdf`;

        await AWS.uploadToS3(pNachPath, response, "BUFFER");

        return { data: { pNachPath, pNachFileName: `pNach_${loan.loanNo}.pdf` }, success: true, message: "File generated successfully", status: 200 };

    }
    catch (ex) {
        saveErrorLog("generatePhysicalNachFile", apiPath, loanId, { error: ex.message, stackTrace: ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex });

        return { data: ex.message, success: false, message: ex.message, status: 200 };
    }
}

module.exports.generatePhysicalNachFile = generatePhysicalNachFile;

module.exports.uploadPhysicalNachFile = async function (loanId, pNachFile, pNachFileStream, uploadedBy = null, req) {
    try {
        let loan = await LoanInfo.findById(loanId);

        if (!loan) return { data: null, success: false, message: "No loan found", status: 200 };

        /** ROLE CHECK */
        if (req && req.user.role != ROLES.ADMIN) {

            if (req.user.role == ROLES.USER && loan.userId.toString() != req.user.id) {
                return { data: null, success: false, message: "Access Denied", status: 400 };
            } else if (req.user.role == ROLES.BORROWING_PARTNER && loan.partnerId.toString() != req.user.partnerId) {
                return { data: null, success: false, message: "Access Denied", status: 400 };
            } else if (req.user.role == ROLES.LENDING_PARTNER && (!lenderDetails || !lenderDetails.length || lenderDetails[0].lenderPartnerId.toString() != req.user.partnerId)) {
                return { data: null, success: false, message: "Access Denied", status: 400 };
            }
        }

        const nachAvailable = await isPnachAvailable(loan._id);

        if (!nachAvailable)
            return { data: null, success: false, message: "Physical NACH not found", status: 200 };

        const authMode = loan.loanContractDetails.authMode;

        const mandate = await DigioMandateGeneration.findOne({ loanIds: loanId, userId: loan.userId, partnerId: loan.partnerId, mode: authMode }).sort({ createdOn: -1 });

        if (!mandate) return { data: null, success: false, message: "No mandate found", status: 200 };

        if (mandate.status === "success" || mandate.status === "register_success")
            return { data: null, success: true, message: "Mandate already signed", status: 200 };

        const staticPath = "ProofImages/" + loan.userId;

        const request = {
            "id": mandate.id ? mandate.id : "",
            "file_data": pNachFileStream.replace(/^data:.+;base64,/, "")
        };

        const url = `v3/client/mandate/submit_signed_mandate_raw`;

        const response = await callDigioApi("POST", url, request);

        if (response.success) {

            let { error: validateError, response: validateRes } = validateFile(FILE_UPLOAD_MODULES.BANK_DETAILS_UPLOAD, FILE_UPLOAD_TYPES.ENACH_IMAGE, pNachFileStream, getExtension(pNachFile));

            if (validateError) {
                return {
                    data: "",
                    success: false,
                    message: validateError.message,
                    status: 400
                };
            }

            const pNachFileName = `pNach_${loan.loanNo}${getExtension(pNachFile)}`;

            const pNachPath = `${staticPath}/Mandate/${pNachFileName}`;

            await AWS.uploadToS3(pNachPath, pNachFileStream);

            if (mandate.status !== "register_success" && mandate.status !== "success") {
                mandate.status = "success";
                mandate.uploadedBy = uploadedBy;
                await mandate.save();
            }

            loan.loanContractDetails.pNachFile = pNachPath;
            await loan.save();

            return { data: "", success: true, message: "Document uploaded successfully", status: 200 };
        }
        else
            return { data: null, success: false, message: response.message, status: 200 };
    }
    catch (ex) {

        saveErrorLog("uploadPhysicalNachFile", apiPath, loanId, { error: ex.message, stackTrace: ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex });

        return { data: ex.response && ex.response.data && ex.response.data.message ? ex.response.data.message : ex.message, success: false, message: ex.response && ex.response.data && ex.response.data.message ? ex.response.data.message : ex.message, status: 200 };
    }
}

module.exports.sendPhysicalNACHLink = async (loanId, req) => {
    try {
        const isNachAvailable = await isPnachAvailable(loanId);

        if (!isNachAvailable) return { data: "", success: false, message: "Physical NACH not available" };

        const loan = await LoanInfo.findById(loanId);

        if (!loan) {
            return { data: "", success: false, message: "Physical NACH not available" };
        }

        /** ROLES CHECK */
        if (req && req.user.role != ROLES.ADMIN) {
            if (req.user.role == ROLES.USER && (loan.userId.toString() != req.user.id)) {
                return { data: null, success: false, message: "Access Denied" };
            } else if (req.user.role == ROLES.BORROWING_PARTNER && loan.partnerId.toString() != req.user.partnerId) {
                return { data: null, success: false, message: "Access Denied" };
            } else if (req.user.role == ROLES.LENDING_PARTNER && (!lenderDetails || !lenderDetails.length || lenderDetails[0].lenderPartnerId.toString() != req.user.partnerId)) {
                return { data: null, success: false, message: "Access Denied" };
            }
        }


        const user = await UserInfo.findById(loan.userId);

        const partner = await PartnerMaster.findById(loan.partnerId);

        const generateUrl = await generatePhysicalNachFile(loanId);

        if (!generateUrl.success)
            return generateUrl;

        const downloadFilePath = getSignedUrlToDownload({ url: generateUrl.data.pNachPath });

        const notificationTemplate = await NotificationTemplates.findOne({ channel: "WhatsApp", partnerId: loan.partnerId, template: "physical_nach_link" });

        let internalLink = `${config.get("keys.editBankLink")}/${loan._id.toString()}?mandate=true`;

        const eNachCode = config.get("shortUrl.eNachCode");
        const shortUrlResponse = await generateCustomUrl(internalLink, eNachCode, loan.userId, loan._id, loan.loanNo, loan.merchantCode, partner.displayName, null);

        if (shortUrlResponse.success)
            internalLink = shortUrlResponse.data;

        let smsModel = getSmsModel();

        smsModel.To = user.userDetails.mobileNo[0] ? user.userDetails.mobileNo[0].mobileNumber : ""; // mobile number change 
        smsModel.Type = notificationTemplate.type;
        smsModel.From = config.get("keys.from");

        smsModel.TemplateName = notificationTemplate.whatsAppTemplate;
        smsModel.VAR1 = `${user.userDetails.firstName} ${user.userDetails.lastName}`;
        smsModel.VAR2 = `${loan.amount}`;
        smsModel.VAR3 = partner.brandName;
        smsModel.VAR4 = internalLink;
        smsModel.VAR5 = config.get("karix.ffSupportLink");
        smsModel.CTA = `${internalLink}?c=wa`.replace(config.get("tinyUrl.urlPrefix"), "");
        smsModel.Attachment1 = downloadFilePath;

        const whatsAppResponse = await pushToNotifications(loan.partnerId, loan.merchantCode, loan.productId, loan.trxnId,
            loan.userId, loan._id, "WhatsApp", user.userDetails.mobileNo[0].mobileNumber, "physical_nach_link", null, null, null, smsModel, false, null, "primary");// mobile number change 

        if (whatsAppResponse.success)
            await sendNotifications(whatsAppResponse.data._id);

        return {
            data: "",
            success: true,
            message: "Link sent successfully",
            status: 200
        }

    }
    catch (ex) {
        saveErrorLog("sendPhysicalNACHLink", apiPath, loanId, { error: ex.message, stackTrace: ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex });

        return { data: null, success: false, message: ex.response && ex.response.data && ex.response.data.message ? ex.response.data.message : ex.message, status: 200 };

    }
}

module.exports.getMandateDetails = async function (mandateId) {
    try {
        const url = `v3/client/mandate/${mandateId}`;
        const response = await callDigioApi("POST", url, request);

        if (response)
            return { data: response, success: true, message: "record found", status: 200 };
        else
            return { data: null, success: false, message: response.message, status: 200 };
    }
    catch (ex) {

        saveErrorLog("getMandateDetails", apiPath, mandateId, { error: ex.message, stackTrace: ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex });

        return { data: null, success: false, message: ex.message, status: 200 };
    }
}


module.exports.cancelMandateJob = async function () {

    let mandates = await DigioMandateGeneration.find({ isActive: false, status: "register_success", umrn: { $nin: [null, ""] }, cancellationRequestSubmitted: { $ne: true } })

    for (let i = 0; i < mandates.length; i++) {
        let mandate = mandates[i]
        let result = await cancelMandate({ umrn: mandate.umrn }, true, true)

        console.log(`Total: ${mandates.length}, Completed: ${i + 1} --  ${result.message}`)
        if (result.message == "Request Initiated") return
        if ((i % 100) == 0) await sleep(3000)
    }

}
