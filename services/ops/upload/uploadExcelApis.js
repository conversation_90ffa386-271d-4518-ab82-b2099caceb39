const mongoose = require("mongoose");
const debug = require("debug")("app:uploadExcel");
const multer = require('multer');
const fs = require("fs");
const config = require("config");
const IsNullEmpty = require("isnullemptyorwhitespace");
const xlsxFile = require('read-excel-file/node');
const { groupBy } = require("lodash");

const { LoanInfo } = require("../../../models/ops/loan/loan");
const { getMultipleLoansInstallments, updateEmiPaidStatus } = require("../../../services/ops/loan/installments");
const { DigioCollectionSchedule } = require("../../../models/ops/digio/digioApi");
const { insertSchedular } = require("../../../services/ops/loan/postDisbursal");
const { saveErrorLog } = require("../../../services/ops/common/masters");
const Logger = require("../../../middleware/logger");
const logger = new Logger();

const apiPath = __dirname;

exports.uploadExcel = async function (req, res) {
    let output = {
        success: false,
        message: "Error Occurred",
        status: 200
    }
    return new Promise((resolve) => {
        try {
            let path = config.get("keys.tempPath");
            let fileName = "";
            let filePath = "";
            let fileOriginalName = "";
            let extension = "";
            let storage = multer.diskStorage({
                destination: function (req, file, cb) {
                    cb(null, path);
                },
                filename: function (req, file, cb) {
                    let i = 1;
                    fileOriginalName = file.originalname;
                    extension = file.originalname.substring(file.originalname.lastIndexOf('.') - 1 + 1);
                    fileName = file.originalname.substring(0, file.originalname.lastIndexOf('.'));
                    filePath = path + "//" + fileName + extension;

                    while (fs.existsSync(filePath)) {
                        fileName = file.originalname.substring(0, file.originalname.lastIndexOf('.')) + "_" + i.toString();
                        filePath = path + "//" + fileName + extension;
                        i++;
                    }
                    cb(null, fileName + extension);
                }
            });
            let upload = multer({ storage: storage }).single('file')

            upload(req, res, async function (err) {
                if (err instanceof multer.MulterError) {
                    output.message = err;
                    resolve(output);
                } else if (err) {
                    output.message = err;
                    resolve(output);
                } else {
                    fileName = req.body.type + "_" + req.body.userId;
                    let NewfilePath = path + "//" + fileName + extension;
                    let i = 1;
                    //logger.logs("filePath", filePath);

                    while (!fs.existsSync(filePath)) {
                        await sleep(5000);
                    }

                    while (fs.existsSync(NewfilePath)) {
                        const NewfileName = fileName + "_" + i.toString();
                        NewfilePath = path + "//" + NewfileName + extension;
                        i++;
                    }

                    let response = null;
                    await insertSchedular("Upload Excel - " + req.body.type, "File uploaded on server", null);
                    fs.rename(filePath, NewfilePath, async () => {
                        await insertSchedular("Upload Excel - " + req.body.type, "Verify Excel File Started", null);
                        response = await verifyExcelFile(req.body.type, req.body.partnerId, NewfilePath);
                        logger.logs("verifyExcelFile response", response);
                        if (response.success) {
                            await insertSchedular("Upload Excel - " + req.body.type, "Verify Excel File Completed", null);
                            await insertSchedular("Upload Excel - " + req.body.type, "Upload Excel File to Db Started", null);
                            response = await uploadExcelFileData(req.body.userId, req.body.partnerId, req.body.type, response.data);
                            logger.logs("uploadExcelFileData response", response);
                            if (response.success) {
                                output = {
                                    success: true,
                                    message: "uploaded successfully",
                                    status: 200
                                }
                                resolve(output);
                            } else {
                                output.message = response.message;
                                await insertSchedular("Upload Excel - " + req.body.type, "Upload Excel File Completed With Error", output);

                                resolve(output);
                            }
                        } else {
                            output.message = response.message;
                            await insertSchedular("Upload Excel - " + req.body.type, "Verify Excel File Completed With Error", output);
                            resolve(output);
                        }

                    });
                }
            });
        }
        catch (ex) {
            logger.logs(ex);
            saveErrorLog("uploadExcel", apiPath, null, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
            output.message = ex.message;
            resolve(output);
        }
    });
};

async function verifyExcelFile(type, partnerId, path) {
    let output = {
        data: null,
        success: false,
        message: "",
        status: 200
    }
    //logger.logs("started verfiy excel file");
    return new Promise(async (resolve) => {
        try {
            const defaultMessage = "Loan Installment Collection Changes Pending";
            if (type === "Upload_Received_Emis") {
                output = {
                    data: null,
                    success: false,
                    message: defaultMessage,
                    status: 200
                }

                const excelSheet = 'Sheet1';

                //Check Sheets
                await xlsxFile(path, { getSheets: true }).then(async (sheets) => {
                    if (sheets.length > 1) {
                        output.message = "Please provide valid Excel";
                        fs.unlinkSync(path);
                        resolve(output);
                    } else {
                        sheets.forEach((obj) => {
                            if (excelSheet != obj.name) {
                                output.message = "Please provide valid Excel";
                                fs.unlinkSync(path);
                                resolve(output);
                            }
                        });
                    }
                });

                let isValidationFailed = false;

                const sheet_rows = ["Loan_No", "Emi_No", "Received_On", "Amount"];

                await xlsxFile(path, { sheet: excelSheet }).then((rows) => {
                    for (let j = 0; j < rows[0].length; j++) {
                        if (sheet_rows[j] !== rows[0][j]) {
                            output.message = "Column " + sheet_rows[j] + " in sheet \"" + excelSheet + "\" is missing";
                            j = rows[0].length;
                            isValidationFailed = true;
                        }
                    }
                });

                if (isValidationFailed) {
                    resolve(output);
                }

                let Records = [];
                let LoanNos = [];

                await xlsxFile(path, { sheet: excelSheet }).then((rows) => {
                    for (let row = 1; row < rows.length; row++) {
                        const record = {
                            loanNo: rows[row][0].toString(),
                            emiNo: rows[row][1],
                            receivedOn: rows[row][2],
                            amount: rows[row][3]
                        }
                        Records.push(record);
                        LoanNos.push(rows[row][0].toString());
                    }
                });

                const uniqueLoanNos = [...new Set(LoanNos)];

                const today = Date.now();

                if (Records.length > 0) {
                    for (let row = 0; row < Records.length; row++) {
                        const actualRow = row + 1;
                        const receivedDate = Records[row]["receivedOn"].toString().split("-");
                        console.log(receivedDate);
                        console.log(receivedDate.length);
                        //Check Mandatory Fields
                        if (IsNullEmpty(Records[row]["loanNo"])) {
                            output.message = "field Loan_No in sheet \"Sheet1\" is Mandatory. Please check row no " + actualRow.toString();
                        } else if (IsNullEmpty(Records[row]["emiNo"])) {
                            output.message = "field Emi_No in sheet \"Sheet1\" is Mandatory. Please check row no " + actualRow.toString();
                        } else if (IsNullEmpty(Records[row]["receivedOn"])) {
                            output.message = "field Received_On in sheet \"Sheet1\" is Mandatory. Please check row no " + actualRow.toString();
                        } else if (receivedDate.length != 3) {
                            output.message = "field Received_On in sheet \"Sheet1\" does not have valid format. Please check row no " + actualRow.toString();
                        } else if (receivedDate[0].length != 2 || receivedDate[1].length != 2 || receivedDate[2].length != 4) {
                            output.message = "field Received_On in sheet \"Sheet1\" does not have valid format. Please check row no " + actualRow.toString();
                        }
                        else if (IsNullEmpty(Records[row]["amount"])) {
                            output.message = "field amount in sheet \"Sheet1\" is Mandatory. Please check row no " + actualRow.toString();
                        }
                        // else if (Object.prototype.toString.call(Records[row]["receivedOn"]) != '[object Date]') {
                        //     output.message = "field Received_On in sheet \"Sheet1\" does not have valid format. Please check row no " + actualRow.toString();
                        // }

                        if (output.message == "") {
                            // const receivedOn = new Date(Records[row]["receivedOn"].getFullYear(), Records[row]["receivedOn"].getMonth(), Records[row]["receivedOn"].getDate(), 1, 0, 0);
                            const receivedOn = new Date(receivedDate[2], receivedDate[1] - 1, receivedDate[0], 1, 0, 0);
                            if (receivedOn > new Date(today)) {
                                output.message = "field Received_On in sheet \"Sheet1\" should be less than current Date";
                            }
                        }

                        if (output.message != defaultMessage) {
                            isValidationFailed = true;
                            row = Records.length;
                        }
                    }
                } else {
                    output.message = "Please provide records";
                    isValidationFailed = true;
                }

                if (isValidationFailed) {
                    resolve(output);
                }

                if (uniqueLoanNos.length > 0) {
                    const loans = await LoanInfo.find({ loanNo: { $in: uniqueLoanNos }, partnerId: mongoose.Types.ObjectId(partnerId) }).select({ _id: 1, loanNo: 1 });

                    const loanHash = groupBy(loans, "loanNo");

                    for (let j = 0; j < Records.length; j++) {
                        const loanDetails = loanHash[Records[j].loanNo.toString()][0];
                        //logger.logs("loanDetails", loanDetails);

                        if (loanDetails != null && loanDetails != "") {
                            const digioCollectionSchedule = await DigioCollectionSchedule.findOne({ loanId: mongoose.Types.ObjectId(loanDetails._id), emis: Records[j].emiNo, sendingState: "Send" });

                            if (!IsNullEmpty(digioCollectionSchedule)) {
                                output.message = "Can not update status for emi no " + Records[j].emiNo.toString() + " of loan " + Records[j].loanNo + " as it is sent for eNach Collection";
                                j = Records.length;
                                resolve(output);
                            }
                        } else {
                            output.message = "loan no " + NonExistLoans.join(",") + " not found for selected Partner";
                            resolve(output);
                        }
                    }
                }

                const data = {
                    uniqueLoanNos: uniqueLoanNos,
                    Records: Records
                };

                output = {
                    data: data,
                    success: true,
                    message: "Verified",
                    status: 200
                };

                resolve(output);
            }
        } catch (ex) {
            logger.logs(ex);
            saveErrorLog("verifyExcelFile", apiPath, null, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
            output.message = ex.message;
            resolve(output);
        }
    });
};

async function uploadExcelFileData(userId, partnerId, type, userData) {
    let output = {
        success: false,
        message: "Error Occurred",
        status: 200
    }
    const today = new Date();
    return new Promise(async (resolve) => {
        try {
            if (type === "Upload_Received_Emis") {
                const uniqueLoanNos = userData.uniqueLoanNos;
                const Records = userData.Records;

                const loanStatus = ["Pending"];

                const allInstallments = await getMultipleLoansInstallments(null, uniqueLoanNos, loanStatus, null);

                if (allInstallments.success) {
                    if (allInstallments.data.length > 0) {

                        const loans = await LoanInfo.find({ loanNo: { $in: uniqueLoanNos }, partnerId: mongoose.Types.ObjectId(partnerId) }).select({ _id: 1, loanNo: 1 });

                        const installmentHash = groupBy(allInstallments.data, "loanNo");
                        const loanHash = groupBy(loans, "loanNo");

                        for (let i = 0; i < uniqueLoanNos.length; i++) {
                            const loan = loanHash[uniqueLoanNos[i].toString()][0];
                            const installments = installmentHash[uniqueLoanNos[i].toString()];

                            const excelRecords = Records.filter((x) => x.loanNo == uniqueLoanNos[i]);

                            if (excelRecords.length > 0) {
                                for (let emi = 0; emi < excelRecords.length; emi++) {
                                    const emis = installments.filter(x => x.installmentNumber == parseInt(excelRecords[emi]["emiNo"]));

                                    if (emis.length > 0) {
                                        // const receivedOn = new Date(excelRecords[emi]["receivedOn"].getFullYear(), excelRecords[emi]["receivedOn"].getMonth(), excelRecords[emi]["receivedOn"].getDate(), today.getHours(), today.getMinutes(), today.getSeconds());

                                        const receivedDate = excelRecords[emi]["receivedOn"].toString().trim().split("-");

                                        const receivedOn = new Date(receivedDate[2], receivedDate[1] - 1, receivedDate[0], today.getHours(), today.getMinutes(), today.getSeconds());


                                        const updateEmiStatus = await updateEmiPaidStatus(loan._id, emis[0].installmentNumber, excelRecords[emi].amount, receivedOn, "Excel_Upload", userId);
                                    } else {
                                        output.message = `Emi No ${excelRecords[emi]["emiNo"]} of loan no ${uniqueLoanNos[i]} is not in Pending State`;
                                        emi = excelRecords.length;
                                        resolve(output);
                                    }
                                }
                            }
                        }

                        output = {
                            success: true,
                            message: "Uploaded into database",
                            status: 200
                        }

                    } else output.message = "No Pending Installments Found";
                } else output.message = allInstallments.message;
                resolve(output);
            }
        }
        catch (ex) {
            logger.logs(ex);
            saveErrorLog("uploadExcelFileData", apiPath, null, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
            output.message = ex.message;
            resolve(output);
        }
    });
};