const mongoose = require("mongoose");
const apiPath = __dirname;

const { LoanInfo, LoanInstallments, LoanFees } = require("../../../models/ops/loan/loan");
const { PartnerMaster, NotificationsList, ConfigParameters } = require("../../../models/ops/partner/partner");
const { UserInfo, UserPartnerInfo } = require("../../../models/ops/registration/users");
const _ = require("lodash");
const { generateUpiLink, cancelPaymentLink, resendPaymentLink } = require("../admin/admin");
const { AgentCollections, AgentActivity } = require("../../../models/ops/agent/agent");
const { PaymentCollection } = require("../../../models/ops/loan/postDisbursal");
const { sendOtp, verifyOtp } = require("../otp-service/otp-service");
const { OtpInfo } = require("../../../models/ops/common/master");
const { sendNotifications } = require("../loan/postDisbursal");
const { getSmsModel, pushToNotifications, getSessionExpireTime, sleep } = require("../common/common");
const config = require("config");
const output = require("sharp/lib/output");
const { getPartnerConfig } = require("../common/DbDocuments");
const { PartnerRoles } = require("../../../models/ops/registration/partner-roles");
const { saveErrorLog } = require("../common/masters");
const { getSignedUrlToDownload } = require("../../../models/ops/aws/s3");
const Logger = require("../../../middleware/logger");
const logger = new Logger();
const debug = require("debug")("app:admin");
const { PAYMENT_CHANNEL } = require('../../ops/common/constants').Constants;


module.exports.sendOtpForLogin = async (model, req) => {
    let output = {
        data: {},
        success: false,
        message: 'Some Error Occurred',
        status: 501
    };

    try {
        let body = model

        let mobileNumber = body.mobileNumber ? body.mobileNumber.trim().toUpperCase() : "";

        if (!mobileNumber) {
            return output;
        }

        let userQuery = {
            "userDetails.mobileNo.mobileNumber": mobileNumber,
            "userDetails.role": "Collection Agent"
        }

        let user = await UserInfo.findOne(userQuery).lean()

        if (!user) {
            return output;
        }


        let validationPayload = {
            notifyTo: mobileNumber,
            channel: "SMS",
            notificationType: "COLLECTION_OTP",
            otpLength: 6
        }

        let sendOtpRes = await sendOtp(validationPayload);

        if (sendOtpRes && sendOtpRes.error) {
            return output;
        }

        const otp = sendOtpRes.response.otp

        let payload = {
            mobileNo: [mobileNumber],
            userId: user._id,
            otp: otp,
            purpose: "COLLECTION_OTP",
            "channel": "SMS",
            insertedOn: new Date(),
            isActive: true
        }

        await OtpInfo.create(payload)

        let smsModel = getSmsModel();
        smsModel.To = mobileNumber;
        smsModel.TemplateName = "Collection_OTP";
        smsModel.Type = "TSMS";
        smsModel.From = config.get("keys.from");
        smsModel.VAR1 = otp
        smsModel.VAR2 = `${config.get("keys.webSubdomain")} #${otp}`

        const sendSms = await pushToNotifications(null, null, null, null, user._id, null, "SMS", mobileNumber, "Collection_OTP", "otp", null, null, smsModel, false, "TSMS", null, null, mobileNumber);

        //Send Notification
        if (sendSms.success) {
            const response = await sendNotifications(sendSms.data._id);

            output = {
                data: {},
                success: true,
                message: "Success",
                status: 200
            }
        }
    } catch (ex) {

        logger.logs(ex);
        saveErrorLog("sendAgentOtpForLogin", apiPath, {}, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }
    return output
}

module.exports.verifyOtpForLogin = async (req, res) => {
    let output = {
        data: {},
        success: false,
        message: 'Some Error Occurred',
        status: 501
    };

    try {

        let body = req.body

        let mobileNumber = body.mobileNumber ? body.mobileNumber.trim().toUpperCase() : "";
        let otp = body.otp ? body.otp.trim().toLowerCase() : "";

        if (!mobileNumber || !otp) {
            return output;
        }

        let userQuery = {
            "userDetails.mobileNo.mobileNumber": mobileNumber,
            "userDetails.role": "Collection Agent"
        }

        let user = await UserInfo.findOne(userQuery)
        if (!user) {
            return output;
        }

        let validationPayload = {
            notifyTo: mobileNumber,
            channel: "SMS",
            notificationType: "COLLECTION_OTP",
            otp: otp
        }

        let verifyOtpRes = await verifyOtp(validationPayload);

        if (verifyOtpRes && verifyOtpRes.error) {
            output.message = "Invalid OTP"
            return output
        }

        let optDoc = await OtpInfo.findOne({ mobileNo: mobileNumber, purpose: "COLLECTION_OTP" }).sort({ _id: -1 })

        if (!optDoc) {
            return output
        }


        if (!(optDoc && optDoc.otp == otp)) {
            output.message = "Invalid OTP"
            return output
        }

        let partner = null
        let name = "";

        let userPartnerInfo = await UserPartnerInfo.findOne({ isPartnerUser: true, userId: user._id });

        if (userPartnerInfo) {
            partner = await getPartnerConfig(null, userPartnerInfo.partnerId, null);
            if (partner) name = partner.data.partnerName;
        } else {
            partner = await getPartnerConfig(config.get("keys.partnerCode"), null, null);
            userPartnerInfo = await UserPartnerInfo.findOne({ userId: mongoose.Types.ObjectId(user._id), partnerId: mongoose.Types.ObjectId(partner.data._id) });
            if (userPartnerInfo) name = user.userDetails.firstName + " " + user.userDetails.lastName;
        }

        const token = user.generateAuthToken({ loggedInFrom: req.headers.loggedinfrom, partnerId: partner.data._id.toString() });

        let sessionExpireTime = getSessionExpireTime()
        res.cookie('x-auth-token', token, {
            domain: config.get("keys.cookieDomain"),
            httpOnly: true,
            expires: sessionExpireTime
        });

        let userRoles = user.userDetails.entitlementRole;

        let entitlements = [];
        let roles = [];

        if (userRoles) {

            for (const role of userRoles) {

                roles.push(role.role);
            }

            let partnerRoles = await PartnerRoles.find({ name: { $in: roles } }).exec();

            for (const ent of partnerRoles) {
                entitlements = [...entitlements, ...ent.entitlements];
            }
        }

        let responsePayload = {
            userId: user._id,
            name: name,
            role: user.userDetails.role,
            entitlements: entitlements,
            registeredFrom: user.userDetails.registeredFrom,
            imageUrl: user.userDetails.imageUrl,
            loggedInFrom: "Fundfina",
            token: config.get("keys.cookieDomain") == 'localhost' ? token : "",
        }

        output = {
            data: responsePayload,
            success: true,
            message: "Success",
            status: 200
        }
    } catch (ex) {

        logger.logs(ex);
        saveErrorLog("sendAgentOtpForLogin", apiPath, {}, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }

    return output
}

const getLoanDetail = async function (model, req) {
    let output = {
        data: {},
        success: false,
        message: 'Some Error Occurred',
        status: 501
    };

    const loanNo = model.loanNo;
    let emiCount = model.emiCount;

    if (!loanNo)
        return output


    try {

        let loan = await LoanInfo.findOne({ loanNo: loanNo, status: { $in: ["Loan In Progress", "Written-Off"] } }).lean()

        if (!loan)
            return output;


        let userInfo = await UserInfo.findOne({ _id: loan.userId }).lean()

        if (!userInfo)
            return output;

        let time24HoursAgo = new Date();
        time24HoursAgo.setHours(time24HoursAgo.getHours() - 24);

        let partnerMaster = await PartnerMaster.findOne({ _id: loan.partnerId })

        let loanInstallments = await LoanInstallments.find({ loanId: loan._id, status: { $in: ["Pending", "Partial"] } }).sort({ installmentNumber: 1 })
        let loanFees = await LoanFees.find({ loanId: loan._id, status: { $in: ["Pending", "Partial"] } })

        let loanFeeHash = {}

        for (let i = 0; i < loanFees.length; i++) {
            loanFeeHash[loanFees[i].installmentNumber] = loanFees[i]
        }

        let totalPendingAmount = 0
        let paymentCollectionQueries = [];

        for (let i = 0; i < loanInstallments.length; i++) {
            const emi = loanInstallments[i]
            totalPendingAmount += +loanInstallments[i].installmentAmount + +loanInstallments[i].moratoriumInterest + +(loanFeeHash[loanInstallments[i].installmentNumber] ? loanFeeHash[loanInstallments[i].installmentNumber].feeAmount : 0)
            paymentCollectionQueries.push({ loanId: loan._id, emiNumber: emi.installmentNumber })

        }

        let paymentCollection = await PaymentCollection.find({ $and: [{ $or: paymentCollectionQueries }, { channel: { $in: [PAYMENT_CHANNEL.CF_UPI, PAYMENT_CHANNEL.FF_UPI, PAYMENT_CHANNEL.VA] }, insertedOn: { $gte: time24HoursAgo } }], isActive: true });

        let agentCollections = null

        if (paymentCollection.length) {
            agentCollections = await AgentCollections.findOne({ loanId: loan._id, paymentReferenceNumber: paymentCollection[0].referenceNo })
            emiCount = paymentCollection.length
        }

        let emiList = [], emiNumbers = [];
        let totalEmiAmount = 0, totalPenalty = 0;

        for (let i = 0; i < loanInstallments.length; i++) {
            const emi = loanInstallments[i]
            let penaltyDoc = loanFeeHash[loanInstallments[i].installmentNumber];

            emi.totalAmountToPay = +emi.installmentAmount + +emi.moratoriumInterest + +(penaltyDoc ? penaltyDoc.feeAmount : 0)
            totalEmiAmount += emi.totalAmountToPay;

            totalPenalty += penaltyDoc ? + penaltyDoc.feeAmount : 0

            emiList.push(emi);
            emiNumbers.push(emi.installmentNumber)
            if (emiCount && emiCount == i + 1)
                break;
        }

        let agent = await UserInfo.findById(req.user.id)

        let responsePayload = {
            collectionAmount: totalEmiAmount,
            pendingInstallments: loanInstallments.length,
            totalPendingAmount,
            totalPenalty,
            loanNo: loan.loanNo,
            amount: loan.amount,
            partnerName: partnerMaster.displayName,
            partnerId: loan.partnerId,
            userDetails: {
                mobileNo: userInfo.userDetails && userInfo.userDetails.mobileNo ? userInfo.userDetails.mobileNo : []
            },
            loanId: loan._id,
            isRequestSentAlready: !!paymentCollection.length,
            emiCount: paymentCollection.length,
            isAgentProfileUpdated: !!(agent.profileDocDetails && agent.profileDocDetails.length && agent.profileDocDetails[0].filePath),
            emiNumbers
        }

        if (responsePayload.pendingInstallments != responsePayload.emiCount) {
            responsePayload.mode = "PARTIAL"

        }

        if (paymentCollection.length) {
            responsePayload.requestedOn = paymentCollection[0].insertedOn
        }

        if (agentCollections) {
            responsePayload.id = agentCollections._id
            responsePayload.mode = agentCollections.meta.mode
            responsePayload.paymentLink = agentCollections.paymentLink
            responsePayload.accountInfo = agentCollections.accountInfo

            if (agentCollections.files && agentCollections.files.length) {
                let file = agentCollections.files[agentCollections.files.length - 1]
                file.signedUrl = getSignedUrlToDownload({ url: file.url })
                responsePayload.file = { ...file }
            }

        }

        output = {
            data: responsePayload,
            success: true,
            message: "Success",
            status: 200
        }

    } catch (ex) {
        logger.logs(ex);
        saveErrorLog("generateUpiLink", apiPath, {}, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }
    return output;
}
module.exports.getLoanDetail = getLoanDetail

module.exports.sendPaymentLink = async function (model, req) {
    let output = {
        data: {},
        success: false,
        message: 'Some Error Occurred',
        status: 501
    };

    const loanNo = model.loanNo;
    const emiCount = model.emiCount;
    const mobileNo = model.mobileNo;

    if (!loanNo || !emiCount) return output

    if ((model.channel === "UPI_COLLECTION" || model.channel === "VA_COLLECTION") && !mobileNo) return output

    try {
        let loan = await LoanInfo.findOne({ loanNo: loanNo }).lean()

        model.loanId = loan._id.toString()

        model.event = model.channel

        let linkResult = {}

        if (model.event == "WALLET") {

            let loanDetail = await getWalletPayloadDetails(model, req)

            if (loanDetail.success) {
                linkResult = {
                    success: true,
                    data: {},
                    notificationPayload: {
                        collectionAmount: loanDetail.data.collectionAmount,
                        totalPendingAmount: loanDetail.data.totalPendingAmount,
                        loanNo: loanDetail.data.loanNo,
                        partnerName: loan.partner,
                        partnerId: loan.partnerId
                    },
                    emis: loanDetail.data.emiNumbers
                }
            } else {
                return output
            }

        } else {
            linkResult = await generateUpiLink(model, req)
        }

        if (linkResult.success) {

            let payload = {
                loanId: loan._id,
                userId: req.user.id,
                paymentLink: linkResult.data.link,
                accountInfo: linkResult.data.accountInfo,
                paymentReferenceNumber: linkResult.data.referenceNo,
                emis: linkResult.emis,
                meta: {
                    loanNo,
                    emiCount,
                    mode: model.mode,
                    mobileNumber: mobileNo,
                    channel: model.channel,
                    coordinates: req.body.coordinates,
                    paymentDate: model.paymentDate ? new Date(model.paymentDate) : null
                },
                collectionDetail: linkResult.notificationPayload || {},
            }
            let result = await AgentCollections.create(payload)

            let responsePayload = {
                id: result._id,
                paymentLink: linkResult.data.link,
                accountInfo: linkResult.data.accountInfo
            }
            output = {
                data: responsePayload,
                success: true,
                message: "Success",
                status: 200
            }

            let activityLog = {
                loanId: loan._id,
                userId: req.user.id,
                type: "SEND_PAYMENT_LINK",
                meta: {
                    agentCollectionId: result._id,
                    coordinates: req.body.coordinates
                }
            }

            AgentActivity.create(activityLog)
        }
    } catch (ex) {
        logger.logs(ex);
        saveErrorLog("generateUpiLink", apiPath, {}, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }

    return output;
}

const getWalletPayloadDetails = async (modal, req) => {

    return getLoanDetail(modal, req)

}

module.exports.resendPaymentLink = async function (req, res) {
    let output = {
        data: {},
        success: false,
        message: 'Some Error Occurred',
        status: 501
    };


    let id = req.body.id

    if (!id) return output

    try {

        let doc = await AgentCollections.findOne({ _id: mongoose.Types.ObjectId(id) })

        if (!doc) return output

        let loan = await LoanInfo.findById(doc.loanId)
        let userInfo = await UserInfo.findById(loan.userId)

        let mobileNo = ""

        if (userInfo && userInfo.userDetails && userInfo.userDetails.mobileNo && userInfo.userDetails.mobileNo.length) {
            mobileNo = userInfo.userDetails.mobileNo[0].mobileNumber
        }

        let payload = doc.collectionDetail

        if (mobileNo) {
            const whatsAppResponse = await pushToNotifications(loan.partnerId, loan.merchantCode, loan.productId, loan.trxnId, userInfo._id, loan._id, "WhatsApp", mobileNo, loan.status == "Written-Off" ? "payment_link_v2" : "payment_link_cf_v2", "loan", null, null, null, false, null, null, null, null, payload);
            if (whatsAppResponse.success) await sendNotifications(whatsAppResponse.data._id, null, payload);
        }

        let activityLog = {
            loanId: loan._id,
            userId: req.user.id,
            type: "RESEND_PAYMENT_LINK",
            meta: {
                notificationPayload: payload,
                coordinates: req.body.coordinates
            }
        }

        await AgentActivity.create(activityLog)

        output = {
            data: {},
            success: true,
            message: "Success",
            status: 200
        }

    } catch (ex) {
        logger.logs(ex);
        saveErrorLog("cancelPaymentLink", apiPath, {}, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }

    return output;

}

module.exports.cancelPaymentLink = async function (req, res) {
    let output = {
        data: {},
        success: false,
        message: 'Some Error Occurred',
        status: 501
    };

    let id = req.body.id

    if (!id) return output

    try {

        let doc = await AgentCollections.findOne({ _id: mongoose.Types.ObjectId(id) })

        if (!doc) return output

        let result = await cancelPaymentLink({ referenceNo: doc.paymentReferenceNumber })

        output = {
            data: {},
            success: true,
            message: "Success",
            status: 200
        }

        doc.status = "Cancelled"
        doc.save()

        let activityLog = {
            loanId: doc.loanId,
            userId: req.user.id,
            type: "CANCEL_PAYMENT_LINK",
            meta: {
                coordinates: req.body.coordinates
            }
        }

        await AgentActivity.create(activityLog)

    } catch (ex) {
        logger.logs(ex);
        saveErrorLog("cancelPaymentLink", apiPath, {}, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }

    return output;
}


module.exports.uploadPaymentDocument = async function (req, res) {
    let output = {
        data: {},
        success: false,
        message: 'Some Error Occurred',
        status: 501
    };

    const id = req.body.id;
    const fileUrl = req.body.fileUrl;
    if (!id || !fileUrl) return output

    try {

        let doc = await AgentCollections.findOne({ _id: mongoose.Types.ObjectId(id) })

        if (!doc) return output

        if (doc.files) {
            doc.files.push({ url: fileUrl, insertedOn: new Date() })
        }

        output = {
            data: {},
            success: true,
            message: "Success",
            status: 200
        }

        if (doc.status != "Paid") doc.status = "Confirmation Received"
        await doc.save()

        let activityLog = {
            loanId: doc.loanId,
            userId: req.user.id,
            type: "UPLOAD_DOCUMENT",
            meta: {
                coordinates: req.body.coordinates
            }
        }

        await AgentActivity.create(activityLog)
    } catch (ex) {
        logger.logs(ex);
        saveErrorLog("uploadPaymentDocument", apiPath, {}, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }

    return output;
}

module.exports.sendAgentIdentity = async function (req, res) {
    let output = {
        data: {},
        success: false,
        message: 'Some Error Occurred',
        status: 501
    };


    let loanNo = req.body.loanNo

    if (!loanNo) return output

    try {

        let loan = await LoanInfo.findOne({ loanNo }).lean()

        if (!loan) return output

        let userInfo = await UserInfo.findById(loan.userId)
        let agent = await UserInfo.findById(req.user.id)

        let mobileNo = ""

        if (userInfo && userInfo.userDetails && userInfo.userDetails.mobileNo && userInfo.userDetails.mobileNo.length) {
            mobileNo = userInfo.userDetails.mobileNo[0].mobileNumber
        }

        let image = "", imageName = ""

        if (agent.profileDocDetails && agent.profileDocDetails.length) {
            image = `${config.get("awsSystemFilesCredentials.baseUrl")}${agent.profileDocDetails[0].filePath}`
            let fileName = agent.profileDocDetails[0].fileName
            fileName = fileName.split("/")
            imageName = fileName[fileName.length - 1]
        }

        let payload = {
            merchantName: userInfo.userDetails.firstName,
            agentName: agent.userDetails.firstName,
            agentMobileNo: agent.userDetails.mobileNo[0].mobileNumber,
            fundfinaContact: "+919956159435",
            fileUrl: image,
            fileName: imageName
        }

        if (mobileNo) {
            const whatsAppResponse = await pushToNotifications(loan.partnerId, loan.merchantCode, loan.productId, loan.trxnId, userInfo._id, loan._id, "WhatsApp", mobileNo, "agent_details", "loan", null, null, null, false, null, null, null, null, payload);
            if (whatsAppResponse.success) await sendNotifications(whatsAppResponse.data._id, null, payload);
        }

        let activityLog = {
            loanId: loan._id,
            userId: agent._id,
            type: "SEND_AGENT_DETAILS",
            meta: {
                notificationPayload: payload,
                coordinates: req.body.coordinates
            }
        }

        await AgentActivity.create(activityLog)

        output = {
            data: {},
            success: true,
            message: "Success",
            status: 200
        }

    } catch (ex) {
        logger.logs(ex);
        saveErrorLog("cancelPaymentLink", apiPath, {}, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }

    return output;

}


module.exports.getAgentCollectionLogs = async function (model, req) {
    let output = {
        data: {},
        success: false,
        message: 'Some Error Occurred',
        status: 501
    };
    const limit = 10;
    const skip = model.page ? (+model.page - 1) * limit : 0

    const { loanNo, name, partnerName, fromDate, toDate, mobileNo, status, channel, amount } = model;

    try {

        let query = {}

        if (loanNo) {
            query["meta.loanNo"] = loanNo
        }

        if (status) {
            query["status"] = status
        }

        if (channel) {
            query["meta.channel"] = channel
        }
        if (amount) {
            query["collectionDetail.collectionAmount"] = +amount
        }
        if (fromDate && toDate) {

            let fDate = new Date(fromDate)
            let tDate = new Date(toDate)
            fDate.setHours(0, 0, 0, 0)
            tDate.setHours(23, 59, 59, 999)

            query.insertedOn = { $gte: fDate, $lte: tDate }
        }

        let userQuery = {}

        if (partnerName) {
            let partners = await PartnerMaster.find({ partnerName: { $regex: partnerName, $options: "i" } })
            let partnerQuery = {
                partnerId: { $in: partners.map(item => item._id) }
            }
            let partnerUsers = await UserPartnerInfo.find(partnerQuery).lean()

            userQuery._id = { $in: partnerUsers.map(item => item.userId) }
        }

        if (mobileNo) {
            userQuery["userDetails.mobileNo.mobileNumber"] = mobileNo
        }

        if (name) {
            userQuery["userDetails.firstName"] = { $regex: name, $options: "i" }
        }

        if (Object.keys(userQuery).length) {
            userQuery = { "userDetails.role": "Collection Agent", ...userQuery }
            let users = await UserInfo.find(userQuery).lean()

            let userIds = users.map(item => item._id)
            query.userId = { $in: userIds }
        }

        let totalCount = await AgentCollections.countDocuments(query)
        let data = [], partnerHash = {}, loanHash = {}

        if (model.isAllLoans !== 'true') {
            data = await AgentCollections.find(query).skip(skip).limit(limit).sort({ _id: -1 }).lean()
        } else {
            data = await AgentCollections.find(query).lean()
            let loanIds = data.map(item => item.loanId)
            let loans = await LoanInfo.find({ _id: { $in: loanIds } }).select({ _id: true, flowDetails: true, disbursalDetails: true, lenderDetails: true, partner: true })

            let partnerIds = []

            loans.forEach(element => {
                let partnerId = element.lenderDetails[0].lenderPartnerId

                let flowDetail = element.flowDetails.find(item => item.Step = "Written-Off")
                let writtenOffDate = flowDetail ? flowDetail.insertedOn : ""
                partnerIds.push(partnerId)

                loanHash[element._id.toString()] = {
                    _id: element._id,
                    writtenOffDate,
                    disbursalDate: element.disbursalDetails.disbursedOn,
                    lendingPartnerId: partnerId,
                    borrowingPartner: {
                        name: element.partner
                    }
                }
            })

            let partners = await PartnerMaster.find({ _id: { $in: partnerIds } })

            partners.forEach(element => {
                partnerHash[element._id.toString()] = {
                    name: element.partnerName
                }
            })

        }

        let userIds = data.map(item => item.userId)

        let users = await UserInfo.find({ _id: { $in: userIds } }).lean()

        let userHash = {}

        users.forEach(element => {
            userHash[element._id.toString()] = {
                name: element.userDetails.firstName
            }
        })

        data.forEach(element => {
            element.agent = userHash[element.userId.toString()]
            if (model.isAllLoans == 'true') {
                let loanDetail = loanHash[element.loanId.toString()]
                let lendingPartner = partnerHash[loanDetail.lendingPartnerId.toString()]
                element.borrowingPartner = loanDetail.borrowingPartner
                element.lendingPartner = lendingPartner
                element.disbursalDate = loanDetail.disbursalDate
                element.writtenOffDate = loanDetail.writtenOffDate
            }
        })

        output = {
            data: data,
            success: true,
            message: "Success",
            status: 200,
            totalPages: Math.ceil(totalCount / limit)
        }

    } catch (ex) {
        logger.logs(ex);
        saveErrorLog("generateUpiLink", apiPath, {}, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }
    return output;
}

module.exports.resentNotificationJob = async function (req, res) {
    let output = {
        data: {},
        success: false,
        message: 'Some Error Occurred',
        status: 501
    };


    let paymentConfig = await ConfigParameters.findOne({ channel: "PAYMENT_COLLECTION_NOTIFICATION" }).lean()

    let startDate = new Date(), endDate = new Date()
    startDate.setHours(startDate.getHours() - 24,)
    endDate.setMinutes(endDate.getMinutes() - paymentConfig.minutes)

    let aggregateQuery = [
        {
            $match: {
                channel: { $in: [PAYMENT_CHANNEL.CF_UPI, PAYMENT_CHANNEL.FF_UPI] },
                isPaid: false,
                isActive: true,
                insertedOn: { $gte: startDate, $lte: endDate },
            }
        },
        {
            $group: {
                _id: "$referenceNo",
            }
        }
    ]

    let pendingPayments = await PaymentCollection.aggregate(aggregateQuery)

    for (let index = 0; index < pendingPayments.length; index++) {
        const element = pendingPayments[index];

        let result = await resendPaymentLink({ referenceNo: element._id }, true)

        if (index % 100 == 0) await sleep(3000)
    }

    output = {
        data: {},
        success: true,
        message: 'Success',
        status: 200
    };

    return output
}