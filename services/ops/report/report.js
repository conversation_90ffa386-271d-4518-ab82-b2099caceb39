
const { LoanInfo, LoanInstallments } = require('../../../models/ops/loan/loan');
const { PartnerMaster } = require('../../../models/ops/partner/partner')
var json2xls = require('json2xls');
const fs = require("fs");
const moment = require('moment');
const { getMailModel } = require("../common/common");
const { sendExternalMail } = require("../common/mailer");
const config = require("config");
const { MailerConfig } = require("../../../models/ops/common/mailer");

async function generateReport(date) {

    try {
        let monthStart = new Date(moment(date).startOf('month'))
        let monthEnd = new Date(moment(date).endOf('month'))

        console.log("monthStart monthStart" , monthStart)
        console.log("monthEnd monthEnd" , monthEnd)

        const todayDate = new Date();

        const today = new Date(
            todayDate.getFullYear(),
            todayDate.getMonth(),
            todayDate.getDate(), 5, 30, 0, 0
        );

        console.log("today" , today)

        let aggregateQuery = [{
            $match: {
                'disbursalDetails.disbursedOn': {
                    $gt: monthStart,
                    $lt: monthEnd
                }
            }
        }, {
            $group: {
                _id: '$partnerId',
                totalAmount: {
                    $sum: '$amount'
                },
                totalCount: {
                    $sum: 1
                }
            }
        }]

        let loanInfoMeta = await LoanInfo.aggregate(aggregateQuery); /** NO LOAN STATUS CONSIDRED - DATA BASED ON ONLY DISBUSED ON DATE - (ALL PARTNERS) */

        let loanMetaHash = {}

        for (let i = 0; i < loanInfoMeta.length; i++) {
            loanMetaHash[loanInfoMeta[i]._id.toString()] = loanInfoMeta[i]
        }

        let partners = await PartnerMaster.find({ partnerType: "Borrowing Partner", isActive: true, partnerCode: { $nin: ["FUND", "PEassy"] } }).lean()

        let partnerData = []

        let totalPreviousPrincipleOutStanding = 0;

        let totalRow = {
            "Month": "Total",
            "Partner": "",
            "Event Default Rate": 0,
            "Event Default Percentage": 0,
            "Total Principal Default Rate": 0,
            "Total Principal Default Percentage": 0,
            "Outstanding Default Rate": 0,
            "Outstanding Default Percentage": 0,
            "Total Disbursement Count": 0,
            "Total Disbursement Amount": 0,
            "Change in Total Outstanding from last Week": 0,
            "Percentage Change compared to last week": 0,
            "Percentage Change compared to total disbursement": 0,
            "Num Delinq": 0,
            "Amt Delinq": 0,
            "Prin Delinq": 0,
            "Num Def": 0,
            "Amt Def": 0,
            "Prin Def": 0,
            "Num Writ": 0,
            "Amt Writ": 0,
            "Prin Writ": 0
        }

        for (let i = 0; i < partners.length; i++) {

            let columns = {
                "Month": moment(date).format('MMMM-YY'),
                "Partner": "",
                "Event Default Rate": 0,
                "Event Default Percentage": 0,
                "Total Principal Default Rate": 0,
                "Total Principal Default Percentage": 0,
                "Outstanding Default Rate": 0,
                "Outstanding Default Percentage": 0,
                "Total Disbursement Count": 0,
                "Total Disbursement Amount": 0,
                "Change in Total Outstanding from last Week": 0,
                "Percentage Change compared to last week": "0",
                "Percentage Change compared to total disbursement": 0,
                "Num Delinq": 0,
                "Amt Delinq": 0,
                "Prin Delinq": 0,
                "Num Def": 0,
                "Amt Def": 0,
                "Prin Def": 0,
                "Num Writ": 0,
                "Amt Writ": 0,
                "Prin Writ": 0
            }


            let result = await getPartnerLevelData(partners[i]._id, monthStart, monthEnd, today);
            let arreasResult = await getArrearsPartnerLevelData(partners[i]._id, monthStart, monthEnd);

            columns["Partner"] = partners[i].displayName;

            if (result) {

                if (loanMetaHash[partners[i]._id]) {
                    columns["Total Disbursement Count"] = loanMetaHash[partners[i]._id].totalCount;
                    columns["Total Disbursement Amount"] = loanMetaHash[partners[i]._id].totalAmount;

                    //Total
                    totalRow["Total Disbursement Count"] += +loanMetaHash[partners[i]._id].totalCount;
                    totalRow["Total Disbursement Amount"] += +loanMetaHash[partners[i]._id].totalAmount
                }

                columns["Event Default Rate"] = result.outStandingLoanCount;
                columns["Total Principal Default Rate"] = result.totalAmount;
                columns["Outstanding Default Rate"] = result.totalPrincipleOutStanding;
                columns["Change in Total Outstanding from last Week"] = toFixedCustom(result.principleDifference);

                //Total
                totalRow["Event Default Rate"] += +result.outStandingLoanCount;
                totalRow["Total Principal Default Rate"] += +result.totalAmount;
                totalRow["Outstanding Default Rate"] += result.totalPrincipleOutStanding;
                totalRow["Change in Total Outstanding from last Week"] += toFixedCustom(result.principleDifference);

                if (result.principleDifference && result.previousPrincipleOutStanding) {
                    columns["Percentage Change compared to last week"] = toFixedCustom((result.principleDifference / result.previousPrincipleOutStanding) * 100) + '%';
                }

                if (result.previousPrincipleOutStanding)
                    totalPreviousPrincipleOutStanding += result.previousPrincipleOutStanding;

                if (result.principleDifference && columns["Total Disbursement Amount"]) {
                    columns["Percentage Change compared to total disbursement"] = toFixedCustom((result.principleDifference / columns["Total Disbursement Amount"]) * 100) + '%'
                }

                columns["Event Default Percentage"] = toFixedCustom(((result.outStandingLoanCount / columns["Total Disbursement Count"]) * 100) || 0) + '%';
                columns["Total Principal Default Percentage"] = toFixedCustom(((result.totalAmount / columns["Total Disbursement Amount"]) * 100) || 0) + '%';
                columns["Outstanding Default Percentage"] = toFixedCustom(((result.totalPrincipleOutStanding / columns["Total Disbursement Amount"]) * 100) || 0) + '%';
            }

            if (arreasResult) {
                columns["Num Delinq"] = arreasResult.numDelinq
                columns["Amt Delinq"] = toFixedCustom(arreasResult.amountDelinq)
                columns["Prin Delinq"] = toFixedCustom(arreasResult.prinDelinq)
                columns["Num Def"] = arreasResult.numDefault
                columns["Amt Def"] = toFixedCustom(arreasResult.amountDefault)
                columns["Prin Def"] = toFixedCustom(arreasResult.prinDefault)
                columns["Num Writ"] = arreasResult.numWriteOff
                columns["Amt Writ"] = toFixedCustom(arreasResult.amountWriteOff)
                columns["Prin Writ"] = toFixedCustom(arreasResult.prinWriteOff)

                //Total
                totalRow["Num Delinq"] += arreasResult.numDelinq
                totalRow["Amt Delinq"] += toFixedCustom(arreasResult.amountDelinq)
                totalRow["Prin Delinq"] += toFixedCustom(arreasResult.prinDelinq)
                totalRow["Num Def"] += arreasResult.numDefault
                totalRow["Amt Def"] += toFixedCustom(arreasResult.amountDefault)
                totalRow["Prin Def"] += toFixedCustom(arreasResult.prinDefault)
                totalRow["Num Writ"] += arreasResult.numWriteOff
                totalRow["Amt Writ"] += toFixedCustom(arreasResult.amountWriteOff)
                totalRow["Prin Writ"] += toFixedCustom(arreasResult.prinWriteOff)

            }

            //Total
            totalRow["Percentage Change compared to last week"] = toFixedCustom(totalPreviousPrincipleOutStanding && ((totalRow["Change in Total Outstanding from last Week"] / totalPreviousPrincipleOutStanding) * 100) || 0) + '%';
            totalRow["Percentage Change compared to total disbursement"] = toFixedCustom(((totalRow["Change in Total Outstanding from last Week"] / totalRow["Total Disbursement Amount"]) * 100) || 0) + '%'
            totalRow["Event Default Percentage"] = toFixedCustom(((totalRow["Event Default Rate"] / totalRow["Total Disbursement Count"]) * 100) || 0) + '%';
            totalRow["Total Principal Default Percentage"] = toFixedCustom(((totalRow["Total Principal Default Rate"] / totalRow["Total Disbursement Amount"]) * 100) || 0) + '%';
            totalRow["Outstanding Default Percentage"] = toFixedCustom(((totalRow["Outstanding Default Rate"] / totalRow["Total Disbursement Amount"]) * 100) || 0) + '%';

            if (result && arreasResult)
                partnerData.push(columns)
        }

        partnerData.push(totalRow)

        console.log(partnerData)
        return partnerData;

    } catch (error) {
        console.log(error)
    }
}

async function getPartnerLevelData(partnerId, startDate, endDate, today) {

    try {
        let defaultLoansAggregate = [{
            $match: {
                partnerId: partnerId,
                status: {
                    $in: [
                        'Loan In Progress',
                        'Written-Off'
                    ]
                },
                'disbursalDetails.disbursedOn': {
                    $gt: startDate,
                    $lt: endDate
                }
            }
        }, {
            $addFields: {
                daysDifference: {
                    $dateDiff: {
                        startDate: '$nextEmiRepaymentDate',
                        endDate: today,
                        unit: 'day'
                    }
                }
            }
        }, {
            $match: {
                daysDifference: {
                    $gte: 7
                }
            }
        }, {
            $group: {
                _id: '$partnerId',
                principleOutStanding: {
                    $sum: "$principalAmount.outstanding"
                },
                totalAmount: {
                    $sum: '$amount'
                },
                loanIds: {
                    $push: '$_id'
                }
            }
        }]

        let result = await LoanInfo.aggregate(defaultLoansAggregate)

        if (!result.length)
            return null;

        let dateBefore = new Date(today)
        dateBefore = new Date(dateBefore.setDate(new Date().getDate() - 7))

        console.log('dateBefore' ,dateBefore)

        let loanInstallments = await LoanInstallments.aggregate([
            {
                $match: {
                    loanId: { $in: result[0].loanIds },
                    $or: [{ status: "Pending" }, { transactionDate: { $gte: dateBefore } }]
                }
            },
            {
                $group: {
                    _id: null,
                    totalPrincipleOutStanding: {
                        $sum: "$installmentPrincipal"
                    }
                }
            }
        ])

        let resultObj = {
            outStandingLoanCount: result.length ? result[0].loanIds.length : 0,
            totalAmount: result.length ? result[0].totalAmount : 0,
            totalPrincipleOutStanding: result.length ? result[0].principleOutStanding : 0,
            principleDifference: (loanInstallments.length ? loanInstallments[0].totalPrincipleOutStanding : 0) - (result.length ? result[0].principleOutStanding : 0),
            previousPrincipleOutStanding: (loanInstallments.length ? loanInstallments[0].totalPrincipleOutStanding : 0),
            firstPercentageValue: 0,
            secondPercentageValue: 0
        }

        return resultObj;
    } catch (error) {
        console.log(error)
    }

}

async function getArrearsPartnerLevelData(partnerId, startDate, endDate) {

    let loans = await LoanInfo.aggregate([
        {
            $match: {
                partnerId,
                'disbursalDetails.disbursedOn': {
                    $gt: startDate,
                    $lt: endDate
                },
                "arrears.0": { $exists: true },
                "arrears.delinquency": { $in: ["Delinquent", "Default", "Write-Off"] }
            }
        },
        {
            $project: {
                arrears: 1,
                interestRate: 1,
                amount: 1,
                tenure: 1
            }
        }
    ])

    if (!loans.length)
        return null

    let resultObj = {
        numDelinq: 0,
        amountDelinq: 0,
        prinDelinq: 0,
        numDefault: 0,
        amountDefault: 0,
        prinDefault: 0,
        numWriteOff: 0,
        amountWriteOff: 0,
        prinWriteOff: 0
    }

    for (let i = 0; i < loans.length; i++) {

        let loan = loans[i]
        let isDelinquentAccurs = false;
        let isDefaultAccurs = false;
        let isWriteOffAccurs = false;

        for (let j = 0; j < loan.arrears.length; j++) {

            let arrear = loan.arrears[j]

            if (arrear.delinquency == "Delinquent" && !isDelinquentAccurs) {
                resultObj.numDelinq += 1;
                // resultObj.amountDelinq += arrear.amount + ((arrear.amount / 100) * loan.interestRate);
                resultObj.amountDelinq += arrear.amount * (1 + (loan.interestRate / 100) * loan.tenure / 365);
                resultObj.prinDelinq += arrear.amount;
                isDelinquentAccurs = true
            }

            if (arrear.delinquency == "Default" && !isDefaultAccurs) {
                resultObj.numDefault += 1;
                // resultObj.amountDefault += arrear.amount + ((arrear.amount / 100) * loan.interestRate);
                resultObj.amountDefault += arrear.amount * (1 + (loan.interestRate / 100) * loan.tenure / 365);
                resultObj.prinDefault += arrear.amount;
                isDefaultAccurs = true
            }

            if (arrear.delinquency == "Write-Off" && !isWriteOffAccurs) {
                resultObj.numWriteOff += 1;
                //resultObj.amountWriteOff += arrear.amount + ((arrear.amount / 100) * loan.interestRate);
                resultObj.amountWriteOff += arrear.amount * (1 + (loan.interestRate / 100) * loan.tenure / 365);
                resultObj.prinWriteOff += arrear.amount;
                isWriteOffAccurs = true
            }
        }
    }

    return resultObj;
}

async function generateCollectionAnalysis() {

    let monthsToAddYear = ["January", "February", "March"]
    let currentMonth = moment().format('MMMM')

    let date = new Date()
    let lastYear = date.getFullYear() - (monthsToAddYear.includes(currentMonth) ? 2 : 1);

    let startDate = new Date(`04/01/${lastYear}`);
    let endDate = new Date();

    let noOfMonthDifference = monthDiff(startDate, endDate) + 1;
    let reportData = []

    for (let i = 0; i < noOfMonthDifference; i++) {

        let date = new Date(new Date(startDate).setMonth(startDate.getMonth() + i))

        let result = await generateReport(date)

        if (result) {
            reportData = reportData.concat(result);
            reportData = reportData.concat([{}, {}])
        }

    }

    var xls = json2xls(reportData);

    const fileName = "weekly_collections_analysis_" + moment().format("DD_MM_YYYY") + ".xlsx";
    const filePath = config.get("exportToExcelPath.generalReports") + fileName;
    fs.writeFileSync(filePath, xls, 'binary');

    const mailerConfig = await MailerConfig.findOne({ name: "Weekly Collection Analysis Report" })

    if (mailerConfig) {
        let mailModel = getMailModel();

        mailModel.attachmentName = fileName
        mailModel.attachmentPath = filePath
        mailModel.to = mailerConfig.to
        mailModel.cc = mailerConfig.cc
        mailModel.date = moment().format("DD/MM/YYYY")
        const mailResponse = await sendExternalMail(mailModel, "Weekly Collection Analysis", "Weekly Collection Analysis Report");
    }

    return {
        success: true,
        data: [],
        message: "Success",
        status: 200
    }
}

function monthDiff(dateFrom, dateTo) {
    return dateTo.getMonth() - dateFrom.getMonth() +
        (12 * (dateTo.getFullYear() - dateFrom.getFullYear()))
}

function toFixedCustom(n) {

    if (Number(n) === n && n % 1 !== 0) { // Is float
        return +n.toFixed(2)
    } else {
        return n
    }
}

module.exports = {
    generateReport,
    generateCollectionAnalysis
}