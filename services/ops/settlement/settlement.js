const mongoose = require("mongoose");
const { getNewId, await<PERSON>andler, sleep, getProductType } = require("../common/common");
const config = require("config");
const { saveErrorLog } = require("../common/masters");
const IsNullEmpty = require("isnullemptyorwhitespace");
const { SettlementWebhookLog, SettlementMISStage, SettlementMISLog, SettlementTransactions } = require("../../../models/ops/settlement/settlement");
const { LoanInstallments } = require("../../../models/ops/loan/loan");
const apiPath = __dirname;
const { MIS_LOG_STAGING_STATUS, MIS_LOG_CHANNEL } = require("../common/constants").Constants
const AuthSignature = require('../../auth-signature/auth-signature');
const fetch = require("node-fetch");
const { httpsRequest } = require('../common/https-client');

const debug = require("debug")("app:disbursal:disbursal");

/** Log insta alert call back */
async function paymentsLogger(req) {


    try {
        let callbackInfo = req.body;
        let settlementWebhookLog = new SettlementWebhookLog(callbackInfo);
        settlementWebhookLog.insertedOn = new Date();
        const insertSettlementWebhookLog = await settlementWebhookLog.save();

        handleSettlementAlert(callbackInfo, req.params)

    } catch (ex) {
        saveErrorLog("paymentsLogger", apiPath, req.body, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }

    return {}
}

async function handleSettlementAlert(payload, queryParams) {

    try {
        let gateway = queryParams.gateway || "idfc";
        let webhookType = queryParams.webhookType || "";

        let headers = {
            'Content-Type': 'application/json'
        }
        let paymentService = config.get("paymentService");
        // let url = paymentService.baseUrl + paymentService.settlementAlert;
        let url = paymentService.baseUrl + paymentService.settlementAlert + `/${gateway}`;

        if (webhookType)
            url += `/${webhookType}`;

        let [error, response, rawRequest] = await httpsRequest(url, { method: 'POST', headers, body: JSON.stringify(payload) })
        console.log(error, response)
    } catch (ex) {
        saveErrorLog("handleSettlementAlert", apiPath, req.body, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }
}

async function settlementMISStage(model) {

    let output = {
        success: false,
        message: "Some Error Occurred",
        status: 400,
    };

    let query = null

    try {

        if (!model.transactions || !Array.isArray(model.transactions) || !model.transactions.length) {
            output.message = "Missing transactions";
            return output;
        }

        let maxLimit = config.get("settlementMIS") && config.get("settlementMIS").maxLimit || 100;

        if (model.transactions && model.transactions.length > maxLimit) {
            output.message = "Transaction limit exceeded";
            return output;
        }

        if (!model.utrNo) {
            output.message = "utrNo mandatory";
            return output;
        }

        if (!model.totalRecords) {
            output.message = "totalRecords mandatory";
            return output;
        }

        let isValid = validateMISTransactions(model.transactions);

        if (!isValid) {
            output.message = "Invalid transactions";
            return output;
        }

        let totalAmount = 0;
        for (let i = 0; i < model.transactions.length; i++) {
            totalAmount += model.transactions[i].rePaymentAmount;
        }

        let misLogRes = await SettlementMISLog.findOne({ "info.utrNo": model.utrNo, channel: MIS_LOG_CHANNEL.API }).lean()

        if (misLogRes && misLogRes.info && misLogRes.info.counter >= misLogRes.info.totalRecords) {
            output.message = "Total Records Reached";
            return output;
        }

        if (misLogRes && misLogRes.stagingStatus == MIS_LOG_STAGING_STATUS.FAILED) {
            return output;
        }

        misId = misLogRes && misLogRes._id;

        let now = new Date()
        query = { "info.utrNo": model.utrNo, channel: MIS_LOG_CHANNEL.API }
        let updateQuery = {
            $set: {
                "info.utrNo": model.utrNo,
                "info.totalRecords": model.totalRecords,
                channel: MIS_LOG_CHANNEL.API,
                modifiedOn: now,
                insertedOn: now,
                stagingStatus: MIS_LOG_STAGING_STATUS.PROCESSING
            },
            $inc: { "info.counter": model.transactions.length, "info.totalAmount": totalAmount }
        };

        let misLog = await SettlementMISLog.findOneAndUpdate(query, updateQuery, { new: true, upsert: true }).lean()

        prepareAndInsertTransactions(model.transactions, misLog, model);

        return {
            data: {},
            success: true,
            message: "Transactions Received Successfully",
            status: 200
        }

    } catch (ex) {

        if (query) {
            await SettlementMISLog.updateOne(query, { $set: { stagingStatus: MIS_LOG_STAGING_STATUS.FAILED, modifiedOn: new Date(), failureReason: ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex } })
        }
        saveErrorLog("settlementMis", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        return output
    }

}

function validateMISTransactions(transactions) {
    for (let i = 0; i < transactions.length; i++) {

        let transaction = transactions[i];

        if (!transaction.merchantCode ||
            !transaction.productId ||
            !transaction.referenceNo ||
            !transaction.emiNumber ||
            !transaction.rePaymentAmount ||
            !transaction.collecedOn)
            return false
    }

    return true
}

async function prepareAndInsertTransactions(transactions, misLog, model) {

    try {

        let index = 1
        if (misLog && misLog.info && misLog.info.counter) {
            index = (misLog.info.counter - transactions.length) + 1
        }

        for (let i = 0; i < transactions.length; i++) {
            let transaction = transactions[i];

            transaction.channel = config.get("settlementMIS") && config.get("settlementMIS").channel && config.get("settlementMIS").channel.api;
            transaction.createdOn = new Date()
            transaction.utrNo = model.utrNo
            transaction.index = index
            transaction.channelInfo = { id: misLog._id }
            index += 1
        }

        await SettlementMISStage.insertMany(transactions, { ordered: false })

        if (misLog.info.totalRecords == misLog.info.counter) {

            await SettlementMISLog.updateOne({ _id: misLog._id }, { $set: { stagingStatus: MIS_LOG_STAGING_STATUS.COMPLETED } })

            await insertSettlementTransactions(misLog, transactions);
        }

    } catch (ex) {

        let query = { "info.utrNo": model.utrNo, channel: MIS_LOG_CHANNEL.API };
        await SettlementMISLog.updateOne(query, { $set: { stagingStatus: MIS_LOG_STAGING_STATUS.FAILED, failureReason: ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex } })

        saveErrorLog("prepareAndInsertTransactions", apiPath, { transactions }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }
}

async function insertSettlementTransactions(misLog, transactions) {

    try {

        let loanInstallmentRes = await LoanInstallments.find({ referenceNo: transactions[0].referenceNo });

        let now = new Date()
        let obj = {
            utrNo: misLog.info.utrNo,
            isMISReceived: true,
            misAmount: misLog.info.totalAmount,
            partnerId: loanInstallmentRes && loanInstallmentRes.data && loanInstallmentRes.data.length ? loanInstallmentRes.data[0].partnerId : null,
            settlementType: "BULK",
            status: MIS_LOG_STAGING_STATUS.PENDING,
            totalMisCount: misLog.info.totalRecords,
            date: now,
            insertedOn: now,
            updatedOn: now,

            //DUMMY
            isSettlementReceived: true,
            transactionAmount: misLog.info.totalAmount
        }

        let settlementTransactions = new SettlementTransactions(obj);
        await settlementTransactions.save();
    } catch (ex) {
        saveErrorLog("insertSettlementTransactions", apiPath, { transactions }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }
}



module.exports = {
    paymentsLogger,
    settlementMISStage
}