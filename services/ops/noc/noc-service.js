const mongoose = require("mongoose");
const { getMailModel, getSmsModel, getDMYFormattedDate, pushToNotifications } = require("../common/common");
const config = require("config");
const { NotificationTemplates } = require("../../../models/ops/common/master");
const { saveErrorLog } = require("../common/masters");
const { LoanInfo } = require("../../../models/ops/loan/loan");
const { UserInfo } = require("../../../models/ops/registration/users");
const { uploadLocalFileToProofImages, getSignedUrlToDownload } = require("../../../models/ops/aws/s3");
const { PartnerMaster } = require("../../../models/ops/partner/partner");
const { sendMail } = require("../../../services/ops/common/mailer");
const { NocLog } = require('../../../models/ops/noc/noc');
const { NOC_EMAIL_EXTRACT } = require('../common/constants').Constants;
const postDisbursal = require("../loan/postDisbursal");
const { generateCustomUrl } = require("../../../services/ops/common/links");
const fs = require("fs");
const apiPath = __dirname;

async function uploadNocDocument(loanNo, filePath, fileName, nocId) {

    let output = {
        data: null,
        success: false,
        message: "Some Error Occured",
        status: 400,
    };

    try {
        const loan = await LoanInfo.findOne({ loanNo: loanNo });

        if (loan) {

            if (!loan.loanContractDetails.nocFile) {
                const staticPath = "ProofImages/" + loan.userId.toString();
                const s3FilePath = staticPath + "/NOC/" + fileName;

                //Upload to S3
                await uploadLocalFileToProofImages(s3FilePath, filePath);

                //Delete file from local
                fs.unlink(filePath, function () {
                    //logger.logs("file deleted successfully");
                });

                //Update NOC Status
                await NocLog.updateOne({ _id: mongoose.Types.ObjectId(nocId) }, {
                    "uploadStatus": NOC_EMAIL_EXTRACT.UPLOAD_STATUS.COMPLETED,
                    filePath: s3FilePath
                });

                loan.loanContractDetails.nocFile = s3FilePath;
                await loan.save();

                //Send Notifications
                await sendNocNotifications(loanNo);
            }
        }
        else {
            output.message = "Loan not found";
            fs.unlink(filePath, function () {
                //logger.logs("file deleted successfully");
            });
            //Update NOC Status
            await NocLog.updateOne({ _id: nocId }, {
                "uploadStatus": NOC_EMAIL_EXTRACT.UPLOAD_STATUS.FAILED,
                failureReason: "Loan not found"
            });
        }

        return output;

    } catch (error) {
        saveErrorLog("getTransactionBatchApi", apiPath, { loanNo, filePath, fileName, nocId }, error.stack ? error.stack.toString().replace(/\r?\n|\r/g, " ") : error);
        output.message = error.stack ? error.stack.toString().replace(/\r?\n|\r/g, " ") : error;
        return output
    }
}

async function sendNocNotifications(loanNo) {

    let output = {
        data: null,
        success: false,
        message: "Some Error Occured",
        status: 400,
    };

    try {
        const loan = await LoanInfo.findOne({ loanNo: loanNo }).select({ _id: 1, loanNo: 1, userId: 1, loanContractDetails: 1, partnerId: 1, merchantCode: 1, trxnId: 1, lenderDetails: 1 });

        if (loan) {
            const user = await UserInfo.findById(loan.userId).select({ _id: 1, userDetails: 1 });

            if (loan.loanContractDetails.nocFile) {
                const fileParts = loan.loanContractDetails.nocFile.split("/");
                let mailModel = getMailModel();
                mailModel.number = loan.loanNo;
                mailModel.attachmentName = fileParts[fileParts.length - 1];
                const signUrl = getSignedUrlToDownload({ url: loan.loanContractDetails.nocFile, expireSeconds: config.get('awsProofImageCredentials.nocUrlTimeout') });
                mailModel.attachmentPathHref = signUrl;

                //Send email
                await sendMail(
                    loan.userId,
                    loan._id,
                    "Loan Application Emails to Merchants of Partner",
                    "NOC",
                    mailModel,
                    "User"
                );

                //Send WhatsApp/SMS
                if (user.userDetails.mobileNo[0] && user.userDetails.mobileNo[0].channel && user.userDetails.mobileNo[0].channel.whatsapp && user.userDetails.mobileNo[0].channel.whatsapp.isActive) {
                    // if (user.userDetails.mobileNo[0]) {
                    const notificationTemplate = await NotificationTemplates.findOne({ channel: "WhatsApp", template: "noc_repayment" });
                    const lendingPartner = await PartnerMaster.findById(loan.lenderDetails[0].lenderPartnerId);

                    let smsModel = getSmsModel();
                    smsModel.To = user.userDetails.mobileNo[0] ? user.userDetails.mobileNo[0].mobileNumber : ""; // mobile number change 
                    smsModel.Type = notificationTemplate.type;
                    smsModel.From = config.get("keys.from");

                    smsModel.TemplateName = notificationTemplate.whatsAppTemplate;
                    smsModel.VAR1 = `${user.userDetails.firstName} ${user.userDetails.lastName}`;
                    smsModel.VAR2 = `${lendingPartner.brandName}`;
                    smsModel.VAR3 = `${loan.loanNo}`;
                    smsModel.AttachmentPath1 = signUrl;

                    const whatsAppResponse = await pushToNotifications(loan.partnerId, loan.merchantCode, loan.productId, loan.trxnId,
                        loan.userId, loan._id, "WhatsApp", user.userDetails.mobileNo[0].mobileNumber, "noc_repayment", "loan", null, null, smsModel, false, null);

                    //Call sendnotifications
                    if (whatsAppResponse.success)
                        await postDisbursal.sendNotifications(whatsAppResponse.data._id);
                }
                else {
                    const type = config.get("shortUrl.documentLink");
                    const generateCustomUrlResponse = await generateCustomUrl(signUrl, type, loan.userId, loan.loanId, loan.loanNo, loan.merchantCode, loan.partner, null);

                    if (generateCustomUrlResponse.success) {
                        let smsModel = getSmsModel();
                        smsModel.To = user.userDetails.mobileNo[0].mobileNumber;
                        smsModel.TemplateName = "noc_repayment";
                        smsModel.VAR1 = loan.loanNo;
                        smsModel.VAR2 = generateCustomUrlResponse.data;
                        smsModel.Type = "TSMS";
                        smsModel.From = config.get("keys.from");

                        const sendSms = await pushToNotifications(loan.partnerId, loan.merchantCode, loan.productId, loan.trxnId,
                            loan.userId, loan.loanId, "SMS", user.userDetails.mobileNo[0].mobileNumber, smsModel.TemplateName, "loan", null, null, smsModel, false, "TSMS");

                        //Call sendnotifications
                        if (sendSms.success)
                            response = await postDisbursal.sendNotifications(sendSms.data._id);
                    }
                }
                output.success = true;
                output.message = "Notification sent successfully!";
            }
            else
                output.message = "File Not found";
        }
        else
            output.message = "Loan not found";

        return output;

    } catch (error) {
        saveErrorLog("sendNocNotifications", apiPath, { loanNo: loanNo }, error.stack ? error.stack.toString().replace(/\r?\n|\r/g, " ") : error);
        output.message = error.stack ? error.stack.toString().replace(/\r?\n|\r/g, " ") : error;
        return output
    }
}

async function sendNocRequestMail(loanNo) {
    let output = {
        success: false,
        message: "Some Error Occurred",
        status: 200,
    };

    try {
        const updatedLoan = await LoanInfo.findOne({ loanNo: loanNo }).select({ _id: 1, loanNo: 1, status: 1, productType: 1, lenderDetails: 1, userId: 1, disbursalDetails: 1, lastEmiPaidDate: 1, totalOutstanding: 1 });

        if (updatedLoan) {
            const userInfo = await UserInfo.findOne(updatedLoan.userId).select({ _id: 1, userDetails: 1 });

            //Send Mail to Lending Partner for NOC
            if (updatedLoan.status === "Close Repaid" || updatedLoan.status === "Close Prepaid" || updatedLoan.status === "WrittenOff Paid") {
                let mailModel = getMailModel();
                const lendingPartner = await PartnerMaster.findById(updatedLoan.lenderDetails[0].lenderPartnerId);
                mailModel.to = lendingPartner.emails && lendingPartner.emails.nocEmailsTo ? lendingPartner.emails.nocEmailsTo : "";
                mailModel.cc = lendingPartner.emails && lendingPartner.emails.nocEmailsCc ? lendingPartner.emails.nocEmailsCc : "";
                mailModel.from = config.get("mailConfig.fromNoc");

                if (mailModel.to) {
                    mailModel.number = updatedLoan.loanNo;
                    mailModel.name = `${userInfo.userDetails.firstName} ${userInfo.userDetails.lastName}`;
                    mailModel.email = `${userInfo.userDetails.emailId[0]}`;
                    mailModel.number1 = updatedLoan.lenderDetails[0].lenderLoanId ? updatedLoan.lenderDetails[0].lenderLoanId : "-";
                    mailModel.number2 = updatedLoan.lenderDetails[0].lenderCustId ? updatedLoan.lenderDetails[0].lenderCustId : "-";
                    mailModel.date = updatedLoan.disbursalDetails.disbursedOn ? getDMYFormattedDate(updatedLoan.disbursalDetails.disbursedOn, "/") : "-";
                    mailModel.date1 = updatedLoan.lastEmiPaidDate ? getDMYFormattedDate(updatedLoan.lastEmiPaidDate, "/") : "-";
                    mailModel.amount = updatedLoan.totalOutstanding ? updatedLoan.totalOutstanding.toString() : "0";

                    //Send email
                    await sendMail(userInfo._id, updatedLoan._id, "Emails to Lender", "NOC", mailModel, "Lender");

                    output.message = "Mail sent successfully"
                }
            }
        }
        else
            output.message = "Loan not found";
    }
    catch (ex) {
        console.log("Error", ex);
    }

    return output;
}

module.exports = {
    uploadNocDocument,
    sendNocNotifications,
    sendNocRequestMail
}