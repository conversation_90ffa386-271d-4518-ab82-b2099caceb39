const mongoose = require("mongoose");
const { generateUniqueFileName } = require("../common/common");
const config = require("config");
const { saveErrorLog } = require("../common/masters");
const { NOC_EMAIL_EXTRACT } = require('../common/constants').Constants;
const { uploadNocDocument } = require('./noc-service');
const { NocLog } = require('../../../models/ops/noc/noc');
const Imap = require('imap');
const moment = require("moment");
const fs = require("fs");
const { Base64Decode } = require('base64-stream')
const apiPath = __dirname;
let imap;

function connectEmail(callback) {

    let emailCredrentials = config.get("nocEmailCredentials");
    try {
        const imapConfig = {
            user: emailCredrentials.email,
            password: emailCredrentials.password,
            host: 'imap.gmail.com',
            port: '993',
            tls: true,
            tlsOptions: {
                rejectUnauthorized: false
            }
        }
        imap = new Imap(imapConfig);

        imap.once("ready", () => {
            callback && callback()
        });
        imap.once("error", function (ex) {
            saveErrorLog("connectEmail", apiPath, {}, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        });

        imap.connect();
    } catch (ex) {
        saveErrorLog("connectEmail", apiPath, {}, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);

    }
}

async function extractEmail(model = {}, req, res) {
    try {
        connectEmail(async () => { // Connected Successfully
            imap.openBox("INBOX", false, function (err, mailBox) {
                if (err) {
                    imap.end();
                    saveErrorLog("extractEmail", apiPath, {}, err.stack ? err.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
                    return;
                }

                let filterDate = moment().format('YYYY-MM-DD')

                if (req.body && req.body.date)
                    filterDate = moment(req.body.date).format('YYYY-MM-DD')

                imap.search(["ALL", ['ON', filterDate]], async function (err, results) {
                    if (!results || !results.length) {
                        imap.end();
                        return;
                    }

                    let unProcessedIds = await getUnProcessedIds(results);

                    if (!unProcessedIds || !unProcessedIds.length) {
                        imap.end();
                        return;
                    }

                    let f = imap.fetch(unProcessedIds, { bodies: "", struct: true, envelope: true });
                    f.on("message", processMessage);
                    f.once("error", function (ex) {
                        imap.end();
                        saveErrorLog("extractEmail", apiPath, {}, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
                    });
                    f.once("end", function () {
                    });
                });
            });
        })

    } catch (ex) {
        saveErrorLog("extractEmail", apiPath, {}, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }
}

function processMessage(msg) {
    let emailCredrentials = config.get("nocEmailCredentials");
    try {
        msg.once('attributes', async function (attrs) {

            if (attrs && attrs.envelope && attrs.envelope.subject && attrs.envelope.subject.includes("NOC")) {

                let loanNo = getLoanNo(attrs.envelope.subject);

                if (!loanNo)
                    return

                let attachments = findAttachmentFromAttrs(attrs.struct);

                if (attachments && attachments.length) {
                    for (let i = 0; i < 1; i++) { /** For Noc it should be one file */
                        let attachment = attachments[i];
                        let fileType = getFileType(attachment)
                        if (NOC_EMAIL_EXTRACT.ALLOWED_FILE_TYPES_TO_DOWNLOAD && NOC_EMAIL_EXTRACT.ALLOWED_FILE_TYPES_TO_DOWNLOAD.length && (!fileType || !NOC_EMAIL_EXTRACT.ALLOWED_FILE_TYPES_TO_DOWNLOAD.includes(fileType))) {
                            continue;
                        }
                        let fetch = imap.fetch(attrs.uid, {
                            bodies: [attachment.partID],
                            struct: true
                        });

                        let now = new Date();
                        let emailFrom = (attrs && attrs.envelope && attrs.envelope.from && attrs.envelope.from.length && attrs.envelope.from[0].mailbox + '@' + attrs.envelope.from[0].host)
                        let attachemntFileName = getEmailAttchmentName(attachment);
                        let insertObj = {
                            mailbox: {
                                email: emailCredrentials.email
                            },
                            uploadStatus: NOC_EMAIL_EXTRACT.UPLOAD_STATUS.PENDING,
                            emailNo: attrs.uid,
                            emailReceivedOn: attrs.date,
                            fileName: attachemntFileName,
                            subject: attrs && attrs.envelope && attrs.envelope.subject || "",
                            emailFrom,
                            emailMeta: {
                                from: attrs && attrs.envelope && attrs.envelope.from && attrs.envelope.from || "",
                                replyTo: attrs && attrs.envelope && attrs.envelope.replyTo && attrs.envelope.replyTo || ""
                            },
                            loanNo,
                            insertedOn: now,
                            modifiedOn: now
                        }
                        let { err, response } = await insertNOCLog(insertObj);
                        if (err) {
                            return;
                        }
                        fetch.on('message', processAttachment(attachment, response.insertedId, { fileName: attachemntFileName, loanNo }));
                        fetch.on('error', (ex) => {
                            saveErrorLog("processMessage", apiPath, {}, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
                        })
                    }
                }
            }
        });
    } catch (ex) {
        saveErrorLog("processMessage", apiPath, {}, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }
}

function findAttachmentFromAttrs(struct, attachments) {
    try {
        attachments = attachments || [];
        for (var i = 0, len = struct.length, r; i < len; ++i) {
            if (Array.isArray(struct[i])) {
                findAttachmentFromAttrs(struct[i], attachments);
            } else {
                if (struct[i].disposition && ['INLINE', 'ATTACHMENT'].indexOf(struct[i].disposition.type) > -1) {
                    attachments.push(struct[i]);
                }
            }
        }
        return attachments;
    } catch (ex) {
        saveErrorLog("findAttachmentFromAttrs", apiPath, {}, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }
}

function getFileType(attachment) {

    let type = null;

    if (attachment && attachment.disposition && attachment.disposition && attachment.disposition.params && attachment.disposition.params.filename) {
        let nameSplit = attachment.disposition.params.filename.split('.');
        type = nameSplit[nameSplit.length - 1];
    } else if (attachment && attachment.params && attachment.params && attachment.params.name) {
        let nameSplit = attachment.params.name.split('.');
        type = nameSplit[nameSplit.length - 1];
    }

    return type;
}


function getEmailAttchmentName(attachment) {

    if (attachment && attachment.disposition && attachment.disposition && attachment.disposition.params && attachment.disposition.params.filename) {
        return attachment.disposition.params.filename
    } else if (attachment && attachment.params && attachment.params && attachment.params.name) {
        return attachment.params.name
    }

    return ""
}


async function getUnProcessedIds(ids) {

    try {
        let dayStart = new Date(new Date().setHours(0, 0, 0, 0));
        let dayEnd = new Date(new Date().setHours(23, 59, 59, 999));

        let query = {
            insertedOn: { $gte: dayStart, $lte: dayEnd }
        };

        let processedIds = await NocLog.find(query).select({ "emailNo": 1 }).lean();

        if (!processedIds)
            return null

        let processedIdsHash = {};
        for (let i = 0; i < processedIds.length; i++) {
            processedIdsHash[processedIds[i].emailNo] = processedIds[i]
        }

        let unProcessedIds = []

        for (let i = 0; i < ids.length; i++) {

            if (!processedIdsHash[ids[i]]) {
                unProcessedIds.push(ids[i])
            }
        }

        return unProcessedIds;
    } catch (error) {
        saveErrorLog("getUnProcessedIds", apiPath, { ids, query }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }
}


async function insertNOCLog(insertObj) {

    try {
        let insertedObj = await NocLog.insertMany([insertObj])

        return { err: null, response: { insertedId: insertedObj && insertedObj.length && insertedObj[0]._id.toString() } };
    } catch (ex) {
        saveErrorLog("insertNOCLog", apiPath, { insertObj }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        return { err: ex, response: null }
    }
}

function processAttachment(attachment, docId, payload) {

    let extension = getFileType(attachment);
    let fileName = generateUniqueFileName() + '.' + extension;
    let filePath = config.get("exportToExcelPath.generalReports") + fileName;
    let encoding = attachment.encoding;

    return function (msg) {
        msg.on('body', async function (stream) {
            try {
                //Create a write stream so that we can stream the attachment to file;
                let writeStream = fs.createWriteStream(filePath);
                writeStream.on('finish', async function () {
                    await uploadNocDocument(payload.loanNo, filePath, fileName, docId)
                });

                writeStream.on('error', async function (ex) {
                    let updateQuery = {
                        "uploadStatus": NOC_EMAIL_EXTRACT.UPLOAD_STATUS.FAILED,
                        failureReason: ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex
                    }
                    await updateNOCLog(docId, updateQuery);
                    saveErrorLog("processAttachment", apiPath, {}, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
                })

                //stream.pipe(writeStream); this would write base64 data to the file.
                //so we decode during streaming using 
                if (encoding === 'BASE64') {
                    //the stream is base64 encoded, so here the stream is decode on the fly and piped to the write stream (file)
                    stream.pipe(new Base64Decode()).pipe(writeStream)
                } else {
                    //here we have none or some other decoding streamed directly to the file which renders it useless probably
                    stream.pipe(writeStream);
                }
            } catch (ex) {
                let updateQuery = {
                    "info.uploadStatus": NOC_EMAIL_EXTRACT.UPLOAD_STATUS.FAILED,
                    failureReason: ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex
                }
                await updateNOCLog(docId, updateQuery);
                saveErrorLog("processAttachment", apiPath, {}, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
            }
        });
    };

}

async function updateNOCLog(docId, updateObj) {

    try {
        let updatedObjBuild = {
            $set: updateObj
        }
        let updatedObj = await NocLog.updateOne({ _id: ObjectId(docId) }, updatedObjBuild);

        return { err: null, response: updatedObj }
    } catch (ex) {
        saveErrorLog("updateNOCLog", apiPath, { docId, updateObj }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        return { err: ex, response: null }
    }
}

function getLoanNo(subject) {
    let subjectSplit = subject.split("Loan No");
    if (subjectSplit && subjectSplit.length) {
        return subjectSplit[subjectSplit.length - 1] && subjectSplit[subjectSplit.length - 1].trim()
    } else
        return ""
}


module.exports = {
    extractEmail
}