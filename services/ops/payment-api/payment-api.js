const mongoose = require("mongoose");
const { getNewId, await<PERSON><PERSON><PERSON>, sleep, getProductType } = require("../common/common");
const config = require("config");
const { saveErrorLog } = require("../common/masters");
const { SettlementWebhookLog, SettlementMISStage } = require("../../../models/ops/settlement/settlement");
const apiPath = __dirname;
const debug = require("debug")("app:disbursal:disbursal");
let Crypto = require("crypto-js");
const { initateBatchPayment, confirmBatchPayment, getTransactionBatch, getAccoutBalance } = require('../../ops/disbursal/payment-gateway-service/payment-gateway-service');


async function getTransactionBatchApi(req, res) {

    let output = {
        data: null,
        success: false,
        message: "Some Error Occured",
        status: 400,
    };

    try {
        let { error, response } = validateRequest(req);

        if (error) {
            output.message = "UnAuthorized";
            return output;
        }

        let [transactionBatchError, transactionBatch, transactionBatchRawRequest] = await getTransactionBatch(req.body);
        output = {
            data: {
                error: transactionBatchError,
                response: transactionBatch,
                rawRequest: transactionBatchRawRequest
            },
            success: true,
            message: "Success",
            status: 200
        };

        return output;

    } catch (error) {
        saveErrorLog("getTransactionBatchApi", apiPath, { body: req.body }, error.stack ? error.stack.toString().replace(/\r?\n|\r/g, " ") : error);
        output.message =  error.stack ? error.stack.toString().replace(/\r?\n|\r/g, " ") : error;
        return output
    }
}

async function initateBatchPaymentApi(req, res) {

    let output = {
        data: null,
        success: false,
        message: "Some Error Occured",
        status: 400,
    };

    try {
        let { error, response } = validateRequest(req);

        if (error) {
            output.message = "UnAuthorized";
            return output;
        }

        if (!req.body || !req.body.transactions || !req.body.transactions.length) {
            output.message = "Missing transactions";
            return output;
        }

        let [initiateBatchPaymentError, initiateBatchPaymentResponse, initiateBatchPaymentRawRequest] = await initateBatchPayment(req.body);

        return {
            success: true,
            message: "Success",
            data: {
                error: initiateBatchPaymentError,
                response: initiateBatchPaymentResponse,
                rawRequest: initiateBatchPaymentRawRequest
            },
            status: 200
        }

    } catch (error) {
        saveErrorLog("initateBatchPaymentApi", apiPath, { body: req.body }, error.stack ? error.stack.toString().replace(/\r?\n|\r/g, " ") : error);
        output.message =  error.stack ? error.stack.toString().replace(/\r?\n|\r/g, " ") : error;
        return output
    }
}

async function confirmBatchPaymentApi(req, res) {

    let output = {
        data: null,
        success: false,
        message: "Some Error Occured",
        status: 400,
    };

    try {
        let { error, response } = validateRequest(req);

        if (error) {
            output.message = "UnAuthorized";
            return output;
        }

        let [confirmPaymentError, confimPaymentResponse, confirmPayoutRawRequest] = await confirmBatchPayment(req.body)

        return {
            success: true,
            message: "Success",
            data: {
                error: confirmPaymentError,
                response: confimPaymentResponse,
                rawRequest: confirmPayoutRawRequest
            },
            status: 200
        }
    } catch (error) {
        saveErrorLog("confirmBatchPaymentApi", apiPath, { body: req.body }, error.stack ? error.stack.toString().replace(/\r?\n|\r/g, " ") : error);
        output.message = error.stack ? error.stack.toString().replace(/\r?\n|\r/g, " ") : error;
        return output
    }
}

function validateRequest(req) {

    try {

        if (!req.headers || !req.headers["timestamp"])
            return { error: "Missing timeStamp", response: null };

        if (!req.headers || !req.headers["hmac"])
            return { error: "Missing hmac", response: null };

        if (!req.body) {
            return { error: "Missing body", response: null };
        }

        let timestamp = req.headers['timestamp'];
        let hmac = req.headers['hmac'];
        let message = JSON.stringify(req.body) + timestamp;

        let secretKey = config.get("paymentApi.secretKey");
        const hmacCalculated = Crypto.HmacSHA256(message, secretKey).toString();

        if (hmacCalculated == hmac) {
            return { error: null, response: true }
        }

        return { error: "Invalid hmac", response: null }

    } catch (error) {
        return { error, response: null };
    }
}

async function getAccountBalance(req, res) {

    let output = {
        data: null,
        success: false,
        message: "Some Error Occured",
        status: 400,
    };

    try {
        let { error, response } = validateRequest(req);

        if (error) {
            output.message = "UnAuthorized";
            return output;
        }

        let [balanceError, balanceResponse, balanceRequest] = await getAccoutBalance(req.body);

        output.data = {
            error: balanceError,
            response: balanceResponse,
            rawRequest: balanceRequest
        };
        output.success = true;
        output.message = "Success";
        return output;

    } catch (error) {
        saveErrorLog("getAccountBalance", apiPath, { body: req.body }, error.stack ? error.stack.toString().replace(/\r?\n|\r/g, " ") : error);
        output.message = error.stack ? error.stack.toString().replace(/\r?\n|\r/g, " ") : error;
        return output;
    }

}

module.exports = {
    getTransactionBatchApi,
    initateBatchPaymentApi,
    confirmBatchPaymentApi,
    getAccountBalance
}