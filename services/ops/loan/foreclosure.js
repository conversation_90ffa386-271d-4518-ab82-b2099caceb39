//Libraries
// -------------------------------------------------------------------------------------------------------------------------
const mongoose = require("mongoose");
const _ = require("lodash");
const moment = require("moment");
const config = require("config");
const DateDiff = require("date-diff");
const { dateAdd } = require("dateadd");
const json2xls = require("json2xls");
const fs = require("fs");
const debug = require("debug")("app:admin");
const IsNullEmpty = require("isnullemptyorwhitespace");
const fetch = require("node-fetch");
const apiPath = __dirname;
const { includes, forEach } = require("lodash");
const { mode } = require("crypto-js");
const joi = require("@hapi/joi");

// -------------------------------------------------------------------------------------------------------------------------

//Models
// ------------------------------------------------------------------------------------------------------------------------------------------
const { LoanInfo } = require("../../../models/ops/loan/loan");
const { PartnerMaster } = require("../../../models/ops/partner/partner");
// ------------------------------------------------------------------------------------------------------------------------------------------

//External Methods
// -------------------------------------------------------------------------------------------------------------------------
const { saveErrorLog } = require("../../../services/ops/common/masters");
const { getInstallments, getLoanFees, waveoffPenalty, adjustInterestForForeclosure, applyLateFee } = require("../loan/installments");
const { requestLoanRepayment, requestConsolidatedLoanRepayment } = require("../loan/postDisbursal");
const Logger = require("../../../middleware/logger");
const logger = new Logger();
// -------------------------------------------------------------------------------------------------------------------------


//Internal Methods
// -------------------------------------------------------------------------------------------------------------------------
module.exports.getLoanListForForeclosure = async function (model) {
    let output = {
        data: null,
        success: false,
        message: "Some Error Occured",
        status: 200,
    };
    try {
        let page = model.page || 1;
        let limit = model.limit || 10;

        let fromParts = model.fromDate.split("/");
        let fromDate = new Date(fromParts[2], fromParts[1] - 1, fromParts[0], 0, 0, 0);
        let toParts = model.toDate.split("/");
        let toDate = new Date(toParts[2], toParts[1] - 1, toParts[0], 23, 59, 59);

        let query1 = {};
        let query2 = {};
        let query3 = {};

        if (model.loanNo !== "")
            query1["loanNo"] = model.loanNo.trim();

        if (!model.merchantCode && !model.mobileNo && !model.panNo && !model.name)
            query2["$or"] = [
                {
                    status: {
                        $nin: ["Initiated", "Draft", "Under Review", "Loan Contract Accepted", "Recourse Pending"]
                    }
                },
                {
                    $and: [{
                        status: "Under Review"
                    },
                    {
                        "userInfo.userDetails.kycStatus": "Approved"
                    }
                    ]
                },
                {
                    $and: [{
                        status: "Loan Contract Accepted"
                    },
                    {
                        "userInfo.userDetails.kycStatus": "Approved"
                    }
                    ]
                },
                {
                    $and: [{
                        status: "Recourse Pending"
                    },
                    {
                        "userInfo.userDetails.kycStatus": "Approved"
                    }
                    ]
                }
            ];
        else
            query2["$or"] = [
                {
                    status: {
                        $nin: ["Initiated", "Draft", "Under Review"]
                    }
                },
                {
                    $and: [{
                        status: "Under Review"
                    },
                    {
                        "userInfo.userDetails.kycStatus": "Approved"
                    }
                    ]
                },
                {
                    status: "Draft"
                }
            ];

        if (model.status !== "") {
            query1.status = model.status
        } else {
            query1.status = { $in: ["Loan In Progress", "Written-Off"] }
        }

        if (model.postDisbursalStatus !== "") {
            query1.postDisbursalStatus = model.postDisbursalStatus
        }

        if (model.mobileNo !== "") {
            query2["userInfo.userDetails.mobileNo.mobileNumber"] = model.mobileNo.trim();
        }

        if (model.merchantCode)
            query1["merchantCode"] = model.merchantCode.trim();

        if (model.panNo)
            query2["userInfo.panNo"] = {
                $regex: new RegExp("^" + model.panNo.trim() + "$", "i")
            };

        if (model.partner !== "all")
            query1["partner"] = model.partner;


        if (model.name !== "") {
            query3["$or"] = [{
                $expr: {
                    $regexMatch: {
                        input: {
                            $trim: {
                                input: {
                                    $concat: ["$userInfo.userDetails.firstName", " ", { $ifNull: ["$userInfo.userDetails.lastName", " "] }]
                                }
                            }
                        },
                        regex: {
                            $trim: {
                                input: model.name
                            }
                        },
                        options: "i"
                    }

                }
            }]
        }

        if (_.isEmpty(query1)) query1["updatedOn"] = { $gte: fromDate, $lte: toDate };

        if (!model.merchantCode && !model.mobileNo && !model.panNo && !model.name)
            query1["isDraft"] = false;

        const loans = await LoanInfo.aggregate([
            {
                $match: query1,
            },
            {
                $lookup: {
                    from: "UserInfo",
                    localField: "userId",
                    foreignField: "_id",
                    as: "userInfo",
                },
            },
            { $unwind: "$userInfo" },
            {
                $match: query2,
            },
            {
                $match: query3,
            },
            {
                $lookup: {
                    from: "PreApprovals",
                    let: { trxnId: "$trxnId", productId: "$productId", partnerId: "$partnerId", merchantCode: "$merchantCode" },
                    pipeline: [
                        { $match: { $expr: { $and: [{ $eq: ["$partnerId", "$$partnerId"] }, { $eq: ["$merchantCode", "$$merchantCode"] }, { $or: [{ $eq: ["$trxnId", "$$trxnId"] }, { $eq: ["$productId", "$$productId"] }] }] } } },
                    ],
                    as: "preApprovals",
                }
            },
            {
                $project: {
                    "userInfo.userDetails": 1,
                    loanNo: 1,
                    _id: 1,
                    amount: 1,
                    purpose: 1,
                    maturity: 1,
                    tenure: 1,
                    interestRate: 1,
                    loanType: 1,
                    isMaturityMonth: 1,
                    insertedOn: 1,
                    adminRemarks: 1,
                    rejectDescription: 1,
                    repaymentFrequency: 1,
                    postDisbursalStatus: 1,
                    clientRemarks: 1,
                    status: 1,
                    emiAmount: 1,
                    partner: 1,
                    preApprovals: "$preApprovals",
                    merchantCode: 1,
                    trxnId: 1,
                    partnerId: 1,
                    updatedOn: 1,
                    kycStatus: "$userInfo.userDetails.kycStatus",
                    isStp: 1,
                    isForceMandate: 1,
                    loanStepDetails: 1,
                    lenderDetails: 1
                },
            },
            {
                $sort: { updatedOn: -1 }
            },
            {
                $group: {
                    _id: null,
                    data: { $push: "$$ROOT" },
                    totalCount: { $sum: 1 }
                }
            },
            {
                $unwind: "$data"
            }
        ])
            .skip((page - 1) * limit)
            .limit(limit)
            .allowDiskUse(true);

        let totalCount = loans && loans.length ? loans[0].totalCount : 0
        // let paginatedLoans = loans.map((item) => item.data);
        let paginatedLoans = [];

        for (let i = 0; i < loans.length; i++) {

            let item = loans[i];

            if (item && item.data && item.data.userInfo && item.data.userInfo.userDetails && item.data.userInfo.userDetails.password)
                delete item.data.userInfo.userDetails.password

            paginatedLoans.push(item.data);
        }

        output = {
            data: paginatedLoans,
            success: true,
            message: "",
            status: 200,
            totalPages: Math.ceil(totalCount / limit)
        };
    } catch (ex) {
        logger.logs(ex);
        saveErrorLog("getLoanListForForeclosure", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};

module.exports.getForeclosureDetails = async function (model) {
    let output = {
        data: {
            interestAmount: 0,
            totalEdis: 0,
            principalPending: 0,
            interestPending: 0,
            lateChargesPending: 0,
            principalOutstanding: 0,
            interestOutstanding: 0,
            outstandingEdis: 0
        },
        success: false,
        message: "Some Error Occured",
        status: 200,
    };

    try {
        const installmentDetails = await getInstallments(null, model.loanNo);

        if (installmentDetails.success) {
            const installments = installmentDetails.data;
            const totalEdis = installments.length;
            const interestAmount = installments.filter((x) => x.installmentInterest != null).reduce(function (prev, cur) {
                return prev + parseFloat(cur.installmentInterest);
            }, 0);

            let today = new Date();
            let toDate = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59);


            let principalPending = 0;
            let interestPending = 0;
            let principalOutstanding = 0;
            let interestOutstanding = 0;
            let outstandingEdis = 0;


            const pendingInstallments = installments.filter((x) => x.status == "Pending" && new Date(x.dueDate) <= toDate);

            if (pendingInstallments && pendingInstallments.length > 0) {
                for (let i = 0; i < pendingInstallments.length; i++) {
                    principalPending += pendingInstallments[i].principalOutstanding;
                    interestPending += pendingInstallments[i].interestOutstanding;
                }
            }

            const outstandingInstallments = installments.filter((x) => x.status == "Pending" && new Date(x.dueDate) > toDate);

            if (outstandingInstallments && outstandingInstallments.length > 0) {
                for (let i = 0; i < outstandingInstallments.length; i++) {
                    principalOutstanding += outstandingInstallments[i].principalOutstanding;
                    interestOutstanding += outstandingInstallments[i].interestOutstanding;
                }

                outstandingEdis = outstandingInstallments.length;
            }

            lateChargesPending = 0;

            const feeDetails = await getLoanFees(null, model.loanNo);

            if (feeDetails.success && feeDetails.data && feeDetails.data != null) {
                const loanFees = feeDetails.data;
                lateChargesPending = loanFees.filter((x) => x.status == "Pending" && x.isWaiveOff == false && x.feeAmount > 0).reduce(function (prev, cur) {
                    return prev + parseFloat(cur.feeAmount);
                }, 0);

            } else if (!feeDetails.success) {
                output.message = feeDetails.message;
                return output;
            }

            output = {
                data: {
                    interestAmount: +interestAmount.toFixed(2),
                    totalEdis: totalEdis,
                    principalPending: +principalPending.toFixed(2),
                    interestPending: +interestPending.toFixed(2),
                    lateChargesPending: +lateChargesPending.toFixed(2),
                    principalOutstanding: +principalOutstanding.toFixed(2),
                    interestOutstanding: +interestOutstanding.toFixed(2),
                    outstandingEdis: +outstandingEdis.toFixed(2)
                },
                success: true,
                message: "Executed Successfully",
                status: 200,
            };

        } else output.message = installmentDetails.message;
    } catch (ex) {
        logger.logs(ex);
        saveErrorLog("getForeclosureDetails", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};

module.exports.initiateForeclosure = async function (model, userId) {
    let output = {
        success: false,
        message: "Some Error Occured",
        status: 200,
    };

    try {
        // loanNo: model.loanNo,
        // isWaiveOff: model.isWaiveOff,
        // collectionMode: model.collectionMode,
        // foreclosureReason: model.foreclosureReason
        let collectionMode = "installment";
        if (!IsNullEmpty(model.loanNo)) {
            const loanInfo = await LoanInfo.findOne({ loanNo: model.loanNo }).select({ _id: 1, partnerId: 1, loanNo: 1, status: 1 });

            const partnerMaster = await PartnerMaster.findById(loanInfo.partnerId).select({ _id: 1, apiAccess: 1, partnerCode: 1 });

            if (!IsNullEmpty(partnerMaster.apiAccess.consolidatedLoanRepayment) && partnerMaster.apiAccess.consolidatedLoanRepayment == true) collectionMode = "single";


            let penaltyResponse = await applyLateFee(loanInfo.partnerId, null, null, true, model.loanNo, loanInfo.status);

            if (penaltyResponse.success) {
                if (model.isWaiveOff == true) {
                    const loanNos = [];
                    loanNos.push(model.loanNo);
                    await waveoffPenalty(loanNos, null);
                }

                await adjustInterestForForeclosure(model.loanNo, userId);
                const conf = JSON.parse(JSON.stringify(config.get(partnerMaster.partnerCode.toLowerCase())));

                const model1 = {
                    "partnerId": loanInfo.partnerId,
                    "status": loanInfo.status,
                    "loanNo": loanInfo.loanNo,
                    "isIncludeHold": true,
                    conf
                };

                if (collectionMode == "installment")
                    requestLoanRepayment(model1);
                else
                    requestConsolidatedLoanRepayment(model1);

                await LoanInfo.findOneAndUpdate({ loanNo: model.loanNo }, { $set: { foreclosureReason: model.foreclosureReason } });

                output = {
                    success: true,
                    message: "Initiated Successfully",
                    status: 200,
                };

            } else output.message = penaltyResponse.message;

        } else output.message = "loanNo is Mandatory";

    } catch (ex) {
        logger.logs(ex);
        saveErrorLog("initiateForeclosure", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
}