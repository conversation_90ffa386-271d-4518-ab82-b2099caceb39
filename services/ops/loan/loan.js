//Libraries
// ------------------------------------------------------------------------------------------------------------------------------------------
const mongoose = require("mongoose");
const debug = require("debug")("app:loan");
const moment = require("moment");
const { query } = require("winston");
//const { model } = require("mongoose");
const { round, lte } = require("lodash");
const config = require("config");
const fs = require("fs");
const path = require("path");
const puppeteer = require("puppeteer");
const handlebars = require("handlebars");
const dateFormat = require("dateformat");
const _ = require("lodash");
const { isDate, dateAdd } = require("dateadd");
const IsNullEmpty = require("isnullemptyorwhitespace");
const DateDiff = require("date-diff");
const apiPath = __dirname;
const { ToWords } = require('to-words');
// ------------------------------------------------------------------------------------------------------------------------------------------

//Models
// ------------------------------------------------------------------------------------------------------------------------------------------
const { UserPartnerInfo } = require("../../../models/ops/registration/users");
const { LoanInfo, LoanIdRepository, LoanInfoHistory, updateBankDetailHistory } = require("../../../models/ops/loan/loan");
const { PartnerMaster, PartnerLoans, PreApprovals, PartnerConfig, ProductMaster, ProductPartnerConfig, accessDetailsModel, LendingPartnerAllocation, ConfigParameters } = require("../../../models/ops/partner/partner");
const { NpciHolidayList } = require("../../../models/ops/digio/digioApi");
const { UserInfo } = require("../../../models/ops/registration/users");
const { OtpInfo, NotificationTemplates, DefinationDetailsMaster } = require("../../../models/ops/common/master");
const { Links } = require("../../../models/ops/common/links");
const { CampaignMaster } = require("../../../models/ops/campaign/campaign");
// ------------------------------------------------------------------------------------------------------------------------------------------

//External Methods
// ------------------------------------------------------------------------------------------------------------------------------------------
const { sendMail } = require("../../../services/ops/common/mailer");
const payOne = require("../../../services/ops/partner/payOne");
const { saveErrorLog } = require("../../../services/ops/common/masters");
const { getMailModel, ceil10, floor10, round10, pushToNotifications, convertToCamelCase, insertStepsInLoanInfo,
  updateUserAndLoanSteps, getDMYFormattedDate, getProductType, getAge, getSmsModel, getYMDFormattedDate } = require("../../../services/ops/common/common");
const { DigioMandateGeneration, DigioNachResponseLog, DigioNachWebHookResponse, DigioMandateLog, DigioBankList } = require("../../../models/ops/digio/digioApi");
const postDisbursal = require("./postDisbursal");
const eNachService = require("../mandate/eNach");
const { generateCustomUrl, createFundfinaShortLink } = require("../common/links");
const { logUserOtp } = require("../common/masters");
const { callBorrowingPartnerAPI, getOfferDetails } = require("../../../services/ops/partner/borrowingPartner");
const { DEFAULT_UUID_LENGTH } = require("short-unique-id");
const { validateAndSaveBankDetails, checkMandateCurrentBankDetails, sendDigilockerRequest } = require("../../../services/ops/verification/digioApi");
const { getMaskedMobileNo, generateOtpForMerchant } = require("../../../services/ops/registration/verificationService");
const { computeLoanCashback } = require("../../../services/ops/loan/cashback");
const { uploadToS3, getSignedUrlToDownload } = require("../../../models/ops/aws/s3");
const installmentService = require("./installments");
const { sendOtp, verifyOtp } = require('../otp-service/otp-service');
const Logger = require("../../../middleware/logger");
const logger = new Logger();
const { OTP_CHANNELS, NOTIFICATION_TYPES, ROLES } = require('../../ops/common/constants').Constants
// ------------------------------------------------------------------------------------------------------------------------------------------

//Internal WorkFlow Methods
// ------------------------------------------------------------------------------------------------------------------------------------------
module.exports.isValidForNewLoan = async function (model, partnerId = null) {
  try {
    if (model.role != "User")
      return {
        data: "",
        success: false,
        message: "Invalid User Role for creating loan request",
        status: 400,
      };
    let query = {
      userId: model.id,
      status: {
        $nin: [
          "Rejected By Admin",
          "Rejected By Client",
          "Loan Closed",
          "Loan Cancelled",
          "Close Prepaid",
          "Close Repaid",
          "Closed Prepaid"
        ],
      },
    }
    let isWalkInCustomer = false;
    if (partnerId) {
      query.partnerId = partnerId;
      const partnerMaster = await PartnerMaster.findById(partnerId);
      if (partnerMaster && partnerMaster.isWalkInCustomer) isWalkInCustomer = true;
    }
    if (!isWalkInCustomer) {
      return {
        data: "",
        success: false,
        message: "Walkin Customer Not Allowed For Partner",
        status: 200
      };
    }
    const loan = await LoanInfo.find(query);

    const user = await UserInfo.findById(model.id);

    if (
      loan.length > 0 &&
      user.userDetails.kycStatus &&
      user.userDetails.kycStatus.toLowerCase() != "rejected"
    ) {
      return {
        data: "",
        success: false,
        message: "You have already submitted a loan request",
        status: 200,
      };
    } else if (loan.length > 0 && loan[0].isDraft) {
      return {
        data: "",
        success: false,
        message: "Loan already in Draft",
        status: 200,
      };
    } else {
      return {
        data: "",
        success: true,
        message: "Allow",
        status: 200,
      };
    }
  } catch (error) {
    saveErrorLog("isValidForNewLoan", apiPath, { model, partnerId }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    return {
      data: "",
      success: false,
      message: "Some Error",
      status: 200
    };
  }

};

module.exports.getLoanDetails = async function (model, partnerId = null) {

  try {
    const user = await UserInfo.findById(model.id).select("userDetails").lean();

    if (user && user.userDetails && user.userDetails.password)
      delete user.userDetails.password

    // const loan = await LoanInfo.find({
    //   userId: model.id,
    // }).sort({ insertedOn: -1 });

    // const user = await UserInfo.findById("611256b88912db4219e7af50").select("userDetails");

    let query1 = {};

    query1["userId"] = user._id;
    if (partnerId) {
      query1["partnerId"] = mongoose.Types.ObjectId(partnerId);
    }

    let loans = [];

    // let loan = await LoanInfo.aggregate([
    //   {
    //     $match: query1,
    //   },
    //   {
    //     $lookup: {
    //       from: "PartnerMaster",
    //       localField: "partnerId",
    //       foreignField: "_id",
    //       as: "partnerMaster",
    //     },
    //   },
    //   {
    //     $project: {
    //       "partnerMaster": 1,
    //       loanNo: 1,-
    //       userId: 1,-
    //       _id: 1,-
    //       amount: 1,-
    //       purpose: 1,-
    //       maturity: 1,-
    //       tenure: 1,-
    //       interestRate: 1,-
    //       loanType: 1,-
    //       isMaturityMonth: 1,-
    //       insertedOn: 1,-
    //       adminRemarks: 1,-
    //       rejectDescription: 1,-
    //       repaymentFrequency: 1,-
    //       postDisbursalStatus: 1,
    //       clientRemarks: 1,
    //       status: 1,-
    //       isDraft: 1-
    //     },
    //   },
    // ]).sort({ insertedOn: -1 });

    let loan = await LoanInfo.find(query1).select({
      loanNo: 1,
      userId: 1,
      _id: 1,
      amount: 1,
      purpose: 1,
      maturity: 1,
      tenure: 1,
      interestRate: 1,
      loanType: 1,
      isMaturityMonth: 1,
      insertedOn: 1,
      adminRemarks: 1,
      rejectDescription: 1,
      repaymentFrequency: 1,
      postDisbursalStatus: 1,
      clientRemarks: 1,
      status: 1,
      isDraft: 1,
      disbursalDetails: 1
    }).sort({ insertedOn: -1 });

    let loanPartnerMaster = await PartnerMaster.findOne({ _id: partnerId });

    const loanIds = loan.map(x => x._id);

    let loanInstallments = await installmentService.getMultipleLoansInstallments(loanIds);
    if (!loanInstallments.success) {
      return {
        data: null,
        success: false,
        message: loanInstallments.message,
        status: 200,
      };
    }
    loanInstallments = await installmentService.convertMultipleEmiDetailsFormat(loanIds, loanInstallments.data);
    if (!loanInstallments.success) {
      return {
        data: null,
        success: false,
        message: loanInstallments.message,
        status: 200,
      };
    }

    for (const item of loan) {
      const installments = loanInstallments.data.filter(x => x.loanId.toString() == item._id.toString());
      const loanItem = {
        loanNo: item.loanNo,
        userId: item.userId,
        _id: item._id,
        amount: item.amount,
        purpose: item.purpose,
        maturity: item.maturity,
        tenure: item.tenure,
        interestRate: item.interestRate,
        loanType: item.loanType,
        isMaturityMonth: item.isMaturityMonth,
        insertedOn: item.insertedOn,
        adminRemarks: item.adminRemarks,
        rejectDescription: item.rejectDescription,
        repaymentFrequency: item.repaymentFrequency,
        postDisbursalStatus: item.postDisbursalStatus,
        clientRemarks: item.clientRemarks,
        status: item.status,
        emiDetails: installments,
        isDraft: item.isDraft,
        hideAcceptLink: (loanPartnerMaster && loanPartnerMaster.partnerCode === "Eko" || loanPartnerMaster && loanPartnerMaster.partnerCode === "MPay") ? true : false,
        bankDetails: item.disbursalDetails || {}
      };

      loans.push(loanItem);

    }

    return {
      data: {
        user: user,
        loans: loans,
      },
      success: true,
      message: "",
      status: 200,
    };
  } catch (ex) {
    saveErrorLog("getLoanDetails", apiPath, { model, partnerId }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    return {
      data: null,
      success: false,
      message: ex.message,
      status: 200,
    };
  }

};

module.exports.getPersonalLoanDetails = async function (model, partnerId = null) {

  try {
    const user = await UserInfo.findById(model.id).select("userDetails").lean();

    if (user && user.userDetails && user.userDetails.password)
      delete user.userDetails.password

    let query1 = {};

    query1["userId"] = user._id;
    query1["productType"] = "PL";

    if (partnerId) {
      query1["partnerId"] = mongoose.Types.ObjectId(partnerId);
    }

    let loans = [];

    let loan = await LoanInfo.find(query1).select({
      loanNo: 1,
      userId: 1,
      _id: 1,
      amount: 1,
      purpose: 1,
      maturity: 1,
      tenure: 1,
      interestRate: 1,
      loanType: 1,
      isMaturityMonth: 1,
      insertedOn: 1,
      adminRemarks: 1,
      rejectDescription: 1,
      repaymentFrequency: 1,
      postDisbursalStatus: 1,
      clientRemarks: 1,
      status: 1,
      isDraft: 1,
      disbursalDetails: 1
    }).sort({ insertedOn: -1 });

    let loanPartnerMaster = await PartnerMaster.findOne({ _id: partnerId });

    const loanIds = loan.map(x => x._id);

    let loanInstallments = await installmentService.getMultipleLoansInstallments(loanIds);
    if (!loanInstallments.success) {
      return {
        data: null,
        success: false,
        message: loanInstallments.message,
        status: 200,
      };
    }
    loanInstallments = await installmentService.convertMultipleEmiDetailsFormat(loanIds, loanInstallments.data);
    if (!loanInstallments.success) {
      return {
        data: null,
        success: false,
        message: loanInstallments.message,
        status: 200,
      };
    }

    for (const item of loan) {
      const installments = loanInstallments.data.filter(x => x.loanId.toString() == item._id.toString());

      const loanItem = {
        loanNo: item.loanNo,
        userId: item.userId,
        _id: item._id,
        amount: item.amount,
        purpose: item.purpose,
        maturity: item.maturity,
        tenure: item.tenure,
        interestRate: item.interestRate,
        loanType: item.loanType,
        isMaturityMonth: item.isMaturityMonth,
        insertedOn: item.insertedOn,
        adminRemarks: item.adminRemarks,
        rejectDescription: item.rejectDescription,
        repaymentFrequency: item.repaymentFrequency,
        postDisbursalStatus: item.postDisbursalStatus,
        clientRemarks: item.clientRemarks,
        status: item.status,
        emiDetails: installments,
        isDraft: item.isDraft,
        hideAcceptLink: (loanPartnerMaster && loanPartnerMaster.partnerCode === "Eko" || loanPartnerMaster && loanPartnerMaster.partnerCode === "MPay") ? true : false,
        bankDetails: item.disbursalDetails || {}
      };

      loans.push(loanItem);

    }

    return {
      data: {
        user: user,
        loans: loans,
      },
      success: true,
      message: "",
      status: 200,
    };
  } catch (ex) {
    saveErrorLog("getPersonalLoanDetails", apiPath, { model, partnerId }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    return {
      data: null,
      success: false,
      message: ex.message,
      status: 200,
    };
  }

};

module.exports.applyLoanData = async function (loanId, userId) {
  try {
    const loan = await LoanInfo.findOne({ _id: loanId, userId: userId });

    if (loan) {
      const stpCheck = await checkStpLoan(loanId);

      const displayText = await computeLoanCashback(loanId, false);
      logger.logs("displayText", displayText);
      if (displayText.success == false) {
        return {
          data: loan,
          success: false,
          message: displayText.message,
          status: 200,
          estimate: await getLoanEstimation(loanId, loan.amount),
          isStp: false,
          discountText: null,
          bankText: null,
          offerAmount: null,
          multipleOf: config.get("keys.loanAmountMultiple")
        };
      }

      let preApproval = null;

      if (loan.productId)
        preApproval = await PreApprovals.findOne({ partnerId: loan.partnerId, productId: loan.productId });

      if (!preApproval && loan.trxnId)
        preApproval = await PreApprovals.findOne({ partnerId: loan.partnerId, trxnId: loan.trxnId });

      return {
        data: loan,
        success: true,
        message: "",
        status: 200,
        estimate: await getLoanEstimation(loanId, loan.amount),
        isStp: stpCheck.success,
        discountText: displayText.discountText,
        bankText: displayText.bankText,
        offerAmount: preApproval && preApproval.approvedAmount ? preApproval.approvedAmount : null,
        multipleOf: config.get("keys.loanAmountMultiple")
      };
    } else
      return {
        data: loan,
        success: false,
        message: "No data found",
        status: 200,
        estimate: await getLoanEstimation(loanId, loan.amount),
        isStp: false,
        discountText: null,
        bankText: null,
        offerAmount: null,
        multipleOf: config.get("keys.loanAmountMultiple")
      };
  } catch (ex) {
    saveErrorLog("applyLoanData", apiPath, loanId, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    return {
      data: null,
      success: false,
      message: ex.message,
      status: 200
    };
  }
};

module.exports.insertUpdateLoan = async function (model, userId, req) {
  let user = await UserInfo.findById(userId);

  let eNach = false;

  if (model.amount < config.get("keys.minLoanAmount"))
    return {
      success: false,
      data: "",
      message: `Amount cannot be less than ${config.get("keys.minLoanAmount")}`,
      status: 200,
      url: "",
      isStp: false,
      eNach: eNach
    };

  if (model.amount % config.get("keys.loanAmountMultiple") !== 0)
    return {
      success: false,
      data: "",
      message: `Amount must be multiple of ${config.get("keys.loanAmountMultiple")}`,
      status: 200,
      url: "",
      isStp: false,
      eNach: eNach
    };

  if (model.loanId) {
    let loan = await LoanInfo.findById(model.loanId);

    /** ROLE CHECK */
    if (loan && req && req.user.role == ROLES.USER && req.user.id != loan.userId.toString()) {
      return {
        success: false,
        data: "",
        message: `Invalid Loan Id`,
        status: 200
      };
    }

    if (!loan)
      return {
        success: false,
        data: "",
        message: "Invalid loan Id",
        status: 400,
        url: "",
        isStp: false,
        eNach: eNach
      };
    else if (loan.status == "Review" || loan.isDraft || (user.userDetails.kycStatus == "Review" && (loan.status === "Under Review" || loan.status === "Loan Contract Accepted" || loan.status === "Recourse Pending"))) {
      let preApproval = null;

      if (loan.productId)
        preApproval = await PreApprovals.findOne({ partnerId: loan.partnerId, productId: loan.productId });

      if (!preApproval && loan.trxnId)
        preApproval = await PreApprovals.findOne({ partnerId: loan.partnerId, trxnId: loan.trxnId });

      if (preApproval && model.amount > preApproval.approvedAmount)
        return {
          success: false,
          data: "",
          message: "Loan amount accont be greater than offer amount",
          status: 200,
          url: "",
          isStp: false,
          eNach: eNach
        };

      loan.isMaturityMonth = false;
      loan.isDraft = loan.isDraft ? true : false;
      loan.isActive = true;
      loan.userId = userId;
      loan.amount = model.amount;
      loan.purpose = model.purpose;
      loan.description = model.description;
      loan.campaignTime = model.campaignTime ? model.campaignTime : "30 Days";
      loan.repaymentFrequency = model.repaymentFrequency;
      loan.currency = "Indian Rupee";
      loan.interestType = loan.interestType ? loan.interestType : "Compound Interest";
      loan.updatedOn = Date.now();
      loan.status = (loan.status === "Under Review" || loan.status === "Review") ? loan.status : "Draft";
      loan.loanType = "EMI";
      loan.partner = loan.partner ? loan.partner : "Fundfina";
      loan.productType = loan.productType ? loan.productType : "TL_90";
      loan.ipAddress = model.ipAddress ? model.ipAddress : ""

      const updateSteps = await insertStepsInLoanInfo(loan);

      if (!updateSteps.success)
        return {
          data: "",
          success: false,
          message: updateSteps.message,
          status: 200,
          url: "",
          isStp: false,
          eNach: eNach
        };

      loan = updateSteps.data.loan;

      //Update User and Loan Steps till Review
      const loanStep = await updateUserAndLoanSteps(loan, user, "Loan", null, null);

      loan = loanStep.data.loan;

      user = loanStep.data.user;

      let stpCheck = {};

      if (model.isStp)
        stpCheck = await checkStpLoan(model.loanId);

      if (!stpCheck.success) {
        loan.isStp = false;
        await user.save();
        const response = await loan.save();

        let digilocker = promptDigilocker(user);
        let isForceDigiLocker = false, isCaptureNewDocument = false

        let kycInfo = await checkKycExpire(user, loan, digilocker)

        if (kycInfo) {
          digilocker = kycInfo.digilocker
          isForceDigiLocker = kycInfo.isForceDigiLocker
          isCaptureNewDocument = kycInfo.isCaptureNewDocument
        }

        await setEmiAmount(model.loanId);

        return {
          data: response,
          success: true,
          message: "Updated successfully",
          status: 200,
          url: "",
          isStp: false,
          eNach: eNach,
          digilocker: digilocker,
          isForceDigiLocker,
          kycExpired: kycInfo.expired,
          isCaptureNewDocument
        };
      }
      else {
        await loan.save();

        loan = await LoanInfo.findById(model.loanId);

        const mandate = await createMandate(model.loanId, "insertUpdateLoan", false, true);

        if (mandate.success) {
          const kycStep = await updateUserAndLoanSteps(loan, user, "KYC", "User", user._id);

          const shopDetailsStep = await updateUserAndLoanSteps(kycStep.data.loan, kycStep.data.user, "Shop", null, null);

          const questionnairreStep = await updateUserAndLoanSteps(shopDetailsStep.data.loan, shopDetailsStep.data.user, "Questionnairre", null, null);

          const reviewStep = await updateUserAndLoanSteps(questionnairreStep.data.loan, questionnairreStep.data.user, "Review", null, null);

          loan = reviewStep.data.loan;

          user = reviewStep.data.user;

          await user.save();

          loan.isStp = true;

          const response = await loan.save();

          await setEmiAmount(model.loanId);

          if (mandate.data && mandate.data.includes("updateBankRedirect"))
            eNach = true;

          return {
            data: response,
            success: true,
            message: "Updated successfully",
            status: 200,
            url: mandate.data,
            isStp: true,
            eNach: eNach
          };
        }
        else {
          loan.isStp = false;
          await user.save();
          const response = await loan.save();

          await setEmiAmount(model.loanId);

          return {
            data: response,
            success: true,
            message: "Updated successfully",
            status: 200,
            url: "",
            isStp: false,
            eNach: eNach,
            digilocker: false
          };
        }
      }
    } else
      return {
        success: false,
        data: "",
        message: "Loan details cannot be edited",
        status: 400,
        url: "",
        isStp: false,
        eNach: eNach,
        digilocker: false
      };
  } else {
    let partnerMaster = null;

    if (model.partnerId)
      partnerMaster = await PartnerMaster.findById(model.partnerId);
    else
      partnerMaster = await PartnerMaster.findOne({ partnerCode: { $regex: "FUND", $options: "i" } });

    const loanTxnId = await getLoanId();

    let loan = new LoanInfo({
      isMaturityMonth: false,
      isDraft: true,
      isActive: true,
      userId: userId,
      amount: model.amount,
      purpose: model.purpose,
      description: model.description,
      campaignTime: model.campaignTime ? model.campaignTime : "30 Days",
      repaymentFrequency: model.repaymentFrequency,
      currency: "Indian Rupee",
      interestType: "Compound Interest",
      insertedOn: Date.now(),
      status: "Draft",
      loanType: "EMI",
      partner: partnerMaster.partnerName,
      partnerId: partnerMaster._id,
      loanNo: loanTxnId.data.toString(),
      productType: "TL_90",
      ipAddress: model.ipAddress ? model.ipAddress : "",
      updatedOn: Date.now()
    });

    const updateSteps = await insertStepsInLoanInfo(loan);

    if (!updateSteps.success)
      return {
        data: "",
        success: false,
        message: updateSteps.message,
        status: 200,
        url: "",
        isStp: false,
        eNach: eNach
      };

    loan = updateSteps.data.loan;

    const contactInformationStep = await updateUserAndLoanSteps(loan, user, "Contact Information", null, null);

    const otpScreenStep = await updateUserAndLoanSteps(contactInformationStep.data.loan, contactInformationStep.data.user, "OTP Screen", null, null);

    const otpMatchedStep = await updateUserAndLoanSteps(otpScreenStep.data.loan, otpScreenStep.data.user, "OTP Matched", null, null);

    const completeStep = await updateUserAndLoanSteps(otpMatchedStep.data.loan, otpMatchedStep.data.user, "Loan", null, null);

    if (!completeStep.success)
      return {
        data: "",
        success: false,
        message: completeStep.message,
        status: 400,
        url: "",
        isStp: false,
        eNach: eNach
      };

    loan = completeStep.data.loan;

    user = completeStep.data.user;

    const response = await loan.save();

    await user.save();

    let digilocker = promptDigilocker(user);
    let isForceDigiLocker = false, isCaptureNewDocument = false

    let kycInfo = await checkKycExpire(user, loan, digilocker)

    if (kycInfo) {
      digilocker = kycInfo.digilocker
      isForceDigiLocker = kycInfo.isForceDigiLocker
      isCaptureNewDocument = kycInfo.isCaptureNewDocument
    }


    return {
      data: response,
      success: true,
      message: "Saved successfully",
      status: 200,
      url: "",
      isStp: false,
      eNach: eNach,
      digilocker: digilocker,
      isForceDigiLocker,
      isCaptureNewDocument
    };
  }
};

module.exports.updateLoanStatus = async function (loanId, model) {
  try {
    const loanInfo = await LoanInfo.findById(loanId);

    if (loanInfo.length == 0) {
      return {
        data: "",
        success: false,
        message: "Invalid loan Id",
        status: 400,
      };
    } else {
      const partnerMaster = await PartnerMaster.findById(loanInfo.partnerId);

      if (partnerMaster.partnerCode.toLowerCase() == "mpay" || partnerMaster.partnerCode.toLowerCase() == "eko") {
        return {
          data: "",
          success: false,
          message: "Not Allowed For This Partner. Please Accept From Partner Portal",
          status: 400,
        };
      }

      if (!IsNullEmpty(partnerMaster) && model.loanStatus.toLowerCase() === "rejected by client") {
        const productType = getProductType(loanInfo.productType);
        const apiVar = partnerMaster.partnerCode.toLowerCase() + ".offerRejectedByUserApis";
        let apis = config.get(apiVar);

        if (apis) {
          apis = apis.filter((x) => x.productType == productType);
          if (apis.length > 0) {
            for (let i = 0; i < apis.length; i++) {
              const borrowingParterRequestModel = apis[i].model != null ? JSON.parse(JSON.stringify(apis[i].model)) : {};
              borrowingParterRequestModel.source = "application";
              borrowingParterRequestModel.module = "offerRejectedByUser";
              borrowingParterRequestModel.loanId = loanInfo._id;
              borrowingParterRequestModel.partnerId = partnerMaster._id;
              borrowingParterRequestModel.partnerCode = partnerMaster.partnerCode;
              borrowingParterRequestModel.partnerDisplayName = partnerMaster.displayName;
              borrowingParterRequestModel.apiType = apis[i].apiType;
              borrowingParterRequestModel.isRetryOffline = apis[i].isRetryOffline;
              borrowingParterRequestModel.url = apis[i].url;

              if (apis[i].isWaitForResponse) {
                const lendingApiResponse = await callBorrowingPartnerAPI(borrowingParterRequestModel);
                if (!lendingApiResponse.success) {
                  i = apis.length;
                  output.message = lendingApiResponse.message;
                  return output;
                }
              } else {
                callBorrowingPartnerAPI(borrowingParterRequestModel);
              }
            }
          }
        }
      }


      loanInfo.status = model.loanStatus;
      loanInfo.clientRemarks = model.rejectReason;
      loanInfo.rejectDescription = model.rejectDescription;
      loanInfo.flowDetails.push({
        Step: model.loanStatus,
        changedBy: loanInfo.userId,
        changedByRole: "User",
        insertedOn: Date.now(),
      });

      loanInfo.updatedOn = Date.now();

      const response = await loanInfo.save();

      //Need to send notification for Pay1

      return {
        data: response,
        success: true,
        message: "Status updated successfully",
        status: 200,
      };

    }
  } catch (ex) {
    saveErrorLog("updateLoanStatus", apiPath, { model, loanId }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    return {
      data: null,
      success: false,
      message: ex.message,
      status: 200,
    };
  }

};

async function emiSchedule(loanId, disbursalDate, isSave) {
  let output = {
    data: null,
    success: false,
    message: "Some Error Occurred",
    status: 501
  };
  try {
    const loan = await LoanInfo.findById(loanId);
    const partner = await PartnerMaster.findById(loan.partnerId);
    const _interestType = loan.interestType.trim().toLowerCase() === "simple Interest" ? "simple" : "compound";
    let roundType = _interestType === "compound" ? "upper" : "lower";
    let interestComputation = "Post";

    if (isSave) {
      const productType = IsNullEmpty(loan.productType) ? "TL_90" : loan.productType;
      if (productType) {
        const products = productType.split("_");

        if (products && products.length > 0) {
          const productMaster = await ProductMaster.findOne({ "product": products[0] });
          if (productMaster) {
            const productPartnerConfig = await ProductPartnerConfig.findOne({ partnerId: mongoose.Types.ObjectId(partner._id), productId: mongoose.Types.ObjectId(productMaster._id) });

            if (productPartnerConfig) interestComputation = productPartnerConfig.interestComputation;
          }
        }
      }
    }

    if (!IsNullEmpty(loan.lenderDetails)) {
      const lendingPartner = await PartnerMaster.findById(loan.lenderDetails[0].lenderPartnerId);

      if (!IsNullEmpty(lendingPartner)) {
        roundType = IsNullEmpty(lendingPartner.roundType) ? roundType : lendingPartner.roundType.toLowerCase();
      }
    }

    if (IsNullEmpty(loan)) {
      return {
        data: "",
        success: false,
        message: "Invalid loan Id",
        status: 400,
      };
    } else if (!isDate(disbursalDate)) {
      return {
        data: "",
        success: false,
        message: "disbursalDate not is proper format",
        status: 400,
      };
    } else if (typeof isSave != "boolean") {
      return {
        data: "",
        success: false,
        message: "isSave not is proper format",
        status: 400,
      };
    } else if (IsNullEmpty(loan.amount)) {
      return {
        data: "",
        success: false,
        message: "Loan Amount is not defined",
        status: 400,
      };
    } else if (IsNullEmpty(loan.interestRate)) {
      return {
        data: "",
        success: false,
        message: "interest rate is not defined",
        status: 400,
      };
    } else if (IsNullEmpty(loan.repaymentFrequency)) {
      return {
        data: "",
        success: false,
        message: "repaymentFrequency is not defined",
        status: 400,
      };
    } else if (IsNullEmpty(loan.tenure)) {
      return {
        data: "",
        success: false,
        message: "tenure is not defined",
        status: 400,
      };
    } else if (IsNullEmpty(loan.isMaturityMonth)) {
      return {
        data: "",
        success: false,
        message: "maturity month is not defined",
        status: 400,
      };
    } else if (IsNullEmpty(loan.interestType)) {
      return {
        data: "",
        success: false,
        message: "Interest type is not defined",
        status: 400,
      };
    } else if (IsNullEmpty(loan.status)) {
      return {
        data: "",
        success: false,
        message: "loan status is not defined",
        status: 400,
      };
    } else if (loan.amount == 0 || loan.interestRate == 0 || loan.tenure == 0) {
      return {
        data: "",
        success: false,
        message: "data not proper for creating repayment schedule",
        status: 400,
      };
    } else if (isSave) {
      const productType = IsNullEmpty(loan.productType) ? "TL_90" : loan.productType;
      let emis = [];
      if (loan.interestType.trim().toLowerCase() == "simple interest") {
        emis = await calculateEmi(
          loan.amount,
          loan.tenure,
          loan.interestRate,
          loan.isMaturityMonth,
          loan.repaymentFrequency,
          disbursalDate,
          "simple",
          false,
          roundType,
          productType,
          loan.primaryCollectionType === "eNach" ? true : false,
          loan.adhocDays,
          interestComputation
        );
      } else if (loan.interestType.trim().toLowerCase() == "compound interest") {
        emis = await calculateEmi(
          loan.amount,
          loan.tenure,
          loan.interestRate,
          loan.isMaturityMonth,
          loan.repaymentFrequency,
          disbursalDate,
          "compound",
          false,
          roundType,
          productType,
          loan.primaryCollectionType === "eNach" ? true : false,
          loan.adhocDays,
          interestComputation
        );
      }

      const savingInstallments = await installmentService.saveInstallments(loanId, emis);
      logger.logs("savingInstallments", savingInstallments);
      if (!savingInstallments.success) {
        output.message = savingInstallments.message;
        return output;
      } else
        return {
          data: emis,
          success: true,
          message: "emis fetched successfully",
          status: 200,
        };

    } else {
      let emis = [];
      let type = "";
      const productType = IsNullEmpty(loan.productType) ? "TL_90" : loan.productType;

      if (loan.interestType.trim().toLowerCase() == "simple interest") {
        emis = await calculateEmi(
          loan.amount,
          loan.tenure,
          loan.interestRate,
          loan.isMaturityMonth,
          loan.repaymentFrequency,
          disbursalDate,
          "simple",
          false,
          roundType,
          productType,
          loan.primaryCollectionType === "eNach" ? true : false,
          loan.adhocDays,
          interestComputation
        );
      } else if (loan.interestType.trim().toLowerCase() == "compound interest") {
        emis = await calculateEmi(
          loan.amount,
          loan.tenure,
          loan.interestRate,
          loan.isMaturityMonth,
          loan.repaymentFrequency,
          disbursalDate,
          "compound",
          false,
          roundType,
          productType,
          loan.primaryCollectionType === "eNach" ? true : false,
          loan.adhocDays,
          interestComputation
        );
      }

      return {
        data: emis,
        success: true,
        message: "emis fetched successfully",
        status: 200,
      };
    }
  } catch (ex) {
    const model = { loanId: loanId, disbursalDate: disbursalDate, isSave: isSave };
    saveErrorLog("emiSchedule", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex.toString());
    output.message = ex.message;
    return output;
  }
};
module.exports.emiSchedule = emiSchedule;

async function calculateEmi(loanAmount, tenure, interestRate, isMaturityMonth, repaymentFrequency, disbursalDate, interestType = "compound", considerHoliday = false, roundType = "upper", productType = "TL_90", holdCollection = false, adhocDays = null, interestComputation = "Post") {
  // logger.logs(`loanAmount ${loanAmount}`);
  // logger.logs(`interestRate ${interestRate}`);
  // logger.logs(`isMaturityMonth ${isMaturityMonth}`);
  // logger.logs(`repaymentFrequency ${repaymentFrequency}`);
  // logger.logs(`disbursalDate ${disbursalDate}`);
  // logger.logs(`interestType ${interestType}`);
  // logger.logs(`roundType ${roundType}`);
  // logger.logs(`productType ${productType}`);
  // logger.logs(`holdCollection ${holdCollection}`);
  // logger.logs(`adhocDays ${adhocDays}`);
  // logger.logs(`-------------------------------------------------`);
  try {
    let frequency;
    let repayments;
    let toDate;
    let temp;
    let type = repaymentFrequency.toLowerCase();

    if (isMaturityMonth) {
      toDate = dateAdd("day", tenure, disbursalDate);

      let diff = new DateDiff(toDate, disbursalDate);

      switch (type) {
        case "daily":
          frequency = 365;
          repayments = diff.days();
          break;
        case "weekly":
          frequency = 52;
          repayments = diff.weeks();
          break;
        case "fortnightly":
          frequency = 26;
          repayments = diff.days() / 15;
          break;
        case "monthly":
          frequency = 12;
          repayments = diff.months();
          break;
        case "adhoc":
          frequency = 12;
          repayments = diff.months();
          break;
      }
    } else {
      let newDate = dateAdd("year", 1, disbursalDate);
      // logger.logs(`newDate ${newDate}`);
      // logger.logs(`disbursalDate ${disbursalDate}`);
      let diff = new DateDiff(newDate, disbursalDate);
      let days = diff.days();
      //logger.logs(`days ${days}`);
      //const daysPerMonth = days / 12;

      switch (type) {
        case "daily":
          frequency = days;
          repayments = tenure;
          break;
        case "weekly":
          frequency = days / 7;
          repayments = ceil10(tenure / 7, 0);
          break;
        case "fortnightly":
          frequency = days / 15;
          repayments = ceil10(tenure / 15, 0);
          break;
        case "monthly":
          frequency = 12;
          repayments = ceil10(tenure / (days / 12), 0);
          break;
        case "adhoc":
          frequency = floor10(days / adhocDays, 0);
          repayments = ceil10(tenure / adhocDays, 0);
          break;
      }
      toDate = dateAdd("day", tenure, disbursalDate);
    }
    // logger.logs(`loanAmount ${loanAmount}`);
    // logger.logs(`interestRate ${interestRate}`);
    // logger.logs(`interestType ${interestType}`);
    // logger.logs(`tenure ${tenure}`);
    //logger.logs(`frequency ${frequency}`);
    //logger.logs(`repayments ${repayments}`);
    // logger.logs(`disbursalDate ${disbursalDate}`);
    //logger.logs(`toDate ${toDate}`);

    interestRate = interestRate / 100;
    let interestPerPeriod = interestRate / frequency;

    // logger.logs(`interestRate ${interestRate}`);
    // logger.logs(`frequency ${frequency}`);
    // logger.logs(`interestPerPeriod ${interestPerPeriod}`);

    let base = 1 + interestPerPeriod;

    let power;
    if (interestType == "compound") {
      power = Math.pow(base, repayments);
    } else if (interestType == "simple") {
      power = Math.pow(base, -repayments);
    }

    //logger.logs(`power ${power}`);
    //logger.logs(`repayments ${repayments}`);
    //logger.logs(power/repayments);
    if (interestType == "compound") {
      temp = loanAmount * (power / repayments);
    } else if (interestType == "simple") {
      power = 1 - power;
      let temp1 = loanAmount * interestPerPeriod;
      temp = temp1 / power;
    }

    let emi = roundType.toLowerCase() == "upper" ? ceil10(temp, -2) : floor10(temp, -2);

    //logger.logs(`total emi ${emi}`);

    temp = loanAmount / repayments;
    let totalPrincipleRecovered = roundType.toLowerCase() == "upper" ? ceil10(temp, -2) : floor10(temp, -2);
    // logger.logs(`total totalPrincipleRecovered ${totalPrincipleRecovered}`);

    if (interestType == "compound") {
      temp = emi - totalPrincipleRecovered;
    } else if (interestType == "simple") {
      temp = loanAmount * interestPerPeriod;
    }

    let totalInterest = roundType.toLowerCase() == "upper" ? ceil10(temp, -2) : floor10(temp, -2);
    //logger.logs(`totalInterest ${totalInterest}`);

    let principleOutstanding = loanAmount;
    let principleRecovered = 0;
    let emiNo = 1;
    let insertedOn = new Date();
    let installmentDetails = [];
    let interest = 0;

    switch (type) {
      case "daily":
        disbursalDate = dateAdd("day", 1, disbursalDate);
        break;
      case "weekly":
        disbursalDate = dateAdd("week", 1, disbursalDate);
        break;
      case "fortnightly":
        disbursalDate = dateAdd("fortnight", 1, disbursalDate);
        break;
      case "monthly":
        disbursalDate = dateAdd("month", 1, disbursalDate);
        break;
      case "yearly":
        disbursalDate = dateAdd("year", 1, disbursalDate);
        break;
      case "adhoc":
        disbursalDate = dateAdd("day", adhocDays, disbursalDate);
        break;
    }

    let daysSubstracted = 0;
    let isHoliday = false;

    let strDisbursalDate = moment(disbursalDate).format("DD/MM/yyyy");
    let holiday = await NpciHolidayList.findOne({ day: strDisbursalDate });

    //check if date is holiday
    //if holiday T-1 and check again
    //save minus days in a variable
    if (considerHoliday) {
      if (holiday)
        isHoliday = true;

      while (isHoliday) {
        disbursalDate = dateAdd("day", -1, disbursalDate);
        strDisbursalDate = moment(disbursalDate).format("DD/MM/yyyy");
        daysSubstracted += 1;

        holiday = await NpciHolidayList.findOne({ day: strDisbursalDate, isActive: true });

        if (!holiday)
          isHoliday = false;
      }
    }

    //const roundEmi = roundType == "upper" ? Math.ceil(emi) : Math.floor(emi);
    //logger.logs("emi", emi);
    let roundEmi = repayments == 1 ? emi : Math.ceil(emi);
    //let roundEmi = Math.ceil(emi);
    //logger.logs("roundEmi", roundEmi);
    const roundDiff = roundEmi - emi;
    // logger.logs("roundEmi", roundEmi);
    // logger.logs("emi", emi);
    // logger.logs("roundDiff", roundDiff);
    let extraCollected = 0;
    let totalPrincipalRecovered = 0;
    let newPrincipalOutstanding = 0;
    let tempRepayment = 1;

    do {
      if (disbursalDate > toDate) {
        disbursalDate = toDate;
      }
      temp = principleOutstanding - principleRecovered;

      if (productType == "DL_30") {
        //logger.logs("inside DL_30");
        if (interestType == "compound") {
          principleOutstanding = temp;
          principleRecovered = 0;
          temp = emi - principleRecovered;
          interest = round10(temp, -2);
          newPrincipalOutstanding = principleOutstanding;

        } else if (interestType == "simple") {
          principleOutstanding = temp;
          temp = principleOutstanding * interestPerPeriod;
          interest = round10(temp, -2);
          emi = interest;
          principleRecovered = 0;
          totalPrincipalRecovered = 0;
          newPrincipalOutstanding = principleOutstanding;
          roundEmi = round10(emi, -2);
        }
      } else {
        //logger.logs("inside TL_90");

        if (interestType == "compound") {
          principleOutstanding = temp;
          temp = loanAmount / repayments;
          principleRecovered = round10(temp, -2);
          //console.log("principleRecovered", principleRecovered);
          temp = emi - principleRecovered;
          if (tempRepayment == repayments) {
            const diff = loanAmount - (principleRecovered * repayments);
            principleRecovered = principleRecovered + diff;
          }


          // console.log("emi", emi);
          // console.log("interest", temp);
          // console.log("roundType", roundType);

          interest = round10(temp, -2);
          //console.log("interest", interest);
          newPrincipalOutstanding = round10(principleOutstanding, -2);
        } else if (interestType == "simple") {
          principleOutstanding = temp;
          temp = principleOutstanding * interestPerPeriod;
          interest = round10(temp, -2);
          temp = emi - interest;
          principleRecovered = roundType.toLowerCase() == "upper" ? ceil10(temp, -2) : floor10(temp, -2);
          principleRecovered = roundEmi - interest;
          totalPrincipalRecovered += principleRecovered;
          newPrincipalOutstanding = round10(principleOutstanding, -2);
        }
      }


      let emiLog = {
        emiNumber: emiNo,
        interestRecovered: interest,
        interestAmount: interest,
        principalRecovered: round10(principleRecovered, -2),
        principalOutstanding: newPrincipalOutstanding,
        penalty: 0,
        moratoriumInterest: 0,
        dumpRepaymentAmount: emi,
        rePaymentAmount: roundEmi,
        rePaymentDate: disbursalDate,
        rePaymentActualDate: disbursalDate,
        rePaymentStatus: "Pending",
        rePaymentReceivedDate: null,
        insertedOn: insertedOn,
        updatedOn: insertedOn,
        holdCollection: holdCollection
      };
      extraCollected += roundDiff;
      installmentDetails.push(emiLog);

      emiNo++;

      //add substracted days to disbursal date
      disbursalDate = dateAdd("day", daysSubstracted, disbursalDate);

      switch (type) {
        case "daily":
          disbursalDate = dateAdd("day", 1, disbursalDate);
          break;
        case "weekly":
          disbursalDate = dateAdd("week", 1, disbursalDate);
          break;
        case "fortnightly":
          disbursalDate = dateAdd("fortnight", 1, disbursalDate);
          break;
        case "monthly":
          disbursalDate = dateAdd("month", 1, disbursalDate);
          break;
        case "yearly":
          disbursalDate = dateAdd("year", 1, disbursalDate);
          break;
        case "adhoc":
          disbursalDate = dateAdd("day", adhocDays, disbursalDate);
          break;
      }

      //check if date is holiday
      //if holiday T-1 and check again
      //save minus days in a variable
      if (considerHoliday) {
        strDisbursalDate = moment(disbursalDate).format("DD/MM/yyyy");

        holiday = await NpciHolidayList.findOne({ day: strDisbursalDate });
        daysSubstracted = 0;

        if (holiday)
          isHoliday = true;

        while (isHoliday) {
          disbursalDate = dateAdd("day", -1, disbursalDate);
          strDisbursalDate = moment(disbursalDate).format("DD/MM/yyyy");
          daysSubstracted += 1;

          holiday = await NpciHolidayList.findOne({ day: strDisbursalDate, isActive: true });

          if (!holiday)
            isHoliday = false;
        }
      }
      tempRepayment++;
    } while (tempRepayment <= repayments);

    //reCalculation of Emi
    if (installmentDetails.length > 0) {
      installmentDetails[installmentDetails.length - 1].principalRecovered = installmentDetails[installmentDetails.length - 1].principalOutstanding;
      installmentDetails[installmentDetails.length - 1].dumpRepaymentAmount = installmentDetails[installmentDetails.length - 1].principalRecovered + installmentDetails[installmentDetails.length - 1].interestAmount;
      installmentDetails[installmentDetails.length - 1].rePaymentAmount = Math.ceil(installmentDetails[installmentDetails.length - 1].dumpRepaymentAmount);
    }

    if (interestType == "compound" && installmentDetails.length > 0 && extraCollected > 0) {
      //logger.logs("extraCollected", extraCollected);
      extraCollected = Math.ceil(extraCollected);
      //logger.logs("extraCollected", extraCollected);
      if (installmentDetails.length > 0) {
        let currentRow = installmentDetails.length - 1;

        let emirows = Math.ceil(extraCollected / installmentDetails[currentRow].interestAmount);

        emirows = emirows > installmentDetails.length ? installmentDetails.length : emirows;

        for (let i = 0; i < emirows; i++) {
          const emiDeduction = extraCollected >= installmentDetails[currentRow].interestAmount ? installmentDetails[currentRow].interestAmount : extraCollected;
          extraCollected = extraCollected - emiDeduction;
          installmentDetails[currentRow].interestAmount = parseFloat((installmentDetails[currentRow].interestAmount - emiDeduction).toFixed(2));
          installmentDetails[currentRow].dumpRepaymentAmount = roundType == "upper" ? ceil10(installmentDetails[currentRow].principalRecovered + installmentDetails[currentRow].interestAmount, -2) : floor10(installmentDetails[currentRow].principalRecovered + installmentDetails[currentRow].interestAmount, -2);
          installmentDetails[currentRow].rePaymentAmount = roundType == "upper" ? Math.ceil(installmentDetails[currentRow].dumpRepaymentAmount) : Math.floor(installmentDetails[currentRow].dumpRepaymentAmount);
          currentRow--;
        }

        if (extraCollected > 0) {
          installmentDetails[installmentDetails.length - 1].principalRecovered = ceil10((installmentDetails[installmentDetails.length - 1].principalRecovered - extraCollected), -2);
          installmentDetails[installmentDetails.length - 1].dumpRepaymentAmount = ceil10(installmentDetails[installmentDetails.length - 1].dumpRepaymentAmount - extraCollected, -2);
          installmentDetails[installmentDetails.length - 1].rePaymentAmount = Math.ceil(installmentDetails[installmentDetails.length - 1].dumpRepaymentAmount);
        }
      }
    }

    if (interestType == "compound") {
      for (const record of installmentDetails) {
        record.interestAmount = round10(record.rePaymentAmount - record.principalRecovered, -2);
      }
    }

    if (interestComputation.toLowerCase() == "pre" && installmentDetails.length > 0) {
      for (let i = 0; i < installmentDetails.length; i++) {
        const emiAmount = installmentDetails[i].rePaymentAmount - installmentDetails[i].interestAmount;
        installmentDetails[i].rePaymentAmount = Math.round(emiAmount);
      }
    }
    return installmentDetails;
  }
  catch (ex) {
    const model = {
      loanId: loanAmount,
      tenure: tenure,
      interestRate: interestRate,
      isMaturityMonth: isMaturityMonth,
      repaymentFrequency: repaymentFrequency,
      disbursalDate: disbursalDate,
      interestType: interestType,
      considerHoliday: considerHoliday,
      roundType: roundType,
      productType: productType
    };
    saveErrorLog("calculateEmi", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    throw ex;
  }
};
module.exports.calculateEmi = calculateEmi;

async function createContract(loan) {
  try {

    if (!loan.lenderDetails || !loan.lenderDetails[0])
      return {
        data: "",
        success: false,
        message: "Lending partner not found",
        status: 400,
      };

    let lenderId = loan.lenderDetails[0].lenderPartnerId;

    const user = await UserInfo.findById(loan.userId).select({ addressDetails: 1, questionnairreDetails: 1, shopDetails: 1, poaDetails: 1, userDetails: 1, panNo: 1, questionnairreDetails: 1, panDocVerification: 1, gstDetails: 1 });

    if (!lenderId) {
      return {
        data: "",
        success: false,
        message: "Lending partner not found",
        status: 400,
      };
    }

    let coAddress = _.filter(
      user.addressDetails,
      (x) => x.type == "correspondence"
    );

    let bank = "";
    let branch = "";
    let accountNo = "";
    let ifscCode = "";
    let accountType = "";
    let accountName = "";
    let timestamp = "";
    let maritalStatus = "";
    let gender = "";
    let frequency = "";
    let shopName = "";
    let shopAddress = "";
    let shopCity = "";
    let shopState = "";
    let shopPincode = "";
    let shopContactNo = "";
    let corr_addressline1 = "";
    let corr_addressline2 = "";
    let corr_addressline3 = "";
    let corr_city = "";
    let corr_state = "";
    let corr_pincode = "";
    let lender_addressline1 = "";
    let lender_addressline2 = "";
    let lender_addressline3 = "";
    let lender_city = "";
    let lender_state = "";
    let lender_pincode = "";
    let addressAt = "";
    //let interestType = "";
    let emiAmount = "";
    let emiDueDate = "";
    let lenderTimeStamp = "";
    let platformTimeStamp = "";
    let totalInterest = 0;
    let totalRepaymentAmount = 0;
    let termStartDate = null;
    let termEndDate = null;
    let grievanceOfficer = "";
    let disbursalConsentGivenOn = "";
    let collectionType = "";
    let ipAddress = "";
    let geoLocation = "";
    let contractSignDate = "";
    let shortContractSignTimeStamp = `${dateFormat(loan.loanContractDetails.acceptedOn, "dddd, mmmm dS, yyyy, h:MM:ss TT")}`;

    if (loan.isMaturityMonth) frequency = "Monthly";
    else if (
      loan.repaymentFrequency &&
      loan.repaymentFrequency.toLowerCase() == "daily"
    )
      frequency = " Days";
    else if (
      loan.repaymentFrequency &&
      loan.repaymentFrequency.toLowerCase() == "weekly"
    )
      frequency = " Weeks";
    else if (
      loan.repaymentFrequency &&
      loan.repaymentFrequency.toLowerCase() == "monthly"
    )
      frequency = " Months";
    else if (
      loan.repaymentFrequency &&
      loan.repaymentFrequency.toLowerCase() == "fortnightly"
    )
      frequency = " Fortnights";
    else if (
      loan.repaymentFrequency &&
      loan.repaymentFrequency.toLowerCase() == "adhoc"
    )
      frequency = " Days";

    if (user.questionnairreDetails) {
      if (
        user.questionnairreDetails.maritalStatus &&
        (user.questionnairreDetails.maritalStatus.toLowerCase() == "m" ||
          user.questionnairreDetails.maritalStatus.toLowerCase() == "married")
      )
        maritalStatus = "Married";
      else if (
        user.questionnairreDetails.maritalStatus &&
        (user.questionnairreDetails.maritalStatus.toLowerCase() == "s" ||
          user.questionnairreDetails.maritalStatus.toLowerCase() == "single")
      )
        maritalStatus = "Single";

      if (
        user.questionnairreDetails.gender &&
        (user.questionnairreDetails.gender.toLowerCase() == "m" ||
          user.questionnairreDetails.gender.toLowerCase() == "male")
      )
        gender = "Male";
      else if (
        user.questionnairreDetails.gender &&
        (user.questionnairreDetails.gender.toLowerCase() == "f" ||
          user.questionnairreDetails.gender.toLowerCase() == "female")
      )
        gender = "Female";
    }

    const lendingPartner = await PartnerMaster.findById(lenderId);

    if (!lendingPartner) {
      return {
        data: "",
        success: false,
        message: "Lending Partner not found",
        status: 400,
      };
    }

    if (lendingPartner.grievanceDetails) {
      if (lendingPartner.grievanceDetails.name)
        grievanceOfficer += `Name: ${lendingPartner.grievanceDetails.name}`;
      if (lendingPartner.grievanceDetails.email)
        grievanceOfficer += `, Email: ${lendingPartner.grievanceDetails.email}`;
      if (lendingPartner.grievanceDetails.contactNo)
        grievanceOfficer += `, Contact: ${lendingPartner.grievanceDetails.contactNo}`;
    }

    let userFirstName = user.userDetails.firstName ? user.userDetails.firstName.trim() : "";
    let userMiddleName = user.userDetails.middleName ? user.userDetails.middleName.trim() : "";
    let userLastName = user.userDetails.lastName ? user.userDetails.lastName.trim() : "";

    let fullBorrowerName = userFirstName;

    if (userMiddleName)
      fullBorrowerName += ` ${userMiddleName}`;

    if (userLastName)
      fullBorrowerName += ` ${userLastName}`;

    if (loan.loanContractDetails) {

      if (loan.loanContractDetails.isAccepted) {
        timestamp =
          `This contract has been digitally signed on 
        ${dateFormat(loan.loanContractDetails.acceptedOn, "dddd, mmmm dS, yyyy, h:MM:ss TT")} by ${convertToCamelCase(fullBorrowerName)}.`;

        lenderTimeStamp =
          `This contract has been digitally signed on 
        ${dateFormat(loan.loanContractDetails.acceptedOn, "dddd, mmmm dS, yyyy, h:MM:ss TT")} by ${convertToCamelCase(lendingPartner.partnerName)}.`;

        platformTimeStamp =
          `This contract has been digitally signed on 
        ${dateFormat(loan.loanContractDetails.acceptedOn, "dddd, mmmm dS, yyyy, h:MM:ss TT")} by Fundfina Marketplace Pvt Ltd.`;

        contractSignDate = dateFormat(loan.loanContractDetails.acceptedOn, "dd/mm/yyyy");
      }

      if (loan.loanContractDetails.disbursalConsent)
        disbursalConsentGivenOn =
          `This contract has been digitally signed on 
        ${dateFormat(loan.loanContractDetails.disbursalConsentGivenOn, "dddd, mmmm dS, yyyy, h:MM:ss TT")} by ${convertToCamelCase(fullBorrowerName)}.`;

      if (loan.loanContractDetails.ipAddress)
        ipAddress = loan.loanContractDetails.ipAddress;

      if (loan.loanContractDetails.coordinates && loan.loanContractDetails.coordinates.latitude)
        geoLocation = `${loan.loanContractDetails.coordinates.latitude}, ${loan.loanContractDetails.coordinates.longitude}`;

    }

    let contractTemplate = "";

    let showEmis = true;

    let p_type = loan.productType ? loan.productType.split("_") : "";

    p_type = p_type && p_type[0] ? p_type[0] : "";

    if (p_type === "LC")
      contractTemplate = lendingPartner.creditContractTemplate;
    else
      contractTemplate = lendingPartner.contractTemplate;

    const productMaster = await ProductMaster.findOne({ product: p_type });

    if (contractTemplate == "") {
      return {
        data: "",
        success: false,
        message: "Contract template not found",
        status: 400,
      };
    }

    if (p_type === "LC" && loan.isParentLoan)
      showEmis = false;

    const borrowerPartner = await PartnerMaster.findById(loan.partnerId);

    if (!borrowerPartner)
      return {
        data: "",
        success: false,
        message: "Borrower Partner not found",
        status: 400,
      };

    const borrowingPartnerConfig = await PartnerConfig.findOne({ partnerId: borrowerPartner._id });

    if (!borrowingPartnerConfig)
      return {
        data: "",
        success: false,
        message: "Borrower Partner Config not found",
        status: 400,
      };

    if (borrowingPartnerConfig.collectionType && borrowingPartnerConfig.collectionType.toLowerCase() === "wallet")
      collectionType = "Wallet";
    else if (borrowingPartnerConfig.collectionType && borrowingPartnerConfig.collectionType.toLowerCase() === "enach")
      collectionType = "NACH/PDC";

    let penaltyGraceDays = 0;
    if (loan.loanFeeType === "VERSION1") {
      const partnerProductConfig = await ProductPartnerConfig.find({ partnerId: loan.partnerId });
      const _partnerProductConfig = partnerProductConfig.filter((x) => x.productId.toString() == productMaster._id.toString());
      if (_partnerProductConfig && _partnerProductConfig.length > 0 && _partnerProductConfig[0].penaltyGraceDays) {
        const frquencyDays = _partnerProductConfig[0].penaltyGraceDays.filter((x) => x.frequency.toLowerCase() == loan.repaymentFrequency.toLowerCase());

        if (frquencyDays && frquencyDays.length > 0)
          penaltyGraceDays = frquencyDays[0].days;
      }
    }
    else if (loan.loanFeeType === "VERSION2")
      penaltyGraceDays = productMaster.penaltyGraceDays && productMaster.penaltyGraceDays.version2 ? productMaster.penaltyGraceDays.version2 : 0;


    if (loan.disbursalBankDetails.bankName) bank = loan.disbursalBankDetails.bankName;
    if (loan.disbursalBankDetails.branch) branch = loan.disbursalBankDetails.branch;
    if (loan.disbursalBankDetails.accountNo) accountNo = loan.disbursalBankDetails.accountNo;
    if (loan.disbursalBankDetails.ifsc) ifscCode = loan.disbursalBankDetails.ifsc;
    if (loan.disbursalBankDetails.accountType) accountType = loan.disbursalBankDetails.accountType;
    if (loan.disbursalBankDetails.accountName) accountName = loan.disbursalBankDetails.accountName;

    let lenderAddress = _.filter(
      lendingPartner.addressDetails,
      (x) => x.type == "correspondence"
    );

    const preApproval = await PreApprovals.findOne({ partnerId: mongoose.Types.ObjectId(loan.partnerId), merchantCode: loan.merchantCode, productType: loan.productType.split("_")[0], $or: [{ trxnId: loan.trxnId }, { productId: loan.trxnId }, { productId: loan.productId }] }).sort({ insertedOn: -1 });

    //logger.logs("preApproval", preApproval);
    let chargeInPer = 0;
    let processingCharges = 0;

    const charges = await getLoanCharges(loan.amount, loan._id, preApproval._id, false);

    if (!charges.success) {
      return {
        data: "",
        success: false,
        message: charges.message,
        status: 400,
      };
    }

    if (p_type !== "LC" || loan.isParentLoan) {
      processingCharges = charges.processingChargeAmt;
    }

    let gstCharges = charges.gstCharge;
    let stampingCharges = charges.stampingCharge;

    if (user.shopDetails && user.shopDetails.length > 0) {
      shopName = user.shopDetails[user.shopDetails.length - 1].shopName;
      shopAddress = user.shopDetails[user.shopDetails.length - 1].shopAddress;
      shopCity = user.shopDetails[user.shopDetails.length - 1].city;
      shopState = user.shopDetails[user.shopDetails.length - 1].state;
      shopPincode = user.shopDetails[user.shopDetails.length - 1].pincode;
      shopContactNo = user.shopDetails[user.shopDetails.length - 1].contactNo;
      shopType = user.shopDetails[user.shopDetails.length - 1].shopType;
    }

    if (coAddress[0].add1) corr_addressline1 = coAddress[0].add1;
    if (coAddress[0].add2) corr_addressline2 = coAddress[0].add2;
    if (coAddress[0].add3) corr_addressline3 = coAddress[0].add3;
    if (coAddress[0].city) corr_city = coAddress[0].city;
    if (coAddress[0].state) corr_state = coAddress[0].state;
    if (coAddress[0].pincode) corr_pincode = coAddress[0].pincode;

    if (lenderAddress[0].add1) lender_addressline1 = lenderAddress[0].add1;
    if (lenderAddress[0].add2) lender_addressline2 = lenderAddress[0].add2;
    if (lenderAddress[0].add3) lender_addressline3 = lenderAddress[0].add3;
    if (lenderAddress[0].city) lender_city = lenderAddress[0].city;
    if (lenderAddress[0].state) lender_state = lenderAddress[0].state;
    if (lenderAddress[0].pincode) lender_pincode = lenderAddress[0].pincode;

    if (shopAddress && shopAddress != "")
      addressAt = " having our registered office at " + shopAddress;

    const today = new Date();
    let emiDate = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 5, 30, 1, 0);

    if (loan.loanContractDetails && loan.loanContractDetails.acceptedOn)
      emiDate = new Date(loan.loanContractDetails.acceptedOn.getFullYear(), loan.loanContractDetails.acceptedOn.getMonth(), loan.loanContractDetails.acceptedOn.getDate(), 5, 30, 1, 0);

    const disbursalDate = loan.productType == "DL_30" ? dateAdd("day", -1, emiDate) : emiDate;

    let emiDetails = await emiSchedule(loan._id, disbursalDate, false);

    let disbursalAmount = 0;

    if (p_type === "LC")
      disbursalAmount = loan.amount - processingCharges;
    else
      disbursalAmount = loan.amount - processingCharges - gstCharges - stampingCharges;

    let isAdjustedPrinciple = false;

    if (!IsNullEmpty(emiDetails) && !IsNullEmpty(emiDetails.data)) {
      if (emiDetails.data.length > 0) {
        emiAmount = emiDetails.data[0].rePaymentAmount;
        emiDueDate = dateFormat(emiDetails.data[0].rePaymentDate, "dd/mm/yyyy");
        disbursalAmount = loan.productType == "DL_30" ? (disbursalAmount - emiDetails.data[0].rePaymentAmount) : disbursalAmount;

        if (emiDetails.data.length > 1 && (emiDetails.data[emiDetails.data.length - 1].principalRecovered != emiDetails.data[emiDetails.data.length - 2].principalRecovered)) {
          emiDetails.data[emiDetails.data.length - 1].principalOutstanding = `${emiDetails.data[emiDetails.data.length - 1].principalOutstanding.toString()}*`;
          isAdjustedPrinciple = true;
        }
      }

      totalInterest = emiDetails.data.filter((x) => x.interestAmount != 0).reduce(function (prev, cur) {
        return prev + cur.interestAmount;
      }, 0);

      totalRepaymentAmount = emiDetails.data.filter((x) => x.rePaymentAmount != 0).reduce(function (prev, cur) {
        return prev + cur.rePaymentAmount;
      }, 0);

      termStartDate = emiDetails.data[0].rePaymentDate;
      termEndDate = emiDetails.data[emiDetails.data.length - 1].rePaymentDate;
    }

    let poaNo = "";
    let gstNo = "";
    let poaType = "";

    if (user.poaDetails && user.poaDetails.length > 0) {
      poaNo = user.poaDetails[0].poaNo;
      poaType = user.poaDetails[0].poaType;
    }

    if (user.gstDetails && user.gstDetails.length > 0)
      gstNo = user.gstDetails[0].gstNo;

    let fullAddress = "";

    if (corr_addressline1)
      fullAddress = corr_addressline1;

    if (corr_addressline2)
      fullAddress += " " + corr_addressline2;

    if (corr_addressline3)
      fullAddress += " " + corr_addressline3;

    if (corr_city)
      fullAddress += ", " + corr_city;

    if (corr_state)
      fullAddress += ", " + corr_state;

    let loanStartDate = "";

    const tomoDate = dateAdd("day", 1, emiDate);

    if (p_type === "LC" && loan.isParentLoan && loan.loanContractDetails && loan.loanContractDetails.isAccepted)
      loanStartDate = dateFormat(loan.loanContractDetails.acceptedOn, "dd/mm/yyyy");
    else if (p_type === "LC" && !loan.isParentLoan)
      loanStartDate = dateFormat(tomoDate, "dd/mm/yyyy");

    let strToday = dateFormat(Date.now(), "dd/mm/yyyy");

    var monthNames = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];

    let day = "";
    let month = "";
    let year = "";

    if (loan.loanContractDetails && loan.loanContractDetails.acceptedOn) {
      strToday = dateFormat(loan.loanContractDetails.acceptedOn, "dd/mm/yyyy");
      day = loan.loanContractDetails.acceptedOn.getDate();
      month = monthNames[loan.loanContractDetails.acceptedOn.getMonth()];
      year = loan.loanContractDetails.acceptedOn.getFullYear();
    }

    const detailsMismatch = user.userDetails.detailsMismatch && user.userDetails.detailsMismatch.isMismatch ? true : false;

    let poiName = "";
    let poaName = "";
    let poiDob = "";
    let poaDob = "";
    let mismatchType = "";
    let nameMismatch = false;
    let dobMismatch = false;
    let nameAndDobMismatch = false;
    let fatherName = "";

    if (detailsMismatch) {
      if (user.userDetails.detailsMismatch && user.userDetails.detailsMismatch.type)
        mismatchType = user.userDetails.detailsMismatch.type

      if (user.userDetails.detailsMismatch && user.userDetails.detailsMismatch.details && user.userDetails.detailsMismatch.details.poiName)
        poiName = convertToCamelCase(user.userDetails.detailsMismatch.details.poiName);

      if (user.userDetails.detailsMismatch && user.userDetails.detailsMismatch.details && user.userDetails.detailsMismatch.details.poaName)
        poaName = convertToCamelCase(user.userDetails.detailsMismatch.details.poaName);

      if (user.userDetails.detailsMismatch && user.userDetails.detailsMismatch.details && user.userDetails.detailsMismatch.details.poiDob)
        poiDob = dateFormat(user.userDetails.detailsMismatch.details.poiDob, "dd/mm/yyyy");

      if (user.userDetails.detailsMismatch && user.userDetails.detailsMismatch.details && user.userDetails.detailsMismatch.details.poaDob)
        poaDob = dateFormat(user.userDetails.detailsMismatch.details.poaDob, "dd/mm/yyyy");
    }

    if (mismatchType === "Name" || mismatchType === "Name & DOB")
      nameMismatch = true;

    if (mismatchType === "DOB" || mismatchType === "Name & DOB")
      dobMismatch = true;

    if (mismatchType === "Name & DOB")
      nameAndDobMismatch = true;

    let age = "";

    if (user.userDetails.dob)
      age = getAge(user.userDetails.dob);

    if (user.panDocVerification && user.panDocVerification.ocr && user.panDocVerification.ocr.fathers_name)
      fatherName = convertToCamelCase(user.panDocVerification.ocr.fathers_name);

    const aprV1 = (processingCharges + totalInterest) / loan.amount;

    const daysInTerm = new DateDiff(termEndDate, termStartDate).days();

    const aprV2 = ((aprV1 / daysInTerm) * 365) * 100;

    const greivanceUrl = await ConfigParameters.findOne({ channel: "GRIEVANCE_URL", isActive: true });

    const tncUrl = await ConfigParameters.findOne({ channel: "TNC_URL", isActive: true });

    let loanType = "";

    if (p_type === "TL")
      loanType = "Term Loan";
    else if (p_type === "DL")
      loanType = "Daily Loan";
    else if (p_type === "LC")
      loanType = "Line of Credit";

    const toWords = new ToWords();

    const dataObj = {
      today: strToday,
      name: convertToCamelCase(fullBorrowerName),
      panNo: user.panNo,
      poaNo: poaNo,
      dob: dateFormat(user.userDetails.dob, "dd/mm/yyyy"),
      phone: "",
      maritalStatus: maritalStatus,
      poaType: poaType,
      mobile: user.userDetails.mobileNo[0] ? user.userDetails.mobileNo[0].mobileNumber : "", //mobile number change
      nationality: "INDIAN",
      emailId: user.userDetails.emailId[0],
      gender: gender,
      co_name: "",
      co_panNo: "",
      co_aadhaar: "",
      co_relationship: "",
      co_dob: "",
      corr_addressline1: corr_addressline1,
      corr_addressline2: corr_addressline2,
      corr_addressline3: corr_addressline3,
      corr_city: corr_city,
      corr_state: corr_state,
      corr_pincode: corr_pincode,
      per_addressline1: "",
      per_addressline2: "",
      per_addressline3: "",
      per_city: "",
      per_state: "",
      per_pincode: "",
      shopName: shopName,
      designation: "",
      doj: "",
      offPhone: "",
      offMobile: shopContactNo,
      offExp: "",
      offEmail: "",
      offFax: "",
      offGstNo: "",
      off1_addressline1: shopAddress,
      off1_city: shopCity,
      off1_state: shopState,
      off1_pincode: shopPincode,
      off2_addressline1: "",
      off2_city: "",
      off2_state: "",
      off2_pincode: "",
      amount: loan.amount,
      income: user.questionnairreDetails.monthlyIncome,
      purpose: loan.purpose,
      tenure: loan.tenure.toString() + " Days",
      bankName: bank,
      branchName: branch,
      accountNumber: accountNo,
      ifscCode: ifscCode,
      timestamp: timestamp,
      lenderName: lendingPartner.partnerName,
      lenderPanNo: lendingPartner.panNo,
      lenderAddress: lender_addressline1 + " " + lender_addressline2 + " " + lender_addressline3,
      lenderCity: lender_city,
      lenderState: lender_state,
      lenderPincode: lender_pincode,
      loanId: loan._id,
      processingFee: processingCharges,
      gstCharges: gstCharges,
      insuranceAmount: "0",
      applicationCharges: "0",
      stampingCharges: stampingCharges,
      preEmiInterest: "0",
      disbursementAmount: disbursalAmount,
      advanceEmiAmount: "0",
      roi: loan.interestRate,
      estimatedEmi: emiAmount,
      emiDate: emiDueDate,
      emiDueDate: "This may vary as per schedule",
      emiSchedule: emiDetails.data,
      amountInWords: inWords(loan.amount),
      atAddress: addressAt,
      frequency: loan.repaymentFrequency.toLowerCase(),
      loanNo: loan.loanNo,
      fullAddress: fullAddress,
      loanStartDate: loanStartDate,
      showEmis: showEmis,
      lenderTimeStamp: lenderTimeStamp,
      platformTimeStamp: platformTimeStamp,
      day: day,
      month: month,
      year: year,
      detailsMismatch: detailsMismatch,
      poiName: poiName,
      poaName: poaName,
      poiDob: poiDob,
      poaDob: poaDob,
      mismatchType: mismatchType,
      nameMismatch: nameMismatch,
      dobMismatch: dobMismatch,
      age: age,
      fatherName: fatherName,
      nameAndDobMismatch: nameAndDobMismatch,
      isAdjustedPrinciple: isAdjustedPrinciple,
      repaymentFrequency: loan.repaymentFrequency,
      noOfInstalments: emiDetails.data.length,
      totalInterest: round10(totalInterest, -2),
      totalRepaymentAmount: totalRepaymentAmount,
      apr: round10(aprV2, -2),
      grievanceOfficer: grievanceOfficer,
      disbursalConsent: loan.loanContractDetails && loan.loanContractDetails.disbursalConsent ? true : false,
      disbursalConsentGivenOn: disbursalConsentGivenOn,
      accountType: accountType,
      accountName: accountName,
      borrowerPartnerName: borrowerPartner.partnerName ? borrowerPartner.partnerName : "",
      greivanceUrl: greivanceUrl.config && greivanceUrl.config.url ? greivanceUrl.config.url : "",
      tncUrl: tncUrl.config && tncUrl.config.url ? tncUrl.config.url : "",
      collectionType: collectionType,
      ip: ipAddress,
      geoLocation: geoLocation,
      gstNo: gstNo,
      shopType: shopType,
      contractSignDate: contractSignDate,
      brandName: borrowerPartner.partnerName ? convertToCamelCase(borrowerPartner.partnerName) : "",
      shortContractSignTimeStamp: shortContractSignTimeStamp,
      loanType: loanType,
      amountInWords: toWords.convert(loan.amount),
      processingFeeInWords: toWords.convert(processingCharges),
      roiInWords: toWords.convert(loan.interestRate),
      aprInWords: toWords.convert(round10(aprV2, -2)),
      tenureInWords: toWords.convert(loan.tenure),
      termStartDate: dateFormat(termStartDate, "dd/mm/yyyy"),
      termEndDate: dateFormat(termEndDate, "dd/mm/yyyy"),
      processingFeePer: loan.processingFeePer,
      totalCharges: processingCharges + gstCharges + stampingCharges,
      totalChargesInWords: toWords.convert(processingCharges + gstCharges + stampingCharges),
      penalty: productMaster && productMaster.penaltyChargePercentage ? productMaster.penaltyChargePercentage : 0,
      penaltyGraceDays: penaltyGraceDays
    };

    let font = getContractFont(contractTemplate);

    let footer = "";

    if (!font)
      footer = `<div id="footer-template" style="font-size:10px !important; color:#808080; padding-left:10px; padding-right:10px; width: 100%"><table style="width: 100%; border: 1px solid #000; border-collapse: collapse;" cellpadding="3" cellspacing="3"><tr><td style="width: 15%; border: 1px solid #000;">Borrower Name:</td><td style="width: 35%; border: 1px solid #000;">${convertToCamelCase(fullBorrowerName)}</td><td style="width: 50%; border: 1px solid #000;"> ${timestamp}</td></tr></table></div>`;
    else
      footer = `<div id="footer-template" style="font-size:10px !important; color:#808080; padding-left:10px; padding-right:10px; width: 100%"><table style="width: 100%; border: 1px solid #000; border-collapse: collapse; font-family: ${font}" cellpadding="3" cellspacing="3"><tr><td style="width: 15%; border: 1px solid #000;">Borrower Name:</td><td style="width: 35%; border: 1px solid #000;">${convertToCamelCase(fullBorrowerName)}</td><td style="width: 50%; border: 1px solid #000;"> ${timestamp}</td></tr></table></div>`;

    const header = lendingPartner.headerContent ? lendingPartner.headerContent : "";

    await generateKfsDocument(
      dataObj,
      loan.userId,
      loan.loanNo,
      footer,
      lendingPartner.dispayHeaderFooter ? lendingPartner.dispayHeaderFooter : false,
      header
    );

    const response = await generateContract(
      dataObj,
      contractTemplate,
      loan.userId,
      loan.loanNo,
      footer,
      lendingPartner.dispayHeaderFooter ? lendingPartner.dispayHeaderFooter : false,
      header
    );

    return response;
  } catch (ex) {
    saveErrorLog("createContract", apiPath, { loanId: loan._id }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);

    return {
      data: "",
      success: false,
      message: ex.message ? ex.message : "",
      status: 500,
    };
  }
};

module.exports.createContract = createContract;

async function generateContract(
  dataObj,
  templateName,
  userId,
  loanId,
  footer,
  displayHeaderFooter,
  header
) {
  try {
    let proofImagePath = "ProofImages/" + userId + "/LoanContract";

    let filePath = proofImagePath + "/loanContract_" + loanId + ".pdf";

    let dataBinding = dataObj;

    let templatePath = "/templates/" + templateName;

    const templateHtml = fs.readFileSync(
      path.join(process.cwd(), templatePath),
      "utf8"
    );

    const template = handlebars.compile(templateHtml);

    const finalHtml = template(dataBinding);

    const options = {
      format: "A4",
      headerTemplate: header ? header : "<p></p>",
      footerTemplate: footer,
      displayHeaderFooter: displayHeaderFooter,
      margin: {
        top: header ? "80px" : "20px",
        right: "20px",
        bottom: "80px",
        left: "20px",
      },
      printBackground: true,
      // path: filePath,
    };

    const launchOptions = {
      ignoreHTTPSErrors: true,
      args: [
        "--unlimited-storage",
        "--full-memory-crash-report",
        "--disable-gpu",
        "--ignore-certificate-errors",
        "--no-sandbox",
        "--disable-setuid-sandbox",
        "--disable-dev-shm-usage",
        "--lang=en-US;q=0.9,en;q=0.8",
        "--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/68.0.3440.106 Safari/537.36",
      ],
    };

    const browser = await puppeteer.launch(launchOptions);

    // const browser = await puppeteer.launch({
    //   args: ["--no-sandbox"],
    //   headless: true,
    // });

    const page = await browser.newPage();
    await page.goto(`data:text/html,${finalHtml}`, {
      waitUntil: "networkidle0",
    });

    const pdfFile = await page.pdf(options);

    await uploadToS3(filePath, pdfFile, "BUFFER")
    await browser.close();

    return {
      data: filePath,
      success: true,
      message: "Success: Loan contract regenerated.",
      status: 200,
    };
  } catch (ex) {
    saveErrorLog("generateContract", apiPath, {
      dataObj,
      templateName,
      userId,
      loanId,
      footer,
      displayHeaderFooter,
      header
    }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);

    return {
      data: "",
      success: false,
      message: ex.message,
      status: 200,
    };
  }
};

async function generateKfsDocument(
  dataObj,
  userId,
  loanId,
  footer,
  displayHeaderFooter,
  header
) {
  try {
    let proofImagePath = "ProofImages/" + userId + "/KFS";

    let filePath = proofImagePath + "/kfs_" + loanId + ".pdf";

    let dataBinding = dataObj;

    let templatePath = "/templates/kfs.html";

    const templateHtml = fs.readFileSync(
      path.join(process.cwd(), templatePath),
      "utf8"
    );

    const template = handlebars.compile(templateHtml);

    const finalHtml = template(dataBinding);

    const options = {
      format: "A4",
      headerTemplate: "<p></p>",
      footerTemplate: footer,
      displayHeaderFooter: displayHeaderFooter,
      margin: {
        top: "10px",
        right: "20px",
        bottom: "80px",
        left: "20px",
      },
      printBackground: true,
      // path: filePath,
    };

    const launchOptions = {
      ignoreHTTPSErrors: true,
      args: [
        "--unlimited-storage",
        "--full-memory-crash-report",
        "--disable-gpu",
        "--ignore-certificate-errors",
        "--no-sandbox",
        "--disable-setuid-sandbox",
        "--disable-dev-shm-usage",
        "--lang=en-US;q=0.9,en;q=0.8",
        "--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/68.0.3440.106 Safari/537.36",
      ],
    };

    const browser = await puppeteer.launch(launchOptions);

    const page = await browser.newPage();
    await page.goto(`data:text/html,${finalHtml}`, {
      waitUntil: "networkidle0",
    });

    const pdfFile = await page.pdf(options);

    await uploadToS3(filePath, pdfFile, "BUFFER")
    await browser.close();

    return {
      data: filePath,
      success: true,
      message: "Success: KFS document regenerated.",
      status: 200,
    };
  } catch (err) {
    saveErrorLog("generateKfsDocument", apiPath, {
      dataObj,
      userId,
      loanId,
      footer,
      displayHeaderFooter,
      header
    }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);

    return {
      data: "",
      success: false,
      message: ex.message,
      status: 200,
    };
  }
};

function getGstCharges(processingFee) {
  if (processingFee == 0) {
    return 0;
  } else {
    const gstRate = 18;

    //return 0; //changed from 3-Nov-2020 9:00 p.m. as suggested by Rahul

    return +parseFloat((processingFee * gstRate) / 100).toFixed(2); //applied from 2-Apr-2021
  }
};
module.exports.getGstCharges = getGstCharges;

function getProcessingFee(amount, charge) {
  let processingFee = 0;
  const temp = amount * charge;
  processingFee = (temp > 0) ? (processingFee + (temp / 100)) : processingFee;
  return processingFee;
};
module.exports.getProcessingFee = getProcessingFee;

function getStampingCharges() {
  return 10;
};
module.exports.getStampingCharges = getStampingCharges;

async function getDisbursalCharges(amount, charge) {
  const processingFee = getProcessingFee(amount, charge) || 0;
  //console.log("processingFee", processingFee);
  const stampingCharges = getStampingCharges() || 0;
  //console.log("stampingCharges", stampingCharges);
  const gstCharges = getGstCharges(processingFee) || 0;
  //console.log("gstCharges", gstCharges);
  return processingFee + stampingCharges + gstCharges;
};
module.exports.getDisbursalCharges = getDisbursalCharges;

function inWords(num) {
  let a = [
    "",
    "one ",
    "two ",
    "three ",
    "four ",
    "five ",
    "six ",
    "seven ",
    "eight ",
    "nine ",
    "ten ",
    "eleven ",
    "twelve ",
    "thirteen ",
    "fourteen ",
    "fifteen ",
    "sixteen ",
    "seventeen ",
    "eighteen ",
    "nineteen ",
  ];
  let b = [
    "",
    "",
    "twenty",
    "thirty",
    "forty",
    "fifty",
    "sixty",
    "seventy",
    "eighty",
    "ninety",
  ];

  if ((num = num.toString()).length > 9) return "overflow";
  n = ("000000000" + num)
    .substr(-9)
    .match(/^(\d{2})(\d{2})(\d{2})(\d{1})(\d{2})$/);
  if (!n) return;
  let str = "";
  str +=
    n[1] != 0
      ? (a[Number(n[1])] || b[n[1][0]] + " " + a[n[1][1]]) + "crore "
      : "";
  str +=
    n[2] != 0
      ? (a[Number(n[2])] || b[n[2][0]] + " " + a[n[2][1]]) + "lakh "
      : "";
  str +=
    n[3] != 0
      ? (a[Number(n[3])] || b[n[3][0]] + " " + a[n[3][1]]) + "thousand "
      : "";
  str +=
    n[4] != 0
      ? (a[Number(n[4])] || b[n[4][0]] + " " + a[n[4][1]]) + "hundred "
      : "";
  str +=
    n[5] != 0
      ? (str != "" ? "and " : "") +
      (a[Number(n[5])] || b[n[5][0]] + " " + a[n[5][1]]) +
      "only "
      : "";
  return str;
};

module.exports.generateOtpManually = async function (loanId, purpose, channel = "SMS", req) {
  try {
    const loan = await LoanInfo.findById(loanId);

    if (!loanId) {
      return {
        data: "",
        success: false,
        message: "Invalid loan Id",
        status: 501,
      };
    } else {

      if (req && req.user.role == ROLES.USER && (loan.userId.toString() != req.user.id)) {
        return {
          data: "",
          success: false,
          message: "Invalid loan Id",
          status: 501,
        };
      }

      const user = await UserInfo.findById(loan.userId);

      if (!user) {
        return {
          data: "",
          success: false,
          message: "Invalid user Id",
          status: 501,
        };
      } else {
        if (!user.userDetails.mobileNo[0] || !user.userDetails.mobileNo[0].mobileNumber) { //mobile number change
          return {
            data: "",
            success: false,
            message: "Invalid Mobile No",
            status: 501,
          };
        }

        /** CHANGES START */
        let payload = {
          notifyTo: user.userDetails.mobileNo[0] ? user.userDetails.mobileNo[0].mobileNumber : "", //mobile number change
          channel: OTP_CHANNELS.SMS,
          notificationType: NOTIFICATION_TYPES.LOAN_CONTRACT,
          otpLength: 6
        }
        let sendOtpRes = await sendOtp(payload);

        if (sendOtpRes && sendOtpRes.error) {
          console.log('aryanotp', sendOtpRes)
          return {
            data: "",
            success: false,
            message: sendOtpRes.error && sendOtpRes.error.message || "Too Many Attempts Please Try After Sometime",
            status: 200
          };
        }
        /** CHANGES END */

        // let otp = Math.floor(100000 + Math.random() * 900000);

        // await OtpInfo.updateMany(
        //   { loanId: loanId, userId: user._id, channel: channel },
        //   { $set: { isActive: false } }
        // );

        // const item = new OtpInfo({
        //   userId: user._id,
        //   loanId: loanId,
        //   partnerId: loan.partnerId,
        //   merchantCode: loan.merchantCode,
        //   productId: loan.productId,
        //   trxnId: loan.trxnId,
        //   otp,
        //   mobileNo: user.userDetails.mobileNo[0] ? [user.userDetails.mobileNo[0].mobileNumber] : [],
        //   isActive: true,
        //   purpose: purpose,
        //   insertedOn: Date.now(),
        //   channel: channel
        // });

        // const response = item.save();

        return {
          data: sendOtpRes && sendOtpRes.response,
          otp: sendOtpRes && sendOtpRes.response.otp,
          mobileNo: payload.notifyTo, //mobile number change
          amount: loan.amount ? loan.amount.toString() : "",
          success: true,
          message: "OTP generated",
          status: 200,
          userId: user._id,
          loanId: loanId
        };
      }
    }
  } catch (ex) {
    saveErrorLog("generateOtpManually", apiPath, { loanId, purpose }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    return {
      data: null,
      success: false,
      message: ex.message,
      status: 200,
    };
  }

};

module.exports.verifyCustomOtp = async function (loanId, otp, purpose = "", otpAutoFill = false, channel = "SMS", req) {
  try {

    // const record = await OtpInfo.findOne({ loanId: loanId, isActive: true, channel: channel }).sort(
    //   {
    //     insertedOn: -1,
    //   }
    // );

    let user = null;

    const loan = await LoanInfo.findById(loanId);
    if (loan)
      user = await UserInfo.findById(loan.userId);

    if (!loan) {
      return {
        data: "",
        success: false,
        message: "Invalid Loan Id",
        status: 200,
      };
    }
    /** ROLES CHECK */
    if (req && req.user.role != ROLES.ADMIN) {
      if (req.user.role == ROLES.USER && (loan.userId.toString() != req.user.id)) {
        return { data: null, success: false, message: "Access Denied" };
      } else if (req.user.role == ROLES.BORROWING_PARTNER && loan.partnerId.toString() != req.user.partnerId) {
        return { data: null, success: false, message: "Access Denied" };
      } else if (req.user.role == ROLES.LENDING_PARTNER && (!loan.lenderDetails || !loan.lenderDetails.length || loan.lenderDetails[0].lenderPartnerId.toString() != req.user.partnerId)) {
        return { data: null, success: false, message: "Access Denied" };
      }
    }

    /** CHANGES START */

    let payload = {
      notifyTo: user.userDetails.mobileNo[0] ? user.userDetails.mobileNo[0].mobileNumber : "",
      notificationType: NOTIFICATION_TYPES.LOAN_CONTRACT,
      channel: OTP_CHANNELS.SMS,
      otp
    }

    const { error: verifyOtpError, response: record } = await verifyOtp(payload);

    if (verifyOtpError)
      return {
        data: "",
        success: false,
        message: verifyOtpError && verifyOtpError.message || "No record found for loan Id",
        status: 200,
      };

    record.mobileNo = record.notifyTo

    /** CHANGES END */

    if (record.otp != otp) {
      await logUserOtp(user && user._id ? user._id : null, loanId ? loanId : null, loan && loan.partnerId ? loan.partnerId : null, loan && loan.merchantCode ? loan.merchantCode : null,
        otp, record && record.mobileNo ? record.mobileNo : null, false, purpose, otpAutoFill, channel);

      return {
        data: "",
        success: false,
        message: "OTP does not match",
        status: 200,
      };
    }

    // record.isActive = false;
    // const response = await record.save();

    await logUserOtp(user && user._id ? user._id : null, loanId ? loanId : null, loan && loan.partnerId ? loan.partnerId : null, loan && loan.merchantCode ? loan.merchantCode : null,
      otp, record && record.mobileNo ? record.mobileNo : null, false, purpose, otpAutoFill, channel);

    return {
      data: record,
      success: true,
      message: "OTP matched!",
      status: 200,
    };
  }
  catch (ex) {
    saveErrorLog("verifyCustomOtp", apiPath, { loanId, otp, purpose, otpAutoFill }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    console.log(ex);
    return {
      data: "",
      success: false,
      message: ex.message,
      status: 200,
    };
  }
};

async function getLoanId() {
  let output = {
    data: null,
    success: false,
    message: "Some Error Occurred",
    status: 501,
  };
  try {
    let loanId;
    let loanIdGenerated = false;

    do {
      let repository = await LoanIdRepository.find()
        .sort({ loanId: -1 })
        .limit(1);

      loanId = repository[0].loanId + 1;

      const loanIdRepository = new LoanIdRepository({
        loanId: loanId,
      });

      await loanIdRepository
        .save()
        .then(() => {
          loanIdGenerated = true;

          output.success = true;
          output.message = "Loan Id generated successfully";
          output.status = 200;
          output.data = loanId;
        })
        .catch((err) => {
          const duplicate = err.toString().includes("duplicate key", 0);
          if (!duplicate) {
            loanIdGenerated = true;

            output.data = null;
            output.success = false;
            output.message = err.toString();
            output.status = 501;
          }
        });
    } while (!loanIdGenerated);

  } catch (ex) {
    saveErrorLog("getLoanId", apiPath, {}, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    output.message = ex.message;
  }
  return output;
};
module.exports.getLoanId = getLoanId;

module.exports.getLoanInformation = async function (loanId, req) {
  try {
    let query = {};

    query["_id"] = loanId;

    let loan = await LoanInfo.findById(loanId);
    console.log('aryan3', loan)

    if (!loan) {
      return {
        data: null,
        success: false,
        message: "Invalid LoanId",
        status: 400,
      };
    }

    if (req && req.user.role != ROLES.ADMIN) {

      if ((req.user.role == ROLES.BORROWING_PARTNER || req.user.role == ROLES.USER) && loan.partnerId.toString() != req.user.partnerId) {
        return {
          data: null,
          success: false,
          message: "Access Denied",
          status: 400,
        };
      }

      if ((req.user.role == ROLES.LENDING_PARTNER) && loan.lenderDetails[0].lenderPartnerId.toString() != req.user.partnerId) {
        return {
          data: null,
          success: false,
          message: "Access Denied",
          status: 400,
        };
      }
    }

    let user = await UserInfo.findById(loan.userId);

    return {
      data: { loan, user },
      success: true,
      message: "",
      status: 200,
    };
  } catch (ex) {
    saveErrorLog("getLoanInformation", apiPath, loanId, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    return {
      data: null,
      success: false,
      message: ex.message,
      status: 200,
    };
  }
};

module.exports.getContractInformation = async function (loanId, req) {
  try {
    let loan = await LoanInfo.findById(loanId);

    if (!loan) {
      return {
        data: null,
        success: false,
        message: "Invalid Loan",
        status: 400,
      };
    }

    /** ROLES CHECK */
    if (req && req.user.role != ROLES.ADMIN) {
      if (req.user.role == ROLES.USER && (loan.userId.toString() != req.user.id)) {
        return { data: null, success: false, message: "Access Denied", status: 400 };
      } else if (req.user.role == ROLES.BORROWING_PARTNER && loan.partnerId.toString() != req.user.partnerId) {
        return { data: null, success: false, message: "Access Denied", status: 400 };
      } else if (req.user.role == ROLES.LENDING_PARTNER && (!lenderDetails || !lenderDetails.length || lenderDetails[0].lenderPartnerId.toString() != req.user.partnerId)) {
        return { data: null, success: false, message: "Access Denied", status: 400 };
      }
    }


    let user = await UserInfo.findById(loan.userId);
    let emiAmount = "";
    let emiDueDate = "";
    let tenure = "";

    if (loan.isMaturityMonth) frequency = "Monthly";
    else if (loan.repaymentFrequency && loan.repaymentFrequency.toLowerCase() == "daily")
      tenure = loan.maturity.toString() + " Days";
    else if (loan.repaymentFrequency && loan.repaymentFrequency.toLowerCase() == "weekly")
      tenure = loan.maturity.toString() + " Weeks";
    else if (loan.repaymentFrequency && loan.repaymentFrequency.toLowerCase() == "monthly")
      tenure = loan.maturity.toString() + " Months";
    else if (loan.repaymentFrequency && loan.repaymentFrequency.toLowerCase() == "fortnightly")
      tenure = loan.maturity.toString() + " Fortnights";
    else if (loan.repaymentFrequency && loan.repaymentFrequency.toLowerCase() == "adhoc")
      tenure = loan.tenure.toString() + " Days";

    let borrowerPartner = await PartnerMaster.findById(loan.partnerId);
    console.log('aryan5', borrowerPartner)

    console.log('sda', borrowerPartner)

    const borrowingPartnerConfig = await PartnerConfig.findOne({ partnerId: loan.partnerId });

    let eNachRequired = (loan.primaryCollectionType === "eNach" || loan.secondaryCollectionType === "eNach") ? true : false;
    let eNachSigned = false;
    let mandateBankDetailsMatch = true;

    if (eNachRequired) {
      const authMode = loan.loanContractDetails && loan.loanContractDetails.authMode ? loan.loanContractDetails.authMode : "";

      let mandateGeneration = await DigioMandateGeneration.findOne({ loanIds: loan._id, mode: authMode }).sort({ createdOn: -1 });

      if (mandateGeneration) {
        const checkMandate = await checkMandateCurrentBankDetails(mandateGeneration, loan._id);

        mandateBankDetailsMatch = checkMandate.success;

        eNachSigned = mandateGeneration.status && (mandateGeneration.status == "success" || mandateGeneration.status == "register_success") ? true : false;
      }
    }

    //Update eNach Step
    if (!loan.loanContractDetails.isAccepted) {
      if (!eNachRequired || (eNachRequired && eNachSigned) || !borrowingPartnerConfig.isMandateRequired) {
        const updateStep = await updateUserAndLoanSteps(loan, user, "eNach", null, null);

        loan = updateStep.data.loan;
        user = updateStep.data.user;

        await loan.save();
        await user.save();
      }
    }

    if (loan.loanContractDetails.isAccepted) {
      if (loan.status === "Draft" || loan.status === "Loan Is Funding") {
        if (eNachRequired) {
          const authMode = loan.loanContractDetails && loan.loanContractDetails.authMode ? loan.loanContractDetails.authMode : "";
          const mandateGeneration = await DigioMandateGeneration.findOne({ loanIds: loan._id, mode: authMode }).sort({ createdOn: -1 });
          let isSigned = false;
          if (mandateGeneration && mandateGeneration.mode === "api")
            isSigned = mandateGeneration.status && (mandateGeneration.status == "success" || mandateGeneration.status == "register_success") ? true : false;
          else if (mandateGeneration && (mandateGeneration.mode === "esign" || mandateGeneration.mode === "physical"))
            isSigned = mandateGeneration.status && (mandateGeneration.status == "register_success") ? true : false;

          if (!isSigned)
            loan.status = "Recourse Pending";
          else
            loan.status = "Loan Contract Accepted";
        }
        else
          loan.status = "Loan Contract Accepted";

        await loan.save();
      }

      if (user.userDetails.kycStatus !== "Approved") {
        user.userDetails.kycStatus = "Under Review";
        await user.save();
      }
    }
    // TODO: check conditions satisfy, why aryan 70 is not coming.
    console.log('aryan71', { partnerId: mongoose.Types.ObjectId(loan.partnerId), merchantCode: loan.merchantCode, productType: loan.productType.split("_")[0], $or: [{ trxnId: loan.trxnId }, { productId: loan.trxnId }, { productId: loan.productId }] });

    const preApproval = await PreApprovals.findOne({ partnerId: mongoose.Types.ObjectId(loan.partnerId), merchantCode: loan.merchantCode, productType: 'TL-M', $or: [{ trxnId: loan.trxnId }, { productId: loan.trxnId }, { productId: loan.productId }] }).sort({ insertedOn: -1 });

    let p_type = loan.productType ? loan.productType.split("_") : "";
    p_type = p_type && p_type[0] ? p_type[0] : "";

    const charges = await getLoanCharges(loan.amount, loan._id, preApproval?._id, true);

    if (!charges.success) {
      return {
        data: null,
        success: false,
        message: charges.message,
        status: 200,
      };
    }

    let processingCharges = 0;
    let discountAmount = 0;

    if (p_type !== "LC" || loan.isParentLoan) {
      discountAmount = charges.discountAmount;
      processingCharges = (charges.discountType == "pf" && charges.computeType == "pre") ? (charges.processingChargeAmt + charges.discountAmount) : charges.processingChargeAmt;
    }
    const today = new Date();
    const emiDate = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 5, 30, 1, 0);
    const disbursalDate = loan.productType == "DL_30" ? dateAdd("day", -1, emiDate) : emiDate;

    let installmentDetails = await emiSchedule(loan._id, disbursalDate, false);
    let disbursalAmount = 0;

    if (p_type === "LC")
      disbursalAmount = loan.amount - processingCharges;
    else
      disbursalAmount = charges.disbursalAmount;

    let totalInterestPayable = 0;

    if (!IsNullEmpty(installmentDetails) && !IsNullEmpty(installmentDetails.data)) {
      if (installmentDetails.data.length > 0) {
        emiAmount = installmentDetails.data[0].rePaymentAmount;
        emiDueDate = dateFormat(installmentDetails.data[0].rePaymentDate, "dd/mm/yyyy");
        disbursalAmount = loan.productType == "DL_30" ? (disbursalAmount - installmentDetails.data[0].rePaymentAmount) : disbursalAmount;

        totalInterestPayable = installmentDetails.data.filter((x) => x.interestAmount != 0).reduce(function (prev, cur) {
          return prev + cur.interestAmount;
        }, 0);
      }
    }

    let isSigned = (loan.loanContractDetails.isAccepted && user.userDetails.kycStatus !== "Review") ? true : false;

    let partnerCode = config.get("keys.hideContractForPartner");

    //const estimate = await getLoanEstimation(loanId, loan.amount);

    let contractSuccessCallBackUrl = loan.loanContractDetails && loan.loanContractDetails.contractSuccessCallBackUrl ? loan.loanContractDetails.contractSuccessCallBackUrl : null;
    let eNachErrorCallBackUrl = loan.loanContractDetails && loan.loanContractDetails.eNachErrorCallBackUrl ? loan.loanContractDetails.eNachErrorCallBackUrl : null;

    if (!contractSuccessCallBackUrl)
      contractSuccessCallBackUrl = borrowerPartner.contractSuccessCallBackUrl ? borrowerPartner.contractSuccessCallBackUrl : null;

    if (!eNachErrorCallBackUrl)
      eNachErrorCallBackUrl = borrowerPartner.eNachErrorCallBackUrl ? borrowerPartner.eNachErrorCallBackUrl : null;

    const loanInstallments = await installmentService.convertEmiDetailsFormat(loanId);

    return {
      data: {
        id: loan._id,
        loanNo: loan.loanNo,
        amount: loan.amount,
        roi: loan.interestRate,
        tenure,
        gstCharges: charges.gstCharge,
        stampingCharges: charges.stampingCharge,
        disbursalAmount,
        discountAmount,
        emiAmount,
        processingCharges,
        contractDetails: loan.loanContractDetails,
        displayContract: borrowerPartner.partnerCode == partnerCode ? false : true,
        isSigned: isSigned,
        emiDetails: loanInstallments.success ? loanInstallments.data : [],
        eNachRequired: false, //eNachRequired
        eNachSigned: eNachSigned,
        status: loan.status,
        isStp: loan.isStp ? true : false,
        p_type: p_type,
        emiCount: loanInstallments.success ? loanInstallments.data.length : null,
        totalInterestPayable: round10(totalInterestPayable, -2),
        collectionType: loan.primaryCollectionType === "eNach" ? "Bank Account" : "Wallet",
        contractSuccessCallBackUrl,
        eNachErrorCallBackUrl,
        mandateBankDetailsMatch,
        kycStatus: user && user.userDetails && user.userDetails.kycStatus ? user.userDetails.kycStatus : "",
        disbursalType: borrowingPartnerConfig.disbursalType ? borrowingPartnerConfig.disbursalType : "",
        borrowingPartner: borrowerPartner.brandName,
        photoMatch: loan.photoMatch,
        isMandateRequired: borrowingPartnerConfig.isMandateRequired
      },
      success: true,
      message: "",
      status: 200,
    };

  }
  catch (ex) {
    saveErrorLog("getContractInformation", apiPath, loanId, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    logger.logs(ex);
    throw (ex);
  }
};

module.exports.getEmi = async function (model) {
  try {
    let emis = [];
    emis = await calculateEmi(
      model.amount,
      model.maturity,
      model.interestRate,
      false,
      model.repaymentFrequency,
      new Date(),
      "compound",
      false,
      "upper",
      "TL_90",
      false,
      1,
      "Post"
    );

    return {
      data: emis,
      success: true,
      message: "",
      status: 200,
    };
  } catch (ex) {
    saveErrorLog("getEmi", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    return {
      data: null,
      success: false,
      message: ex.message,
      status: 200,
    };
  }
};
module.exports.createPortfolioReport = createPortfolioReport;

async function createPortfolioReport(templateName, data) {
  try {
    let dataBinding = data;

    let templatePath = "/templates/" + templateName;

    let templateHtml = fs.readFileSync(
      path.join(process.cwd(), templatePath),
      "utf8"
    );

    let template = handlebars.compile(templateHtml);

    let finalHtml = template(dataBinding);
    let options = {
      format: "A4",
      headerTemplate: `<div id="footer-template" style="font-size:10px !important; color:#808080; padding-left:10px; padding-right:10px; width: 100%"><table style="width: 100%; cellpadding="3" cellspacing="3"><tr><td style="width: 85%;"> <img src="data:image/png;base64,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" style="width:200px;" /></td><td style="width: 15%; border: 2px solid; border-color:black; text-align:center;font-weight:bold;">${data.aprRealisedReturn}%</td></tr></table></div>`,
      landscape: true,
      margin: {
        top: "120px",
        right: "20px",
        bottom: "100px",
        left: "20px",
      },
      printBackground: true,
      footerTemplate: `<div id="footer-template" style="font-size:10px !important; color:#808080; padding-left:10px; padding-right:10px; width: 100%"><table style="width: 100%; cellpadding="3" cellspacing="3"><tr><td style="width: 40%;"> <img src="data:image/png;base64,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" style="width:100px;" /></td><td style="width: 60%;"><span style="width:100%">GST: 20AACCF8339A1Z7 <br/>Contact: <EMAIL> <EMAIL></span></td></tr></table></div>`,
      displayHeaderFooter: true,
    };

    const browser = await puppeteer.launch({
      args: ["--no-sandbox"],
      headless: true,
    });
    const page = await browser.newPage();
    await page.goto(`data:text/html,${finalHtml}`, {
      waitUntil: "networkidle0",
    });
    const pdf = await page.pdf(options);
    await browser.close();

    return {
      data: pdf,
      success: true,
      message: "Success: Loan contract regenerated.",
      status: 200,
    };
  } catch (ex) {
    saveErrorLog("createPortfolioReport", apiPath, { templateName, data }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    console.log("err", ex);
    return {
      data: "",
      success: false,
      message: ex.message,
      status: 200,
    };
  }
};

async function _arrayBufferToBase64(buffer) {
  var binary = "";
  var bytes = new Uint8Array(buffer);
  var len = bytes.byteLength;
  for (var i = 0; i < len; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  return window.btoa(binary);
};

module.exports.updateCollectionType = async function () {
  try {
    const loans = await LoanInfo.find({ primaryCollectionType: null, isDraft: false });

    let i = 0;

    for (const item of loans) {

      const partnerMaster = await PartnerMaster.findOne({ _id: item.partnerId });

      console.log(item.loanNo);

      if (partnerMaster) {
        if (partnerMaster.eNachRequired) {
          item.primaryCollectionType = "eNach";
          item.secondaryCollectionType = "";
          await item.save();
          i += 1;
        }
        else {
          item.primaryCollectionType = "Wallet";
          item.secondaryCollectionType = "";
          await item.save();
          i += 1;
        }
      }
    }

    console.log(`${i} loans updated`);

    return {
      data: "",
      success: true,
      message: `${i} loans updated`
    }
  } catch (ex) {
    saveErrorLog("updateCollectionType", apiPath, {}, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    return {
      data: "",
      success: false,
      message: ex.message
    }
  }

};

module.exports.reSignContract = async function (loanNos) {
  let successList = [];
  let failedList = [];
  let output = {
    data: {},
    message: "",
    success: false,
    status: 200
  }
  try {

    for (const loanNo of loanNos) {
      const loan = await LoanInfo.findOne({ loanNo: loanNo });

      if (loan) {
        console.log(`Regenerating contract: ${loan.loanNo}`);

        if (!loan.loanContractDetails.acceptedOn) {
          loan.loanContractDetails.acceptedOn = Date.now();
          await loan.save();
        }

        await createContract(loan);
        successList.push(loanNo);
      }
      else
        failedList.push(loanNo);
    }


    output.data = { successList, failedList };
    output.success = true;
    output.message = "Regenerated successfully";

  } catch (ex) {
    saveErrorLog("reSignContract", apiPath, loanNo, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    output.message = ex.message;
  }

  return output;
};

module.exports.bulkUpdateMandateIds = async function () {
  try {

    const list = await DigioMandateGeneration.find();

    let i = 0;

    for (const item of list) {

      if (item.loanId && (!item.loanIds || item.loanIds.length === 0)) {
        item.loanIds = [item.loanId];
        await item.save();
        i += 1;
      }

      console.log("item.loanId", item.loanId);

    }

    const list2 = await DigioMandateLog.find();

    let j = 0;

    for (const item of list2) {

      if (item.loanId && (!item.loanIds || item.loanIds.length === 0)) {
        item.loanIds = [item.loanId];
        await item.save();
        j += 1;
      }

      console.log("item.loanId", item.loanId);

    }

    const list3 = await DigioNachResponseLog.find();

    let k = 0;

    for (const item of list3) {

      if (item.loanId && (!item.loanIds || item.loanIds.length === 0)) {

        item.loanIds = [item.loanId];
        await item.save();
        k += 1;

      }
    }

    const list4 = await DigioNachWebHookResponse.find();

    let m = 0;

    for (const item of list4) {

      if (item.loanId && (!item.loanIds || item.loanIds.length === 0)) {

        item.loanIds = [item.loanId];
        await item.save();
        m += 1;

      }

      console.log("item.loanId", item.loanId);

    }

    console.log(`${i} records updated`);
    console.log(`${j} records updated`);
    console.log(`${k} records updated`);
    console.log(`${m} records updated`);

    return {
      data: "",
      success: true,
      message: `${i} DigioMandateGeneration records updated, ${j} DigioMandateLog records updated, ${k} DigioNachResponseLog records updated, ${m} DigioNachWebHookResponse records updated`
    }

  }
  catch (ex) {
    saveErrorLog("bulkUpdateMandateIds", apiPath, {}, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    console.log(ex);
    return {
      data: "",
      success: false,
      message: ex.message
    }
  }
};

module.exports.bulkInsertProductPartnerConfig = async function () {

  try {

    const partnerMaster = await PartnerMaster.find({ partnerType: "Borrowing Partner", isActive: true });

    let i = 0;

    for (const item of partnerMaster) {

      const termLoan = await ProductMaster.findOne({ product: "TL" });

      const termLoanConfig = await ProductPartnerConfig.findOne({ partnerId: item._id, productId: termLoan._id });

      if (!termLoanConfig) {

        const termLoanModel = new ProductPartnerConfig({
          partnerId: item._id,
          productId: termLoan._id,
          childLoanApprovedLimitInPer: config.get("productPartnerConfig.TL.childLoanApprovedLimitInPer"),
          interestComputation: config.get("productPartnerConfig.TL.interestComputation"),
          penaltyGraceDays: config.get("productPartnerConfig.TL.penaltyGraceDays")
        });

        await termLoanModel.save();

      }

      const dailyLoan = await ProductMaster.findOne({ product: "DL" });

      const dailyLoanConfig = await ProductPartnerConfig.findOne({ partnerId: item._id, productId: dailyLoan._id });

      if (!dailyLoanConfig) {

        const dailyLoanModel = new ProductPartnerConfig({
          partnerId: item._id,
          productId: dailyLoan._id,
          childLoanApprovedLimitInPer: config.get("productPartnerConfig.DL.childLoanApprovedLimitInPer"),
          interestComputation: config.get("productPartnerConfig.DL.interestComputation"),
          penaltyGraceDays: config.get("productPartnerConfig.DL.penaltyGraceDays")
        });

        await dailyLoanModel.save();

      }

      const creditLineLoan = await ProductMaster.findOne({ product: "LC" });

      const creditLineLoanConfig = await ProductPartnerConfig.findOne({ partnerId: item._id, productId: creditLineLoan._id });

      if (!creditLineLoanConfig) {

        const creditLineLoanModel = new ProductPartnerConfig({
          partnerId: item._id,
          productId: creditLineLoan._id,
          childLoanApprovedLimitInPer: config.get("productPartnerConfig.LC.childLoanApprovedLimitInPer"),
          interestComputation: config.get("productPartnerConfig.LC.interestComputation"),
          penaltyGraceDays: config.get("productPartnerConfig.LC.penaltyGraceDays")
        });

        await creditLineLoanModel.save();

        i += 1;

      }

    }

    return {
      data: "",
      success: true,
      message: `${i} Records updated`,
      status: 200
    }

  }
  catch (ex) {
    saveErrorLog("bulkInsertProductPartnerConfig", apiPath, {}, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    console.log(ex);

    return {
      data: "",
      success: false,
      message: ex.message,
      status: 200
    }
  }

};

module.exports.updatePartnerIdInMandateCollection = async function () {
  try {
    const list = await DigioMandateGeneration.find({ partnerId: null });

    let i = 0;

    for (const item of list) {

      const loanId = item.loanIds && item.loanIds[0] ? item.loanIds[0] : null;

      if (loanId) {
        const loan = await LoanInfo.findById(loanId);

        if (loan) {
          item.partnerId = loan.partnerId;
          await item.save();
          i += 1;
          console.log("loanNo", loan.loanNo);
        }
      }
    }

    return {
      data: "",
      success: true,
      message: `${i} records updated`
    }

  }
  catch (ex) {
    saveErrorLog("updatePartnerIdInMandateCollection", apiPath, {}, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    console.log(ex);

    return {
      data: "",
      success: false,
      message: ex.message,

    }

  }
};

async function backupLoanInfo(loanId) {
  let output = {
    success: false,
    message: "Some Error Occurred",
    status: 501,
  };
  try {
    const loanInfo = await LoanInfo.findById(loanId);
    if (loanInfo) {
      const tempLoanInfo = loanInfo.toJSON();
      tempLoanInfo.loanId = mongoose.Types.ObjectId(tempLoanInfo["_id"]);

      delete tempLoanInfo["_id"];
      const loanInfoHistory = new LoanInfoHistory(tempLoanInfo);
      await loanInfoHistory.save();

      output = {
        success: true,
        message: "Saved Successfully",
        status: 200,
      };
    } else {
      output.message = "No Loan found";
    }
  } catch (ex) {
    saveErrorLog("backupLoanInfo", apiPath, loanId, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    output.message = ex.message;
  }
  return output;
};
module.exports.backupLoanInfo = backupLoanInfo;

async function createMandate(loanId, callingMethod = null, sendNotification = false, isInternalLink = false, authMode = "api") {
  try {

    if (!loanId)
      return {
        data: "",
        success: false,
        message: "Loan Id not provided",
        status: 200
      }

    let loan = await LoanInfo.findById(loanId);

    if (!loan)
      return {
        data: "",
        success: false,
        message: "Loan not found",
        status: 200
      }

    const user = await UserInfo.findById(loan.userId);

    if (!user)
      return {
        data: "",
        success: false,
        message: "User not found",
        status: 200
      }

    if (callingMethod === "resendLink" && user.userDetails.kycStatus && user.userDetails.kycStatus !== "Approved")
      return {
        data: "",
        success: false,
        message: "KYC Status is not approved",
        status: 200
      }

    const partnerMaster = await PartnerMaster.findById(loan.partnerId);

    if (!partnerMaster)
      return {
        data: "",
        success: false,
        message: "Partner not found",
        status: 200
      }

    const partnerConfig = await PartnerConfig.findOne({ partnerId: loan.partnerId });

    if (!partnerConfig)
      return {
        data: "",
        success: false,
        message: "PartnerConfig not defined",
        status: 200
      }

    // let proofImagePath = config.get("keys.proofImages") + "/" + loan.userId;

    // if (!fs.existsSync(proofImagePath))
    //   fs.mkdirSync(proofImagePath);

    let longUrl = "";
    let shortUrl = "";
    let isMandateSigned = false;
    let mandateAuthMode = "";

    if (callingMethod === "resendLink" || callingMethod === "insertUpdateBankDetails") {
      if (partnerConfig.disbursalType.toLowerCase() == "merchant") {
        const userPartnerInfo = await UserPartnerInfo.findOne({ userId: mongoose.Types.ObjectId(loan.userId), partnerId: mongoose.Types.ObjectId(loan.partnerId), merchantCode: loan.merchantCode });

        if (userPartnerInfo)
          loan.disbursalBankDetails = {
            bankName: userPartnerInfo.bankDetails[0].bankName,
            branch: userPartnerInfo.bankDetails[0].branch,
            accountNo: userPartnerInfo.bankDetails[0].accountNo,
            accountName: userPartnerInfo.bankDetails[0].accountName,
            ifsc: userPartnerInfo.bankDetails[0].ifsc,
            accountType: userPartnerInfo.bankDetails[0].accountType,
            address: userPartnerInfo.bankDetails[0].address,
            city: userPartnerInfo.bankDetails[0].city,
            state: userPartnerInfo.bankDetails[0].state,
            branch: userPartnerInfo.bankDetails[0].branch,
            insertedOn: Date.now(),
            updatedOn: Date.now()
          };
      } else {
        const bankAccount = partnerMaster.bankAccounts.filter((x) => x.isActive == true);

        if (bankAccount) {
          loan.disbursalBankDetails = {
            bankName: bankAccount[0].bankName,
            branch: bankAccount[0].branch,
            accountNo: bankAccount[0].no,
            accountName: bankAccount[0].name,
            ifsc: bankAccount[0].ifsc,
            accountType: bankAccount[0].type,
            address: bankAccount[0].address,
            city: bankAccount[0].city,
            state: bankAccount[0].state,
            branch: bankAccount[0].branch,
            insertedOn: Date.now(),
            updatedOn: Date.now()
          };
          if (partnerConfig.disbursalType.toLowerCase() == "virtual") {
            const partnerLoans = await PartnerLoans.findOne({ loanNo: loan.loanNo });
            loan.disbursalBankDetails.accountNo = partnerLoans.virtualAccountNo;
          }
        }
      }

      await loan.save();
    }

    let productType = loan.productType ? loan.productType.split("_") : null;
    productType = productType && productType[0] ? productType[0] : "";

    const assignResponse = await checkAndAssignMandate(loan, productType, true);

    if (!assignResponse.success)
      return assignResponse;

    loan = await LoanInfo.findById(loanId);

    let primaryCollectionType = "";
    let secondaryCollectionType = "";

    if (partnerConfig.collectionType === "eNach") {
      primaryCollectionType = "eNach";
      secondaryCollectionType = "";
    }
    else if (partnerConfig.collectionType === "Wallet") {
      if (assignResponse.signedMandateExists) {
        primaryCollectionType = "Wallet";
        secondaryCollectionType = "eNach";
      } else if (loan.isForceMandate || assignResponse.oldMandateExists || (loan.amount >= partnerConfig.eNachAmount && !loan.byPassEnach)) {
        primaryCollectionType = "Wallet";
        secondaryCollectionType = "eNach";
      }
      else {
        primaryCollectionType = "Wallet";
        secondaryCollectionType = "";
      }
    }

    if (primaryCollectionType === "eNach" || secondaryCollectionType === "eNach") {
      if (assignResponse.signedMandateExists) {

        longUrl = productType === "LC" && !loan.isParentLoan ? config.get("keys.creditLineConsent") + "/" + loanId : config.get("keys.loanContract") + "/" + loanId;

        const contractCode = productType === "LC" && !loan.isParentLoan ? config.get("shortUrl.consentCode") : config.get("shortUrl.contractCode");

        const shortUrlResponse = await generateCustomUrl(longUrl, contractCode, loan.userId, loan._id, loan.loanNo, loan.merchantCode, partnerMaster.displayName, null);

        let contractUrl = "";

        if (shortUrlResponse.success)
          contractUrl = shortUrlResponse.data;

        //if (partnerConfig.isIN)
        loan.loanContractDetails.eNachUrl = contractUrl;
        loan.loanContractDetails.contractUrl = contractUrl;
        loan.loanContractDetails.redirectionLongUrl = longUrl;

        loan.primaryCollectionType = primaryCollectionType;
        loan.secondaryCollectionType = secondaryCollectionType;

        shortUrl = contractUrl;

        isMandateSigned = true;
        mandateAuthMode = assignResponse.authMode;

        await loan.save();

        if (sendNotification) {
          let mailModel = getMailModel();

          if (loan.status === "Review") {
            const reason = await DefinationDetailsMaster.findOne({ definationType: "loan review reasons", defination: loan.reviewReason });

            mailModel.number = loan.loanNo;
            mailModel.rejectionreason = reason && reason.value ? reason.value : " ";
            mailModel.comments = loan.adminRemarks;
            mailModel.link = `<a href=${contractUrl}?c=e>Link</a>`;
            await sendMail(loan.userId, loan._id, "Loan Application Emails to Borrower", "Review your Loan", mailModel, "User");

            //Send WhatsApp Link
            const notificationTemplate = await NotificationTemplates.findOne({ channel: "WhatsApp", partnerId: loan.partnerId, template: "loan_review" });

            if (!notificationTemplate)
              return {
                data: "",
                success: false,
                message: "Template not defined",
                status: 200
              }

            let smsModel = getSmsModel();
            smsModel.To = user.userDetails.mobileNo[0] ? user.userDetails.mobileNo[0].mobileNumber : ""; //mobile number change
            smsModel.Type = notificationTemplate.type;
            smsModel.From = config.get("keys.from");

            smsModel.TemplateName = notificationTemplate.whatsAppTemplate;
            smsModel.VAR1 = `${user.userDetails.firstName} ${user.userDetails.lastName}`;
            smsModel.VAR2 = reason && reason.value ? reason.value : " ";
            smsModel.VAR3 = `${contractUrl}?c=wa`;
            smsModel.VAR4 = loan.adminRemarks;
            smsModel.VAR5 = config.get("karix.ffSupportLink");
            smsModel.CTA = `${contractUrl}?c=wa`;

            const whatsAppResponse = await pushToNotifications(loan.partnerId, loan.merchantCode, loan.productId, loan.trxnId,
              loan.userId, loan._id, "WhatsApp", user.userDetails.mobileNo[0].mobileNumber, "loan_review", null, null, null, smsModel, false, null, "primary");  //mobile number change

            if (whatsAppResponse.success)
              await postDisbursal.sendNotifications(whatsAppResponse.data._id);
          }
          else {
            mailModel.number = productType === "LC" && !loan.isParentLoan ? loan.amount.toString() : loan.loanNo;
            mailModel.link1 = `<a href=${contractUrl}?c=e>`;

            if (productType === "LC" && !loan.isParentLoan)
              mailModel.link1 += "Consent Link</a>";
            else
              mailModel.link1 += "Contract Link</a>";

            if (productType === "LC") {
              let preApproval = await PreApprovals.findOne({ partnerId: loan.partnerId, merchantCode: loan.merchantCode, productId: loan.productId });

              if (!preApproval)
                preApproval = await PreApprovals.findOne({ partnerId: loan.partnerId, merchantCode: loan.merchantCode, trxnId: loan.trxnId });

              let expiryDate = null;

              if (preApproval && preApproval.expiredOn)
                expiryDate = getDMYFormattedDate(preApproval.expiredOn, "/");
              else
                expiryDate = getDMYFormattedDate(new Date(loan.insertedOn.getFullYear(), loan.insertedOn.getMonth() + 1, 0), "/");

              mailModel.date = expiryDate;
            }

            let mobileNumberForMasking = user.userDetails.mobileNo[0] ? user.userDetails.mobileNo[0].mobileNumber : ""; //mobile number change

            mailModel.mobile = productType === "LC" && !loan.isParentLoan ? "" : `XXXXXX${mobileNumberForMasking.substr(mobileNumberForMasking.length - 4)}`;

            //Send email
            await sendMail(user._id, loanId, "Emails to Borrower", productType === "LC" && !loan.isParentLoan ? "CreditLine Loan Consent" : "Loan Contract", mailModel, "User");

            //sendWhatsApp Notification
            const whatsAppResponse = await pushToNotifications(loan.partnerId, loan.merchantCode, loan.productId, loan.trxnId,
              loan.userId, loan._id, "WhatsApp", user.userDetails.mobileNo[0].mobileNumber, productType === "LC" && !loan.isParentLoan ? "contract_lc_sign_eng" : "contract_sign_eng", "loan", null, null, null, false, null);

            //Call sendnotifications
            if (whatsAppResponse.success)
              await postDisbursal.sendNotifications(whatsAppResponse.data._id);
          }
        }
      }
      else {

        if (isInternalLink && partnerConfig.internalNachLink) {

          let mailModel = getMailModel();
          mailModel.number = loan.loanNo;

          let internalLink = `${config.get("keys.editBankLink")}/${loan._id.toString()}?mandate=true`

          longUrl = internalLink;

          const eNachCode = config.get("shortUrl.eNachCode");
          const shortUrlResponse = await generateCustomUrl(internalLink, eNachCode, loan.userId, loan._id, loan.loanNo, loan.merchantCode, partnerMaster.displayName, null);

          if (shortUrlResponse.success) {
            internalLink = shortUrlResponse.data
            shortUrl = shortUrlResponse.data;
          }

          loan.loanContractDetails.contractUrl = shortUrl;
          loan.loanContractDetails.eNachUrl = shortUrl;
          loan.loanContractDetails.redirectionLongUrl = longUrl;

          loan.primaryCollectionType = primaryCollectionType;
          loan.secondaryCollectionType = secondaryCollectionType;

          await loan.save();

          if (sendNotification) {
            if (loan.status === "Review") {
              const reason = await DefinationDetailsMaster.findOne({ definationType: "loan review reasons", defination: loan.reviewReason });

              mailModel.rejectionreason = reason && reason.value ? reason.value : " ";
              mailModel.comments = loan.adminRemarks;
              mailModel.link = `<a href=${internalLink}?c=e>Link</a>`;
              await sendMail(loan.userId, loan._id, "Loan Application Emails to Borrower", "Review your Loan", mailModel, "User");

              //Send WhatsApp Link
              const notificationTemplate = await NotificationTemplates.findOne({ channel: "WhatsApp", partnerId: loan.partnerId, template: "loan_review" });

              if (!notificationTemplate)
                return {
                  data: "",
                  success: false,
                  message: "Template not defined",
                  status: 200
                }

              let smsModel = getSmsModel();
              smsModel.To = user.userDetails.mobileNo[0] ? user.userDetails.mobileNo[0].mobileNumber : ""; //mobile number change
              smsModel.Type = notificationTemplate.type;
              smsModel.From = config.get("keys.from");

              smsModel.TemplateName = notificationTemplate.whatsAppTemplate;
              smsModel.VAR1 = `${user.userDetails.firstName} ${user.userDetails.lastName}`;
              smsModel.VAR2 = reason && reason.value ? reason.value : " ";
              smsModel.VAR3 = `${internalLink}?c=wa`;
              smsModel.VAR4 = loan.adminRemarks;
              smsModel.VAR5 = config.get("karix.ffSupportLink");
              smsModel.CTA = `${internalLink}?c=wa`;

              const whatsAppResponse = await pushToNotifications(loan.partnerId, loan.merchantCode, loan.productId, loan.trxnId,
                loan.userId, loan._id, "WhatsApp", user.userDetails.mobileNo[0].mobileNumber, "loan_review", null, null, null, smsModel, false, null, "primary");//mobile number change

              if (whatsAppResponse.success)
                await postDisbursal.sendNotifications(whatsAppResponse.data._id);
            }
            else {
              mailModel.link = `<a href=${internalLink}?c=e>e-Nach Link</a>`;
              //Send email
              await sendMail(user._id, loanId, "Emails to Borrower", "Mandate Link", mailModel, "User");

              const whatsAppResponse = await pushToNotifications(loan.partnerId, loan.merchantCode, loan.productId, loan.trxnId,
                loan.userId, loan._id, "WhatsApp", user.userDetails.mobileNo[0].mobileNumber, productType === "LC" && !loan.isParentLoan ? "contract_lc_sign_eng" : "contract_sign_eng", null, null, null, null, false, null, "primary", internalLink);//mobile number change

              //Call sendnotifications
              if (whatsAppResponse.success)
                await postDisbursal.sendNotifications(whatsAppResponse.data._id);
            }

          }

        } else {
          const eNachResponse = await eNachService.sendEnachRequest(loan._id, loan.userId, authMode);

          if (!eNachResponse.success) {
            if (primaryCollectionType === "eNach" || partnerConfig.isMandateRequired)
              return {
                data: "",
                success: false,
                message: eNachResponse.message,
                status: 200,
                shortUrl: null
              }
            else {
              primaryCollectionType = "Wallet";
              secondaryCollectionType = "";
            }
          }
          else {
            loan.primaryCollectionType = primaryCollectionType;
            loan.secondaryCollectionType = secondaryCollectionType;

            if (!partnerConfig.internalNachLink) {
              loan.loanContractDetails.contractUrl = eNachResponse.eNachUrl;
              loan.loanContractDetails.eNachUrl = eNachResponse.eNachUrl;
              loan.loanContractDetails.redirectionLongUrl = eNachResponse.longUrl;
            }

            loan.loanContractDetails.authMode = authMode;

            longUrl = eNachResponse.longUrl;
            shortUrl = eNachResponse.eNachUrl;

            isMandateSigned = eNachResponse.isMandateSigned;
            mandateAuthMode = eNachResponse.authMode;

            await loan.save();

            let mailModel = getMailModel();
            mailModel.number = loan.loanNo;

            if (sendNotification) {
              if (loan.status === "Review") {
                const reason = await DefinationDetailsMaster.findOne({ definationType: "loan review reasons", defination: loan.reviewReason });
                mailModel.rejectionreason = reason && reason.value ? reason.value : " ";
                mailModel.comments = loan.adminRemarks;
                mailModel.link = `<a href=${eNachResponse.eNachUrl}?c=e>Link</a>`;
                await sendMail(loan.userId, loan._id, "Loan Application Emails to Borrower", "Review your Loan", mailModel, "User");

                //Send WhatsApp Link
                const notificationTemplate = await NotificationTemplates.findOne({ channel: "WhatsApp", partnerId: loan.partnerId, template: "loan_review" });

                if (!notificationTemplate)
                  return {
                    data: "",
                    success: false,
                    message: "Template not defined",
                    status: 200
                  }

                let smsModel = getSmsModel();
                smsModel.To = user.userDetails.mobileNo[0] ? user.userDetails.mobileNo[0].mobileNumber : "" //mobile number change
                smsModel.Type = notificationTemplate.type;
                smsModel.From = config.get("keys.from");

                smsModel.TemplateName = notificationTemplate.whatsAppTemplate;
                smsModel.VAR1 = `${user.userDetails.firstName} ${user.userDetails.lastName}`;
                smsModel.VAR2 = reason && reason.value ? reason.value : " ";
                smsModel.VAR3 = `${eNachResponse.eNachUrl}?c=wa`;
                smsModel.VAR4 = loan.adminRemarks;
                smsModel.VAR5 = config.get("karix.ffSupportLink");
                smsModel.CTA = `${eNachResponse.eNachUrl}?c=wa`;

                const whatsAppResponse = await pushToNotifications(loan.partnerId, loan.merchantCode, loan.productId, loan.trxnId,
                  loan.userId, loan._id, "WhatsApp", user.userDetails.mobileNo[0].mobileNumber, "loan_review", null, null, null, smsModel, false, null, "primary");//mobile number change

                if (whatsAppResponse.success)
                  await postDisbursal.sendNotifications(whatsAppResponse.data._id);
              }
              else {
                mailModel.link = `<a href=${eNachResponse.eNachUrl}?c=e>e-Nach Link</a>`;
                //Send email
                await sendMail(user._id, loanId, "Emails to Borrower", "Mandate Link", mailModel, "User");

                const whatsAppResponse = await pushToNotifications(loan.partnerId, loan.merchantCode, loan.productId, loan.trxnId,
                  loan.userId, loan._id, "WhatsApp", user.userDetails.mobileNo[0].mobileNumber, productType === "LC" && !loan.isParentLoan ? "contract_lc_sign_eng" : "contract_sign_eng", null, null, null, null, false, null); //mobile number change

                //Call sendnotifications
                if (whatsAppResponse.success)
                  await postDisbursal.sendNotifications(whatsAppResponse.data._id);
              }
            }
          }
        }
      }
    }

    if (primaryCollectionType === "Wallet" && secondaryCollectionType === "") {

      longUrl = config.get("keys.loanContract") + "/" + loanId;

      const contractCode = config.get("shortUrl.contractCode");

      const shortUrlResponse = await generateCustomUrl(longUrl, contractCode, loan.userId, loan._id, loan.loanNo, loan.merchantCode, partnerMaster.displayName, null);

      let contractUrl = "";

      if (shortUrlResponse.success)
        contractUrl = shortUrlResponse.data;

      loan.loanContractDetails.contractUrl = contractUrl;
      loan.loanContractDetails.eNachUrl = "";

      loan.primaryCollectionType = primaryCollectionType;
      loan.secondaryCollectionType = secondaryCollectionType;
      loan.loanContractDetails.redirectionLongUrl = longUrl;

      shortUrl = contractUrl;

      await loan.save();

      if (sendNotification) {
        let mailModel = getMailModel();
        mailModel.number = loan.loanNo;
        mailModel.link1 = `<a href=${contractUrl}?c=e>Contract Link</a>`;

        mailModel.mobile = `XXXXXX${user.userDetails.mobileNo[0].mobileNumber.substr(user.userDetails.mobileNo[0].mobileNumber.length - 4)}`; //mobile number change

        //Send email
        await sendMail(user._id, loanId, "Emails to Borrower", "Loan Contract", mailModel, "User");

        //sendWhatsApp Notification
        const whatsAppResponse = await pushToNotifications(loan.partnerId, loan.merchantCode, loan.productId, loan.trxnId,
          loan.userId, loan._id, "WhatsApp", user.userDetails.mobileNo[0].mobileNumber, productType === "LC" && !loan.isParentLoan ? "contract_lc_sign_eng" : "contract_sign_eng", "loan", null, null, null, false, null); //mobile number change

        //Call sendnotifications
        if (whatsAppResponse.success)
          await postDisbursal.sendNotifications(whatsAppResponse.data._id);
      }
    }

    loan = null;

    return {
      data: longUrl,
      success: true,
      message: sendNotification ? "Link sent successfully" : "Saved successfully",
      status: 200,
      shortUrl: shortUrl,
      isMandateSigned: isMandateSigned,
      mandateAuthMode
    }

  } catch (ex) {
    saveErrorLog("createMandate", apiPath, { loanId, callingMethod }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    return {
      data: "",
      success: false,
      message: ex.message,
      status: 200,
      shortUrl: null,
      isMandateSigned: false
    };
  }
};
module.exports.createMandate = createMandate;

async function checkAndAssignMandate(loan, productType, assign = false) {
  try {
    const currentLoanMandate = await DigioMandateGeneration.findOne({ loanIds: loan._id, isActive: true });

    const userPartnerInfo = await UserPartnerInfo.findOne({ userId: loan.userId, partnerId: loan.partnerId, merchantCode: loan.merchantCode });

    let mandate = null;
    let authMode = null;
    let signedMandateExists = false;
    let oldMandateExists = false;
    //check if mandate exists for partner
    if (!currentLoanMandate) {

      mandate = await DigioMandateGeneration.findOne({
        userId: loan.userId,
        partnerId: loan.partnerId,
        isActive: true,
        status: "register_success"
      })
        .sort({ "mandateDetails.mandate_data.maximum_amount": -1, createdOn: -1 });

      if (productType === "LC" && !loan.isParentLoan && !mandate)
        return {
          data: "",
          success: false,
          message: "No mandate found",
          status: 200,
          signedMandateExists: signedMandateExists,
          oldMandateExists: oldMandateExists
        }

      const isMandateDetailsMatch = await checkMandateCurrentBankDetails(mandate, loan._id);

      if (mandate)
        oldMandateExists = true;

      if (!isMandateDetailsMatch.success) {
        return {
          data: "",
          success: true,
          message: "Mandate Bank Details do not match",
          status: 200,
          signedMandateExists: signedMandateExists,
          oldMandateExists: oldMandateExists
        }
      }

      if (mandate && assign) {
        //Update mandate exist as false if bank details not validated for current loan
        signedMandateExists = true;
        oldMandateExists = true;

        if (!userPartnerInfo || !userPartnerInfo.bankDetails || userPartnerInfo.bankDetails.length === 0)
          signedMandateExists = false;

        const validatedForLoanId = userPartnerInfo.bankDetails && userPartnerInfo.bankDetails[0] && userPartnerInfo.bankDetails[0].docVerification && userPartnerInfo.bankDetails[0].docVerification.validatedForLoanId ? userPartnerInfo.bankDetails[0].docVerification.validatedForLoanId : null;

        if (signedMandateExists && (!validatedForLoanId || !validatedForLoanId.equals(loan._id))) {
          if (productType !== "LC")
            signedMandateExists = false;
          else if (productType === "LC" && loan.isParentLoan)
            signedMandateExists = false;
        }

        if (signedMandateExists) {
          if (!mandate.loanIds.includes(loan._id)) {
            mandate.loanIds.push(loan._id);
            mandate.updatedOn = Date.now();
            await mandate.save();

            eNachService.updateMandateLogs(mandate, loan._id);
          }

          loan.loanContractDetails.authMode = mandate.mode;

          authMode = mandate.mode;

          await loan.save();
        }
      }
    }
    else if (currentLoanMandate && currentLoanMandate.status === "register_success") {
      const isMandateDetailsMatch = await checkMandateCurrentBankDetails(currentLoanMandate, loan._id);

      signedMandateExists = isMandateDetailsMatch.success;
      oldMandateExists = true;

      if (!loan.loanContractDetails.authMode) {
        loan.loanContractDetails.authMode = currentLoanMandate.mode;

        authMode = currentLoanMandate.mode;

        await loan.save();
      }

      //Update mandate exist as false if bank details not validated for current loan
      if (signedMandateExists) {

        if (!userPartnerInfo || !userPartnerInfo.bankDetails || userPartnerInfo.bankDetails.length === 0)
          signedMandateExists = false;

        //const isAlreadyVerified = userPartnerInfo.bankDetails && userPartnerInfo.bankDetails[0] && userPartnerInfo.bankDetails[0].docVerification && userPartnerInfo.bankDetails[0].docVerification.isBankVerified ? true : false;
        const validatedForLoanId = userPartnerInfo.bankDetails && userPartnerInfo.bankDetails[0] && userPartnerInfo.bankDetails[0].docVerification && userPartnerInfo.bankDetails[0].docVerification.validatedForLoanId ? userPartnerInfo.bankDetails[0].docVerification.validatedForLoanId : null;

        if (signedMandateExists && (!validatedForLoanId || !validatedForLoanId.equals(loan._id)))
          signedMandateExists = false;
      }

    }

    return {
      data: "",
      success: true,
      signedMandateExists: signedMandateExists,
      message: "",
      status: 200,
      oldMandateExists: oldMandateExists,
      authMode
    }
  }
  catch (ex) {
    saveErrorLog("checkAndAssignMandate", apiPath, { loan, productType }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);

    return {
      data: "",
      success: false,
      mandateExists: mandateExists,
      message: ex.message,
      status: 200,
      oldMandateExists: oldMandateExists,
      authMode
    }
  }
};
module.exports.checkAndAssignMandate = checkAndAssignMandate;

async function checkStpLoan(loanId) {
  try {
    if (!loanId)
      return { data: "", success: false, message: "Loan Id not provided" };
    else {
      const loan = await LoanInfo.findById(loanId);

      const onlyCharacters = /^[a-zA-Z ]+$/;

      if (!loan)
        return { data: "", success: false, message: "Loan not found" };

      const user = await UserInfo.findById(loan.userId);

      if (!user)
        return { data: "", success: false, message: "User not found" };

      const userPartnerInfo = await UserPartnerInfo.findOne({ userId: loan.userId, partnerId: loan.partnerId, merchantCode: loan.merchantCode });
      if (!userPartnerInfo)
        return { data: "", success: false, message: "UserPartnerInfo not found" };

      //check KYC Status
      if (user.userDetails.kycStatus !== "Approved")
        return { data: "", success: false, message: "KYC is not approved" };

      if (!loan.merchantCode || !loan.productType || !loan.trxnId || !loan.interestRate || !loan.repaymentFrequency || !loan.tenure)
        return { data: "", success: false, message: "Loan details not sufficient" };

      if (!userPartnerInfo.bankDetails || userPartnerInfo.bankDetails && userPartnerInfo.bankDetails.length === 0)
        return { data: "", success: false, message: "Bank Details not present" };

      if (!user.questionnairreDetails || user.questionnairreDetails && !user.questionnairreDetails.whatsApp)
        return { data: "", success: false, message: "Questionnairre details pending" };

      const isAlreadyVerified = userPartnerInfo.bankDetails && userPartnerInfo.bankDetails[0] && userPartnerInfo.bankDetails[0].docVerification && userPartnerInfo.bankDetails[0].docVerification.isBankVerified ? true : false;

      if (!isAlreadyVerified)
        return { data: "", success: false, message: "Bank is not verified" };

      if (!userPartnerInfo.bankDetails[0].accountName || !onlyCharacters.test(userPartnerInfo.bankDetails[0].accountName))
        return { data: "", success: false, message: "Account Name can only contain characters" };

      const partnerConfig = await PartnerConfig.findOne({ partnerId: loan.partnerId });

      let productType = loan.productType ? loan.productType.split("_") : null;
      productType = productType && productType[0] ? productType[0] : "";

      const assignResponse = await checkAndAssignMandate(loan, productType, false);

      if (assignResponse.oldMandateExists || (loan.amount >= partnerConfig.eNachAmount && partnerConfig.isMandateRequired)) {
        const digioBank = await DigioBankList.findOne({
          systemBankName: {
            $regex: new RegExp("^" + userPartnerInfo.bankDetails[0].bankName + "$", "i")
          }
        });

        // if (!digioBank || (digioBank && !digioBank.netBanking && !digioBank.debitCard && !digioBank.esign))
        //   return { data: "", success: false, message: "e-Nach option not available for selected bank. Please select different bank." };
      }

      return { data: "", success: true, message: "STP case" };

    }
  }
  catch (ex) {
    saveErrorLog("checkSTP", apiPath, { loanId }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex.toString());

    return { data: "", success: false, message: ex.message };
  }
};

module.exports.updateContractCases = async function () {
  try {
    const loans = await LoanInfo.find({ status: "Loan Contract Accepted" });

    let i = 0;

    for (const loan of loans) {

      console.log("loanNo: ", loan.loanNo);

      if (loan.lenderDetails && loan.lenderDetails.length > 0) {
        loan.status = "Lender Assigned";

        loan.flowDetails.push({
          Step: "Lender Assigned",
          changedBy: null,
          changedByRole: "System",
          insertedOn: Date.now(),
        });

        await loan.save();

        i += 1;

      }

    }

    return {
      data: "",
      success: true,
      message: `${i} cases updated`,
      status: 200
    }
  }
  catch (ex) {
    saveErrorLog("updateContractCases", apiPath, {}, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    console.log(ex);
    return {
      data: "",
      success: true,
      message: ex.message,
      status: 200
    }
  }
};

module.exports.moveLoanToAcceptedByClient = async function (loans) {
  let i = 0;
  try {

    for (const loanNo of loans) {

      const loan = await LoanInfo.findOne({ loanNo: loanNo });

      if (loan) {
        loan.status = "Accepted By Client";

        loan.loanContractDetails.isAccepted = false;
        loan.loanContractDetails.acceptedOn = null;

        loan.lenderDetails = [];

        let loanIsFundingIndex = loan.flowDetails.findIndex(e => e.Step === "Loan Is Funding");
        if (loanIsFundingIndex >= 0)
          loan.flowDetails.splice(loanIsFundingIndex, 1);

        let contractAccpetedIndex = loan.flowDetails.findIndex(e => e.Step === "Loan Contract Accepted");
        if (contractAccpetedIndex >= 0)
          loan.flowDetails.splice(contractAccpetedIndex, 1);

        await loan.save()

        i += 1;
      }

    }
    return {
      data: "",
      success: true,
      message: `${i} loans updated successuflly`
    }
  }
  catch (ex) {
    saveErrorLog("moveLoanToAcceptedByClient", apiPath, loans, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);

    return {
      data: "",
      success: false,
      message: ex.message
    }
  }
}

module.exports.updateLendingPartner = async function (loans, partnerId) {
  try {

    const partner = await PartnerMaster.findById({ _id: partnerId });

    if (!partner)
      return {
        data: "",
        success: false,
        message: `Partner not found`
      }

    //const partnerUser = await UserPartnerInfo.findOne({ partnerId: partner._id, isPartnerUser: true });

    let successLoans = "";

    let failLoans = "";

    for (const loanNo of loans) {

      const loan = await LoanInfo.findOne({ loanNo: loanNo });

      if (loan) {
        if (loan.lenderDetails && loan.lenderDetails[0]) {
          //loan.lenderDetails[0].lenderId = partnerUser._id;
          loan.lenderDetails[0].lenderPartnerId = partnerId;
          await loan.save();

          const response = await createContract(loan);

          if (response.success)
            successLoans += loanNo;
          else
            failLoans += loanNo;

        }
        else
          failLoans += loanNo;
      }
      else
        failLoans += loanNo;
    }

    return {
      data: "",
      success: true,
      message: `${successLoans} ${failLoans}`
    }
  }
  catch (ex) {
    saveErrorLog("updateLendingPartner", apiPath, { loans, partnerId }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);

    return {
      data: "",
      success: false,
      message: ex.message
    }
  }
};

module.exports.getLoanStatus = async function (loanId, req) {
  let merchantCode = null;
  let status = null;

  const loan = await LoanInfo.findById(loanId);

  if (!loan) {
    return { data: null, success: false, message: "" };
  }

  /** ROLES CHECK */
  if (req && req.user.role != ROLES.ADMIN) {
    if (req.user.role == ROLES.USER && (loan.userId.toString() != req.user.id)) {
      return { data: null, success: false, message: "Access Denied" };
    } else if (req.user.role == ROLES.BORROWING_PARTNER && loan.partnerId.toString() != req.user.partnerId) {
      return { data: null, success: false, message: "Access Denied" };
    } else if (req.user.role == ROLES.LENDING_PARTNER && (!loan.lenderDetails || !loan.lenderDetails.length || loan.lenderDetails[0].lenderPartnerId.toString() != req.user.partnerId)) {
      return { data: null, success: false, message: "Access Denied" };
    }
  }

  try {
    if (loan) {
      merchantCode = loan.merchantCode ? loan.merchantCode : null;
      status = loan.status ? loan.status : null;
    }

  }
  catch (ex) {
    saveErrorLog("getLoanStatus", apiPath, loanId, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex.toString());
  }

  return { data: { merchantCode, status, isForceMandate: loan.isForceMandate }, success: true, message: "" };
};

// ------------------------------------------------------------------------------------------------------------------------------------------
module.exports.insertUpdateBankDetails = async function (model, userId, req) {
  try {
    const loan = await LoanInfo.findById(model.loanId);

    if (!loan) {
      return {
        data: "",
        success: false,
        message: "Invalid Loan Id",
        status: 200
      }
    }

    /** ROLE CHECK */
    if (req && req.user.role == ROLES.USER && req.user.id != loan.userId.toString()) {
      return {
        data: "",
        success: false,
        message: "Invalid Loan Id",
        status: 200
      }
    }

    if (!loan.isForceMandate) {

      let updatedBankDetailCount = await updateBankDetailHistory.countDocuments({ loanId: model.loanId })

      if (updatedBankDetailCount >= (config.get("keys.allowUpdateBankDetailsMax") || 3)) {
        return {
          data: "",
          success: false,
          message: "Exceeded maximum number of updates ",
          status: 200
        }
      }

      //create a new mandate if loan is in funding stage
      // if (loan && loan.status === "Loan Is Funding") {
      //   const mandate = await createMandate(model.loanId, "insertUpdateBankDetails", true);
      // }

    }
    const saveBankDetails = await validateAndSaveBankDetails(userId, model.partnerId, model.accountNo, model.ifsc, model.accountType, model.accountName, null, null, model.merchantCode, model.loanId, true);

    if (saveBankDetails.success && model.loanId) {

      if (saveBankDetails.message === "Bank validated successfully") {
        try {
          updateBankDetailHistory.create({ loanId: model.loanId, insertedBy: userId });
        } catch (er) {
          console.log(er)
        }
      }

      // if (loan && loan.status === "Draft") {
      //   const user = await UserInfo.findById(loan.userId);

      //   let preApprovals = null;

      //   preApprovals = await PreApprovals.findOne({ merchantCode: loan.merchantCode, trxnId: loan.trxnId });

      //   if (!preApprovals)
      //     preApprovals = await PreApprovals.findOne({ merchantCode: loan.merchantCode, productId: loan.productId });

      //   if (preApprovals && preApprovals.expiredOn > Date.now()) {
      //     const sendMessage = await pushToNotifications(loan.partnerId, loan.merchantCode, loan.productId, loan.trxnId, loan.userId, loan._id,
      //       "WhatsApp", user.userDetails.mobileNo[0], "incomplete__application_eng_1", "loan", null, null, null, false, null);

      //     if (sendMessage.success)
      //       await postDisbursal.sendNotifications(sendMessage.data._id);
      //   }
      // }
    }
    return saveBankDetails;
  }
  catch (ex) {
    saveErrorLog("insertUpdateBankDetails", apiPath, { model, userId }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex.toString());

    return {
      data: "",
      success: false,
      message: ex.message ? ex.message : "",
      status: 200
    }
  }

};

module.exports.createMandateForLoanByUser = async function (model, userId, req) {

  if (!model.loanId) {
    return {
      data: "",
      success: false,
      message: "loan id missing ",
      status: 200
    }
  }

  if (!model.mandateType || (model.mandateType != "DEBIT_CARD" && model.mandateType !== "E_SIGN" && model.mandateType !== "NET_BANKING" && model.mandateType !== "PHYSICAL")) {
    return {
      data: "",
      success: false,
      message: "invalid mandate type",
      status: 200
    }
  }

  const loan = await LoanInfo.findById(model.loanId);

  if (!loan) {
    return {
      data: "",
      success: false,
      message: "Invalid loanId",
      status: 400
    }
  }

  /** ROLES CHECK */
  if (req && req.user.role == ROLES.USER && userId != loan.userId.toString()) {
    return {
      data: "",
      success: false,
      message: "Invalid loanId",
      status: 400
    }
  }

  const user = await UserInfo.findById(loan.userId);

  if (loan && (loan.status === "Loan Is Funding" || loan.status === "Draft" || loan.status === "Review" || loan.isForceMandate ||
    (loan.status === "Under Review" && user.userDetails.kycStatus === "Review"))) {

    let authMode = config.get("digio.eNach.auth_mode")

    if (model.mandateType === "DEBIT_CARD" || model.mandateType === "NET_BANKING") {
      authMode = config.get("digio.eNach.authModeApi")
    } else if (model.mandateType === "E_SIGN") {
      authMode = config.get("digio.eNach.authModeAadhaar")
    }
    else if (model.mandateType === "PHYSICAL") {
      authMode = config.get("digio.eNach.authModePhysical")
    }

    const mandate = await createMandate(model.loanId, "insertUpdateBankDetails", false, false, authMode);

    return {
      data: "",
      eNachLink: mandate.shortUrl,
      success: !mandate.shortUrl ? false : true,
      message: mandate.message,
      status: 200,
      authMode: mandate.mandateAuthMode,
      isMandateSigned: mandate.isMandateSigned
    }
  } else {
    return {
      data: "",
      success: false,
      message: "Invalid Loan",
      status: 200
    }
  }
};

module.exports.sendOTPForMerchant = async function (userId, loanId, mobile, purpose, isAutoFill = true, channel = "SMS", isVerifyMobileNumber) {
  let output = {
    success: false,
    message: "Some Error Occurred",
    status: 501,
  };
  try {
    let uniqueMobileNos = [];

    mobile = mobile.map(item => item.mobileNumber)

    if (channel === "SMS")
      uniqueMobileNos = [...new Set(mobile)];
    else
      uniqueMobileNos.push(mobile[0]);

    // CHANGES MADE START
    // const generateCustomOTP = await generateOtpForMerchant(userId, purpose, loanId, uniqueMobileNos, channel);

    let channelConst = channel

    if (channel == "WhatsApp") {
      channelConst = OTP_CHANNELS.WHATSAPP;
    }

    let otpPayload = {
      notifyTo: uniqueMobileNos[0],
      channel: channelConst,
      notificationType: NOTIFICATION_TYPES.MERCHANT_OTP,
      otpLength: 4
    }
    let generateCustomOTP = await sendOtp(otpPayload);

    let isSentOnMobile = false;
    let maskedMobile = getMaskedMobileNo(uniqueMobileNos[0]);

    if (generateCustomOTP.error) {
      let errPayload = {
        success: false,
        message: generateCustomOTP.error.message,
        data: ""
      }
      return res
        .status(200)
        .send(_.pick, errPayload, ["data", "success", "message"]);
    }

    generateCustomOTP = generateCustomOTP.response
    // CHANGES MADE END

    if (uniqueMobileNos.length > 0) {
      let loanInfo = await LoanInfo.findById(loanId).select({ _id: 1, partnerId: 1, merchantCode: 1, productId: 1, trxnId: 1, loanStepDetails: 1 });
      const partnerMaster = await PartnerMaster.findById(loanInfo.partnerId).select({ brandName: 1 });
      const partnerConfig = await PartnerConfig.findOne({ partnerId: mongoose.Types.ObjectId(partnerMaster._id) });

      if (partnerConfig && partnerConfig.autofill_OTP && isAutoFill == false)
        isAutoFill = partnerConfig.autofill_OTP;

      for (const mobileNo of uniqueMobileNos) {
        let smsModel = getSmsModel();
        smsModel.To = mobileNo;
        smsModel.TemplateName = purpose; //Change Password
        smsModel.Type = "TSMS";
        smsModel.From = config.get("keys.from");

        if (channel === "WhatsApp" && purpose === "otp_confirmation") {
          smsModel.VAR1 = generateCustomOTP.otp;
          smsModel.VAR2 = `${partnerMaster.brandName}`;
        }
        else if (purpose === "OTP_Registeration") {
          smsModel.VAR1 = generateCustomOTP.otp;
          smsModel.VAR2 = "";
          if (isAutoFill) {
            smsModel.VAR3 = `@${config.get("keys.webSubdomain")}`;
            smsModel.VAR4 = `#${generateCustomOTP.otp}`;
          }

        } else if (purpose === "Registration OTP") {
          smsModel.VAR1 = generateCustomOTP.otp;
          if (isAutoFill) {
            smsModel.VAR2 = `@${config.get("keys.webSubdomain")}`;
            smsModel.VAR3 = `#${generateCustomOTP.otp}`;
          }
        } else if (purpose === "Merchant_Authentication_OTP2") {
          smsModel.VAR1 = generateCustomOTP.otp;

          if (isAutoFill)
            smsModel.VAR2 = `@${config.get("keys.webSubdomain")} #${generateCustomOTP.otp}`;

        }

        //Save Notification
        const sendSms = await pushToNotifications(loanInfo.partnerId, loanInfo.merchantCode, loanInfo.productId, loanInfo.trxnId,
          userId, loanId, channel, mobileNo, purpose, "otp", null, null, smsModel, false, channel == "SMS" ? "TSMS" : null, null, null, uniqueMobileNos[0]);

        //Send Notification
        if (sendSms.success) {
          const response = await postDisbursal.sendNotifications(sendSms.data._id);

          if (response.success) isSentOnMobile = true;
        } else
          output.message = sendSms.message;

      }

      if (isSentOnMobile) {
        let user = await UserInfo.findById(userId);

        let updateStepName = "OTP Screen"

        if (isVerifyMobileNumber) updateStepName = "Verify Mobile Number"

        const foundIndex = loanInfo.loanStepDetails.findIndex(x => x.Step === updateStepName && x.isCompleted === true);

        if (foundIndex === -1) {
          const otpScreenStep = await updateUserAndLoanSteps(loanInfo, user, updateStepName, null, null);

          loanInfo = otpScreenStep.data.loan;

          user = otpScreenStep.data.user;

          await loanInfo.save();

          await user.save();
        }
      }
    }

    if (isSentOnMobile)
      output = {
        success: true,
        message: uniqueMobileNos.length > 1 ? `${channel === "SMS" ? "SMS" : "WhatsApp"} OTP sent on ${maskedMobile} and alternate mobile numbers` : `${channel === "SMS" ? "SMS" : "WhatsApp"} OTP sent on ${maskedMobile}`,
        status: 200,
      }
    else
      output.message = `Unable to send ${channel === "SMS" ? "SMS" : "WhatsApp"} OTP on ${maskedMobile}`;

  } catch (ex) {
    logger.logs("error :", ex);
    output.message = ex.message;
    saveErrorLog("sendOTP", apiPath, null, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
  }
  return output;
};

async function getLoanEstimation(loanId, loanAmount) {
  try {
    if (!loanId)
      return {
        data: {
          display: false
        },
        success: false,
        message: "Loan Information not sufficient",
        status: 200
      }

    const loan = await LoanInfo.findById(loanId);

    const model = new accessDetailsModel();

    if (!loan.productId || !loan.trxnId)
      return {
        data: {
          display: false
        },
        success: false,
        message: "Loan Information not sufficient",
        status: 200
      }

    model.req = {
      productId: loan.productId,
      trxnId: loan.trxnId,
      loanAmount: loanAmount,
      loanStartDate: getYMDFormattedDate(Date.now(), "/").toString()
    };

    model.partnerId = loan.partnerId;

    const response = await getOfferDetails(model, false, null, true);

    let tenure = "";

    if (loan.isMaturityMonth) tenure = loan.maturity.toString() + " Months";
    else if (loan.repaymentFrequency && loan.repaymentFrequency.toLowerCase() === "daily")
      tenure = loan.maturity.toString() + " Days";
    else if (loan.repaymentFrequency && loan.repaymentFrequency.toLowerCase() === "weekly")
      tenure = loan.maturity.toString() + " Weeks";
    else if (loan.repaymentFrequency && loan.repaymentFrequency.toLowerCase() === "monthly")
      tenure = loan.maturity.toString() + " Months";
    else if (loan.repaymentFrequency && loan.repaymentFrequency.toLowerCase() === "fortnightly")
      tenure = loan.maturity.toString() + " Fortnights";
    else if (loan.repaymentFrequency && loan.repaymentFrequency.toLowerCase() === "adhoc")
      tenure = loan.tenure.toString() + " Days";

    const p_type = loan.productType ? loan.productType.split("_") : null;

    const partnerConfig = await PartnerConfig.findOne({ partnerId: loan.partnerId });

    if (partnerConfig) {

      const LC_Days = partnerConfig.LC_InvoiceTenure ? partnerConfig.LC_InvoiceTenure.toString() : "";
      const LC_ParentDays = partnerConfig.LC_ParentTenure ? partnerConfig.LC_ParentTenure.toString() : "";
      const LC_InvoiceFrequency = partnerConfig.LC_InvoiceFrequency ? partnerConfig.LC_InvoiceFrequency.toString() : "";
      const LC_ParentFrequency = partnerConfig.LC_ParentFrequency ? partnerConfig.LC_ParentFrequency.toString() : "";

      LC_Tenure = `${LC_Days} ${LC_InvoiceFrequency}`;
      LC_ParentTenure = `${LC_ParentDays} ${LC_ParentFrequency}`;
    }

    if (response.success)
      return {
        data: {
          display: true,
          processingFeeCharge: response.data.processingFeeInterestRate,
          emiAmount: response.data.installmentAmount,
          disbursalAmount: response.data.totalDisbursedAmount,
          processingCharges: response.data.processingFee,
          otherCharges: response.data.otherCharges,
          interestRate: response.data.interestRate,
          bankWallet: partnerConfig && partnerConfig.collectionType && partnerConfig.collectionType === "eNach" ? "Bank Account" : "Wallet",
          frequency: loan.repaymentFrequency,
          lateCharges: response.data.latePaymentCharges,
          totalInterest: response.data.totalInterestPayable,
          lateDays: loan.productType === "DL_30" ? "4" : "2",
          partnerName: loan.partner ? loan.partner : "",
          loanAmount: loan.amount ? loan.amount : 0,
          tenure: tenure,
          productType: p_type[0] ? p_type[0] : "",
          LC_Tenure: LC_Tenure,
          LC_ParentTenure: LC_ParentTenure
        },
        success: true,
        message: "Information fetched successfully",
        status: 200
      }
    else
      return {
        data: {
          display: false
        },
        success: false,
        message: "Loan Information not sufficient",
        status: 200
      }
  }
  catch (ex) {
    saveErrorLog("getLoanEstimation", apiPath, { loanId, loanAmount }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    return {
      data: {
        display: false
      },
      success: false,
      message: ex.message,
      status: 200
    }
  }
};
module.exports.getLoanEstimation = getLoanEstimation;

// module.exports.updateImagePath = async function () {
//   try {
//     const users = await UserInfo.find().select({ _id: 1 });

//     for (const userid of users) {
//       const user = await UserInfo.findById(userId);

//       //Pan Document
//       if (user.panDocDetails && user.panDocDetails.length > 0) {
//         for (let i = 0; i < user.panDocDetails.length; i++) {
//           user.panDocDetails[i].filePath = user.panDocDetails[i].filePath.replace("/ProofImages", "");
//         }
//       }

//       //Pan Document
//       if (user.poaDetails && user.poaDetails.length > 0) {
//         for (const poaItem of user.poaDetails) {
//           for (const poaDocItem of poaItem.docDetails) {
//             user.poaDetails[i].filePath = user.panDocDetails[i].filePath.replace("/ProofImages", "");
//           }
//         }
//       }


//     }

//   }
//   catch (ex) {

//   }
// }

// module.exports.loanImagePaths = async function () {
//   try {
//     const loans = await LoanInfo.find().select({ _id: 1 });

//     let updateCount = 0;

//     for (const loanId of loans) {
//       const loan = await LoanInfo.findById(loanId);

//       console.log("Loan No", loan.loanNo);

//       let change = false;

//       if (loan.loanContractDetails && loan.loanContractDetails.contractFile) {
//         loan.loanContractDetails.contractFile = loan.loanContractDetails.contractFile.replace("/ProofImages", "");
//         change = true;
//       }

//       if (loan.loanContractDetails && loan.loanContractDetails.photoFilePath) {
//         loan.loanContractDetails.photoFilePath = loan.loanContractDetails.photoFilePath.replace("/ProofImages", "");
//         change = true;
//       }

//       if (change) {
//         await loan.save();
//         updateCount++;
//       }
//     }

//     return {
//       data: `${updateCount} records updated`,
//       success: true,
//       messsage: ""
//     }
//   }
//   catch (ex) {
//     console.log(ex);
//     return {
//       data: "",
//       success: false,
//       messsage: ex.message
//     }
//   }
// }

async function getLoanCharges(loanAmount, loanId = null, preApprovalId = null, isApplyDiscount = true) {
  let output = {
    productType: "",
    interestRate: 0,
    processingChargePer: 0,
    processingChargeAmt: 0,
    stampingCharge: 0,
    gstCharge: 0,
    otherCharges: 0,
    totalCharges: 0,
    disbursalAmount: 0,
    discountAmount: 0,
    discountType: "",
    computeType: "",
    success: false,
    message: "Some Error Occurred",
    status: 200,
  };
  try {
    // logger.logs("loanId", loanId);
    // logger.logs("preApprovalId", preApprovalId);
    let preApproval = null;
    let productType = null;
    let discountType = "";
    let computeType = "";
    let otherCharges = 0;
    let disbursalAmount = 0;
    let totalCharges = 0;
    let discountAmount = 0;

    if (IsNullEmpty(preApprovalId) && IsNullEmpty(loanId)) {
      output.message = "Either preApprovalId or loanId is mandatory";
      return output;
    }

    if (!IsNullEmpty(preApprovalId)) {
      preApproval = await PreApprovals.findById(preApprovalId);
    } else if (!IsNullEmpty(loanId)) {
      const loanInfo = await LoanInfo.findById(loanId);
      productType = loanInfo.productType.split("_")[0];
      preApproval = await PreApprovals.findOne({ partnerId: mongoose.Types.ObjectId(loanInfo.partnerId), merchantCode: loanInfo.merchantCode, productType: loanInfo.productType.split("_")[0], productId: loanInfo.productId });

      if (IsNullEmpty(preApproval)) {
        preApproval = await PreApprovals.findOne({ partnerId: mongoose.Types.ObjectId(loanInfo.partnerId), merchantCode: loanInfo.merchantCode, productType: loanInfo.productType.split("_")[0], trxnId: loanInfo.trxnId });
      }
    }

    if (preApproval && preApproval != null) {
      productType = productType == null ? preApproval.productType : productType;
      const chargeInPer = preApproval.processingFee ? parseFloat(preApproval.processingFee.replace("%", "").trim()) : 0;
      let interestRate = preApproval.annualIntrestRate ? parseFloat(preApproval.annualIntrestRate.replace("%", "").trim()) : 0;
      let processingCharge = getProcessingFee(loanAmount, chargeInPer);

      if (!IsNullEmpty(loanId)) {
        const loanInfo = await LoanInfo.findById(loanId).select({ discount: 1, interestRate: 1, partnerId: 1, productId: 1, amount: 1 });
        if (loanInfo && loanInfo != null) {
          interestRate = loanInfo.interestRate;

          if (loanInfo.discount && loanInfo.discount != null && loanInfo.discount.campaignId && loanInfo.discount.campaignId != null && loanInfo.discount.discountAmount && loanInfo.discount.discountAmount != null && isApplyDiscount) {
            discountAmount = loanInfo.discount.discountAmount;

            const campaignMaster = await CampaignMaster.findById(loanInfo.discount.campaignId);
            if (campaignMaster && campaignMaster != null) {
              discountType = campaignMaster.discountOn.toLowerCase();
              computeType = campaignMaster.compute.toLowerCase();

              if (campaignMaster.compute.toLowerCase() == "pre" && campaignMaster.discountOn.toLowerCase() == "pf") {
                processingCharge = processingCharge - loanInfo.discount.discountAmount;
              } else if (campaignMaster.discountType.toLowerCase() == "per" && campaignMaster.discountOn.toLowerCase() == "roi") {
                disbursalAmount = loanInfo.discount.discountAmount;
              }
            } else {
              output.message = "Campaign Not Found in Master and Campaign Id is assigned";
              return output;
            }
          } else if (isApplyDiscount) {
            const partnerConfig = await PartnerConfig.findOne({ partnerId: loanInfo.partnerId });
            if (partnerConfig && partnerConfig.campaign) {
              const campaign = partnerConfig.campaign.filter((x) => x.isActive == true);
              if (campaign.length > 0) {
                const campaignMaster = await CampaignMaster.findById(campaign[0].campaignId);

                if (campaignMaster && campaignMaster != null) {
                  //check discountOn and discountType
                  if (campaignMaster.discountOn.toLowerCase() == "pf" || campaignMaster.discountOn.toLowerCase() == "roi") {
                    if (campaignMaster.discountType.toLowerCase() == "per" || campaignMaster.discountType.toLowerCase() == "flat") {
                      if (campaignMaster.discountOn.toLowerCase() == "roi" && campaignMaster.discountType.toLowerCase() == "flat") {
                        output.message = "discount on roi with discount type flat is not allowed";
                        return output;
                      } else if (campaignMaster.discountOn.toLowerCase() == "roi" && campaignMaster.compute.toLowerCase() == "post") {
                        output.message = "compute type post is not allowed for discount on roi";
                        return output;
                      }
                    } else {
                      output.message = "discount type is mandatory";
                      return output;
                    }
                  } else {
                    output.message = "discount On is mandatory";
                    return output;
                  }

                  const startDate = new Date(campaignMaster.fromDate.getFullYear(), campaignMaster.fromDate.getMonth(), campaignMaster.fromDate.getDate(), 0, 0, 0);

                  const endDate = new Date(campaignMaster.toDate.getFullYear(), campaignMaster.toDate.getMonth(), campaignMaster.toDate.getDate(), 23, 59, 59);

                  const today = Date.now();

                  if (today >= startDate && today <= endDate) {
                    discountType = campaignMaster.discountOn.toLowerCase();;
                    computeType = campaignMaster.compute.toLowerCase();

                    let roiAmount = 0;
                    let discountInterestRate = loanInfo.interestRate;

                    if (campaignMaster.discountOn.toLowerCase() == "roi") {
                      const tempModel = {
                        productId: loanInfo.productId,
                        loanAmount: loanInfo.amount,
                        loanStartDate: getYMDFormattedDate(Date.now(), "/")
                      }

                      const model = {
                        partnerId: loanInfo.partnerId,
                        req: tempModel
                      }

                      const offer = await getOfferDetails(model, false, loanInfo.interestRate, true);

                      if (offer.success == true) {
                        roiAmount = offer.data.totalInterestPayable;
                      } else {
                        output.message = offer.message;
                        return output;
                      }
                    }

                    if (campaignMaster.discountType.toLowerCase() == "flat" && campaignMaster.discountOn.toLowerCase() == "pf") {
                      discountAmount = (processingCharge > campaignMaster.value) ? campaignMaster.value : processingCharge;
                    } else if (campaignMaster.discountType.toLowerCase() == "per" && campaignMaster.discountOn.toLowerCase() == "pf") {
                      discountAmount = Math.round((processingCharge * campaignMaster.value) / 100);
                    } else if (campaignMaster.discountType.toLowerCase() == "per" && campaignMaster.discountOn.toLowerCase() == "roi") {
                      const tempModel = {
                        productId: loanInfo.productId,
                        loanAmount: loanInfo.amount,
                        loanStartDate: getYMDFormattedDate(Date.now(), "/")
                      };
                      const model = { partnerId: loanInfo.partnerId, req: tempModel };

                      discountInterestRate = (loanInfo.interestRate > campaignMaster.value) ? (loanInfo.interestRate - campaignMaster.value) : loanInfo.interestRate;

                      const offer = await getOfferDetails(model, false, discountInterestRate, true);

                      if (offer.success == true) {
                        discountAmount = roiAmount - offer.data.totalInterestPayable;
                      } else {
                        output.message = offer.message;
                        return output;
                      }
                    }

                    if (campaignMaster.compute.toLowerCase() == "pre" && campaignMaster.discountOn.toLowerCase() == "pf") {
                      processingCharge -= discountAmount;
                    } else if (campaignMaster.discountType.toLowerCase() == "per" && campaignMaster.discountOn.toLowerCase() == "roi") {
                      disbursalAmount = discountAmount;
                    }
                  }
                }
              }
            }
          }
        } else {
          output.message = "No Loan Found";
          return output;
        }
      }

      const gstCharge = getGstCharges(processingCharge);

      const stampingCharge = getStampingCharges();

      if (productType === "LC") {
        disbursalAmount = disbursalAmount + loanAmount - processingCharge;
        totalCharges = processingCharge;
      } else {
        disbursalAmount = disbursalAmount + loanAmount - processingCharge - gstCharge - stampingCharge;
        otherCharges = stampingCharge + gstCharge;
        totalCharges = processingCharge + gstCharge + stampingCharge;
      }

      output = {
        interestRate,
        productType,
        processingChargePer: chargeInPer,
        processingChargeAmt: processingCharge,
        stampingCharge: stampingCharge,
        gstCharge: gstCharge,
        otherCharges,
        totalCharges,
        disbursalAmount,
        discountAmount,
        discountType,
        computeType,
        success: true,
        message: "fetched successfully",
        status: 200,
      };
    } else output.message = "offer Not Found";
  } catch (ex) {
    logger.logs("error", ex);
    saveErrorLog("getLoanCharges", apiPath, { loanAmount, preApprovalId }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    output.message = ex;
  }
  return output;
};
module.exports.getLoanCharges = getLoanCharges;

function promptDigilocker(user) {
  try {

    if (!user.panDocDetails || user.panDocDetails.length === 0)
      return true;
    else if (!user.poaDetails || user.poaDetails.length === 0)
      return true;
    else if (!user.panDocVerification || (user.panDocVerification &&
      (user.panDocVerification.type !== "Ocr" && user.panDocVerification.type !== "digilocker")))
      return true;
    else if ((!user.poaDetails[0].docVerification ||
      (user.poaDetails[0].docVerification &&
        user.poaDetails[0].docVerification.type !== "Ocr" && user.poaDetails[0].docVerification.type !== "digilocker")))
      return true;
    else
      return false;
  }
  catch (ex) {
    saveErrorLog("promptDigilocker", apiPath, user._id, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    return true;
  }
};

async function getBankInformation(loanId) {
  try {
    const loan = await LoanInfo.findById(loanId);

    let eNach = false;

    if (loan.loanContractDetails && loan.loanContractDetails.redirectionLongUrl && loan.loanContractDetails.redirectionLongUrl.includes("updateBankRedirect"))
      eNach = true;

    let mandate = null;

    if (eNach)
      mandate = await DigioMandateGeneration.findOne({ userId: loan.userId, partnerId: loan.partnerId }).sort({ createdOn: 1 });

    const bankDetails = {
      accountNo: mandate && mandate.mandateDetails && mandate.mandateDetails.mandate_data && mandate.mandateDetails.mandate_data.customer_account_number ? mandate.mandateDetails.mandate_data.customer_account_number : "",
      bankName: mandate && mandate.mandateDetails && mandate.mandateDetails.mandate_data && mandate.mandateDetails.mandate_data.destination_bank_name ? mandate.mandateDetails.mandate_data.destination_bank_name : ""
    };

    return {
      data: bankDetails,
      success: bankDetails.accountNo ? true : false,
      url: loan.loanContractDetails.redirectionLongUrl ? loan.loanContractDetails.redirectionLongUrl : "",
      eNach,
      mode: mandate ? mandate.mode : null,
      message: "Fetched successfully",
      status: 200
    }
  }
  catch (ex) {
    saveErrorLog("getBankInformation", apiPath, { loanId }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);

    return {
      data: null,
      success: false,
      message: ex.message,
      url: null,
      eNach: false,
      status: 200
    }
  }
};

module.exports.getBankInformation = getBankInformation;

async function setEmiAmount(loanId) {
  let output = {
    success: false,
    message: "Some Error Occurred",
    status: 200,
  };
  try {
    const schedule = await emiSchedule(loanId, new Date(Date.now()), false);

    if (schedule != null && schedule.data && schedule.data != null && schedule.data.length > 0) {

      await LoanInfo.findOneAndUpdate({ _id: mongoose.Types.ObjectId(loanId) },
        { $set: { emiAmount: schedule.data[0].rePaymentAmount } }, { new: true, useFindAndModify: false });

      output = {
        success: true,
        message: "emiAmount Set Successfully",
        status: 200,
      };

    } else output.message = "Schedule Is Empty";
  } catch (ex) {
    saveErrorLog("setEmiAmount", apiPath, { loanId }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    output.message = ex.message;
  }
  return output;
};
module.exports.setEmiAmount = setEmiAmount;

async function getMandateRequireStatus(loanId, req) {
  try {
    const loan = await LoanInfo.findById(loanId);

    if (!loan) {
      return {
        data: "",
        success: false,
        message: "Loan id is missing",
        status: 500
      }
    }

    if (req && req.user.id != loan.userId.toString()) {
      return {
        data: "",
        success: false,
        message: "Invalid LoanId",
        status: 500
      }
    }

    const partnerId = loan.partnerId

    const partnerConfig = await PartnerConfig.findOne({ partnerId })

    if (!partnerConfig) {
      return {
        data: "",
        success: false,
        message: "Partner Config not Found",
        status: 500
      }
    }

    let isMandateRequired = false

    let productType = loan.productType ? loan.productType.split("_") : null;
    productType = productType && productType[0] ? productType[0] : "";

    const assignResponse = await checkAndAssignMandate(loan, productType, true);

    if (!assignResponse.success)
      return assignResponse;

    if (partnerConfig.collectionType === "eNach") {
      if (assignResponse.signedMandateExists)
        isMandateRequired = false;
      else
        isMandateRequired = true;
    }
    else if (partnerConfig.collectionType === "Wallet") {
      if (assignResponse.signedMandateExists)
        isMandateRequired = false;
      else if (loan.isForceMandate || assignResponse.oldMandateExists || (loan.amount >= partnerConfig.eNachAmount && !loan.byPassEnach))
        isMandateRequired = true;
      else
        isMandateRequired = false;
    }

    return {
      data: {
        isMandateRequired: false,
        allowBypassMandate: false,  //!partnerConfig.isMandateRequired,
        selectedMode: loan.loanContractDetails && loan.loanContractDetails.authMode ? loan.loanContractDetails.authMode : null
      },
      success: false,
      message: "success",
      status: 200
    };


  } catch (ex) {
    return {
      data: "",
      success: false,
      message: ex.message ? ex.message.toString().replace(/\r?\n|\r/g, " ") : ex,
      status: 500
    }
  }
};

module.exports.getMandateRequireStatus = getMandateRequireStatus;

async function validateResendLinkRequest(loanId, req) {
  try {

    let loan = await LoanInfo.findById(loanId);

    if (!loan) {
      return {
        data: "",
        message: "Invalid LoanId",
        success: false,
        status: 400
      }
    }

    if (req && req.user.role != ROLES.ADMIN && req.user.partnerId != loan.partnerId.toString()) {
      return {
        data: "",
        message: "Access Denied",
        success: false,
        status: 400
      }
    }


    if (loan.status !== "Loan Contract Accepted")
      return {
        data: "",
        success: true,
        status: 200
      }
    else if (loan.status === "Loan Contract Accepted" && loan.isForceMandate)
      return {
        data: "",
        success: true,
        status: 200
      }
    else
      return {
        data: "",
        success: false,
        message: "Loan Contract is already signed",
        status: 200
      }
  }
  catch (ex) {
    saveErrorLog("validateResendLinkRequest", apiPath, { loanId }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    return {
      data: "",
      success: false,
      message: ex.message ? ex.message : "",
      status: 200
    }
  }
};
module.exports.validateResendLinkRequest = validateResendLinkRequest;

async function getKfsDetails(loanId) {
  try {

    const loan = await LoanInfo.findById(loanId);

    const user = await UserInfo.findById(loan.userId).select({ addressDetails: 1, questionnairreDetails: 1, shopDetails: 1, poaDetails: 1, userDetails: 1, panNo: 1, questionnairreDetails: 1, panDocVerification: 1 });

    //let interestType = "";
    let emiAmount = 0;
    let emiDueDate = "";
    let totalInterest = 0;
    let totalRepaymentAmount = 0;
    let termStartDate = null;
    let termEndDate = null;
    let collectionType = null;

    let userFirstName = user.userDetails.firstName ? user.userDetails.firstName.trim() : "";
    let userMiddleName = user.userDetails.middleName ? user.userDetails.middleName.trim() : "";
    let userLastName = user.userDetails.lastName ? user.userDetails.lastName.trim() : "";

    let fullBorrowerName = userFirstName;

    if (userMiddleName)
      fullBorrowerName += ` ${userMiddleName}`;

    if (userLastName)
      fullBorrowerName += ` ${userLastName}`;

    const preApproval = await PreApprovals.findOne({ partnerId: mongoose.Types.ObjectId(loan.partnerId), merchantCode: loan.merchantCode, productType: loan.productType.split("_")[0], $or: [{ trxnId: loan.trxnId }, { productId: loan.trxnId }, { productId: loan.productId }] }).sort({ insertedOn: -1 });

    let processingCharges = 0;

    const charges = await getLoanCharges(loan.amount, loan._id, preApproval._id, false);

    if (!charges.success) {
      return {
        data: "",
        success: false,
        message: charges.message,
        status: 400,
      };
    }

    const borrowingPartnerConfig = await PartnerConfig.findOne({ partnerId: loan.partnerId });

    if (!borrowingPartnerConfig)
      return {
        data: "",
        success: false,
        message: "Borrower Partner Config not found",
        status: 400,
      };

    if (borrowingPartnerConfig.collectionType && borrowingPartnerConfig.collectionType.toLowerCase() === "wallet")
      collectionType = "Wallet";
    else if (borrowingPartnerConfig.collectionType && borrowingPartnerConfig.collectionType.toLowerCase() === "enach")
      collectionType = "NACH/PDC";

    let p_type = loan.productType ? loan.productType.split("_") : "";

    p_type = p_type && p_type[0] ? p_type[0] : "";

    if (p_type !== "LC" || loan.isParentLoan) {
      processingCharges = charges.processingChargeAmt;
    }

    let gstCharges = charges.gstCharge;
    let stampingCharges = charges.stampingCharge;

    const today = new Date();
    let emiDate = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 5, 30, 1, 0);

    if (loan.loanContractDetails && loan.loanContractDetails.acceptedOn)
      emiDate = new Date(loan.loanContractDetails.acceptedOn.getFullYear(), loan.loanContractDetails.acceptedOn.getMonth(), loan.loanContractDetails.acceptedOn.getDate(), 5, 30, 1, 0);

    const disbursalDate = loan.productType == "DL_30" ? dateAdd("day", -1, emiDate) : emiDate;

    let emiDetails = await emiSchedule(loan._id, disbursalDate, false);

    let disbursalAmount = 0;

    if (p_type === "LC")
      disbursalAmount = loan.amount - processingCharges;
    else
      disbursalAmount = loan.amount - processingCharges - gstCharges - stampingCharges;

    let isAdjustedPrinciple = false;

    if (!IsNullEmpty(emiDetails) && !IsNullEmpty(emiDetails.data)) {
      if (emiDetails.data.length > 0) {
        emiAmount = emiDetails.data[0].rePaymentAmount;
        emiDueDate = dateFormat(emiDetails.data[0].rePaymentDate, "dd/mm/yyyy");
        disbursalAmount = loan.productType == "DL_30" ? (disbursalAmount - emiDetails.data[0].rePaymentAmount) : disbursalAmount;

        if (emiDetails.data.length > 1 && (emiDetails.data[emiDetails.data.length - 1].principalRecovered != emiDetails.data[emiDetails.data.length - 2].principalRecovered)) {
          emiDetails.data[emiDetails.data.length - 1].principalOutstanding = `${emiDetails.data[emiDetails.data.length - 1].principalOutstanding.toString()}*`;
          isAdjustedPrinciple = true;
        }
      }

      totalInterest = emiDetails.data.filter((x) => x.interestAmount != 0).reduce(function (prev, cur) {
        return prev + cur.interestAmount;
      }, 0);

      totalRepaymentAmount = emiDetails.data.filter((x) => x.rePaymentAmount != 0).reduce(function (prev, cur) {
        return prev + cur.rePaymentAmount;
      }, 0);

      termStartDate = emiDetails.data[0].rePaymentDate;
      termEndDate = emiDetails.data[emiDetails.data.length - 1].rePaymentDate;

    }

    if (user.poaDetails && user.poaDetails.length > 0) {
      poaNo = user.poaDetails[0].poaNo;
      poaType = user.poaDetails[0].poaType;
    }

    let loanStartDate = "";

    const tomoDate = dateAdd("day", 1, emiDate);

    if (p_type === "LC" && loan.isParentLoan && loan.loanContractDetails && loan.loanContractDetails.isAccepted)
      loanStartDate = dateFormat(loan.loanContractDetails.acceptedOn, "dd/mm/yyyy");
    else if (p_type === "LC" && !loan.isParentLoan)
      loanStartDate = dateFormat(tomoDate, "dd/mm/yyyy");

    let strToday = dateFormat(Date.now(), "dd/mm/yyyy");

    const aprV1 = (processingCharges + totalInterest) / loan.amount;

    const daysInTerm = new DateDiff(termEndDate, termStartDate).days();

    const aprV2 = ((aprV1 / daysInTerm) * 365) * 100;

    return {
      data: {
        today: strToday,
        name: convertToCamelCase(fullBorrowerName),
        amount: loan.amount,
        tenure: loan.tenure.toString() + " Days",
        loanId: loan._id,
        processingFee: processingCharges,
        gstCharges: gstCharges,
        insuranceAmount: 0,
        applicationCharges: 0,
        stampingCharges: stampingCharges,
        preEmiInterest: 0,
        disbursementAmount: disbursalAmount,
        advanceEmiAmount: 0,
        roi: loan.interestRate,
        estimatedEmi: emiAmount,
        emiDate: emiDueDate,
        emiDueDate: "This may vary as per schedule",
        emiSchedule: emiDetails.data,
        amountInWords: inWords(loan.amount),
        frequency: loan.repaymentFrequency.toLowerCase(),
        loanNo: loan.loanNo,
        loanStartDate: loanStartDate,
        isAdjustedPrinciple: isAdjustedPrinciple,
        repaymentFrequency: loan.repaymentFrequency,
        noOfInstalments: emiDetails.data.length,
        totalInterest: round10(totalInterest, -2),
        totalRepaymentAmount: round10(totalRepaymentAmount, -2),
        apr: round10(aprV2, -2),
        collectionType: collectionType
      },
      success: true,
      message: "Fetched successfully",
      status: 200
    };

  } catch (ex) {
    saveErrorLog("getKfsDetails", apiPath, { loanId: loanId }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);

    return {
      data: "",
      success: false,
      message: ex.message ? ex.message : "",
      status: 500,
    };
  }
};
module.exports.getKfsDetails = getKfsDetails;

module.exports.setLoanCharges = async function (loanId, lendingPartnerId = null) {
  let output = {
    success: false,
    message: "Some Error Occurred",
    status: 200
  };
  try {
    let productPartnerConfig = null;
    const loanInfo = await LoanInfo.findById(loanId);
    const interestType = loanInfo.interestType;
    const interestRate = loanInfo.interestRate;
    //const maturity = loanInfo.maturity;
    const tenure = loanInfo.tenure;
    const adhocDays = loanInfo.adhocDays;
    const repaymentFrequency = loanInfo.repaymentFrequency;
    const _interestType = interestType === "Simple Interest" ? "simple" : "compound";
    let roundType = _interestType === "compound" ? "upper" : "lower";

    let lendingPartner = null;
    if (lendingPartnerId != null) {
      lendingPartner = await PartnerMaster.findById(lendingPartnerId).select({ processingFeeDisPer: 1, interestType: 1, roundType: 1 });
      roundType = IsNullEmpty(lendingPartner.roundType) ? roundType : lendingPartner.roundType.toLowerCase();
    }

    const productType = getProductType(loanInfo.productType);
    const partnerMaster = await PartnerMaster.findById(loanInfo.partnerId);
    const productMaster = await ProductMaster.findOne({ product: productType });

    if (!IsNullEmpty(productMaster)) {
      productPartnerConfig = await ProductPartnerConfig.findOne({ partnerId: mongoose.Types.ObjectId(partnerMaster._id), productId: mongoose.Types.ObjectId(productMaster._id) });

      if (!IsNullEmpty(productMaster)) {
        if (!IsNullEmpty(productPartnerConfig.interestComputation)) {
          //Emi Amount Calculation
          const emiSchedule = await calculateEmi(loanInfo.amount, tenure, interestRate, false, repaymentFrequency, new Date(new Date().toISOString()), _interestType, false, roundType, loanInfo.productType, false, adhocDays, "Post");

          if (emiSchedule.length > 0) {
            const preApproval = await PreApprovals.findOne({
              partnerId: mongoose.Types.ObjectId(loanInfo.partnerId),
              merchantCode: loanInfo.merchantCode,
              productType: productType,
              $or: [{ trxnId: loanInfo.trxnId }, { productId: loanInfo.trxnId }, { productId: loanInfo.productId }]
            }).sort({ insertedOn: -1 });

            let interestPreRecovered = 0;

            const charges = await getLoanCharges(loanInfo.amount, loanInfo._id, loanInfo.offerId);

            const stampingCharges = charges.stampingCharge;
            const custProcessingFee = charges.processingChargeAmt;
            const custGstCharges = charges.gstCharge;

            const custTotalCharges = custProcessingFee + custGstCharges + stampingCharges;

            //Used for discount percentage
            let processingFee = 0;
            if (lendingPartner != null && lendingPartner.processingFeeDisPer) {
              processingFee = getProcessingFee(loanInfo.amount, lendingPartner.processingFeeDisPer);
            }
            processingFee = custProcessingFee - processingFee;
            const gstCharges = getGstCharges(processingFee);
            const totalCharges = processingFee + gstCharges + stampingCharges;

            if (productPartnerConfig.interestComputation.toLowerCase() == "pre" && productType == "DL") {
              interestPreRecovered = emiSchedule[0].interestAmount;
            } else if (productPartnerConfig.interestComputation.toLowerCase() == "pre") {
              interestPreRecovered = emiSchedule.filter((x) => x.interestAmount != 0).reduce(function (prev, cur) {
                return prev + cur.interestAmount;
              }, 0);
            }

            const disbursalAmount = loanInfo.amount - custTotalCharges - interestPreRecovered;
            const tempDisbursalDetails = loanInfo.disbursalDetails;

            tempDisbursalDetails.stampingCharges = +parseFloat(stampingCharges).toFixed(2);

            if (productType == "LC") {
              tempDisbursalDetails.custProcessingFee = 0;
              tempDisbursalDetails.custGstCharges = 0;
              tempDisbursalDetails.custTotalChargesRecovered = +parseFloat(stampingCharges).toFixed(2);

              tempDisbursalDetails.processingFee = 0;
              tempDisbursalDetails.gstCharges = 0;
              tempDisbursalDetails.totalChargesRecovered = +parseFloat(stampingCharges).toFixed(2);
            } else {
              tempDisbursalDetails.custProcessingFee = productType == "LC" ? 0 : +parseFloat(custProcessingFee).toFixed(2);
              tempDisbursalDetails.custGstCharges = productType == "LC" ? 0 : +parseFloat(custGstCharges).toFixed(2);
              tempDisbursalDetails.custTotalChargesRecovered = +parseFloat(custTotalCharges).toFixed(2);

              tempDisbursalDetails.processingFee = productType == "LC" ? 0 : +parseFloat(processingFee).toFixed(2);
              tempDisbursalDetails.gstCharges = productType == "LC" ? 0 : +parseFloat(gstCharges).toFixed(2);
              tempDisbursalDetails.totalChargesRecovered = +parseFloat(totalCharges).toFixed(2);
            }

            tempDisbursalDetails.interestPreRecovered = +parseFloat(interestPreRecovered).toFixed(2);
            tempDisbursalDetails.disbursalAmount = +parseFloat(disbursalAmount).toFixed(2);

            await LoanInfo.findOneAndUpdate({ _id: mongoose.Types.ObjectId(loanId) }, { $set: { disbursalDetails: tempDisbursalDetails, updatedOn: Date.now() } }, { new: true, useFindAndModify: false });

            await setEmiAmount(loanId);

            output = {
              success: true,
              message: "Charges Updated Successfully",
              status: 200
            };
          } else output.message = "Issue while calculating EmiSchedule";
        } else output.message = "interestComputation is not Set for productType " + productType;
      } else output.message = "Product Partner Config is not Set for productType " + productType;
    } else output.message = "Product Master not found for productType " + productType;
  } catch (ex) {
    logger.logs("error :", ex);
    saveErrorLog("setCharges", apiPath, { loanId }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    output.message = ex.message;
  }

  logger.logs("output", output);
  return output;
};

module.exports.updateLoanContractImage = async function (loanId, imagePath) {
  let output = {
    success: false,
    message: "Some Error Occurred",
    status: 200
  };
  try {

    let filter = {
      _id: mongoose.Types.ObjectId(loanId)
    }

    let [base, filePath] = imagePath.split(".com/")

    let fileName = filePath.split("/")
    fileName = fileName[fileName.length - 1]

    let update = {
      $set: {
        "loanContractDetails.photoFileName": fileName,
        "loanContractDetails.photoFilePath": filePath
      }
    }

    await LoanInfo.updateOne(filter, update)

    output = {
      success: true,
      message: "Image Updated Successfully",
      status: 200
    };

  } catch (ex) {
    logger.logs("error :", ex);
    saveErrorLog("updateLoanContractImage", apiPath, { loanId }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    output.message = ex.message;
  }
  return output;
};

module.exports.downloadAllDocument = async function (loanId, req) {
  let output = {
    success: false,
    message: "Some Error Occurred",
    status: 200
  };
  try {

    let loan = await LoanInfo.findById(loanId)

    if (!loan) return output

    if (req && req.user.role != ROLES.ADMIN) {

      if (req.user.role == ROLES.LENDING_PARTNER && loan.lenderDetails[0].lenderPartnerId.toString() != req.user.partnerId) {
        return {
          success: false,
          message: "Access Denied",
          status: 400
        }
      }

      if ((req.user.role == ROLES.BORROWING_PARTNER || req.user.role == ROLES.USER) && loan.partnerId.toString() != req.user.partnerId) {
        return {
          success: false,
          message: "Access Denied",
          status: 400
        }
      }
    }

    let { userId } = loan

    const user = await UserInfo.findById(userId)

    let files = []

    if (loan.loanContractDetails) {
      if (loan.loanContractDetails.photoFilePath) {
        let url = getSignedUrlToDownload({ url: loan.loanContractDetails.photoFilePath })
        files.push({ url, name: loan.loanContractDetails.photoFileName })
      }

      if (loan.loanContractDetails.contractFile) {
        let url = getSignedUrlToDownload({ url: loan.loanContractDetails.contractFile })
        files.push({ url, name: "Loan_Contract.pdf" })
      }

      if (loan.loanContractDetails.kfsFile) {
        let url = getSignedUrlToDownload({ url: loan.loanContractDetails.kfsFile })
        files.push({ url, name: "kfs.pdf" })
      }
    }

    if (user.panDocDetails && user.panDocDetails.length) {
      user.panDocDetails.forEach(item => {
        if (item.filePath) {
          let url = getSignedUrlToDownload({ url: item.filePath })
          files.push({ url, name: item.fileName })
        }
      })
    }

    if (user.poaDetails && user.poaDetails.length) {
      user.poaDetails.forEach(element => {
        if (element.docDetails) {
          element.docDetails.forEach(item => {
            if (item.filePath) {
              let url = getSignedUrlToDownload({ url: item.filePath })
              files.push({ url, name: item.fileName })
            }
          })
        }
      })
    }

    if (user.gstDetails && user.gstDetails.length) {
      user.gstDetails.forEach(element => {
        if (element.docDetails) {
          element.docDetails.forEach(item => {
            if (item.filePath) {
              let url = getSignedUrlToDownload({ url: item.filePath })
              files.push({ url, name: item.fileName })
            }
          })
        }
      })
    }

    if (user.additionalDocuments && user.additionalDocuments.length) {
      user.additionalDocuments.forEach(element => {
        if (element.docDetails) {
          element.docDetails.forEach(item => {
            if (item.filePath) {
              let url = getSignedUrlToDownload({ url: item.filePath })
              files.push({ url, name: item.fileName })
            }
          })
        }
      })
    }

    output = {
      data: files,
      success: true,
      message: "Success",
      status: 200
    };

  } catch (ex) {
    logger.logs("error :", ex);
    saveErrorLog("downloadAllDocument", apiPath, { loanId }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    output.message = ex.message;
  }
  return output;
};

module.exports.getLastCompletedStep = async function (loanId, req) {
  try {
    const loan = await LoanInfo.findById(loanId);

    if (!loan) {
      return {
        data: null,
        success: false,
        message: "Invalid LoanId",
        status: 400
      };
    }
    /** ROLES CHECK */
    if (req && req.user.role != ROLES.ADMIN) {
      if (req.user.role == ROLES.USER && (loan.userId.toString() != req.user.id)) {
        return { data: null, success: false, message: "Access Denied", status: 400 };
      } else if (req.user.role == ROLES.BORROWING_PARTNER && loan.partnerId.toString() != req.user.partnerId) {
        return { data: null, success: false, message: "Access Denied", status: 400 };
      } else if (req.user.role == ROLES.LENDING_PARTNER && (!lenderDetails || !lenderDetails.length || lenderDetails[0].lenderPartnerId.toString() != req.user.partnerId)) {
        return { data: null, success: false, message: "Access Denied", status: 400 };
      }
    }


    let continueFrom = "loanTab", isForceDigiLocker = false, isCaptureNewDocument = false

    if (loan.status === "Draft" && loan.lastStepCompleted) {

      switch (loan.lastStepCompleted) {
        case "Loan":
          const user = await UserInfo.findById(loan.userId);

          let digilocker = promptDigilocker(user);
          let kycInfo = await checkKycExpire(user, loan, digilocker)

          if (kycInfo) {
            digilocker = kycInfo.digilocker
            isForceDigiLocker = kycInfo.isForceDigiLocker
            isCaptureNewDocument = kycInfo.isCaptureNewDocument
          }

          if (digilocker)
            continueFrom = loan.isMobileNumberConfirmed ? "digilocker" : "";
          else
            continueFrom = "kycTab";
          break;
        case "KYC":
          continueFrom = "shopTab";
          break;
        case "Shop":
          continueFrom = "questionairreTab";
          break;
        case "Questionnairre":
          continueFrom = "reviewTab";
          break;
        case "Review":
          continueFrom = "bank";
          break;
        case "Bank":

          const { data } = await getMandateRequireStatus(loanId);

          if (data.isMandateRequired)
            continueFrom = "mandate";
          else
            continueFrom = "loan contract";
          break;
        case "eNach":
          continueFrom = "bank";

          if (loan.primaryCollectionType === "eNach" || loan.secondaryCollectionType === "eNach") {
            const authMode = loan.loanContractDetails && loan.loanContractDetails.authMode ? loan.loanContractDetails.authMode : "";

            const mandate = await DigioMandateGeneration.findOne({ loanIds: loan._id, mode: authMode }).sort({ createdOn: -1 });

            if (mandate && (mandate.status === "success" || mandate.status === "register_success"))
              continueFrom = "loan contract";
          }
          break;
        case "Loan Contract":
          continueFrom = "loan contract";
          break;
        default:
          continueFrom = "loanTab";
          break;
      }

    }

    return {
      data: continueFrom,
      isForceDigiLocker,
      isCaptureNewDocument,
      success: true,
      message: "",
      status: 200
    };

  }
  catch (ex) {
    saveErrorLog("getLastCompletedStep", apiPath, { loanId }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    output.message = ex.message;

    return {
      data: "",
      success: false,
      message: ex.message ? ex.message : "",
      status: 200
    };
  }
}

const checkKycExpire = async (user, loan, digilocker) => {

  let isForceDigiLocker = false, expired = false
  let isCaptureNewDocument = false


  try {

    let productType = loan.productType
    let loanProductType = productType.substr(0, 2)

    if (user.userDetails && user.userDetails.kycApprovedOn) {

      const diff = new DateDiff(new Date(), user.userDetails.kycApprovedOn);
      const diffDays = diff.days()

      let { config } = await ConfigParameters.findOne({ "channel": "KYC_RECAPTURE", })

      let productConfig = config[loanProductType]

      if (diffDays >= productConfig.value) {
        expired = true
        digilocker = true

        if (user.panDocVerification && user.panDocVerification.type == "digilocker") {
          isForceDigiLocker = true
        }
      }

      let panDocDetails = user.panDocDetails

      if (panDocDetails && panDocDetails.length) {
        let panInfo = panDocDetails[panDocDetails.length - 1]
        if (panInfo) {
          const panDiff = new DateDiff(new Date(), panInfo.insertedOn);
          const panDiffDays = panDiff.days()
          isCaptureNewDocument = panDiffDays >= productConfig.value
        }
      }

    }
  } catch (e) {
    console.log(e)
  }

  return {
    isForceDigiLocker,
    digilocker,
    expired,
    isCaptureNewDocument
  }
}

function getContractFont(template) {
  let font = null;

  try {

    switch (template) {
      case "loanContract_Kinara.html":
        font = "tw cen mt";
    }

  }
  catch (ex) {
    saveErrorLog("getContractFont", apiPath, { template }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
  }

  return font;
}



module.exports.checkKycExpire = checkKycExpire
