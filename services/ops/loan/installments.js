//Libraries
// -------------------------------------------------------------------------------------------------------------------
const mongoose = require("mongoose");
const debug = require("debug")("app:installments");
const IsNullEmpty = require("isnullemptyorwhitespace");
const apiPath = __dirname;
const { dateAdd } = require("dateadd");
const DateDiff = require("date-diff");
const config = require("config");

// -------------------------------------------------------------------------------------------------------------------

//Models
// -------------------------------------------------------------------------------------------------------------------
const { LoanInfo, LoanInstallments, LoanFees, HoldCollectionLog } = require("../../../models/ops/loan/loan");
const { FundfinaPolicy } = require("../../../models/ops/partner/partner");
const { PartnerMaster, ProductMaster, ProductPartnerConfig, LedgerAccount } = require("../../../models/ops/partner/partner");
const { UserInfo } = require("../../../models/ops/registration/users");
const { PaymentCollection } = require("../../../models/ops/loan/postDisbursal");
// -------------------------------------------------------------------------------------------------------------------

//External Methods
// -------------------------------------------------------------------------------------------------------------------
const { saveErrorLog } = require("../../../services/ops/common/masters");
const { round10, getFromDate, getToDate, getDMYFormattedDate, getName, getMailModel } = require("../common/common");
const lendingPartnerAPI = require("../../../services/ops/partner/lendingPartner");
const { groupBy } = require("lodash");
const { sendMailOnFailure, sendMail } = require("../common/mailer");
const Logger = require("../../../middleware/logger");
const logger = new Logger();
const { PAYMENT_CHANNEL } = require('../common/constants').Constants
// -------------------------------------------------------------------------------------------------------------------


//Internal Methods
// -------------------------------------------------------------------------------------------------------------------
async function convertEmiScheduleToInstallments(loanId, loanNo, emis, partnerId, productType) {
    let output = {
        data: null,
        success: false,
        message: "Some Error Occurred",
        status: 200,
    };
    try {
        if (emis != null && emis.length > 0) {
            const installments = [];
            for (let i = 0; i < emis.length; i++) {
                const installment = {
                    loanId: mongoose.Types.ObjectId(loanId),
                    loanNo: loanNo,
                    installmentNumber: emis[i].emiNumber,
                    partnerId: partnerId,
                    installmentPrincipal: round10(emis[i].principalRecovered, -2),
                    initialPrincipalOutstanding: round10(emis[i].principalOutstanding, -2),
                    installmentInterest: round10(emis[i].interestAmount, -2),
                    installmentAmount: round10(emis[i].rePaymentAmount, -2),
                    moratoriumInterest: 0,
                    dueDate: emis[i].rePaymentDate,
                    actualDueDate: emis[i].rePaymentActualDate,
                    channel: null,
                    status: emis[i].rePaymentStatus,
                    holdCollectionViaWallet: false,
                    lastRepaymentRequestedDate: null,
                    principalReceived: 0,
                    interestReceived: 0,
                    moratoriumInterestReceived: 0,
                    principalOutstanding: round10(emis[i].principalRecovered, -2),
                    interestOutstanding: round10(emis[i].interestAmount, -2),
                    moratoriumInterestOutstanding: 0,
                    transactionDate: null,
                    lendingPartnerUTR: null,
                    lendingPartnerSettlementDate: null,
                    insertedOn: emis[i].insertedOn,
                    updatedOn: emis[i].updatedOn,
                    updatedBy: null,
                    productType: productType
                };
                installments.push(installment);
            }

            output = {
                data: installments,
                success: true,
                message: "Converted Successfully",
                status: 200,
            };
        } else output.message = "No EmiDetails Found";

    } catch (ex) {
        saveErrorLog("convertEmiScheduleToInstallments", apiPath, { loanId, loanNo, emis, partnerId, productType }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};
module.exports.convertEmiScheduleToInstallments = convertEmiScheduleToInstallments;

module.exports.saveInstallments = async function (loanId, emis) {
    let output = {
        success: false,
        message: "Some Error Occurred",
        status: 200,
    };
    try {
        const allowedLoanStatus = ["loan is funding", "lender assigned", "initiated fund transfer", "ready for disbursal"];

        const loanInfo = await LoanInfo.findById(loanId).select({ status: 1, loanNo: 1, partnerId: 1, productType: 1 });
        const count = await LoanInstallments.countDocuments({ loanNo: loanInfo.loanNo });

        if (loanInfo && loanInfo != null) {
            if (count == 0) {
                if (allowedLoanStatus.includes(loanInfo.status.toLowerCase())) {

                    const convertedInstallments = await convertEmiScheduleToInstallments(loanId, loanInfo.loanNo, emis, loanInfo.partnerId, loanInfo.productType);

                    if (convertedInstallments.success) {
                        const installments = convertedInstallments.data;


                        if (installments.length > 0) {
                            const bulkInsert = await LoanInstallments.insertMany(installments, { ordered: true });
                            logger.logs("bulkInsert", bulkInsert);


                            const totalPrincipalOutstanding = installments.filter((x) => x.principalOutstanding > 0).reduce(function (prev, cur) {
                                return prev + parseFloat(cur.principalOutstanding);
                            }, 0);

                            const totalInterestOutstanding = installments.filter((x) => x.interestOutstanding > 0).reduce(function (prev, cur) {
                                return prev + parseFloat(cur.interestOutstanding);
                            }, 0);


                            let emiConsolidated = {}

                            if (convertedInstallments.data)
                                emiConsolidated = await getEmiConsolidatedInformation(convertedInstallments.data, []) || {}

                            await LoanInfo.updateOne({ _id: mongoose.Types.ObjectId(loanId) }, {
                                $set: {
                                    principalAmount: {
                                        collected: 0,
                                        outstanding: round10(totalPrincipalOutstanding, -2),
                                        settled: 0
                                    },
                                    interestAmount: {
                                        collected: 0,
                                        outstanding: round10(totalInterestOutstanding, -2),
                                        settled: 0
                                    },
                                    totalEmisCollected: 0,
                                    lastEmiPaidDate: null,
                                    nextEmiRepaymentDate: installments[0].dueDate,
                                    ...emiConsolidated
                                }
                            });

                            return {
                                success: true,
                                message: "Inserted Successfully",
                                status: 200,
                            };
                        } else {
                            return {
                                success: false,
                                message: "No Installments",
                                status: 200,
                            };
                        }

                    } else output.message = convertedInstallments.message;
                } else output.message = "loan status should be either " + allowedLoanStatus.join(" or ");
            } else {
                output = {
                    success: true,
                    message: "Already Exists",
                    status: 200,
                };
            }
        } else output.message = "Loan Does Not Exists";
    } catch (ex) {
        saveErrorLog("saveInstallments", apiPath, { loanId, emis }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};

async function getInstallments(loanId = null, loanNo = null, installmentStatus = null, installmentNumber = null) {
    let output = {
        data: null,
        success: false,
        message: "Some Error Occurred",
        status: 200,
    };
    try {
        let filter = {};

        if (loanId != null || loanNo != null) {
            if (loanId != null) filter.loanId = mongoose.Types.ObjectId(loanId);

            if (loanNo != null) filter.loanNo = loanNo;

            if (installmentStatus != null) filter.status = { $in: installmentStatus };

            if (installmentNumber != null) filter.installmentNumber = installmentNumber;

            const installments = await LoanInstallments.find(filter).sort({ installmentNumber: 1 });

            if (installments && installments.length > 0) {
                output = {
                    data: installments,
                    success: true,
                    message: "Fetched Successfully",
                    status: 200,
                };
            } else output.message = "No Installments Found";
        } else output.message = "loanId or loanNo is mandatory";
    } catch (ex) {
        saveErrorLog("getInstallments", apiPath, { loanId, loanNo, installmentStatus, installmentNumber }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};
module.exports.getInstallments = getInstallments;

async function getMultipleLoansInstallments(loanIds = null, loanNos = null, installmentStatus = null, installmentNumber = null) {
    let output = {
        data: [],
        success: false,
        message: "Some Error Occurred",
        status: 200,
    };
    try {
        let filter = {};

        if (loanIds != null || loanNos != null) {
            if (loanIds != null) filter.loanId = { $in: loanIds };

            if (loanNos != null) filter.loanNo = { $in: loanNos };

            if (installmentStatus != null) filter.status = { $in: installmentStatus };

            if (installmentNumber != null) filter.installmentNumber = installmentNumber;

            // const installments = await LoanInstallments.find(filter).sort({ loanNo: 1, installmentNumber: 1 }).lean();
            //logger.logs("filter", filter);
            const installments = await LoanInstallments.aggregate([{ $match: filter }, { $sort: { loanNo: 1, installmentNumber: 1 } }]).allowDiskUse(true)

            if (installments && installments.length > 0) {
                output = {
                    data: installments,
                    success: true,
                    message: "Fetched Successfully",
                    status: 200,
                };
            } else {
                output = {
                    data: [],
                    success: true,
                    message: "No Installments found",
                    status: 200,
                };
            }
        } else output.message = "loanIds or loanNos are mandatory";
    } catch (ex) {
        logger.logs(ex);
        saveErrorLog("getMultipleLoansInstallments", apiPath, { loanIds, loanNos, installmentStatus, installmentNumber }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};
module.exports.getMultipleLoansInstallments = getMultipleLoansInstallments;

async function getLoanFees(loanId = null, loanNo = null, installmentStatus = null, installmentNumber = null) {
    let output = {
        data: null,
        success: false,
        message: "Some Error Occurred",
        status: 200,
    };
    try {
        let filter = {};

        if (loanId != null || loanNo != null) {
            if (loanId != null) filter.loanId = mongoose.Types.ObjectId(loanId);

            if (loanNo != null) filter.loanNo = loanNo;

            if (installmentStatus != null) filter.status = installmentStatus;

            if (installmentNumber != null) filter.installmentNumber = installmentNumber;

            const fees = await LoanFees.find(filter).sort({ installmentNumber: 1 });

            if (fees && fees.length > 0) {
                output = {
                    data: fees,
                    success: true,
                    message: "Fetched Successfully",
                    status: 200,
                };
            } else {
                output = {
                    data: null,
                    success: true,
                    message: "No Fees Found",
                    status: 200,
                };
            }
        } else output.message = "loanId or loanNo is mandatory";
    } catch (ex) {
        saveErrorLog("getLoanFees", apiPath, { loanId, loanNo, installmentStatus, installmentNumber }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};
module.exports.getLoanFees = getLoanFees;

async function getMultipleLoanFees(loanIds = null, loanNos = null, installmentStatus = null, installmentNumber = null) {
    let output = {
        data: [],
        success: false,
        message: "Some Error Occurred",
        status: 200,
    };
    try {
        let filter = {};

        if (loanIds != null || loanNos != null) {
            if (loanIds != null) filter.loanId = { $in: loanIds };

            if (loanNos != null) filter.loanNo = { $in: loanNos };

            if (installmentStatus != null) filter.status = { $in: installmentStatus };

            if (installmentNumber != null) filter.installmentNumber = installmentNumber;

            //const fees = await LoanFees.find(filter).sort({ loanNo: 1, installmentNumber: 1 });
            const fees = await LoanFees.aggregate([{ $match: filter }, { $sort: { loanNo: 1, installmentNumber: 1 } }]).allowDiskUse(true)

            if (fees && fees.length > 0) {
                output = {
                    data: fees,
                    success: true,
                    message: "Fetched Successfully",
                    status: 200,
                };
            } else {
                output = {
                    data: [],
                    success: true,
                    message: "No Fees Found",
                    status: 200,
                };
            }
        } else output.message = "loanId or loanNo is mandatory";
    } catch (ex) {
        saveErrorLog("getMultipleLoanFees", apiPath, { loanIds, loanNos, installmentStatus, installmentNumber }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};
module.exports.getMultipleLoanFees = getMultipleLoanFees;

module.exports.getInstallmentsCollected = async function (loanIds = null, isRequireAllInstallments = false, isRequireFees = false, installmentNumber = null) {
    let output = {
        data: [],
        installments: [],
        fees: [],
        success: false,
        message: "Some Error Occurred",
        status: 200,
    };
    try {
        if (loanIds != null) {
            let responseData = [];

            const allInstallments = await getMultipleLoansInstallments(loanIds, null, ["Received", "Partial"], installmentNumber);
            const allFees = await getMultipleLoanFees(loanIds, null, ["Received", "Partial"], installmentNumber);

            if (allInstallments != null && allInstallments.success == true) {
                if (isRequireAllInstallments) output.installments = allInstallments.data;
                if (allFees != null && allFees.success == true) {
                    if (isRequireFees) output.fees = allFees.data;

                    for (const loanId of loanIds) {
                        const record = {
                            loanId: loanId,
                            interestReceived: 0,
                            principalReceived: 0,
                            collectedAmount: 0,
                            penaltyReceived: 0,
                            moratoriumInterestReceived: 0,
                            totalCollectedAmount: 0
                        };

                        const installments = allInstallments.data.filter((x) => x.loanId.toString() == loanId.toString());

                        if (installments != null && installments.length > 0) {
                            record.interestReceived = installments.filter((x) => x.interestReceived > 0).reduce(function (prev, cur) {
                                return prev + parseFloat(cur.interestReceived);
                            }, 0);

                            record.principalReceived = installments.filter((x) => x.principalReceived > 0).reduce(function (prev, cur) {
                                return prev + parseFloat(cur.principalReceived);
                            }, 0);

                            record.moratoriumInterestReceived = installments.filter((x) => x.moratoriumInterestReceived > 0).reduce(function (prev, cur) {
                                return prev + parseFloat(cur.moratoriumInterestReceived);
                            }, 0);
                        }

                        const fees = allFees.data.filter((x) => x.loanId.toString() == loanId.toString());

                        if (fees != null && fees.success == true) {
                            record.penaltyReceived = fees.filter((x) => x.feeAmountReceived > 0).reduce(function (prev, cur) {
                                return prev + parseFloat(cur.feeAmountReceived);
                            }, 0);
                        }

                        record.collectedAmount = record.interestReceived + record.principalReceived;
                        record.totalCollectedAmount = record.interestReceived + record.principalReceived + record.moratoriumInterestReceived + record.penaltyReceived;
                        responseData.push(record);

                    }
                } else output.message = allFees.message;
            } else output.message = allInstallments.message;

            output.data = responseData;
            output.success = true;
            output.message = "Fetched Successfully";
            output.status = 200;
        } else output.message = "loanIds is mandatory";
    } catch (ex) {
        logger.logs(ex);
        saveErrorLog("getInstallmentsCollected", apiPath, { loanIds, isRequireAllInstallments, isRequireFees, installmentNumber }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};

module.exports.getInstallmentDetails = async function (loanId, isRequireAllInstallments = false, isRequireFees = false) {
    let output = {
        firstInstallmentDate: null,
        lastInstallmentDate: null,
        firstInstallmentAmount: 0,
        lastInstallmentAmount: 0,
        totalInterestAmount: 0,
        overDuePendingInstallments: 0,
        overDuePendingInstallmentAmount: 0,
        totalPenalty: 0,
        totalPenaltyInstallments: 0,
        totalInstallments: 0,
        pendingInstallments: 0,
        receivedInstallments: 0,
        installments: null,
        fees: null,
        success: false,
        message: "Some Error Occurred",
        status: 200,
    };
    try {
        const installments = await getInstallments(loanId);

        if (installments.success) {
            const today = new Date();
            const repaymentFrequency = ["daily", "weekly", "fortnightly"];

            const loanInfo = await LoanInfo.findById(loanId).select({ repaymentFrequency: 1 });

            if (installments.data && installments.data != null && installments.data.length > 0) {
                output.totalInstallments = installments.data.length;

                let installment = installments.data.filter((x) => x.installmentNumber == 1);

                if (installment && installment != null && installment.length > 0) {
                    output.firstInstallmentAmount = installment[0].installmentAmount;
                    output.firstInstallmentDate = installment[0].actualDueDate;
                }

                installment = installments.data.filter((x) => x.installmentNumber == installments.data.length);

                if (installment && installment != null && installment.length > 0) {
                    output.lastInstallmentAmount = installment[0].installmentAmount;
                    output.lastInstallmentDate = installment[0].actualDueDate;
                }

                output.totalInterestAmount = installments.data.filter((x) => x.installmentInterest > 0).reduce(function (prev, cur) {
                    return prev + parseFloat(cur.installmentInterest);
                }, 0);

                //Pending
                const pendingInstallments = installments.data.filter(x => x.status == "Pending" || x.status == "Partial");

                output.pendingInstallments = pendingInstallments.length;

                if (pendingInstallments && pendingInstallments != null && pendingInstallments.length > 0) {
                    //Due Pending

                    const overDuePendingInstallments = pendingInstallments.filter(x => (
                        (loanInfo.repaymentFrequency.toLowerCase() == "monthly" && (new DateDiff(today, x.dueDate).days() > 4))
                        ||
                        (repaymentFrequency.includes(loanInfo.repaymentFrequency.toLowerCase()) && (new DateDiff(today, x.dueDate).days() > 2))
                    ));

                    if (overDuePendingInstallments && overDuePendingInstallments != null && overDuePendingInstallments.length > 0) {
                        output.overDuePendingInstallments = overDuePendingInstallments.length;
                        output.overDuePendingInstallmentAmount = overDuePendingInstallments.filter((x) => x.interestOutstanding > 0).reduce(function (prev, cur) {
                            return prev + parseFloat(cur.interestOutstanding);
                        }, 0);
                    }
                }

                //Received
                const receivedInstallments = installments.data.filter(x => x.status == "Received");
                output.receivedInstallments = receivedInstallments.length;

                //Penalty
                const fees = await getLoanFees(loanId);

                if (fees.success && fees.data && fees.data != null && fees.data.length > 0) {
                    output.totalPenaltyInstallments = fees.data.length;

                    output.totalPenalty = fees.data.filter((x) => x.feeAmount > 0 && x.isWaiveOff == false).reduce(function (prev, cur) {
                        return prev + parseFloat(cur.feeAmount);
                    }, 0);

                    if (isRequireFees) output.fees = fees.data;
                }

                if (isRequireAllInstallments) output.installments = installments.data;

                output.success = true;
                output.message = "Fetched Successfully";
                output.status = 200;
            } else output.message = "No Installments Found";
        } else output.message = installments.message;

    } catch (ex) {
        saveErrorLog("getInstallmentDetails", apiPath, { loanId, isRequireAllInstallments, isRequireFees }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};

module.exports.convertEmiDetailsFormat = async function (loanId, installments = null) {
    let output = {
        data: null,
        success: false,
        message: "Some Error Occurred",
        status: 200,
    };
    try {
        let emis = [];
        const fees = await getLoanFees(loanId);

        if (installments == null) {
            const _installments = await getInstallments(loanId);
            if (_installments.success == false) {
                output = {
                    data: null,
                    success: false,
                    message: _installments.message,
                    status: 200,
                };
            } else installments = _installments.data;
        }

        if (installments && installments != null && installments.length > 0) {
            for (const installment of installments) {
                let penalty = 0;
                if (fees && fees != null && fees.data && fees.data != null && fees.data.length > 0) {
                    penalty = fees.data.filter((x) => x.installmentNumber == installment.installmentNumber && x.isWaiveOff == false).reduce(function (prev, cur) {
                        return prev + parseFloat(cur.feeAmount);
                    }, 0);
                }
                const emi = {
                    "emiNumber": installment.installmentNumber,
                    "interestRecovered": installment.installmentInterest,
                    "interestAmount": installment.installmentInterest,
                    "principalRecovered": installment.installmentPrincipal,
                    "principalOutstanding": installment.initialPrincipalOutstanding,
                    "penalty": penalty,
                    "moratoriumInterest": installment.moratoriumInterest,
                    "rePaymentAmount": installment.installmentAmount,
                    "rePaymentDate": installment.dueDate,
                    "rePaymentActualDate": installment.actualDueDate,
                    "rePaymentStatus": installment.status,
                    "rePaymentReceivedDate": installment.status == "Received" ? installment.transactionDate : null,
                    "insertedOn": installment.insertedOn,
                    "updatedOn": installment.updatedOn,
                    "holdCollection": installment.holdCollectionViaWallet
                }
                emis.push(emi);
            }
        }
        output = {
            data: emis,
            success: true,
            message: "Fetched Successfully",
            status: 200,
        };
    } catch (ex) {
        saveErrorLog("convertEmiDetailsFormat", apiPath, { loanId, installments }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }

    return output;
};

module.exports.convertMultipleEmiDetailsFormat = async function (loanIds, multipleInstallments) {
    let output = {
        data: null,
        success: false,
        message: "Some Error Occurred",
        status: 200,
    };
    try {
        let emis = [];
        const fees = await getMultipleLoanFees(loanIds);
        if (multipleInstallments && multipleInstallments != null && multipleInstallments.length > 0) {
            for (const loanId of loanIds) {
                const installments = multipleInstallments.filter(x => x.loanId.toString() == loanId.toString());
                for (const installment of installments) {
                    let penalty = 0;
                    if (fees && fees != null && fees.data && fees.data != null && fees.data.length > 0) {
                        penalty = fees.data.filter((x) => x.loanId.toString() == loanId.toString() && x.installmentNumber == installment.installmentNumber && x.isWaiveOff == false).reduce(function (prev, cur) {
                            return prev + parseFloat(cur.feeAmount);
                        }, 0);
                    }
                    const emi = {
                        "loanId": loanId,
                        "emiNumber": installment.installmentNumber,
                        "interestRecovered": installment.installmentInterest,
                        "interestAmount": installment.installmentInterest,
                        "principalRecovered": installment.installmentPrincipal,
                        "principalOutstanding": installment.initialPrincipalOutstanding,
                        "penalty": penalty,
                        "moratoriumInterest": installment.moratoriumInterest,
                        "rePaymentAmount": installment.installmentAmount,
                        "rePaymentDate": installment.dueDate,
                        "rePaymentActualDate": installment.actualDueDate,
                        "rePaymentStatus": installment.status == "Received" ? installment.status : "Pending",
                        "rePaymentReceivedDate": installment.status == "Received" ? installment.transactionDate : null,
                        "insertedOn": installment.insertedOn,
                        "updatedOn": installment.updatedOn
                    }
                    emis.push(emi);
                }
            }
        }
        output = {
            data: emis,
            success: true,
            message: "Fetched Successfully",
            status: 200,
        };
    } catch (ex) {
        saveErrorLog("convertEmiDetailsFormat", apiPath, { loanIds, multipleInstallments }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }

    return output;
};

module.exports.getDelyedPaymentsDaySlabWise = async function (loanId = null, loanNo = null) {
    let output = {
        delay30Days: 0,
        delay60Days: 0,
        delay90Days: 0,
        delayGT90Days: 0,
        success: false,
        message: "Some Error Occurred",
        status: 200,
    };
    try {
        if (loanId == null && loanNo == null) {
            output.message = "loanId or loanNo is mandatory";
            return output;
        }
        let installments = null;

        const allInstallments = await getInstallments(loanNo, null);
        if (allInstallments.success) {
            installments = allInstallments.data;
        } else {
            output.message = allInstallments.message;
            return output;
        }

        if (installments != null && installments.length > 0) {
            const pendingInstallments = installments.filter((x) => x.status == "Pending" || x.status == "Partial");
            if (pendingInstallments && pendingInstallments != null && pendingInstallments.length > 0) {
                const today = new Date();
                const day30 = dateAdd("day", -30, today);
                const day31 = dateAdd("day", -31, today);
                const day60 = dateAdd("day", -60, today);
                const day61 = dateAdd("day", -61, today);
                const day90 = dateAdd("day", -90, today);

                output.delay30Days = pendingInstallments.filter((x) => x.dueDate >= day30 && x.dueDate <= today).reduce(function (prev, cur) {
                    return prev + cur.principalOutstanding + cur.interestOutstanding;
                }, 0);

                output.delay60Days = pendingInstallments.filter((x) => x.dueDate >= day60 && x.dueDate <= day31).reduce(function (prev, cur) {
                    return prev + cur.principalOutstanding + cur.interestOutstanding;
                }, 0);

                output.delay90Days = pendingInstallments.filter((x) => x.dueDate >= day90 && x.dueDate <= day61).reduce(function (prev, cur) {
                    return prev + cur.principalOutstanding + cur.interestOutstanding;
                }, 0);

                output.delayGT90Days = pendingInstallments.filter((x) => x.dueDate < day90).reduce(function (prev, cur) {
                    return prev + cur.principalOutstanding + cur.interestOutstanding;
                }, 0);

                output.delay30Days = output.delay30Days > 0 ? round10(delay30Days, -2) : output.delay30Days;
                output.delay60Days = output.delay60Days > 0 ? round10(delay60Days, -2) : output.delay60Days;
                output.delay90Days = output.delay90Days > 0 ? round10(delay90Days, -2) : output.delay90Days;
                output.delayGT90Days = output.delayGT90Days > 0 ? round10(delayGT90Days, -2) : output.delayGT90Days;
            }
            output.success = true;
            output.message = "Calculated Successfully";
        } else output.message = "No Installments Found";
    } catch (ex) {
        saveErrorLog("getDelyedPaymentsDaySlabWise", apiPath, { loanId, loanNo }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};

module.exports.getPeriodicIntrestRecovered = async function (loanId, month = null, year = null) {
    //Not done for partial as changes in Payment details pending for enach.
    let output = {
        interestRecovered: 0,
        success: false,
        message: "Some Error Occurred",
        status: 200,
    };
    try {
        const installments = await getInstallments(loanId);
        if (installments.success) {
            const receivedInstallments = installments.data.filter((x) => x.status == "Received");
            let interestRecovered = 0;
            if (receivedInstallments && receivedInstallments != null && receivedInstallments.length > 0) {
                for (const installment of receivedInstallments) {

                    const receivedPayment = installment.paymentDetails.filter((x) => x.interestReceived > 0 && (month == null || (month != null && new Date(x.transactionDate).getMonth() == month - 1)) && (year == null || (year != null && new Date(x.transactionDate).getFullYear() == year))).reduce(function (prev, cur) {
                        return prev + parseFloat(cur.interestReceived);
                    }, 0);
                    interestRecovered += receivedPayment;
                }
            }

            output = {
                interestRecovered,
                success: true,
                message: "Caluulated Successfully",
                status: 200,
            };
        } else output.message = installments.message;
    } catch (ex) {
        saveErrorLog("getPeriodicIntrestRecovered", apiPath, { loanId, month, year }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};

module.exports.getPeriodicInstallmentsRecovered = async function (loanIds = null, loanNos = null, fromDate = null, toDate = null, requireLoanFees = false, writtenOff = true, closePrepaid = true, closeRepaid = true, checkExcludeList = true, writtenOffPaid = true, requireInstallments = true, excludeChannels = null) {
    //Not done for partial as changes in Payment details pending for enach.
    let output = {
        data: null,
        fees: null,
        success: false,
        message: "Some Error Occurred",
        status: 200,
    };
    try {

        let excludeList = [];
        if (!writtenOff) excludeList.push("Written-Off");
        if (!closePrepaid) excludeList.push("Close Prepaid");
        if (!closeRepaid) excludeList.push("Close Repaid");
        if (!writtenOffPaid) excludeList.push("WrittenOff Paid");


        let installmentFilter = { status: { $in: ["Received"] } };
        let feesFilter = { status: { $in: ["Received"] } };
        //logger.logs("excludeList", excludeList);
        let query = { status: { $nin: excludeList }, "disbursalDetails.disbursedOn": { $ne: null } };

        if (fromDate != null && toDate != null) {
            installmentFilter.transactionDate = { $gte: fromDate, $lt: toDate };
            feesFilter.feeCollectedDate = { $gte: fromDate, $lt: toDate };
        } else if (fromDate != null) {
            installmentFilter.transactionDate = { $gte: fromDate };
            feesFilter.feeCollectedDate = { $gte: fromDate };
        } else if (toDate != null) {
            installmentFilter.transactionDate = { $lt: toDate };
            feesFilter.feeCollectedDate = { $lt: toDate };
        }


        if (loanNos && loanNos != null) {
            installmentFilter.loanNo = { $in: loanNos };
            feesFilter.loanNo = { $in: loanNos };
            query.loanNo = { $in: loanNos };
        } else if (loanIds && loanIds != null) {
            installmentFilter.loanId = { $in: loanIds };
            feesFilter.loanId = { $in: loanIds };
            query.loanId = { $in: loanIds };
        }
        //logger.logs("checkExcludeList", checkExcludeList);

        if (checkExcludeList && excludeList.length > 0) {
            const loans = await LoanInfo.find(query).select({ _id: 1, loanNo: 1 });

            const uniqueLoanNos = [...new Set(loans.map(item => item.loanNo))];

            installmentFilter.loanNo = { $in: uniqueLoanNos };
            feesFilter.loanNo = { $in: uniqueLoanNos };
        }

        if (excludeChannels != null) {
            installmentFilter.channel = { $nin: excludeChannels };
            feesFilter.channel = { $nin: excludeChannels };
        }

        //logger.logs("installmentFilter", installmentFilter);
        installmentFilter = {loanNo: "105914", status: "Received", updatedOn: {$gte: new Date("2024-06-29")}}
        feesFilter = {loanNo: "105914", status: "Received", updatedOn: {$gte: new Date("2024-06-29")}}
        if (requireInstallments) output.data = await LoanInstallments.find(installmentFilter);

        if (requireLoanFees) output.fees = await LoanFees.find(feesFilter);

        output.success = true;
        output.message = "Fetched Successfully";
        output.status = 200;
    } catch (ex) {
        logger.logs(ex);
        saveErrorLog("getPeriodicInstallmentsRecovered", apiPath, { loanIds, loanNos, fromDate, toDate, requireLoanFees, writtenOff, closePrepaid, closeRepaid }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};

module.exports.applyLateFee = async function (partnerId, _productType = null, _loanId = null, _applyForHold = false, _loanNo = null, _loanStatus = null) {
    let output = {
        success: false,
        message: "Some Error Occurred",
        status: 200,
    };
    try {
        const today = new Date();
        const todayStartDate = new Date()
        todayStartDate.setHours(0, 0, 0, 0, 0, 0,)
        const model = { partnerId, _productType, _loanId, _applyForHold };

        if (!IsNullEmpty(partnerId)) {
            let query = {
                partnerId: mongoose.Types.ObjectId(partnerId),
                status: { $in: ["Loan In Progress", "Written-Off"] }
            };

            if (_loanStatus != null) query.status = _loanStatus;

            if (_productType && _productType != null) query.productType = _productType;

            if (_loanId) query._id = mongoose.Types.ObjectId(_loanId);

            if (_loanNo) query.loanNo = _loanNo;

            const limit = 250;
            const totalRecords = await LoanInfo.countDocuments(query);
            const totalPages = totalRecords / limit;
            let skip = 0;

            for (let page = 0; page < (totalPages + 1); page++) {
                skip = page * limit;

                const loans = await LoanInfo.find(query).select({ _id: 1, loanNo: 1, productType: 1, partnerId: 1, repaymentFrequency: 1, loanFeeType: 1, usedGraceDays: 1 }).sort({ loanNo: 1 }).skip(skip).limit(limit);

                let uniqueProducts = [];
                let uniquePartnerId = [];
                let uniqueLoans = [];

                for (let i = 0; i < loans.length; i++) {
                    uniqueProducts.push(loans[i].productType.split("_")[0]);
                    uniquePartnerId.push(loans[i].partnerId);
                    uniqueLoans.push(loans[i]._id);
                }

                uniqueProducts = [...new Set(uniqueProducts)];
                uniquePartnerId = [...new Set(uniquePartnerId)];
                uniqueLoans = [...new Set(uniqueLoans)];

                const products = await ProductMaster.find({ product: { $in: uniqueProducts } });

                const productHash = {};

                for (let i = 0; i < products.length; i++) {
                    productHash[products[i].product] = products[i];
                }

                const partnerProductConfig = await ProductPartnerConfig.find({ partnerId: mongoose.Types.ObjectId(partnerId) });

                const installments = await getMultipleLoansInstallments(uniqueLoans, null, ["Pending"], null);

                if (!installments.success) {
                    output.message = installments.message;
                    return output;
                }

                let bulkWriteQuery = []

                for (const itemLoanInfo of loans) {
                    let penaltyGraceDays = 4;

                    const productType = itemLoanInfo.productType.split("_")[0];

                    const productMaster = productHash[productType];

                    if (productMaster) {
                        if (itemLoanInfo.loanFeeType === "VERSION2") {
                            if (productMaster.penaltyGraceDays && productMaster.penaltyGraceDays.version2 >= 0) penaltyGraceDays = productMaster.penaltyGraceDays.version2
                        }
                        else {
                            const _partnerProductConfig = partnerProductConfig.filter((x) => x.productId.toString() == productMaster._id.toString());

                            if (_partnerProductConfig && _partnerProductConfig.length > 0 && _partnerProductConfig[0].penaltyGraceDays) {
                                const frquencyDays = _partnerProductConfig[0].penaltyGraceDays.filter((x) => x.frequency.toLowerCase() == itemLoanInfo.repaymentFrequency.toLowerCase());

                                if (frquencyDays && frquencyDays.length > 0) {
                                    penaltyGraceDays = frquencyDays[0].days;
                                } else {
                                    output.message = `No penaltyGraceDays in ProductPartnerConfig for Product ${productType} and partner Id ${partnerId} and frequency ${itemLoanInfo.repaymentFrequency.toLowerCase()}`;

                                    saveErrorLog("applyLateFee", apiPath, model, output.message);

                                    return output;
                                }
                            } else {
                                output.message = `No record found in ProductPartnerConfig for Product ${productType} and partner Id ${partnerId}`;

                                saveErrorLog("applyLateFee", apiPath, model, output.message);

                                return output;
                            }
                        }
                    } else {
                        output.message = `No record found in ProductMaster for Product ${productType}`;
                        saveErrorLog("applyLateFee", apiPath, model, output.message);
                        return output;
                    }

                    // let loanInstallments = [];
                    // for (let i = 0; i < installments.data.length; i++) {
                    //     if (installments.data[i].loanNo == itemLoanInfo.loanNo && installments.data[i].dueDate <= today && installments.data[i].holdCollectionViaWallet == _applyForHold) {
                    //         const diff = new DateDiff(today, installments.data[i].dueDate).days();

                    //         if (diff > penaltyGraceDays) {
                    //             loanInstallments.push(installments.data[i]);
                    //         }
                    //     }
                    // }

                    let loanInstallments = []
                    if (itemLoanInfo.loanFeeType === "VERSION2") {

                        let loanInfoGraceDays = itemLoanInfo.usedGraceDays || 0

                        if (loanInfoGraceDays < penaltyGraceDays) {

                            const gracesDayForLoan = await getUsedGraceDaysForLoan(itemLoanInfo.loanNo)

                            let totalGraceDaysUsed = 0

                            if (gracesDayForLoan && gracesDayForLoan.success && gracesDayForLoan.data[0]) {
                                if (gracesDayForLoan.data && gracesDayForLoan.data[0].usedGraceDays) {
                                    totalGraceDaysUsed = gracesDayForLoan.data[0].usedGraceDays
                                }
                            }

                            bulkWriteQuery.push({
                                updateOne: {
                                    filter: {
                                        _id: itemLoanInfo._id
                                    },
                                    update: {
                                        $set: {
                                            usedGraceDays: totalGraceDaysUsed
                                        }
                                    }
                                }
                            })

                            if (totalGraceDaysUsed <= penaltyGraceDays) {
                                continue;
                            }
                        }

                        loanInstallments = installments.data.filter((x) => x.loanNo == itemLoanInfo.loanNo && x.dueDate < todayStartDate && (_applyForHold == true || (_applyForHold == false && x.holdCollectionViaWallet == _applyForHold)));

                    } else {
                        loanInstallments = installments.data.filter((x) => x.loanNo == itemLoanInfo.loanNo && x.dueDate <= today && (_applyForHold == true || (_applyForHold == false && x.holdCollectionViaWallet == _applyForHold)) && ((new DateDiff(today, x.dueDate).days()) > penaltyGraceDays));
                    }



                    if (loanInstallments && loanInstallments != null && loanInstallments.length > 0) {
                        const loanFees = await getLoanFees(itemLoanInfo._id);

                        if (!loanFees.success) {
                            output.message = loanFees.message;
                            return output;
                        }

                        for (let i = 0; i < loanInstallments.length; i++) {
                            let loanFeeDetail = null;

                            if (loanFees.data != null && loanFees.data.length > 0) {
                                loanFeeDetail = loanFees.data.find((x) => x.installmentNumber == loanInstallments[i].installmentNumber && x.feeType == "Late Fee");
                            }


                            let feeAmount = productMaster.penaltyCharges,
                                numberOfDays = 0;

                            if (itemLoanInfo.loanFeeType === "VERSION2") {
                                let installmentAmount = loanInstallments[i].installmentAmount
                                let feePercentage = productMaster.penaltyChargePercentage || 0.1
                                let amount = (feePercentage / 100) * installmentAmount
                                numberOfDays = Math.round(new DateDiff(todayStartDate, loanInstallments[i].dueDate).days());
                                feeAmount = round10(amount * numberOfDays, -2)
                            } else {
                                let installmentAmount = loanInstallments[i].installmentAmount;
                                let feePercentage = productMaster.penaltyChargePercentage || 0.1;
                                let amount = (feePercentage / 100) * installmentAmount;
                                numberOfDays = Math.round(new DateDiff(todayStartDate, loanInstallments[i].dueDate).days());

                                feeAmount = round10(amount * numberOfDays, -2);
                                feeAmount = feeAmount > productMaster.penaltyCharges ? productMaster.penaltyCharges : feeAmount;
                            }


                            if (!loanFeeDetail) {

                                // if (productType == "LC") {
                                //     const diff = Math.round(new DateDiff(today, loanInstallments[i].dueDate).days());
                                //     feeAmount = productMaster.penaltyCharges * diff;
                                // }

                                const loanFee = new LoanFees({
                                    loanInstallmentId: mongoose.Types.ObjectId(loanInstallments[i]._id),
                                    loanId: mongoose.Types.ObjectId(loanInstallments[i].loanId),
                                    loanNo: loanInstallments[i].loanNo,
                                    installmentNumber: loanInstallments[i].installmentNumber,
                                    channel: loanInstallments[i].channel,
                                    feeType: "Late Fee",
                                    feeAmount: feeAmount,
                                    status: "Pending",
                                    feeAmountReceived: 0,
                                    feeAmountOutstanding: feeAmount,
                                    feeDueDate: loanInstallments[i].dueDate,
                                    lastFeeRepaymentRequestedDate: null,
                                    feeCollectedDate: null,
                                    isWaiveOff: false,
                                    insertedOn: Date.now(),
                                    updatedOn: null,
                                    updatedBy: null,
                                    numberOfDays
                                });

                                await loanFee.save();
                            } else {
                                let feeAmountOutstanding = feeAmount - (loanFeeDetail.feeAmountReceived || 0);
                                let result = await LoanFees.updateOne({ _id: loanFeeDetail._id }, { $set: { feeAmount, feeAmountOutstanding: round10(feeAmountOutstanding, -2), numberOfDays, updatedOn: Date.now() } });
                            }
                        }
                    }
                }

                if (bulkWriteQuery.length) {
                    await LoanInfo.bulkWrite(bulkWriteQuery)
                }
            }

            output.success = true;
            output.message = "Penalty Applied Successfully";
            output.status = 200;
        } else output.message = "PartnerId Is Mandatory";
    } catch (ex) {
        saveErrorLog("applyLateFee", apiPath, { partnerId, _productType, _loanId, _applyForHold }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }

    return output;
};

const getUsedGraceDaysForLoan = async (loanNo) => {

    let consumedGraceDaysQuery = [
        {
            '$match': {
                'loanNo': loanNo,
                'dueDate': {
                    '$lte': new Date()
                }
            }
        }, {
            '$addFields': {
                'usedGraceDays': {
                    '$dateDiff': {
                        'startDate': '$dueDate',
                        'endDate': {
                            '$cond': [
                                {
                                    '$ne': [
                                        '$transactionDate', null
                                    ]
                                }, '$transactionDate', new Date()
                            ]
                        },
                        'unit': 'day',
                        'timezone': '+05:30'
                    }
                }
            }
        }, {
            '$group': {
                '_id': '$loanNo',
                'usedGraceDays': {
                    '$sum': {
                        '$cond': [
                            {
                                '$gt': [
                                    '$usedGraceDays', 0
                                ]
                            }, '$usedGraceDays', 0
                        ]
                    }
                }
            }
        }
    ]

    let gracesDayForLoan = await aggregateInstallment(consumedGraceDaysQuery)
    return gracesDayForLoan

}

module.exports.updateEmiPaidStatus = async function (loanId, installmentNumber, repayAmount, paidOn, channel = "", updatedBy = null, referenceNo = null) {
    let output = {
        updatedOn: null,
        success: false,
        message: "Some Error Occurred",
        status: 200
    };
    try {
        // console.log("loanId", loanId);
        // console.log("installmentNumber", installmentNumber);
        logger.logs("channel", channel);
        logger.logs("updatedBy", updatedBy);

        const loanInfo = await LoanInfo.findById(loanId).select({ loanNo: 1, status: 1, productType: 1, lenderDetails: 1, userId: 1, disbursalDetails: 1, lastEmiPaidDate: 1, totalOutstanding: 1 });

        if (!IsNullEmpty(loanInfo)) {
            const userInfo = await UserInfo.findById(loanInfo.userId).select({ userDetails: 1 });
            const filter = { loanId: mongoose.Types.ObjectId(loanId), installmentNumber: installmentNumber };
            const loanFeeFilter = { loanId: mongoose.Types.ObjectId(loanId), installmentNumber: installmentNumber, isWaiveOff: false };

            const loanInstallment = await LoanInstallments.findOne(filter);
            const loanFee = await LoanFees.findOne(loanFeeFilter);

            if (loanInstallment.status == "Received" && (loanFee == null || (loanFee != null && loanFee.status == "Received"))) {
                output.updatedOn = loanInstallment.transactionDate;
                output.success = true;
                output.message = `Emi Not Found In Pending State For loanNo ${loanInfo.loanNo} and emiNumber ${installmentNumber}`;
                return output;
            } else {
                let totalPrincipalReceived = 0;
                let totalInterestReceived = 0;
                let totalMoratoriumInterestReceived = 0;
                let totalPenaltyReceived = 0;

                const fundfinaPolicy = await FundfinaPolicy.findOne({ policyName: "Repayment Collection" });

                if (fundfinaPolicy != null && fundfinaPolicy.loanAppropriationSequence != null && fundfinaPolicy.loanAppropriationSequence.length > 0) {
                    let pendingAmount = repayAmount;
                    let principalReceived = loanInstallment.principalReceived;
                    let interestReceived = loanInstallment.interestReceived;
                    let moratoriumInterestReceived = loanInstallment.moratoriumInterestReceived;
                    let principalOutstanding = loanInstallment.principalOutstanding;
                    let interestOutstanding = loanInstallment.interestOutstanding;
                    let moratoriumInterestOutstanding = loanInstallment.moratoriumInterestOutstanding;
                    let installmentStatus = loanInstallment.status;

                    let feeAmountReceived = null;
                    let feeAmountOutstanding = null;
                    let feeStatus = null;
                    let isInstallmentReceived = false;
                    let isFeeReceived = false;

                    if (loanFee != null) {
                        feeAmountReceived = loanFee.feeAmountReceived;
                        feeAmountOutstanding = loanFee.feeAmountOutstanding;
                        feeStatus = loanFee.status;
                    } else feeStatus = "Received";

                    for (let i = 0; i < fundfinaPolicy.loanAppropriationSequence.length; i++) {
                        logger.logs("fundfinaPolicy.loanAppropriationSequence[i]", fundfinaPolicy.loanAppropriationSequence[i]);

                        if (pendingAmount > 0) {
                            if (fundfinaPolicy.loanAppropriationSequence[i] == "interest" && interestOutstanding > 0) {
                                logger.logs("pendingAmount", pendingAmount);
                                const deductedAmount = (pendingAmount >= interestOutstanding) ? interestOutstanding : pendingAmount;
                                interestOutstanding = round10(interestOutstanding - deductedAmount, -2);
                                interestReceived += deductedAmount;
                                pendingAmount = round10(pendingAmount - deductedAmount, -2);
                                totalInterestReceived += deductedAmount;
                                isInstallmentReceived = true;
                            } else if (fundfinaPolicy.loanAppropriationSequence[i] == "principal" && principalOutstanding > 0) {
                                logger.logs("pendingAmount", pendingAmount);
                                const deductedAmount = (pendingAmount >= principalOutstanding) ? principalOutstanding : pendingAmount;
                                principalOutstanding = round10(principalOutstanding - deductedAmount, -2);
                                principalReceived += deductedAmount;
                                pendingAmount = round10(pendingAmount - deductedAmount, -2);
                                totalPrincipalReceived += deductedAmount;
                                isInstallmentReceived = true;
                            } else if (fundfinaPolicy.loanAppropriationSequence[i] == "moratoriumInterest" && moratoriumInterestOutstanding > 0) {
                                const deductedAmount = (pendingAmount >= moratoriumInterestOutstanding) ? moratoriumInterestOutstanding : pendingAmount;
                                moratoriumInterestOutstanding = round10(moratoriumInterestOutstanding - deductedAmount, -2);
                                moratoriumInterestReceived += deductedAmount;
                                pendingAmount = round10(pendingAmount - deductedAmount, -2);
                                totalMoratoriumInterestReceived += deductedAmount;
                                isInstallmentReceived = true;
                            } else if (fundfinaPolicy.loanAppropriationSequence[i] == "penalty" && loanFee != null) {
                                if (feeAmountOutstanding > 0) {
                                    const deductedAmount = (pendingAmount >= feeAmountOutstanding) ? feeAmountOutstanding : pendingAmount;
                                    feeAmountOutstanding = round10(feeAmountOutstanding - deductedAmount, -2);
                                    feeAmountReceived += deductedAmount;
                                    pendingAmount = round10(pendingAmount - deductedAmount, -2);
                                    totalPenaltyReceived += deductedAmount;
                                    if (feeAmountOutstanding == 0) feeStatus = "Received";
                                    isFeeReceived = true;
                                }
                            }
                        } else i = fundfinaPolicy.loanAppropriationSequence.length;
                    }

                    if (isInstallmentReceived == true) {
                        if (principalOutstanding == 0 && interestOutstanding == 0 && moratoriumInterestOutstanding == 0)
                            installmentStatus = "Received";
                        else {
                            installmentStatus = "Received";
                            //installmentStatus = "Partial"; //Discuss with Bikram
                        }

                        principalReceived = principalReceived > 0 ? round10(principalReceived, -2) : principalReceived;
                        interestReceived = interestReceived > 0 ? round10(interestReceived, -2) : interestReceived;
                        moratoriumInterestReceived = moratoriumInterestReceived > 0 ? round10(moratoriumInterestReceived, -2) : moratoriumInterestReceived;
                        principalOutstanding = principalOutstanding > 0 ? round10(principalOutstanding, -2) : principalOutstanding;
                        interestOutstanding = interestOutstanding > 0 ? round10(interestOutstanding, -2) : interestOutstanding;
                        moratoriumInterestOutstanding = moratoriumInterestOutstanding > 0 ? round10(moratoriumInterestOutstanding, -2) : moratoriumInterestOutstanding;

                        // logger.logs("principalReceived", principalReceived);
                        // logger.logs("interestReceived", interestReceived);
                        // logger.logs("moratoriumInterestReceived", moratoriumInterestReceived);
                        // logger.logs("principalOutstanding", principalOutstanding);
                        // logger.logs("interestOutstanding", interestOutstanding);
                        // logger.logs("moratoriumInterestOutstanding", moratoriumInterestOutstanding);
                        let updatequery = { status: installmentStatus, principalReceived: principalReceived, interestReceived: interestReceived, moratoriumInterestReceived: moratoriumInterestReceived, principalOutstanding: principalOutstanding, interestOutstanding: interestOutstanding, moratoriumInterestOutstanding: moratoriumInterestOutstanding, transactionDate: paidOn, updatedOn: Date.now(), channel: channel, updatedBy: updatedBy, referenceNo: referenceNo };

                        if (loanInfo.status == "Written-Off") {
                            updatequery.isRecovered = true;
                            updatequery.recoveryDate = Date.now();
                        }

                        await LoanInstallments.findOneAndUpdate({ _id: loanInstallment._id, installmentNumber: installmentNumber, status: { $in: ["Pending"] } }, { $set: updatequery }, { new: true, useFindAndModify: false });
                    }

                    logger.logs("isFeeReceived", isFeeReceived);

                    if (isFeeReceived) {
                        await LoanFees.findOneAndUpdate({ _id: loanFee._id, installmentNumber: installmentNumber, status: { $in: ["Pending"] } }, { $set: { feeAmountReceived: feeAmountReceived, feeAmountOutstanding: feeAmountOutstanding, status: feeStatus, channel: channel, feeCollectedDate: paidOn, updatedOn: Date.now(), updatedBy: updatedBy, referenceNo: referenceNo } }, { new: true, useFindAndModify: false });
                    }

                    const pendingInstallments = await LoanInstallments.countDocuments({ loanId: mongoose.Types.ObjectId(loanId), status: "Pending" });

                    const pendingFees = await LoanFees.countDocuments({ loanId: mongoose.Types.ObjectId(loanId), status: "Pending" }).lean();

                    let isAllInstallmentsPaid = false;



                    //Update Loan Status if all installments are paid
                    if (pendingInstallments == 0 && pendingFees == 0) {
                        let isPrincipalPaid = true;

                        if (loanInfo.productType == "DL_30") {
                            const lastInstallment = await LoanInstallments.findOne({ loanId: mongoose.Types.ObjectId(loanId) }).sort({ installmentNumber: -1 });

                            isPrincipalPaid = lastInstallment.isPrincipalPaid;
                        }

                        if (isPrincipalPaid) {
                            isAllInstallmentsPaid = true;
                            let loanStatus = "Close Repaid";

                            if (loanInfo.status == "Loan In Progress") {
                                const installments = await LoanInstallments.find({ loanId: mongoose.Types.ObjectId(loanId) });
                                const loanFees = await LoanFees.find({ loanId: mongoose.Types.ObjectId(loanId) });

                                const lastInstallmentDueDate = new Date(installments[installments.length - 1].dueDate);

                                const latePaidInstallments = installments.filter((x) => x.transactionDate != null && new Date(x.transactionDate) > lastInstallmentDueDate).length;

                                const latePaidFees = loanFees.filter((x) => x.feeCollectedDate != null && new Date(x.feeCollectedDate) > lastInstallmentDueDate).length;

                                if (latePaidInstallments == 0 && latePaidFees == 0) loanStatus = "Close Prepaid";
                            } else loanStatus = "WrittenOff Paid";

                            const updateResponse = await LoanInfo.findOneAndUpdate({ _id: mongoose.Types.ObjectId(loanId), status: { $in: ["Loan In Progress", "Written-Off"] } }, {
                                $set: { status: loanStatus, updatedOn: Date.now(), adminModifiedOn: Date.now(), postDisbursalStatus: null }, $push: {
                                    flowDetails: {
                                        Step: loanStatus,
                                        changedByRole: "System",
                                        insertedOn: Date.now()
                                    }
                                }
                            }, { new: true, useFindAndModify: false });
                        }
                    }

                    const responseUpdateLedger = await UpdateLedger(loanId, installmentNumber, repayAmount, paidOn, channel = "", totalPrincipalReceived, totalInterestReceived, totalMoratoriumInterestReceived, totalPenaltyReceived, installmentStatus, feeStatus, loanInstallment._id.toString(), isAllInstallmentsPaid);

                    //logger.logs("responseUpdateLedger", responseUpdateLedger);


                    const installments = await LoanInstallments.find({ loanId: mongoose.Types.ObjectId(loanId) }).sort({ installmentNumber: 1 });

                    let principalCollected = 0;
                    let interestCollected = 0;
                    principalOutstanding = 0;
                    interestOutstanding = 0;
                    let lastRecoveryDate = loanInfo.status == "Written-Off" ? Date.now() : null;
                    let totalRecoveredAmount = 0;
                    let totalEmisCollected = 0;

                    for (let i = 0; i < installments.length; i++) {
                        principalCollected += installments[i].principalReceived;
                        interestCollected += installments[i].interestReceived;
                        principalOutstanding += installments[i].principalOutstanding;
                        interestOutstanding += installments[i].interestOutstanding;

                        if (installments[i].isRecovered == true) {
                            totalRecoveredAmount += installments[i].installmentAmount;
                        }

                        if (installments[i].status == "Received") {
                            totalEmisCollected += 1;
                        }
                    }



                    await LoanInfo.updateOne({ _id: mongoose.Types.ObjectId(loanId) },
                        {
                            $set: { lastEmiPaidDate: paidOn, "principalAmount.collected": principalCollected.toFixed(2), "principalAmount.outstanding": principalOutstanding.toFixed(2), "interestAmount.collected": interestCollected.toFixed(2), "interestAmount.outstanding": interestOutstanding.toFixed(2), totalEmisCollected: totalEmisCollected, lastRecoveryDate: lastRecoveryDate, totalRecoveredAmount: totalRecoveredAmount }
                        }
                    );

                    output = {
                        success: true,
                        message: "Updated Successfully",
                        status: 200,
                    };

                    await getEmiConsolidatedInformation(null, null, [mongoose.Types.ObjectId(loanId)]);

                    const updatedLoan = await LoanInfo.findById(loanId).select({ loanNo: 1, status: 1, productType: 1, lenderDetails: 1, userId: 1, disbursalDetails: 1, lastEmiPaidDate: 1, totalOutstanding: 1 });

                    //Send Mail to Lending Partner for NOC
                    if (updatedLoan.status === "Close Repaid" || updatedLoan.status === "Close Prepaid" || updatedLoan.status === "WrittenOff Paid") {
                        let mailModel = getMailModel();
                        const lendingPartner = await PartnerMaster.findById(updatedLoan.lenderDetails[0].lenderPartnerId);
                        mailModel.to = lendingPartner.emails && lendingPartner.emails.nocEmailsTo ? lendingPartner.emails.nocEmailsTo : "";
                        mailModel.cc = lendingPartner.emails && lendingPartner.emails.nocEmailsCc ? lendingPartner.emails.nocEmailsCc : "";
                        mailModel.from = config.get("mailConfig.fromNoc");

                        if (mailModel.to) {
                            mailModel.number = updatedLoan.loanNo;
                            mailModel.name = `${userInfo.userDetails.firstName} ${userInfo.userDetails.lastName}`;
                            mailModel.email = `${userInfo.userDetails.emailId[0]}`;
                            mailModel.number1 = updatedLoan.lenderDetails[0].lenderLoanId ? updatedLoan.lenderDetails[0].lenderLoanId : "-";
                            mailModel.number2 = updatedLoan.lenderDetails[0].lenderCustId ? updatedLoan.lenderDetails[0].lenderCustId : "-";
                            mailModel.date = updatedLoan.disbursalDetails.disbursedOn ? getDMYFormattedDate(updatedLoan.disbursalDetails.disbursedOn, "/") : "-";
                            mailModel.date1 = updatedLoan.lastEmiPaidDate ? getDMYFormattedDate(updatedLoan.lastEmiPaidDate, "/") : "-";
                            mailModel.amount = updatedLoan.totalOutstanding ? updatedLoan.totalOutstanding.toString() : "0";

                            //Send email
                            await sendMail(userInfo._id, loanId, "Emails to Lender", "NOC", mailModel, "Lender");
                        }
                    }

                } else output.message = `Fundfina policy not found`;
            }
        } else output.message = `loanId ${loanId} not found`;
    } catch (ex) {
        logger.logs("error :", ex);
        output.message = ex.message;
        saveErrorLog("updateEmiPaidStatus", apiPath, { loanId, installmentNumber, repayAmount, paidOn, source }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }
    return output;
};

async function UpdateLedger(loanId, installmentNumber, repayAmount, paidOn, source, totalPrincipalReceived, totalInterestReceived, totalMoratoriumInterestReceived, totalPenaltyReceived, installmentStatus, feeStatus, emiReferenceNo, isAllInstallmentsPaid) {
    let output = {
        success: false,
        message: "Some Error Occurred",
        status: 200
    };
    try {
        const loanInfo = await LoanInfo.findById(loanId).select({ _id: 1, amount: 1, loanNo: 1, lenderDetails: 1, userId: 1, disbursalDetails: 1 });

        const user = await UserInfo.findById(loanInfo.userId).select({ userDetails: 1 });

        if (loanInfo != null && user != null) {
            const name = getName(user.userDetails.firstName, user.userDetails.middleName, user.userDetails.lastName);
            let lenderDetails = [];

            for (const lender of loanInfo.lenderDetails) {
                const percentage = (lender.amount / loanInfo.amount) * 100;

                const Emi = parseFloat((repayAmount * percentage) / 100).toFixed(2);

                const lenderDetail = { lenderId: lender.lenderPartnerId, per: percentage, emi: Emi };

                lenderDetails.push(lenderDetail);
            }

            for (let i = 0; i < lenderDetails.length; i++) {
                const lendingPartner = await PartnerMaster.findById(lenderDetails[i].lenderId);

                let isAllowCallingLenderRepaymentAPI = true;

                //Check for Lendbox
                if (lendingPartner.partnerCode.toLowerCase() == "lbox") {
                    const allowBeforeDisbursalDate = new Date(2022, 4, 27, 0, 0, 1);
                    const disbursedOn = loanInfo.disbursalDetails.disbursedOn;
                    if (disbursedOn > allowBeforeDisbursalDate) isAllowCallingLenderRepaymentAPI = false;
                }

                //Calling Lending Partner API
                if (lendingPartner.isApiCall == true && isAllowCallingLenderRepaymentAPI == true && installmentStatus == "Received" && feeStatus == "Received") {
                    const lenderPartnerCode = lendingPartner.partnerCode.toLowerCase();
                    const apiVar = lenderPartnerCode + ".repaymentApis";
                    const repaymentApis = config.get(apiVar);

                    if (!IsNullEmpty(repaymentApis)) {
                        if (repaymentApis.length > 0) {
                            for (let j = 0; j < repaymentApis.length; j++) {
                                const lenderApiModel = {
                                    source: "application",
                                    module: "loanRepayment",
                                    loanId: loanId,
                                    lenderId: lendingPartner.partnerId,
                                    partnerCode: lendingPartner.partnerCode,
                                    partnerDisplayName: lendingPartner.displayName,
                                    apiType: repaymentApis[j].apiType,
                                    isRetryOffline: repaymentApis[j].isRetryOffline,
                                    referenceNo: emiReferenceNo,
                                    emiNumber: installmentNumber,
                                    url: repaymentApis[j].url
                                };

                                if (repaymentApis[j].isWaitForResponse) {
                                    const lendingApiResponse = await lendingPartnerAPI.callLendingPartnerAPI(lenderApiModel);
                                    if (!lendingApiResponse.success) {
                                        j = repaymentApis.length;
                                        output.message = lendingApiResponse.message;
                                        resolve(output);
                                        return output;
                                    } else {
                                        loan = await LoanInfo.findById(loanId);
                                    }
                                } else {
                                    await lendingPartnerAPI.callLendingPartnerAPI(lenderApiModel);
                                }
                            }
                        }
                    }
                }

                const narration = "EMI repayment from " + user.userDetails.clientCode + "-" + loanInfo.loanNo + " " + name + " - " + installmentNumber.toString() + (isAllInstallmentsPaid == true ? " - END" : "");

                const balance = lendingPartner.ledgerDetails.balance + parseFloat(lenderDetails[i].emi);

                const ledgerAccount = new LedgerAccount({
                    partnerId: mongoose.Types.ObjectId(lendingPartner._id),
                    loanId: mongoose.Types.ObjectId(loanId),
                    transactionDate: Date.now(),
                    depositDate: Date.now(),
                    utrNo: null,
                    narration: narration,
                    installmentNumber: installmentNumber,
                    type: "Emi",
                    creditAmt: lenderDetails[i].emi,
                    debitAmt: null,
                    holdAmt: null,
                    balance: balance,
                    insertedOn: Date.now(),
                });
                await ledgerAccount.save();

                await PartnerMaster.findOneAndUpdate({ _id: mongoose.Types.ObjectId(lendingPartner._id) },
                    {
                        $set: { "ledgerDetails.updatedOn": Date.now() },
                        $inc: { "ledgerDetails.balance": round10(lenderDetails[i].emi, -2) }
                    },
                    { new: true, useFindAndModify: false }
                );
            }

            output = {
                success: true,
                message: "Updated Successfully",
                status: 200,
            };
        } else output.message = (loanInfo != null) ? "User Not Found" : "Loan Not Found";
    } catch (ex) {
        logger.logs("error :", ex);
        output.message = ex.message;
        saveErrorLog("UpdateLedger", apiPath, { loanId, installmentNumber, repayAmount, paidOn, source, totalPrincipalReceived, totalInterestReceived, totalMoratoriumInterestReceived, totalPenaltyReceived, installmentStatus, feeStatus, emiReferenceNo, isAllInstallmentsPaid }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }
    return output;
};

module.exports.getInstallmentsPending = async function (loanIds = null, loanNos = null, installmentStatus = null, installmentNumber = null, isRequireAllInstallments = false, isRequireFees = false, tillDate = null, overdueBy = null, overDueFrom = null, overDueTo = null, isRequirePendingInstallments = false, isRequirePendingFees = false) {
    let output = {
        data: [],
        installments: [],
        fees: [],
        pendingInstallments: [],
        pendingFees: [],
        pendingInstallmentsTillDate: [],
        pendingFeesTillDate: [],
        success: false,
        message: "Some Error Occurred",
        status: 200,
    };
    try {
        let filter = {};
        let today = Date.now();
        if (loanIds != null || loanNos != null) {

            let responseData = [];
            let pendingInstallments = [];
            let pendingFees = [];
            let pendingInstallmentsTillDate = [];
            let pendingFeesTillDate = [];

            const allInstallments = await getMultipleLoansInstallments(loanIds, loanNos, null, installmentNumber);
            const allFees = await getMultipleLoanFees(loanIds, loanNos, null, installmentNumber);

            if (loanIds == null) {
                const loanInfo = await LoanInfo.find({ loanNo: { $in: loanNos } }).select({ _id: 1 });
                loanIds = [...new Set(loanInfo.map(item => item._id))];
            }

            if (installmentStatus == null) installmentStatus = ["Pending", "Partial"];

            if (allInstallments != null && allInstallments.success == true) {
                if (isRequireAllInstallments) output.installments = allInstallments.data;

                if (allFees != null && allFees.success == true) {
                    if (isRequireFees) output.fees = allFees.data && allFees.data.length > 0 ? allFees.data : [];

                    for (const loanId of loanIds) {
                        const record = {
                            loanId: loanId,
                            principalOutstanding: 0,
                            interestOutstanding: 0,
                            moratoriumInterestOutstanding: 0,
                            feeAmountOutstanding: 0,
                            loanOutstanding: 0,
                            totalOutstanding: 0,
                            principalOutstandingTillDate: null,
                            interestOutstandingTillDate: null,
                            moratoriumInterestOutstandingTillDate: null,
                            feeAmountOutstandingTillDate: null,
                            loanOutstandingTillDate: null,
                            totalOutstandingTillDate: null,
                            lastPaymentReceivedDate: null,
                            pendingInstallments: 0,
                            pendingFees: 0,
                            overDueByDays: 0,
                            pendingInstallmentsTillDate: 0,
                            moratoriumInterest: 0
                        };

                        let installments = [];
                        let tempInstallments = [];
                        if (overdueBy != null && overDueFrom != null && overDueTo != null) {
                            tempInstallments = allInstallments.data.filter((x) => x.loanId.toString() == loanId.toString() && installmentStatus.includes(x.status));

                            if (tempInstallments != null && tempInstallments.length > 0) {
                                for (const installment of tempInstallments) {
                                    const _overDueByDays = new DateDiff(today, installment.dueDate).days();

                                    record.overDueByDays = record.overDueByDays > _overDueByDays ? record.overDueByDays : _overDueByDays;

                                    if ((overdueBy == 'g' && _overDueByDays > overDueFrom) || (_overDueByDays > overDueFrom && _overDueByDays < overDueTo)) installments.push(installment);
                                }
                            }
                        } else {
                            installments = allInstallments.data.filter((x) => x.loanId.toString() == loanId.toString() && installmentStatus.includes(x.status));

                            for (const installment of installments) {
                                const _overDueByDays = new DateDiff(today, installment.dueDate).days();

                                record.overDueByDays = record.overDueByDays > _overDueByDays ? record.overDueByDays : _overDueByDays;
                            }

                            tempInstallments = installments.filter((x) => x.status == "Pending");
                        }

                        pendingInstallments = pendingInstallments.concat(...tempInstallments);
                        record.pendingInstallments = tempInstallments.length;

                        if (installments != null && installments.length > 0) {

                            record.principalOutstanding = installments.filter((x) => x.installmentPrincipal > 0).reduce(function (prev, cur) {
                                return prev + cur.installmentPrincipal;
                            }, 0);


                            record.loanOutstanding = installments.filter((x) => x.installmentAmount > 0).reduce(function (prev, cur) {
                                return prev + cur.installmentAmount;
                            }, 0);

                            record.interestOutstanding = installments.filter((x) => x.interestOutstanding > 0).reduce(function (prev, cur) {
                                return prev + cur.interestOutstanding;
                            }, 0);
                            //logger.logs("record.interestOutstanding", record.interestOutstanding);

                            record.moratoriumInterestOutstanding = installments.filter((x) => x.moratoriumInterestOutstanding > 0).reduce(function (prev, cur) {
                                return prev + cur.moratoriumInterestOutstanding;
                            }, 0);

                            record.moratoriumInterest = installments.filter((x) => x.moratoriumInterest > 0).reduce(function (prev, cur) {
                                return prev + parseFloat(cur.moratoriumInterest);
                            }, 0);


                            if (tillDate != null) {
                                const _pendingInstallmentsTillDate = installments.filter((x) => x.dueDate < tillDate);

                                if (_pendingInstallmentsTillDate != null && _pendingInstallmentsTillDate.length > 0) {
                                    record.principalOutstandingTillDate = _pendingInstallmentsTillDate.filter((x) => x.principalOutstanding > 0).reduce(function (prev, cur) {
                                        return prev + cur.principalOutstanding;
                                    }, 0);

                                    record.interestOutstandingTillDate = _pendingInstallmentsTillDate.filter((x) => x.interestOutstanding > 0).reduce(function (prev, cur) {
                                        return prev + cur.interestOutstanding;
                                    }, 0);

                                    record.moratoriumInterestOutstandingTillDate = _pendingInstallmentsTillDate.filter((x) => x.moratoriumInterestOutstanding > 0).reduce(function (prev, cur) {
                                        return prev + cur.moratoriumInterestOutstanding;
                                    }, 0);

                                    record.pendingInstallmentsTillDate = _pendingInstallmentsTillDate.length;

                                    record.loanOutstandingTillDate = round10(record.principalOutstandingTillDate + record.interestOutstandingTillDate, -2);

                                    pendingInstallmentsTillDate = pendingInstallmentsTillDate.concat(..._pendingInstallmentsTillDate);
                                }
                            }
                        }

                        if (allFees.data != null) {
                            const fees = allFees.data.filter((x) => x.loanId.toString() == loanId.toString() && installmentStatus.includes(x.status));

                            record.pendingFees = fees.length;

                            if (fees != null && fees.length > 0) {
                                pendingFees = pendingFees.concat(...fees);
                                record.feeAmountOutstanding = fees.filter((x) => x.feeAmountOutstanding > 0 && x.isWaiveOff == false).reduce(function (prev, cur) {
                                    return prev + cur.feeAmountOutstanding;
                                }, 0);

                                if (tillDate != null) {
                                    const _pendingFeesTillDate = fees.filter((x) => x.feeDueDate < tillDate);

                                    if (_pendingFeesTillDate != null && _pendingFeesTillDate.length > 0) {
                                        record.feeAmountOutstandingTillDate = _pendingFeesTillDate.filter((x) => x.feeAmountOutstanding > 0 && x.isWaiveOff == false).reduce(function (prev, cur) {
                                            return prev + cur.feeAmountOutstanding;
                                        }, 0);

                                        pendingFeesTillDate = pendingFeesTillDate.concat(..._pendingFeesTillDate);
                                    }
                                }
                            }
                        }

                        // record.loanOutstanding = round10(record.principalOutstanding + record.interestOutstanding, -2);

                        record.totalOutstanding = round10(record.loanOutstanding + record.moratoriumInterestOutstanding + record.feeAmountOutstanding, -2);

                        if (tillDate != null) {
                            record.totalOutstandingTillDate = round10(record.principalOutstandingTillDate + record.interestOutstandingTillDate + record.moratoriumInterestOutstandingTillDate + record.feeAmountOutstandingTillDate, -2);
                        }

                        const lastPaymentReceivedDate = allInstallments.data.filter((x) => x.loanId.toString() == loanId.toString() && x.status == "Received").reduce(function (prev, cur) {
                            return (prev && prev.transactionDate > cur.transactionDate) ? prev.transactionDate : cur.transactionDate;
                        }, null);

                        if (lastPaymentReceivedDate)
                            record.lastPaymentReceivedDate = getDMYFormattedDate(lastPaymentReceivedDate, "/");

                        responseData.push(record);
                    }
                    output.pendingInstallments = pendingInstallments;
                    output.pendingFees = pendingFees;
                    output.pendingInstallmentsTillDate = pendingInstallmentsTillDate;
                    output.pendingFeesTillDate = pendingFeesTillDate;
                } else output.message = allFees.message;
            } else output.message = allInstallments.message;

            output.data = responseData;
            output.success = true;
            output.message = "Fetched Successfully";
            output.status = 200;

        } else output.message = "loanIds or loanNos is mandatory";
    } catch (ex) {
        logger.logs(ex);
        saveErrorLog("getInstallmentsPending", apiPath, { loanIds, loanNos, installmentStatus, installmentNumber, isRequireAllInstallments, isRequireFees, tillDate, overdueBy, overDueFrom, overDueTo, isRequirePendingInstallments, isRequirePendingFees }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};

module.exports.cancelInstallments = async function (loanId) {
    let output = {
        success: false,
        message: "Some Error Occurred",
        status: 200,
    };
    try {
        await LoanInstallments.updateMany({ loanId: mongoose.Types.ObjectId(loanId), status: { $in: ["Pending", "Partial"] } }, { $set: { status: "Loan Cancelled", updatedBy: "System", updatedOn: Date.now() } });

        await LoanFees.updateMany({ loanId: mongoose.Types.ObjectId(loanId), status: { $in: ["Pending", "Partial"] } }, { $set: { status: "Loan Cancelled", updatedBy: "System", updatedOn: Date.now() } });

        output = {
            success: true,
            message: "Updated Successfully",
            status: 200,
        };
    } catch (ex) {
        logger.logs("error :", ex);
        output.message = ex.message;
        saveErrorLog("cancelInstallments", apiPath, { loanId }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }
    return output;
};

module.exports.holdCollectionViaWallet = async function (loanIds, holdFlag = true, installmentNumbers = null, holdBy = null) {
    let output = {
        success: false,
        message: "Some Error Occurred",
        status: 200,
    };
    try {
        if (loanIds != null && loanIds.length > 0) {
            let query = { loanId: { $in: loanIds }, status: { $in: ["Pending", "Partial"] } };

            if (installmentNumbers != null && installmentNumbers.length > 0) query.installmentNumber = { $in: installmentNumbers };

            const installments = await LoanInstallments.find(query).select({ loanId: 1, installmentNumber: 1 });

            const fees = await LoanFees.find(query).select({ loanId: 1, feeType: 1, installmentNumber: 1 });
            let collection = [];

            const installmentHash = groupBy(installments, "loanId");
            const feesHash = groupBy(fees, "loanId");

            for (let i = 0; i < loanIds.length; i++) {
                const loanInstallements = installmentHash[loanIds[i].toString()];
                const loanFees = feesHash[loanIds[i].toString()];

                if (loanInstallements.length > 0) {
                    let installments = [];
                    for (let j = 0; j < loanInstallements.length; j++) {
                        installments.push(loanInstallements[j].installmentNumber)
                    }

                    if (installments.length > 0) {
                        const log = {
                            loanId: loanIds[i],
                            holdFlag: holdFlag,
                            installmentNumbers: installments.sort(function (a, b) { return a - b }).join(","),
                            Type: "installment",
                            holdBy: holdBy,
                            insertedOn: Date.now()
                        };
                        collection.push(log);
                    }
                }

                if (fees.length > 0) {

                    let installments = [];
                    for (let j = 0; j < loanFees.length; j++) {
                        installments.push(loanFees[j].installmentNumber)
                    }
                    if (installments.length > 0) {
                        const log = {
                            loanId: loanIds[i],
                            holdFlag: holdFlag,
                            installmentNumbers: installments.sort(function (a, b) { return a - b }).join(","),
                            Type: "Late Fee",
                            holdBy: holdBy,
                            insertedOn: Date.now()
                        };
                        collection.push(log);
                    }
                }
            }

            if (collection.length > 0) await HoldCollectionLog.insertMany(collection);
            if (holdFlag) {
                console.log("true")

                await LoanInstallments.updateMany(query, { $set: { holdCollectionViaWallet: holdFlag, holdCollectionStartDate: Date.now() } });

                await LoanFees.updateMany(query, { $set: { holdCollectionViaWallet: holdFlag, holdCollectionStartDate: Date.now() } });

            } else {
                console.log("false")
                await LoanInstallments.updateMany(query, { $set: { holdCollectionViaWallet: holdFlag, holdCollectionEndDate: Date.now() } });

                await LoanFees.updateMany(query, { $set: { holdCollectionViaWallet: holdFlag, holdCollectionEndDate: Date.now() } });
            }


            output = {
                success: true,
                message: "Updated Successfully",
                status: 200,
            };
        } else output.message = "loanIds Are Mandatory";

    } catch (ex) {
        logger.logs("error :", ex);
        output.message = ex.message;
        saveErrorLog("holdCollectionViaWallet", apiPath, { loanIds, holdFlag, installmentNumbers }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }
    return output;
};

module.exports.getSingleInstallmentDetails = async function (loanId, installmentNumber) {
    let output = {
        installmentAmount: 0,
        principalReceived: 0,
        interestReceived: 0,
        moratoriumInterestReceived: 0,
        principalOutstanding: 0,
        interestOutstanding: 0,
        moratoriumInterestOutstanding: 0,
        feeAmount: 0,
        feeAmountReceived: 0,
        feeAmountOutstanding: 0,
        actualDueDate: "",
        transactionDate: "",
        dueDate: "",
        receivedAmount: 0,
        outstandingAmount: 0,
        installmentHoldCollectionViaWallet: false,
        installmentStatus: null,
        feesStatus: "Received",
        success: false,
        message: "Some Error Occurred",
        status: 200,
    };
    try {
        const filter = { loanId: mongoose.Types.ObjectId(loanId), installmentNumber: installmentNumber };

        const loanInstallments = await LoanInstallments.findOne(filter);
        const fees = await LoanFees.find(filter);

        if (loanInstallments != null) {
            output.installmentAmount = loanInstallments.installmentAmount;
            output.principalReceived = loanInstallments.principalReceived;
            output.interestReceived = loanInstallments.interestReceived;
            output.moratoriumInterestReceived = loanInstallments.moratoriumInterestReceived;
            output.principalOutstanding = loanInstallments.principalOutstanding;
            output.interestOutstanding = loanInstallments.interestOutstanding;
            output.moratoriumInterestOutstanding = loanInstallments.moratoriumInterestOutstanding;
            output.installmentHoldCollectionViaWallet = loanInstallments.holdCollectionViaWallet;
            output.installmentStatus = loanInstallments.status;
            output.actualDueDate = loanInstallments.actualDueDate;
            output.transactionDate = loanInstallments.transactionDate;
            output.dueDate = loanInstallments.dueDate;
        }

        if (fees != null && fees.length > 0) {
            for (let i = 0; i < fees.length; i++) {
                if (!fees[i].isWaiveOff) {
                    output.feeAmount += fees[i].feeAmount;
                    output.feeAmountReceived += fees[i].feeAmountReceived;
                    output.feeAmountOutstanding += fees[i].feeAmountOutstanding;
                    if (fees[i].status == "pending") output.feesStatus = fees[i].status;
                }
            }
        }

        output.receivedAmount = round10(output.principalReceived + output.interestReceived + output.moratoriumInterestReceived + output.feeAmountReceived, -2);

        output.outstandingAmount = round10(output.principalOutstanding + output.interestOutstanding + output.moratoriumInterestOutstanding + output.feeAmountOutstanding, -2);

        output.success = true;
        output.message = "Fetched Successfully";
        output.status = 200;

    } catch (ex) {
        saveErrorLog("getSingleInstallmentDetails", apiPath, { loanId, installmentNumber }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};

module.exports.getInstallmentsForRepayment = async function (loanIds, repaymentDate, isIncludeHold = false) {
    let output = {
        repaymentInstallments: [],
        success: false,
        message: "Some Error Occurred",
        status: 200,
    };
    try {
        let records = [];

        const checkInstallmentStatus = ["Pending", "Partial"];

        let query = {
            loanId: { $in: loanIds },
            dueDate: { $lte: repaymentDate },
            status: { $in: checkInstallmentStatus }
        }

        if (isIncludeHold == false) query.holdCollectionViaWallet = false;

        const installments = await LoanInstallments.aggregate([{
            $match: query
        }, { $sort: { loanNo: 1, installmentNumber: 1 } }]).allowDiskUse(true);

        if (installments.length > 0) {
            query = { loanId: { $in: loanIds }, isWaiveOff: false, feeDueDate: { $lte: repaymentDate }, status: { $in: checkInstallmentStatus } };

            if (isIncludeHold == false) query.holdCollectionViaWallet = false;

            const fees = await LoanFees.aggregate([{
                $match: query
            }, { $sort: { loanNo: 1, installmentNumber: 1 } }]).allowDiskUse(true);

            for (let i = 0; i < installments.length; i++) {
                const record = {
                    loanId: installments[i].loanId,
                    loanNo: installments[i].loanNo,
                    principalRecovered: installments[i].principalOutstanding,
                    interestAmount: installments[i].interestOutstanding,
                    rePaymentDate: installments[i].dueDate,
                    actualDueDate: installments[i].actualDueDate,
                    installmentNumber: installments[i].installmentNumber,
                    moratoriumInterest: installments[i].moratoriumInterestOutstanding,
                    repayAmount: round10(installments[i].principalOutstanding + installments[i].interestOutstanding, -2),
                    penalty: 0,
                    totalRepayAmount: 0,
                };

                record.penalty = fees.filter((x) => x.loanId.toString() == installments[i].loanId.toString() && x.installmentNumber == installments[i].installmentNumber && x.holdCollectionViaWallet == false && x.isWaiveOff == false).reduce(function (prev, cur) {
                    return prev + parseFloat(cur.feeAmountOutstanding);
                }, 0);

                record.totalRepayAmount = round10(installments[i].principalOutstanding + installments[i].interestOutstanding + installments[i].moratoriumInterestOutstanding + record.penalty, -2);

                records.push(record);

                output = {
                    repaymentInstallments: records,
                    success: true,
                    message: "Fetched Successfully",
                    status: 200,
                };
            }
        } else {
            output = {
                repaymentInstallments: [],
                success: true,
                message: "No Installments For Repayment",
                status: 200,
            };
        }
    } catch (ex) {
        saveErrorLog("getInstallmentsForRepayment", apiPath, { loanIds, repaymentDate }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};

module.exports.getNextPendingEmiDate = async function (loanNos) {
    let output = {
        data: [],
        success: false,
        message: "Some Error Occurred",
        status: 200,
    };
    try {
        let allInstallments = await LoanInstallments.aggregate([
            {
                $match: {
                    loanNo: {
                        $in: loanNos,
                    },
                    status: "Received",
                },
            },
            {
                $group: {
                    _id: "$loanNo",
                    installmentNo: {
                        $sum: 1
                    },
                },
            },
            {
                $lookup: {
                    from: "LoanInstallments",
                    localField: "_id",
                    foreignField: "loanNo",
                    as: "loanInstallments",
                },
            },
            {
                $project: {
                    loanInstallmentsRecord: {
                        $arrayElemAt: [
                            {
                                $filter: {
                                    input: "$loanInstallments",
                                    as: "item",
                                    cond: {
                                        $and: [
                                            {
                                                $eq: [
                                                    "$$item.status",
                                                    "Pending",
                                                ],
                                            },
                                            {
                                                $gt: [
                                                    "$$item.installmentNumber",
                                                    "$installmentNo",
                                                ],
                                            },
                                        ],
                                    },
                                },
                            },
                            0,
                        ],
                    },
                },
            },
        ]);

        const records = [];

        const today = new Date();
        const NonExistsLoans = [];

        for (let i = 0; i < loanNos.length; i++) {
            let nextInstallmentDate = null;
            let installment = null;

            if (allInstallments != null && allInstallments.length > 0)
                installment = allInstallments.filter((x) => x._id == loanNos[i]);


            if (installment != null && installment.length > 0 && installment[0].loanInstallmentsRecord && installment[0].loanInstallmentsRecord != null && installment[0].loanInstallmentsRecord.dueDate != null) {
                nextInstallmentDate = installment[0].loanInstallmentsRecord.dueDate;
            } else {
                NonExistsLoans.push(loanNos[i]);
            }

            const diffInDays = (nextInstallmentDate != null) ? new DateDiff(today, nextInstallmentDate).days() : null;

            const record = { loanNo: loanNos[i], nextInstallmentDate: nextInstallmentDate, diffInDays: diffInDays };
            records.push(record);
        }

        if (NonExistsLoans.length > 0) {
            allInstallments = await LoanInstallments.aggregate([
                {
                    $match: {
                        loanNo: {
                            $in: NonExistsLoans,
                        },
                        status: "Pending",
                    },
                },
                {
                    $group: {
                        _id: "$loanNo",
                        installmentNo: {
                            $min: "$installmentNumber",
                        },
                    },
                },
                {
                    $lookup: {
                        from: "LoanInstallments",
                        localField: "_id",
                        foreignField: "loanNo",
                        as: "loanInstallments",
                    },
                },
                {
                    $project: {
                        loanInstallmentsRecord: {
                            $arrayElemAt: [
                                {
                                    $filter: {
                                        input: "$loanInstallments",
                                        as: "item",
                                        cond: {
                                            $and: [
                                                {
                                                    $eq: [
                                                        "$$item.status",
                                                        "Pending",
                                                    ],
                                                },
                                                {
                                                    $eq: [
                                                        "$$item.installmentNumber",
                                                        "$installmentNo",
                                                    ],
                                                },
                                            ],
                                        },
                                    },
                                },
                                0,
                            ],
                        },
                    },
                },
            ]);

            for (let i = 0; i < NonExistsLoans.length; i++) {
                let nextInstallmentDate = null;
                let installment = null;

                if (allInstallments != null && allInstallments.length > 0)
                    installment = allInstallments.filter((x) => x._id.toString() == NonExistsLoans[i].toString());

                if (installment != null && installment.length > 0 && installment[0].loanInstallmentsRecord && installment[0].loanInstallmentsRecord != null && installment[0].loanInstallmentsRecord.dueDate != null) {
                    nextInstallmentDate = installment[0].loanInstallmentsRecord.dueDate;
                }

                const diffInDays = (nextInstallmentDate != null) ? new DateDiff(today, nextInstallmentDate).days() : null;

                const record = { loanNo: NonExistsLoans[i], nextInstallmentDate: nextInstallmentDate, diffInDays: diffInDays };
                records.push(record);
            }
        }

        output = {
            data: records,
            success: true,
            message: "Fetched Successfully",
            status: 200,
        };


        //     const records = [];
        //     const today = new Date();

        //     for (let i = 0; i < loanIds.length; i++) {
        //         const installments = allInstallments.data.filter((x) => x.loanId.toString() == loanIds[i].toString());
        //         const fees = allFees.data.filter((x) => x.loanId.toString() == loanIds[i].toString());

        //         let minInstallmentDate = null;
        //         if (installments.length > 0) {
        //             minInstallmentDate = installments.filter((x) => x.dueDate != null).reduce(function (a, b) { return a.dueDate < b.dueDate ? a.dueDate : b.dueDate; });
        //         }

        //         let minFeesDate = null;
        //         if (fees.length > 0) {
        //             minFeesDate = fees.filter((x) => x.feeDueDate != null).reduce(function (a, b) { return a.feeDueDate < b.feeDueDate ? a.feeDueDate : b.feeDueDate; });
        //         }

        //         let minDate = null;
        //         if (minInstallmentDate != null && minFeesDate != null)
        //             minDate = minInstallmentDate < minFeesDate ? minInstallmentDate : minFeesDate;
        //         else
        //             minDate = minInstallmentDate != null ? minInstallmentDate : minFeesDate;

        //         const diffInDays = (minDate != null) ? diff(today, minDate).days() : null;

        //         const record = { loanId: loanIds[i], minDate: minDate, diffInDays: diffInDays };
        //         records.push(record);
        //     }
        //     output = {
        //         data: records,
        //         success: true,
        //         message: "Fetched Successfully",
        //         status: 200,
        //     };
        // } else output.message = !allInstallments.success ? allInstallments.message : allFees.message;
    } catch (ex) {
        logger.logs(ex);
        saveErrorLog("getNextPendingEmiDate", apiPath, { loanNos }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};

module.exports.updateAllPendingEmiStatus = async function (loanId, Status, updatedBy) {
    let output = {
        success: false,
        message: "Some Error Occurred",
        status: 200,
    };
    try {
        if (Status == "Received" || Status == "Close Prepaid") {
            await LoanInstallments.updateMany({ loanId: mongoose.Types.ObjectId(loanId), status: { $in: ["Pending", "Partial"] } }, { $set: { status: Status, transactionDate: Date.now(), updatedBy: "System", updatedOn: Date.now() } });

            await LoanFees.updateMany({ loanId: mongoose.Types.ObjectId(loanId), status: { $in: ["Pending", "Partial"] } }, { $set: { status: Status, feeCollectedDate: Date.now(), updatedBy: "System", updatedOn: Date.now() } });
        }

        output = {
            success: true,
            message: "Updated Successfully",
            status: 200,
        };
    } catch (ex) {
        saveErrorLog("updateAllPendingEmiStatus", apiPath, { loanId, Status, updatedBy }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};

module.exports.updateDLPrinciplePaidStatus = async function (loanId, installmentNumber) {
    let output = {
        success: false,
        message: "Some Error Occurred",
        status: 200,
    };
    try {
        await LoanInstallments.updateOne({ loanId: mongoose.Types.ObjectId(loanId), installmentNumber: installmentNumber },
            { $set: { isPrincipalPaid: true, principalPaidOn: Date.now(), updatedBy: "System", updatedOn: Date.now() } });

        output = {
            success: true,
            message: "Updated Successfully",
            status: 200,
        };
    } catch (ex) {
        logger.logs(ex);
        output.message = ex.message;
        saveErrorLog("updateDLPrinciplePaidStatus", apiPath, { loanId, installmentNumber }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }
    return output;
};

module.exports.getPeriodicInstallments = async function (loanNos, installmentStatus = [], installmentNumbers = [], fromDate = null, toDate = null) {
    let output = {
        data: null,
        success: false,
        message: "Some Error Occurred",
        status: 200,
    };
    try {
        let installmentFilter = {};
        let feesFilter = {};

        if (loanNos.length > 0) {
            installmentFilter.loanNo = { $in: loanNos };
            feesFilter.loanNo = { $in: loanNos };
        }

        if (installmentStatus != null && installmentStatus.length > 0) {
            installmentFilter.status = { $in: installmentStatus };
            feesFilter.status = { $in: installmentStatus };
        }

        if (installmentNumbers != null && installmentNumbers.length > 0) {
            installmentFilter.installmentNumber = { $in: installmentNumbers };
            feesFilter.installmentNumber = { $in: installmentNumbers };
        }

        if (fromDate != null) {
            installmentFilter.dueDate = { $gte: fromDate };
            feesFilter.feeDueDate = { $gte: fromDate };
        }

        if (toDate != null) {
            installmentFilter.dueDate = { $lte: toDate };
            feesFilter.feeDueDate = { $lte: toDate };
        }

        const Installments = await LoanInstallments.find(installmentFilter);
        const Fees = await LoanFees.find(installmentFilter);

        let records = [];

        for (const loanNo of loanNos) {
            let loanInstallments = [];
            let loanFees = 0;

            if (Installments != null && Installments.length > 0) {
                loanInstallments = Installments.filter(x => x.loanNo == loanNo);
            }

            if (Fees != null && Fees.length > 0) {
                loanFees = Fees.filter(x => x.loanNo == loanNo);
            }

            let uniqueInstallmentsNumbers = null;
            let uniqueFeesInstallmentsNumbers = null;

            if (loanInstallments != null && loanInstallments.length > 0)
                uniqueInstallmentsNumbers = loanInstallments.map(item => item.installmentNumber);

            if (loanFees != null && loanFees.length > 0)
                uniqueFeesInstallmentsNumbers = loanFees.map(item => item.installmentNumber);

            let uniqueInstallments = [];

            if (uniqueInstallmentsNumbers != null && uniqueInstallmentsNumbers.length > 0 && uniqueFeesInstallmentsNumbers != null && uniqueFeesInstallmentsNumbers.length > 0)
                uniqueInstallments = [...new Set([...uniqueInstallmentsNumbers, ...uniqueFeesInstallmentsNumbers])];
            else if (uniqueInstallmentsNumbers != null && uniqueInstallmentsNumbers.length > 0)
                uniqueInstallments = uniqueInstallmentsNumbers;
            else if (uniqueFeesInstallmentsNumbers != null && uniqueFeesInstallmentsNumbers.length > 0)
                uniqueInstallments = uniqueFeesInstallmentsNumbers;

            for (let i = 0; i < uniqueInstallments.length; i++) {
                let record = {
                    loanNo: loanNo,
                    loanId: null,
                    installmentNumber: uniqueInstallments[i],
                    installmentPrincipal: 0,
                    installmentInterest: 0,
                    installmentAmount: 0,
                    moratoriumInterest: 0,
                    principalReceived: 0,
                    interestReceived: 0,
                    moratoriumInterestReceived: 0,
                    principalOutstanding: 0,
                    interestOutstanding: 0,
                    moratoriumInterestOutstanding: 0,
                    installmentHoldCollectionViaWallet: false,
                    status: null,
                    dueDate: null,
                    actualDueDate: null,
                    transactionDate: null,
                    isPrincipalPaid: false,
                    principalPaidOn: null,
                    feeAmount: 0,
                    feeAmountReceived: 0,
                    feeAmountOutstanding: 0,
                    receivedAmount: 0,
                    outstandingAmount: 0,
                    totalAmount: 0
                }

                if (loanInstallments != null && loanInstallments.length > 0) {
                    const installment = loanInstallments.filter((x) => x.installmentNumber == uniqueInstallments[i]);
                    if (installment != null && installment.length > 0) {
                        record.loanId = installment[0].loanId;
                        record.installmentPrincipal = installment[0].installmentPrincipal;
                        record.installmentInterest = installment[0].installmentInterest;
                        record.installmentAmount = installment[0].installmentAmount;
                        record.principalReceived = installment[0].principalReceived;
                        record.interestReceived = installment[0].interestReceived;
                        record.moratoriumInterestReceived = installment[0].moratoriumInterestReceived;
                        record.principalOutstanding = installment[0].principalOutstanding;
                        record.interestOutstanding = installment[0].interestOutstanding;
                        record.moratoriumInterestOutstanding = installment[0].moratoriumInterestOutstanding;
                        record.installmentHoldCollectionViaWallet = installment[0].holdCollectionViaWallet;
                        record.status = installment[0].status;
                        record.actualDueDate = installment[0].actualDueDate;
                        record.transactionDate = installment[0].transactionDate;
                        record.dueDate = installment[0].dueDate;
                        record.isPrincipalPaid = installment[0].isPrincipalPaid;
                        record.principalPaidOn = installment[0].principalPaidOn;
                    }
                }

                if (loanFees != null && loanFees.length > 0) {
                    const fees = loanFees.filter((x) => x.installmentNumber == uniqueInstallments[i]);
                    if (fees != null && fees.length > 0) {
                        for (let j = 0; j < fees.length; j++) {
                            if (!fees[j].isWaiveOff) {
                                record.feeAmount += fees[j].feeAmount;
                                record.feeAmountReceived += fees[j].feeAmountReceived;
                                record.feeAmountOutstanding += fees[j].feeAmountOutstanding;
                            }
                        }

                        if (record.loanId == null) {
                            installment = await LoanInstallments.find({ loanNo: fees[0].loanNo, installmentNumber: uniqueInstallments[i] });
                            if (installment != null && installment.length > 0) {
                                record.loanId = installment[0].loanId;
                                record.installmentPrincipal = installment[0].installmentPrincipal;
                                record.installmentInterest = installment[0].installmentInterest;
                                record.installmentAmount = installment[0].installmentAmount;
                                record.principalReceived = installment[0].principalReceived;
                                record.interestReceived = installment[0].interestReceived;
                                record.moratoriumInterestReceived = installment[0].moratoriumInterestReceived;
                                record.principalOutstanding = installment[0].principalOutstanding;
                                record.interestOutstanding = installment[0].interestOutstanding;
                                record.moratoriumInterestOutstanding = installment[0].moratoriumInterestOutstanding;
                                record.installmentHoldCollectionViaWallet = installment[0].holdCollectionViaWallet;
                                record.status = installment[0].status;
                                record.actualDueDate = installment[0].actualDueDate;
                                record.transactionDate = installment[0].transactionDate;
                                record.dueDate = installment[0].dueDate;
                                record.isPrincipalPaid = installment[0].isPrincipalPaid;
                                record.principalPaidOn = installment[0].principalPaidOn;
                            }
                        }
                    }
                }

                record.receivedAmount = round10(record.principalReceived + record.interestReceived + record.moratoriumInterestReceived + record.feeAmountReceived, -2);

                record.outstandingAmount = round10(record.principalOutstanding + record.interestOutstanding + record.moratoriumInterestOutstanding + record.feeAmountOutstanding, -2);

                record.totalAmount = round10(record.installmentAmount + record.moratoriumInterestReceived + record.feeAmount, -2);

                records.push(record);
            }
        }

        output = {
            data: records,
            success: true,
            message: "Fetched Successfully",
            status: 200,
        };
    } catch (ex) {
        output.message = ex.message;
        saveErrorLog("getPeriodicInstallments", apiPath, { loanNos, installmentStatus, installmentNumbers }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }
    return output;
};

module.exports.getExpiringLoansForPeriod = async function (loanNos, startDate, endDate) {
    let output = {
        data: null,
        success: false,
        message: "Some Error Occurred",
        status: 200,
    };
    try {
        const installments = await LoanInstallments.aggregate([
            { $match: { loanNo: { $in: loanNos } } },
            {
                $group: {
                    _id: "$loanNo",
                    loanId: { "$first": "$loanId" },
                    installment: {
                        "$max": "$installmentNumber"
                    },
                    dueDate: {
                        "$max": "$dueDate"
                    }
                }
            },
            { $match: { 'dueDate': { $gte: startDate, $lt: endDate } } },
            {
                $project: {
                    loanNo: '$_id',
                    loanId: "$loanId",
                    installment: "$installment",
                    dueDate: "$dueDate"
                }
            }
        ]);
        output = {
            data: installments,
            success: true,
            message: "Fetched Successfully",
            status: 200,
        };
    } catch (ex) {
        logger.logs(ex);
        output.message = ex.message;
        saveErrorLog("getExpiringLoansForPeriod", apiPath, { loanNos, startDate, endDate }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }
    return output;
};
// module.exports.getSingleInstallmentDetails = getSingleInstallmentDetails;

// -------------------------------------------------------------------------------------------------------------------

module.exports.addInstallmentForDailyLoan = async function (partnerId) {
    let output = {
        success: false,
        message: "Some Error Occurred",
        status: 501,
    };
    try {
        if (!IsNullEmpty(partnerId)) {
            let today = new Date();

            today = dateAdd("day", -1, today);

            const checkDate = String(today.getDate()) + "/" + String(today.getMonth() + 1) + "/" + String(today.getFullYear());

            let fromDate = getFromDate(checkDate);
            let toDate = getToDate(checkDate);

            const loans = await LoanInfo.find({ partnerId: mongoose.Types.ObjectId(partnerId), productType: "DL_30", status: { $in: ["Loan In Progress", "Written-Off"] } }).select({ _id: 1, loanNo: 1, userId: 1, partnerId: 1, merchantCode: 1 });

            const uniqueLoanNos = [...new Set(loans.map(item => item.loanNo))];

            const expiringLoans = await LoanInstallments.aggregate([
                { $match: { loanNo: { $in: uniqueLoanNos } } },
                {
                    $group: {
                        _id: "$loanNo",
                        loanId: { "$first": "$loanId" },
                        installment: {
                            "$max": "$installmentNumber"
                        },
                        dueDate: {
                            "$max": "$dueDate"
                        }
                    }
                },
                { $match: { 'dueDate': { $gte: fromDate, $lte: toDate } } },
                {
                    $project: {
                        loanNo: '$_id',
                        loanId: "$loanId",
                        installmentNo: "$installment",
                        dueDate: "$dueDate"
                    }
                }
            ]);

            if (expiringLoans != null && expiringLoans.length > 0) {
                for (let i = 0; i < expiringLoans.length; i++) {
                    const installment = await LoanInstallments.findOne({ loanId: mongoose.Types.ObjectId(expiringLoans[i].loanId), installmentNumber: expiringLoans[i].installmentNo });

                    if (installment != null && installment.isPrincipalPaid == false) {
                        const dueDate = dateAdd("day", 1, installment.dueDate);
                        const newInstallmentNo = installment.installmentNumber + 1;

                        const loanInstallment = new LoanInstallments({
                            loanId: installment.loanId,
                            loanNo: installment.loanNo,
                            installmentNumber: newInstallmentNo,
                            partnerId: installment.partnerId,
                            installmentPrincipal: installment.installmentPrincipal,
                            initialPrincipalOutstanding: installment.initialPrincipalOutstanding,
                            installmentInterest: installment.installmentInterest,
                            installmentAmount: installment.installmentAmount,
                            moratoriumInterest: installment.moratoriumInterest,
                            dueDate: dueDate,
                            actualDueDate: dueDate,
                            channel: installment.channel,
                            status: "Pending",
                            holdCollectionViaWallet: installment.holdCollectionViaWallet,
                            lastRepaymentRequestedDate: null,
                            principalReceived: 0,
                            interestReceived: 0,
                            moratoriumInterestReceived: 0,
                            principalOutstanding: installment.installmentPrincipal,
                            interestOutstanding: installment.installmentInterest,
                            moratoriumInterestOutstanding: installment.moratoriumInterest,
                            transactionDate: null,
                            lendingPartnerUTR: null,
                            lendingPartnerSettlementDate: null,
                            insertedOn: Date.now(),
                            isPrincipalPaid: false,
                            principalPaidOn: null,
                            updatedOn: null,
                            updatedBy: null
                        });
                        await loanInstallment.save();

                        installment.installmentAmount = installment.installmentAmount - installment.installmentPrincipal;
                        installment.installmentPrincipal = 0;
                        installment.principalOutstanding = 0;
                        await installment.save();

                        await LoanInfo.updateOne({ _id: mongoose.Types.ObjectId(expiringLoans[i].loanId), isActive: false }, { $set: { isActive: true, status: "Loan In Progress", updatedOn: Date.now() } });
                    }
                }

                output = {
                    success: true,
                    message: "Inserted Successfully",
                    status: 200,
                };
            } else {
                output = {
                    success: true,
                    message: "No Loan found",
                    status: 200,
                };
            }
        } else output.message = "PartnerId Is Mandatory";
    } catch (ex) {
        output.message = ex.message;
        saveErrorLog("addInstallmentForDailyLoan", apiPath, { partnerId }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }
    return output;
};

const aggregateInstallment = async function (query) {
    //Not done for partial as changes in Payment details pending for enach.
    let output = {
        data: null,
        success: false,
        message: "Some Error Occurred",
        status: 200,
    };
    try {

        output.data = await LoanInstallments.aggregate(query).allowDiskUse(true);

        output.success = true;
        output.message = "success";
        output.status = 200;
    }
    catch (ex) {
        logger.logs(ex);
        saveErrorLog("aggregateInstallment", apiPath, query, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};

module.exports.aggregateInstallment = aggregateInstallment

module.exports.distinctInstallment = async function (key, query) {
    let output = {
        data: null,
        success: false,
        message: "Some Error Occurred",
        status: 200,
    };
    try {

        output.data = await LoanInstallments.distinct(key, query);

        output.success = true;
        output.message = "success";
        output.status = 200;
    }
    catch (ex) {
        logger.logs(ex);
        saveErrorLog("distinctInstallment", apiPath, query, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};

async function getEmiConsolidatedInformation(pendingInstallments, pendingFees, loanIds) {
    try {
        if (loanIds) {
            let chunkSize = 500;
            let totalChunks = (Math.floor(loanIds.length / chunkSize)) + (loanIds.length % chunkSize ? 1 : 0);
            for (let i = 0; i < totalChunks; i++) {
                let currentChuckLoanIds = loanIds.splice(0, chunkSize);

                let aggregateQuery = [
                    {
                        $match: {
                            loanId: { $in: currentChuckLoanIds },
                            status: { $in: ["Pending", "Partial"] }
                        }
                    },
                    {
                        $sort: { installmentNumber: 1 }
                    },
                    {
                        $group: {
                            _id: '$loanId',
                            installments: {
                                $push: '$$ROOT'
                            }
                        }
                    }]

                let loanInstallements = await LoanInstallments.aggregate(aggregateQuery)
                let loanFees = await LoanFees.aggregate(aggregateQuery)

                let loanInstallementsHash = {}
                let loanFeesHash = {}

                for (let j = 0; j < loanInstallements.length; j++) {
                    loanInstallementsHash[loanInstallements[j]._id.toString()] = loanInstallements[j].installments
                }

                for (let j = 0; j < loanFees.length; j++) {
                    loanFeesHash[loanFees[j]._id.toString()] = loanFees[j].installments
                }

                let bulkWriteQuery = []

                for (let j = 0; j < currentChuckLoanIds.length; j++) {

                    let calculatedInformation = getEmiCalculatedData(loanInstallementsHash[currentChuckLoanIds[j].toString()] || [], loanFeesHash[currentChuckLoanIds[j].toString()] || []);

                    if (calculatedInformation) {
                        bulkWriteQuery.push({
                            updateOne: {
                                filter: { _id: currentChuckLoanIds[j] },
                                update: {
                                    $set: calculatedInformation
                                }
                            }
                        })
                    }
                }

                let bulkWriteRes = await LoanInfo.bulkWrite(bulkWriteQuery);
            }
        } else {
            return getEmiCalculatedData(pendingInstallments, pendingFees)
        }
    } catch (ex) {
        sendMailForFailure("Get emi consolidated information failure", `Get emi consolidated information failed. Failure Time : ${new Date().toLocaleString()}  Please call again manually.`);
        saveErrorLog("getEmiConsolidatedInformation", apiPath, { pendingInstallments, pendingFees, loanIds }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }
};
module.exports.getEmiConsolidatedInformation = getEmiConsolidatedInformation;

function getEmiCalculatedData(pendingInstallments, pendingFees) {

    const todayDate = new Date();

    const today = new Date(todayDate.getFullYear(), todayDate.getMonth(), todayDate.getDate(), 5, 30, 0, 0);

    try {
        const obj = {
            moratoriumInterestOutstanding: 0,
            feeAmountOutstanding: 0,
            totalOutstanding: 0,
            principalOutstandingTillDate: 0,
            interestOutstandingTillDate: 0,
            moratoriumInterestOutstandingTillDate: 0,
            feeAmountOutstandingTillDate: 0,
            pendingInstallmentsTillDate: 0,
            totalMoratoriumInterest: 0,
            totalInstallmentAmount: 0,
            totalOutstandingAmountTillDate: 0,
            pendingInstallments: pendingInstallments.length
        }

        if (!pendingInstallments || !pendingInstallments.length)
            return obj

        for (let i = 0; i < pendingInstallments.length; i++) {

            let pendingInstallment = pendingInstallments[i]

            if (pendingInstallment.moratoriumInterestOutstanding)
                obj.moratoriumInterestOutstanding += pendingInstallment.moratoriumInterestOutstanding;

            if (pendingInstallment.installmentAmount)
                obj.totalInstallmentAmount += pendingInstallment.installmentAmount; //field name should be either pendingInstallmentAmount or installmentAmountOutstaning

            if (pendingInstallment.moratoriumInterest)
                obj.totalMoratoriumInterest += pendingInstallment.moratoriumInterest; //field name should be either pendingMoratoriumInterest or MoratoriumInterestOutstaning


            if (pendingInstallment.dueDate < today) {

                if (pendingInstallment.principalOutstanding)
                    obj.principalOutstandingTillDate += pendingInstallment.principalOutstanding

                if (pendingInstallment.interestOutstanding)
                    obj.interestOutstandingTillDate += pendingInstallment.interestOutstanding;

                if (pendingInstallment.moratoriumInterestOutstanding)
                    obj.moratoriumInterestOutstandingTillDate += pendingInstallment.moratoriumInterestOutstanding;

                if (pendingInstallment.installmentAmount)
                    obj.pendingInstallmentsTillDate += pendingInstallment.installmentAmount;

            }
        }

        obj.nextEmiRepaymentDate = pendingInstallments[0].dueDate;

        for (let i = 0; i < pendingFees.length; i++) {
            let fees = pendingFees[i]

            if (fees.feeAmountOutstanding)
                obj.feeAmountOutstanding += fees.feeAmountOutstanding;

            if (fees.feeDueDate < today) {

                if (obj.feeAmountOutstanding)
                    obj.feeAmountOutstandingTillDate += fees.feeAmountOutstanding
            }
        }

        obj.totalOutstanding = round10(obj.totalInstallmentAmount + obj.moratoriumInterestOutstanding + obj.feeAmountOutstanding, -2);

        obj.totalOutstandingAmountTillDate = round10(obj.principalOutstandingTillDate + obj.interestOutstandingTillDate + obj.moratoriumInterestOutstandingTillDate + obj.feeAmountOutstandingTillDate, -2);

        return obj
    } catch (ex) {
        sendMailForFailure("Get emi calulated data failure", `Get emi calculated data failed ${pendingInstallments.length ? 'for loan ' + pendingInstallments[0].loanId.toString() : ""} .Failure Time : ${new Date().toLocaleString()}  Please call again manually.`);
        saveErrorLog("getEmiCalculatedData", apiPath, { pendingInstallments, pendingFees }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        return null
    }

};

module.exports.getLoansPendingTillDate = async function (loanNos, installmentStatus, tillDate) {
    let output = {
        pendingLoanNos: [],
        success: false,
        message: "Some Error Occurred",
        status: 200,
    };
    try {
        let filter = { dueDate: { $lt: tillDate } };
        if (loanNos != null) {
            if (loanNos != null) filter.loanNo = { $in: loanNos };
            if (installmentStatus != null) filter.status = { $in: installmentStatus };
            const installments = await LoanInstallments.aggregate([{ $match: filter }, { $sort: { loanNo: 1, installmentNumber: 1 } }, { $project: { loanNo: '$loanNo' } }]).allowDiskUse(true);
            if (installments != null && installments.length > 0) {
                const pendingLoanNos = [...new Set(installments.map(item => item.loanNo))];
                output = {
                    pendingLoanNos: pendingLoanNos,
                    success: true,
                    message: "Fetched Successfully",
                    status: 200,
                };
            } else {
                output = {
                    pendingLoanNos: [],
                    success: true,
                    message: "No Pending Installment's ",
                    status: 200,
                };
            }
        } else output.message = "loanIds or loanNos are mandatory";
    } catch (ex) {
        logger.logs(ex);
        saveErrorLog("getLoansPendingTillDate", apiPath, { loanNos, installmentStatus, tillDate }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};

async function sendMailForFailure(subject, message) {
    let body = "Dear Team,<br/>" + message;
    let res = await sendMailOnFailure(subject, body);
    return res;
};

module.exports.updateClosedLoansStatus = async function () {
    let output = {
        success: false,
        message: "Some Error Occurred",
        status: 200,
    };

    try {
        const loans = await LoanInfo.aggregate([
            { $match: { status: { $in: ["Loan In Progress", "Written-Off"] } } },
            {
                $lookup: {
                    from: "LoanInstallments",
                    localField: "_id",
                    foreignField: "loanId",
                    pipeline: [
                        {
                            $match: {
                                status: "Pending",
                            },
                        },
                    ],
                    as: "loanInstallmets",
                }
            },
            { $match: { loanInstallmets: [] } },
            {
                $project: {
                    _id: "$_id",
                    loanNo: "$loanNo",
                    status: "$status"
                }
            }
        ]);
        console.log("loans", loans);
        if (loans.length > 0) {
            for (let i = 0; i < loans.length; i++) {
                let loanStatus = "Close Repaid";
                const loanId = loans[i]._id;

                if (loans[i].status == "Loan In Progress") {
                    const installments = await LoanInstallments.find({ loanId: mongoose.Types.ObjectId(loanId) });
                    const loanFees = await LoanFees.find({ loanId: mongoose.Types.ObjectId(loanId) });

                    const lastInstallmentDueDate = new Date(installments[installments.length - 1].dueDate);

                    const latePaidInstallments = installments.filter((x) => x.transactionDate != null && new Date(x.transactionDate) > lastInstallmentDueDate).length;

                    const latePaidFees = loanFees.filter((x) => x.feeCollectedDate != null && new Date(x.feeCollectedDate) > lastInstallmentDueDate).length;

                    if (latePaidInstallments == 0 && latePaidFees == 0) loanStatus = "Close Prepaid";
                } else loanStatus = "WrittenOff Paid";

                const lastEmiReceived = await LoanInstallments.findOne({ loanId: mongoose.Types.ObjectId(loanId) }).sort({ transactionDate: -1 }).select({ transactionDate: 1 });

                await LoanInfo.findOneAndUpdate({ _id: mongoose.Types.ObjectId(loanId), status: { $in: ["Loan In Progress", "Written-Off"] } }, {
                    $set: { status: loanStatus, updatedOn: lastEmiReceived.transactionDate, adminModifiedOn: lastEmiReceived.transactionDate, postDisbursalStatus: null }, $push: {
                        flowDetails: {
                            Step: loanStatus,
                            changedByRole: "System",
                            insertedOn: lastEmiReceived.transactionDate
                        }
                    }
                }, { new: true, useFindAndModify: false });
            }
        }

        output = {
            success: true,
            message: "Updated Successfully",
            status: 200,
        };

    } catch (ex) {
        logger.logs(ex);
        saveErrorLog("updateClosedLoansStatus", apiPath, null, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};

module.exports.waveoffPenalty = async function (loanNos, installmentNos = null) {
    let output = {
        success: false,
        message: "Some Error Occurred",
        status: 200,
    };
    try {
        if (!IsNullEmpty(loanNos)) {
            const filter = { loanNo: { $in: loanNos } };
            if (!IsNullEmpty(installmentNos)) filter.installmentNumber = { $in: installmentNos };

            const fees = await LoanFees.find(filter);

            if (fees && fees.length > 0) {
                const today = new Date();
                const checkDate = String(today.getDate()) + "/" + String(today.getMonth() + 1) + "/" + String(today.getFullYear());
                const fromDate = getFromDate(checkDate);
                const toDate = getToDate(checkDate);

                for (let i = 0; i < loanNos.length; i++) {
                    let bulkWriteArray = [];
                    const loanFees = fees.filter((x) => x.loanNo == loanNos[i]);
                    if (loanFees && loanFees.length > 0) {
                        const requestedInstallments = await PaymentCollection.find({
                            loanNo: loanNos[i],
                            insertedOn: { $gte: fromDate, $lte: toDate },
                            channel: PAYMENT_CHANNEL.WALLET // WALLET CHANGES
                        }).select({ _id: 1, emiNumber: 1 });


                        for (let j = 0; j < loanFees.length; j++) {
                            let isExist = false;
                            if (requestedInstallments && requestedInstallments.length > 0) {
                                const installment = requestedInstallments.filter((x) => x.emiNumber == loanFees[j].installmentNumber);
                                if (installment && installment.length > 0) isExist = true;
                            }

                            if (!isExist) {
                                bulkWriteArray.push({
                                    updateOne: {
                                        filter: {
                                            _id: loanFees[j]._id
                                        },
                                        update: {
                                            $set: {
                                                isWaiveOff: true,
                                                updatedOn: Date.now()
                                            }
                                        }
                                    }
                                });
                            }
                        }
                    }

                    if (bulkWriteArray.length > 0)
                        await LoanFees.bulkWrite(bulkWriteArray);
                }

                output = {
                    success: true,
                    message: "Penalty Waved-Off Successfully",
                    status: 200,
                };

            } else {
                output = {
                    success: true,
                    message: "No Penalty for Waved-Off",
                    status: 200,
                };
            }
        } else {
            output.message = "loanNo's are mandatory";
        }
    } catch (ex) {
        logger.logs(ex);
        saveErrorLog("waveoffPenalty", apiPath, { loanNos, installmentNos }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};

module.exports.adjustInterestForForeclosure = async function (loanNo, userId) {
    let output = {
        success: false,
        message: "Some Error Occurred",
        status: 200,
    };
    try {
        let today = new Date();
        let toDate = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59);

        const emiDate = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 5, 30, 0, 0);

        const loanInstallments = await LoanInstallments.find({ loanNo: loanNo, status: "Pending", dueDate: { $gt: toDate } });
        let bulkWriteArray = [];

        let adjustedLoanInterest = 0;

        for (let i = 0; i < loanInstallments.length; i++) {
            if (IsNullEmpty(loanInstallments[i].isForeclosure) || loanInstallments[i].isForeclosure == false) {
                const installmentAmount = +(loanInstallments[i].installmentPrincipal + loanInstallments[i].moratoriumInterest).toFixed(2);

                adjustedLoanInterest += loanInstallments[i].installmentInterest;

                bulkWriteArray.push({
                    updateOne: {
                        filter: {
                            _id: loanInstallments[i]._id
                        },
                        update: {
                            $set: {
                                dueDate: emiDate,
                                installmentInterest: 0,
                                adjustedInterest: loanInstallments[i].installmentInterest,
                                interestOutstanding: 0,
                                installmentAmount: installmentAmount,
                                isForeclosure: true,
                                updatedOn: Date.now()
                            }
                        }
                    }
                });
            }
        }

        if (bulkWriteArray.length > 0) {
            await LoanInstallments.bulkWrite(bulkWriteArray);

            await LoanInfo.findOneAndUpdate({ loanNo: loanNo }, { $set: { isForeclosure: true, adjustedLoanInterest: +adjustedLoanInterest.toFixed(2), foreclosureBy: userId, foreclosureOn: Date.now() } });
        }

        output = {
            success: true,
            message: "Updated Successfully",
            status: 200,
        };

    } catch (ex) {
        logger.logs(ex);
        saveErrorLog("adjustInterestForForeclosure", apiPath, { loanNo }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
}