//Libraries
// ------------------------------------------------------------------------------------------------------------------------------------------
const mongoose = require("mongoose");
const debug = require("debug")("app:loan");
const apiPath = __dirname;
// ------------------------------------------------------------------------------------------------------------------------------------------

//Models
// ------------------------------------------------------------------------------------------------------------------------------------------
const { LoanInfo } = require("../../../models/ops/loan/loan");
const { PreApprovals, PartnerConfig, PartnerMaster } = require("../../../models/ops/partner/partner");
const { CampaignMaster } = require("../../../models/ops/campaign/campaign");
// ------------------------------------------------------------------------------------------------------------------------------------------

//External Methods
// ------------------------------------------------------------------------------------------------------------------------------------------
const { saveErrorLog } = require("../../../services/ops/common/masters");
const { getDMYFormattedDate, getYMDFormattedDate } = require("../../../services/ops/common/common");
const { getOfferDetails } = require("../../../services/ops/partner/borrowingPartner");
const loanServices = require("../../../services/ops/loan/loan");
const Logger = require("../../../middleware/logger");
const logger = new Logger();
// ------------------------------------------------------------------------------------------------------------------------------------------

//Internal WorkFlow Methods
// ------------------------------------------------------------------------------------------------------------------------------------------
async function computeLoanCashback(loanId, isSave = false) {
    let output = {
        success: false,
        message: "Some Error Occurred",
        discountAmount: null,
        discountText: null,
        bankText: null,
        discountType: null,
        computeType: null
    }

    let discountText = "";
    let bankText = "";
    let discountType = "";
    let computeType = "";

    try {
        logger.logs("loanId", loanId);
        logger.logs("isSave", isSave);
        const loanInfo = await LoanInfo.findById(loanId);
        if (loanInfo && loanInfo != null) {
            if (loanInfo.discount && loanInfo.discount != null && loanInfo.discount.campaignId && loanInfo.discount.campaignId != null && loanInfo.discount.discountAmount && loanInfo.discount.discountAmount != null) {
                const campaignMaster = await CampaignMaster.findById(loanInfo.discount.campaignId);
                logger.logs("inside 1");
                if (campaignMaster && campaignMaster != null) {
                    discountType = campaignMaster.discountOn;
                    computeType = campaignMaster.compute;
                    const partnerConfig = await PartnerConfig.findOne({ partnerId: loanInfo.partnerId });
                    const campaign = partnerConfig.campaign.filter((x) => x.isActive == true);
                    const startDate = new Date(campaignMaster.fromDate.getFullYear(), campaignMaster.fromDate.getMonth(), campaignMaster.fromDate.getDate(), 0, 0, 0);
                    const endDate = new Date(campaignMaster.toDate.getFullYear(), campaignMaster.toDate.getMonth(), campaignMaster.toDate.getDate(), 23, 59, 59);
                    discountText = campaign[0].discountText.replace("#discount", `₹${loanInfo.discount.discountAmount}`);
                    bankText = campaign[0].bankText.replace("#discount", `₹${loanInfo.discount.discountAmount}`);

                    discountText = discountText.replace("#from", getDMYFormattedDate(startDate, "/"));
                    discountText = discountText.replace("#to", getDMYFormattedDate(endDate, "/"));

                    output = {
                        success: true,
                        message: "Discount Already Provided",
                        discountAmount: loanInfo.discount.discountAmount,
                        discountText: discountText,
                        bankText: bankText,
                        discountType,
                        computeType
                    }
                } else {
                    output.message = "No Campaign Found";
                    output.success = true;
                }
            } else {
                console.log("inside 2");
                const partnerConfig = await PartnerConfig.findOne({ partnerId: loanInfo.partnerId });
                const partnerInfo =  await PartnerMaster.findOne({ _id: mongoose.Types.ObjectId(loanInfo.partnerId)})

                if(!partnerInfo.isWalkInCustomer){
                  
            
              
                const today = Date.now();

                const preApproval = await PreApprovals.findOne({
                    partnerId: mongoose.Types.ObjectId(loanInfo.partnerId),
                    merchantCode: loanInfo.merchantCode,
                    productType: loanInfo.productType.split("_")[0],
                    $or: [{ trxnId: loanInfo.trxnId }, { productId: loanInfo.trxnId }, { productId: loanInfo.productId }]
                }).sort({ insertedOn: -1 });

                // let preApproval = await PreApprovals.findOne({ partnerId: loanInfo.partnerId, merchantCode: loanInfo.merchantCode, productId: loanInfo.productId });

                // if (!preApproval)
                //   preApproval = await PreApprovals.findOne({ partnerId: loanInfo.partnerId, merchantCode: loanInfo.merchantCode, trxnId: loanInfo.trxnId });
                if (preApproval && preApproval != null ) {

                    if (partnerConfig && partnerConfig.campaign) {
                        const campaign = partnerConfig.campaign.filter((x) => x.isActive == true);
                        //logger.logs("campaign", campaign);
                        if (campaign.length > 0) {
                            const campaignMaster = await CampaignMaster.findById(campaign[0].campaignId);

                            if (campaignMaster && campaignMaster != null) {
                                discountType = campaignMaster.discountOn;
                                computeType = campaignMaster.compute;

                                //check product type
                                if (!campaignMaster.productType.includes(preApproval.productType)) {
                                    output.message = "Not applied to this Product Type";
                                    output.success = true;
                                    return output;
                                }

                                //check loan Type
                                if (!campaignMaster.loanType.includes(preApproval.loanType)) {
                                    output.message = "Not applied to this Loan Type";
                                    output.success = true;
                                    return output;
                                }

                                //check consumed no's
                                if (campaignMaster.maxNumber && campaignMaster.maxNumber != null && campaignMaster.consumedNumbers && campaignMaster.consumedNumbers != null && campaignMaster.consumedNumbers >= campaignMaster.maxNumber) {
                                    output.message = "Max Number Reached";
                                    output.success = true;
                                    return output;
                                }

                                //Check consumed Amount
                                if (campaignMaster.budgetedAmount && campaignMaster.budgetedAmount != null && campaignMaster.cosumedAmount && campaignMaster.cosumedAmount != null && campaignMaster.cosumedAmount >= campaignMaster.budgetedAmount) {
                                    output.message = "Budgeted Amount Reached";
                                    output.success = true;
                                    return output;
                                }

                                //check discountOn and discountType
                                if (campaignMaster.discountOn.toLowerCase() == "pf" || campaignMaster.discountOn.toLowerCase() == "roi") {
                                    if (campaignMaster.discountType.toLowerCase() == "per" || campaignMaster.discountType.toLowerCase() == "flat") {
                                        if (campaignMaster.discountOn.toLowerCase() == "roi" && campaignMaster.discountType.toLowerCase() == "flat") {
                                            output.message = "discount on roi with discount type flat is not allowed";
                                            return output;
                                        } else if (campaignMaster.discountOn.toLowerCase() == "roi" && campaignMaster.compute.toLowerCase() == "post") {
                                            output.message = "compute type post is not allowed for discount on roi";
                                            return output;
                                        }
                                    } else {
                                        output.message = "discount type is mandatory";
                                        return output;
                                    }
                                } else {
                                    output.message = "discount On is mandatory";
                                    return output;
                                }

                                //Apply Discount
                                const startDate = new Date(campaignMaster.fromDate.getFullYear(), campaignMaster.fromDate.getMonth(), campaignMaster.fromDate.getDate(), 0, 0, 0);

                                const endDate = new Date(campaignMaster.toDate.getFullYear(), campaignMaster.toDate.getMonth(), campaignMaster.toDate.getDate(), 23, 59, 59);

                                if (today >= startDate && today <= endDate) {
                                    let discountAmount = 0;
                                    // let pfAmount = 0;
                                    // let roiAmount = 0;
                                    // let discountInterestRate = loanInfo.interestRate;

                                    // if (campaignMaster.discountOn.toLowerCase() == "pf") {
                                    //     const charges = await loanServices.getLoanCharges(loanInfo.amount, loanInfo._id, preApproval._id, false);
                                    //     if (charges.success) pfAmount = charges.processingChargeAmt;
                                    //     else {
                                    //         output.message = charges.message;
                                    //         return output;
                                    //     }
                                    // } else if (campaignMaster.discountOn.toLowerCase() == "roi") {
                                    //     const tempModel = {
                                    //         productId: loanInfo.productId,
                                    //         loanAmount: loanInfo.amount,
                                    //         loanStartDate: getYMDFormattedDate(Date.now, "/")
                                    //     }

                                    //     const offer = await getOfferDetails(tempModel, false, null, true);
                                    //     if (offer.success == true) {
                                    //         roiAmount = offer.totalInterestPayable;
                                    //     } else {
                                    //         output.message = offer.message;
                                    //         return output;
                                    //     }
                                    // }

                                    // if (campaignMaster.discountType.toLowerCase() == "flat" && campaignMaster.discountOn.toLowerCase() == "pf") {
                                    //     discountAmount = (pfAmount > campaignMaster.value) ? campaignMaster.value : pfAmount;
                                    // } else if (campaignMaster.discountType.toLowerCase() == "per" && campaignMaster.discountOn.toLowerCase() == "pf") {
                                    //     discountAmount = Math.round((pfAmount * campaignMaster.value) / 100);
                                    // } else if (campaignMaster.discountType.toLowerCase() == "per" && campaignMaster.discountOn.toLowerCase() == "roi") {
                                    //     const tempModel = {
                                    //         productId: loanInfo.productId,
                                    //         loanAmount: loanInfo.amount,
                                    //         loanStartDate: getYMDFormattedDate(Date.now, "/")
                                    //     };
                                    //     discountInterestRate = (loanInfo.interestRate > campaignMaster.value) ? (loanInfo.interestRate - campaignMaster.value) : loanInfo.interestRate;
                                    //     const offer = await getOfferDetails(tempModel, false, discountInterestRate, true);
                                    //     if (offer.success == true) {
                                    //         discountAmount = roiAmount - offer.totalInterestPayable;
                                    //     } else {
                                    //         output.message = offer.message;
                                    //         return output;
                                    //     }
                                    // }
                                    const charges = await loanServices.getLoanCharges(loanInfo.amount, loanInfo._id, preApproval._id, true);
                                    logger.logs("charges", charges);
                                    discountAmount = charges.discountAmount;

                                    if (discountAmount && discountAmount > 0) {
                                        discountText = campaign[0].discountText.replace("#discount", `₹${discountAmount}`);
                                        bankText = campaign[0].bankText.replace("#discount", `₹${discountAmount}`);
                                        discountText = discountText.replace("#from", getDMYFormattedDate(startDate, "/"));
                                        discountText = discountText.replace("#to", getDMYFormattedDate(endDate, "/"));

                                        if (isSave == true) {
                                            logger.logs("updating");
                                            await CampaignMaster.findOneAndUpdate({ _id: campaignMaster._id },
                                                {
                                                    $inc: {
                                                        cosumedAmount: discountAmount,
                                                        consumedNumbers: 1
                                                    }
                                                }, { new: true, useFindAndModify: false }).lean();

                                            if (campaignMaster.discountOn.toLowerCase() == "roi") {
                                                loanInfo.discount = {
                                                    campaignId: campaignMaster._id,
                                                    discountAmount: discountAmount,
                                                    actualInterestRate: loanInfo.interestRate,
                                                    discountedInterestRate: campaignMaster.value,
                                                    isPaid: true,
                                                    paidOn: null,
                                                    utrNo: null,
                                                    insertedOn: Date.now(),
                                                    updateOn: null
                                                };
                                            } else {
                                                loanInfo.discount = {
                                                    campaignId: campaignMaster._id,
                                                    discountAmount: discountAmount,
                                                    actualInterestRate: loanInfo.interestRate,
                                                    discountedInterestRate: null,
                                                    isPaid: false,
                                                    paidOn: null,
                                                    utrNo: null,
                                                    insertedOn: Date.now(),
                                                    updateOn: null
                                                };
                                            }
                                            await loanInfo.save();
                                            output = {
                                                success: true,
                                                message: "Discount Provided",
                                                discountAmount: discountAmount,
                                                discountText: discountText,
                                                bankText: bankText,
                                                discountType,
                                                computeType
                                            }
                                        } else {
                                            logger.logs("discountText", discountText);
                                            output = {
                                                success: true,
                                                message: "fetched successfully",
                                                discountAmount: discountAmount,
                                                discountText: discountText,
                                                bankText: bankText,
                                                discountType,
                                                computeType
                                            }
                                        }
                                    }
                                } else {
                                    output.message = "No Campaign Found";
                                    output.success = true;
                                }
                            } else {
                                output.message = "No Campaign Found";
                                output.success = true;
                            }
                        } else {
                            output.message = "No Campaign is assigned";
                            output.success = true;
                        }
                    } else {
                        output.message = "No Campaign is assigned";
                        output.success = true;
                    }
                } else {
                    output.message = "Offer Not Found";
                }
            } else{
                output.message = "WalkInCustomer";
                output.success = true;
            }
            }
        } else output.message = "Loan Not Found";
    } catch (ex) {
        logger.logs("error", ex);
        saveErrorLog("computeLoanCashback", apiPath, { loanId, isSave }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex.toString());
    }

    return output;
}
module.exports.computeLoanCashback = computeLoanCashback;
// ------------------------------------------------------------------------------------------------------------------------------------------