//Libraries
// ------------------------------------------------------------------------------------------------------------------------------------------
const mongoose = require("mongoose");
let debug = require("debug")("app:disbursal");
const DateDiff = require("date-diff");
const IsNullEmpty = require("isnullemptyorwhitespace");
const { isDate, dateAdd } = require("dateadd");
const { date } = require("@hapi/joi");
const config = require("config");
const json2xls = require("json2xls");
const fs = require("fs");
const multer = require('multer');
const XLSX = require('xlsx');
const apiPath = __dirname;
const moment = require("moment");
// ------------------------------------------------------------------------------------------------------------------------------------------

//Models
// ------------------------------------------------------------------------------------------------------------------------------------------
const { LoanInfo } = require("../../../models/ops/loan/loan");
const { UserInfo, UserPartnerInfo } = require("../../../models/ops/registration/users");
const { PreApprovals, ProductPartnerConfig, ProductMaster } = require("../../../models/ops/partner/partner");
const { LedgerAccount, PartnerMaster, TrancheMaster } = require("../../../models/ops/partner/partner");
const { OutgoingPayments } = require("../../../models/ops/loan/postDisbursal");
const { DigioMandateGeneration } = require("../../../models/ops/digio/digioApi");
const { GatewayDisbursalRejectionCodes } = require("../../../models/ops/loan/disbursal");
// ------------------------------------------------------------------------------------------------------------------------------------------

//External Methods
// ------------------------------------------------------------------------------------------------------------------------------------------

const lendingPartnerService = require("../../../services/ops/partner/lendingPartner");
const postDisbursal = require("./postDisbursal");
const { saveErrorLog } = require("../../../services/ops/common/masters");
const { sendMail, sendExternalMail } = require("../../../services/ops/common/mailer");
const { getName, getFromDate, getToDate, getMailModel, getDMYFormattedDate, sleep, getDMYFormattedDateWithTime,
  pushToNotifications, getProductType } = require("../common/common");
const loanServices = require("./loan");
const { callBorrowingPartnerAPI } = require("../../../services/ops/partner/borrowingPartner");
const { excelDisbursal } = require("../disbursal/disbursal");
const { CampaignMaster } = require("../../../models/ops/campaign/campaign");
const { getSignedUrlToDownload } = require("../../../models/ops/aws/s3");
const Logger = require("../../../middleware/logger");
const logger = new Logger();
// ------------------------------------------------------------------------------------------------------------------------------------------

//Internal Methods
// ------------------------------------------------------------------------------------------------------------------------------------------
module.exports.getDisbursalList = async function (model) {
  try {
    let fromDate = getFromDate(model.fromDate);
    let toDate = getToDate(model.toDate);


    let query1 = {
      insertedOn: {
        $gte: fromDate,
        $lt: toDate,
      },
    };

    let query2 = { "partnerMaster.partnerType": { $regex: "Borrowing Partner", $options: "i" } };

    if (!IsNullEmpty(model.borrowingPartner) && model.borrowingPartner.toLowerCase() != "all") {
      query2["partnerMaster.partnerName"] = { $regex: model.borrowingPartner, $options: "i" };
    }

    if (!IsNullEmpty(model.lendingPartner) && model.lendingPartner.toLowerCase() != "all") {
      const userPartnerInfo = await UserPartnerInfo.findOne({ userId: mongoose.Types.ObjectId(model.lendingPartner) }).select({ partnerId: 1 });
      query1["lenderDetails.lenderPartnerId"] = mongoose.Types.ObjectId(userPartnerInfo.partnerId);
    }

    if (!IsNullEmpty(model.productId)) {
      query1["productId"] = { $regex: model.productId, $options: "i" };
    }

    if (!IsNullEmpty(model.loanNo)) {
      query1["loanNo"] = { $regex: model.loanNo, $options: "i" };
    }

    if (!IsNullEmpty(model.status) && model.status.toLowerCase() == "pending") {
      query1["status"] = { $in: ["Lender Assigned", "Initiated Fund Transfer", "Ready For Disbursal"] };
    } else if (!IsNullEmpty(model.status) && (model.status == "Initiated Fund Transfer" || model.status == "Ready For Disbursal")) {
      query1["status"] = model.status;
    } else if (!IsNullEmpty(model.status) && model.status.toLowerCase() == "disbursed") {
      query1["disbursalDetails"] = { $not: { $size: 0 } };
      query1["disbursalDetails.disbursedOn"] = { $ne: null };
    } else {
      query1["status"] = { $in: ["Loan In Progress", "Lender Assigned", "Initiated Fund Transfer", "Ready For Disbursal"] };
    }

    logger.logs("query1", query1);
    logger.logs("query2", query2);

    let reports = await LoanInfo.aggregate([
      {
        $match: query1,
      },
      {
        $lookup: {
          from: "UserInfo",
          localField: "userId",
          foreignField: "_id",
          as: "userInfo",
        },
      },
      {
        $unwind: "$userInfo"
      },
      {
        $lookup: {
          from: "UserPartnerInfo",
          let: { uid: "$userInfo._id", pid: "$partnerId", merchantCode: "$merchantCode" },
          pipeline: [
            { $match: { $expr: { $and: [{ $eq: ["$userId", "$$uid"] }, { $eq: ["$partnerId", "$$pid"] }, { $eq: ["$merchantCode", "$$merchantCode"] }] } } },
          ],
          as: "userPartnerInfo",
        }
      },
      { $unwind: "$userPartnerInfo" },
      {
        $lookup: {
          from: "PartnerMaster",
          localField: "userPartnerInfo.partnerId",
          foreignField: "_id",
          as: "partnerMaster",
        },
      },
      {
        $match: query2,
      },
      // {
      //   $lookup: {
      //     from: "UserInfo",
      //     localField: "lenderDetails.lenderId",
      //     foreignField: "_id",
      //     as: "lendingUser",
      //   },
      // },
      // {
      //   $lookup: {
      //     from: "UserPartnerInfo",
      //     foreignField: "userId",
      //     localField: "lendingUser._id",
      //     as: "ledningUserPartnerInfo"
      //   }
      // },
      // {
      //   $unwind: "$ledningUserPartnerInfo"
      // },
      {
        $lookup: {
          from: "PartnerMaster",
          foreignField: "_id",
          localField: "lenderDetails.lenderPartnerId",
          as: "lendingPartnerMaster"
        }
      },
      {
        $unwind: "$lendingPartnerMaster"
      },
      {
        $match: { "lendingPartnerMaster.partnerType": "Lending Partner" }
      },
      {
        $project: {
          _id: 1,
          productId: 1,
          loanNo: 1,
          merchantCode: 1,
          partnerId: 1,
          productType: 1,
          partnerName: "$partnerMaster.displayName",
          lendingPartnerName: "$lendingPartnerMaster.displayName",
          firstName: "$userInfo.userDetails.firstName",
          middleName: "$userInfo.userDetails.middleName",
          lastName: "$userInfo.userDetails.lastName",
          amount: 1,
          processingCharge: "$partnerMaster.processingCharge",
          disbursalDetails: 1,
          isActive: "$partnerMaster.isActive",
          partner: 1,
          status: 1,
          updatedOn: 1,
          isRedisbursal: 1,
          disbursalStatus: 1,
          disbursalError: 1
        },
      },
    ]).sort({ updatedOn: 1 });

    // logger.logs(reports);
    let records = [];
    if (reports.length > 0) {
      for (report of reports) {
        //logger.logs(report);
        let chargeInPer = 0;
        let charge = 0;
        let disbursalAmount = report.amount;
        const productType = report.productType.split("_")[0];

        if (productType != "LC") {
          const preApproval = await PreApprovals.findOne({
            partnerId: mongoose.Types.ObjectId(report.partnerId),
            merchantCode: report.merchantCode,
            productType: productType, $or: [{ trxnId: report.trxnId }, { productId: report.trxnId }, { productId: report.productId }]
          }).sort({ insertedOn: -1 });

          const charges = await loanServices.getLoanCharges(report.amount, report._id, preApproval._id);

          charge = charges.processingChargeAmt;

          disbursalAmount = report.amount - charge;
        }

        const productMaster = await ProductMaster.findOne({ product: productType });
        if (productMaster) {
          const productPartnerConfig = await ProductPartnerConfig.findOne({ partnerId: mongoose.Types.ObjectId(report.partnerId), productId: mongoose.Types.ObjectId(productMaster._id) });
          if (productPartnerConfig && productPartnerConfig.interestComputation == "Pre") {
            const schedule = await loanServices.emiSchedule(report._id, new Date(), false);
            const interest = schedule.data.filter((x) => x.interestAmount != 0).reduce(function (prev, cur) {
              return prev + cur.interestAmount;
            }, 0);
            disbursalAmount = disbursalAmount - interest;
          }
        }


        //logger.logs(report.disbursalDetails);
        //const disbursed=report.disbursalDetails.filter(x=>x.disbursedOn!=null);
        let record = {
          loanId: report._id,
          name: getName(report.firstName, report.middleName, report.lastName),
          partnerName: report.partnerName,
          lendingPartnerName: report.lendingPartnerName,
          productId: report.productId,
          loanNo: report.loanNo,
          loanAmount: report.amount,
          charge: charge,
          disbursalAmount: disbursalAmount,
          partnerStatus: report.isActive ? "Active" : "Deactive",
          loanStatus: report.status,
          disbursalStatus: !report.disbursalDetails ? "Pending" : (report.disbursalDetails.disbursedOn != null ? "Disbursed" : "Pending"),
          disbursedOn: report.disbursalDetails.disbursedOn,
          isRedisbursal: report.isRedisbursal,
          disbursalStatusInfo: report.disbursalStatus,
          disbursalError: report.disbursalError
        };
        records.push(record);
      }
    }

    //logger.logs(records);

    return {
      data: records,
      success: true,
      message: "Data fetched successfully",
      status: 200,
    };
  } catch (ex) {
    saveErrorLog("getDisbursalList", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    return {
      success: false,
      message: ex.message,
      status: 501,
    };
  }
};

function getCutOffTime(cutOffTimeConfig) {

  let cutOffTime = cutOffTimeConfig;
  let currentDate = new Date()
  return currentDate.setHours(cutOffTime.hours || 0, cutOffTime.minutes || 0, 0);
}

module.exports.getDisbursalListV2 = async function (model) {
  try {
    let fromDate = getFromDate(model.fromDate);
    let toDate = getToDate(model.toDate);
    let currentPage = model.currentPage;
    let limit = model.pageSize;
    let loans = [];
    let loansCount = 0;

    let filterQuery = {
      insertedOn: {
        $gte: fromDate,
        $lte: toDate,
      },
    };

    let sortQuery = { updatedOn: -1 };

    if (!IsNullEmpty(model.borrowingPartner) && model.borrowingPartner.toLowerCase() != "all") {

      filterQuery["partnerId"] = mongoose.Types.ObjectId(model.borrowingPartner);
    }

    if (!IsNullEmpty(model.lendingPartner) && model.lendingPartner.toLowerCase() != "all") {

      filterQuery["lenderDetails.lenderPartnerId"] = mongoose.Types.ObjectId(model.lendingPartner);
    }

    if (!IsNullEmpty(model.productId)) {

      filterQuery["productId"] = { $regex: model.productId, $options: "i" };
    }

    if (!IsNullEmpty(model.loanNo)) {

      filterQuery["loanNo"] = { $regex: model.loanNo, $options: "i" };
    }

    if (!IsNullEmpty(model.status) && model.status.toLowerCase() == "pending") {

      filterQuery["status"] = { $in: ["Lender Assigned"] };

    } else if (!IsNullEmpty(model.status) && model.status.toLowerCase() == "failed") {

      filterQuery["status"] = { $in: ["Initiated Fund Transfer", "Ready For Disbursal"] };
      filterQuery["isRedisbursal"] = true;

    } else if (!IsNullEmpty(model.status) && model.status.toLowerCase() == "processing") {

      filterQuery["status"] = { $in: ["Initiated Fund Transfer", "Ready For Disbursal"] };
      filterQuery["disbursalDetails.disbursedOn"] = { $eq: null };
      filterQuery["isRedisbursal"] = { $ne: true };
      //sortQuery = { updatedOn: -1 };
    } else if (!IsNullEmpty(model.status) && model.status.toLowerCase() == "disbursed") {

      filterQuery["disbursalDetails"] = { $not: { $size: 0 } };
      filterQuery["disbursalDetails.disbursedOn"] = { $ne: null };
      //filterQuery["isRedisbursal"] = { $ne: true };
      sortQuery = { updatedOn: -1 };

    } else {
      filterQuery["status"] = { $in: ["Loan In Progress", "Lender Assigned", "Initiated Fund Transfer", "Ready For Disbursal"] };
      sortQuery = { updatedOn: -1 };
    }

    loans = await LoanInfo.find(
      filterQuery,
      {
        _id: 1,
        userId: 1,
        productId: 1,
        loanNo: 1,
        merchantCode: 1,
        partnerId: 1,
        productType: 1,
        amount: 1,
        disbursalDetails: 1,
        lenderDetails: 1,
        partner: 1,
        status: 1,
        updatedOn: 1,
        isRedisbursal: 1,
        disbursalStatus: 1,
        disbursalError: 1
      }
    ).sort(sortQuery).lean()
    //.skip((currentPage - 1) * limit).limit(limit).lean();


    // loansCount = LoanInfo.find(
    //   {
    //     filterQuery
    //   }).count();

    let partnerIds = [];
    let userIds = [];

    for (loan of loans) {
      if (loan.partnerId) {
        partnerIds.push(mongoose.Types.ObjectId(loan.partnerId));
      }
      if (loan.lenderDetails && loan.lenderDetails.length && loan.lenderDetails[0].lenderPartnerId) {
        partnerIds.push(mongoose.Types.ObjectId(loan.lenderDetails[0].lenderPartnerId));
        loan.lenderPartnerId = loan.lenderDetails[0].lenderPartnerId
      }
      if (loan.userId) {
        userIds.push(loan.userId);
      }
    }

    let records = [];

    if (loans.length > 0) {

      let partnerMasterList = await PartnerMaster.find({ _id: { $in: partnerIds } }).lean();

      let userInfoList = await UserInfo.find({ _id: { $in: userIds } });

      //Hash
      userInfoHash = {};
      partnerMasterHash = {};

      partnerMasterList.map((partner) => {
        partnerMasterHash[partner._id] = partner;
      })

      userInfoList.map((user) => {
        userInfoHash[user._id] = user;
      })

      for (loan of loans) {

        let name = "";
        let lendingPartnerName = "";
        let partnerName = "";

        if (userInfoHash[loan.userId]) {
          let user = userInfoHash[loan.userId.toString()]
          name = getName(user.userDetails.firstName, user.userDetails.middleName, user.userDetails.lastName);
        }
        if (partnerMasterHash[loan.partnerId]) {
          partnerName = partnerMasterHash[loan.partnerId].partnerName;
        }
        if (partnerMasterHash[loan.lenderPartnerId]) {
          lendingPartnerName = partnerMasterHash[loan.lenderPartnerId].partnerName;
          lendingPartnerDisbursalMode = partnerMasterHash[loan.lenderPartnerId].disbursalMode;
        }

        let enterprisePartner = partnerMasterHash[loan.partnerId];
        let lendingPartner = partnerMasterHash[loan.lenderPartnerId];

        let isCutOffTimeExpired = false;

        if (enterprisePartner.cutOffTime && lendingPartner && lendingPartner.cutOffTime) {

          let enterprisePartnerCutOffTime = getCutOffTime(enterprisePartner.cutOffTime);
          let lendingPartnerCutOffTime = getCutOffTime(lendingPartner.cutOffTime);

          let currentDateTime = new Date()

          if (enterprisePartnerCutOffTime > currentDateTime || lendingPartnerCutOffTime > currentDateTime) {
            isCutOffTimeExpired = true
          }

        } else if (enterprisePartner.cutOffTime) {

          let enterprisePartnerCutOffTime = getCutOffTime(enterprisePartner.cutOffTime);
          let currentDateTime = new Date()

          if (currentDateTime > enterprisePartnerCutOffTime) {
            isCutOffTimeExpired = true
          }

        } else if (lendingPartner && lendingPartner.cutOffTime) {

          let lendingPartnerCutOffTime = getCutOffTime(lendingPartner.cutOffTime);

          let currentDateTime = new Date()

          if (currentDateTime > lendingPartnerCutOffTime) {
            isCutOffTimeExpired = true

          }
        } else if (config.get("disbursal.cutOffTime")) {

          let cutOffTime = getCutOffTime(config.get("disbursal.cutOffTime"));
          let currentDateTime = new Date()

          if (currentDateTime > cutOffTime) {
            isCutOffTimeExpired = true
          }
        }

        let record = {
          loanId: loan._id,
          name: name,
          partnerName: partnerName,
          lendingPartnerName: lendingPartnerName,
          productId: loan.productId,
          loanNo: loan.loanNo,
          loanAmount: loan.amount,
          charge: loan.disbursalDetails.totalChargesRecovered,
          disbursalAmount: loan.disbursalDetails.disbursalAmount,
          partnerStatus: loan.isActive ? "Active" : "Deactive",
          loanStatus: loan.status,
          disbursalStatus: !loan.disbursalDetails ? "Pending" : (loan.disbursalDetails.disbursedOn != null ? "Disbursed" : "Pending"),
          disbursedOn: loan.disbursalDetails.disbursedOn,
          isRedisbursal: loan.isRedisbursal,
          disbursalStatusInfo: loan.disbursalStatus,
          disbursalError: loan.disbursalError,
          isCutOffTimeExpired: isCutOffTimeExpired,
          disbursalMode: lendingPartnerDisbursalMode
        };
        records.push(record);
      }
    }

    return {
      data: records,
      success: true,
      message: "Data fetched successfully",
      status: 200,
    };
  } catch (ex) {
    saveErrorLog("getDisbursalListV2", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    return {
      success: false,
      message: ex.message,
      status: 501,
    };
  }
};

module.exports.saveDisbursalDetails = async function (req) {
  let output = {
    data: null,
    success: false,
    message: "Some Error Occurred",
    status: 501,
  };
  try {
    const model = req.body;
    const loanInfo = await LoanInfo.findById(model.loanId);
    if (IsNullEmpty(loanInfo)) {
      output.message = "Invalid loan Id";
      return output;
    } else {
      const disbursalDetails = loanInfo.disbursalDetails;

      if (!IsNullEmpty(disbursalDetails) && !IsNullEmpty(disbursalDetails.disbursedOn)) {
        output.message = "Already Disbursed. System does not allow to redisburse.";
        return output;
      } else {
        const partnerMaster = await PartnerMaster.findById(loanInfo.partnerId);

        if (!IsNullEmpty(partnerMaster)) {
          const charges = await loanServices.getLoanCharges(loanInfo.amount, loanInfo._id, null);
          logger.logs("charges", charges);
          if (charges.success) {
            logger.logs("inside success");

            const today = new Date();
            const dotParts = model.dateOfTransaction.split("/");
            const dateofTransaction = new Date(dotParts[2], dotParts[1] - 1, dotParts[0]);
            if (loanInfo.disbursalDetails == null) {
              const disbursalDetails = {
                bankReferenceNo: model.bankReferenceNo,
                paymentMode: model.paymentMode,
                dot: dateofTransaction,
                channel: "Manual",
                bankCode: model.bankCode,
                disbursedBy: req.user.id,
                disbursedOn: null,
                stampingCharges: charges.stampingCharge,
                gstCharges: charges.gstCharge,
                processingFee: charges.processingChargeAmt,
              };
              loanInfo.disbursalDetails = disbursalDetails;
            } else {
              loanInfo.disbursalDetails.bankReferenceNo = model.bankReferenceNo;
              loanInfo.disbursalDetails.paymentMode = model.paymentMode;
              loanInfo.disbursalDetails.dot = dateofTransaction;
              loanInfo.disbursalDetails.channel = "Manual";
              loanInfo.disbursalDetails.bankCode = model.bankCode;
              loanInfo.disbursalDetails.disbursedBy = req.user.id;
              loanInfo.disbursalDetails.disbursedOn = null;
              loanInfo.disbursalDetails.stampingCharges = charges.stampingCharge;
              loanInfo.disbursalDetails.gstCharges = charges.gstCharge;
              loanInfo.disbursalDetails.processingFee = charges.processingChargeAmt;
            }


            loanInfo.status = "Ready For Disbursal";
            loanInfo.flowDetails.push({
              changedBy: req.user.id,
              changedByRole: "Admin",
              Step: "Ready For Disbursal",
              insertedOn: Date.now()
            });


            await loanInfo.save();

            output = await disbursePartnerLoan(loanInfo._id, req.user.id);
          } else {
            saveErrorLog("saveDisbursalDetails", apiPath, req.body, charges.message);
            output.message = charges.message;
            return output;
          }
        } else {
          saveErrorLog("saveDisbursalDetails", apiPath, req.body, "Partner Name not found");
          output.message = "Partner Name not found";
          return output;
        }
      }
    }
  } catch (ex) {
    logger.logs("ex", ex);
    saveErrorLog("saveDisbursalDetails", apiPath, req.body, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    output.message = ex.message;
  }
  return output;
};

async function checkLenderBalance(lenderDetails) {
  let _success = true;
  let _message = "checking Lender Balance Completed";
  let _status = 200;
  try {
    logger.logs("cheking lender balance");
    for (let i = 0; i < lenderDetails.length; i++) {
      logger.logs("lender id : " + lenderDetails[i].lenderPartnerId);
      // const userPartnerInfo = await UserPartnerInfo.aggregate([
      //   { $match: { userId: mongoose.Types.ObjectId(lenderDetails[i].lenderId) } },
      //   {
      //     $lookup: {
      //       from: "PartnerMaster",
      //       foreignField: "_id",
      //       localField: "partnerId",
      //       as: "lendingPartnerMaster"
      //     }
      //   },
      //   {
      //     $unwind: "$lendingPartnerMaster"
      //   },
      //   {
      //     $match: { "lendingPartnerMaster.partnerType": "Lending Partner" }
      //   },
      //   {
      //     $project: {
      //       ledgerDetails: 1,
      //       displayName: "$lendingPartnerMaster.displayName"
      //     }
      //   }
      // ]);
      const partnerMaster = await PartnerMaster.findById(lenderDetails[i].lenderPartnerId);
      if (IsNullEmpty(partnerMaster)) {
        _message = "Lender with id " + String(lenderDetails[i].lenderPartnerId) + " not found";
        _status = 400;
        _success = false;
        i = lenderDetails.length + 1;
      } else {
        const ledgerDetails = partnerMaster.ledgerDetails;

        if (IsNullEmpty(ledgerDetails)) {
          _message = "Disbursal is not possible as avaiable balance with lender " + partnerMaster.displayName + " is null.";
          _status = 400;
          _success = false;
          i = lenderDetails.length + 1;
        } else if (((ledgerDetails.balance || 0) - (lenderDetails[i].amount || 0)) < 0) {
          _message = "Disbursal is not possible as avaiable balance with lender " + partnerMaster.displayName
            + " is " + String(ledgerDetails.balance) + " and invested in loan is " + String(lenderDetails[i].amount);
          _status = 400;
          _success = false;
          i = lenderDetails.length + 1;
        }
      }
    }
  } catch (ex) {
    saveErrorLog("checkLenderBalance", apiPath, lenderDetails, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    return {
      success: false,
      message: "issue occurred while checking lender balance",
      status: 400,
    };
  }

  return {
    success: _success,
    message: _message,
    status: _status,
  };
};

async function saveDisbursalTransactions(loanId, userId, loanNo, partnerId, merchantCode, productType, lenderDetails, trxnId, amount, dateOfTransaction) {
  return new Promise(async (resolve, reject) => {
    let _success = false;
    let _message = "Tranasactions insertion failed";
    let _status = 400;
    try {
      logger.logs("saving transactions for ledger");
      let name = "";
      let narration = "";
      let borrowerPartner;
      const loanInfo = await LoanInfo.findById(loanId);
      const user = await UserInfo.findById(userId);

      if (!IsNullEmpty(user)) {
        name = getName(user.userDetails.firstName, user.userDetails.middleName, user.userDetails.lastName);

        for (let i = 0; i < lenderDetails.length; i++) {
          // let lenderUserDetails = await UserPartnerInfo.aggregate([
          //   { $match: { userId: mongoose.Types.ObjectId(lenderDetails[i].lenderId) } },
          //   {
          //     $lookup: {
          //       from: "PartnerMaster",
          //       foreignField: "_id",
          //       localField: "partnerId",
          //       as: "lendingPartnerMaster"
          //     }
          //   },
          //   {
          //     $unwind: "$lendingPartnerMaster"
          //   },
          //   {
          //     $match: { "lendingPartnerMaster.partnerType": "Lending Partner" }
          //   },
          //   {
          //     $project: {
          //       _id: 1
          //     }
          //   }
          // ]);
          // const userPartnerInfo = await UserPartnerInfo.findById(lenderUserDetails[0]._id);

          const lendingPartner = await PartnerMaster.findById(lenderDetails[i].lenderPartnerId);

          borrowerPartner = null;

          borrowerPartner = await PartnerMaster.findById(partnerId);

          if (!borrowerPartner) {
            _success = false;
            _message = "Borrower Partner not found";
            _status = 400;
            i = lenderDetails.length + 1;
          } else {
            //logger.logs(borrowerPartner);
            // logger.logs("amount : ", amount);
            // logger.logs("partner id :", user.partnerDetails.partnerId);
            // logger.logs("processing charge : ", borrowerPartner.processingCharge);
            // logger.logs("partnerName", borrowerPartner.partnerName);

            if (IsNullEmpty(borrowerPartner.processingCharge)) {
              _success = false;
              _message = "processing charge of partner " + borrowerPartner.partnerName + " not found";
              _status = 400;
              i = lenderDetails.length + 1;
            } else {
              let chargeInPer = 0;

              narration = "Loan to " + user.userDetails.clientCode + "-" + loanNo + " " + name;

              let balance = lendingPartner.ledgerDetails.balance - lenderDetails[i].amount;

              let _LedgerAccount = new LedgerAccount({
                partnerId: mongoose.Types.ObjectId(lendingPartner._id),
                loanId: mongoose.Types.ObjectId(loanId),
                transactionDate: dateOfTransaction,
                depositDate: dateOfTransaction,
                utrNo: null,
                narration: narration,
                type: "loan Amount",
                creditAmt: null,
                debitAmt: lenderDetails[i].amount,
                holdAmt: lenderDetails[i].amount,
                balance: balance,
                insertedOn: Date.now(),
              });

              logger.logs("_LedgerAccount : ", _LedgerAccount);
              await _LedgerAccount.save();

              narration = "Processing Fee For " + user.userDetails.clientCode + "-" + loanNo;

              logger.logs("balance : ", balance);


              let disbursalCharge = 0;

              logger.logs("lendingPartner.processingFeeDisPer", lendingPartner.processingFeeDisPer);
              if (lendingPartner && lendingPartner.processingFeeDisPer) {
                disbursalCharge = loanInfo.disbursalDetails.processingFee - loanInfo.disbursalDetails.gstCharges;
              }

              let _LedgerAccount1 = new LedgerAccount({
                partnerId: mongoose.Types.ObjectId(lendingPartner._id),
                loanId: mongoose.Types.ObjectId(loanId),
                transactionDate: dateOfTransaction,
                depositDate: dateOfTransaction,
                utrNo: null,
                narration: narration,
                type: "Processing Fee",
                creditAmt: disbursalCharge,
                debitAmt: null,
                holdAmt: 0,
                balance: balance,
                insertedOn: Date.now()
              });

              await _LedgerAccount1.save();

              await PartnerMaster.update({ _id: mongoose.Types.ObjectId(lendingPartner._id) }, { "$inc": { "ledgerDetails.balance": -lenderDetails[i].amount } });
            }
          }
        }
      }
      logger.logs("saving transactions for ledger completed");
      resolve({
        success: _success,
        message: _message,
        status: _status,
      });
      return;
    } catch (ex) {
      logger.logs(ex);
      const model1 = { loanId: loanId, userId: userId, loanNo: loanNo, partnerId: partnerId, merchantCode: merchantCode, productType: productType, lenderDetails: lenderDetails, trxnId: trxnId, amount: amount, dateOfTransaction: dateOfTransaction };
      saveErrorLog("saveDisbursalTransactions", apiPath, model1, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
      reject(ex);
      return;
    }
  });
};

module.exports.getBorrowingPartners = async function (model) {
  try {
    const report = await PartnerMaster.find({
      partnerType: "Borrowing Partner", isActive: true
    })
      .select({ _id: 1, partnerName: 1, displayName: 1 })
      .sort({ partnerName: 1 });
    return {
      data: report,
      success: true,
      message: "Data fetched successfully",
      status: 200,
    };
  } catch (ex) {
    saveErrorLog("getBorrowingPartners", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    return {
      success: false,
      message: ex.message,
      status: 501,
    };
  }
};

module.exports.getActiveTrancheDetails = async function (loanId) {
  let output = {
    data: null,
    success: false,
    message: "Some Error Occurred",
    status: 501,
  };
  try {
    let loanInfo = await LoanInfo.findById(loanId);
    let disbursalDetails = {
      bankReferenceNo: "",
      doD: "",
      paymentMode: "",
      bankCode: ""
    };

    if (!IsNullEmpty(loanInfo.disbursalDetails)) {
      disbursalDetails.bankReferenceNo = !IsNullEmpty(loanInfo.disbursalDetails.bankReferenceNo) ? loanInfo.disbursalDetails.bankReferenceNo : "";
      if (!IsNullEmpty(loanInfo.disbursalDetails.dot)) {
        const dateOfDisbursal = new Date(loanInfo.disbursalDetails.dot);
        disbursalDetails.doD = dateOfDisbursal;
      }
      disbursalDetails.paymentMode = !IsNullEmpty(loanInfo.disbursalDetails.paymentMode) ? loanInfo.disbursalDetails.paymentMode : "";
      disbursalDetails.bankCode = !IsNullEmpty(loanInfo.disbursalDetails.bankCode) ? loanInfo.disbursalDetails.bankCode : "";
    }
    let model = null;
    if (!IsNullEmpty(loanInfo)) {
      // let userPartnerInfo = await UserPartnerInfo.aggregate([
      //   { $match: { userId: mongoose.Types.ObjectId(loanInfo.lenderDetails[0].lenderId) } },
      //   {
      //     $lookup: {
      //       from: "PartnerMaster",
      //       foreignField: "_id",
      //       localField: "partnerId",
      //       as: "lendingPartnerMaster"
      //     }
      //   },
      //   {
      //     $unwind: "$lendingPartnerMaster"
      //   },
      //   {
      //     $match: { "lendingPartnerMaster.partnerType": "Lending Partner" }
      //   },
      //   {
      //     $project: {
      //       _id: 1,
      //       partnerId: "$lendingPartnerMaster._id"
      //     }
      //   }
      // ]);
      const partnerMaster = await PartnerMaster.findById(loanInfo.lenderDetails[0].lenderPartnerId);

      if (!IsNullEmpty(partnerMaster)) {
        const eNachResponse = await eNachStatus(loanInfo);

        //const partnerId = userPartnerInfo[0].partnerId;
        let trancheMaster = await TrancheMaster.find({ partnerId: mongoose.Types.ObjectId(partnerMaster._id) }).sort({ no: -1 }).limit(1);
        if (IsNullEmpty(trancheMaster)) {
          const newTranche = await generateTranche(partnerMaster._id);
          if (newTranche.success) {
            trancheMaster = await TrancheMaster.findOne({ trancheNo: newTranche.trancheNo });
            if (!IsNullEmpty(trancheMaster)) {
              const availableAmount = trancheMaster.amount - trancheMaster.disbursedAmount;
              model = {
                trancheNo: trancheMaster.trancheNo,
                trancheAmount: trancheMaster.amount,
                disbursedAmount: trancheMaster.disbursedAmount,
                availableAmount: availableAmount,
                disbursalDetails: disbursalDetails
              };

              output = {
                data: model,
                success: true,
                message: "fetched successfully",
                status: 200,
                eNachResponse
              };
            }
          }
        }
        else {
          const availableAmount = trancheMaster[0].amount - trancheMaster[0].disbursedAmount;
          model = {
            trancheNo: trancheMaster[0].trancheNo,
            trancheAmount: trancheMaster[0].amount,
            disbursedAmount: trancheMaster[0].disbursedAmount,
            availableAmount: availableAmount,
            disbursalDetails: disbursalDetails
          };
        }

        output = {
          data: model,
          success: true,
          message: "fetched successfully",
          status: 200,
          eNachResponse
        };
      } else {
        output.message = "lender is not selected";
      }
    } else {
      output.message = "loan not found";
    }
  } catch (ex) {
    const model = {
      loanId: loanId
    }
    saveErrorLog("getActiveTrancheDetails", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    output.message = ex.message;
  }
  return output;
};

module.exports.addTranche = async function (loanId) {
  let output = {
    data: null,
    success: false,
    message: "Some Error Occurred",
    status: 501,
  };
  try {

    const loanInfo = await LoanInfo.findById(loanId);
    if (!IsNullEmpty(loanInfo)) {
      // const userPartnerInfo = await UserPartnerInfo.aggregate([
      //   { $match: { userId: mongoose.Types.ObjectId(loanInfo.lenderDetails[0].lenderId) } },
      //   {
      //     $lookup: {
      //       from: "PartnerMaster",
      //       foreignField: "_id",
      //       localField: "partnerId",
      //       as: "lendingPartnerMaster"
      //     }
      //   },
      //   {
      //     $unwind: "$lendingPartnerMaster"
      //   },
      //   {
      //     $match: { "lendingPartnerMaster.partnerType": "Lending Partner" }
      //   },
      //   {
      //     $project: {
      //       _id: 1,
      //       partnerId: "$lendingPartnerMaster._id"
      //     }
      //   }
      // ]);

      if (!IsNullEmpty(loanInfo.lenderDetails)) {
        let trancheMaster = await TrancheMaster.find({ partnerId: mongoose.Types.ObjectId(loanInfo.lenderDetails[0].lenderPartnerId) }).sort({ no: -1 }).limit(1);

        if (IsNullEmpty(trancheMaster) || (trancheMaster[0].disbursedAmount >= trancheMaster[0].amount)) {
          const newTranche = await generateTranche(loanInfo.lenderDetails[0].lenderPartnerId);
          if (newTranche.success) {
            trancheMaster = await TrancheMaster.findOne({ trancheNo: newTranche.trancheNo });
            if (!IsNullEmpty(trancheMaster)) {
              const availableAmount = trancheMaster.amount - trancheMaster.disbursedAmount;
              const model = {
                trancheNo: trancheMaster.trancheNo,
                trancheAmount: trancheMaster.amount,
                disbursedAmount: trancheMaster.disbursedAmount,
                availableAmount: availableAmount
              };

              output = {
                data: model,
                success: true,
                message: "fetched successfully",
                status: 200
              };
            }
          }
        }
        else {
          const availableAmount = trancheMaster[0].amount - trancheMaster[0].disbursedAmount;
          const model = {
            trancheNo: trancheMaster[0].trancheNo,
            trancheAmount: trancheMaster[0].amount,
            disbursedAmount: trancheMaster[0].disbursedAmount,
            availableAmount: availableAmount
          };

          output = {
            data: model,
            success: true,
            message: "fetched successfully",
            status: 200
          };
        }
      } else {
        output.message = "Lender Not Found in User Info";
      }

    }
    else {
      output.message = "Loan Not Found";
    }
  } catch (ex) {
    logger.logs(ex);
    const model = { loanId: loanId };
    saveErrorLog("addTranche", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    output.message = ex.message;
  }
  return output;
};

async function generateTranche(partnerId) {
  let no;
  let trancheNo;
  let trancheGenerated = false;
  let output = {
    trancheNo: null,
    success: false,
    message: "Some Error Occurred",
    status: 501,
  };
  try {
    logger.logs("partnerId is", partnerId);
    const partnerMaster = await PartnerMaster.findById(partnerId);

    if (!IsNullEmpty(partnerMaster)) {
      await TrancheMaster.updateMany({ partnerId: mongoose.Types.ObjectId(partnerId), isCurrent: true }, { isCurrent: false });
      do {
        let repository = await TrancheMaster.find({ partnerId: mongoose.Types.ObjectId(partnerId) })
          .sort({ no: -1 })
          .limit(1);

        if (!IsNullEmpty(repository)) {
          no = repository[0].no + 1;
        } else {
          no = 1;
        }
        trancheNo = partnerMaster.partnerCode + "_Tranche_" + no;
        const amount = config.get("keys.trancheAmount");
        const fldgAmount = (amount * 10) / 100;
        const trancheMaster = new TrancheMaster({
          partnerId: mongoose.Types.ObjectId(partnerId),
          no: no,
          trancheNo: trancheNo,
          amount: amount,
          disbursedAmount: 0,
          transactionDate: null,
          depositDate: null,
          fldgAmount: fldgAmount,
          expiryDate: null,
          isOpen: true,
          isCurrent: true,
          insertedOn: Date.now(),
          modifiedOn: Date.now()
        });

        await trancheMaster
          .save()
          .then(() => {
            trancheGenerated = true;

            output.success = true;
            output.message = "Tranche no generated successfully";
            output.status = 200;
            output.trancheNo = trancheNo;
          })
          .catch((err) => {
            const duplicate = err.toString().includes("duplicate key", 0);
            if (!duplicate) {
              trancheGenerated = true;
              output.trancheNo = null;
              output.success = false;
              output.message = err.toString();
              output.status = 501;
            }
          });
      } while (!trancheGenerated);

    } else {
      output.message = "Partner does not exists";
    }
  } catch (ex) {
    const model = { partnerId: partnerId };
    saveErrorLog("addTranche", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    output.message = ex.message;
  }

  return output;
};

module.exports.generateTranche = generateTranche;

async function eNachStatus(loan) {
  const authMode = loan.loanContractDetails && loan.loanContractDetails.authMode ? loan.loanContractDetails.authMode : "";

  if (loan.primaryCollectionType === "eNach") {
    const mandate = await DigioMandateGeneration.findOne({ loanIds: loan._id, mode: authMode }).sort({ createdOn: -1 });

    if (!mandate)
      return {
        eNachNo: "", isEnachRequired: true, eNachStatus: "not found", eNachErrorCode: "", eNachErrorMessage: "Mandate not found", proceed: false
      }
    else {
      let eNachErrorMessage = "";

      if (mandate.status.toLowerCase() !== "register_success")
        eNachErrorMessage = mandate.rejectReason ? mandate.rejectReason : "e-Mandate not yet registered";

      return {
        eNachNo: mandate.mandate_id, isEnachRequired: true, eNachStatus: mandate.status, eNachErrorCode: mandate.rejectCode, eNachErrorMessage: eNachErrorMessage, proceed: mandate.status.toLowerCase() == "register_success" ? true : false
      }
    }
  }
  else if (loan.secondaryCollectionType === "eNach") {
    const mandate = await DigioMandateGeneration.findOne({ loanIds: loan._id, mode: authMode }).sort({ createdOn: -1 });

    if (!mandate)
      return {
        eNachNo: "", isEnachRequired: true, eNachStatus: "not found", eNachErrorCode: "", eNachErrorMessage: "Mandate not found", proceed: false
      }
    else {
      let eNachErrorMessage = "";

      if (mandate.status.toLowerCase() !== "success" && mandate.status.toLowerCase() !== "register_success")
        eNachErrorMessage = mandate.rejectReason ? mandate.rejectReason : "umrn not yet recieved";

      return {
        eNachNo: mandate.mandate_id, isEnachRequired: true, eNachStatus: mandate.status, eNachErrorCode: mandate.rejectCode, eNachErrorMessage: eNachErrorMessage, proceed: (mandate.status.toLowerCase() == "register_success" || mandate.status.toLowerCase() == "success") ? true : false
      }
    }
  }
  else {
    return {
      eNachNo: "", isEnachRequired: false, eNachStatus: "", eNachErrorCode: "", eNachErrorMessage: "", proceed: true
    }
  }

};

module.exports.exportDisbursalFile = async function (loans, paymentMode) {
  let output = {
    filePath: null,
    success: false,
    message: "Some Error Occurred",
    status: 501,
  };

  try {
    let loanInfo = await LoanInfo.aggregate([
      {
        $match: { loanNo: { $in: loans } },
      },
      { $unwind: "$lenderDetails" },
      // {
      //   $lookup: {
      //     from: "UserInfo",
      //     localField: "lenderDetails.lenderId",
      //     foreignField: "_id",
      //     as: "userInfo",
      //   },
      // },
      // { $unwind: "$userInfo" },
      // {
      //   $lookup: {
      //     from: "UserPartnerInfo",
      //     foreignField: "userId",
      //     localField: "userInfo._id",
      //     as: "userPartnerInfo"
      //   }
      // },
      // {
      //   $unwind: "$userPartnerInfo"
      // },
      {
        $lookup: {
          from: "PartnerMaster",
          foreignField: "_id",
          localField: "lenderDetails.lenderPartnerId",
          as: "lendingPartnerMaster"
        }
      },
      {
        $unwind: "$lendingPartnerMaster"
      },
      {
        $match: { "lendingPartnerMaster.partnerType": "Lending Partner" }
      },
      {
        $lookup: {
          from: "PartnerMaster",
          localField: "partnerId",
          foreignField: "_id",
          as: "borrowingPartner",
        },
      },
      { $unwind: "$borrowingPartner" },
      {
        $group: {
          _id: "$_id",
          loanNo: { $first: "$loanNo" },
          amount: { $first: "$amount" },
          // userBankDetails: { $first: "$userInfo.bankDetails" },
          borrowingPartnerAccountDetails: { $first: "$disbursalBankDetails" },
          lendingPartnerAccountDetails: { $first: "$lendingPartnerMaster.bankAccounts" },
          borrowingPartnerProcessingCharge: { $first: "$borrowingPartner.processingCharge" },
          partnerId: { $first: "$partnerId" },
          merchantCode: { $first: "$merchantCode" },
          productType: { $first: "$productType" },
          trxnId: { $first: "$trxnId" },
          processingFeeDisPer: { $first: "$lendingPartnerMaster.processingFeeDisPer" },
        }
      }
    ]).allowDiskUse(true);


    if (!IsNullEmpty(loanInfo)) {
      const today = new Date(Date.now());
      const currentDate = getDMYFormattedDate(today, "-");
      let disbursalRecords = [];
      let exportedLoans = [];
      for (const loan of loanInfo) {
        const preApproval = await PreApprovals.findOne({
          partnerId: mongoose.Types.ObjectId(loan.partnerId),
          merchantCode: loan.merchantCode,
          productType: loan.productType.split("_")[0],
          $or: [{ trxnId: loan.trxnId }, { productId: loan.trxnId }, { productId: loan.productId }]
        }).sort({ insertedOn: -1 });

        let charge = 0;
        let disbursalCharge = 0;
        let disbursalAmount = loan.amount;
        let interestComputation = "Post";
        let preInterestCollected = 0;
        const productType = loan.productType.split("_")[0];

        if (productType != "LC") {
          if (!IsNullEmpty(preApproval)) {
            charge = preApproval.processingFee.replace("%", "").trim();
          } else {
            charge = loan.borrowingPartnerProcessingCharge;
          }
          //logger.logs("charge : ", charge);
          const charges = await loanServices.getLoanCharges(loan.amount, loan._id, preApproval._id);

          if (!charges.success) {
            output.message = charges.message;
            return output;
          }
          disbursalCharge = charges.totalCharges;
          disbursalAmount = disbursalAmount - disbursalCharge;
        }

        const productMaster = await ProductMaster.findOne({ product: productType });
        if (productMaster) {
          const productPartnerConfig = await ProductPartnerConfig.findOne({ partnerId: mongoose.Types.ObjectId(loan.partnerId), productId: mongoose.Types.ObjectId(productMaster._id) });
          if (productPartnerConfig && productPartnerConfig.interestComputation == "Pre") {
            interestComputation = "Pre";
            const schedule = await loanServices.emiSchedule(loan._id, new Date(), false);
            const interest = schedule.data.filter((x) => x.interestAmount != 0).reduce(function (prev, cur) {
              return prev + cur.interestAmount;
            }, 0);
            preInterestCollected = interest;
            disbursalAmount = disbursalAmount - interest;
          }
        }



        if (!IsNullEmpty(loan.lendingPartnerAccountDetails) && !IsNullEmpty(loan.borrowingPartnerAccountDetails)) {
          const lenderAccountDetails = loan.lendingPartnerAccountDetails.filter((x) => x.type == "Disbursal Escrow" && x.isActive == true);

          if (!IsNullEmpty(lenderAccountDetails) && !IsNullEmpty(loan.borrowingPartnerAccountDetails)) {

            if (disbursalCharge > 0) {
              //logger.logs("prefix", config.get("disbursal.charge.prefixCustomerRefNo"));
              //Charges Amount
              const disbursalRecord = {
                clientCode: config.get("disbursal.clientCode").toString(),
                debitAccountNo: lenderAccountDetails[0].no.toString(),
                transactionTypeCode: config.get("disbursal.charge.transactionTypeCode").toString(),
                valueDate: currentDate.toString(),
                amount: disbursalCharge.toString(),
                beneficiaryName: config.get("disbursal.charge.beneficaryName").toString(),
                beneficiaryAccountNo: config.get("disbursal.charge.beneficaryAccountNo").toString(),
                ifscCode: config.get("disbursal.charge.ifscCode").toString(),
                customerRefNo: config.get("disbursal.charge.prefixCustomerRefNo").toString() + loan.loanNo.toString() + config.get("disbursal.charge.postfixCutomerRefNo").toString(),
                beneficiaryEmailId: "",
                beneficiaryMobileNo: "",
                remarks: "",
                paymentType: paymentMode.toString(),
                purposeCode: config.get("disbursal.charge.purposeCode").toString(),
                beneficiaryAccountType: config.get("disbursal.beneficiaryAccountType").toString(),
                payableLocation: "",
                printBranchName: "",
                modeOfDelivery: "",
                transactionCurrency: config.get("disbursal.transactionCurrency").toString(),
                beneficiaryAdd1: "",
                beneficiaryAdd2: "",
                beneficiaryAdd3: "",
                beneficiaryAdd4: "",
                beneficiaryId: ""
              };

              disbursalRecords.push(disbursalRecord);
            }

            if (preInterestCollected > 0) {
              const disbursalRecord = {
                clientCode: config.get("disbursal.clientCode").toString(),
                debitAccountNo: lenderAccountDetails[0].no.toString(),
                transactionTypeCode: config.get("disbursal.charge.transactionTypeCode").toString(),
                valueDate: currentDate.toString(),
                amount: preInterestCollected.toString(),
                beneficiaryName: config.get("disbursal.charge.beneficaryName").toString(),
                beneficiaryAccountNo: config.get("disbursal.charge.beneficaryAccountNo").toString(),
                ifscCode: config.get("disbursal.charge.ifscCode").toString(),
                customerRefNo: config.get("disbursal.charge.prefixCustomerRefNo").toString() + loan.loanNo.toString() + config.get("disbursal.charge.postfixPreInterestCode").toString(),
                beneficiaryEmailId: "",
                beneficiaryMobileNo: "",
                remarks: "",
                paymentType: paymentMode.toString(),
                purposeCode: config.get("disbursal.charge.purposeCode").toString(),
                beneficiaryAccountType: config.get("disbursal.beneficiaryAccountType").toString(),
                payableLocation: "",
                printBranchName: "",
                modeOfDelivery: "",
                transactionCurrency: config.get("disbursal.transactionCurrency").toString(),
                beneficiaryAdd1: "",
                beneficiaryAdd2: "",
                beneficiaryAdd3: "",
                beneficiaryAdd4: "",
                beneficiaryId: ""
              };
              disbursalRecords.push(disbursalRecord);
            }
            //logger.logs("Charges", recordAmount);
            if (disbursalAmount > 0) {
              //Disbursal Amount
              const disbursalRecord = {
                clientCode: config.get("disbursal.clientCode").toString(),
                debitAccountNo: lenderAccountDetails[0].no.toString(),
                transactionTypeCode: config.get("disbursal.charge.transactionTypeCode").toString(),
                valueDate: currentDate.toString(),
                amount: disbursalAmount.toString(),
                beneficiaryName: loan.borrowingPartnerAccountDetails.accountName.toString(),
                beneficiaryAccountNo: loan.borrowingPartnerAccountDetails.accountNo.toString(),
                ifscCode: loan.borrowingPartnerAccountDetails.ifsc.toString(),
                customerRefNo: config.get("disbursal.amount.prefixCustomerRefNo").toString() + loan.loanNo.toString(),
                beneficiaryEmailId: "",
                beneficiaryMobileNo: "",
                remarks: "",
                paymentType: paymentMode.toString(),
                purposeCode: config.get("disbursal.amount.purposeCode").toString(),
                beneficiaryAccountType: config.get("disbursal.beneficiaryAccountType").toString(),
                payableLocation: "",
                printBranchName: "",
                modeOfDelivery: "",
                transactionCurrency: config.get("disbursal.transactionCurrency").toString(),
                beneficiaryAdd1: "",
                beneficiaryAdd2: "",
                beneficiaryAdd3: "",
                beneficiaryAdd4: "",
                beneficiaryId: ""
              };
              disbursalRecords.push(disbursalRecord);

              exportedLoans.push(loan.loanNo);
              //logger.logs("amount", record1);
            }

          } else {
            if (IsNullEmpty(lenderAccountDetails))
              output.message = "Lender Account Details not found for loan no " + loan.loanNo;
            else
              output.message = "Borrower Account Details not found for loan no " + loan.loanNo;
          }
        }
      }
      logger.logs("disbursalRecords", disbursalRecords);

      if (disbursalRecords.length > 0) {
        let currentMonth = today.getMonth() + 1;
        currentMonth = currentMonth < 9 ? '0' + currentMonth : currentMonth;

        let currentDay = today.getDate();
        currentDay = currentDay < 9 ? '0' + currentDay : currentDay;

        let hours = today.getHours();
        hours = hours < 9 ? '0' + hours : hours;

        let minutes = today.getMinutes();
        minutes = minutes < 9 ? '0' + minutes : minutes;

        let seconds = today.getSeconds();
        seconds = seconds < 9 ? '0' + seconds : seconds;

        const fileName = today.getFullYear() + currentMonth + currentDay + "_" + hours + minutes + seconds + "_Disb_Esc_Ins.xlsx";
        logger.logs("fileName", fileName);
        const filePath = config.get("exportToExcelPath.generalReports") + fileName;
        const mailResponse = await exportIDFC(disbursalRecords, "disbursal", fileName, filePath, null);

        logger.logs("mailResponse", mailResponse);
        if (mailResponse.success) {
          output.message = "Document created successfully";

          for (const loanNo of exportedLoans) {
            const loanInfo = await LoanInfo.findOne({ loanNo: loanNo });
            if (!IsNullEmpty(loanInfo)) {
              loanInfo.status = "Initiated Fund Transfer";
              loanInfo.flowDetails.push({
                Step: "Initiated Fund Transfer", insertedOn: Date.now()
              });
              await loanInfo.save();
            }
          }
          output = {
            filePath: filePath,
            success: true,
            message: "Disbursal report sent successfully on mail",
            status: 200,
          };
        }
      }
    } else {
      output = {
        filePath: null,
        success: true,
        message: "No record found",
        status: 200,
      };
    }
  }
  catch (ex) {
    const model = { loans: loans };
    saveErrorLog("exportDisbursalFile", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    output.message = ex.message;
  }
  return output;
};

module.exports.uploadDisbursalExcel = async function (req, res) {
  let output = {
    success: false,
    message: "Error Occurred",
    status: 200
  }
  return new Promise((resolve) => {
    try {
      let path = config.get("keys.tempPath");
      let fileOriginalName = "";
      let fileName = "";
      let filePath = "";
      let extension = "";

      let storage = multer.diskStorage({
        destination: function (req, file, cb) {
          cb(null, path);
        },
        filename: function (req, file, cb) {
          let i = 1;
          extension = file.originalname.substring(file.originalname.lastIndexOf('.') - 1 + 1);

          fileName = file.originalname.substring(0, file.originalname.lastIndexOf('.'));
          fileOriginalName = fileName + extension;
          filePath = path + "//" + fileName + extension;
          while (fs.existsSync(filePath)) {
            fileName = file.originalname.substring(0, file.originalname.lastIndexOf('.')) + "_" + i.toString();
            filePath = path + "//" + fileName + extension;
            i++;
          }
          cb(null, fileName + extension);
        }
      });

      let upload = multer({ storage: storage }).single('file')

      upload(req, res, async function (err) {
        if (err instanceof multer.MulterError) {
          output.message = err;
          resolve(output);
        } else if (err) {
          output.message = err;
          resolve(output);
        } else {
          const today = new Date(Date.now());
          let currentMonth = today.getMonth() + 1;
          currentMonth = currentMonth < 9 ? '0' + currentMonth : currentMonth;

          let currentDay = today.getDate();
          currentDay = currentDay < 9 ? '0' + currentDay : currentDay;

          let hours = today.getHours();
          hours = hours < 9 ? '0' + hours : hours;

          let minutes = today.getMinutes();
          minutes = minutes < 9 ? '0' + minutes : minutes;

          let seconds = today.getSeconds();
          seconds = seconds < 9 ? '0' + seconds : seconds;
          logger.logs("req.body.type", req.body.type);
          fileName = req.body.type + "_" + currentMonth + currentDay + hours + minutes + seconds;
          let NewfilePath = path + "//" + fileName + extension;
          let i = 1;


          while (!fs.existsSync(filePath)) {
            await sleep(5000);
          }

          while (fs.existsSync(NewfilePath)) {
            const NewfileName = fileName + "_" + i.toString();
            NewfilePath = path + "//" + NewfileName + extension;
            i++;
          }

          let response = null;
          fs.rename(filePath, NewfilePath, async () => {
            let index = 0;
            let workbook = XLSX.readFile(NewfilePath);
            let sheet_name_list = workbook.SheetNames;
            let xlData = XLSX.utils.sheet_to_json(workbook.Sheets[sheet_name_list[0]]);
            //console.log("xlData", xlData);
            for (let i = 0; i < xlData.length; i++) {
              for (const key in xlData[i]) {
                if (xlData[i][key] == "Customer Reference Number") {
                  index = i;
                }
              }
              if (index > 0) {
                i = xlData.length;
              }
            }

            let checkColumns = ["Customer Reference Number", "Status", "Payment Type", "RBI/UTR Reference Number"];
            let receivedColumns = [];
            for (column of checkColumns) {
              //logger.logs("column", column);
              for (const key in xlData[index]) {
                //logger.logs("xlData[index][key]", xlData[index][key]);
                if (column == xlData[index][key]) {
                  receivedColumns.push(key);
                }
              }
            }

            if (checkColumns.length != receivedColumns.length) {
              output.message = "Columns \"" + checkColumns.join(",") + "\" are mandatory.";
              resolve(output);
            }

            index = index + 1;
            let loans = [];
            for (let i = index; i < xlData.length; i++) {
              if (xlData[i][receivedColumns[0]] && xlData[i][receivedColumns[0]] != "" && xlData[i][receivedColumns[0]] != "- -" && xlData[i][receivedColumns[1]] == "Accepted By Bank") {
                //console.log("xlData", xlData[i]);
                let isUpdated = true;
                const utrNo = xlData[i][receivedColumns[3]];
                const paymentMode = xlData[i][receivedColumns[2]];
                const dot = new Date();

                if (req.body.type == "disbursalDiscountUpload") {
                  const loanNo = xlData[i][receivedColumns[0]].replace(config.get("disbursal.amount.prefixCustomerRefNo"), "").trim().replace("CB", "").trim();

                  loanInfo = await LoanInfo.findOne({ loanNo: loanNo, status: "Loan In Progress", "discount.isPaid": false });
                  if (!IsNullEmpty(loanInfo) && !IsNullEmpty(loanInfo.discount)) {
                    loanInfo.discount.isPaid = true;
                    loanInfo.discount.paidOn = Date.now();
                    loanInfo.discount.utrNo = utrNo;
                    await loanInfo.save();
                    isUpdated = true;

                    const userInfo = await UserInfo.findById(loanInfo.userId).select({ userDetails: 1 });

                    //Send WhatsApp Notification
                    const whatsAppResponse = await pushToNotifications(loanInfo.partnerId, loanInfo.merchantCode, loanInfo.productId, loanInfo.trxnId, loanInfo.userId, loanInfo._id, "WhatsApp", userInfo.userDetails.mobileNo[0].mobileNumber, "cashback_notification_eng", "loan", null, null, null, false, null);

                    //Call sendnotifications
                    if (whatsAppResponse.success)
                      await postDisbursal.sendNotifications(whatsAppResponse.data._id);
                  }
                } else {
                  const loanNo = xlData[i][receivedColumns[0]].replace(config.get("disbursal.amount.prefixCustomerRefNo"), "").trim();
                  let loanInfo = await LoanInfo.findOne({ loanNo: loanNo, status: "Initiated Fund Transfer" });

                  if (!IsNullEmpty(loanInfo)) {
                    loanInfo.disbursalDetails.bankReferenceNo = utrNo;
                    loanInfo.disbursalDetails.paymentMode = paymentMode;
                    loanInfo.disbursalDetails.dot = dot;
                    loanInfo.disbursalDetails.bankCode = "IDFC";
                    loanInfo.disbursalDetails.disbursedBy = req.user.id;
                    loanInfo.disbursalDetails.disbursedOn = null;
                    loanInfo.disbursalDetails.channel = "Excel";
                    loanInfo.status = "Ready For Disbursal";
                    loanInfo.flowDetails.push({
                      changedBy: req.user.id,
                      changedByRole: "Admin",
                      Step: "Ready For Disbursal",
                      insertedOn: Date.now()
                    });
                    await loanInfo.save();
                    loans.push(loanInfo._id);
                    isUpdated = true;
                  }
                }

                if (isUpdated) {
                  const outgoingPayments = await OutgoingPayments.findOne({ transactionType: "disbursal", customerRefNo: xlData[i][receivedColumns[0]] });
                  if (!IsNullEmpty(outgoingPayments)) {
                    outgoingPayments.utrReferenceNo = utrNo;
                    outgoingPayments.bankResponseFileName = fileOriginalName;
                    outgoingPayments.updatedOn = Date.now();
                    await outgoingPayments.save();
                  }
                }
              }
            }

            if (loans.length > 0) excelDisbursal(loans, req.user.id, fileOriginalName);

            output = {
              success: true,
              message: (req.body.type == "disbursalDiscountUpload") ? "File Uploaded Successfully" : "File will be processed and you will be informed via Email",
              status: 200
            }
            resolve(output);
          });
        }
      });
    }
    catch (ex) {
      //logger.logs(ex);
      saveErrorLog("uploadDisbursalExcel", apiPath, null, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
      output.message = ex.message;
      resolve(output);
    }
  });
};

module.exports.getDisbursalDetails = async function (loanId) {
  let output = {
    data: null,
    success: false,
    message: "Some Error Occurred",
    status: 501,
  };
  try {
    let loanInfo = await LoanInfo.findById(loanId);
    let disbursalDetails = {
      bankReferenceNo: "",
      doD: "",
      paymentMode: "",
      bankCode: ""
    }

    if (!IsNullEmpty(loanInfo.disbursalDetails)) {
      disbursalDetails.bankReferenceNo = !IsNullEmpty(loanInfo.disbursalDetails.bankReferenceNo) ? loanInfo.disbursalDetails.bankReferenceNo : "";
      if (!IsNullEmpty(loanInfo.disbursalDetails.dot)) {
        const dateOfDisbursal = new Date(loanInfo.disbursalDetails.dot);
        disbursalDetails.doD = dateOfDisbursal;
      }
      disbursalDetails.paymentMode = !IsNullEmpty(loanInfo.disbursalDetails.paymentMode) ? loanInfo.disbursalDetails.paymentMode : "";
      disbursalDetails.bankCode = !IsNullEmpty(loanInfo.disbursalDetails.bankCode) ? loanInfo.disbursalDetails.bankCode : "";
    }
    output = {
      data: disbursalDetails,
      success: true,
      message: "disbursal details fetched successfully",
      status: 200,
    };

  } catch (ex) {
    const model = {
      loanId: loanId
    };
    saveErrorLog("getDisbursalDetails", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    output.message = ex.message;
  }
  return output;
};

async function autoDisburseLoans(loans, disbursedBy, fileName = "") {
  let output = {
    success: false,
    message: "Error Occurred",
    status: 200
  }
  try {
    let disbursed = 0;
    let failed = 0;

    for (const loan of loans) {
      const disbursalReport = await disbursePartnerLoan(loan, disbursedBy);
      if (disbursalReport.success == false) failed++; else disbursed++;
    }
    let mailModel = getMailModel();

    mailModel.name = fileName;
    mailModel.date = await getDMYFormattedDateWithTime(Date.now(), "-");
    mailModel.number = loans.length;
    mailModel.number1 = disbursed;
    mailModel.number2 = failed;
    mailModel.to = config.get("disbursal.toEmail");
    mailModel.cc = config.get("disbursal.ccEmail");

    const mailResponse = await sendExternalMail(mailModel, "Disbursal mail to Admin", "Disbursal Mail");
    //autoDisbursePendingLoans(disbursedBy);
  } catch (ex) {
    const model = {
      fileName: fileName,
      loans: loans,
      disbursedBy: disbursedBy
    }
    saveErrorLog("autoDisburseLoans", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    output.message = ex.message;
    resolve(output);
  }
  return output;
};

async function disbursePartnerLoan(loanId, disbursedBy) {
  let output = {
    success: false,
    message: "Some Error Occurred",
    status: 501,
  };
  try {
    let loanInfo = await LoanInfo.findById(loanId);

    if (IsNullEmpty(loanInfo)) {
      output.message = "Invalid loan Id";
      return output;
    } else {
      const disbursalDetails = loanInfo.disbursalDetails;
      if (!IsNullEmpty(disbursalDetails) && !IsNullEmpty(disbursalDetails.disbursedOn)) {
        output.message = "Already Disbursed. System does not allow to redisburse.";
        return output;
      } else {
        //console.log("inside enach");
        const eNachResponse = await eNachStatus(loanInfo);

        if (eNachResponse.proceed) {
          const lenderDetails = loanInfo.lenderDetails;
          logger.logs("lenderDetails", lenderDetails);
          const lenderEmpty = IsNullEmpty(lenderDetails);
          logger.logs("lenderEmpty", lenderEmpty);
          if (!IsNullEmpty(lenderDetails)) {
            //const _checkLenderBalance = await checkLenderBalance(lenderDetails);

            // if (_checkLenderBalance.success) {
            const partnerMaster = await PartnerMaster.findById(loanInfo.partnerId);

            if (!IsNullEmpty(partnerMaster)) {
              const preApproval = await PreApprovals.findOne({
                partnerId: mongoose.Types.ObjectId(loanInfo.partnerId),
                merchantCode: loanInfo.merchantCode,
                productType: loanInfo.productType.split("_")[0],
                $or: [{ trxnId: loanInfo.trxnId }, { productId: loanInfo.trxnId }, { productId: loanInfo.productId }]
              }).sort({ insertedOn: -1 });

              let chargeInPer = 0;
              const productType = getProductType(loanInfo.productType);

              if (!IsNullEmpty(preApproval)) {
                chargeInPer = preApproval.processingFee.replace("%", "").trim();
              } else {
                chargeInPer = partnerMaster.processingCharge;
              }
              const charges = await loanServices.getLoanCharges(loanInfo.amount, loanInfo._id, preApproval._id);

              if (!charges.success) {
                output.message = charges.message;
                return output;
              }

              logger.logs("partnerMaster.partnerCode", partnerMaster.partnerCode);

              const apiVar = partnerMaster.partnerCode.toLowerCase() + ".loanDisbursedApis";
              let apis = config.get(apiVar);

              if (apis) {
                apis = await apis.filter((x) => x.productType == productType);
                logger.logs("apis", apis);
                if (apis.length > 0) {
                  for (let i = 0; i < apis.length; i++) {
                    logger.logs("i", i);
                    const borrowingParterRequestModel = apis[i].model != null ? JSON.parse(JSON.stringify(apis[i].model)) : {};
                    borrowingParterRequestModel.source = "application";
                    borrowingParterRequestModel.module = "loanDisbursed";
                    borrowingParterRequestModel.loanId = loanInfo._id;
                    borrowingParterRequestModel.partnerId = partnerMaster._id;
                    borrowingParterRequestModel.partnerCode = partnerMaster.partnerCode;
                    borrowingParterRequestModel.partnerDisplayName = partnerMaster.displayName;
                    borrowingParterRequestModel.apiType = apis[i].apiType;
                    borrowingParterRequestModel.isRetryOffline = apis[i].isRetryOffline;
                    borrowingParterRequestModel.url = apis[i].url;
                    borrowingParterRequestModel.bankReferenceNo = loanInfo.disbursalDetails.bankReferenceNo;

                    if (apis[i].isWaitForResponse) {
                      const borrowerAPIResponse = await callBorrowingPartnerAPI(borrowingParterRequestModel);
                      logger.logs("borrowerAPIResponse", borrowerAPIResponse);

                      if (!borrowerAPIResponse.success) {
                        i = apis.length;
                        const model = { loanId: loanId, disbursedBy: disbursedBy };
                        saveErrorLog("disbursePartnerLoan", apiPath, model, borrowerAPIResponse.message);
                        output.message = borrowerAPIResponse.message;
                        return output;
                      }
                    } else {
                      callBorrowingPartnerAPI(borrowingParterRequestModel);
                    }
                  }
                }
              }

              loanInfo = await LoanInfo.findById(loanId);

              loanInfo.disbursalDetails.custProcessingFee = productType == "LC" ? 0 : charges.processingChargeAmt;
              loanInfo.disbursalDetails.custGstCharges = productType == "LC" ? 0 : charges.gstCharge;
              loanInfo.disbursalDetails.stampingCharges = charges.stampingCharge;
              loanInfo.disbursalDetails.custTotalChargesRecovered = loanInfo.disbursalDetails.custProcessingFee + loanInfo.disbursalDetails.custGstCharges + loanInfo.disbursalDetails.stampingCharges;

              const today = new Date();
              const emiDate = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 5, 30, 0, 0);

              await saveDisbursalTransactions(loanInfo._id, loanInfo.userId, loanInfo.loanNo, loanInfo.partnerId, loanInfo.merchantCode, loanInfo.productType, loanInfo.lenderDetails, loanInfo.trxnId, loanInfo.amount, loanInfo.disbursalDetails.dot)
                .then(async (result) => {
                  //logger.logs(result);
                  logger.logs("inside success of save disbursal transaction");
                  let disbursalDate = loanInfo.productType == "DL_30" ? dateAdd("day", -1, emiDate) : emiDate;

                  const emis = await loanServices.emiSchedule(loanInfo._id, disbursalDate, true);

                  let interestPreRecovered = 0;
                  const productType = loanInfo.productType.split("_")[0];

                  const productMaster = await ProductMaster.findOne({ product: productType });
                  if (productMaster) {
                    const productPartnerConfig = await ProductPartnerConfig.findOne({ partnerId: mongoose.Types.ObjectId(loanInfo.partnerId), productId: mongoose.Types.ObjectId(productMaster._id) });
                    if (productPartnerConfig && productPartnerConfig.interestComputation == "Pre") {
                      interestPreRecovered = emis.data.filter((x) => x.interestAmount != 0).reduce(function (prev, cur) {
                        return prev + cur.interestAmount;
                      }, 0);
                    }
                  }

                  //logger.logs("emis are ", emis);
                  if (emis.success) {
                    logger.logs("inside emis success");
                    loanInfo.disbursalDetails.disbursedOn = Date.now();
                    loanInfo.status = "Loan In Progress";
                    loanInfo.flowDetails.push({
                      Step: "Loan In Progress",
                      changedBy: disbursedBy,
                      changedByRole: "Admin",
                      insertedOn: Date.now(),
                    });

                    if (loanInfo.discount && loanInfo.discount.campaignId && loanInfo.discount.campaignId != null) {
                      const campaignMaster = await CampaignMaster.findById(loanInfo.discount.campaignId);
                      if (campaignMaster && campaignMaster != null && campaignMaster.compute.toLowerCase() == "pre") {
                        loanInfo.discount.isPaid = true;
                        loanInfo.discount.paidOn = Date.now();
                        loanInfo.discount.utrNo = loanInfo.disbursalDetails.bankReferenceNo;
                      }
                    }
                    // let lendingPartner = await UserPartnerInfo.aggregate([
                    //   { $match: { userId: mongoose.Types.ObjectId(loanInfo.lenderDetails[0].lenderId) } },
                    //   {
                    //     $lookup: {
                    //       from: "PartnerMaster",
                    //       foreignField: "_id",
                    //       localField: "partnerId",
                    //       as: "lendingPartnerMaster"
                    //     }
                    //   },
                    //   {
                    //     $unwind: "$lendingPartnerMaster"
                    //   },
                    //   {
                    //     $match: { "lendingPartnerMaster.partnerType": "Lending Partner" }
                    //   },
                    //   {
                    //     $project: {
                    //       _id: 1,
                    //       userId: 1,
                    //       lenderId: "$lendingPartnerMaster._id",
                    //       partnerCode: "$lendingPartnerMaster.partnerCode",
                    //       isApiCall: "$lendingPartnerMaster.isApiCall",
                    //       displayName: "$lendingPartnerMaster.displayName",
                    //       processingFeeDisPer: "$lendingPartnerMaster.processingFeeDisPer"
                    //     }.lenderPartnerId
                    //   }
                    // ]);
                    const lendingPartner = await PartnerMaster.findById(loanInfo.lenderDetails[0].lenderPartnerId);

                    logger.logs("lendingPartner", lendingPartner);

                    if (!IsNullEmpty(lendingPartner)) {
                      if (productType == "LC") {
                        loanInfo.disbursalDetails.processingFee = 0;
                        loanInfo.disbursalDetails.gstCharges = 0;
                        loanInfo.disbursalDetails.totalChargesRecovered = loanInfo.disbursalDetails.stampingCharges;
                      } else {
                        const processingFeeDisAmount = loanServices.getProcessingFee(loanInfo.amount, lendingPartner.processingFeeDisPer);
                        loanInfo.disbursalDetails.processingFee = charges.processingChargeAmt - processingFeeDisAmount;
                        loanInfo.disbursalDetails.gstCharges = loanServices.getGstCharges(loanInfo.disbursalDetails.processingFee);
                        loanInfo.disbursalDetails.totalChargesRecovered = loanInfo.disbursalDetails.processingFee + loanInfo.disbursalDetails.gstCharges + loanInfo.disbursalDetails.stampingCharges;
                      }

                      if (lendingPartner.isApiCall == true) {
                        const lenderPartnerCode = lendingPartner.partnerCode.toLowerCase();
                        const apiVar = lenderPartnerCode + ".disbursementApis";
                        const disbursementApis = config.get(apiVar);

                        if (disbursementApis && disbursementApis.length > 0) {
                          for (let i = 0; i < disbursementApis.length; i++) {
                            const lenderApiModel = {
                              source: "application",
                              module: "disbursement",
                              loanId: loanInfo._id,
                              lenderId: lendingPartner._id,
                              partnerCode: lendingPartner.partnerCode,
                              partnerDisplayName: lendingPartner.displayName,
                              lendingPartnerUserId: lendingPartner.userId,
                              paymentMode: loanInfo.disbursalDetails.paymentMode,
                              referenceId: loanInfo.disbursalDetails.bankReferenceNo,
                              apiType: disbursementApis[i].apiType,
                              isRetryOffline: disbursementApis[i].isRetryOffline,
                              url: disbursementApis[i].url,
                              disbursementDate: new Date(Date.now())
                            };
                            logger.logs("apiType", disbursementApis[i].apiType);
                            if (disbursementApis[i].isWaitForResponse) {
                              const lendingApiResponse = await lendingPartnerService.callLendingPartnerAPI(lenderApiModel);
                              if (!lendingApiResponse.success) {
                                i = disbursementApis.length;
                                output.message = lendingApiResponse.message;
                                return output;
                              }
                            } else {
                              lendingPartnerService.callLendingPartnerAPI(lenderApiModel);
                            }
                          }
                        }
                      }

                      let trancheMaster = await TrancheMaster.find({ partnerId: mongoose.Types.ObjectId(lendingPartner._id) }).sort({ no: -1 }).limit(1);

                      if (IsNullEmpty(trancheMaster) || (trancheMaster[0].disbursedAmount >= trancheMaster[0].amount)) {
                        const newTranche = await generateTranche(lendingPartner._id);
                        if (newTranche.success) {
                          trancheMaster = await TrancheMaster.findOne({ trancheNo: newTranche.trancheNo });
                          if (!IsNullEmpty(trancheMaster)) {
                            trancheMaster.disbursedAmount = trancheMaster.disbursedAmount + loanInfo.lenderDetails[0].amount;
                            trancheMaster.modifiedOn = Date.now();
                            if (trancheMaster.disbursedAmount > trancheMaster.amount) {
                              if (IsNullEmpty(lendingPartner.fldgPer)) {
                                output.message = "Please set fldgPer for Lending Partner";
                                return output;
                              }
                              trancheMaster.amount = trancheMaster.disbursedAmount;
                              trancheMaster.fldgAmount = (trancheMaster.amount * lendingPartner.fldgPer) / 100;
                            }
                            await trancheMaster.save();
                            loanInfo.trancheNo = trancheMaster.trancheNo;
                          }
                        }
                      } else {
                        trancheMaster = await TrancheMaster.findById(trancheMaster[0]._id);
                        trancheMaster.disbursedAmount = trancheMaster.disbursedAmount + loanInfo.lenderDetails[0].amount;
                        trancheMaster.modifiedOn = Date.now();
                        if (trancheMaster.disbursedAmount > trancheMaster.amount) {
                          if (IsNullEmpty(lendingPartner.fldgPer)) {
                            output.message = "Please set fldgPer for Lending Partner";
                            return output;
                          }
                          trancheMaster.amount = trancheMaster.disbursedAmount;
                          trancheMaster.fldgAmount = (trancheMaster.amount * lendingPartner.fldgPer) / 100;
                        }
                        await trancheMaster.save();
                        loanInfo.trancheNo = trancheMaster.trancheNo;
                      }
                    }
                    await loanInfo.save();

                    //Send Mailer
                    let mailModel = getMailModel();

                    if (!IsNullEmpty(loanInfo.partnerId)) {
                      const partnerMaster = await PartnerMaster.findById(loanInfo.partnerId);
                      if (!IsNullEmpty(partnerMaster)) {
                        if (partnerMaster.partnerCode.toUpperCase() == "FUND") {
                          mailModel.number = loanInfo.loanNo;
                          //const sendMailToUser = await sendMail(loanInfo.userId, loanInfo._id, "Loan Application Emails to Borrower", "Loan Disbursed", mailModel, "User");
                          await sendDisbursalMail(mailModel, loanInfo._id, "Loan Application Emails to Borrower", "Loan Disbursed");
                        } else {
                          if (partnerMaster.partnerCode.toLowerCase() == "mpay") mailModel.cc = config.get("mpay.accountEmails");

                          mailModel.number = loanInfo.merchantCode;
                          await sendDisbursalMail(mailModel, loanInfo._id, "Loan Application Emails to Merchants of Partner", "Loan Disbursed");
                          //const sendMailToUser = await sendMail(loanInfo.userId, loanInfo._id, "Loan Application Emails to Merchants of Partner", "Loan Disbursed", mailModel, "User");
                        }
                      }
                    } else {
                      mailModel.number = loanInfo.loanNo;
                      await sendDisbursalMail(mailModel, loanInfo._id, "Loan Application Emails to Borrower", "Loan Disbursed");
                      //const sendMailToUser = await sendMail(loanInfo.userId, loanInfo._id, "Loan Application Emails to Borrower", "Loan Disbursed", mailModel, "User");
                    }

                    logger.logs("loanInfo.productType", loanInfo.productType);

                    if (loanInfo.productType == "DL_30") {
                      //Calling repayment for 1st Emi
                      const repaymentRequest = { partnerId: loanInfo.partnerId, loanId: loanId };
                      const repaymentResponse = await postDisbursal.requestLoanRepaymentForDailyLoans(repaymentRequest);
                      logger.logs("repaymentResponse", repaymentResponse);
                    }

                    output = {
                      data: null,
                      success: true,
                      message: "Disbursal details saved successfully",
                      status: 200,
                    };

                    const userRecord = await UserInfo.findById(loanInfo.userId).select({ userDetails: 1 });

                    if (productType === "LC" && !loanInfo.isParentLoan) {

                      if (userRecord.userDetails && userRecord.userDetails.mobileNo && userRecord.userDetails.mobileNo[0] && userRecord.userDetails.mobileNo[0].mobileNumber) {
                        //sendWhatsApp Notification
                        const whatsAppResponse = await pushToNotifications(loanInfo.partnerId, loanInfo.merchantCode, loanInfo.productId, loanInfo.trxnId,
                          loanInfo.userId, loanInfo._id, "WhatsApp", userRecord.userDetails.mobileNo[0].mobileNumber, "autopay_setup_merchant_eng", "loan", null, null, null, false, null);

                        //Call sendnotifications
                        if (whatsAppResponse.success)
                          await postDisbursal.sendNotifications(whatsAppResponse.data._id);
                      }
                    } else {
                      const whatsAppResponse = await pushToNotifications(loanInfo.partnerId, loanInfo.merchantCode, loanInfo.productId, loanInfo.trxnId,
                        loanInfo.userId, loanInfo._id, "WhatsApp", userRecord.userDetails.mobileNo[0].mobileNumber, "loan_disbursal_eng", "loan", null, null, null, false, null);

                      //Call sendnotifications
                      if (whatsAppResponse.success)
                        await postDisbursal.sendNotifications(whatsAppResponse.data._id);
                    }

                    return output;
                  } else {
                    const model = { loanId: loanInfo._id, disbursalDate, isSave: true };
                    saveErrorLog("disbursePartnerLoan", apiPath, model, emis.message);
                    logger.logs("inside emis failure");
                    output.message = emis.message;
                    return output;
                  }
                })
                .catch((err) => {
                  const model = { loanId: loanId, disbursedBy: disbursedBy };
                  saveErrorLog("disbursePartnerLoan", apiPath, model, err);
                  logger.logs("inside failure of save disbursal transaction");
                  logger.logs("errorr is : ", err);
                  output.message = err;
                  return output;
                });

            } else {
              const model = { loanId: loanId, disbursedBy: disbursedBy };
              saveErrorLog("disbursePartnerLoan", apiPath, model, "Partner Name not found");
              output.message = "Partner Name not found";
              return output;
            }
            // } else {
            //   const model = { loanId: loanId, disbursedBy: disbursedBy };
            //   saveErrorLog("disbursePartnerLoan", apiPath, model, _checkLenderBalance.message);
            //   output.message = _checkLenderBalance.message;
            //   return output;
            // }
          } else {
            const model = { loanId: loanId, disbursedBy: disbursedBy };
            saveErrorLog("disbursePartnerLoan", apiPath, model, "No Lender is selected. Please select the lender.");
            output.message = "No Lender is selected. Please select the lender.";
            return output;
          }
        } else {
          const model = { loanId: loanId, disbursedBy: disbursedBy };
          saveErrorLog("disbursePartnerLoan", apiPath, model, eNachResponse.eNachErrorMessage);
          output = {
            data: eNachResponse.eNachErrorCode,
            success: eNachResponse.proceed,
            message: eNachResponse.eNachErrorMessage,
            status: 200,
          };
          return output;
        }
      }
    }
  } catch (ex) {
    logger.logs("ex", ex);
    const model = {
      loanId: loanId,
      disbursedBy: disbursedBy
    }
    saveErrorLog("disbursePartnerLoan", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    output.message = ex.message;
  }

  return output;
};

async function autoDisbursePendingLoans(disbursedBy = null) {
  let output = {
    success: false,
    message: "Some Error Occurred",
    status: 501,
  };
  try {
    const loanInfo = await LoanInfo.find({ status: "Ready For Disbursal" }).select({ _id: 1, loanNo: 1, disbursalDetails: 1 });
    for (const loan of loanInfo) {
      disbursedBy = disbursedBy ? disbursedBy : loan.disbursalDetails.disbursedBy;
      const disbursalReport = await disbursePartnerLoan(loan._id, disbursedBy);
    }
    output = {
      success: true,
      message: "Tried disbursing Pending Loans",
      status: 200,
    };
  } catch (ex) {
    saveErrorLog("autoDisbursePendingLoans", apiPath, null, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    output.message = ex.message;
  }

  return output;
};
module.exports.autoDisbursePendingLoans = autoDisbursePendingLoans;

module.exports.disburseLoans = async function (req) {
  let output = {
    success: false,
    message: "Some Error Occurred",
    status: 501,
  };

  try {
    let disbursed = 0;
    let failed = 0;
    if (!IsNullEmpty(req.body.loans)) {
      const loanInfo = await LoanInfo.find({ loanNo: { $in: req.body.loans }, status: "Ready For Disbursal" }).select({ _id: 1, loanNo: 1 });
      if (!IsNullEmpty(loanInfo) && loanInfo.length > 0) {
        for (let i = 0; i < loanInfo.length; i++) {
          //console.log(`loans ${loanInfo[i]} and disbursed by ${req.user.id}`);
          const disbursalReport = await disbursePartnerLoan(loanInfo[i]._id, req.user.id);

          if (disbursalReport.success == false) disbursed++; else failed++;
        }
        output = {
          success: true,
          message: `${disbursed} loans disbursed successfully out of ${loanInfo.length}`,
          status: 200,
        };
      } else {
        output.message = "No Loans Found in Ready For Disbursal State";
      }
    } else {
      output.message = "Please provide Loans";
    }

  } catch (ex) {
    const model = {
      loans: loans
    }
    saveErrorLog("disburseLoans", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    output.message = ex.message;
    resolve(output);
  }
  return output;
};

async function exportIDFC(records, type, fileName, filePath, name = "Team", isSave = true, isSendMail = true) {
  let output = {
    success: false,
    message: "Some Error Occurred",
    status: 501,
  };
  try {
    if (records.length > 0) {
      let exportRecords = [];
      for (const record of records) {
        const excelRecord = {};
        excelRecord["Client Code"] = record.clientCode.toString();
        excelRecord["Debit account no"] = record.debitAccountNo.toString();
        excelRecord["Transaction type code"] = record.transactionTypeCode.toString();
        excelRecord["Value date"] = record.valueDate.toString();
        excelRecord["Penalty"] = record.penalty.toString();
        excelRecord["Amount"] = record.amount.toString();
        excelRecord["Beneficary Name"] = record.beneficiaryName.toString();
        excelRecord["Beneficary Accunt no"] = record.beneficiaryAccountNo.toString();
        excelRecord["IFSC code"] = record.ifscCode.toString();
        excelRecord["Customer Ref no"] = record.customerRefNo.toString();
        excelRecord["Beneficary email id"] = record.beneficiaryEmailId.toString();
        excelRecord["Beneficiary mobile no"] = record.beneficiaryMobileNo.toString();
        excelRecord["Remarks"] = record.remarks.toString();
        excelRecord["Payment Type"] = record.paymentType.toString();
        excelRecord["Purpose code"] = record.purposeCode.toString();
        excelRecord["Bene a / c type"] = record.beneficiaryAccountType.toString();
        excelRecord["Payable Location"] = record.payableLocation.toString();
        excelRecord["Print branch name"] = record.printBranchName.toString();
        excelRecord["Mode of delivery"] = record.modeOfDelivery.toString();
        excelRecord["Transaction currency"] = record.transactionCurrency.toString();
        excelRecord["BENE_ADD1"] = record.beneficiaryAdd1.toString();
        excelRecord["BENE_ADD2"] = record.beneficiaryAdd2.toString();
        excelRecord["BENE_ADD3"] = record.beneficiaryAdd3.toString();
        excelRecord["BENE_ADD4"] = record.beneficiaryAdd4.toString();
        excelRecord["Beneficiary ID"] = record.beneficiaryId.toString();

        exportRecords.push(excelRecord);

        const outgoingPayments = new OutgoingPayments({
          transactionType: type,
          clientCode: record.clientCode.toString() == "" ? null : record.clientCode.toString(),
          debitAccountNo: record.debitAccountNo.toString() == "" ? null : record.debitAccountNo.toString(),
          transactionTypeCode: record.transactionTypeCode.toString() == "" ? null : record.transactionTypeCode.toString(),
          valueDate: record.valueDate.toString() == "" ? null : record.valueDate.toString(),
          penalty: record.penalty.toString() == "" ? null : record.penalty.toString(),
          amount: record.amount.toString() == "" ? null : record.amount.toString(),
          beneficiaryName: record.beneficiaryName.toString() == "" ? null : record.beneficiaryName.toString(),
          beneficiaryAccountNo: record.beneficiaryAccountNo.toString() == "" ? null : record.beneficiaryAccountNo.toString(),
          ifscCode: record.ifscCode.toString() == "" ? null : record.ifscCode.toString(),
          customerRefNo: record.customerRefNo.toString() == "" ? null : record.customerRefNo.toString(),
          beneficiaryEmailId: record.beneficiaryEmailId.toString() == "" ? null : record.beneficiaryEmailId.toString(),
          beneficiaryMobileNo: record.beneficiaryMobileNo.toString() == "" ? null : record.beneficiaryMobileNo.toString(),
          remarks: record.remarks.toString() == "" ? null : record.remarks.toString(),
          paymentType: record.paymentType.toString() == "" ? null : record.paymentType.toString(),
          purposeCode: record.purposeCode.toString() == "" ? null : record.purposeCode.toString(),
          beneficiaryAccountType: record.beneficiaryAccountType.toString() == "" ? null : record.beneficiaryAccountType.toString(),
          payableLocation: record.payableLocation.toString() == "" ? null : record.payableLocation.toString(),
          printBranchName: record.printBranchName.toString() == "" ? null : record.printBranchName.toString(),
          modeOfDelivery: record.modeOfDelivery.toString() == "" ? null : record.modeOfDelivery.toString(),
          transactionCurrency: record.transactionCurrency.toString() == "" ? null : record.transactionCurrency.toString(),
          beneficiaryAdd1: record.beneficiaryAdd1.toString() == "" ? null : record.beneficiaryAdd1.toString(),
          beneficiaryAdd2: record.beneficiaryAdd2.toString() == "" ? null : record.beneficiaryAdd2.toString(),
          beneficiaryAdd3: record.beneficiaryAdd3.toString() == "" ? null : record.beneficiaryAdd3.toString(),
          beneficiaryAdd4: record.beneficiaryAdd4.toString() == "" ? null : record.beneficiaryAdd4.toString(),
          beneficiaryId: record.beneficiaryId.toString() == "" ? null : record.beneficiaryId.toString(),
          fileName: fileName,
          bankResponseFileName: null,
          utrReferenceNo: null,
          insertedOn: Date.now(),
          updatedOn: Date.now()
        });

        if (isSave === true) {
          await outgoingPayments.save();
        }
      }
      //logger.logs("exportRecords", exportRecords.length);
      let xls = json2xls(exportRecords);
      fs.writeFileSync(filePath, xls, "binary");
      logger.logs("file exported to path ", filePath);
      if (isSendMail === true) {

        let mailModel = getMailModel();
        mailModel.attachmentName = fileName;
        mailModel.attachmentPath = filePath;
        mailModel.name = name;

        let mailResponse = null;
        if (type == "disbursal") {
          mailModel.salutation = "Team";
          mailModel.to = config.get("disbursal.toEmail");
          mailModel.cc = config.get("disbursal.ccEmail");
          mailModel.date = getDMYFormattedDateWithTime(Date.now(), "-");
          mailResponse = await sendExternalMail(mailModel, "Disbursal", "Disbursal Report");
        } else if (type == "payout") {
          mailModel.to = config.get("payOut.toEmail");
          mailModel.cc = config.get("payOut.ccEmail");
          mailResponse = await sendExternalMail(mailModel, "Pay Out", "Pay Out EDI");
        }

        if (mailResponse.success) {
          fs.unlink(filePath, function () {
            logger.logs("file deleted successfully");
          });

          output = {
            success: true,
            message: "Send Successfully",
            status: 200,
          };
        } else {
          output = {
            success: false,
            message: mailResponse.message,
            status: 501,
          };
        }
      } else {
        output = {
          success: true,
          message: "Send Successfully",
          status: 200,
        };
      }
    } else {
      output = {
        success: false,
        message: "No records found",
        status: 501,
      };
    }
  } catch (ex) {
    logger.logs("error", ex);
    saveErrorLog("exportIDFC", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    output.message = ex.message;
  }
  return output;
};
module.exports.exportIDFC = exportIDFC;

async function sendDisbursalMail(mailModel, loanId, category, subcategory) {
  try {

    const loan = await LoanInfo.findById(loanId).select({ _id: 1, loanNo: 1, userId: 1, loanContractDetails: 1 });

    mailModel.attachmentName = "loanContract_" + loan.loanNo + ".pdf";

    // const proofImagePath = config.get("keys.proofImages") + "/" + loan.userId + "/LoanContract";

    // mailModel.attachmentPath = proofImagePath + "/loanContract_" + loan.loanNo + ".pdf";

    let signUrl = getSignedUrlToDownload({ url: loan.loanContractDetails.contractFile })

    mailModel.attachmentPathHref = signUrl;

    if (loan.loanContractDetails.kfsFile) {
      const kfsUrl = getSignedUrlToDownload({ url: loan.loanContractDetails.kfsFile });

      if (kfsUrl) {
        mailModel.attachmentName1 = "kfs_" + loan.loanNo + ".pdf";
        mailModel.attachmentPathHref1 = kfsUrl;
      }
    }

    //Send email
    await sendMail(
      loan.userId,
      loan._id,
      category,
      subcategory,
      mailModel,
      "User"
    );

    return {
      data: "",
      success: true,
      message: "Contract sent successfully"
    }
  }
  catch (ex) {
    saveErrorLog("mailLoanContract", apiPath, { mailModel, loanId }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);

    return {
      data: "",
      success: false,
      message: ex.message
    }
  }
};

module.exports.exportDiscountFile = async function (model) {
  let output = {
    success: false,
    message: "Some Error Occurred",
    status: 501,
  };
  try {
    const today = new Date(Date.now());
    const currentDate = getDMYFormattedDate(today, "-");
    let toDate = new Date(Date.now());
    if (!IsNullEmpty(model.toDate)) {
      const tempToDate = new Date(model.toDate + 'T00:00:43.496+00:00');
      toDate = new Date(tempToDate.getFullYear(), tempToDate.getMonth(), tempToDate.getDate(), 23, 59, 59, 0);
    }

    const to_date = new Date(toDate.getFullYear(), toDate.getMonth(), toDate.getDate(), 23, 59, 59, 0);

    const from_date = new Date(dateAdd("day", -1, to_date));

    //logger.logs("fromDate", from_date);
    //logger.logs("toDate", to_date);
    let query = { status: "Loan In Progress", "disbursalDetails.disbursedOn": { $gte: from_date, $lt: to_date }, discount: { $exists: true }, "discount.isPaid": false };

    if (!IsNullEmpty(model.excludePartners)) {
      query["partnerId"] = { $nin: model.excludePartners }
    }

    if (!IsNullEmpty(model.includePartners)) {
      query["partnerId"] = { $in: model.excludePartners }
    }
    // logger.logs("query", query);
    const loanInfo = await LoanInfo.aggregate([
      {
        $match: query
      },
      {
        $lookup: {
          from: "UserInfo",
          localField: "userId",
          foreignField: "_id",
          as: "userInfo",
        }
      },
      { $unwind: "$userInfo" },
      {
        $lookup: {
          from: "UserPartnerInfo",
          let: { uid: "$userInfo._id", pid: "$partnerId", merchantCode: "$merchantCode" },
          pipeline: [
            { $match: { $expr: { $and: [{ $eq: ["$userId", "$$uid"] }, { $eq: ["$partnerId", "$$pid"] }, { $eq: ["$merchantCode", "$$merchantCode"] }] } } },
          ],
          as: "userPartnerInfo",
        }
      },
      { $unwind: "$userPartnerInfo" },
      {
        $project: {
          _id: 1,
          loanNo: 1,
          discount: 1,
          bankDetails: "$userPartnerInfo.bankDetails"
        }
      },
    ]);
    //logger.logs("loanInfo", loanInfo);
    if (loanInfo.length > 0) {
      let exportRecords = [];
      let currentMonth = today.getMonth() + 1;
      currentMonth = currentMonth < 9 ? '0' + currentMonth : currentMonth;

      let currentDay = today.getDate();
      currentDay = currentDay < 9 ? '0' + currentDay : currentDay;

      let hours = today.getHours();
      hours = hours < 9 ? '0' + hours : hours;

      let minutes = today.getMinutes();
      minutes = minutes < 9 ? '0' + minutes : minutes;

      let seconds = today.getSeconds();
      seconds = seconds < 9 ? '0' + seconds : seconds;

      const fileName = today.getFullYear() + currentMonth + currentDay + "_" + hours + minutes + seconds + "_Disb_Esc_Ins_Discount.xlsx";
      // logger.logs("fileName", fileName);
      const filePath = config.get("exportToExcelPath.generalReports") + fileName;

      for (const record of loanInfo) {
        logger.logs("loanNo ", record.loanNo);
        const excelRecord = {};
        excelRecord["Client Code"] = config.get("disbursal.clientCode").toString();
        excelRecord["Debit account no"] = config.get("disbursal.fundfinaMarketingAccount").toString();
        excelRecord["Transaction type code"] = config.get("disbursal.charge.transactionTypeCode").toString();
        excelRecord["Value date"] = currentDate;
        excelRecord["Amount"] = record.discount.discountAmount.toString();
        excelRecord["Beneficary Name"] = record.bankDetails[0].accountName.toString();
        excelRecord["Beneficary Accunt no"] = record.bankDetails[0].accountNo.toString();
        excelRecord["IFSC code"] = record.bankDetails[0].ifsc.toString();
        excelRecord["Customer Ref no"] = config.get("disbursal.amount.prefixCustomerRefNo").toString() + record.loanNo.toString() + "CB";
        excelRecord["Beneficary email id"] = "";
        excelRecord["Beneficiary mobile no"] = "";
        excelRecord["Remarks"] = "Cash-back from Fundfina";
        excelRecord["Payment Type"] = "IMPS";
        excelRecord["Purpose code"] = config.get("disbursal.charge.purposeCode").toString();
        excelRecord["Bene a / c type"] = config.get("disbursal.beneficiaryAccountType").toString();
        excelRecord["Payable Location"] = "";
        excelRecord["Print branch name"] = "";
        excelRecord["Mode of delivery"] = "";
        excelRecord["Transaction currency"] = config.get("disbursal.transactionCurrency").toString();
        excelRecord["BENE_ADD1"] = "";
        excelRecord["BENE_ADD2"] = "";
        excelRecord["BENE_ADD3"] = "";
        excelRecord["BENE_ADD4"] = "";
        excelRecord["Beneficiary ID"] = "";
        exportRecords.push(excelRecord);
      }
      //logger.logs("exportRecords", exportRecords.length);
      if (exportRecords.length > 0) {
        let isSaveOutgoingPayments = true;

        if (!IsNullEmpty(model.isSaveDB)) {
          isSaveOutgoingPayments = model.isSaveOutgoingPayments;
        }

        if (isSaveOutgoingPayments) {
          for (const record of exportRecords) {
            const outgoingPayments = new OutgoingPayments({
              transactionType: "disbursal",
              clientCode: record["Client Code"],
              debitAccountNo: record["Debit account no"],
              transactionTypeCode: record["Transaction type code"],
              valueDate: record["Value date"],
              amount: record["Amount"],
              beneficiaryName: record["Beneficary Name"],
              beneficiaryAccountNo: record["Beneficary Accunt no"],
              ifscCode: record["IFSC code"],
              customerRefNo: record["Customer Ref no"],
              beneficiaryEmailId: record["Beneficary email id"],
              beneficiaryMobileNo: record["Beneficiary mobile no"],
              remarks: record["Remarks"],
              paymentType: record["Payment Type"],
              purposeCode: record["Purpose code"],
              beneficiaryAccountType: record["Bene a / c type"],
              payableLocation: record["Payable Location"],
              printBranchName: record["Print branch name"],
              modeOfDelivery: record["Mode of delivery"],
              transactionCurrency: record["Transaction currency"],
              beneficiaryAdd1: record["BENE_ADD1"],
              beneficiaryAdd2: record["BENE_ADD2"],
              beneficiaryAdd3: record["BENE_ADD3"],
              beneficiaryAdd4: record["BENE_ADD4"],
              beneficiaryId: record["Beneficiary ID"],
              fileName: fileName,
              bankResponseFileName: null,
              utrReferenceNo: null,
              insertedOn: Date.now(),
              updatedOn: Date.now()
            });
            await outgoingPayments.save();
          }
        }

        let xls = json2xls(exportRecords);
        fs.writeFileSync(filePath, xls, "binary");
        let mailModel = getMailModel();
        mailModel.attachmentName = fileName;
        mailModel.attachmentPath = filePath;
        mailModel.name = "Team";

        let mailResponse = null;
        mailModel.salutation = "Team";
        mailModel.to = config.get("disbursal.toEmail");
        mailModel.cc = config.get("disbursal.ccEmail");
        mailModel.date = getDMYFormattedDateWithTime(Date.now(), "-") + " For Discount";
        mailResponse = await sendExternalMail(mailModel, "Disbursal", "Disbursal Report");
        // logger.logs("filePath", filePath);
        // logger.logs("mailResponse", mailResponse);
        if (mailResponse.success) {
          fs.unlink(filePath, function () {
            logger.logs("file deleted successfully");
          });

          output = {
            success: true,
            message: "Send Successfully",
            status: 200,
          };
        } else {
          output = {
            success: false,
            message: mailResponse.message,
            status: 501,
          };
        }
      }
    } else {
      output = {
        success: false,
        message: "No records found",
        status: 501,
      };
    }
  } catch (ex) {
    logger.logs("error", ex);
    saveErrorLog("exportDiscountFile", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    output.message = ex.message;
  }
  return output;
};
// ------------------------------------------------------------------------------------------------------------------------------------------