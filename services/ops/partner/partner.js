//Libraries
// ------------------------------------------------------------------------------------------------------------------------------------------
const _ = require("lodash");
const bycrypt = require("bcrypt");
const mongoose = require("mongoose");
const debug = require("debug")("app:partner");
const IsNullEmpty = require("isnullemptyorwhitespace");
const config = require("config");
const fetch = require("node-fetch");
const DateDiff = require("date-diff");
const apiPath = __dirname;
// ----------------------------------------------------------------------------------------------------------------------------------------

//Models
// ----------------------------------------------------------------------------------------------------------------------------------------
const { PartnerMaster, PartnerLoans, PartnerLoansHistory, AccessDetails, PreApprovals, PartnerConfig, LedgerAccount, ConfigParameters } = require("../../../models/ops/partner/partner");
const { UserInfo, UserPartnerInfo, ClientCodeRepository, KYCInfo, MissingPincodes } = require("../../../models/ops/registration/users");
const { BankBranchMaster, CityStateMaster } = require("../../../models/ops/common/master");
const { LoanInfo } = require("../../../models/ops/loan/loan");
// ----------------------------------------------------------------------------------------------------------------------------------------

//External Methods
// ----------------------------------------------------------------------------------------------------------------------------------------
const { getProductId, getName, sleep, pushToNotifications, getDMYFormattedDate, getProductType, round10, removeSpecialChars, downloadImageFromS3, saveGstDetails, isPnachAvailable, buildMobileNumberObject, getMobileNumberArray } = require("../../../services/ops/common/common");
const loanService = require("../../../services/ops/loan/loan");
const userService = require("../../../services/ops/registration/users");
const { saveErrorLog, getDefinitionMaster } = require("../../../services/ops/common/masters");
const { backupUserInfo } = require("../../../services/ops/common/common");
const { validateAndSaveBankDetails, ocrVerification, verifyPanData, verifyDlData, verifyVoterIdData, verifyClientBank } = require("../../../services/ops/verification/digioApi");
const { generateCustomUrl } = require("../../../services/ops/common/links");
const postDisbursal = require("../../../services/ops/loan/postDisbursal");
const { sendNotifications } = postDisbursal;
const { callBorrowingPartnerAPI } = require("../../../services/ops/partner/borrowingPartner");
const { backupUserPartnerInfo } = require("../../../services/ops/verification/digioApi");
const { getMaskedMobileNo } = require("../../../services/ops/registration/verificationService");
const { uploadToS3 } = require("../../../models/ops/aws/s3");
const installmentService = require("../../../services/ops/loan/installments");
const { verifyOtp } = require('../../../services/ops/otp-service/otp-service');
const Logger = require("../../../middleware/logger");
const logger = new Logger();
const { ROLES } = require('../../../services/ops/common/constants').Constants;
// ----------------------------------------------------------------------------------------------------------------------------------------


//Internal Methods
// ----------------------------------------------------------------------------------------------------------------------------------------
module.exports.insertNewPartner = async function (model) {
  let output = {
    data: null,
    success: false,
    message: "",
    status: 501,
  };
  try {

    /** CHANGES START */
    let payload = {
      notifyTo: model.mobileNo,
      notificationType: "PARTNER_REGISTRATION",
      channel: "SMS",
      otp: model.otp
    };
    let { error: verifyOtpError, response: verifyOtpResponse } = await verifyOtp(payload);

    if (verifyOtpError) {
      return {
        data: "",
        success: false,
        message: verifyOtpError && verifyOtpError.message || "Invalid OTP",
        status: 400,
      };
    }
    /** CHANGES END */

    let user = await UserInfo.find()
      .or([
        { "userDetails.emailId": { $regex: new RegExp(model.emailId, "i") } },
        { "userDetails.mobileNo.mobileNumber": model.mobileNo },
      ])
      .select("-userDetails.password");

    if (user.length != 0) {
      return {
        data: "",
        success: false,
        message: "Please Enter Valid Email/Mobile",
        status: 400,
      };
    }

    let partner = await PartnerMaster.findOne({
      partnerName: { $regex: new RegExp(model.partnerName, "i") },
    });

    if (partner) {
      return {
        data: "",
        success: false,
        message: "Partner name already exists",
        status: 400,
      };
    }

    let parts = model.doi.split("/");
    // Please pay attention to the month (parts[1]); JavaScript counts months from 0:
    // January - 0, Februar.y - 1, etc.
    let doi = new Date(parts[2], parts[1] - 1, parts[0]);

    let partnerInfo = new PartnerMaster({
      partnerName: model.partnerName.trim(),
      partnerCode: model.partnerName.substring(0, 4),
      partnerType: model.partnerType,
      interestType: model.partnerType == "Lending Partner" ? "Compound Interest" : null,
      roundType: model.partnerType == "Lending Partner" ? "Upper" : null,
      typeOfEntity: model.typeOfEntity,
      doi: doi,
      processingCharge: 2, //default
      isActive: false,
      insertedOn: Date.now(),
      contractTemplate: "loanContract.html",
      displayLogo: false,
      logoFileName: null,
      callBackUrl: "",
      displayName: model.partnerName.trim(),
      fldgPer: model.partnerType == "Lending Partner" ? 10 : null,
      brandName: model.partnerName.trim(),
      creditContractTemplate: "",
      contractSuccessCallBackUrl: "",
      eNachErrorCallBackUrl: "",
      ledgerDetails: {
        virtualAccountNo: null,
        balance: 0,
        holdAmount: 0,
        totalVACredit: 0,
        payOut: 0,
        daysInvested: 0,
        insertedOn: Date.now(),
        updatedOn: Date.now(),
      },
    });

    let partnerResponse = await partnerInfo.save();

    user = new UserInfo({
      userDetails: {
        userName: model.userName,
        role: model.partnerType,
        isMobileValidated: true,
        isEmailValidated: false,
        passwordModifiedOn: Date.now(),
      },
      ipAddress: model.ipAddress,
      insertedOn: Date.now(),
      tnc: "Y",
    });

    if (model.emailId)
      model.emailId = model.emailId.trim().toLowerCase();

    user.userDetails.emailId.push(model.emailId);
    user.userDetails.mobileNo.push(buildMobileNumberObject(model.mobileNo.trim())) // mobile number change

    // user.partnerDetails = {
    //   partnerId: partnerResponse._id,
    //   partnerName: partnerResponse.partnerName.trim(),
    //   partnerType: partnerResponse.partnerType,
    // };

    // user.isPartnerUser = true;
    // user.isRetailer = false;

    user.userDetails.documentConsent = true;

    user.kycStepDetails.push({
      Step: "KYC",
      isCompleted: false,
      insertedOn: Date.now(),
      completedOn: null,
    });

    user.kycStepDetails.push({
      Step: "Questionnairre",
      isCompleted: false,
      insertedOn: Date.now(),
      completedOn: null,
    });

    //Hashing the password
    const salt = await bycrypt.genSalt(10);
    user.userDetails.password = await bycrypt.hash(model.password.trim(), salt);

    const result = await user.save();

    const userPartnerInfo = new UserPartnerInfo({
      userId: user._id,
      partnerId: partnerResponse._id,
      merchantCode: null,
      isRetailer: false,
      isPartnerUser: true,
      loanHistory: [],
      insertedOn: Date.now(),
      updatedOn: Date.now()
    });
    await userPartnerInfo.save();

    const partnerConfig = new PartnerConfig({
      partnerId: partnerResponse._id,
      bankValidationAmount: config.get("partnerConfig.bankValidationAmount"),
      eNachAmount: config.get("partnerConfig.eNachAmount"),
      disbursalType: config.get("partnerConfig.disbursalType"),
      isApiPartner: false,
      collectionType: config.get("partnerConfig.collectionType"),
      LC_InvoiceTenure: config.get("partnerConfig.LC_InvoiceTenure"),
      LC_InvoiceFrequency: config.get("partnerConfig.LC_InvoiceFrequency"),
      LC_ParentTenure: config.get("partnerConfig.LC_ParentTenure"),
      LC_ParentFrequency: config.get("partnerConfig.LC_ParentFrequency"),
      campaign: [],
      partnerName: model.partnerName.trim(),
      isMandateRequired: true,
      settlementMode: model.partnerType == "Lending Partner" ? config.get("partnerConfig.settlementMode") : "",
      primaryTrancheValue: model.partnerType == "Lending Partner" ? config.get("partnerConfig.primaryTrancheValue") : "",
      onboardingType: "1 Step",
      allowMobileEdit: true,
      isLoanApprovalRequired: false,
      questionnairre: {
        whatsApp: true,
        gender: true,
        maritalStatus: true,
        children: true,
        homeOwnershipType: true,
        vehicle: true,
        monthlyOutgoing: true,
        monthlyIncome: true,
        jobType: true,
        purchasedInOneYear: true,
        degree: true,
        purchasedInOneYear: true,
        recommendedBy: true
      },
      preApproval: {
        loanDescription: false
      },
      apiSecurityEnabled: true,
      updatePartnerBank: false
    });

    await partnerConfig.save();

    return {
      data: _.omit(result, ["userDetails.password"]),
      success: true,
      message: "User saved successfully",
      status: 200,
    };
  }
  catch (ex) {
    saveErrorLog("insertNewPartner", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    output.message = ex.message;
  }
  return output;
};

module.exports.isValidPartnerName = async function (partnerName) {
  try {
    let partner = await PartnerMaster.findOne({
      partnerName: { $regex: new RegExp(partnerName, "i") },
    });

    if (partner) {
      return {
        data: "",
        success: false,
        message: "Partner name already exists",
        status: 400,
      };
    } else {
      return {
        data: "",
        success: true,
        message: "Partner name is available",
        status: 200,
      };
    }
  } catch (ex) {
    const model = {
      partnerName: partnerName
    };
    saveErrorLog("isValidPartnerName", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    output.message = ex.message;
  }
  return output;

};

module.exports.PushToFundfina = async function (partnerLoanId, isAutoLogin) {
  return new Promise(async (resolve, reject) => {
    let output = {
      data: null,
      userId: null,
      loanId: null,
      success: false,
      message: "Some Error Occurred",
      status: 501,
    };

    try {
      let partnerLoan = await PartnerLoans.findById(partnerLoanId);
      let isNewUser = false;
      let userInfo = null;
      let userPartnerInfo = null;
      let preApproval = null;

      if (!IsNullEmpty(partnerLoan)) {

        if (IsNullEmpty(partnerLoan.productId)) {
          let getproductId = getProductId(partnerLoan.trxnId, partnerLoan.merchantCode, partnerLoan._id);

          //update productId in PartnerLoans
          partnerLoan.productId = getproductId.data;
          const partnerLoanResponse = await partnerLoan.save();

          //update productId in PreApproval
          preApproval = await PreApprovals.findOne({ partnerId: mongoose.Types.ObjectId(partnerLoan.partnerId), merchantCode: partnerLoan.merchantCode, trxnId: partnerLoan.trxnId });

          if (preApproval && preApproval != null) {
            preApproval.productId = getproductId.data;
            await preApproval.save();
          }
        }

        let productType = "TL_90";
        let interestType = "Compound Interest";
        let loanAmount = partnerLoan.loanRequest.loanAmount;
        let interest = null;
        let merchantType = "Retailer";
        let campaignTime = "90 Days";
        let maturity = 90;
        let tenure = 90;

        // logger.logs("partnerLoan.partnerId", partnerLoan.partnerId);
        // logger.logs("partnerLoan.merchantCode", partnerLoan.merchantCode);
        // logger.logs("partnerLoan.trxnId", partnerLoan.trxnId);
        // logger.logs(" partnerLoan.trxnId", partnerLoan.trxnId);

        if (!IsNullEmpty(partnerLoan.productId)) {
          preApproval = await PreApprovals.findOne({ partnerId: mongoose.Types.ObjectId(partnerLoan.partnerId), merchantCode: partnerLoan.merchantCode, productId: partnerLoan.productId }).sort({ modifiedOn: -1 });
        }

        if (preApproval == null && !IsNullEmpty(partnerLoan.trxnId)) {
          preApproval = await PreApprovals.findOne({ partnerId: mongoose.Types.ObjectId(partnerLoan.partnerId), merchantCode: partnerLoan.merchantCode, trxnId: partnerLoan.trxnId }).sort({ modifiedOn: -1 });
        }

        if (!IsNullEmpty(preApproval)) {
          merchantType = preApproval.merchantType;
          productType = preApproval.productType.toLowerCase() == "dl" ? "DL_30" : (preApproval.productType.toLowerCase() == "lc" ? "LC_90" : "TL_90");
          interestType = preApproval.productType.toLowerCase() == "dl" ? "Simple Interest" : preApproval.interestType;
          loanAmount = (partnerLoan.loanRequest.loanAmount == null) ? preApproval.approvedAmount : partnerLoan.loanRequest.loanAmount;
          const charges = await loanService.getLoanCharges(loanAmount, null, preApproval._id);

          if (!charges.success) {
            output.message = charges.message;
            return output;
          }
          //interest = preApproval.annualIntrestRate.replace("%", "").trim();
          interest = charges.interestRate;


          if (partnerLoan.loanRequest.tenure && partnerLoan.loanRequest.tenure != null && partnerLoan.loanRequest.tenure != "") {
            maturity = partnerLoan.loanRequest.tenure;
            tenure = partnerLoan.loanRequest.tenure;
            campaignTime = `${partnerLoan.loanRequest.tenure.toString()} Days`;
          } else {
            maturity = preApproval.productType.toLowerCase() == "lc" ? (preApproval.tenureInDays / 30) : preApproval.tenureInDays;
            tenure = preApproval.productType.toLowerCase() == "lc" ? 90 : preApproval.tenureInDays;
            campaignTime = `${preApproval.tenureInDays} Days`;
            // switch (maturity) {
            //   case 90:
            //     campaignTime = "90 Days";
            //     break;
            //   case 30:
            //     campaignTime = "30 Days";
            //     break;
            //   default:
            //     campaignTime = "90 Days";
            //     break;
            // }
          }
        }

        let partnerMaster = await PartnerMaster.findById(partnerLoan.partnerId);
        let loanInfo = await LoanInfo.findOne({ productId: partnerLoan.productId });
        const conf = config.get(partnerMaster.partnerCode.toLowerCase());

        if (!IsNullEmpty(loanInfo)) {
          logger.logs("inside loanInfo is Not NULL");
          userInfo = await UserInfo.findById(loanInfo.userId);
        } else {
          logger.logs("inside loanInfo is NULL");
          userPartnerInfo = await UserPartnerInfo.findOne({ partnerId: mongoose.Types.ObjectId(partnerMaster._id), merchantCode: partnerLoan.merchantCode });

          if (IsNullEmpty(userInfo) && !IsNullEmpty(userPartnerInfo)) {
            userInfo = await UserInfo.findById(userPartnerInfo.userId);
          }
        }

        let isPanChangeAllowed = false;
        let isAlreadyExist = false;
        let isExistingUser = false;

        //User Exists and applying with Different PAN 
        if (!IsNullEmpty(userInfo) && userInfo.panNo != null && userInfo.panNo != partnerLoan.panNo) {
          logger.logs("user exist but now applying with different pan");

          const existKycInfo = await KYCInfo.countDocuments({ userId: mongoose.Types.ObjectId(userInfo._id), existingUserId: { $ne: null } });


          if (existKycInfo > 0) {
            //2nd time trying to update the Pan
            logger.logs("Already updated pan and now second time updating the pan");
            output.message = `Pan No updation is not allowed. Pan no with Fundfina is ${userInfo.panNo} and applying now with pan No ${partnerLoan.panNo}`;
            partnerLoan.errorMsg = output.message;
            await partnerLoan.save();
            reject(output);
            return output;
          } else {
            const existingUser = await UserInfo.findOne({ panNo: partnerLoan.panNo });

            if (existingUser && existingUser != null) {
              isExistingUser = true;
              logger.logs("Pan exists for another user");
              //Check user exists for another partner
              const existForAnotherPartner = await UserPartnerInfo.countDocuments({ userId: mongoose.Types.ObjectId(existingUser._id), partnerId: { $ne: mongoose.Types.ObjectId(partnerMaster._id) } });

              if (existForAnotherPartner > 0) {
                logger.logs("Pan exists for another partner user");
                output.message = `Pan No updation is not allowed. Pan no with Fundfina is ${userInfo.panNo} and applying now with pan No ${partnerLoan.panNo}`;
                partnerLoan.errorMsg = output.message;
                await partnerLoan.save();
                reject(output);
                return output;
              } else {
                logger.logs("Pan no exists for same partner but for different merchant");
                //Check if any loan is pending
                const existingLoans = await LoanInfo.countDocuments({
                  userId: mongoose.Types.ObjectId(existingUser._id), status: {
                    $in: ["Lender Approval", "Lender Assigned", "Loan Contract Accepted", "Ready For Disbursal", "Initiated Fund Transfer", "Loan In Progress", "Written-Off", "WrittenOff Paid"]
                  }
                });

                if (existingLoans > 0) {
                  logger.logs("Another loan is running for user who is having new pan");
                  output.message = `Pan No updation is not allowed. Pan no with Fundfina is ${userInfo.panNo} and applying now with pan No ${partnerLoan.panNo}`;
                  partnerLoan.errorMsg = output.message;
                  await partnerLoan.save();
                  reject(output);
                  return output;
                } else {
                  logger.logs("No any other Loan running for existing user having same pan");
                  isPanChangeAllowed = true;
                  isAlreadyExist = true;
                }
              }
            } else {
              logger.logs("Pan does not exists in system");
              const existingLoans = await LoanInfo.countDocuments({
                userId: mongoose.Types.ObjectId(userInfo._id), status: {
                  $in: ["Lender Approval", "Lender Assigned", "Loan Contract Accepted", "Ready For Disbursal", "Initiated Fund Transfer", "Loan In Progress", "Written-Off", "WrittenOff Paid"]
                }
              });

              if (existingLoans > 0) {
                logger.logs("Another loan is running for user who is having new pan");
                output.message = `Pan No updation is not allowed when existing loan is running. Please close existing loan. Pan no with Fundfina is ${userInfo.panNo} and applying now with pan No ${partnerLoan.panNo}`;
                partnerLoan.errorMsg = output.message;
                await partnerLoan.save();
                reject(output);
                return output;
              } else {
                logger.logs("No any other Loan running for existing user having same pan");
                isPanChangeAllowed = true;
                isAlreadyExist = true;
              }
            }

            if (isPanChangeAllowed == true) {
              //take backup of current UserInfo.
              const backup = await backupUserInfo(userInfo._id);

              if (backup.success) {
                logger.logs("isAlreadyExist", isAlreadyExist);
                if (isAlreadyExist == true) {
                  //Make KycInfo inActive and update new userId in UserPartnerInfo and LoanInfo and remove old UserInfo

                  //Update userId in KYCInfo
                  await KYCInfo.updateMany({ userId: mongoose.Types.ObjectId(userInfo._id), isActive: true }, { isActive: false, updatedOn: Date.now() });

                  if (isExistingUser) {
                    await KYCInfo.updateMany({ userId: mongoose.Types.ObjectId(userInfo._id) }, { existingUserId: mongoose.Types.ObjectId(userInfo._id), userId: mongoose.Types.ObjectId(existingUser._id), updatedOn: Date.now() });

                    //Update userId in UserPartnerInfo
                    const alluserPartnerInfos = await UserPartnerInfo.find({ partnerId: mongoose.Types.ObjectId(partnerMaster._id), userId: mongoose.Types.ObjectId(userInfo._id) }).select({ _id: 1, partnerId: 1, merchantCode: 1 });

                    for (const user in alluserPartnerInfos) {
                      await backupUserPartnerInfo(user._id);
                    }

                    await UserPartnerInfo.updateMany({ partnerId: mongoose.Types.ObjectId(partnerMaster._id), userId: mongoose.Types.ObjectId(userInfo._id) }, { userId: mongoose.Types.ObjectId(existingUser._id), updatedOn: Date.now() });

                    //Update userId in LoanInfo
                    for (const user in alluserPartnerInfos) {
                      const allLoans = await LoanInfo.find({ partnerId: mongoose.Types.ObjectId(user.partnerId), merchantCode: user.merchantCode, userId: mongoose.Types.ObjectId(userInfo._id) }).select({ _id: 1 });
                      for (const loan of allLoans) {
                        await backupLoanInfo(loan._id);
                      }
                      await LoanInfo.updateMany({ partnerId: mongoose.Types.ObjectId(user.partnerId), merchantCode: user.merchantCode, userId: mongoose.Types.ObjectId(userInfo._id) }, { userId: mongoose.Types.ObjectId(existingUser._id), updatedOn: Date.now() });
                    }

                    //Remove UserInfo only if not assigned to any other merchant
                    const isUserPartnerInfoExists = UserPartnerInfo.countDocuments({ userId: mongoose.Types.ObjectId(userInfo._id) });

                    if (isUserPartnerInfoExists == 0) await userInfo.remove();

                    userInfo = await UserInfo.findById(existingUser._id);
                  }
                } else {
                  //change pan in UserInfo and Make KycInfo inActive
                  await KYCInfo.updateMany({ userId: mongoose.Types.ObjectId(userInfo._id), isActive: true }, { isActive: false });
                }
              } else {
                saveErrorLog("updateUserInfo", apiPath, model, backup.message);
                output.message = backup.message;
                return output;
              }
            }
          }
        }
        //if (userInfo) logger.logs("userInfo", userInfo._id); 


        if (!IsNullEmpty(userPartnerInfo) && !IsNullEmpty(userInfo) && !IsNullEmpty(userInfo.panNo) && userInfo.panNo.toUpperCase() != partnerLoan.panNo.toUpperCase() && !isPanChangeAllowed) {
          logger.logs("inside -1");
          const clientCode = await ClientCodeRepository.countDocuments({ panNumber: userInfo.panNo });
          if (clientCode > 0) {
            output.message = `Changing of Pan No is not allowed. Pan no with Fundfina is ${userInfo.panNo} and you are applying with pan No ${partnerLoan.panNo}`;
            partnerLoan.errorMsg = output.message;
            await partnerLoan.save();
            reject(output);
            return output;
          }
        } else if (IsNullEmpty(loanInfo) || IsNullEmpty(loanInfo.status)) {
          logger.logs("inside -2");
          userInfo = await UserInfo.findOne({ panNo: { $regex: new RegExp(partnerLoan.panNo, "i") } });

          if (IsNullEmpty(userInfo) && !IsNullEmpty(userPartnerInfo)) {
            logger.logs("inside -2.1");
            userInfo = await UserInfo.findById(userPartnerInfo.userId);
          } else if (IsNullEmpty(userInfo) && !IsNullEmpty(loanInfo)) {
            logger.logs("inside -2.2");
            userInfo = await UserInfo.findById(loanInfo.userId);
          } else if (!IsNullEmpty(userInfo) && !IsNullEmpty(userPartnerInfo) && userPartnerInfo.userId != userInfo._id) {
            logger.logs("inside -2.3");
            const userInfo1 = await UserInfo.findById(userPartnerInfo.userId);
            if (userInfo1 && userInfo1.panNo == null) {
              const backup = await backupUserInfo(userInfo1._id);
              if (backup.success) {
                await userInfo1.remove();
                await backupUserPartnerInfo(userPartnerInfo._id);
                await userPartnerInfo.remove();
                userPartnerInfo = null;
              }
            }
          }
        }

        //Insert or Update UserInfo
        if (IsNullEmpty(userInfo)) {
          logger.logs("inside insert UserInfo");
          const resInsertUserInfo = await insertUserInfo(partnerLoan._id);
          logger.logs("resInsertUserInfo", resInsertUserInfo);
          if (resInsertUserInfo.success && resInsertUserInfo.success == true) {
            userInfo = await UserInfo.findById(resInsertUserInfo.userId);
            userPartnerInfo = await UserPartnerInfo.findById(resInsertUserInfo.userPartnerInfoId);
            isNewUser = true;
          } else {
            output.message = resInsertUserInfo.message;
            partnerLoan.errorMsg = output.message;
            await partnerLoan.save();
            reject(output);
            return output;
          }
          //logger.logs("creditResponse", creditResponse);
        } else {
          logger.logs("inside Update UserInfo");
          const resUpdateUserInfo = await updateUserInfo(partnerLoan._id, userInfo._id);

          if (resUpdateUserInfo.success && resUpdateUserInfo.success == true) {
            userInfo = await UserInfo.findById(resUpdateUserInfo.userId);
            userPartnerInfo = await UserPartnerInfo.findById(resUpdateUserInfo.userPartnerInfoId);
          } else {
            output.message = resUpdateUserInfo.message;
            partnerLoan.errorMsg = output.message;
            await partnerLoan.save();
            reject(output);
            return output;
          }
        }

        let isKycRecapture = false

        //Insert or Update LoanInfo
        if (IsNullEmpty(loanInfo) || loanInfo.status == null) {
          if (partnerMaster.partnerCode.toLowerCase() == "pay") {
            if (IsNullEmpty(loanInfo)) {
              const preApprovalResponse = await PreApprovals.findOneAndUpdate({ _id: preApproval._id, isApplied: { $in: [null, false] } }, { $set: { isApplied: true } }, { new: true, useFindAndModify: false }).lean();

              if (preApprovalResponse == null) {
                output.message = "Mulitple request received. Please try after 5 min";
                resolve(output);
                return output;
              }
              const loanDetails = await loanService.getLoanId();
              const processingFeeAmount = loanService.getProcessingFee(loanAmount, parseFloat(preApproval.processingFee.replace("%", "").trim()))

              if (loanDetails.success == true) {
                loanInfo = new LoanInfo({
                  userId: userInfo._id,
                  loanNo: loanDetails.data,
                  partner: partnerMaster.partnerName,
                  partnerId: partnerMaster._id,
                  merchantType: merchantType,
                  merchantCode: partnerLoan.merchantCode,
                  trxnId: partnerLoan.trxnId,
                  productId: partnerLoan.productId,
                  productType: productType,
                  amount: loanAmount,
                  purpose: "Business",
                  description: "pay1 merchant working capital loan",
                  maturity: maturity,
                  tenure: tenure,
                  interestRate: interest,
                  currency: "Indian Rupee",
                  campaignTime: campaignTime,
                  loanType: "EMI",
                  repaymentFrequency: "Daily",
                  additionalDescription: "pay1 merchant working capital loan",
                  isMaturityMonth: false,
                  interestType: interestType,
                  isDraft: true,
                  status: "Initiated",
                  adminRemarks: null,
                  isActive: true,
                  insertedOn: Date.now(),
                  updatedOn: Date.now(),
                  offerId: preApproval._id,
                  processingFeePer: parseFloat(preApproval.processingFee.replace("%", "").trim()),
                  processingFeeAmount: processingFeeAmount,
                  isSettled: false,
                  batchId: 0
                });
              } else {
                reject(loanDetails);
                return output;
              }
            }

            //Loan Status
            if (loanInfo.status == null)
              loanInfo.status = "Initiated";

            if (!isAutoLogin) {

              loanInfo.loanStepDetails.push({
                Step: "Contact Information",
                isCompleted: true,
                insertedOn: Date.now(),
                completedOn: Date.now(),
              });

              loanInfo.loanStepDetails.push({
                Step: "OTP Screen",
                isCompleted: false,
                insertedOn: Date.now(),
                completedOn: null,
              });

              loanInfo.loanStepDetails.push({
                Step: "OTP Matched",
                isCompleted: false,
                insertedOn: Date.now(),
                completedOn: null
              });

            }

            loanInfo.loanStepDetails.push({
              Step: "Loan",
              isCompleted: false,
              insertedOn: Date.now(),
              completedOn: null,
            });

            if (isAutoLogin) {

              loanInfo.isAutoLogin = isAutoLogin

              loanInfo.loanStepDetails.push({
                Step: "Verify Mobile Number",
                isCompleted: false,
                insertedOn: Date.now(),
                completedOn: null,
              });

              loanInfo.loanStepDetails.push({
                Step: "Mobile Verified Successfully",
                isCompleted: false,
                insertedOn: Date.now(),
                completedOn: null
              });

            }

            loanInfo.loanStepDetails.push({
              Step: "KYC",
              isCompleted: false,
              insertedOn: Date.now(),
              completedOn: null,
            });

            loanInfo.loanStepDetails.push({
              Step: "Shop",
              isCompleted: false,
              insertedOn: Date.now(),
              completedOn: null,
            });

            loanInfo.loanStepDetails.push({
              Step: "Questionnairre",
              isCompleted: false,
              insertedOn: Date.now(),
              completedOn: null,
            });

            loanInfo.loanStepDetails.push({
              Step: "Review",
              isCompleted: false,
              insertedOn: Date.now(),
              completedOn: null,
            });

            loanInfo.loanStepDetails.push({
              Step: "Bank",
              isCompleted: false,
              insertedOn: Date.now(),
              completedOn: null,
            });

            loanInfo.loanStepDetails.push({
              Step: "eNach",
              isCompleted: false,
              insertedOn: Date.now(),
              completedOn: null,
            });

            loanInfo.loanStepDetails.push({
              Step: "Loan Contract",
              isCompleted: false,
              insertedOn: Date.now(),
              completedOn: null,
            });

            loanInfo.flowDetails.push({
              Step: "Initiated",
              changedBy: userInfo._id,
              changedByRole: "User",
              insertedOn: Date.now()
            });

            loanInfo.lastStepCompleted = "Contact Information";
            loanInfo.lastStepCompletedAt = Date.now();

            await loanInfo.save();


            const userInfo1 = await UserInfo.findById(userInfo._id);
            userInfo1.loan = {
              lastUpdatedByLoanId: loanInfo._id,
              lastUpdatedByLoanNo: loanInfo.loanNo,
              lastUpdatedByPartner: partnerMaster.partnerName,
              lastUpdatedByPartnerId: partnerMaster._id,
              updatedOn: Date.now()
            }
            await userInfo1.save();

          } else {
            if (IsNullEmpty(loanInfo)) {
              const preApprovalResponse = await PreApprovals.findOneAndUpdate({ _id: preApproval._id, isApplied: { $in: [null, false] } }, { $set: { isApplied: true } }, { new: true, useFindAndModify: false }).lean();

              if (preApprovalResponse == null) {
                output.message = "Mulitple request received. Please try after 5 min";
                resolve(output);
                return output;
              }

              const loanDetails = await loanService.getLoanId();
              const processingFeeAmount = loanService.getProcessingFee(loanAmount, parseFloat(preApproval.processingFee.replace("%", "").trim()));

              if (loanDetails.success == true) {
                loanInfo = new LoanInfo({
                  userId: userInfo._id,
                  loanNo: loanDetails.data,
                  partner: partnerMaster.partnerName,
                  partnerId: partnerMaster._id,
                  merchantCode: partnerLoan.merchantCode,
                  merchantType: merchantType,
                  trxnId: partnerLoan.trxnId,
                  productId: partnerLoan.productId,
                  productType: productType,
                  amount: loanAmount,
                  purpose: "Business",
                  description: "Working Capital Loan",
                  maturity: maturity,
                  tenure: tenure,
                  interestRate: interest,
                  currency: "Indian Rupee",
                  campaignTime: campaignTime,
                  loanType: "EMI",
                  repaymentFrequency: preApproval.productType.toLowerCase() == "lc" ? "Monthly" : "Daily",
                  additionalDescription: null,
                  isMaturityMonth: false,
                  interestType: interestType,
                  isDraft: conf.isAutoCompleteKycSteps == true ? false : true,
                  status: conf.isAutoCompleteKycSteps == true ? "Under Review" : "Initiated",
                  adminRemarks: null,
                  isActive: true,
                  insertedOn: Date.now(),
                  updatedOn: Date.now(),
                  ipAddress: IsNullEmpty(partnerLoan.ipAddress) ? null : partnerLoan.ipAddress,
                  isParentLoan: preApproval.productType.toLowerCase() == "lc" ? true : false,
                  offerId: preApproval._id,
                  processingFeePer: parseFloat(preApproval.processingFee.replace("%", "").trim()),
                  processingFeeAmount: preApproval.processingFeeAmount,
                  isSettled: false,
                  batchId: 0
                });
              } else {
                reject(loanDetails);
                return output;
              }
            }

            //Loan Status
            if (loanInfo.status == null)
              loanInfo.status = conf.isAutoCompleteKycSteps == true ? "Under Review" : "Initiated";

            if (!isAutoLogin) {
              loanInfo.loanStepDetails.push({
                Step: "Contact Information",
                isCompleted: true,
                insertedOn: Date.now(),
                completedOn: Date.now(),
              });

              loanInfo.loanStepDetails.push({
                Step: "OTP Screen",
                isCompleted: false,
                insertedOn: Date.now(),
                completedOn: null,
              });

              loanInfo.loanStepDetails.push({
                Step: "OTP Matched",
                isCompleted: false,
                insertedOn: Date.now(),
                completedOn: null
              });
            }
            //Step Details
            loanInfo.loanStepDetails.push({
              Step: "Loan",
              isCompleted: conf.isAutoCompleteKycSteps == true ? true : false,
              insertedOn: Date.now(),
              completedOn: conf.isAutoCompleteKycSteps == true ? Date.now() : null
            });

            if (isAutoLogin) {

              loanInfo.isAutoLogin = isAutoLogin

              loanInfo.loanStepDetails.push({
                Step: "Verify Mobile Number",
                isCompleted: false,
                insertedOn: Date.now(),
                completedOn: null,
              });

              loanInfo.loanStepDetails.push({
                Step: "Mobile Verified Successfully",
                isCompleted: false,
                insertedOn: Date.now(),
                completedOn: null
              });
            }

            loanInfo.loanStepDetails.push({
              Step: "KYC",
              isCompleted: conf.isAutoCompleteKycSteps == true ? true : false,
              insertedOn: Date.now(),
              completedOn: conf.isAutoCompleteKycSteps == true ? Date.now() : null
            });

            loanInfo.loanStepDetails.push({
              Step: "Shop",
              isCompleted: conf.isAutoCompleteKycSteps == true ? true : false,
              insertedOn: Date.now(),
              completedOn: conf.isAutoCompleteKycSteps == true ? Date.now() : null
            });

            loanInfo.loanStepDetails.push({
              Step: "Questionnairre",
              isCompleted: conf.isAutoCompleteKycSteps == true ? true : false,
              insertedOn: Date.now(),
              completedOn: conf.isAutoCompleteKycSteps == true ? Date.now() : null
            });

            loanInfo.loanStepDetails.push({
              Step: "Review",
              isCompleted: conf.isAutoCompleteKycSteps == true ? true : false,
              insertedOn: Date.now(),
              completedOn: conf.isAutoCompleteKycSteps == true ? Date.now() : null
            });

            loanInfo.loanStepDetails.push({
              Step: "Bank",
              isCompleted: conf.isAutoCompleteKycSteps == true ? true : false,
              insertedOn: Date.now(),
              completedOn: conf.isAutoCompleteKycSteps == true ? Date.now() : null
            });

            loanInfo.loanStepDetails.push({
              Step: "eNach",
              isCompleted: false,
              insertedOn: Date.now(),
              completedOn: null
            });

            loanInfo.loanStepDetails.push({
              Step: "Loan Contract",
              isCompleted: false,
              insertedOn: Date.now(),
              completedOn: null
            });

            //Flow Details
            loanInfo.flowDetails.push({
              Step: conf.isAutoCompleteKycSteps == true ? "Under Review" : "Initiated",
              changedBy: userInfo._id,
              changedByRole: "User",
              insertedOn: Date.now()
            });

            if (conf.isAutoCompleteKycSteps == true) {
              loanInfo.lastStepCompleted = "Review";
              loanInfo.lastStepCompletedAt = Date.now();
            }
            else {
              loanInfo.lastStepCompleted = "Contact Information";
              loanInfo.lastStepCompletedAt = Date.now();
            }

            if (conf.isAutoCompleteKycSteps == true && conf.isDigiLockerMandatory == true) {
              loanInfo.loanContractDetails.contractSuccessCallBackUrl = partnerLoan.contractSuccessCallBackUrl;
              loanInfo.loanContractDetails.eNachErrorCallBackUrl = partnerLoan.eNachErrorCallBackUrl;
            }

            await loanInfo.save();
            const userInfo1 = await UserInfo.findById(userInfo._id);
            userInfo1.userDetails.loan = {
              lastUpdatedByLoanId: loanInfo._id,
              lastUpdatedByLoanNo: loanInfo.loanNo,
              lastUpdatedByPartner: partnerMaster.partnerName,
              lastUpdatedByPartnerId: partnerMaster._id,
              updatedOn: Date.now()
            }
            await userInfo1.save();

          }
          partnerLoan.loanNo = loanInfo.loanNo;
          await partnerLoan.save();

          isKycRecapture = await initiateKycRecapture(loanInfo.userId, loanInfo.productType)

        } else {
          userInfo = await UserInfo.findOneAndUpdate({ _id: mongoose.Types.ObjectId(userInfo._id) }, {
            $set: {
              "userDetails.loan": {
                lastUpdatedByLoanId: loanInfo._id,
                lastUpdatedByLoanNo: loanInfo.loanNo,
                lastUpdatedByPartner: partnerMaster.partnerName,
                lastUpdatedByPartnerId: partnerMaster._id,
                updatedOn: Date.now()
              }
            }
          }, {
            new: true
          }).lean();

          if (loanInfo.trxnId != partnerLoan.trxnId) {
            loanInfo = await LoanInfo.findOneAndUpdate({ _id: mongoose.Types.ObjectId(loanInfo._id) }, {
              $set: {
                trxnId: partnerLoan.trxnId
              }
            }, {
              new: true
            }).lean();
          }
          //userInfo = await UserInfo.findById(userInfo._id);
        }

        //Auto Approve Loan if digilocker details received
        if (conf.isDigiLockerMandatory && conf.isDigiLockerMandatory == true && partnerLoan.digilocker && userInfo.userDetails.kycStatus != "Approved") {
          let poiDob = partnerLoan && partnerLoan.digilocker && partnerLoan.digilocker.document_details && partnerLoan.digilocker.pan && partnerLoan.digilocker.pan.dob ? partnerLoan.digilocker.pan.dob.trim() : "";

          let poiName = partnerLoan && partnerLoan.digilocker && partnerLoan.digilocker.document_details && partnerLoan.digilocker.pan && partnerLoan.digilocker.pan.dob.name ? partnerLoan.digilocker.pan.dob.name.trim().toLowerCase() : "";

          if (poiDob && poiDob.contains("-"))
            poiDob = poiDob.replace("-", "/");

          let poaDob = partnerLoan && partnerLoan.digilocker && partnerLoan.digilocker.document_details && partnerLoan.digilocker.aadhaar && partnerLoan.digilocker.aadhaar.dob ? partnerLoan.digilocker.aadhaar.dob.trim() : "";

          let poaName = partnerLoan && partnerLoan.digilocker && partnerLoan.digilocker.document_details && partnerLoan.digilocker.pan && partnerLoan.digilocker.pan.dob.name ? partnerLoan.digilocker.pan.dob.name.trim().toLowerCase() : "";

          if (poaDob && poaDob.contains("-"))
            poaDob = poaDob.replace("-", "/");

          let detailsMismatch = "None";

          if (poiDob !== poaDob && poiName !== poaName)
            detailsMismatch = "Name & DOB";
          else if (poiName !== poaName)
            detailsMismatch = "Name";
          else if (poiDob !== poaDob)
            detailsMismatch = "DOB";

          if (detailsMismatch !== "None") {
            let dateOfBirth1 = null;
            let dateOfBirth2 = null;

            if (poiDob) {
              let parts = poiDob.split("/");
              dateOfBirth1 = new Date(parts[2], parts[1] - 1, parts[0]);
            }

            if (poaDob) {
              let parts = poaDob.split("/");
              dateOfBirth2 = new Date(parts[2], parts[1] - 1, parts[0]);
            }

            userInfo.userDetails.detailsMismatch = {
              isMismatch: true,
              type: detailsMismatch,
              details: {
                poiName: poiName && (detailsMismatch === "Name" || detailsMismatch === "Name & DOB") ? poiName.trim().toUpperCase() : null,
                poaName: poaName && (detailsMismatch === "Name" || detailsMismatch === "Name & DOB") ? poaName.trim().toUpperCase() : null,
                poiDob: dateOfBirth1 && (detailsMismatch === "DOB" || detailsMismatch === "Name & DOB") ? dateOfBirth1 : null,
                poaDob: dateOfBirth2 && (detailsMismatch === "DOB" || detailsMismatch === "Name & DOB") ? dateOfBirth2 : null
              },
              insertedOn: userInfo.userDetails.detailsMismatch && userInfo.userDetails.detailsMismatch.insertedOn ? userInfo.userDetails.detailsMismatch.insertedOn : Date.now(),
              modifiedOn: Date.now()
            }
          } else userInfo.userDetails.detailsMismatch = null;

          //generate client code for digilocker case
          if (userInfo.userDetails.clientCode == null) {
            let resultClientCode = await userService.getClientCode(userInfo);

            if (resultClientCode.success == true) {
              userInfo.userDetails.clientCode = resultClientCode.data;
              userInfo.userDetails.clientCodeGenDate = Date.now();
              userInfo.userDetails.clientCodeGenBy = null;
            }
          }
          userInfo.kycStepDetails = [
            {
              Step: "Registration",
              isCompleted: true,
              insertedOn: Date.now(),
              completedOn: Date.now(),
            },
            {
              Step: "Loan",
              isCompleted: true,
              insertedOn: Date.now(),
              completedOn: Date.now(),
            },
            {
              Step: "KYC",
              isCompleted: true,
              insertedOn: Date.now(),
              completedOn: Date.now(),
            },
            {
              Step: "Shop",
              isCompleted: true,
              insertedOn: Date.now(),
              completedOn: Date.now(),
            },
            {
              Step: "Questionnairre",
              isCompleted: true,
              insertedOn: Date.now(),
              completedOn: Date.now(),
            }
          ];
          userInfo.userDetails.kycStatus = "Approved";
          await userInfo.save();

          const productType = getProductType(loanInfo.productType);
          let apiVar = partnerMaster.partnerCode.toLowerCase() + ".kycSubmissionApis";
          let apis = config.get(apiVar);

          if (apis) {
            apis = apis.filter((x) => x.productType == productType);
            if (apis.length > 0) {
              for (let i = 0; i < apis.length; i++) {
                const borrowingParterRequestModel = apis[i].model != null ? JSON.parse(JSON.stringify(apis[i].model)) : {};
                borrowingParterRequestModel.source = "application";
                borrowingParterRequestModel.module = "kycSubmission";
                borrowingParterRequestModel.loanId = loanInfo._id;
                borrowingParterRequestModel.partnerId = partnerMaster._id;
                borrowingParterRequestModel.partnerCode = partnerMaster.partnerCode;
                borrowingParterRequestModel.partnerDisplayName = partnerMaster.displayName;
                borrowingParterRequestModel.apiType = apis[i].apiType;
                borrowingParterRequestModel.isRetryOffline = apis[i].isRetryOffline;
                borrowingParterRequestModel.url = apis[i].url;

                if (apis[i].isWaitForResponse) {
                  const lendingApiResponse = await callBorrowingPartnerAPI(borrowingParterRequestModel);
                  if (!lendingApiResponse.success) {
                    i = apis.length;
                    output.message = lendingApiResponse.message;
                    partnerLoan.errorMsg = output.message;
                    await partnerLoan.save();
                    reject(output);
                    return output;
                  }
                } else {
                  callBorrowingPartnerAPI(borrowingParterRequestModel);
                }
              }
            }
          }

          apiVar = partnerMaster.partnerCode.toLowerCase() + ".kycApprovedApis";
          apis = config.get(apiVar);

          if (apis) {
            apis = apis.filter((x) => x.productType == productType);
            if (apis.length > 0) {
              for (let i = 0; i < apis.length; i++) {
                const borrowingParterRequestModel = apis[i].model != null ? JSON.parse(JSON.stringify(apis[i].model)) : {};
                borrowingParterRequestModel.source = "application";
                borrowingParterRequestModel.module = "kycApproved";
                borrowingParterRequestModel.loanId = loanInfo._id;
                borrowingParterRequestModel.partnerId = partnerMaster._id;
                borrowingParterRequestModel.partnerCode = partnerMaster.partnerCode;
                borrowingParterRequestModel.partnerDisplayName = partnerMaster.displayName;
                borrowingParterRequestModel.apiType = apis[i].apiType;
                borrowingParterRequestModel.isRetryOffline = apis[i].isRetryOffline;
                borrowingParterRequestModel.url = apis[i].url;

                if (apis[i].isWaitForResponse) {
                  const lendingApiResponse = await callBorrowingPartnerAPI(borrowingParterRequestModel);
                  if (!lendingApiResponse.success) {
                    i = apis.length;
                    output.message = lendingApiResponse.message;
                    partnerLoan.errorMsg = output.message;
                    await partnerLoan.save();
                    resolve(output);
                    return output;
                  }
                } else {
                  callBorrowingPartnerAPI(borrowingParterRequestModel);
                }
              }
            }
          }
        }

        if (userInfo.userDetails.kycStatus == "Under Review") {
          userPartnerInfo.reviewLink = null;
          userPartnerInfo.incorrectReviewDocument = null;
          await userPartnerInfo.save();

          userPartnerInfo = await UserPartnerInfo.findOne({
            userId: mongoose.Types.ObjectId(userInfo._id),
            partnerId: mongoose.Types.ObjectId(partnerMaster._id),
            merchantCode: partnerLoan.merchantCode
          });
        }

        //Bank Details
        const partnerConfig = await PartnerConfig.findOne({ partnerId: mongoose.Types.ObjectId(partnerMaster._id) }).select({ bankValidationAmount: 1, isApiPartner: 1 });

        // let isBankValidated = true;
        // if (!IsNullEmpty(partnerLoan.bankDetails)) {
        //   if (!IsNullEmpty(userPartnerInfo.bankDetails)) {
        //     if (userPartnerInfo.bankDetails[0].docVerification && userPartnerInfo.bankDetails[0].docVerification.isBankVerified) {
        //       isBankValidated = true;
        //     } else {
        //       //check existence of bank in userPartnerInfo
        //       const bankValidation = userPartnerInfo.bankDetails.filter((x) => x.ifsc == partnerLoan.bankDetails.ifsc && x.accountNo == partnerLoan.bankDetails.accountNo);

        //       if (IsNullEmpty(bankValidation)) isBankValidated = false;
        //     }
        //   } else if (partnerConfig && loanAmount >= partnerConfig.bankValidationAmount) {
        //     isBankValidated = false;
        //   } else isBankValidated = false;

        //   const totalLoans = await LoanInfo.countDocuments({ userId: userInfo._id });

        //   if ((!isBankValidated && !partnerLoan.isBankValidated) || totalLoans > 1) {
        //     //if bank is not validated from partner then calling validateAndSaveBankDetails
        //     const validateAndSaveBankDetailsResult = await validateAndSaveBankDetails(userInfo._id, partnerLoan.partnerId, partnerLoan.bankDetails.accountNo, partnerLoan.bankDetails.ifsc, partnerLoan.bankDetails.accountType == "Saving" ? "Savings" : partnerLoan.bankDetails.accountType, partnerLoan.bankDetails.accountName, null, null, partnerLoan.merchantCode, loanInfo._id);

        //     if (!validateAndSaveBankDetailsResult.success) {
        //       isBankValidated = false;
        //     } else isBankValidated = true;
        //   } else isBankValidated = true;
        // } else if (IsNullEmpty(userPartnerInfo.bankDetails)) {
        //   isBankValidated = false;
        // }

        let isBankValidated = false;

        if (!partnerConfig.updatePartnerBank) {
          if (!IsNullEmpty(userPartnerInfo.bankDetails) && userPartnerInfo.bankDetails[0].docVerification && userPartnerInfo.bankDetails[0].docVerification.isBankVerified) {
            const validateAndSaveBankDetailsResult = await validateAndSaveBankDetails(userInfo._id, partnerLoan.partnerId, userPartnerInfo.bankDetails[0].accountNo, userPartnerInfo.bankDetails[0].ifsc, userPartnerInfo.bankDetails[0].accountType == "Saving" ? "Savings" : userPartnerInfo.bankDetails[0].accountType, userPartnerInfo.bankDetails[0].accountName, null, null, partnerLoan.merchantCode, loanInfo._id, false);

            if (validateAndSaveBankDetailsResult.success) isBankValidated = true;
          }
          else if (!IsNullEmpty(partnerLoan.bankDetails)) {
            const validateAndSaveBankDetailsResult = await validateAndSaveBankDetails(userInfo._id, partnerLoan.partnerId, partnerLoan.bankDetails.accountNo, partnerLoan.bankDetails.ifsc, partnerLoan.bankDetails.accountType == "Saving" ? "Savings" : partnerLoan.bankDetails.accountType, partnerLoan.bankDetails.accountName, null, null, partnerLoan.merchantCode, loanInfo._id, false);

            if (validateAndSaveBankDetailsResult.success) isBankValidated = true;
          }
        }
        else if (!IsNullEmpty(partnerLoan.bankDetails)) {
          const validateAndSaveBankDetailsResult = await validateAndSaveBankDetails(userInfo._id, partnerLoan.partnerId, partnerLoan.bankDetails.accountNo, partnerLoan.bankDetails.ifsc, partnerLoan.bankDetails.accountType == "Saving" ? "Savings" : partnerLoan.bankDetails.accountType, partnerLoan.bankDetails.accountName, null, null, partnerLoan.merchantCode, loanInfo._id, false);

          if (validateAndSaveBankDetailsResult.success) isBankValidated = true;
        }

        if (!userInfo.userDetails.clientCode && partnerConfig.isApiPartner) {
          const reviewNotificationModel = {
            userId: userInfo._id,
            loanId: loanInfo._id,
            loanNo: loanInfo.loanNo,
            productId: partnerLoan.productId,
            partnerId: partnerLoan.partnerId,
            partnerCode: partnerMaster.partnerCode,
            merchantCode: partnerLoan.merchantCode,
            trxnId: partnerLoan.trxnId,
            loanAmount: partnerLoan.loanRequest.loanAmount,
            authKey: partnerMaster.authKey,
            shortauthKey: partnerMaster.shortauthKey,
            mobileNo: userInfo.userDetails.mobileNo,
            productType: loanInfo.productType,
            displayName: partnerMaster.displayName,
            type: config.get("shortUrl.reviewCode"),
            incorrectReviewDocument: "PAN and POA",
            kycRemarks: "Please submit PAN and POA documents"
          };
          sendReviewNotification(reviewNotificationModel);
        }
        else if ((!isBankValidated && partnerConfig.isApiPartner)) {
          const reviewNotificationModel = {
            userId: userInfo._id,
            loanId: loanInfo._id,
            loanNo: loanInfo.loanNo,
            productId: partnerLoan.productId,
            partnerId: partnerLoan.partnerId,
            partnerCode: partnerMaster.partnerCode,
            merchantCode: partnerLoan.merchantCode,
            trxnId: partnerLoan.trxnId,
            loanAmount: partnerLoan.loanRequest.loanAmount,
            authKey: partnerMaster.authKey,
            shortauthKey: partnerMaster.shortauthKey,
            mobileNo: userInfo.userDetails.mobileNo,
            productType: loanInfo.productType,
            displayName: partnerMaster.displayName,
            type: config.get("shortUrl.reviewCode"),
            incorrectReviewDocument: "Bank Details",
            kycRemarks: "Missing BankDetails"
          };
          sendReviewNotification(reviewNotificationModel);
        }
        else if (isKycRecapture && partnerConfig.isApiPartner) {
          const reviewNotificationModel = {
            userId: userInfo._id,
            loanId: loanInfo._id,
            loanNo: loanInfo.loanNo,
            productId: partnerLoan.productId,
            partnerId: partnerLoan.partnerId,
            partnerCode: partnerMaster.partnerCode,
            merchantCode: partnerLoan.merchantCode,
            trxnId: partnerLoan.trxnId,
            loanAmount: partnerLoan.loanRequest.loanAmount,
            authKey: partnerMaster.authKey,
            shortauthKey: partnerMaster.shortauthKey,
            mobileNo: userInfo.userDetails.mobileNo,
            productType: loanInfo.productType,
            displayName: partnerMaster.displayName,
            type: config.get("shortUrl.reviewCode"),
            incorrectReviewDocument: "Kyc recapture",
            kycRemarks: "Kyc expired, Please update kyc detail again"
          };
          sendReviewNotification(reviewNotificationModel);
        }
        else if (isBankValidated && partnerConfig.isApiPartner && userInfo.userDetails.kycStatus == "Review" && userPartnerInfo.incorrectReviewDocument == "Bank Details") {
          const userInfo2 = await UserInfo.findById(userInfo._id);
          userInfo2.userDetails.kycStatus = "Under Review";
          userInfo2.userDetails.kycRemarks = null;
          await userInfo2.save();

          userPartnerInfo.reviewLink = null;
          userPartnerInfo.incorrectReviewDocument = null;
          await userPartnerInfo.save();
        }

        loanService.setLoanCharges(loanInfo._id);

        output = {
          data: partnerLoan.productId,
          userId: userInfo._id,
          loanId: loanInfo._id,
          success: true,
          message: "pushed successfully",
          status: 200,
        };
        resolve(output);
        return output;
      }
    } catch (ex) {
      logger.logs("Error : ", ex);
      const model = { partnerLoanId };
      saveErrorLog("PushToFundfina", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
      reject(output);
      return output;
    }
  });
};

async function insertUserInfo(partnerLoanId) {
  let output = {
    userId: null,
    userPartnerInfoId: null,
    success: false,
    message: null
  }
  try {
    const months = ["jan", "feb", "mar", "apr", "may", "jun", "jul", "aug", "sep", "oct", "nov", "dec"];
    const partnerLoan = await PartnerLoans.findById(partnerLoanId);
    const partnerMaster = await PartnerMaster.findById(partnerLoan.partnerId);

    const conf = config.get(partnerMaster.partnerCode.toLowerCase());
    //Insert UserInfo
    const salt = await bycrypt.genSalt(10);
    const password = await bycrypt.hash(partnerLoan.merchantCode, salt);

    const add1 = !IsNullEmpty(partnerLoan.personalDetails.currentAdd) ? removeSpecialChars(partnerLoan.personalDetails.currentAdd) : null;
    const add3 = !IsNullEmpty(partnerLoan.loanRequest.street) ? removeSpecialChars(partnerLoan.loanRequest.street) : null;

    const userInfo = new UserInfo({
      panNo: partnerLoan.panNo,
      userDetails: {
        userName: partnerLoan.loanRequest.mobileNo,
        password: password,
        role: "User",
        firstName: partnerLoan.loanRequest.firstName,
        middleName: partnerLoan.loanRequest.middleName,
        lastName: partnerLoan.loanRequest.lastName,
        dob: partnerLoan.personalDetails.dob,
        employment: null,
        isEmailValidated: true,
        isMobileValidated: true,
        clientCode: null,
        clientCodeGenDate: null,
        clientCodeGenBy: null,
        kycStatus: "Initiated",
      },
      addressDetails: [
        {
          add1: add1,
          add2: null,
          add3: add3,
          type: "correspondence",
          city: partnerLoan.loanRequest.city,
          state: partnerLoan.loanRequest.state,
          pincode: partnerLoan.loanRequest.pincode,
          country: "INDIA",
          insertedOn: Date.now(),
        },
      ],
      shopDetails: [
        {
          partner: partnerMaster.partnerName,
          merchantCode: partnerLoan.merchantCode,
          trxnId: partnerLoan.trxnId,
          productId: partnerLoan.productId,
          shopName: partnerLoan.shopDetails.name,
          shopLocation: partnerLoan.shopDetails.location,
          shopType: partnerLoan.shopDetails.type,
          shopVintage: partnerLoan.shopDetails.shopVinatge,
          shopOwnershipType: !IsNullEmpty(partnerLoan.shopDetails.ownershipType) ? ((partnerLoan.shopDetails.ownershipType.toLowerCase() == "owned" || partnerLoan.shopDetails.ownershipType.toLowerCase() == "owner") ? "Own" : ((partnerLoan.shopDetails.ownershipType.toLowerCase() == "rented" || partnerLoan.shopDetails.ownershipType.toLowerCase() == "rent") ? "Rent" : null)) : null,
          shopAddress: partnerLoan.shopDetails.add,
          city: partnerLoan.shopDetails.city,
          state: partnerLoan.shopDetails.state,
          pincode: partnerLoan.shopDetails.pincode,
          companyType: partnerLoan.shopDetails.companyType,
          businessVintage: partnerLoan.shopDetails.businessVintage,
          contactNo: partnerLoan.shopDetails.contactNo,
          insertedOn: Date.now(),
        },
      ],
      questionnairreDetails: {
        phoneConnectionType: IsNullEmpty(partnerLoan.personalDetails.phoneConnectionType) ? null : partnerLoan.personalDetails.phoneConnectionType,
        whatsApp: IsNullEmpty(partnerLoan.personalDetails.whatsAppInMobile) ? null : partnerLoan.personalDetails.whatsAppInMobile,
        gender: !IsNullEmpty(partnerLoan.personalDetails.gender) ? (partnerLoan.personalDetails.gender.toLowerCase() == "male" ? "Male" : (partnerLoan.personalDetails.gender.toLowerCase() == "female" ? "Female" : (partnerLoan.personalDetails.gender.toLowerCase() == "transgender" ? "Transgender" : null))) : null,
        maritalStatus: !IsNullEmpty(partnerLoan.personalDetails.maritalStatus) ? (partnerLoan.personalDetails.maritalStatus.toLowerCase() == "married" ? "Married" : "Single") : null,
        children: IsNullEmpty(partnerLoan.personalDetails.children) ? null : partnerLoan.personalDetails.children,
        degree: IsNullEmpty(partnerLoan.personalDetails.educationalDegree) ? null : partnerLoan.personalDetails.educationalDegree,
        monthlyIncome: IsNullEmpty(partnerLoan.personalDetails.avgMonthlyIncome) ? null : partnerLoan.personalDetails.avgMonthlyIncome,
        monthlyOutgoing: IsNullEmpty(partnerLoan.personalDetails.avgMonthlyOutgoings) ? null : partnerLoan.personalDetails.avgMonthlyOutgoings,
        jobType: IsNullEmpty(partnerLoan.personalDetails.jobType) ? null : partnerLoan.personalDetails.jobType,
        homeOwnershipType: !IsNullEmpty(partnerLoan.personalDetails.homeOwnershipType) ? ((partnerLoan.personalDetails.homeOwnershipType.toLowerCase() == "owned" || partnerLoan.personalDetails.homeOwnershipType.toLowerCase() == "owner") ? "Own" : ((partnerLoan.personalDetails.homeOwnershipType.toLowerCase() == "rented" || partnerLoan.personalDetails.homeOwnershipType.toLowerCase() == "rent") ? "Rent" : null)) : null,
        vehicle: IsNullEmpty(partnerLoan.personalDetails.vehicle) ? null : partnerLoan.personalDetails.vehicle,
        purchasedInOneYear: IsNullEmpty(partnerLoan.personalDetails.whiteGoodsIn24Months) ? null : partnerLoan.personalDetails.whiteGoodsIn24Months,
        recommended: null,
        recommendedBy: null
      },
      ipAddress: IsNullEmpty(partnerLoan.ipAddress) ? null : partnerLoan.ipAddress,
      insertedOn: Date.now(),
      modifiedOn: Date.now()
    });

    if (!IsNullEmpty(partnerLoan.loanRequest.emailId)) {
      userInfo.userDetails.emailId.push(partnerLoan.loanRequest.emailId);
    }

    let isPrimaryNumber = !userInfo.userDetails.mobileNo.length

    userInfo.userDetails.mobileNo.push(buildMobileNumberObject(partnerLoan.loanRequest.mobileNo, isPrimaryNumber, isPrimaryNumber)); //mobile number change
    // }

    //Insert Mobiles
    if (!IsNullEmpty(partnerLoan.personalDetails.mobile)) {
      let checkExists = await userInfo.userDetails.mobileNo.find(
        (element) => element.mobileNumber === partnerLoan.personalDetails.mobile //mobile number change
      );
      isPrimaryNumber = !userInfo.userDetails.mobileNo.length

      if (IsNullEmpty(checkExists)) {
        userInfo.userDetails.mobileNo.push(
          buildMobileNumberObject(partnerLoan.personalDetails.mobile, isPrimaryNumber, isPrimaryNumber) //mobile number change
        );
      }
    }

    if (!IsNullEmpty(partnerLoan.personalDetails.alternateMobile)) {
      const checkExists = await userInfo.userDetails.mobileNo.find((element) => element.mobileNumber === partnerLoan.personalDetails.alternateMobile);//mobile number change

      if (IsNullEmpty(checkExists)) {
        userInfo.userDetails.mobileNo.push(
          buildMobileNumberObject(partnerLoan.personalDetails.alternateMobile, false, false) //mobile number change
        );
      }
    }

    if (!IsNullEmpty(partnerLoan.accoutDetails.mobile)) {
      const checkExists = await userInfo.userDetails.mobileNo.find((element) => element.mobileNumber === partnerLoan.accoutDetails.mobile); //mobile number change
      isPrimaryNumber = !userInfo.userDetails.mobileNo.length
      if (IsNullEmpty(checkExists)) {
        userInfo.userDetails.mobileNo.push(
          buildMobileNumberObject(partnerLoan.accoutDetails.mobile, isPrimaryNumber, isPrimaryNumber)//mobile number change
        );
      }
    }

    //Step Details
    if (conf.isDigiLockerMandatory && conf.isDigiLockerMandatory == true && partnerLoan.digilocker && partnerLoan.digilocker.execution_request_id) {
      userInfo.userDetails.kycStatus = "Approved";
      userInfo.kycStepDetails = [
        {
          Step: "Registration",
          isCompleted: true,
          insertedOn: Date.now(),
          completedOn: Date.now(),
        },
        {
          Step: "Loan",
          isCompleted: true,
          insertedOn: Date.now(),
          completedOn: Date.now(),
        },
        {
          Step: "KYC",
          isCompleted: true,
          insertedOn: Date.now(),
          completedOn: Date.now(),
        },
        {
          Step: "Shop",
          isCompleted: true,
          insertedOn: Date.now(),
          completedOn: Date.now(),
        },
        {
          Step: "Questionnairre",
          isCompleted: true,
          insertedOn: Date.now(),
          completedOn: Date.now(),
        }
      ];
    } else if (conf.isAutoCompleteKycSteps == true) {
      userInfo.userDetails.kycStatus = "Under Review";

      userInfo.kycStepDetails = [
        {
          Step: "Registration",
          isCompleted: true,
          insertedOn: Date.now(),
          completedOn: Date.now(),
        },
        {
          Step: "Loan",
          isCompleted: true,
          insertedOn: Date.now(),
          completedOn: Date.now(),
        },
        {
          Step: "KYC",
          isCompleted: true,
          insertedOn: Date.now(),
          completedOn: Date.now(),
        },
        {
          Step: "Shop",
          isCompleted: true,
          insertedOn: Date.now(),
          completedOn: Date.now(),
        },
        {
          Step: "Questionnairre",
          isCompleted: true,
          insertedOn: Date.now(),
          completedOn: Date.now(),
        }
      ];
    } else {
      userInfo.userDetails.employment = "Business";
      userInfo.kycStepDetails = [
        {
          Step: "Registration",
          isCompleted: true,
          insertedOn: Date.now(),
          completedOn: Date.now(),
        },
      ];
    }

    //Transactions
    if (!IsNullEmpty(partnerLoan.transactionDetails) && partnerLoan.transactionDetails.length > 0) {
      for (itemTransaction of partnerLoan.transactionDetails) {
        let monthYear = itemTransaction.monthYear.split("/");

        monthYear[0] = monthYear[0] - 1;

        let monthlyTranCount = await userInfo.monthlyTranCount.find((x) => x.year == monthYear[1] && x.partner == partnerMaster.partnerName && x.serviceType == itemTransaction.type);

        if (IsNullEmpty(monthlyTranCount)) {
          monthlyTranCount = {
            year: monthYear[1],
            partner: partnerMaster.partnerName,
            serviceType: itemTransaction.type,
            jan: 0,
            feb: 0,
            mar: 0,
            apr: 0,
            may: 0,
            jun: 0,
            jul: 0,
            aug: 0,
            sep: 0,
            oct: 0,
            nov: 0,
            dec: 0,
          };

          monthlyTranCount[months[monthYear[0]]] =
            itemTransaction.primaryCount;
          userInfo.monthlyTranCount.push(monthlyTranCount);
        } else {
          monthlyTranCount[months[monthYear[0]]] =
            itemTransaction.primaryCount;
        }

        let monthlyTranVol = await userInfo.monthlyTranVol.find((x) => x.year == monthYear[1] &&
          x.partner == partnerMaster.partnerName && x.serviceType == itemTransaction.type);

        if (IsNullEmpty(monthlyTranVol)) {
          monthlyTranVol = {
            year: monthYear[1],
            partner: partnerMaster.partnerName,
            serviceType: itemTransaction.type,
            jan: 0,
            feb: 0,
            mar: 0,
            apr: 0,
            may: 0,
            jun: 0,
            jul: 0,
            aug: 0,
            sep: 0,
            oct: 0,
            nov: 0,
            dec: 0,
          };

          monthlyTranVol[months[monthYear[0]]] = itemTransaction.value;
          userInfo.monthlyTranVol.push(monthlyTranVol);
        } else {
          monthlyTranVol[months[monthYear[0]]] = itemTransaction.value;
        }
      }

      for (tranCount of userInfo.monthlyTranCount) {
        let totalMonthlyTranCount = await userInfo.totalMonthlyTranCount.find(
          (x) =>
            x.year == tranCount.year &&
            x.partner == tranCount.partner &&
            x.serviceType == tranCount.serviceType
        );
        let totalCount = 0;

        if (IsNullEmpty(totalMonthlyTranCount)) {
          for (i = 0; i < 12; i++) {
            totalCount = totalCount + tranCount[months[i]];
          }
          totalMonthlyTranCount = {
            year: tranCount.year,
            partner: tranCount.partner,
            serviceType: tranCount.serviceType,
            totalCount: totalCount,
          };
          userInfo.totalMonthlyTranCount.push(totalMonthlyTranCount);
        } else {
          for (i = 0; i < 12; i++) {
            totalCount = totalCount + tranCount[months[i]];
          }
          totalMonthlyTranCount.totalCount = totalCount;
        }
      }

      for (tranCount of userInfo.monthlyTranVol) {
        let totalMonthlyTranVol = await userInfo.totalMonthlyTranVol.find(
          (x) =>
            x.year == tranCount.year &&
            x.partner == tranCount.partner &&
            x.serviceType == tranCount.serviceType
        );
        let totalCount = 0;

        if (IsNullEmpty(totalMonthlyTranVol)) {
          for (i = 0; i < 12; i++) {
            totalCount = totalCount + tranCount[months[i]];
          }
          totalMonthlyTranVol = {
            year: tranCount.year,
            partner: tranCount.partner,
            serviceType: tranCount.serviceType,
            totalCount: totalCount,
          };
          userInfo.totalMonthlyTranVol.push(totalMonthlyTranVol);
        } else {
          for (i = 0; i < 12; i++) {
            totalCount = totalCount + tranCount[months[i]];
          }
          totalMonthlyTranVol.totalCount = totalCount;
        }
      }
    }

    await userInfo.save();
    output.userId = userInfo._id;

    userPartnerInfo = new UserPartnerInfo({
      userId: mongoose.Types.ObjectId(userInfo._id),
      partnerId: mongoose.Types.ObjectId(partnerMaster._id),
      merchantCode: partnerLoan.merchantCode,
      isRetailer: true,
      isPartnerUser: false,
      onboardedDate: partnerLoan.loanRequest.onboardedDate,
      loanHistory: [],
      bankDetails: [],
      insertedOn: Date.now(),
      updatedOn: Date.now()
    });

    if (partnerLoan.isBankValidated) {
      const bankMaster = await BankBranchMaster.findOne({ ifsc: { $regex: new RegExp("^" + partnerLoan.bankDetails.ifsc + "$", "i") } });

      let accountType = "Savings"
      if (partnerLoan.bankDetails.accountType) {
        accountType = partnerLoan.bankDetails.accountType.toLowerCase() == "saving" ? "Savings" : partnerLoan.bankDetails.accountType;
      }

      const bankDetails = [{
        bankName: bankMaster.bank,
        branch: bankMaster.branch,
        accountNo: partnerLoan.bankDetails.accountNo,
        accountName: partnerLoan.bankDetails.accountName,
        ifsc: partnerLoan.bankDetails.ifsc,
        accountType: accountType,
        address: bankMaster.address,
        city: bankMaster.city,
        state: bankMaster.state,
        docDetails: [],
        insertedOn: Date.now(),
        docVerification: {
          isBankVerified: true,
          id: null,
          verified: true,
          verifiedAt: Date.now(),
          beneficiary_name_with_bank: partnerLoan.beneficiary_name,
          createdOn: Date.now()
        },
        errorDetails: {
          details: null,
          code: null,
          message: null
        }
      }];

      userPartnerInfo.bankDetails = bankDetails;
    }

    //Loan History
    if (!IsNullEmpty(partnerLoan.loanHistory) && partnerLoan.loanHistory.length > 0) {
      for (itemLoanHistory of partnerLoan.loanHistory) {
        let loanHistory = {
          trxnId: partnerLoan.trxnId,
          productId: partnerLoan.productId,
          providerName: itemLoanHistory.providerName,
          amount: itemLoanHistory.amount,
          status: itemLoanHistory.status,
          totalRepaidAmount: itemLoanHistory.totalRepaidAmount,
          insertedOn: Date.now(),
        };
        userPartnerInfo.loanHistory.push(loanHistory);
      }
    }
    await userPartnerInfo.save();
    output.userPartnerInfoId = userPartnerInfo._id;
    //Update UserId in partnerTransactions in Credit
    const url = config.get("keys.creditApiUrl") + "/api/credit/updatePartnerTransactionUserId";
    const creditModel = { partnerId: partnerMaster._id, merchantCode: partnerLoan.merchantCode, userId: userInfo._id }
    const bodyData = JSON.stringify(creditModel);
    //console.log(url);
    //console.log("bodyData", bodyData);

    let creditResponse = await fetch(
      url,
      {
        method: "Put",
        body: bodyData,
        headers: {
          "Content-Type": "application/json"
        },
      }
    )
    // .then(function (u) {
    //   return u.json();
    // })
    // .then(function (json) {
    //   return json;
    // })
    // .catch((err) => {
    //   return err;
    // });
    logger.logs("creditResponse", creditResponse);
    output = {
      userId: userInfo._id,
      userPartnerInfoId: userPartnerInfo._id,
      success: true,
      message: "inserted successfully"
    }
  } catch (ex) {
    logger.logs("Error : ", ex);
    //await partnerLoan.save();
    saveErrorLog("insertUserInfo", apiPath, { partnerLoanId }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
  }
  return output;
};

async function updateUserInfo(partnerLoanId, userId) {
  let output = {
    userId: null,
    userPartnerInfoId: null,
    success: false,
    message: null
  };
  const model = { partnerLoanId, userId };
  try {
    const months = ["jan", "feb", "mar", "apr", "may", "jun", "jul", "aug", "sep", "oct", "nov", "dec"];
    let _isChanged = false;
    const userInfo = await UserInfo.findById(userId);
    const partnerLoan = await PartnerLoans.findById(partnerLoanId);
    const partnerMaster = await PartnerMaster.findById(partnerLoan.partnerId);
    logger.logs("userId", userId);
    const conf = config.get(partnerMaster.partnerCode.toLowerCase());

    if (IsNullEmpty(userInfo.panNo)) {
      userInfo.panNo = partnerLoan.panNo;
      userInfo.updatedOn = Date.now();
      _isChanged = true;
    }

    let userPartnerInfo = await UserPartnerInfo.findOne({ partnerId: mongoose.Types.ObjectId(partnerMaster._id), merchantCode: partnerLoan.merchantCode, userId: mongoose.Types.ObjectId(userInfo._id) });

    //Create if UserPartnerInfo does not exists
    if (IsNullEmpty(userPartnerInfo)) {
      logger.logs("user does not exists in UserPartnerInfo");

      userPartnerInfo = new UserPartnerInfo({
        userId: mongoose.Types.ObjectId(userInfo._id),
        partnerId: mongoose.Types.ObjectId(partnerMaster._id),
        merchantCode: partnerLoan.merchantCode,
        isRetailer: true,
        isPartnerUser: false,
        reviewLink: null,
        incorrectReviewDocument: null,
        onboardedDate: partnerLoan.loanRequest.onboardedDate,
        loanHistory: [],
        bankDetails: [],
        insertedOn: Date.now(),
        updatedOn: Date.now()
      });
    }

    //Insert Bank Details in UserPartnerInfo only if Bank is Validated From Partner
    if (!IsNullEmpty(partnerLoan.isBankValidated) && partnerLoan.isBankValidated == true) {
      const bankMaster = await BankBranchMaster.findOne({
        ifsc: {
          $regex: new RegExp("^" + partnerLoan.bankDetails.ifsc + "$", "i"),
        },
      });

      let accountType = "Savings";

      if (partnerLoan.bankDetails.accountType) {
        accountType = partnerLoan.bankDetails.accountType.toLowerCase() == "saving" ? "Savings" : partnerLoan.bankDetails.accountType;
      }

      const bankDetails = [{
        bankName: bankMaster.bank,
        branch: bankMaster.branch,
        accountNo: partnerLoan.bankDetails.accountNo,
        accountName: partnerLoan.bankDetails.accountName,
        ifsc: partnerLoan.bankDetails.ifsc,
        accountType: accountType,
        address: bankMaster.address,
        city: bankMaster.city,
        state: bankMaster.state,
        docDetails: [],
        insertedOn: Date.now(),
        docVerification: {
          isBankVerified: true,
          id: null,
          verified: null,
          verifiedAt: Date.now(),
          beneficiary_name_with_bank: partnerLoan.beneficiary_name,
          createdOn: Date.now()
        },
        errorDetails: {
          details: null,
          code: null,
          message: null
        }
      }];

      userPartnerInfo.bankDetails = bankDetails;
    }

    //update onboardedDate if onboardedDate is not avilable in UserPartnerInfo
    if (IsNullEmpty(userPartnerInfo.onboardedDate) && !IsNullEmpty(partnerLoan.loanRequest.onboardedDate)) {
      userPartnerInfo.onboardedDate = partnerLoan.loanRequest.onboardedDate;
    }

    //update Loan History in UserPartnerInfo
    if (!IsNullEmpty(partnerLoan.loanHistory)) {
      for (itemLoanHistory of partnerLoan.loanHistory) {
        let checkExists = null;

        if (!IsNullEmpty(userPartnerInfo.loanHistory)) {
          checkExists = await userPartnerInfo.loanHistory.find((x) =>
            x.partner == partnerMaster.partnerName &&
            x.merchantCode == partnerLoan.merchantCode &&
            x.providerName == itemLoanHistory.providerName &&
            x.amount == itemLoanHistory.amount
          );
        } else {
          userPartnerInfo.loanHistory = [];
        }

        if (IsNullEmpty(checkExists)) {
          userPartnerInfo.loanHistory.push({
            partner: partnerMaster.partnerName,
            merchantCode: partnerLoan.merchantCode,
            trxnId: partnerLoan.trxnId,
            productId: partnerLoan.productId,
            providerName: itemLoanHistory.providerName,
            amount: itemLoanHistory.amount,
            status: itemLoanHistory.status,
            totalRepaidAmount: itemLoanHistory.totalRepaidAmount,
            insertedOn: Date.now(),
          });
        }
      }
    }

    await userPartnerInfo.save();

    userPartnerInfo = await UserPartnerInfo.findOne({
      userId: mongoose.Types.ObjectId(userInfo._id),
      partnerId: mongoose.Types.ObjectId(partnerMaster._id),
      merchantCode: partnerLoan.merchantCode
    });

    output.userId = userInfo._id;
    output.userPartnerInfoId = userPartnerInfo._id;

    let isApplingWithDiffPAN = (userInfo.panNo != partnerLoan.panNo) ? true : false;

    const clientCode = await ClientCodeRepository.countDocuments({ panNumber: userInfo.panNo });

    if (isApplingWithDiffPAN) {
      userInfo.panNo = partnerLoan.panNo;
      userInfo.userDetails.clientCode = null;
      userInfo.userDetails.clientCodeGenDate = null;
      userInfo.userDetails.clientCodeGenBy = null;
      userInfo.userDetails.kycStatus = "Under Review";
      userInfo.updatedOn = Date.now();
      _isChanged = true;
    } else if (clientCode == 0) {
      //Check with Existing Partner Loan
      const existingPartnerLoan = await PartnerLoans.findOne({ partnerId: partnerLoan.partnerId, merchantCode: partnerLoan.merchantCode, _id: { $ne: mongoose.Types.ObjectId(partnerLoanId) } }).sort({ insertedOn: -1 });

      if (existingPartnerLoan) {
        logger.logs("checking current request with existing request");

        if (partnerLoan.personalDetails && existingPartnerLoan.personalDetails && (
          partnerLoan.personalDetails.currentAdd != existingPartnerLoan.personalDetails.currentAdd ||
          partnerLoan.personalDetails.mobile != existingPartnerLoan.personalDetails.mobile ||
          partnerLoan.personalDetails.alternateMobile != existingPartnerLoan.personalDetails.alternateMobile
        ))
          _isChanged = true;
        else if (partnerLoan.personalDetails && !existingPartnerLoan.personalDetails)
          _isChanged = true;

        if (partnerLoan.loanRequest && existingPartnerLoan.loanRequest && (
          partnerLoan.loanRequest.street != existingPartnerLoan.loanRequest.street ||
          partnerLoan.loanRequest.city != existingPartnerLoan.loanRequest.city ||
          partnerLoan.loanRequest.state != existingPartnerLoan.loanRequest.state ||
          partnerLoan.loanRequest.pincode != existingPartnerLoan.loanRequest.pincode ||
          partnerLoan.loanRequest.emailId != existingPartnerLoan.loanRequest.emailId ||
          partnerLoan.loanRequest.mobileNo != existingPartnerLoan.loanRequest.mobileNo))
          _isChanged = true;
        else if (partnerLoan.loanRequest && !existingPartnerLoan.loanRequest)
          _isChanged = true;

        if (partnerLoan.shopDetails && existingPartnerLoan.shopDetails && (
          partnerLoan.shopDetails.name != existingPartnerLoan.shopDetails.name ||
          partnerLoan.shopDetails.location != existingPartnerLoan.shopDetails.location ||
          partnerLoan.shopDetails.type != existingPartnerLoan.shopDetails.type ||
          partnerLoan.shopDetails.ownershipType != existingPartnerLoan.shopDetails.ownershipType ||
          partnerLoan.shopDetails.add != existingPartnerLoan.shopDetails.add ||
          partnerLoan.shopDetails.city != existingPartnerLoan.shopDetails.city ||
          partnerLoan.shopDetails.state != existingPartnerLoan.shopDetails.state ||
          partnerLoan.shopDetails.pincode != existingPartnerLoan.shopDetails.pincode ||
          partnerLoan.shopDetails.companyType != existingPartnerLoan.shopDetails.companyType ||
          partnerLoan.shopDetails.businessVintage != existingPartnerLoan.shopDetails.businessVintage ||
          partnerLoan.shopDetails.contactNo != existingPartnerLoan.shopDetails.contactNo))
          _isChanged = true;
        else if (partnerLoan.shopDetails && !existingPartnerLoan.shopDetails)
          _isChanged = true;

        if (partnerLoan.accoutDetails && existingPartnerLoan.accoutDetails && partnerLoan.accoutDetails.mobile != existingPartnerLoan.accoutDetails.mobile)
          _isChanged = true;
        else if (partnerLoan.accoutDetails && !existingPartnerLoan.accoutDetails)
          _isChanged = true;
      }
    }

    logger.logs("_isChanged", _isChanged);

    if (_isChanged) {
      if (!isApplingWithDiffPAN) _isChanged = false;

      if (IsNullEmpty(userInfo.userDetails.userName) && !IsNullEmpty(partnerLoan.loanRequest.mobileNo)) {
        userInfo.userDetails.userName = partnerLoan.loanRequest.mobileNo;
        _isChanged = true;
      }

      if (IsNullEmpty(userInfo.userDetails.password)) {
        const salt = await bycrypt.genSalt(10);
        const password = await bycrypt.hash(partnerLoan.merchantCode, salt);
        userInfo.userDetails.password = password
      }

      if (IsNullEmpty(userInfo.userDetails.role)) {
        userInfo.userDetails.role = "User";
      }

      if (!IsNullEmpty(partnerLoan.loanRequest.firstName) && (IsNullEmpty(userInfo.userDetails.firstName) || isApplingWithDiffPAN == true)) {
        userInfo.userDetails.firstName = partnerLoan.loanRequest.firstName;
        _isChanged = true;
      }

      if (!IsNullEmpty(partnerLoan.loanRequest.middleName) && (IsNullEmpty(userInfo.userDetails.middleName) || isApplingWithDiffPAN == true)) {
        userInfo.userDetails.middleName = partnerLoan.loanRequest.middleName;
        _isChanged = true;
      }

      if (!IsNullEmpty(partnerLoan.loanRequest.lastName) && (IsNullEmpty(userInfo.userDetails.lastName) || isApplingWithDiffPAN == true)) {
        userInfo.userDetails.lastName = partnerLoan.loanRequest.lastName;
        _isChanged = true;
      }


      //As requested by Uttam if panDocVerification.type == "digilocker" then don't update dob and address -- 16-Jan-2023
      if (!userInfo.panDocVerification || (userInfo.panDocVerification && userInfo.panDocVerification.type !== "digilocker")) {
        if (!IsNullEmpty(partnerLoan.personalDetails.dob) && (IsNullEmpty(userInfo.userDetails.dob) || isApplingWithDiffPAN == true)) {
          userInfo.userDetails.dob = partnerLoan.personalDetails.dob;
          _isChanged = true;
        }

        let _addressDetails = null;
        if (!IsNullEmpty(partnerLoan.personalDetails.currentAdd) && (userInfo.addressDetails || isApplingWithDiffPAN == true)) {
          _addressDetails = userInfo.addressDetails.filter((x) => x.add1 != null && x.add1.toLowerCase() == partnerLoan.personalDetails.currentAdd.toLowerCase());
        }

        if (!IsNullEmpty(_addressDetails)) {
          userInfo.addressDetails[0].add1 = !IsNullEmpty(partnerLoan.personalDetails.currentAdd) ? removeSpecialChars(partnerLoan.personalDetails.currentAdd) : null;
          userInfo.addressDetails[0].add2 = null;
          userInfo.addressDetails[0].add3 = !IsNullEmpty(partnerLoan.loanRequest.street) ? removeSpecialChars(partnerLoan.loanRequest.street) : null;
          userInfo.addressDetails[0].type = "correspondence";
          userInfo.addressDetails[0].city = partnerLoan.loanRequest.city;
          userInfo.addressDetails[0].state = partnerLoan.loanRequest.state;
          userInfo.addressDetails[0].pincode = partnerLoan.loanRequest.pincode;
          userInfo.addressDetails[0].country = "INDIA";
          userInfo.addressDetails[0].insertedOn = Date.now();
          _isChanged = true;
        } else {
          if (userInfo.userDetails.kycStatus == null) userInfo.addressDetails = [];

          let addressDetails = {
            add1: !IsNullEmpty(partnerLoan.personalDetails.currentAdd) ? removeSpecialChars(partnerLoan.personalDetails.currentAdd) : null,
            add2: null,
            add3: !IsNullEmpty(partnerLoan.loanRequest.street) ? removeSpecialChars(partnerLoan.loanRequest.street) : null,
            type: "correspondence",
            city: partnerLoan.loanRequest.city,
            state: partnerLoan.loanRequest.state,
            pincode: partnerLoan.loanRequest.pincode,
            country: "INDIA",
            insertedOn: Date.now()
          }

          userInfo.addressDetails.push(addressDetails);
          _isChanged = true;
        }
      }

      let _shopDetails = null;
      if (!IsNullEmpty(partnerLoan.shopDetails.name) && (!IsNullEmpty(userInfo.shopDetails) || isApplingWithDiffPAN == true)) {
        _shopDetails = userInfo.shopDetails.filter((x) => x.shopName != null && x.shopName.toLowerCase() == partnerLoan.shopDetails.name.toLowerCase());
      } else if (!IsNullEmpty(userInfo.shopDetails)) {
        _shopDetails = userInfo.shopDetails.filter((x) => x.shopName == partnerLoan.shopDetails.name);
      }

      if (IsNullEmpty(_shopDetails)) {

        let shopDetails = {
          partner: partnerMaster.partnerName,
          merchantCode: partnerLoan.merchantCode,
          trxnId: partnerLoan.trxnId,
          productId: partnerLoan.productId,
          shopName: partnerLoan.shopDetails.name,
          shopLocation: partnerLoan.shopDetails.location,
          shopType: partnerLoan.shopDetails.type,
          shopVintage: partnerLoan.shopDetails.shopVinatge,
          shopOwnershipType: !IsNullEmpty(partnerLoan.shopDetails.ownershipType) ? ((partnerLoan.shopDetails.ownershipType.toLowerCase() == "owned" || partnerLoan.shopDetails.ownershipType.toLowerCase() == "owner") ? "Own" : ((partnerLoan.shopDetails.ownershipType.toLowerCase() == "rented" || partnerLoan.shopDetails.ownershipType.toLowerCase() == "rent") ? "Rent" : null)) : null,
          shopAddress: !IsNullEmpty(partnerLoan.shopDetails.add) ? removeSpecialChars(partnerLoan.shopDetails.add) : null,
          city: partnerLoan.shopDetails.city,
          state: partnerLoan.shopDetails.state,
          pincode: partnerLoan.shopDetails.pincode,
          companyType: partnerLoan.shopDetails.companyType,
          businessVintage: partnerLoan.shopDetails.businessVintage,
          contactNo: partnerLoan.shopDetails.contactNo,
          insertedOn: Date.now(),
        };
        userInfo.shopDetails.push(shopDetails);
        _isChanged = true;
      } else {
        for (itemShop of userInfo.shopDetails) {
          if (IsNullEmpty(itemShop.shopLocation) && !IsNullEmpty(partnerLoan.shopDetails.location)) {
            itemShop.shopLocation = partnerLoan.shopDetails.location;
            _isChanged = true;
          }

          if (IsNullEmpty(itemShop.shopType) && !IsNullEmpty(partnerLoan.shopDetails.type)) {
            itemShop.shopType = partnerLoan.shopDetails.type;
            _isChanged = true;
          }

          if (IsNullEmpty(itemShop.shopVintage) && !IsNullEmpty(partnerLoan.shopDetails.shopVinatge)
          ) {
            itemShop.shopVintage = partnerLoan.shopDetails.shopVinatge;
            _isChanged = true;
          }

          if (IsNullEmpty(itemShop.shopOwnershipType) && !IsNullEmpty(partnerLoan.shopDetails.ownershipType)
          ) {
            itemShop.shopOwnershipType = partnerLoan.shopDetails.ownershipType;
            _isChanged = true;
          }

          if (IsNullEmpty(itemShop.shopAddress) && !IsNullEmpty(partnerLoan.shopDetails.add)
          ) {
            itemShop.shopAddress = removeSpecialChars(partnerLoan.shopDetails.add);
            _isChanged = true;
          }

          if (IsNullEmpty(itemShop.city) && !IsNullEmpty(partnerLoan.shopDetails.city)
          ) {
            itemShop.city = partnerLoan.shopDetails.city;
            _isChanged = true;
          }

          if (IsNullEmpty(itemShop.state) && !IsNullEmpty(partnerLoan.shopDetails.state)
          ) {
            itemShop.state = partnerLoan.shopDetails.state;
            _isChanged = true;
          }

          if (IsNullEmpty(itemShop.pincode) && !IsNullEmpty(partnerLoan.shopDetails.pincode)
          ) {
            itemShop.pincode = partnerLoan.shopDetails.pincode;
            _isChanged = true;
          }

          if (IsNullEmpty(itemShop.companyType) && !IsNullEmpty(partnerLoan.shopDetails.companyType)
          ) {
            itemShop.companyType = partnerLoan.shopDetails.companyType;
            _isChanged = true;
          }

          if (IsNullEmpty(itemShop.businessVintage) && !IsNullEmpty(partnerLoan.shopDetails.businessVintage)
          ) {
            itemShop.businessVintage = partnerLoan.shopDetails.businessVintage;
            _isChanged = true;
          }

          if (IsNullEmpty(itemShop.contactNo) && !IsNullEmpty(partnerLoan.shopDetails.contactNo)
          ) {
            itemShop.contactNo = partnerLoan.shopDetails.contactNo;
            _isChanged = true;
          }
        }
      }

      if (!IsNullEmpty(partnerLoan.loanRequest.emailId)) {
        let checkExists = await userInfo.userDetails.emailId.find((element) => element === partnerLoan.loanRequest.emailId);

        if (IsNullEmpty(checkExists)) {
          userInfo.userDetails.emailId.push(partnerLoan.loanRequest.emailId);
          _isChanged = true;
        }
      }

      logger.logs("partnerLoan.loanRequest.mobileNo", partnerLoan.loanRequest.mobileNo);
      if (!IsNullEmpty(partnerLoan.loanRequest.mobileNo)) {
        let checkExists = await userInfo.userDetails.mobileNo.find((element) => element.mobileNumber === partnerLoan.loanRequest.mobileNo); //mobile number object

        if (IsNullEmpty(checkExists)) {
          userInfo.userDetails.mobileNo.splice(0, 0, buildMobileNumberObject(partnerLoan.loanRequest.mobileNo)); //mobile number object
          _isChanged = true;
        } else {
          let allMobileNumbers = getMobileNumberArray(userInfo.userDetails.mobileNo) //mobile number object
          const index = allMobileNumbers.indexOf(partnerLoan.loanRequest.mobileNo);
          if (index > -1) {
            userInfo.userDetails.mobileNo.splice(index, 1);
          }
          userInfo.userDetails.mobileNo.splice(0, 0, buildMobileNumberObject(partnerLoan.loanRequest.mobileNo)); //mobile number object
        }
      }

      if (!IsNullEmpty(partnerLoan.personalDetails.mobile)) {
        let checkExists = await userInfo.userDetails.mobileNo.find((element) => element.mobileNumber === partnerLoan.personalDetails.mobile); //mobile number object

        if (IsNullEmpty(checkExists)) {
          userInfo.userDetails.mobileNo.splice(0, 0, buildMobileNumberObject(partnerLoan.personalDetails.mobile)); //mobile number object
          _isChanged = true;
        } else {
          let allMobileNumbers = getMobileNumberArray(userInfo.userDetails.mobileNo) //mobile number object
          const index = allMobileNumbers.indexOf(partnerLoan.personalDetails.mobile);
          if (index > -1) {
            userInfo.userDetails.mobileNo.splice(index, 1);
          }
          userInfo.userDetails.mobileNo.splice(0, 0, buildMobileNumberObject(partnerLoan.personalDetails.mobile)); //mobile number object
        }
      }

      if (!IsNullEmpty(partnerLoan.personalDetails.alternateMobile)) {
        let checkExists = await userInfo.userDetails.mobileNo.find((element) => element.mobileNumber === partnerLoan.personalDetails.alternateMobile); //mobile number object

        if (IsNullEmpty(checkExists)) {
          userInfo.userDetails.mobileNo.push(
            buildMobileNumberObject(partnerLoan.personalDetails.alternateMobile, false, false)
          );
          _isChanged = true;
        }
      }

      if (!IsNullEmpty(partnerLoan.accoutDetails.mobile)) {
        let checkExists = await userInfo.userDetails.mobileNo.find((element) => element.mobileNumber === partnerLoan.accoutDetails.mobile); //mobile number object

        if (IsNullEmpty(checkExists)) {
          userInfo.userDetails.mobileNo.push(
            buildMobileNumberObject(partnerLoan.accoutDetails.mobile, false, false) //mobile number object
          );
          _isChanged = true;
        }
      }

      userInfo.userDetails.mobileNo.forEach((mobileNumberElement, index) => mobileNumberElement.isPrimary = index === 0) //mobile number change

      //Transactions
      if (conf.isByPassUserDataUrl == true && conf.isAutoCompleteKycSteps == true && !IsNullEmpty(partnerLoan.transactionDetails)) {
        for (itemTransaction of partnerLoan.transactionDetails) {
          let monthYear = itemTransaction.monthYear.split("/");
          monthYear[0] = monthYear[0] - 1;
          let monthlyTranCount = null;

          if (!IsNullEmpty(userInfo.monthlyTranCount)) {
            monthlyTranCount = await userInfo.monthlyTranCount.find(
              (x) =>
                x.year == monthYear[1] &&
                x.partner == partnerMaster.partnerName &&
                x.serviceType == itemTransaction.type
            );
          }

          if (IsNullEmpty(monthlyTranCount)) {
            monthlyTranCount = {
              year: monthYear[1],
              partner: partnerMaster.partnerName,
              serviceType: itemTransaction.type,
              jan: 0,
              feb: 0,
              mar: 0,
              apr: 0,
              may: 0,
              jun: 0,
              jul: 0,
              aug: 0,
              sep: 0,
              oct: 0,
              nov: 0,
              dec: 0,
            };

            monthlyTranCount[months[monthYear[0]]] =
              itemTransaction.primaryCount;
            userInfo.monthlyTranCount.push(monthlyTranCount);
          } else {
            monthlyTranCount[months[monthYear[0]]] =
              itemTransaction.primaryCount;
          }

          let monthlyTranVol = null;
          if (!IsNullEmpty(userInfo.monthlyTranCount)) {
            monthlyTranVol = await userInfo.monthlyTranVol.find(
              (x) =>
                x.year == monthYear[1] &&
                x.partner == partnerMaster.partnerName &&
                x.serviceType == itemTransaction.type
            );
          }

          if (IsNullEmpty(monthlyTranVol)) {
            monthlyTranVol = {
              year: monthYear[1],
              partner: partnerMaster.partnerName,
              serviceType: itemTransaction.type,
              jan: 0,
              feb: 0,
              mar: 0,
              apr: 0,
              may: 0,
              jun: 0,
              jul: 0,
              aug: 0,
              sep: 0,
              oct: 0,
              nov: 0,
              dec: 0,
            };

            monthlyTranVol[months[monthYear[0]]] = itemTransaction.value;
            userInfo.monthlyTranVol.push(monthlyTranVol);
          } else {
            monthlyTranCount[months[monthYear[0]]] =
              itemTransaction.value;
          }
        }

        for (tranCount of userInfo.monthlyTranCount) {
          let totalMonthlyTranCount = null;
          if (!IsNullEmpty(userInfo.totalMonthlyTranCount)) {
            totalMonthlyTranCount = await userInfo.totalMonthlyTranCount.find(
              (x) =>
                x.year == tranCount.year &&
                x.partner == tranCount.partner &&
                x.serviceType == tranCount.serviceType
            );
          }
          let totalCount = 0;

          if (IsNullEmpty(totalMonthlyTranCount)) {
            for (i = 0; i < 12; i++) {
              totalCount = totalCount + tranCount[months[i]];
            }
            totalMonthlyTranCount = {
              year: tranCount.year,
              partner: tranCount.partner,
              serviceType: tranCount.serviceType,
              totalCount: totalCount,
            };
            userInfo.totalMonthlyTranCount.push(totalMonthlyTranCount);
          } else {
            for (i = 0; i < 12; i++) {
              totalCount = totalCount + tranCount[months[i]];
            }
            totalMonthlyTranCount.totalCount = totalCount;
          }
        }

        for (tranCount of userInfo.monthlyTranVol) {
          let totalMonthlyTranVol = null;
          if (!IsNullEmpty(userInfo.totalMonthlyTranVol)) {
            totalMonthlyTranVol = await userInfo.totalMonthlyTranVol.find(
              (x) =>
                x.year == tranCount.year &&
                x.partner == tranCount.partner &&
                x.serviceType == tranCount.serviceType
            );
          }
          let totalCount = 0;

          if (IsNullEmpty(totalMonthlyTranVol)) {
            for (i = 0; i < 12; i++) {
              //console.log(`${months[i]}:${tranCount[months[i]]}`);
              totalCount = totalCount + tranCount[months[i]];
            }
            totalMonthlyTranVol = {
              year: tranCount.year,
              partner: tranCount.partner,
              serviceType: tranCount.serviceType,
              totalCount: totalCount,
            };
            userInfo.totalMonthlyTranVol.push(totalMonthlyTranVol);
          } else {
            for (i = 0; i < 12; i++) {
              totalCount = totalCount + tranCount[months[i]];
            }
            totalMonthlyTranVol.totalCount = totalCount;
          }
        }
      }

      //questionnairreDetails
      if (!IsNullEmpty(userInfo.questionnairreDetails) || isApplingWithDiffPAN == true) {
        if (!IsNullEmpty(partnerLoan.personalDetails.phoneConnectionType) && (IsNullEmpty(userInfo.questionnairreDetails.phoneConnectionType) || isApplingWithDiffPAN == true)) {
          userInfo.questionnairreDetails.phoneConnectionType = partnerLoan.personalDetails.phoneConnectionType;
          _isChanged = true;
        }

        if (!IsNullEmpty(partnerLoan.personalDetails.whatsAppInMobile) && (IsNullEmpty(userInfo.questionnairreDetails.whatsApp) || isApplingWithDiffPAN == true)) {
          userInfo.questionnairreDetails.whatsApp = partnerLoan.personalDetails.whatsAppInMobile;
          _isChanged = true;
        }

        if (!IsNullEmpty(partnerLoan.personalDetails.gender) && (IsNullEmpty(userInfo.questionnairreDetails.gender) || isApplingWithDiffPAN == true)) {
          userInfo.questionnairreDetails.gender = (partnerLoan.personalDetails.gender.toLowerCase() == "male" ? "Male" : (partnerLoan.personalDetails.gender.toLowerCase() == "female" ? "Female" : (partnerLoan.personalDetails.gender.toLowerCase() == "transgender" ? "Transgender" : null)));
          _isChanged = true;
        }

        if (!IsNullEmpty(partnerLoan.personalDetails.maritalStatus) && (IsNullEmpty(userInfo.questionnairreDetails.maritalStatus) || isApplingWithDiffPAN == true)) {
          userInfo.questionnairreDetails.maritalStatus = (partnerLoan.personalDetails.maritalStatus.toLowerCase() == "married" ? "Married" : "Single");
          _isChanged = true;
        }

        if (!IsNullEmpty(partnerLoan.personalDetails.children) && (IsNullEmpty(userInfo.questionnairreDetails.children) || isApplingWithDiffPAN == true)) {
          userInfo.questionnairreDetails.children = partnerLoan.personalDetails.children;
          _isChanged = true;
        }

        if (!IsNullEmpty(partnerLoan.personalDetails.educationalDegree) && (IsNullEmpty(userInfo.questionnairreDetails.degree) || isApplingWithDiffPAN == true)) {
          userInfo.questionnairreDetails.degree = partnerLoan.personalDetails.educationalDegree;
          _isChanged = true;
        }

        if (!IsNullEmpty(partnerLoan.personalDetails.avgMonthlyIncome) && (IsNullEmpty(userInfo.questionnairreDetails.monthlyIncome) || isApplingWithDiffPAN == true)) {
          userInfo.questionnairreDetails.monthlyIncome = partnerLoan.personalDetails.avgMonthlyIncome;
          _isChanged = true;
        }

        if (!IsNullEmpty(partnerLoan.personalDetails.avgMonthlyOutgoings) && (IsNullEmpty(userInfo.questionnairreDetails.monthlyOutgoing) || isApplingWithDiffPAN == true)) {
          userInfo.questionnairreDetails.monthlyOutgoing = partnerLoan.personalDetails.avgMonthlyOutgoings;
          _isChanged = true;
        }

        if (!IsNullEmpty(partnerLoan.personalDetails.jobType) && (IsNullEmpty(userInfo.questionnairreDetails.jobType) || isApplingWithDiffPAN == true)) {
          userInfo.questionnairreDetails.jobType = partnerLoan.personalDetails.jobType;
          _isChanged = true;
        }

        if (!IsNullEmpty(partnerLoan.personalDetails.homeOwnershipType) && (IsNullEmpty(userInfo.questionnairreDetails.homeOwnershipType) || isApplingWithDiffPAN == true)) {
          userInfo.questionnairreDetails.homeOwnershipType = ((partnerLoan.personalDetails.homeOwnershipType.toLowerCase() == "owned" || partnerLoan.personalDetails.homeOwnershipType.toLowerCase() == "owner") ? "Own" : ((partnerLoan.personalDetails.homeOwnershipType.toLowerCase() == "rented" || partnerLoan.personalDetails.homeOwnershipType.toLowerCase() == "rent") ? "Rent" : null));
          _isChanged = true;
        }

        if (!IsNullEmpty(partnerLoan.personalDetails.vehicle) && (IsNullEmpty(userInfo.questionnairreDetails.vehicle) || isApplingWithDiffPAN == true)) {
          userInfo.questionnairreDetails.vehicle = partnerLoan.personalDetails.vehicle;
          _isChanged = true;
        }

        if (!IsNullEmpty(partnerLoan.personalDetails.whiteGoodsIn24Months) && (IsNullEmpty(userInfo.questionnairreDetails.purchasedInOneYear) || isApplingWithDiffPAN == true)) {
          userInfo.questionnairreDetails.purchasedInOneYear = partnerLoan.personalDetails.whiteGoodsIn24Months;
          _isChanged = true;
        }
      }

      //Ip Addess
      if (!IsNullEmpty(partnerLoan.ipAddress) && (IsNullEmpty(userInfo.ipAddress) || isApplingWithDiffPAN == true)) {
        userInfo.ipAddress = partnerLoan.ipAddress;
        _isChanged = true;
      }

      //Step Details
      if (userInfo.userDetails.kycStatus == null) {
        if (conf.isAutoCompleteKycSteps == true) {
          userInfo.userDetails.kycStatus = "Under Review";

          userInfo.kycStepDetails = [
            {
              Step: "Registration",
              isCompleted: true,
              insertedOn: Date.now(),
              completedOn: Date.now(),
            },
            {
              Step: "Loan",
              isCompleted: true,
              insertedOn: Date.now(),
              completedOn: Date.now(),
            },
            {
              Step: "KYC",
              isCompleted: true,
              insertedOn: Date.now(),
              completedOn: Date.now(),
            },
            {
              Step: "Shop",
              isCompleted: true,
              insertedOn: Date.now(),
              completedOn: Date.now(),
            },
            {
              Step: "Questionnairre",
              isCompleted: true,
              insertedOn: Date.now(),
              completedOn: Date.now(),
            }
          ];
        } else {
          userInfo.userDetails.kycStatus = "Initiated";
          userInfo.userDetails.employment = "Business";
          userInfo.kycStepDetails = [
            {
              Step: "Registration",
              isCompleted: true,
              insertedOn: Date.now(),
              completedOn: Date.now(),
            },
          ];
        }
      } else if (userInfo.userDetails.kycStatus != "Under Review" && userInfo.userDetails.kycStatus != "Initiated") {
        userInfo.userDetails.kycStatus = conf.isAutoCompleteKycSteps == true ? "Under Review" : "ReInitiated";
        // userInfo.kycStepDetails.push({
        //   Step: userInfo.userDetails.kycStatus,
        //   isCompleted: true,
        //   insertedOn: Date.now(),
        //   completedOn: Date.now(),
        // });
      }
    } else {
      if (conf.isAutoCompleteKycSteps == true && (userInfo.userDetails.kycStatus == "Initiated" || userInfo.userDetails.kycStatus == "ReInitiated")) {
        userInfo.userDetails.kycStatus = "Under Review";
        _isChanged = true;
      } else if (conf.isAutoCompleteKycSteps == false && userInfo.userDetails.kycStatus == "Under Review") {
        userInfo.userDetails.kycStatus = "ReInitiated";
        _isChanged = true;
      }
    }

    if (_isChanged == true) {
      userInfo.modifiedOn = Date.now();
      const backup = await backupUserInfo(userInfo._id);

      if (backup.success) {
        await userInfo.save();
      } else {
        saveErrorLog("updateUserInfo", apiPath, model, backup.message);
        output.message = backup.message;
        return output;
      }
    }

    if (!IsNullEmpty(userInfo.userDetails.clientCode)) {
      partnerLoan.clientCode = userInfo.userDetails.clientCode;
      await partnerLoan.save();
    }

    const url = config.get("keys.creditApiUrl") + "/api/credit/updatePartnerTransactionUserId";
    const creditModel = { partnerId: partnerMaster._id, merchantCode: partnerLoan.merchantCode, userId: userInfo._id }
    const bodyData = JSON.stringify(creditModel);

    output = {
      userId: userInfo._id,
      userPartnerInfoId: userPartnerInfo._id,
      success: true,
      message: "Updated Successfully"
    };

    const creditResponse = await fetch(
      url,
      {
        method: "Put",
        body: bodyData,
        headers: {
          "Content-Type": "application/json"
        },
      }
    );
    // .then(function (u) {
    //   return u.json();
    // })
    // .then(function (json) {
    //   return json;
    // })
    // .catch((err) => {
    //   console.error("err :", err);
    //   return err;
    // });

  } catch (ex) {
    logger.logs("Error : ", ex);
    output.message = ex.message;
    saveErrorLog("updateUserInfo", apiPath, { partnerLoanId }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
  }
  return output;
};

async function sendReviewNotification(model) {
  try {
    await sleep(3000);
    const longurl = config.get("keys.webUrl") + `leadInfo/${model.merchantCode}/${model.trxnId}/${model.loanAmount}/${encodeURIComponent(model.shortauthKey)}`;

    const partnerMaster = await PartnerMaster.findById(mongoose.Types.ObjectId(model.partnerId));

    const loan = await LoanInfo.findById(mongoose.Types.ObjectId(model.loanId));

    const generateCustomUrlResponse = await generateCustomUrl(longurl, model.type, model.userId, model.loanId, model.loanNo, loan.merchantCode, partnerMaster.displayName, null);

    if (generateCustomUrlResponse.success) {
      const userPartnerInfo = await UserPartnerInfo.findOne({ userId: mongoose.Types.ObjectId(model.userId), partnerId: mongoose.Types.ObjectId(model.partnerId), merchantCode: loan.merchantCode });

      if (userPartnerInfo) {
        userPartnerInfo.reviewLink = generateCustomUrlResponse.data;
        userPartnerInfo.incorrectReviewDocument = model.incorrectReviewDocument;
        userPartnerInfo.updatedOn = Date.now();
        await userPartnerInfo.save();

        const userInfo = await UserInfo.findById(model.userId);
        userInfo.userDetails.kycStatus = "Review";
        userInfo.userDetails.kycRemarks = model.kycRemarks;
        await userInfo.save();

        if (model.partnerCode.toLowerCase() == "mpay") {
          const productType = getProductType(model.productType);
          const apiVar = model.partnerCode.toLowerCase() + ".kycReviewedApis";
          let apis = config.get(apiVar);

          if (apis) {
            apis = apis.filter((x) => x.productType == productType);
            if (apis.length > 0) {
              for (let i = 0; i < apis.length; i++) {
                const borrowingParterRequestModel = apis[i].model != null ? JSON.parse(JSON.stringify(apis[i].model)) : {};
                borrowingParterRequestModel.source = "application";
                borrowingParterRequestModel.module = "kycReviewed";
                borrowingParterRequestModel.loanId = model.loanId;
                borrowingParterRequestModel.partnerId = model.partnerId;
                borrowingParterRequestModel.partnerCode = model.partnerCode;
                borrowingParterRequestModel.partnerDisplayName = partnerMaster.displayName;
                borrowingParterRequestModel.apiType = apis[i].apiType;
                borrowingParterRequestModel.isRetryOffline = apis[i].isRetryOffline;
                borrowingParterRequestModel.url = apis[i].url;
                borrowingParterRequestModel.reason = model.kycRemarks;
                borrowingParterRequestModel.description = model.kycRemarks;
                borrowingParterRequestModel.reviewLink = generateCustomUrlResponse.data;

                if (apis[i].isWaitForResponse) {
                  const lendingApiResponse = await callBorrowingPartnerAPI(borrowingParterRequestModel);
                  if (!lendingApiResponse.success) {
                    i = apis.length;
                    output.message = lendingApiResponse.message;
                    return output;
                  }
                } else {
                  callBorrowingPartnerAPI(borrowingParterRequestModel);
                }
              }
            }
          }
          //await payone.sendNotification(model.loanId, 9, "Missing BankDetails", "Missing BankDetails", "", generateCustomUrlResponse.data);
        }

        let mobileNumberForNotification = model.mobileNo[0] ? model.mobileNo[0].mobileNumber || "" : ""//mobile number change

        const whatsAppResponse = await pushToNotifications(model.partnerId, model.merchantCode, model.productId, model.trxnId, model.userId, model.loanId, "WhatsApp", mobileNumberForNotification, "kyc_reject_eng", "loan", null, null, null, false, null); //mobile number change

        //Call sendnotifications
        if (whatsAppResponse.success) await sendNotifications(whatsAppResponse.data._id);
        else saveErrorLog("sendReviewNotification", apiPath, model, whatsAppResponse.message);
      } else {
        saveErrorLog("sendReviewNotification", apiPath, model, "userPartnerInfo not found");
      }
    } else {
      saveErrorLog("sendReviewNotification", apiPath, model, generateCustomUrlResponse.message);
    }
  } catch (ex) {
    //logger.logs(ex);
    saveErrorLog("sendReviewNotification", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
  }
};

module.exports.getLoanStatus = async function (model, _authKey) {
  let output = {
    data: null,
    success: false,
    message: "",
    status: 501,
  };

  try {
    const todayDate = new Date();

    const today = new Date(todayDate.getFullYear(), todayDate.getMonth(), todayDate.getDate(), 5, 30, 0, 0);

    let partnerMaster = await PartnerMaster.findOne({ authKey: _authKey });

    if (!IsNullEmpty(model.productId) || !IsNullEmpty(model.loanNo)) {
      let loanInfo = null;

      if (!IsNullEmpty(model.productId))
        loanInfo = await LoanInfo.findOne({ productId: model.productId, partnerId: partnerMaster._id });
      else if (!IsNullEmpty(model.loanNo))
        loanInfo = await LoanInfo.findOne({ loanNo: model.loanNo, partnerId: partnerMaster._id });

      if (!IsNullEmpty(loanInfo)) {
        const installmentDetails = await installmentService.getInstallmentsPending([loanInfo._id], loanNos = null, ["Pending"], installmentNumber = null, isRequireAllInstallments = true, isRequireFees = true, tillDate = today, overdueBy = null, overDueFrom = null, overDueTo = null, isRequirePendingInstallments = false, isRequirePendingFees = false);

        if (installmentDetails.success) {

          let loanDetails = {
            loanStatus: "CLOSED",
            disbursementDate: loanInfo.disbursalDetails && loanInfo.disbursalDetails.disbursedOn ? getDMYFormattedDate(loanInfo.disbursalDetails.disbursedOn, "/") : null,
            status: loanInfo.status,
            interestRate: loanInfo.interestRate,
            loanAmount: loanInfo.amount,
            tenure: loanInfo.tenure,
            pendingTenure: 0,
            emiAmount: 0,
            totalPendingPrincipal: 0,
            totalPaidPrincipal: 0,
            totalPaidEmiAmount: 0,
            totalEmisBounces: 0,
            totalLatePaymentCharges: 0,
            totalmoratoriumInterest: 0,
            totalDisbursementAmount: 0,
            processingFees: 0,
            processingFeesWithGst: 0,
            otherCharges: 0,
            totalCharges: 0,
            totalOutstandingAmount: 0,
            overDueEmis: 0,
            overDuePrincipalAmount: 0,
            overDueInterestAmount: 0,
            overDueEmiAmount: 0,
            overDueCharges: 0,
            totalOverDueAmount: 0
          }
          const activeStatuses = ["Initiated", "Draft", "Under Review", "Accepted By Admin", "Accepted By Client", "Review", "Loan Is Funding", "Loan Contract Accepted", "Initiated Fund Transfer", "Ready For Disbursal", "Loan In Progress", "Lender Assigned", "Lender Approval", "Written-Off", "Recourse Pending"];

          if (activeStatuses.includes(loanInfo.status)) loanDetails.loanStatus = "ACTIVE";

          if (!IsNullEmpty(installmentDetails.data)) {
            loanDetails.totalCharges = loanInfo.disbursalDetails.custTotalChargesRecovered;
            loanDetails.otherCharges = loanInfo.disbursalDetails.custTotalChargesRecovered - loanInfo.disbursalDetails.custGstCharges - loanInfo.disbursalDetails.processingFee;
            loanDetails.totalDisbursementAmount = loanInfo.amount - loanInfo.disbursalDetails.custTotalChargesRecovered;
            loanDetails.processingFees = loanInfo.disbursalDetails.processingFee;
            loanDetails.processingFeesWithGst = loanInfo.disbursalDetails.processingFee + loanInfo.disbursalDetails.custGstCharges;
            loanDetails.totalOutstandingAmount = 0;
            loanDetails.emiAmount = loanInfo.emiAmount;

            if (installmentDetails.data.length > 0) {
              loanDetails.overDueEmis = installmentDetails.data[0].pendingInstallmentsTillDate;

              loanDetails.overDuePrincipalAmount = installmentDetails.data[0].principalOutstandingTillDate;

              loanDetails.overDuePrincipalAmount = loanDetails.overDuePrincipalAmount > 0 ? round10(loanDetails.overDuePrincipalAmount, -2) : 0;

              loanDetails.overDueInterestAmount = installmentDetails.data[0].interestOutstandingTillDate;
              loanDetails.overDueInterestAmount = loanDetails.overDueInterestAmount > 0 ? round10(loanDetails.overDueInterestAmount, -2) : 0;

              loanDetails.overDueEmiAmount = installmentDetails.data[0].loanOutstandingTillDate;

              loanDetails.overDueCharges = installmentDetails.data[0].feeAmountOutstandingTillDate + installmentDetails.data[0].moratoriumInterestOutstandingTillDate;

              loanDetails.totalOverDueAmount = installmentDetails.data[0].totalOutstandingTillDate;
            }


            const totalPendingPrincipal = installmentDetails.data[0].principalOutstanding;

            loanDetails.pendingTenure = installmentDetails.pendingInstallments.length;

            const totalPaidPrincipal = installmentDetails.installments.filter(x => x.status == "Received").reduce(function (prev, cur) {
              return prev + parseFloat(cur.installmentPrincipal);
            }, 0);

            const totalPaidEmiAmount = installmentDetails.installments.filter(x => x.status == "Received").reduce(function (prev, cur) {
              return prev + parseFloat(cur.installmentAmount);
            }, 0);

            const totalEmisBounces = installmentDetails.fees.filter((x) => x.feeType == "Late Fee");

            if (!IsNullEmpty(totalEmisBounces)) {
              const totalLatePaymentCharges = totalEmisBounces.reduce(function (prev, cur) {
                return prev + parseFloat(cur.feeAmount);
              }, 0);

              loanDetails.totalLatePaymentCharges = !IsNullEmpty(totalLatePaymentCharges) ? totalLatePaymentCharges : 0;
              loanDetails.totalLatePaymentCharges = round10(loanDetails.totalLatePaymentCharges, -2);
            }

            loanDetails.totalmoratoriumInterest = installmentDetails.moratoriumInterestOutstanding;

            const totalInterest = installmentDetails.data[0].interestOutstanding + installmentDetails.data[0].moratoriumInterestOutstanding;

            loanDetails.totalPendingPrincipal = parseFloat(parseFloat(totalPendingPrincipal).toFixed(2));
            loanDetails.totalPaidPrincipal = parseFloat(parseFloat(totalPaidPrincipal).toFixed(2));
            loanDetails.totalPaidEmiAmount = parseFloat(parseFloat(totalPaidEmiAmount).toFixed(2));
            loanDetails.totalEmisBounces = totalEmisBounces.length;
            loanDetails.totalInterest = parseFloat(parseFloat(totalInterest).toFixed(2));

            if (model.isEmiScheduleRequire && model.isEmiScheduleRequire == true) {

              const emis = await installmentService.convertEmiDetailsFormat(loanInfo._id);
              if (!emis.success) {
                output.message = emis.message;
                return output;
              }

              let allInstallmentDetails = [];
              for (const record of emis.data) {
                const installmentDetails = {
                  emiNumber: record.emiNumber,
                  emiAmount: record.rePaymentAmount,
                  interest: record.interestAmount,
                  principal: record.principalRecovered,
                  principalOutstanding: round10(record.principalOutstanding, -2),
                  closingBalance: round10(record.principalOutstanding - record.principalRecovered, -2),
                  otherCharges: record.penalty + record.moratoriumInterest,
                  status: record.rePaymentStatus,
                  rePaymentDate: getDMYFormattedDate(record.rePaymentDate, "/"),
                  rePaymentActualDate: getDMYFormattedDate(record.rePaymentActualDate, "/"),
                  rePaymentReceivedDate: !IsNullEmpty(record.rePaymentReceivedDate) ? getDMYFormattedDate(record.rePaymentReceivedDate, "/") : null,
                }
                allInstallmentDetails.push(installmentDetails);
              }
              loanDetails.emiSchedule = allInstallmentDetails;
            }
          }

          if (model.isTransactionRequire && model.isTransactionRequire == true) {
            const ledgerAccount = await LedgerAccount.find({ loanId: mongoose.Types.ObjectId(loanInfo._id) }).sort({ insertedOn: 1 });

            if (ledgerAccount && ledgerAccount != null && ledgerAccount.length > 0) {
              const ledgerRecords = [];
              for (const ledgerRecord of ledgerAccount) {
                const record = {
                  desc: ledgerRecord.type,
                  type: ledgerRecord.creditAmt == null ? "Cr." : "Dr.",
                  amount: ledgerRecord.creditAmt == null ? ledgerRecord.debitAmt : ledgerRecord.creditAmt,
                  date: getDMYFormattedDate(ledgerRecord.insertedOn, "/")
                }
                ledgerRecords.push(record);
              }
              loanDetails.transactions = ledgerRecords;
            }
          }

          output = {
            data: loanDetails,
            success: true,
            message: "Fetched Successfully",
            status: 200,
          };
        } else output.message = installmentDetails.message;
      } else output.message = "Please provide valid productId/Loan No";
    } else output.message = "Please provide productId";
  } catch (ex) {
    logger.logs(ex);
    saveErrorLog("getLoanStatus", apiPath, { model, _authKey }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    output.message = ex.message;
  }
  return output;
};

module.exports.registerPartner = async function (model) {
  let output = {
    success: false,
    message: "",
    status: 501,
  };
  let userId = null;
  try {
    const partnerTypes = ["Lending Partner", "Borrowing Partner"];

    if (!partnerTypes.includes(model.partnerType)) {
      output.message = "partnerType should be either Lending Partner/Borrowing Partner";
      return output;
    }

    const definations = await getDefinitionMaster({ definitionType: "business type" });
    if (definations.success) {
      let typeOfEntities = [];
      for (item of definations.data) {
        typeOfEntities.push(item.defination);
      }
      if (!typeOfEntities.includes(model.typeOfEntity)) {
        output.message = "typeOfEntities should be " + typeOfEntities.join(" / ");
        return output;
      }
    }

    let user = await UserInfo.find()
      .or([
        { "userDetails.emailId": { $regex: new RegExp(model.emailId, "i") } },
        { "userDetails.mobileNo.mobileNumber": model.mobileNo },
        { "panNo": model.panNo },
      ])
      .select("-userDetails.password");

    if (user.length != 0) {
      return {
        success: false,
        message: "Please Enter Valid Email/Mobile",
        status: 400,
      };
    }

    const partner = await PartnerMaster.findOne({
      partnerName: { $regex: new RegExp(model.partnerName, "i") },
    });

    if (partner) {
      return {
        data: "",
        success: false,
        message: "Partner name already exists",
        status: 400,
      };
    }

    const parts = model.doi.split("/");

    if (parts.length < 3) {
      output.message = "invalid doi format";
      return output;
    }
    // Please pay attention to the month (parts[1]); JavaScript counts months from 0:
    // January - 0, Februar.y - 1, etc.
    let doi = new Date(parts[2], parts[1] - 1, parts[0]);

    const partnerMaster = new PartnerMaster({
      partnerName: model.partnerName.trim(),
      partnerCode: model.partnerName.substring(0, 4),
      partnerType: model.partnerType,
      typeOfEntity: model.typeOfEntity,
      doi: doi,
      processingCharge: 2, //default
      isActive: true,
      insertedOn: Date.now(),
      contractTemplate: "loanContract.html",
      creditContractTemplate: "",
      contractSuccessCallBackUrl: "",
      eNachErrorCallBackUrl: ""
    });

    const partnerResponse = await partnerMaster.save();

    user = new UserInfo({
      userDetails: {
        userName: model.userName,
        role: model.partnerType,
        isMobileValidated: true,
        isEmailValidated: false,
        passwordModifiedOn: Date.now(),
      },
      insertedOn: Date.now(),
      tnc: "Y",
    });

    user.userDetails.emailId.push(model.emailId.trim());
    user.userDetails.mobileNo.push(buildMobileNumberObject(model.mobileNo.trim()));

    // user.partnerDetails = {
    //   partnerId: partnerResponse._id,
    //   partnerName: partnerResponse.partnerName.trim(),
    //   partnerType: partnerResponse.partnerType,
    // };

    // user.isPartnerUser = true;
    // user.isRetailer = false;

    user.kycStepDetails.push({
      Step: "KYC",
      isCompleted: false,
      insertedOn: Date.now(),
      completedOn: null,
    });

    user.kycStepDetails.push({
      Step: "Questionnairre",
      isCompleted: false,
      insertedOn: Date.now(),
      completedOn: null,
    });

    //Hashing the password
    const salt = await bycrypt.genSalt(10);
    user.userDetails.password = await bycrypt.hash(model.password.trim(), salt);

    let result = await user.save();

    const pwd = _.omit(result, ["userDetails.password"]);

    userId = user._id;

    let proofImagePath = config.get("keys.proofImages") + "/" + userId;
    const staticPath = "ProofImages/" + userId;

    let pan = await UserInfo.findOne({ "userDetails.panNo": { $regex: new RegExp("^" + model.panNo + "$", "i") } });

    if (pan && !pan._id.equals(mongoose.Types.ObjectId(userId))) {
      removePartner(userId);
      return {
        data: "",
        success: false,
        message: "PAN number already exists",
        status: 400,
      };
    }

    user.panNo = model.panNo.toUpperCase();

    let panImagePath = proofImagePath + "/PAN";

    if (model.panFileStream) {
      user.panDocDetails = [];

      let panFileName = `panFile_${getCurrentTimeStamp()}${getExtension(model.panFile)}`;
      let filePath = staticPath + "/PAN/" + panFileName
      await uploadToS3(filePath, model.panFileStream)
      let panDoc_Details = {
        fileName: panFileName,
        filePath: filePath,
        isActive: true,
        insertedOn: Date.now(),
      };

      user.panDocDetails.push(panDoc_Details);
    }

    if (model.udyogAadhaarNo) {
      let udyogAadhaar = await UserInfo.findOne({
        "udyogAadhaar.aadhaarNo": {
          $regex: new RegExp("^" + model.udyogAadhaarNo + "$", "i"),
        },
      });

      if (
        udyogAadhaar &&
        !udyogAadhaar._id.equals(mongoose.Types.ObjectId(userId))
      ) {
        removePartner(userId);
        return {
          data: "",
          success: false,
          message: "Udyog Aadhaar number already exists",
          status: 400,
        };
      }
    }

    if (model.udyogAadhaarFileStream) {
      user.udyogAadhaar = {};

      let filePath = staticPath + "/UdyogAadhaar/" + model.udyogAadhaarFile
      await uploadToS3(filePath, model.udyogAadhaarFileStream)
      let udyogAadhaarDoc_Details = {
        fileName: model.udyogAadhaarFile,
        filePath: filePath,
        isActive: true,
        insertedOn: Date.now(),
      };

      let udyogAadhaar_Details = {
        aadhaarNo: model.udyogAadhaarNo.toUpperCase(),
        docDetails: udyogAadhaarDoc_Details,
        insertedOn: Date.now(),
      };

      user.udyogAadhaar = udyogAadhaar_Details;
    } else {
      user.udyogAadhaar.aadhaarNo = model.udyogAadhaarNo ? model.udyogAadhaarNo.toUpperCase() : "";
    }

    let bankMaster = await BankBranchMaster.findOne({
      ifsc: {
        $regex: new RegExp("^" + model.ifsc + "$", "i"),
      },
    });

    if (!bankMaster) {
      removePartner(userId);
      return {
        data: "",
        success: false,
        message: "Invalid Ifsc Code",
        status: 400,
      };
    }

    let gst = await UserInfo.findOne({
      "gstDetails.gstNo": {
        $regex: new RegExp("^" + model.gstNo + "$", "i"),
      },
    });

    if (gst && !gst._id.equals(mongoose.Types.ObjectId(userId))) {
      removePartner(userId);
      return {
        data: "",
        success: false,
        message: "GST number already exists",
        status: 400,
      };
    }

    if (model.gstFileStream) {
      user.gstDetails = [];

      let filePath = staticPath + "/GST/" + model.gstFile
      await uploadToS3(filePath, model.gstFileStream)

      let gstDoc_Details = {
        fileName: model.gstFile,
        filePath: filePath,
        isActive: true,
        insertedOn: Date.now(),
      };

      let gst_Details = {
        gstNo: model.gstNo.toUpperCase(),
        docDetails: gstDoc_Details,
        insertedOn: Date.now(),
      };

      user.gstDetails.push(gst_Details);
    } else {
      if (user.gstDetails && user.gstDetails[0] && model.gstNo) {
        user.gstDetails[0].gstNo = model.gstNo.toUpperCase();
      }
      else if (model.gstNo) {
        let gst_Details = {
          gstNo: model.gstNo.toUpperCase(),
          docDetails: null,
          insertedOn: Date.now(),
        };

        user.gstDetails.push(gst_Details);
      }
    }

    user.userDetails.kycStatus = user.userDetails.kycStatus !== "Approved" ? "Under Review" : user.userDetails.kycStatus;

    if (user.kycStepDetails.length != 0) {
      user.kycStepDetails[0].isCompleted = true;
      user.kycStepDetails[0].completedOn = Date.now();
    } else {
      user.kycStepDetails.push({
        Step: "KYC",
        isCompleted: true,
        insertedOn: Date.now(),
        completedOn: Date.now(),
      });
    }

    user.addressDetails = user.addressDetails.filter(function (obj) {
      return obj.type.toLowerCase() !== "correspondence";
    });

    let city = await CityStateMaster.findOne({
      state: model.state,
      city: model.city,
    });

    if (!city) {
      removePartner(userId);
      return {
        data: "",
        success: false,
        message: "Invalid City/State",
        status: 400,
      };
    }

    user.addressDetails.push({
      add1: !IsNullEmpty(model.add1) ? removeSpecialChars(model.add1.toUpperCase()) : "",
      add2: !IsNullEmpty(model.add2) ? removeSpecialChars(model.add2.toUpperCase()) : "",
      add3: !IsNullEmpty(model.add3) ? removeSpecialChars(model.add3.toUpperCase()) : "",
      type: "correspondence",
      city: city.city.toUpperCase(),
      state: city.state.toUpperCase(),
      country: model.country.toUpperCase(),
      pincode: model.pincode,
      insertedOn: Date.now(),
    });

    result = await user.save();

    const userPartnerInfo = new UserPartnerInfo({
      userId: mongoose.Types.ObjectId(userId),
      partnerId: mongoose.Types.ObjectId(partnerResponse._id),
      merchantCode: null,
      isRetailer: false,
      isPartnerUser: true,
      loanHistory: [],
      bankDetails: [],
      insertedOn: Date.now(),
      updatedOn: Date.now()
    });

    if (model.bankFileStream) {
      userPartnerInfo.bankDetails = [];


      let filePath = staticPath + "/BankProof/" + model.bankFile
      await uploadToS3(filePath, model.gstFileStream)

      let bankDoc_Details = {
        fileName: model.bankFile,
        filePath: filePath,
        isActive: true,
        insertedOn: Date.now(),
      };

      userPartnerInfo.bankDetails.push({
        accountNo: model.accountNo.toUpperCase(),
        ifsc: model.ifsc.toUpperCase(),
        bankName: bankMaster.bank,
        address: bankMaster.address,
        city: bankMaster.city,
        state: bankMaster.state,
        insertedOn: Date.now(),
        branch: bankMaster.branch,
        accountType: model.accountType,
        docDetails: bankDoc_Details,
      });
    } else if (userPartnerInfo.bankDetails[0]) {
      userPartnerInfo.bankDetails[0].accountNo = model.accountNo.toUpperCase();
      userPartnerInfo.bankDetails[0].ifsc = model.ifsc.toUpperCase();
      userPartnerInfo.bankDetails[0].bankName = bankMaster.bank;
      userPartnerInfo.bankDetails[0].address = bankMaster.address;
      userPartnerInfo.bankDetails[0].city = bankMaster.city;
      userPartnerInfo.bankDetails[0].state = bankMaster.state;
      userPartnerInfo.bankDetails[0].modifiedOn = Date.now();
      userPartnerInfo.bankDetails[0].branch = bankMaster.branch;
      userPartnerInfo.bankDetails[0].accountType = model.accountType;
    }
    await userPartnerInfo.save();

    validateKYC(user._id, partnerResponse._id, "Partner");
    output = {
      success: true,
      message: "Registered Successfully. Please wait until kyc approved by the Fundfinaadmin.",
      status: 200,
    };
    return output;
  }
  catch (ex) {
    saveErrorLog("registerPartner", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    if (!IsNullEmpty(userId)) {
      removePartner(userId);
    }
    output.message = ex.message;
    //console.log(ex);
  }
};

async function removePartner(userId) {
  if (!IsNullEmpty(userId)) {
    const user = await UserInfo.findById(userId);
    if (!IsNullEmpty(user)) {
      const partnerMaster = await PartnerMaster.findById(user.partnerDetails.partnerId);

      if (!IsNullEmpty(partnerMaster)) {
        partnerMaster.remove();
      }
      user.remove();
    }
  }
};

module.exports.checkPartnerAccess = async function (_authKey, _partnerType) {
  let output = {
    success: false,
    partnerId: null,
    message: "Not authorized to access. Please contact Fundfina.",
    status: 401,
  };
  try {
    let partnerMaster = await PartnerMaster.find({ authKey: { $exists: true }, authKey: _authKey, partnerType: _partnerType, isActive: true }).countDocuments();
    if (partnerMaster == 0) {
      partnerMaster = await PartnerMaster.find({ shortauthKey: { $exists: true }, shortauthKey: _authKey, partnerType: _partnerType, isActive: true }).countDocuments();
    }
    if (partnerMaster > 0) {
      partnerMaster = await PartnerMaster.findOne({ authKey: { $exists: true }, authKey: _authKey, partnerType: _partnerType, isActive: true });
      if (IsNullEmpty(partnerMaster)) {
        partnerMaster = await PartnerMaster.findOne({ shortauthKey: { $exists: true }, shortauthKey: _authKey, partnerType: _partnerType, isActive: true });
      }
      output = {
        partnerId: partnerMaster._id,
        partnerCode: partnerMaster.partnerCode,
        success: true,
        message: "Partner is Valid",
        status: 200,
      };
    }
  }
  catch (ex) {
    logger.logs(ex);
    saveErrorLog("checkPartnerAccess", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
  }
  return output;
};

module.exports.saveAccessDetails = async function (model) {
  let accessDetails = new AccessDetails({
    api: model.api,
    partnerId: model.partnerId,
    authKey: model.authKey,
    publicIP: model.publicIP,
    responseTime: model.responseTime,
    isSuccess: model.isSuccess,
    req: model.req ? model.req : null,
    response: model.response ? model.response : null,
    errorMsg: model.errorMsg,
    insertedOn: Date.now()
  });
  await accessDetails.save();
};

async function validateKYC(userId, partnerId, type) {
  let output = {
    success: false,
    message: "",
    status: 501
  }
  try {
    if (type != "Excel") await sleep(30000);

    let userInfo = await UserInfo.findById(userId);
    if (!IsNullEmpty(userInfo)) {
      //Verify Pan Details
      if (IsNullEmpty(userInfo.panDocVerification.isDocVerified) && userInfo.userDetails.clientCode) {
        if (!IsNullEmpty(userInfo.panDocDetails) && userInfo.panDocDetails.length > 0 && !IsNullEmpty(userInfo.panNo)) {
          let filePath = userInfo.panDocDetails[0].filePath
          let panFileStream = await downloadImageFromS3(filePath);
          await ocrVerification(userInfo._id, panFileStream.data, "", true, userInfo.panNo, "PAN");
          panFileStream = null;
        } else if (!IsNullEmpty(userInfo.panNo) && !IsNullEmpty(userInfo.userDetails.dob)) {
          const dob = new Date(userInfo.userDetails.dob);
          let month = dob.getMonth() + 1;
          month = month < 10 ? ("0" + month) : month;
          let day = dob.getDate();
          day = day < 10 ? ("0" + day) : day;
          const dobValue = dob.getFullYear() + "-" + month + "-" + day;
          const fullName = getName(userInfo.userDetails.firstName, userInfo.userDetails.middleName, userInfo.userDetails.lastName);
          await verifyPanData(userInfo._id, userInfo.panNo, fullName, dobValue);
        }
      }

      //Verify POA Details
      if (!IsNullEmpty(userInfo.poaDetails) && userInfo.poaDetails.length > 0 && userInfo.userDetails.clientCode) {

        for (let i = 0; i < userInfo.poaDetails.length; i++) {
          if (IsNullEmpty(userInfo.poaDetails[i].docVerification.isDocVerified)) {
            if (!IsNullEmpty(userInfo.poaDetails[i].docDetails) && userInfo.poaDetails[i].docDetails.length > 0 && (userInfo.poaDetails[i].poaType.toLowerCase() == "voter id" || userInfo.poaDetails[i].poaType.toLowerCase() == "driving licence" || userInfo.poaDetails[i].poaType.toLowerCase() == "aadhaar card" || userInfo.poaDetails[i].poaType.toLowerCase() == "others")) {

              let filePath = "";
              let front_part = "";
              let back_part = "";

              if (userInfo.poaDetails[i].docDetails.length > 1) {
                for (let j = 0; j < userInfo.poaDetails[i].docDetails.length; j++) {
                  if (!IsNullEmpty(userInfo.poaDetails[i].docDetails[j].position)) {
                    if (userInfo.poaDetails[i].docDetails[j].position == "front") {
                      filePath = userInfo.poaDetails[i].docDetails[j].filePath

                      let downloadedImage = await downloadImageFromS3(filePath);
                      front_part = downloadedImage.data
                    } else if (userInfo.poaDetails[i].docDetails[j].position == "back") {
                      filePath = userInfo.poaDetails[i].docDetails[j].filePath

                      let downloadedImage = await downloadImageFromS3(filePath);
                      back_part = downloadedImage.data
                    } else if (j == 0) {
                      filePath = userInfo.poaDetails[i].docDetails[j].filePath

                      let downloadedImage = await downloadImageFromS3(filePath);
                      front_part = downloadedImage.data
                    } else if (j > 0) {
                      filePath = userInfo.poaDetails[i].docDetails[j].filePath

                      let downloadedImage = await downloadImageFromS3(filePath);
                      back_part = downloadedImage.data
                    }
                  }
                }
              } else if (userInfo.poaDetails[i].docDetails.length > 0) {
                filePath = userInfo.poaDetails[i].docDetails[0].filePath
                let downloadedImage = await downloadImageFromS3(filePath);
                front_part = downloadedImage.data
              }

              const resOcrVerification = await ocrVerification(userInfo._id, front_part, back_part, true, userInfo.poaDetails[i].poaNo, "POA", i);
              poaFileStream = null;
            } else {
              if (userInfo.poaDetails[i].poaType.toLowerCase() == "voter id") {
                await verifyVoterIdData(userInfo._id, userInfo.poaDetails[i].poaNo, i);
              } else if (userInfo.poaDetails[i].poaType.toLowerCase() == "driving licence") {
                const dob = new Date(userInfo.userDetails.dob);
                let month = dob.getMonth() + 1;
                month = month < 10 ? ("0" + month) : month;
                let day = dob.getDate();
                day = day < 10 ? ("0" + day) : day;
                const dobValue = dob.getDate() + "/" + month + "/" + day;
                await verifyDlData(userInfo._id, userInfo.poaDetails[i].poaNo, dobValue, i);
              } else {
                userInfo.poaDetails[i].docVerification.isDocMatch = false;
                userInfo.poaDetails[i].docVerification.isDocVerified = userInfo.poaDetails && userInfo.poaDetails[i] && userInfo.poaDetails[i].docVerification && userInfo.poaDetails[i].docVerification.isDocVerified ? userInfo.poaDetails[i].docVerification.isDocVerified : false;
                userInfo.poaDetails[i].docVerification.isNumberVerified = false;
                userInfo.poaDetails[i].docVerification.createdOn = Date.now();
                userInfo.poaDetails[i].docVerification.type = "";
                await userInfo.save();
              }
            }
          }
        }
      }

      //Verify GST
      // if (!IsNullEmpty(userInfo.gstDetails) && userInfo.gstDetails.length > 0 && !IsNullEmpty(userInfo.gstDetails[0].gstNo))
      //   await verifyGstData(userInfo._id, userInfo.gstDetails[0].gstNo.toUpperCase(), 0);

      const gstVerfied = userInfo.gstDetails && userInfo.gstDetails[0] && userInfo.gstDetails[0].docVerification && userInfo.gstDetails[0].docVerification.isGstVerified ? true : false;

      if (!gstVerfied) {
        if (!IsNullEmpty(userInfo.gstDetails) && userInfo.gstDetails.length > 0 && !IsNullEmpty(userInfo.gstDetails[0].gstNo))
          await saveGstDetails("detailedgst", userInfo._id, userInfo.gstDetails[0].gstNo.toUpperCase(), null)
        else if (userInfo.panNo && userInfo.addressDetails && userInfo.addressDetails[0] && userInfo.addressDetails[0].state)
          await saveGstDetails("pantogst", userInfo._id, userInfo.panNo.trim().toUpperCase(), userInfo.addressDetails[0].state.trim().toUpperCase())
      }

      //Verify Bank Account Number if KYC is not approved
      const userPartnerInfo = await UserPartnerInfo.findOne({ userId: mongoose.Types.ObjectId(userId), partnerId: mongoose.Types.ObjectId(partnerId) });
      if (userPartnerInfo) {
        if (!IsNullEmpty(userPartnerInfo.bankDetails) && userPartnerInfo.bankDetails.length > 0 && !IsNullEmpty(userPartnerInfo.bankDetails[0].accountNo) && !IsNullEmpty(userPartnerInfo.bankDetails[0].ifsc) && type != "partnerMerchant")
          await verifyClientBank(userPartnerInfo._id, userPartnerInfo.bankDetails[0].accountNo.toUpperCase(), userPartnerInfo.bankDetails[0].ifsc.toUpperCase());

      }
    }
    output = {
      success: true,
      message: "validated successfully",
      status: 200
    };
  }
  catch (ex) {
    //logger.logs("ex", ex);
    const model = { userId: userId, type: type };
    saveErrorLog("validateKYC", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    output.message = ex.message;
  }
  return output;
};
module.exports.validateKYC = validateKYC;

module.exports.getPartner = async function (partnerId) {
  try {
    let partner = await PartnerMaster.findById(partnerId);

    if (!partner)
      partner = await PartnerMaster.findOne({ partnerCode: "FUND" });

    return {
      data: partner,
      success: true,
      message: "",
      status: 200,
    };
  }
  catch (ex) {
    const partner = await PartnerMaster.findOne({ partnerCode: "FUND" });

    return {
      data: partner,
      success: true,
      message: "",
      status: 200,
    };
  }

};

module.exports.getPartnerFromToken = async function (token) {
  try {
    let partner = null;

    if (!token)
      partner = await PartnerMaster.findOne({ partnerCode: "FUND" }).select({ _id: 1 });
    else
      partner = await PartnerMaster.findOne({ $or: [{ authKey: token }, { shortauthKey: token }] }).select({ _id: 1 });;

    if (!partner)
      partner = await PartnerMaster.findOne({ partnerCode: "FUND" }).select({ _id: 1 });;

    return {
      data: { _id: partner._id },
      success: true,
      message: "",
      status: 200,
    };
  }
  catch (ex) {
    return {
      data: null,
      success: false,
      message: "",
      status: 200,
    };
  }

};

module.exports.getBorrowingPartnerList = async function () {
  try {

    let partner = await PartnerMaster.find({ partnerType: "Borrowing Partner", isActive: true }).select("_id partnerName").sort("partnerName");

    return partner;

  }
  catch (ex) {
    const model = {};
    saveErrorLog("getBorrowingPartnerList", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    output.message = ex.message;
  }
};

async function backupPartnerLoans(id) {
  let output = {
    success: false,
    message: "Some Error Occurred",
    status: 501,
  };
  try {
    const partnerLoans = await PartnerLoans.findById(id);
    if (partnerLoans) {
      const tempPartnerLoans = partnerLoans.toJSON();
      tempPartnerLoans.partnerLoanId = mongoose.Types.ObjectId(tempPartnerLoans["_id"]);
      delete tempPartnerLoans["_id"];
      const partnerLoansHistory = new PartnerLoansHistory(tempPartnerLoans);
      await partnerLoansHistory.save();

      output = {
        success: true,
        message: "Saved Successfully",
        status: 200,
      };
    } else {
      output.message = "No Partner Loan found";
    }
  } catch (ex) {
    output.message = ex.message;
  }
  return output;
};
module.exports.backupPartnerLoans = backupPartnerLoans;

module.exports.getLoanApplicationsForBorrowingPartner = async function (model) {
  let output = {
    data: null,
    success: false,
    message: "",
    status: 501,
  };
  try {
    let query = {};

    let query2 = {};
    // "lenderDetails.lenderId": mongoose.Types.ObjectId(model.userId),
    let borrowingPartner = null;
    if (!IsNullEmpty(model.userId)) {
      let userInfo = await UserInfo.findById(model.userId);

      if (!IsNullEmpty(userInfo)) {
        borrowingPartner = await UserPartnerInfo.aggregate([
          { $match: { userId: mongoose.Types.ObjectId(userInfo._id) } },
          {
            $lookup: {
              from: "PartnerMaster",
              foreignField: "_id",
              localField: "partnerId",
              as: "borrowingPartnerMaster"
            }
          },
          {
            $unwind: "$borrowingPartnerMaster"
          },
          {
            $match: { "borrowingPartnerMaster.partnerType": "Borrowing Partner" }
          },
          {
            $project: {
              _id: 1,
              userId: 1,
              partnerId: "$borrowingPartnerMaster._id",
              partnerCode: "$borrowingPartnerMaster.partnerCode",
              isApiCall: "$borrowingPartnerMaster.isApiCall",
              displayName: "$borrowingPartnerMaster.displayName",
              partnerName: "$borrowingPartnerMaster.partnerName",
              processingCharge: "$borrowingPartnerMaster.processingCharge"
            }
          }
        ]);

        query["partnerId"] = mongoose.Types.ObjectId(borrowingPartner[0].partnerId);
      } else {
        output.message = "user Id does not exists";
        return output;
      }
    }

    let fromParts = model.fromDate.split("/");
    let fromDate = new Date(fromParts[2], fromParts[1] - 1, fromParts[0], 0, 0, 0);
    let toParts = model.toDate.split("/");
    let toDate = new Date(toParts[2], toParts[1] - 1, toParts[0], 23, 59, 59);

    if (!IsNullEmpty(model.productId)) {
      query["productId"] = model.productId;
    }

    if (!IsNullEmpty(model.merchantCode)) {
      query["merchantCode"] = model.merchantCode;
    }

    if (!IsNullEmpty(model.status)) {
      if (model.status == "Under Review") query["status"] = { $in: ["Under Review", "Accepted By Client", "Accepted By Admin"] }
      else if (model.status == "Initiated Fund Transfer") query["status"] = { $in: ["Initiated Fund Transfer", "Ready For Disbursal"] }
      else if (model.status == "Review") {
        // query["status"] = model.status;
        // query2["userDetails.kycStatus"] = "Review";
        query2["$or"] = [
          { "status": model.status },
          { "userInfo.userDetails.kycStatus": "Review" }
        ]
      }
      else query["status"] = model.status;
    }

    let query1 = { "userInfo.userDetails": { $exists: true, $ne: [] } };

    if (!IsNullEmpty(model.mobileNo)) {
      query2["userInfo.userDetails.mobileNo.mobileNumber"] = model.mobileNo;
    }

    if (!IsNullEmpty(model.name)) {
      query1["$or"] = [
        {
          "userInfo.userDetails.firstName": {
            $regex: model.name,
            $options: "i",
          },
        },
        {
          "userInfo.userDetails.lastName": {
            $regex: model.name,
            $options: "i",
          },
        },
      ];
    }

    if (!model.merchantCode && !model.productId && !model.loanNo && !model.name)
      query["updatedOn"] = { $gte: fromDate, $lte: toDate };


    let loanInfo = await LoanInfo.aggregate([
      {
        $match: query,
      },
      {
        $lookup: {
          from: "UserInfo",
          localField: "userId",
          foreignField: "_id",
          as: "userInfo",
        },
      },
      {
        $match: query1,
      },
      {
        $match: query2,
      },
      { $unwind: "$userInfo" },
      {
        $lookup: {
          from: "UserPartnerInfo",
          let: { uid: "$userInfo._id", pid: "$partnerId", merchantCode: "$merchantCode" },
          pipeline: [
            { $match: { $expr: { $and: [{ $eq: ["$userId", "$$uid"] }, { $eq: ["$partnerId", "$$pid"] }, { $eq: ["$merchantCode", "$$merchantCode"] }] } } },
          ],
          as: "userPartnerInfo",
        }
      },
      { $unwind: "$userPartnerInfo" },
      {
        $project: {
          updatedOn: "$updatedOn",
          merchantCode: "$merchantCode",
          loanId: "$_id",
          userDetails: "$userInfo.userDetails",
          loanNo: "$loanNo",
          amount: "$amount",
          status: "$status",
          emiAmount: "$emiAmount",
          processingFee: "$disbursalDetails.processingFee",
          reviewLink: "$userPartnerInfo.reviewLink",
          contractLink: "$loanContractDetails.contractUrl",
          adminRemarks: "$adminRemarks",
          partnerId: "$partnerId",
          productType: "$productType",
          trxnId: "$trxnId"
        }
      },
    ]).sort(model.sortBy);

    if (!IsNullEmpty(loanInfo)) {
      let records = [];

      for (itemLoanInfo of loanInfo) {
        if (itemLoanInfo.userDetails.kycStatus == "Review") {
          itemLoanInfo.status = "Review";
          itemLoanInfo.adminRemarks = itemLoanInfo.userDetails.kycRemarks;
        } else if (itemLoanInfo.status != "Rejected By Admin" && itemLoanInfo.status != "Review") {
          itemLoanInfo.adminRemarks = "";
        }

        itemLoanInfo.updatedOn = itemLoanInfo.updatedOn ? getDMYFormattedDate(itemLoanInfo.updatedOn, "/") : "";
        itemLoanInfo.name = getName(itemLoanInfo.userDetails.firstName, itemLoanInfo.userDetails.middleName, itemLoanInfo.userDetails.lastName);

        if (IsNullEmpty(itemLoanInfo.processingFee)) {
          const preApproval = await PreApprovals.findOne({
            partnerId: mongoose.Types.ObjectId(itemLoanInfo.partnerId),
            merchantCode: itemLoanInfo.merchantCode,
            productType: itemLoanInfo.productType.split("_")[0],
            $or: [{ trxnId: itemLoanInfo.trxnId }, { productId: itemLoanInfo.trxnId }]
          }).sort({ insertedOn: -1 });


          const charges = await loanService.getLoanCharges(itemLoanInfo.amount, itemLoanInfo.loanId, preApproval._id);
          if (!charges.success) {
            output.message = charges.message;
            return output;
          }
          itemLoanInfo.processingFee = charges.processingChargeAmt;
        }

        if (IsNullEmpty(itemLoanInfo.emiAmount)) {
          const _emiSchedule = await loanService.emiSchedule(itemLoanInfo.loanId, new Date(Date.now()), false);
          //console.log("_emiSchedule", _emiSchedule);
          itemLoanInfo.emiAmount = _emiSchedule.data[0].rePaymentAmount;
        }

        switch (itemLoanInfo.status.toLowerCase()) {
          case "initiated":
            itemLoanInfo.fundfinaStatus = "Clicked on Offer";
            break;
          case "draft":
            itemLoanInfo.fundfinaStatus = "Loan Application Incomplete";
            break;
          case "under review":
            itemLoanInfo.fundfinaStatus = "Application Under Review";
            break;
          case "review you loan":
            itemLoanInfo.fundfinaStatus = "Customer to correct application details";
            break;
          case "accepted by client":
            itemLoanInfo.fundfinaStatus = "Application Under Review";
            break;
          case "accepted by admin":
            itemLoanInfo.fundfinaStatus = "Application Under Review";
            break;
          case "loan is funding":
            itemLoanInfo.fundfinaStatus = "Contract Sent to Customer";
            break;
          case "loan contract accepted":
            itemLoanInfo.fundfinaStatus = "Contract Accepted by Customer";
            break;
          case "lender approval":
            itemLoanInfo.fundfinaStatus = "Contract Accepted by Customer";
            break;
          case "lender assigned":
            itemLoanInfo.fundfinaStatus = "Lender Assigned to Loan";
            break;
          case "onboarding completed":
            itemLoanInfo.fundfinaStatus = "User Onboarding Completed";
            break;
          case "initiated fund transfer":
            itemLoanInfo.fundfinaStatus = "Disbursal Initiated";
            break;
          case "ready for disbursal":
            itemLoanInfo.fundfinaStatus = "Disbursal Initiated";
            break;
          case "recourse pending":
            itemLoanInfo.fundfinaStatus = "Mandate registration pending";
            break;
          default:
            itemLoanInfo.fundfinaStatus = itemLoanInfo.status;
            break;
        }

        records.push({
          updatedOn: itemLoanInfo.updatedOn,
          merchantCode: itemLoanInfo.merchantCode,
          loanId: itemLoanInfo.loanId,
          loanNo: itemLoanInfo.loanNo,
          amount: itemLoanInfo.amount,
          status: itemLoanInfo.status,
          emiAmount: itemLoanInfo.emiAmount,
          processingFee: itemLoanInfo.processingFee,
          reviewLink: itemLoanInfo.reviewLink,
          contractLink: itemLoanInfo.contractUrl,
          adminRemarks: itemLoanInfo.adminRemarks,
          name: itemLoanInfo.name,
          fundfinaStatus: itemLoanInfo.fundfinaStatus
        });
      }
      loanInfo = null;
      return {
        data: records,
        success: true,
        message: "details fetched successfully",
        status: 200,
      };
    } else {
      return {
        data: loanInfo,
        success: true,
        message: "No data found",
        status: 200,
      };
    }
  } catch (ex) {
    logger.logs(ex);
    saveErrorLog("getLoanApplicationsForBorrowingPartner", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    return {
      success: false,
      message: ex.message,
      status: 501,
    };
  }
  return output;
};

module.exports.getLoanApplicationsForBorrowingPartnerV2 = async function (model) {
  let output = {
    data: null,
    success: false,
    message: "",
    status: 501,
  };
  try {

    const limit = 10;
    const skip = model.page ? (+model.page - 1) * limit : 0

    let sortKey = model.sortBy || "_id"
    let sortAction = 1

    if (sortKey.startsWith("-")) {
      sortKey = sortKey.substring(1)
      sortAction = -1
    }

    console.log("getLoanApplicationsForBorrowingPartner Start", new Date())
    let query = {};

    let userQuery = {};
    // "lenderDetails.lenderId": mongoose.Types.ObjectId(model.userId),
    let borrowingPartner = null;
    if (!IsNullEmpty(model.userId)) {
      let userInfo = await UserInfo.findById(model.userId);

      if (!IsNullEmpty(userInfo)) {
        borrowingPartner = await UserPartnerInfo.aggregate([
          { $match: { userId: mongoose.Types.ObjectId(userInfo._id) } },
          {
            $lookup: {
              from: "PartnerMaster",
              foreignField: "_id",
              localField: "partnerId",
              as: "borrowingPartnerMaster"
            }
          },
          {
            $unwind: "$borrowingPartnerMaster"
          },
          {
            $match: { "borrowingPartnerMaster.partnerType": "Borrowing Partner" }
          },
          {
            $project: {
              _id: 1,
              userId: 1,
              partnerId: "$borrowingPartnerMaster._id",
              partnerCode: "$borrowingPartnerMaster.partnerCode",
              isApiCall: "$borrowingPartnerMaster.isApiCall",
              displayName: "$borrowingPartnerMaster.displayName",
              partnerName: "$borrowingPartnerMaster.partnerName",
              processingCharge: "$borrowingPartnerMaster.processingCharge"
            }
          }
        ]);

        query["partnerId"] = mongoose.Types.ObjectId(borrowingPartner[0].partnerId);
      } else {
        output.message = "user Id does not exists";
        return output;
      }
    }

    let fromParts = model.fromDate.split("/");
    let fromDate = new Date(fromParts[2], fromParts[1] - 1, fromParts[0], 0, 0, 0);
    let toParts = model.toDate.split("/");
    let toDate = new Date(toParts[2], toParts[1] - 1, toParts[0], 23, 59, 59);

    if (!IsNullEmpty(model.productId)) {
      query["productId"] = model.productId;
    }

    if (!IsNullEmpty(model.merchantCode)) {
      query["merchantCode"] = model.merchantCode;
    }

    if (!IsNullEmpty(model.status)) {
      if (model.status == "Under Review") query["status"] = { $in: ["Under Review", "Accepted By Client", "Accepted By Admin"] }
      else if (model.status == "Initiated Fund Transfer") query["status"] = { $in: ["Initiated Fund Transfer", "Ready For Disbursal"] }
      else if (model.status == "Review") {
        userQuery["userDetails.kycStatus"] = "Review"
      } else if (model.status == "Closed") {
        query["status"] = { $in: ["Close Prepaid", "Close Repaid"] }
      }
      else query["status"] = model.status;
    }

    if (!IsNullEmpty(model.mobileNo)) {
      userQuery["userDetails.mobileNo.mobileNumber"] = model.mobileNo;
    }

    if (!IsNullEmpty(model.name) && model.name.length >= 3) {
      userQuery["$or"] = [
        {
          "userDetails.firstName": {
            $regex: model.name,
            $options: "i",
          },
        },
        {
          "userDetails.lastName": {
            $regex: model.name,
            $options: "i",
          },
        },
      ];
    }

    query["insertedOn"] = { $gte: fromDate, $lte: toDate };

    if (Object.keys(userQuery).length) {
      let usersId = await UserInfo.distinct("_id", userQuery)
      if (model.status == "Review") {
        query["$or"] = [
          { "status": model.status },
          { "userId": { $in: usersId } }
        ]
      } else {
        query.userId = { $in: usersId }
      }
    }

    let totalCount = await LoanInfo.countDocuments(query)

    let loanInfo = await LoanInfo.aggregate([
      {
        $match: query,
      },
      {
        $sort: {
          [sortKey]: sortAction
        }
      },
      {
        $skip: skip
      },
      {
        $limit: limit
      },
      {
        $lookup: {
          from: "UserPartnerInfo",
          let: { uid: "$userId", pid: "$partnerId", merchantCode: "$merchantCode" },
          pipeline: [
            { $match: { $expr: { $and: [{ $eq: ["$userId", "$$uid"] }, { $eq: ["$partnerId", "$$pid"] }, { $eq: ["$merchantCode", "$$merchantCode"] }] } } },
          ],
          as: "userPartnerInfo",
        }
      },
      { $unwind: "$userPartnerInfo" },
      {
        $project: {
          updatedOn: "$updatedOn",
          insertedOn: "$insertedOn",
          merchantCode: "$merchantCode",
          loanId: "$_id",
          userDetails: "$userInfo.userDetails",
          loanNo: "$loanNo",
          amount: "$amount",
          status: "$status",
          emiAmount: "$emiAmount",
          processingFee: "$disbursalDetails.processingFee",
          reviewLink: "$userPartnerInfo.reviewLink",
          contractLink: "$loanContractDetails.contractUrl",
          adminRemarks: "$adminRemarks",
          partnerId: "$partnerId",
          productType: "$productType",
          trxnId: "$trxnId",
          userId: "$userId"
        }
      },
    ])

    let usersId = loanInfo.map(element => element.userId)

    let users = await UserInfo.find({ _id: usersId })

    let userHash = {}

    users.forEach(element => {
      userHash[element._id.toString()] = element
    })

    let records = [];

    for (itemLoanInfo of loanInfo) {

      let userDetails = userHash[itemLoanInfo.userId.toString()]

      if (!userDetails) continue;

      itemLoanInfo.userDetails = userDetails ? userDetails.userDetails : {}

      if (itemLoanInfo.userDetails.kycStatus == "Review") {
        itemLoanInfo.status = "Review";
        itemLoanInfo.adminRemarks = itemLoanInfo.userDetails.kycRemarks;
      } else if (itemLoanInfo.status != "Rejected By Admin" && itemLoanInfo.status != "Review") {
        itemLoanInfo.adminRemarks = "";
      }

      itemLoanInfo.updatedOn = itemLoanInfo.updatedOn ? getDMYFormattedDate(itemLoanInfo.updatedOn, "/") : "";
      itemLoanInfo.insertedOn = itemLoanInfo.insertedOn ? getDMYFormattedDate(itemLoanInfo.insertedOn, "/") : "";
      itemLoanInfo.name = getName(itemLoanInfo.userDetails.firstName, itemLoanInfo.userDetails.middleName, itemLoanInfo.userDetails.lastName);

      switch (itemLoanInfo.status.toLowerCase()) {
        case "initiated":
          itemLoanInfo.fundfinaStatus = "Clicked on Offer";
          break;
        case "draft":
          itemLoanInfo.fundfinaStatus = "Loan Application Incomplete";
          break;
        case "under review":
          itemLoanInfo.fundfinaStatus = "Application Under Review";
          break;
        case "review you loan":
          itemLoanInfo.fundfinaStatus = "Customer to correct application details";
          break;
        case "accepted by client":
          itemLoanInfo.fundfinaStatus = "Application Under Review";
          break;
        case "accepted by admin":
          itemLoanInfo.fundfinaStatus = "Application Under Review";
          break;
        case "loan is funding":
          itemLoanInfo.fundfinaStatus = "Contract Sent to Customer";
          break;
        case "loan contract accepted":
          itemLoanInfo.fundfinaStatus = "Contract Accepted by Customer";
          break;
        case "lender approval":
          itemLoanInfo.fundfinaStatus = "Contract Accepted by Customer";
          break;
        case "lender assigned":
          itemLoanInfo.fundfinaStatus = "Lender Assigned to Loan";
          break;
        case "onboarding completed":
          itemLoanInfo.fundfinaStatus = "User Onboarding Completed";
          break;
        case "initiated fund transfer":
          itemLoanInfo.fundfinaStatus = "Disbursal Initiated";
          break;
        case "ready for disbursal":
          itemLoanInfo.fundfinaStatus = "Disbursal Initiated";
          break;
        case "recourse pending":
          itemLoanInfo.fundfinaStatus = "Mandate registration pending";
          break;
        case "close prepaid":
          itemLoanInfo.fundfinaStatus = "Closed";
          break;
        case "close repaid":
          itemLoanInfo.fundfinaStatus = "Closed";
          break;
        default:
          itemLoanInfo.fundfinaStatus = itemLoanInfo.status;
          break;
      }

      const isExistsPnach = await isPnachAvailable(itemLoanInfo.loanId);

      records.push({
        updatedOn: itemLoanInfo.updatedOn,
        insertedOn: itemLoanInfo.insertedOn,
        merchantCode: itemLoanInfo.merchantCode,
        loanId: itemLoanInfo.loanId,
        loanNo: itemLoanInfo.loanNo,
        amount: itemLoanInfo.amount,
        status: itemLoanInfo.status,
        emiAmount: itemLoanInfo.emiAmount,
        processingFee: itemLoanInfo.processingFee,
        reviewLink: itemLoanInfo.reviewLink,
        contractLink: itemLoanInfo.contractUrl,
        adminRemarks: itemLoanInfo.adminRemarks,
        name: itemLoanInfo.name,
        fundfinaStatus: itemLoanInfo.fundfinaStatus,
        isPnachAvailable: isExistsPnach,
        isPnachDownloadDisabled: isExistsPnach ? false : true,
        isPnachUploadDisabled: isExistsPnach ? false : true,
        files: {
          pNachFile: "",
          pNachFileStream: "",
          pNachFileLabel: ""
        },
        fileErrors: {}
      });
    }
    loanInfo = null;

    console.log("getLoanApplicationsForBorrowingPartner End", new Date())

    return {
      data: records,
      success: true,
      message: "details fetched successfully",
      status: 200,
      totalPages: Math.ceil(totalCount / limit)
    };
  } catch (ex) {
    //logger.logs(ex);
    saveErrorLog("getLoanApplicationsForBorrowingPartner", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    return {
      success: false,
      message: ex.message,
      status: 501,
    };
  }
  return output;
};

module.exports.downloadLoanApplicationsForBorrowingPartnerV2 = async function (model) {
  let output = {
    data: null,
    success: false,
    message: "",
    status: 501,
  };
  try {

    let sortKey = model.sortBy || "_id"
    let sortAction = 1

    if (sortKey.startsWith("-")) {
      sortKey = sortKey.substring(1)
      sortAction = -1
    }

    let query = {};

    let userQuery = {};
    // "lenderDetails.lenderId": mongoose.Types.ObjectId(model.userId),
    let borrowingPartner = null;
    if (!IsNullEmpty(model.userId)) {
      let userInfo = await UserInfo.findById(model.userId);

      if (!IsNullEmpty(userInfo)) {
        borrowingPartner = await UserPartnerInfo.aggregate([
          { $match: { userId: mongoose.Types.ObjectId(userInfo._id) } },
          {
            $lookup: {
              from: "PartnerMaster",
              foreignField: "_id",
              localField: "partnerId",
              as: "borrowingPartnerMaster"
            }
          },
          {
            $unwind: "$borrowingPartnerMaster"
          },
          {
            $match: { "borrowingPartnerMaster.partnerType": "Borrowing Partner" }
          },
          {
            $project: {
              _id: 1,
              userId: 1,
              partnerId: "$borrowingPartnerMaster._id",
              partnerCode: "$borrowingPartnerMaster.partnerCode",
              isApiCall: "$borrowingPartnerMaster.isApiCall",
              displayName: "$borrowingPartnerMaster.displayName",
              partnerName: "$borrowingPartnerMaster.partnerName",
              processingCharge: "$borrowingPartnerMaster.processingCharge"
            }
          }
        ]);

        query["partnerId"] = mongoose.Types.ObjectId(borrowingPartner[0].partnerId);
      } else {
        output.message = "user Id does not exists";
        return output;
      }
    }

    let fromParts = model.fromDate.split("/");
    let fromDate = new Date(fromParts[2], fromParts[1] - 1, fromParts[0], 0, 0, 0);
    let toParts = model.toDate.split("/");
    let toDate = new Date(toParts[2], toParts[1] - 1, toParts[0], 23, 59, 59);

    if (!IsNullEmpty(model.productId)) {
      query["productId"] = model.productId;
    }

    if (!IsNullEmpty(model.merchantCode)) {
      query["merchantCode"] = model.merchantCode;
    }

    if (!IsNullEmpty(model.status)) {
      if (model.status == "Under Review") query["status"] = { $in: ["Under Review", "Accepted By Client", "Accepted By Admin"] }
      else if (model.status == "Initiated Fund Transfer") query["status"] = { $in: ["Initiated Fund Transfer", "Ready For Disbursal"] }
      else if (model.status == "Review") {
        userQuery["userDetails.kycStatus"] = "Review"
      } else if (model.status == "Closed") {
        query["status"] = { $in: ["Close Prepaid", "Close Repaid"] }
      }
      else query["status"] = model.status;
    }

    if (!IsNullEmpty(model.mobileNo)) {
      userQuery["userDetails.mobileNo.mobileNumber"] = model.mobileNo;
    }

    if (!IsNullEmpty(model.name) && model.name.length >= 3) {
      userQuery["$or"] = [
        {
          "userDetails.firstName": {
            $regex: model.name,
            $options: "i",
          },
        },
        {
          "userDetails.lastName": {
            $regex: model.name,
            $options: "i",
          },
        },
      ];
    }

    query["insertedOn"] = { $gte: fromDate, $lte: toDate };

    if (Object.keys(userQuery).length) {
      let usersId = await UserInfo.distinct("_id", userQuery)
      if (model.status == "Review") {
        query["$or"] = [
          { "status": model.status },
          { "userId": { $in: usersId } }
        ]
      } else {
        query.userId = { $in: usersId }
      }
    }

    let loanInfo = await LoanInfo.aggregate([
      {
        $match: query,
      },
      {
        $sort: {
          [sortKey]: sortAction
        }
      },
      {
        $lookup: {
          from: "UserPartnerInfo",
          let: { uid: "$userId", pid: "$partnerId", merchantCode: "$merchantCode" },
          pipeline: [
            { $match: { $expr: { $and: [{ $eq: ["$userId", "$$uid"] }, { $eq: ["$partnerId", "$$pid"] }, { $eq: ["$merchantCode", "$$merchantCode"] }] } } },
          ],
          as: "userPartnerInfo",
        }
      },
      { $unwind: "$userPartnerInfo" },
      {
        $project: {
          updatedOn: "$updatedOn",
          insertedOn: "$insertedOn",
          merchantCode: "$merchantCode",
          loanId: "$_id",
          userDetails: "$userInfo.userDetails",
          loanNo: "$loanNo",
          amount: "$amount",
          status: "$status",
          emiAmount: "$emiAmount",
          processingFee: "$disbursalDetails.processingFee",
          reviewLink: "$userPartnerInfo.reviewLink",
          contractLink: "$loanContractDetails.contractUrl",
          adminRemarks: "$adminRemarks",
          partnerId: "$partnerId",
          productType: "$productType",
          trxnId: "$trxnId",
          userId: "$userId"
        }
      },
    ])

    let usersId = loanInfo.map(element => element.userId)

    let users = await UserInfo.find({ _id: usersId })

    let userHash = {}

    users.forEach(element => {
      userHash[element._id.toString()] = element
    })

    let records = [];

    for (itemLoanInfo of loanInfo) {

      let userDetails = userHash[itemLoanInfo.userId.toString()]

      if (!userDetails) continue;

      itemLoanInfo.userDetails = userDetails ? userDetails.userDetails : {}

      if (itemLoanInfo.userDetails.kycStatus == "Review") {
        itemLoanInfo.status = "Review";
        itemLoanInfo.adminRemarks = itemLoanInfo.userDetails.kycRemarks;
      } else if (itemLoanInfo.status != "Rejected By Admin" && itemLoanInfo.status != "Review") {
        itemLoanInfo.adminRemarks = "";
      }

      itemLoanInfo.updatedOn = itemLoanInfo.updatedOn ? getDMYFormattedDate(itemLoanInfo.updatedOn, "/") : "";
      itemLoanInfo.insertedOn = itemLoanInfo.insertedOn ? getDMYFormattedDate(itemLoanInfo.insertedOn, "/") : "";
      itemLoanInfo.name = getName(itemLoanInfo.userDetails.firstName, itemLoanInfo.userDetails.middleName, itemLoanInfo.userDetails.lastName);

      switch (itemLoanInfo.status.toLowerCase()) {
        case "initiated":
          itemLoanInfo.fundfinaStatus = "Clicked on Offer";
          break;
        case "draft":
          itemLoanInfo.fundfinaStatus = "Loan Application Incomplete";
          break;
        case "under review":
          itemLoanInfo.fundfinaStatus = "Application Under Review";
          break;
        case "review you loan":
          itemLoanInfo.fundfinaStatus = "Customer to correct application details";
          break;
        case "accepted by client":
          itemLoanInfo.fundfinaStatus = "Application Under Review";
          break;
        case "accepted by admin":
          itemLoanInfo.fundfinaStatus = "Application Under Review";
          break;
        case "loan is funding":
          itemLoanInfo.fundfinaStatus = "Contract Sent to Customer";
          break;
        case "loan contract accepted":
          itemLoanInfo.fundfinaStatus = "Contract Accepted by Customer";
          break;
        case "lender approval":
          itemLoanInfo.fundfinaStatus = "Contract Accepted by Customer";
          break;
        case "lender assigned":
          itemLoanInfo.fundfinaStatus = "Lender Assigned to Loan";
          break;
        case "onboarding completed":
          itemLoanInfo.fundfinaStatus = "User Onboarding Completed";
          break;
        case "initiated fund transfer":
          itemLoanInfo.fundfinaStatus = "Disbursal Initiated";
          break;
        case "ready for disbursal":
          itemLoanInfo.fundfinaStatus = "Disbursal Initiated";
          break;
        case "recourse pending":
          itemLoanInfo.fundfinaStatus = "Mandate registration pending";
          break;
        case "close prepaid":
          itemLoanInfo.fundfinaStatus = "Closed";
          break;
        case "close repaid":
          itemLoanInfo.fundfinaStatus = "Closed";
          break;
        default:
          itemLoanInfo.fundfinaStatus = itemLoanInfo.status;
          break;
      }

      records.push({
        updatedOn: itemLoanInfo.updatedOn,
        insertedOn: itemLoanInfo.insertedOn,
        merchantCode: itemLoanInfo.merchantCode,
        loanId: itemLoanInfo.loanId,
        loanNo: itemLoanInfo.loanNo,
        amount: itemLoanInfo.amount,
        status: itemLoanInfo.status,
        emiAmount: itemLoanInfo.emiAmount,
        processingFee: itemLoanInfo.processingFee,
        reviewLink: itemLoanInfo.reviewLink,
        contractLink: itemLoanInfo.contractUrl,
        adminRemarks: itemLoanInfo.adminRemarks,
        name: itemLoanInfo.name,
        fundfinaStatus: itemLoanInfo.fundfinaStatus
      });
    }
    loanInfo = null;


    return {
      data: records,
      success: true,
      message: "details fetched successfully",
      status: 200,
    };
  } catch (ex) {
    //logger.logs(ex);
    saveErrorLog("downloadLoanApplicationsForBorrowingPartner", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    return {
      success: false,
      message: ex.message,
      status: 501,
    };
  }
};

module.exports.sendReviewLink = async function (model, req) {
  let output = {
    success: false,
    message: "",
    status: 501,
  };
  try {

    if (!IsNullEmpty(model.loanId) || !IsNullEmpty(model.loanNo)) {
      let loanInfo = null;
      if (!IsNullEmpty(model.loanId)) {
        loanInfo = await LoanInfo.findById(model.loanId);
      } else {
        loanInfo = await LoanInfo.findOne({ loanNo: model.loanNo });
      }

      if (loanInfo) {

        if (req && req.user.role != ROLES.ADMIN) {

          if (req.user.role == ROLES.USER && loanInfo.userId.toString() != req.user.id) {
            return {
              success: false,
              message: "Access Denied",
              status: 400,
            };
          } else (loanInfo.partnerId.toString() != req.user.partnerId)
          return {
            success: false,
            message: "Access Denied",
            status: 400,
          };
        }

        const userInfo = await UserInfo.findById(loanInfo.userId);
        if (userInfo) {
          const whatsAppResponse = await pushToNotifications(loanInfo.partnerId, loanInfo.merchantCode, loanInfo.productId, loanInfo.trxnId, loanInfo.userId, loanInfo._id, "WhatsApp", userInfo.userDetails.mobileNo[0].mobileNumber, "kyc_reject_eng", "loan", null, null, null, false, null);

          //Call sendNotifications
          if (whatsAppResponse.success) {
            await sendNotifications(whatsAppResponse.data._id);
            output = {
              success: true,
              message: "Send Successfully",
              status: 200,
            };
          }
          else saveErrorLog("sendReviewLink", apiPath, model, whatsAppResponse.message);
        } else output.message = "User Not Found";
      } else output.message = "Loan Not Found";
    } else output.message = "LoanId/loanNo is mandatory";
  } catch (ex) {
    //logger.logs(ex);
    saveErrorLog("sendReviewLink", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    return {
      success: false,
      message: ex.message,
      status: 501,
    };
  }
  return output;
};

module.exports.updatePostApplication = async function (model, partnerLoanId, loanId, conf) {
  let output = {
    success: false,
    message: "",
    status: 501,
  };
  try {
    const partnerLoan = await PartnerLoans.findById(partnerLoanId);
    const loanInfo = await LoanInfo.findById(loanId);
    const userInfo = await UserInfo.findById(loanInfo.userId);
    const userPartnerInfo = await UserPartnerInfo.findOne({ partnerId: mongoose.Types.ObjectId(loanInfo.partnerId), userId: mongoose.Types.ObjectId(loanInfo.userId), merchantCode: loanInfo.merchantCode });

    if (loanInfo.loanContractDetails && loanInfo.loanContractDetails.isAccepted && loanInfo.loanContractDetails.isAccepted == false) {

      let isPartnerLoanUpdated = false;

      //Update bankDetails in PartnerLoan
      if (partnerLoan.bankDetails) {
        if (partnerLoan.bankDetails.ifsc != model.bankDetails.ifsc || partnerLoan.bankDetails.accountName != model.bankDetails.accountName || partnerLoan.bankDetails.accountType != model.bankDetails.accountType || partnerLoan.bankDetails.accountNo != model.bankDetails.accountNo || partnerLoan.bankDetails.ifsc != model.bankDetails.ifsc) {
          partnerLoan.bankDetails = model.bankDetails;
          isPartnerLoanUpdated = true;
        }
      } else {
        partnerLoan.bankDetails = model.bankDetails;
        isPartnerLoanUpdated = true;
      }

      if (partnerLoan.personalDetails.phoneConnectionType != model.personalDetails.phoneConnectionType) {
        partnerLoan.personalDetails.phoneConnectionType = model.personalDetails.phoneConnectionType;
        isPartnerLoanUpdated = true;
      }

      if (partnerLoan.personalDetails.whatsAppInMobile != model.personalDetails.whatsAppInMobile) {
        partnerLoan.personalDetails.whatsAppInMobile = model.personalDetails.whatsAppInMobile;
        isPartnerLoanUpdated = true;
      }

      if (partnerLoan.personalDetails.gender != model.personalDetails.gender) {
        partnerLoan.personalDetails.gender = model.personalDetails.gender;
        isPartnerLoanUpdated = true;
      }

      if (partnerLoan.personalDetails.maritalStatus != model.personalDetails.marital_status) {
        partnerLoan.personalDetails.maritalStatus = model.personalDetails.marital_status;
        isPartnerLoanUpdated = true;
      }

      if (partnerLoan.personalDetails.children != model.personalDetails.children) {
        partnerLoan.personalDetails.children = model.personalDetails.children;
        isPartnerLoanUpdated = true;
      }

      if (partnerLoan.personalDetails.educationalDegree != model.personalDetails.educationalDegree) {
        partnerLoan.personalDetails.educationalDegree = model.personalDetails.educationalDegree;
        isPartnerLoanUpdated = true;
      }

      if (partnerLoan.personalDetails.avgMonthlyIncome != model.personalDetails.avgMonthlyIncome) {
        partnerLoan.personalDetails.avgMonthlyIncome = model.personalDetails.avgMonthlyIncome;
        isPartnerLoanUpdated = true;
      }

      if (partnerLoan.personalDetails.avgMonthlyOutgoings != model.personalDetails.avgMonthlyOutgoings) {
        partnerLoan.personalDetails.avgMonthlyOutgoings = model.personalDetails.avgMonthlyOutgoings;
        isPartnerLoanUpdated = true;
      }

      if (partnerLoan.personalDetails.jobType != model.personalDetails.jobType) {
        partnerLoan.personalDetails.jobType = model.personalDetails.jobType;
        isPartnerLoanUpdated = true;
      }

      if (partnerLoan.personalDetails.homeOwnershipType != model.personalDetails.home_ownership_type) {
        partnerLoan.personalDetails.homeOwnershipType = model.personalDetails.home_ownership_type;
        isPartnerLoanUpdated = true;
      }

      if (partnerLoan.personalDetails.vehicle != model.personalDetails.vehicle) {
        partnerLoan.personalDetails.vehicle = model.personalDetails.vehicle;
        isPartnerLoanUpdated = true;
      }

      if (partnerLoan.personalDetails.whiteGoodsIn24Months != model.personalDetails.whiteGoodsIn24Months) {
        partnerLoan.personalDetails.whiteGoodsIn24Months = model.personalDetails.whiteGoodsIn24Months;
        isPartnerLoanUpdated = true;
      }

      if (isPartnerLoanUpdated) {
        await partnerLoan.save();

        //Update Bank Details in UserPartnerInfo
        if (partnerLoan.isBankValidated && partnerLoan.bankDetails.ifsc) {
          let updateBankDetails = true;

          if (userPartnerInfo.bankDetails) {
            const details = null;

            if (partnerLoan.bankDetails)
              if (partnerLoan.bankDetails.ifsc && partnerLoan.bankDetails.accountNo && partnerLoan.bankDetails.accountType)
                details = userPartnerInfo.bankDetails.filter((x) => x.ifsc == partnerLoan.bankDetails.ifsc && x.accountNo == partnerLoan.bankDetails.accountNo && x.accountType == partnerLoan.bankDetails.accountType);
              else if (partnerLoan.bankDetails.ifsc && partnerLoan.bankDetails.accountNo)
                details = userPartnerInfo.bankDetails.filter((x) => x.ifsc == partnerLoan.bankDetails.ifsc && x.accountNo == partnerLoan.bankDetails.accountNo);
              else if (partnerLoan.bankDetails.ifsc)
                details = userPartnerInfo.bankDetails.filter((x) => x.ifsc == partnerLoan.bankDetails.ifsc);

            if (!IsNullEmpty(details)) {
              updateBankDetails = false;
            }
          }

          if (updateBankDetails) {
            const bankMaster = await BankBranchMaster.findOne({
              ifsc: {
                $regex: new RegExp("^" + partnerLoan.bankDetails.ifsc + "$", "i"),
              },
            });

            const bankDetails = [{
              bankName: bankMaster.bank,
              branch: bankMaster.branch,
              accountNo: partnerLoan.bankDetails.accountNo,
              accountName: partnerLoan.bankDetails.accountName,
              ifsc: partnerLoan.bankDetails.ifsc,
              accountType: partnerLoan.bankDetails.accountType,
              address: bankMaster.address,
              city: bankMaster.city,
              state: bankMaster.state,
              docDetails: [],
              insertedOn: Date.now(),
              docVerification: {
                isBankVerified: true,
                id: null,
                verified: true,
                verifiedAt: Date.now(),
                beneficiary_name_with_bank: partnerLoan.beneficiary_name,
                createdOn: Date.now()
              },
              errorDetails: {
                details: null,
                code: null,
                message: null
              }
            }];

            userPartnerInfo.bankDetails = bankDetails;
            await backupUserPartnerInfo(userPartnerInfo._id);
            await userPartnerInfo.save();

            if (conf.isDigiLockerMandatory && conf.isDigiLockerMandatory == true && loanInfo.status == "Loan Is Funding") {
              const mandate = await loanService.createMandate(loanInfo._id, "loanIsFunding", false);
            } else if (loanInfo.status == "Loan Is Funding") {
              const mandate = await loanService.createMandate(loanInfo._id, "loanIsFunding", true);
            }
          }
        }

        //Update questionnairreDetails in UserInfo
        let isQuestionnaireUpdated = false;
        if (IsNullEmpty(userInfo.questionnairreDetails.phoneConnectionType) && !IsNullEmpty(partnerLoan.personalDetails.phoneConnectionType)) {
          userInfo.questionnairreDetails.phoneConnectionType = partnerLoan.personalDetails.phoneConnectionType;
          isQuestionnaireUpdated = true;
        }

        if (IsNullEmpty(userInfo.questionnairreDetails.whatsApp) && !IsNullEmpty(partnerLoan.personalDetails.whatsAppInMobile)) {
          userInfo.questionnairreDetails.whatsApp = partnerLoan.personalDetails.whatsAppInMobile;
          isQuestionnaireUpdated = true;
        }

        if (IsNullEmpty(userInfo.questionnairreDetails.gender) && !IsNullEmpty(partnerLoan.personalDetails.gender)) {
          userInfo.questionnairreDetails.gender = (partnerLoan.personalDetails.gender.toLowerCase() == "male" ? "Male" : (partnerLoan.personalDetails.gender.toLowerCase() == "female" ? "Female" : (partnerLoan.personalDetails.gender.toLowerCase() == "transgender" ? "Transgender" : null)));
          isQuestionnaireUpdated = true;
        }

        if (IsNullEmpty(userInfo.questionnairreDetails.maritalStatus) && !IsNullEmpty(partnerLoan.personalDetails.maritalStatus)) {
          userInfo.questionnairreDetails.maritalStatus = (partnerLoan.personalDetails.maritalStatus.toLowerCase() == "married" ? "Married" : "Single");
          isQuestionnaireUpdated = true;
        }

        if (IsNullEmpty(userInfo.questionnairreDetails.children) && !IsNullEmpty(partnerLoan.personalDetails.children)) {
          userInfo.questionnairreDetails.children = partnerLoan.personalDetails.children;
          isQuestionnaireUpdated = true;
        }

        if (IsNullEmpty(userInfo.questionnairreDetails.degree) && !IsNullEmpty(partnerLoan.personalDetails.educationalDegree)) {
          userInfo.questionnairreDetails.degree = partnerLoan.personalDetails.educationalDegree;
          isQuestionnaireUpdated = true;
        }

        if (IsNullEmpty(userInfo.questionnairreDetails.monthlyIncome) && !IsNullEmpty(partnerLoan.personalDetails.avgMonthlyIncome)) {
          userInfo.questionnairreDetails.monthlyIncome = partnerLoan.personalDetails.avgMonthlyIncome;
          isQuestionnaireUpdated = true;
        }

        if (IsNullEmpty(userInfo.questionnairreDetails.monthlyOutgoing) && !IsNullEmpty(partnerLoan.personalDetails.avgMonthlyOutgoings)) {
          userInfo.questionnairreDetails.monthlyOutgoing = partnerLoan.personalDetails.avgMonthlyOutgoings;
          isQuestionnaireUpdated = true;
        }

        if (IsNullEmpty(userInfo.questionnairreDetails.jobType) && !IsNullEmpty(partnerLoan.personalDetails.jobType)) {
          userInfo.questionnairreDetails.jobType = partnerLoan.personalDetails.jobType;
          isQuestionnaireUpdated = true;
        }

        if (IsNullEmpty(userInfo.questionnairreDetails.homeOwnershipType) && !IsNullEmpty(partnerLoan.personalDetails.homeOwnershipType)) {
          userInfo.questionnairreDetails.homeOwnershipType = ((partnerLoan.personalDetails.homeOwnershipType.toLowerCase() == "owned" || partnerLoan.personalDetails.homeOwnershipType.toLowerCase() == "owner") ? "Own" : ((partnerLoan.personalDetails.homeOwnershipType.toLowerCase() == "rented" || partnerLoan.personalDetails.homeOwnershipType.toLowerCase() == "rent") ? "Rent" : null));
          isQuestionnaireUpdated = true;
        }

        if (IsNullEmpty(userInfo.questionnairreDetails.vehicle) && !IsNullEmpty(partnerLoan.personalDetails.vehicle)) {
          userInfo.questionnairreDetails.vehicle = partnerLoan.personalDetails.vehicle;
          isQuestionnaireUpdated = true;
        }

        if (IsNullEmpty(userInfo.questionnairreDetails.purchasedInOneYear) && !IsNullEmpty(partnerLoan.personalDetails.whiteGoodsIn24Months)) {
          userInfo.questionnairreDetails.purchasedInOneYear = partnerLoan.personalDetails.whiteGoodsIn24Months;
          isQuestionnaireUpdated = true;
        }

        if (isQuestionnaireUpdated) {
          const backup = await backupUserInfo(userInfo._id);
          await userInfo.save();
        }
      }

      output = {
        success: true,
        message: "updation completed",
        status: 200,
      };
    } else
      output.message = "updation not allowed";
  } catch (ex) {
    saveErrorLog("postUpdate", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    return {
      success: false,
      message: ex.message,
      status: 501,
    };
  }
  return output;
};

module.exports.contactDetails = async function (userId, loanId) {
  let mobileDetails = {
    maskedMobileNo: "",
    isWhatsApp: false
  };

  if (!userId || !loanId)
    return mobileDetails;

  try {
    const user = await UserInfo.findById(userId);

    const loan = await LoanInfo.findById(loanId);

    //const partnerConfig = await PartnerConfig.findOne({ partnerId: loan.partnerId });

    if (user.userDetails.mobileNo && user.userDetails.mobileNo[0] && user.userDetails.mobileNo[0].mobileNumber) {
      //mobileDetails.mobileNo = user.userDetails.mobileNo[0].toString();
      mobileDetails.maskedMobileNo = getMaskedMobileNo(user.userDetails.mobileNo[0].mobileNumber.toString()); //mobile number change
      mobileDetails.isWhatsApp = user.userDetails.whatsApp && user.userDetails.whatsApp.whatsAppConsent ? true : false
    }

    //mobileDetails.allowEdit = partnerConfig.allowMobileEdit;

    return mobileDetails;

  }
  catch (ex) {
    saveErrorLog("contactDetails", apiPath, { userId, loanId }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    return mobileDetails;
  }
};

const initiateKycRecapture = async function (userId, loanProductType) {
  let recapture = false
  try {

    const userInfo = await UserInfo.findOne({ _id: userId })
    loanProductType = loanProductType.substr(0, 2)

    if (userInfo.userDetails && userInfo.userDetails.kycApprovedOn) {

      const diff = new DateDiff(new Date(), userInfo.userDetails.kycApprovedOn);
      const diffDays = diff.days()

      let { config } = await ConfigParameters.findOne({ "channel": "KYC_RECAPTURE", })

      let productConfig = config[loanProductType]

      if (diffDays >= productConfig.value ||
        (!IsNullEmpty(userInfo.isKycRecapture) && userInfo.isKycRecapture == true)) {
        await backupUserInfo(userId)
        await UserInfo.updateOne({ _id: userId }, { $set: { isKycRecapture: false, "userDetails.kycStatus": "Review", "userDetails.kycRemarks": "Kyc Expired" } })
        recapture = true
      }

    }
  } catch (ex) {
    saveErrorLog("initiateKycRecapture", "initiateKycRecapture", { userId }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
  }
  return recapture
};

module.exports.getCityState = async function (partnerId, merchantCode, _state, _city, _pincode) {
  let output = {
    pincode: _pincode,
    state: !IsNullEmpty(_state) ? _state.toUpperCase() : null,
    city: !IsNullEmpty(_city) ? _city.toUpperCase() : null,
  }
  try {
    if (!IsNullEmpty(_pincode)) {
      const stateCityMaster = await CityStateMaster.findOne({ pincode: _pincode.toString() });

      if (!IsNullEmpty(stateCityMaster)) {
        output.state = stateCityMaster.state;
        output.city = stateCityMaster.city;
      } else {
        const missingPincodes = new MissingPincodes({
          partnerId: partnerId,
          merchantCode: merchantCode,
          city: _city,
          state: _state,
          pincode: _pincode,
          insertedOn: Date.now()
        });

        await missingPincodes.save();
      }
    }
  } catch (ex) {
    saveErrorLog("getCityState", apiPath, { partnerId, merchantCode, _state, _city, _pincode }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
  }
  return output;
}

// ------------------------------------------------------------------------------------------------------------------------------------------