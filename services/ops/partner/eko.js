//Libraries
// ------------------------------------------------------------------------------------------------------------------------------------------
const mongoose = require("mongoose");
const { dateAdd } = require("dateadd");
const debug = require("debug")("app:Eko");
const config = require("config");
const fetch = require("node-fetch");
const IsNullEmpty = require("isnullemptyorwhitespace");
// ----------------------------------------------------------------------------------------------------------------------------------------

//Models
// ----------------------------------------------------------------------------------------------------------------------------------------
const { BankBranchMaster, CityStateMaster } = require("../../../models/ops/common/master");
const { PartnerMaster, PartnerLoans, PreApprovals, ProductMaster, ProductPartnerConfig, PartnerConfig } = require("../../../models/ops/partner/partner");
const { PushToFundfina } = require("./partner");
const { LoanInfo } = require("../../../models/ops/loan/loan");
const { DigioBankList } = require("../../../models/ops/digio/digioApi");
const { UserInfo } = require("../../../models/ops/registration/users");
const { uploadToS3 } = require("../../../models/ops/aws/s3");
// ----------------------------------------------------------------------------------------------------------------------------------------

//External Methods
// ----------------------------------------------------------------------------------------------------------------------------------------
const { emiSchedule, getLoanCharges } = require("../loan/loan");
const apiPath = __dirname;
const { saveErrorLog } = require("../../../services/ops/common/masters");
const { validateKYC, updatePostApplication, backupPartnerLoans, getCityState } = require("../../../services/ops/partner/partner");
const { downloadImageBuffer, validatePan, getExtension, getCurrentTimeStamp } = require("../common/common");
const { approvedByAdmin, loanIsFunding } = require("../../../services/ops/loan/workflow");
const { getClientCode } = require("../registration/users");
const Logger = require("../../../middleware/logger");
const logger = new Logger();
// ----------------------------------------------------------------------------------------------------------------------------------------


//Internal Methods
// ----------------------------------------------------------------------------------------------------------------------------------------
module.exports.saveEkoLoanRequest = async function (model, token) {
  let output = {
    statusCode: 200,
    product_id: null,
    Success: "HTTP 200 OK",
    Status: 0,
    Description: "Internal Server Error",
  };
  try {
    const today = new Date();
    const partner = await PartnerMaster.findOne({
      partnerName: {
        $regex: new RegExp("EKO INDIA FINANCIAL SERVICES PRIVATE LIMITED", "i"),
      }
    });

    if (!partner) {
      output.Description = "Invalid Partner";
    } else {

      let existingLoans = await PartnerLoans.findOne({ partnerId: partner._id, productId: model.product_id, merchantCode: model.agent_code }).countDocuments();
      //console.log(existingLoans);

      if (existingLoans > 0) {
        output.product_id = existingLoans.productId;
        output.Description = "Record with product_id already exists";
      } else {
        let isValidTrxnId = false;
        if (partner.partnerCode.toLowerCase() != "eko") {
          isValidTrxnId = true;
          let preApproval = await PreApprovals.findOne({ partnerId: partner._id, merchantCode: model.agent_code }).sort({ modifiedOn: -1 });
          //logger.logs("preApproval", preApproval);
          if (!IsNullEmpty(preApproval)) {
            if (preApproval.trxnId.toLowerCase() != model.trxnId.toString().toLowerCase()) {
              preApproval = await PreApprovals.findOne({ partnerId: partner._id, merchantCode: model.agent_code, trxnId: model.trxnId.toString(), status: "Sent" });
              output.Description = !IsNullEmpty(preApproval) ? "Offer is expired" : "invalid trxnId";
            } else {
              if (Date.diff(today, preApproval.expiredOn).days() < 0) {
                isValidTrxnId = true;
              } else {
                output.Description = "Offer is expired";
              }
            }
          } else {
            output.Description = "PreApproval Offer not Found";
          }
        } else {

          let preApproval = await PreApprovals.findOne({ partnerId: partner._id, merchantCode: model.agent_code, productId: model.product_id, status: "Sent" }).sort({ modifiedOn: -1 });
          let merchantType = "Retailer";

          if (!IsNullEmpty(model.merchantType)) {
            merchantType = model.merchantType;
          }

          if (!IsNullEmpty(preApproval)) {

            if (preApproval.trxnId == null) {
              preApproval.trxnId = model.ekoTrxnId;
              await preApproval.save();
            }

            if (today <= preApproval.expiredOn) {
              isValidTrxnId = true;
            } else {
              output.Description = "Offer is expired";
            }
          } else {
            output.Description = "PreApproval Offer not Found";
          }
        }
        logger.logs("isValidTrxnId", isValidTrxnId);
        if (isValidTrxnId) {
          if (validatePan(model.panNo.toUpperCase())) {
            let state = !IsNullEmpty(model.state) ? model.state.toUpperCase() : "";
            let city = !IsNullEmpty(model.city) ? model.city.toUpperCase() : "";

            if (!IsNullEmpty(model.pincode)) {
              const stateCityMaster = await getCityState(partner._id, model.agent_code, model.state, model.city, model.pincode);
              state = stateCityMaster.state;
              city = stateCityMaster.city;
            }

            const partnerLoan = new PartnerLoans({
              partnerId: partner._id,
              trxnId: model.ekoTrxnId,
              productId: null,
              loanId: null,
              panNo: model.panNo.toUpperCase(),
              clientCode: null,
              merchantCode: model.agent_code,
              loanRequest: {
                firstName: model.firstName,
                middleName: null,
                lastName: model.lastName,
                mobileNo: model.mobileNo,
                emailId: model.emailID,
                aadhaarNo: model.aadhaarNo,
                city: city,
                street: model.street,
                state: state,
                pincode: model.pincode,
                merchantType: model.agent_type,
                loanAmount: model.loan_amount,
                onboardedDate: model.onboardedDate,
                isWeekendLoan: model.isWeekendLoan,
                isActive: true,
                insertedOn: Date.now(),
                modifiedOn: null,
                deactivatedOn: null,
              },
              ipAddress: model.ipAddress,
              virtualAccountNo: model.virtualAccountNo,
              insertedOn: Date.now()
            });

            if (!IsNullEmpty(model.product_id)) {
              partnerLoan.productId = model.product_id;
            }

            if (!IsNullEmpty(model.personalDetails)) {
              let personalDetails = {
                name: model.personalDetails.name,
                gender: model.personalDetails.gender,
                qualification: model.personalDetails.qualification,
                dob: model.personalDetails.dob,
                maritalStatus: model.personalDetails.maritalStatus,
                mobile: model.personalDetails.mobile,
                alternateMobile: model.personalDetails.alternate_mobile,
                homeOwnershipType: model.personalDetails.home_ownership_type,
                currentAdd: model.personalDetails.current_address,
                city: model.personalDetails.city,
                state: model.personalDetails.state,
                pincode: model.personalDetails.pincode,
                marital_status: model.personalDetails.marital_status,
                phoneConnectionType: model.personalDetails.phoneConnectionType,
                whatsAppInMobile: model.personalDetails.whatsAppInMobile,
                children: model.personalDetails.children,
                ownHome: model.personalDetails.ownHome,
                vehicle: model.personalDetails.vehicle,
                avgMonthlyOutgoings: model.personalDetails.avgMonthlyOutgoings,
                avgMonthlyIncome: model.personalDetails.avgMonthlyIncome,
                jobType: model.personalDetails.jobType,
                whiteGoodsIn24Months: model.personalDetails.whiteGoodsIn24Months,
                educationalDegree: model.personalDetails.educationalDegree
              };

              partnerLoan.personalDetails = personalDetails;
            }

            if (!IsNullEmpty(model.shopDetails)) {
              let shopDetails = {
                name: !IsNullEmpty(model.shopDetails.shopName)
                  ? model.shopDetails.shopName
                  : model.shopDetails.shop_name,
                type: !IsNullEmpty(model.shopDetails.shopType)
                  ? model.shopDetails.shopType
                  : model.shopDetails.shop_type,
                add: !IsNullEmpty(model.shopDetails.shopAddress)
                  ? model.shopDetails.shopAddress
                  : model.shopDetails.shop_address,
                city: model.shopDetails.city,
                state: model.shopDetails.state,
                pincode: model.shopDetails.pincode,
                location: !IsNullEmpty(model.shopDetails.shopLocation)
                  ? model.shopDetails.shopLocation
                  : model.shopDetails.shop_location,
                contactNo: model.shopDetails.shop_contact_number,
                ownershipType: model.shopDetails.shop_ownership_type,
                companyType: model.shopDetails.company_type,
                shopVinatge: model.shopDetails.shop_vintage,
                businessVintage: model.shopDetails.business_vintage,
              };

              partnerLoan.shopDetails = shopDetails;
            }

            if (!IsNullEmpty(model.accountDetails)) {
              let accountDetails = {
                mobile: model.accountDetails.mobile,
                panNo: model.accountDetails.panNumber,
                merchantCode: model.accountDetails.merchantCode,
              };
              partnerLoan.accoutDetails = accountDetails;
            }

            if (!IsNullEmpty(model.transactions)) {
              for (itemTransaction of model.transactions) {
                let transaction = {
                  primary: itemTransaction.primary,
                  type: itemTransaction.transactionsType,
                  value: itemTransaction.transactionsValue,
                  monthYear: itemTransaction.transactionsMonthYear,
                  valueType: itemTransaction.transactionsValueType,
                  primaryCount: itemTransaction.primaryCount,
                };
                partnerLoan.transactionDetails.push(transaction);
              }
            }

            if (!IsNullEmpty(model.ekoLoanHistory)) {
              for (itemLoanHistory of model.ekoLoanHistory) {
                let loanHistory = {
                  providerName: itemLoanHistory.loanProviderName,
                  amount: itemLoanHistory.loanAmount,
                  status: itemLoanHistory.status,
                  totalRepaidAmount: itemLoanHistory.totalRepaidAmount,
                };
                partnerLoan.loanHistory.push(loanHistory);
              }
            }

            if (!IsNullEmpty(model.bankDetails)) {
              partnerLoan.bankDetails = model.bankDetails;
            }

            let response = await partnerLoan.save();
            // console.log(response._id);

            let pushResponse = await PushToFundfina(response._id).catch(error => {
              return error;
            });

            logger.logs("pushResponse", pushResponse);

            if (pushResponse.success == true) {
              output.statusCode = 200;
              output.product_id = pushResponse.data;
              output.Success = "HTTP 200 OK";
              output.Status = 1;
              output.Description = "Inserted Successfully";
              logger.logs("calling validateKYC");
              validateKYC(pushResponse.userId, partner._id, "partnerMerchant");
            } else {
              output = {
                statusCode: 200,
                product_id: null,
                Success: "HTTP 200 OK",
                Status: 0,
                Description: pushResponse.message,
              };
            }

            await PartnerLoans.findOneAndUpdate({ _id: mongoose.Types.ObjectId(response._id) }, {
              $set: {
                responseSuccess: pushResponse.success,
                responseMessage: pushResponse.message
              }
            }, { new: true, useFindAndModify: false }).lean();

            return output;
          } else {
            output.Description = "Invalid Pan No " + model.panNo;
          }
        }
      }
    }
  } catch (ex) {
    console.log(ex);
    const model1 = { model: model, token: token }
    saveErrorLog("saveEkoLoanRequest", apiPath, model1, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    return output;
  }

  return output;
};

module.exports.saveLoanRequest = async function (model, token) {
  let output = {
    statusCode: 200,
    product_id: null,
    loanNo: null,
    Success: "HTTP 200 OK",
    Status: 0,
    Description: "Some Error Occurred",
    nachURL: null
  };
  const model1 = { model: model, token: token };
  try {
    let partner;

    const today = new Date();

    if (!IsNullEmpty(token)) {
      partner = await PartnerMaster.findOne({
        authKey: token
      });
    } else {
      partner = await PartnerMaster.findOne({
        partnerName: {
          $regex: new RegExp("EKO INDIA FINANCIAL SERVICES PRIVATE LIMITED", "i"),
        },
      });
    }

    if (partner && partner != null) {
      const conf = config.get(partner.partnerCode.toLowerCase());

      if (conf) {
        let preApproval = null;

        if (model.productId && model.productId != null) {
          preApproval = await PreApprovals.findOne({ partnerId: partner._id, merchantCode: model.agent_code, productId: model.productId, status: "Sent" }).sort({ modifiedOn: -1 });
        }

        if (preApproval == null && model.trxnId && model.trxnId != null) {
          preApproval = await PreApprovals.findOne({ partnerId: partner._id, merchantCode: model.agent_code, trxnId: model.trxnId, status: "Sent" }).sort({ modifiedOn: -1 });
        }

        if (!IsNullEmpty(preApproval)) {
          // let isValidated = true;
          let loanAmount = 0;

          const validationResponse = await loanRequestValdiation(model, preApproval);

          //Added by Uttam
          if (model.loan_amount && model.loan_amount != null)
            loanAmount = Number(model.loan_amount);

          if (validationResponse.success) {
            let existingLoan = null;

            if (!IsNullEmpty(model.productId)) {
              existingLoan = await PartnerLoans.findOne({ partnerId: partner._id, merchantCode: model.agent_code, productId: model.productId });
            }

            if (existingLoan == null && !IsNullEmpty(model.trxnId)) {
              existingLoan = await PartnerLoans.findOne({ partnerId: partner._id, merchantCode: model.agent_code, trxnId: model.trxnId });
            }

            //Validation For DigiLocker Mandatory cases
            let bankMaster = null;
            let partnerLoan = null;
            const onlyCharacters = /^[a-zA-Z ]+$/;


            if (conf.isDigiLockerMandatory) {
              if (IsNullEmpty(model.digilocker)) {
                output.Description = "digilocker details are mandatory";
                return output;
              } else if (IsNullEmpty(model.beneficiary_name)) {
                output.Description = "beneficiary_name is mandatory";
                return output;
              } else if (model.isBankValidated && IsNullEmpty(model.beneficiary_name)) {
                output.Description = "beneficiary_name is mandatory";
                return output;
              } else if (IsNullEmpty(model.bankDetails)) {
                output.Description = "bankdetails are mandatory";
                return output;
              } else if ((IsNullEmpty(model.bankDetails.accountName) || IsNullEmpty(model.bankDetails.accountType) || IsNullEmpty(model.bankDetails.accountNo) || IsNullEmpty(model.bankDetails.ifsc))) {
                output.Description = "bankdetails fields are mandatory";
                return output;
              } else if (!onlyCharacters.test(model.bankDetails.accountName)) {
                output.Description = "Account Name can only contain characters";
                return output;
              }

              bankMaster = await BankBranchMaster.findOne({
                ifsc: {
                  $regex: new RegExp("^" + model.bankDetails.ifsc + "$", "i"),
                },
              });

              if (IsNullEmpty(bankMaster)) {
                output.Description = "bank ifsc does not exist with fundfina.Please get it added first";
                return output;
              }
            }

            if (existingLoan != null) {
              logger.logs("already exists");
              output.Description = "Already Exist. Can Not Update.Please send new request.";
              let loanInfo = null;
              if (!IsNullEmpty(existingLoan.productId)) {
                output.product_id = existingLoan.productId;
                loanInfo = await LoanInfo.findOne({ productId: existingLoan.productId });
              }

              if (loanInfo && loanInfo.isDraft == false) {
                output.loanNo = loanInfo.loanNo;

                if (!IsNullEmpty(existingLoan.productId)) {
                  partnerLoan = await PartnerLoans.findOne({ productId: existingLoan.productId });
                }

                await updatePostApplication(model, partnerLoan._id, loanInfo._id, conf);

                logger.logs("conf.isDigiLockerMandatory", conf.isDigiLockerMandatory);
                if (conf.isDigiLockerMandatory) {
                  if (loanInfo.status == "Under Review") {
                    const resSetDigiLockerFields = await setDigiLockerFieldsInUserInfo(loanInfo.userId, partnerLoan.digilocker);
                    logger.logs("resSetDigiLockerFields", resSetDigiLockerFields);
                    if (resSetDigiLockerFields.success) {
                      const digioBank = await DigioBankList.findOne({
                        systemBankName: {
                          $regex: new RegExp("^" + bankMaster.bank + "$", "i")
                        }
                      });
                      logger.logs("digioBank", digioBank);

                      if (!digioBank || (digioBank && !digioBank.netBanking && !digioBank.debitCard && !digioBank.esign)) {
                        output.Description = "e-Nach option not available for selected bank. Please select different bank.";
                        output.Status = 0;
                        return output;
                      }

                      const approvedByAdminResponse = await approvedByAdmin(loanInfo._id, loanInfo.interestType, loanInfo.tenure, loanInfo.maturity, loanInfo.interestRate, loanInfo.repaymentFrequency, loanInfo.adhocDays, loanInfo.remarks, loanInfo.userId, "digilocker");
                      logger.logs("approvedByAdminResponse", approvedByAdminResponse);
                      if (approvedByAdminResponse.success) {

                        let authMode = config.get("digio.eNach.authModeAadhaar")
                        if (digioBank.debitCard || digioBank.netBanking) authMode = config.get("digio.eNach.authModeApi")

                        const loanIsFundingResponse = await loanIsFunding(loanInfo._id, null, false, "digilocker", false, authMode);
                        logger.logs("loanIsFundingResponse", loanIsFundingResponse);
                        if (loanIsFundingResponse.success) {
                          output.nachURL = loanIsFundingResponse.nachURL;
                          output.Status = 1;
                        } else {
                          output.Description = loanIsFundingResponse.message;
                          output.Status = 0;
                          saveErrorLog("saveLoanRequest", apiPath, model1, loanIsFundingResponse.message);
                        }
                      } else {
                        output.Description = "Error Occurred While Approving Loan. Try Again.";
                        output.Status = 0;
                        saveErrorLog("saveLoanRequest", apiPath, model1, approvedByAdminResponse.message);
                      }

                    }
                  } else if (loanInfo.status == "Accepted By Client") {

                    const digioBank = await DigioBankList.findOne({
                      systemBankName: {
                        $regex: new RegExp("^" + bankMaster.bank + "$", "i")
                      }
                    });

                    if (!digioBank || (digioBank && !digioBank.netBanking && !digioBank.debitCard && !digioBank.esign)) {
                      output.Description = "e-Nach option not available for selected bank. Please select different bank.";
                      output.Status = 0;
                      return output;
                    }

                    let authMode = config.get("digio.eNach.authModeAadhaar")
                    if (digioBank.debitCard || digioBank.netBanking) authMode = config.get("digio.eNach.authModeApi")

                    const loanIsFundingResponse = await loanIsFunding(loanInfo._id, null, false, "digilocker", false, authMode, true);
                    logger.logs("loanIsFundingResponse", loanIsFundingResponse);
                    if (loanIsFundingResponse.success) {
                      output.nachURL = loanIsFundingResponse.nachURL;
                      output.Status = 1;
                    } else {
                      output.Description = "Error Occurred While Loan Is Funding. Try Again.";
                      output.Status = 0;
                      saveErrorLog("saveLoanRequest", apiPath, model1, loanIsFundingResponse.message);
                    }
                  } else if (loanInfo.loanContractDetails.eNachUrl) {
                    output.nachURL = loanInfo.loanContractDetails.eNachUrl;
                  }
                }
              } else {
                const updatePartnerLoan = await UpdatePartnerLoan(existingLoan._id, model);

                if (updatePartnerLoan.success) {
                  let pushResponse = await PushToFundfina(existingLoan._id).catch(error => {
                    return error;
                  });

                  logger.logs("pushResponse", pushResponse);

                  if (pushResponse.success == true) {
                    loanInfo = await LoanInfo.findOne({ productId: pushResponse.data });
                    partnerLoan = await PartnerLoans.findById(existingLoan._id);
                    output.loanNo = loanInfo.loanNo;

                    if (conf.isDigiLockerMandatory) {
                      const resSetDigiLockerFields = await setDigiLockerFieldsInUserInfo(loanInfo.userId, partnerLoan.digilocker);
                      logger.logs("resSetDigiLockerFields", resSetDigiLockerFields);

                      if (resSetDigiLockerFields.success) {
                        if (loanInfo) {
                          const digioBank = await DigioBankList.findOne({
                            systemBankName: {
                              $regex: new RegExp("^" + bankMaster.bank + "$", "i")
                            }
                          });

                          if (!digioBank || (digioBank && !digioBank.netBanking && !digioBank.debitCard && !digioBank.esign)) {
                            partnerLoan = await PartnerLoans.findOneAndUpdate({ _id: mongoose.Types.ObjectId(partnerLoan._id) }, {
                              $set: {
                                responseSuccess: false,
                                responseMessage: "e-Nach option not available for selected bank. Please select different bank."
                              }
                            }, { new: true, useFindAndModify: false }).lean();

                            loanInfo = await LoanInfo.findOneAndUpdate({ _id: mongoose.Types.ObjectId(loanInfo._id) }, {
                              $set: {
                                isDraft: true,
                                status: "Draft"
                              }
                            }, { new: true, useFindAndModify: false }).lean();

                            output.Description = "e-Nach option not available for selected bank. Please select different bank.";
                            return output;
                          } else {
                            loanInfo = await LoanInfo.findOneAndUpdate({ _id: mongoose.Types.ObjectId(loanInfo._id) }, {
                              $set: {
                                isDraft: false,
                                status: "Under Review"
                              }
                            }, { new: true, useFindAndModify: false }).lean();
                          }

                          const approvedByAdminResponse = await approvedByAdmin(loanInfo._id, loanInfo.interestType, loanInfo.tenure, loanInfo.maturity, loanInfo.interestRate, loanInfo.repaymentFrequency, loanInfo.adhocDays, loanInfo.remarks, loanInfo.userId, "digilocker");

                          if (approvedByAdminResponse.success) {

                            let authMode = config.get("digio.eNach.authModeAadhaar")
                            if (digioBank.debitCard || digioBank.netBanking) authMode = config.get("digio.eNach.authModeApi")

                            const loanIsFundingResponse = await loanIsFunding(loanInfo._id, null, false, "digilocker", false, authMode);
                            logger.logs("loanIsFundingResponse", loanIsFundingResponse);
                            if (loanIsFundingResponse.success) {
                              output.nachURL = loanIsFundingResponse.nachURL;
                              output.Description = "Inserted Successfully";
                              output.Status = 1;
                            } else {
                              output.Description = "Error Occurred While Loan Is Funding. Try Again.";
                              output.Status = 0;
                              saveErrorLog("saveLoanRequest", apiPath, model1, loanIsFundingResponse.message);
                            }
                          } else {
                            output.Description = "Error Occurred While Approving Loan.  Try Again.";
                            output.Status = 0;
                            saveErrorLog("saveLoanRequest", apiPath, model1, approvedByAdminResponse.message);
                          }
                        }
                      }
                    } else {
                      //For Document Upload
                      if (!IsNullEmpty(model.documents)) {
                        const userInfo = await UserInfo.findById(pushResponse.userId);
                        if (!IsNullEmpty(userInfo)) {
                          if (!userInfo.panDocVerification || (userInfo.panDocVerification && userInfo.panDocVerification.type !== "digilocker")) {
                            for (doc of model.documents) {
                              //logger.logs("doc.docType", doc.docType);
                              await uploadImage(doc.docNo, doc.docType, doc.docPath, userInfo, doc.docExtension);
                            }
                          }

                          await userInfo.save();
                        }
                      }

                      // logger.logs("calling validateKYC");
                      validateKYC(pushResponse.userId, partner._id, "partnerMerchant");
                      output.Description = "Inserted Successfully";
                      output.Status = 1;
                    }

                    output.statusCode = 200;
                    output.product_id = pushResponse.data;
                    output.Success = "HTTP 200 OK";

                  } else {
                    output = {
                      statusCode: 200,
                      product_id: null,
                      loanNo: null,
                      nachURL: null,
                      Success: "HTTP 200 OK",
                      Status: 0,
                      Description: pushResponse.message,
                    };
                  }

                  await PartnerLoans.findOneAndUpdate({ _id: mongoose.Types.ObjectId(existingLoan._id) }, {
                    $set: {
                      responseSuccess: pushResponse.success,
                      responseMessage: pushResponse.message
                    }
                  }, { new: true, useFindAndModify: false }).lean();
                } else {
                  await PartnerLoans.findOneAndUpdate({ _id: mongoose.Types.ObjectId(existingLoan._id) }, {
                    $set: {
                      responseSuccess: false,
                      responseMessage: updatePartnerLoan.message
                    }
                  }, { new: true, useFindAndModify: false }).lean();
                  output.Description = updatePartnerLoan.message;
                }
              }
            } else {
              let isValidTrxnId = false;

              if (preApproval.trxnId == null) {
                preApproval.trxnId = model.trxnId;
                await preApproval.save();
              }

              if (today <= preApproval.expiredOn) {
                isValidTrxnId = true;
              } else output.Description = "Offer is expired";

              if (isValidTrxnId) {
                if (validatePan(model.panNo.toUpperCase())) {
                  let state = !IsNullEmpty(model.state) ? model.state.toUpperCase() : "";
                  let city = !IsNullEmpty(model.city) ? model.city.toUpperCase() : "";

                  if (!IsNullEmpty(model.pincode)) {
                    const stateCityMaster = await getCityState(partner._id, model.agent_code, model.state, model.city, model.pincode);
                    state = stateCityMaster.state;
                    city = stateCityMaster.city;
                  }

                  let partnerLoan = new PartnerLoans({
                    partnerId: partner._id,
                    trxnId: model.trxnId,
                    productId: !IsNullEmpty(preApproval.productId) ? preApproval.productId : null,
                    loanId: null,
                    panNo: model.panNo.toUpperCase(),
                    clientCode: null,
                    merchantCode: model.agent_code,
                    loanRequest: {
                      firstName: model.firstName,
                      middleName: null,
                      lastName: model.lastName,
                      mobileNo: model.mobileNo,
                      emailId: model.emailID,
                      aadhaarNo: model.aadhaarNo,
                      city: city,
                      street: model.street,
                      state: state,
                      pincode: model.pincode,
                      merchantType: model.agent_type,
                      loanAmount: loanAmount ? loanAmount : preApproval.approvedAmount,
                      tenure: model.tenure ? model.tenure : null,
                      onboardedDate: model.onboardedDate,
                      isWeekendLoan: model.isWeekendLoan,
                      isActive: true,
                      insertedOn: Date.now(),
                      modifiedOn: null,
                      deactivatedOn: null,
                    },
                    ipAddress: model.ipAddress,
                    virtualAccountNo: model.virtualAccountNo,
                    contractSuccessCallBackUrl: model.contractSuccessCallBackUrl ? model.contractSuccessCallBackUrl : null,
                    eNachErrorCallBackUrl: model.eNachErrorCallBackUrl ? model.eNachErrorCallBackUrl : null,
                    isBankValidated: model.isBankValidated ? model.isBankValidated : false,
                    beneficiary_name: model.beneficiary_name ? model.beneficiary_name : null,
                    insertedOn: Date.now()
                  });

                  if (!IsNullEmpty(model.productId)) {
                    partnerLoan.productId = model.productId;
                  }

                  if (!IsNullEmpty(model.personalDetails)) {
                    let personalDetails = {
                      name: model.personalDetails.name,
                      gender: model.personalDetails.gender,
                      qualification: model.personalDetails.qualification,
                      dob: model.personalDetails.dob,
                      maritalStatus: model.personalDetails.marital_status,
                      mobile: model.personalDetails.mobile,
                      alternateMobile: model.personalDetails.alternate_mobile,
                      homeOwnershipType: model.personalDetails.home_ownership_type,
                      currentAdd: model.personalDetails.current_address ? model.personalDetails.current_address.replace("#", "") : null,
                      city: model.personalDetails.city,
                      state: model.personalDetails.state,
                      pincode: model.personalDetails.pincode,
                      marital_status: model.personalDetails.marital_status,
                      phoneConnectionType: model.personalDetails.phoneConnectionType,
                      whatsAppInMobile: model.personalDetails.whatsAppInMobile,
                      children: model.personalDetails.children,
                      ownHome: model.personalDetails.ownHome,
                      vehicle: model.personalDetails.vehicle,
                      avgMonthlyOutgoings: model.personalDetails.avgMonthlyOutgoings,
                      avgMonthlyIncome: model.personalDetails.avgMonthlyIncome,
                      jobType: model.personalDetails.jobType,
                      whiteGoodsIn24Months: model.personalDetails.whiteGoodsIn24Months,
                      educationalDegree: model.personalDetails.educationalDegree
                    };

                    partnerLoan.personalDetails = personalDetails;
                  }

                  if (!IsNullEmpty(model.shopDetails)) {
                    let shopDetails = {
                      name: !IsNullEmpty(model.shopDetails.shopName)
                        ? model.shopDetails.shopName
                        : model.shopDetails.shop_name,
                      type: !IsNullEmpty(model.shopDetails.shopType)
                        ? model.shopDetails.shopType
                        : model.shopDetails.shop_type,
                      add: !IsNullEmpty(model.shopDetails.shopAddress)
                        ? model.shopDetails.shopAddress
                        : model.shopDetails.shop_address,
                      city: model.shopDetails.city,
                      state: model.shopDetails.state,
                      pincode: model.shopDetails.pincode,
                      location: !IsNullEmpty(model.shopDetails.shopLocation)
                        ? model.shopDetails.shopLocation
                        : model.shopDetails.shop_location,
                      contactNo: model.shopDetails.shop_contact_number,
                      ownershipType: model.shopDetails.shop_ownership_type,
                      companyType: model.shopDetails.company_type,
                      shopVinatge: model.shopDetails.shop_vintage,
                      businessVintage: model.shopDetails.business_vintage,
                    };

                    partnerLoan.shopDetails = shopDetails;
                  }

                  if (!IsNullEmpty(model.accountDetails)) {
                    let accountDetails = {
                      mobile: model.accountDetails.mobile,
                      panNo: model.accountDetails.panNumber,
                      merchantCode: model.accountDetails.merchantCode,
                    };
                    partnerLoan.accoutDetails = accountDetails;
                  }

                  if (!IsNullEmpty(model.transactions)) {
                    for (itemTransaction of model.transactions) {
                      let transaction = {
                        primary: itemTransaction.primary,
                        type: itemTransaction.transactionsType,
                        value: itemTransaction.transactionsValue,
                        monthYear: itemTransaction.transactionsMonthYear,
                        valueType: itemTransaction.transactionsValueType,
                        primaryCount: itemTransaction.primaryCount,
                      };
                      partnerLoan.transactionDetails.push(transaction);
                    }
                  }

                  if (!IsNullEmpty(model.ekoLoanHistory)) {
                    for (itemLoanHistory of model.ekoLoanHistory) {
                      let loanHistory = {
                        providerName: itemLoanHistory.loanProviderName,
                        amount: itemLoanHistory.loanAmount,
                        status: itemLoanHistory.status,
                        totalRepaidAmount: itemLoanHistory.totalRepaidAmount,
                      };
                      partnerLoan.loanHistory.push(loanHistory);
                    }
                  }

                  if (!IsNullEmpty(model.loanHistory)) {
                    for (itemLoanHistory of model.loanHistory) {
                      let loanHistory = {
                        providerName: itemLoanHistory.loanProviderName,
                        amount: itemLoanHistory.loanAmount,
                        status: itemLoanHistory.status,
                        totalRepaidAmount: itemLoanHistory.totalRepaidAmount,
                      };
                      partnerLoan.loanHistory.push(loanHistory);
                    }
                  }

                  if (!IsNullEmpty(model.bankDetails)) {
                    partnerLoan.bankDetails = model.bankDetails;
                  }

                  if (!IsNullEmpty(model.documents)) {
                    for (itemdocuments of model.documents) {
                      let document = {
                        docNo: itemdocuments.docNo,
                        docType: itemdocuments.docType,
                        docPath: itemdocuments.docPath,
                        docExtension: itemdocuments.docExtension
                      }
                      partnerLoan.documents.push(document);
                    }
                  }

                  if (!IsNullEmpty(model.digilocker)) {
                    partnerLoan.digilocker = {
                      execution_request_id: model.digilocker.execution_request_id,
                      created_at: model.digilocker.created_at,
                      updated_at: model.digilocker.updated_at,
                      reference_id: model.digilocker.reference_id,
                      status: model.digilocker.status,
                      processing_done: model.digilocker.processing_done ? model.digilocker.processing_done : null,
                      face_match_status: model.digilocker.face_match_status,
                      validation_result: model.digilocker.validation_result,
                      face_match_obj_type: model.digilocker.face_match_obj_type,
                      obj_analysis_status: model.digilocker.obj_analysis_status,
                      document_details: {
                        aadhaar: {
                          docNo: model.digilocker.document_details.aadhaar.docNo,
                          document_type: model.digilocker.document_details.aadhaar.document_type,
                          id_proof_type: model.digilocker.document_details.aadhaar.id_proof_type,
                          gender: model.digilocker.document_details.aadhaar.gender,
                          image: model.digilocker.document_details.aadhaar.image,
                          name: model.digilocker.document_details.aadhaar.name,
                          dob: model.digilocker.document_details.aadhaar.dob,
                          current_address: model.digilocker.document_details.aadhaar.current_address,
                          permanent_address: model.digilocker.document_details.aadhaar.permanent_address,
                          current_address_details: {
                            address: model.digilocker.document_details.aadhaar.current_address_details.address,
                            locality_or_post_office: model.digilocker.document_details.aadhaar.current_address_details.locality_or_post_office,
                            district_or_city: model.digilocker.document_details.aadhaar.current_address_details.district_or_city,
                            state: model.digilocker.document_details.aadhaar.current_address_details.state,
                            pincode: model.digilocker.document_details.aadhaar.current_address_details.pincode,
                          },
                          permanent_address_details: {
                            address: model.digilocker.document_details.aadhaar.permanent_address_details.address,
                            locality_or_post_office: model.digilocker.document_details.aadhaar.permanent_address_details.locality_or_post_office,
                            district_or_city: model.digilocker.document_details.aadhaar.permanent_address_details.district_or_city,
                            state: model.digilocker.document_details.aadhaar.permanent_address_details.state,
                            pincode: model.digilocker.document_details.aadhaar.permanent_address_details.pincode,
                          },
                          docPath: model.digilocker.document_details.aadhaar.docPath,
                        },
                        pan: {
                          name: model.digilocker.document_details.pan.name,
                          docNo: model.digilocker.document_details.pan.docNo,
                          dob: model.digilocker.document_details.pan.dob,
                          document_type: model.digilocker.document_details.pan.document_type,
                          id_proof_type: model.digilocker.document_details.pan.id_proof_type,
                          typeOfHolder: model.digilocker.document_details.pan.typeOfHolder,
                          isIndividual: model.digilocker.document_details.pan.isIndividual ? model.digilocker.document_details.pan.isIndividual : null,
                          isValid: model.digilocker.document_details.pan.isValid ? model.digilocker.document_details.pan.isValid : null,
                          firstName: model.digilocker.document_details.pan.firstName,
                          middleName: model.digilocker.document_details.pan.middleName,
                          lastName: model.digilocker.document_details.pan.lastName,
                          title: model.digilocker.document_details.pan.title,
                          panStatus: model.digilocker.document_details.pan.panStatus,
                          panStatusCode: model.digilocker.document_details.pan.panStatusCode,
                          aadhaarSeedingStatus: model.digilocker.document_details.pan.aadhaarSeedingStatus,
                          aadhaarSeedingStatusCode: model.digilocker.document_details.pan.aadhaarSeedingStatusCode,
                          lastUpdatedOn: model.digilocker.document_details.pan.lastUpdatedOn,
                          docPath: model.digilocker.document_details.pan.docPath
                        }
                      }
                    }
                  }

                  let response = await partnerLoan.save();
                  // console.log(response._id);
                  logger.logs("calling pushResponse2");

                  let pushResponse = await PushToFundfina(response._id).catch(error => {
                    return error;
                  });

                  logger.logs("pushResponse", pushResponse);

                  if (pushResponse.success == true) {
                    loanInfo = await LoanInfo.findOne({ productId: pushResponse.data });
                    output.loanNo = loanInfo.loanNo;

                    if (conf.isDigiLockerMandatory) {
                      const resSetDigiLockerFields = await setDigiLockerFieldsInUserInfo(pushResponse.userId, partnerLoan.digilocker);
                      logger.logs("resSetDigiLockerFields", resSetDigiLockerFields);

                      if (resSetDigiLockerFields.success) {
                        if (loanInfo) {
                          const digioBank = await DigioBankList.findOne({
                            systemBankName: {
                              $regex: new RegExp("^" + bankMaster.bank + "$", "i")
                            }
                          });

                          if (!digioBank || (digioBank && !digioBank.netBanking && !digioBank.debitCard && !digioBank.esign)) {
                            await PartnerLoans.findOneAndUpdate({ _id: mongoose.Types.ObjectId(response._id) }, {
                              $set: {
                                responseSuccess: false,
                                responseMessage: "e-Nach option not available for selected bank. Please select different bank."
                              }
                            }, { new: true, useFindAndModify: false }).lean();
                            loanInfo.isDraft = true;
                            loanInfo.status = "Draft";
                            await loanInfo.save();

                            output.Description = "e-Nach option not available for selected bank. Please select different bank.";
                            return output;
                          }

                          const approvedByAdminResponse = await approvedByAdmin(loanInfo._id, loanInfo.interestType, loanInfo.tenure, loanInfo.maturity, loanInfo.interestRate, loanInfo.repaymentFrequency, loanInfo.adhocDays, loanInfo.remarks, loanInfo.userId, "digilocker");

                          if (approvedByAdminResponse.success) {

                            let authMode = config.get("digio.eNach.authModeAadhaar")
                            if (digioBank.debitCard || digioBank.netBanking) authMode = config.get("digio.eNach.authModeApi")

                            const loanIsFundingResponse = await loanIsFunding(loanInfo._id, null, false, "digilocker", false, authMode);
                            logger.logs("loanIsFundingResponse", loanIsFundingResponse);
                            if (loanIsFundingResponse.success) {
                              output.nachURL = loanIsFundingResponse.nachURL;
                              output.Description = "Inserted Successfully";
                              output.Status = 1;
                            } else {
                              output.Description = "Error Occurred While Loan Is Funding. Try Again.";
                              output.Status = 0;
                              saveErrorLog("saveLoanRequest", apiPath, model1, loanIsFundingResponse.message);
                            }
                          } else {
                            output.Description = "Error Occurred While Approving Loan.  Try Again.";
                            output.Status = 0;
                            saveErrorLog("saveLoanRequest", apiPath, model1, approvedByAdminResponse.message);
                          }
                        }
                      }
                    } else {
                      //For Document Upload
                      if (!IsNullEmpty(model.documents)) {
                        const userInfo = await UserInfo.findById(pushResponse.userId);
                        if (!IsNullEmpty(userInfo)) {
                          for (doc of model.documents) {
                            //logger.logs("doc.docType", doc.docType);
                            await uploadImage(doc.docNo, doc.docType, doc.docPath, userInfo, doc.docExtension);
                          }
                          await userInfo.save();
                        }
                      }

                      // logger.logs("calling validateKYC");
                      validateKYC(pushResponse.userId, partner._id, "partnerMerchant");
                      output.Description = "Inserted Successfully";
                      output.Status = 1;
                    }

                    output.statusCode = 200;
                    output.product_id = pushResponse.data;
                    output.Success = "HTTP 200 OK";

                  } else {
                    output = {
                      statusCode: 200,
                      product_id: null,
                      loanNo: null,
                      nachURL: null,
                      Success: "HTTP 200 OK",
                      Status: 0,
                      Description: pushResponse.message,
                    };
                  }

                  await PartnerLoans.findOneAndUpdate({ _id: mongoose.Types.ObjectId(response._id) }, {
                    $set: {
                      responseSuccess: pushResponse.success,
                      responseMessage: pushResponse.message
                    }
                  }, { new: true, useFindAndModify: false }).lean();
                } else {
                  output.Description = "invalid pan no " + model.panNo;
                }
              }

              return output;
            }
          } else {
            output.Description = validationResponse.message;
          }
        } else {
          output.Description = "PreApproval Offer not Found";
        }
      } else {
        output.Description = "Config is not set by Fundfina. Please contact Fundfina."
      }
    } else {
      output.Description = "Invalid Partner";
    }
  } catch (ex) {
    logger.logs(ex);
    saveErrorLog("saveLoanRequest", apiPath, model1, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    return output;
  }

  return output;
};

async function loanRequestValdiation(model, preApproval) {
  let output = {
    success: false,
    message: "Server Error Occurred",
    status: 200
  }
  try {
    //Check For Loan Amount
    if (model.loan_amount && model.loan_amount != null) {
      loanAmount = Number(model.loan_amount);
      if (loanAmount < config.get("keys.minLoanAmount")) {
        output.message = `loan_amount should be greater than ${config.get("keys.minLoanAmount")}`;
        return output;
      } else if (loanAmount > preApproval.approvedAmount) {
        output.message = `loan_amount should be less than equal to offer amount`;
        return output;
      } else if (loanAmount % config.get("keys.loanAmountMultiple") != 0) {
        output.message = `loan_amount should be multiple of ${config.get("keys.loanAmountMultiple")}`;
        return output;
      }
    } else {
      output.message = "loan_amount is Mandatory";
      return output;
    }

    const partnerConfig = await PartnerConfig.findOne({ partnerId: mongoose.Types.ObjectId(preApproval.partnerId) });

    if (IsNullEmpty(model.virtualAccountNo) && partnerConfig.disbursalType && partnerConfig.disbursalType.toLowerCase() === "virtual") {
      output.message = `Virtual account number not received from Enterprise partner`;
      return output;
    }


    //Check For Tenure
    if (model.tenure && model.tenure != null && model.tenure != "") {
      const productMaster = await ProductMaster.findOne({ product: preApproval.productType });

      if (productMaster != null) {
        const productPartnerConfig = await ProductPartnerConfig.findOne({ partnerId: mongoose.Types.ObjectId(preApproval.partnerId), productId: mongoose.Types.ObjectId(productMaster._id) });

        if (productPartnerConfig != null) {
          if (model.tenure < productPartnerConfig.minTenure || model.tenure > productPartnerConfig.maxTenure) {
            output.message = `tenure should be between ${productPartnerConfig.minTenure} and ${productPartnerConfig.maxTenure}`;
            return output;
          }
        } else {
          output.message = "Product Not Found";
          return output;
        }
      } else {
        output.message = "Product Not Found";
        return output;
      }
    }

    //Check For Shop Details
    const merchantType = !IsNullEmpty(preApproval.merchantType) ? preApproval.merchantType : "Retailer";
    if (merchantType == "Retailer") {
      if (model.shopDetails && model.shopDetails != null) {
        if (IsNullEmpty(model.shopDetails.shop_name)) {
          output.message = "shop_name is Mandatory";
          return output;
        } else if (IsNullEmpty(model.shopDetails.shop_address)) {
          output.message = "shop_address is Mandatory";
          return output;
        } else if (IsNullEmpty(model.shopDetails.shop_type)) {
          output.message = "shop_type is Mandatory";
          return output;
        } else if (IsNullEmpty(model.shopDetails.shop_ownership_type)) {
          output.message = "shop_ownership_type is Mandatory";
          return output;
        } else if (IsNullEmpty(model.shopDetails.state)) {
          output.message = "state is Mandatory";
          return output;
        } else if (IsNullEmpty(model.shopDetails.city)) {
          output.message = "city is Mandatory";
          return output;
        } else if (IsNullEmpty(model.shopDetails.pincode)) {
          output.message = "pincode is Mandatory";
          return output;
        }
      } else {
        output.message = "shopDetails is Mandatory";
        return output;
      }
    }

    output = {
      success: true,
      message: "Validated Successfully",
      status: 200
    }
  } catch (ex) {
    saveErrorLog("loanRequestValdiation", apiPath, { model, preApproval }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
  }
  return output;
};

async function loanOffer(loanId, isAccepted, step, partnerCode, reason, description) {
  //step should be either 1 or 2
  //isAccepted is boolean

  let output = {
    data: null,
    success: false,
    message: "Server Error Occurred",
    status: 501,
  };
  try {
    let loanInfo = await LoanInfo.findById(loanId);

    if (!IsNullEmpty(loanInfo)) {
      let partnerLoans = await PartnerLoans.findOne({
        productId: loanInfo.productId,
        trxnId: loanInfo.trxnId,
      });
      if (!IsNullEmpty(partnerLoans)) {
        logger.logs("partnerId : ", partnerLoans.partnerId);
        let partnerMaster = await PartnerMaster.findById(
          partnerLoans.partnerId
        );

        const isClaimed = isAccepted == true && step == 2 ? true : false;

        logger.logs("loanAmount : ", loanInfo.amount);
        logger.logs("processingCharge : ", partnerMaster.processingCharge);

        //let chargeInPer = 0;
        const preApproval = await PreApprovals.findOne({
          partnerId: mongoose.Types.ObjectId(loanInfo.partnerId),
          merchantCode: loanInfo.merchantCode,
          productType: loanInfo.productType.split("_")[0],
          $or: [{ trxnId: loanInfo.trxnId }, { productId: loanInfo.trxnId }]
        }).sort({ insertedOn: -1 });

        const charges = await getLoanCharges(loanInfo.amount, loanInfo._id, preApproval._id);
        if (charges.success) {
          let today = new Date();
          let tenure = loanInfo.tenure;

          logger.logs("repaymentFrequency : ", loanInfo.repaymentFrequency.toLowerCase());
          logger.logs("tenure : ", tenure);

          let expiryDate = dateAdd("day", 10, today);
          logger.logs("expiryDate : ", expiryDate);


          let totalAmountPayable = 0;
          let interestPayable = 0;
          let emiAmount = 0;
          let totalEmi = 0;

          let schedule = null;
          if (!IsNullEmpty(loanInfo.interestRate)) {
            schedule = await emiSchedule(loanInfo._id, today, false);

            logger.logs("schedule : ", schedule);


            if (schedule.success) {
              let emiSchedule = schedule.data;
              emiAmount = emiSchedule[0].rePaymentAmount;
              for (itemSchedule of emiSchedule) {
                totalEmi = totalEmi + 1;
                totalAmountPayable = totalAmountPayable + parseFloat(itemSchedule.rePaymentAmount);
                interestPayable = interestPayable + parseFloat(itemSchedule.interestAmount);
              }
            } else {
              output.message = "EmiSchedule failed";
              return output;
            }
          }

          let disbursedAmount = loanInfo.amount - charges.totalCharges;
          logger.logs("totalEmi : ", totalEmi);
          logger.logs("interestPayable : ", interestPayable);
          logger.logs("emiAmount : ", emiAmount);
          logger.logs("totalAmountPayable : ", totalAmountPayable);

          let loanOffer = {
            step_num: step,
            proceed: isAccepted,
            claimed: isClaimed,
            reason: isAccepted ? null : reason,
            description: isAccepted ? null : description,
            loanAmount: loanInfo.amount,
            interestRate: isAccepted == true ? loanInfo.interestRate : 0,
            interestType: "percent",
            repaymentDays: 1,
            processingFee: charges.totalCharges,
            loanType: "Emi",
            tenure: tenure,
            autoRepaymentStatus: 1,
            additionalData: loanInfo.adminRemarks,
            expiryDate: expiryDate,
            totalAmountPayable: totalAmountPayable.toFixed(2),
            interestPayable: interestPayable.toFixed(2),
            emiAmount: parseFloat(emiAmount).toFixed(2),
            totalEmi: parseInt(totalEmi),
            requestedAt: loanInfo.insertedOn,
            disbursedAmount: disbursedAmount,
            isActive: true,
            insertedOn: Date.now(),
            deactivatedOn: null,
          };

          let activeOffers = partnerLoans.loanOffer.filter(
            (x) => x.isActive == true
          );

          if (!IsNullEmpty(activeOffers)) {
            for (itemOffers of activeOffers) {
              itemOffers.isActive = false;
              itemOffers.deactivatedOn = Date.now();
            }
          }

          partnerLoans.loanOffer.push(loanOffer);
          partnerLoans.save();

          let loanOfferEko = {
            product_id: partnerLoans.productId,
            step_num: loanOffer.step_num,
            proceed: loanOffer.proceed,
            claimed: loanOffer.claimed,
            data: {
              loanAmount: loanOffer.loanAmount,
              interestRate: loanOffer.interestRate,
              interestType: loanOffer.interestType,
              repaymentDays: loanOffer.repaymentDays,
              processingFees: loanOffer.processingFee,
              loanType: loanOffer.loanType,
              tenure: loanOffer.tenure,
              autoRepaymentStatus: loanOffer.autoRepaymentStatus,
              additionalData: loanOffer.additionalData,
              expiryDate: loanOffer.expiryDate,
              totalAmountPayable: loanOffer.totalAmountPayable,
              interestPayable: loanOffer.interestPayable,
              emiAmount: loanOffer.emiAmount,
              totalEmi: loanOffer.totalEmi,
            },
          };

          if (partnerCode != "eko") {
            loanOfferEko.reason = isAccepted ? null : reason;
            loanOfferEko.description = isAccepted ? null : description;
          }
          let ekoResponse;
          //console.log(loanOfferEko);
          //console.log("partnerCode", partnerCode);
          if (partnerCode === "eko") {
            const developerKey = config.get("eko.developerKey");
            const url = config.get("eko.offerUrl");

            logger.logs("url:", url);
            logger.logs("Request:", loanOfferEko);
            logger.logs("DeveloperKey", developerKey);

            const _body = JSON.stringify(loanOfferEko);
            logger.logs("_body", _body);

            ekoResponse = await fetch(url, {
              method: "post",
              body: _body,
              headers: {
                "Content-Type": "application/json",
                Developer_key: developerKey,
              },
            })
              .then(function (u) {
                return u.json();
              })
              .then(function (json) {
                return json;
              })
              .catch((err) => {
                console.error("err :", err);
                return err;
              });

            logger.logs("ekoResponse : ", ekoResponse);
          } else if (partnerCode === "mpay" || partnerCode.toLowerCase() == "pworld") {

            let urlPath = "";
            let header = "";

            switch (partnerCode.toLowerCase()) {
              case "mpay":
                urlPath = config.get("mpay.offerUrl");
                header = {
                  "Content-Type": "application/json",
                  "dev_key": config.get("mpay.dev_key"),
                }
                break;
              case "pworld":
                urlPath = config.get("payWorld.offerUrl");
                header = {
                  "Content-Type": "application/json",
                  "auth-key": config.get("payWorld.auth-key"),
                }
                break;
            }
            // logger.logs("url:", url);
            // logger.logs("Request:", loanOfferEko);
            // logger.logs("DeveloperKey", developerKey);

            const _body = JSON.stringify(loanOfferEko);
            logger.logs("_body :", _body);

            ekoResponse = await fetch(urlPath, {
              method: "post",
              body: _body,
              headers: header,
            })
              .then(function (u) {
                return u.json();
              })
              .then(function (json) {
                return json;
              })
              .catch((err) => {
                console.error("err :", err);
                return err;
              });

            logger.logs("ekoResponse : ", ekoResponse);
          }

          if (ekoResponse != null) {
            if (!IsNullEmpty(ekoResponse.status)) {
              const status = (ekoResponse.status.toString().toLowerCase() == "1" || ekoResponse.status.toString().toLowerCase() == "true") ? true : false;
              let loanOfferResponse = {
                description: ekoResponse.description,
                status: ekoResponse.status,
                isOfferAcceptance: (step == 2 && status) ? true : false,
                insertedOn: Date.now(),
              };

              partnerLoans.loanOfferResponse.push(loanOfferResponse);
              partnerLoans.save();

              if (status) {
                output = {
                  data: ekoResponse,
                  success: true,
                  message: "offer sent successfully",
                  status: 200,
                };
              } else {
                output.message = ekoResponse.description;
              }
            } else {
              output = {
                data: ekoResponse,
                success: false,
                message: "Error Occurred",
                status: 501,
              };
            }
          } else {
            output = {
              data: ekoResponse,
              success: false,
              message: "Error Occurred",
              status: 501,
            };
          }
        } else output.message = charges.message;
      } else output.message = "Partner Loan not found";
    } else output.message = "Loan not found";

  } catch (ex) {
    const model = { loanId: loanId, isAccepted: isAccepted, step: step, partnerCode: partnerCode };
    saveErrorLog("loanOffer", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
  }
  return output;
};
module.exports.loanOffer = loanOffer;

module.exports.saveBorrowerLoanAcceptance = async function (model, partnerId) {
  let output = {
    status: 0,
    description: "Server Error Occurred",
    statusCode: 501
  };

  try {
    if (!IsNullEmpty(model)) {
      let partnerLoans = await PartnerLoans.findOne({
        productId: model.product_id,
        partnerId: mongoose.Types.ObjectId(partnerId)
      });

      const customerAccept = model.data.consent.customerAccept.toString().toLowerCase() == "true" ? true : false;
      let errorReason = null;
      let errorDescription = null;

      if (!IsNullEmpty(model.data.consent.reason)) errorReason = model.data.consent.reason.toString();
      if (!IsNullEmpty(model.data.consent.description)) errorDescription = model.data.consent.description.toString();

      if (!IsNullEmpty(partnerLoans)) {
        let borrowerAcceptance = {
          step_num: model.step_num,
          customerAccept: customerAccept,
          reason: errorReason,
          description: errorDescription,
          insertedOn: Date.now(),
        };

        partnerLoans.borrowerAcceptance.push(borrowerAcceptance);
        await partnerLoans.save();


        let loanInfo = await LoanInfo.findOne({
          productId: partnerLoans.productId,
          status: { $regex: "Approved By Admin", $options: "i" }
        });

        if (!IsNullEmpty(loanInfo)) {
          const userInfo = await UserInfo.findOne({
            isPartnerUser: true,
            "partnerDetails.partnerName": { $eq: loanInfo.partner },
          });

          if (!IsNullEmpty(userInfo)) {
            loanInfo.status = customerAccept == true ? "Accepted By Client" : "Rejected By Client";
            loanInfo.clientRemarks = errorReason;
            loanInfo.rejectDescription = errorDescription;
            // logger.logs("model.data.consent.customerAccept",model.data.consent.customerAccept);
            // logger.logs("loanInfo.Status",loanInfo.Status);
            const loanFlow = {
              Step: customerAccept == true ? "Accepted By Client" : "Rejected By Client",
              changedBy: userInfo._id,
              changedByRole: "User",
              insertedOn: Date.now(),
            };
            loanInfo.flowDetails.push(loanFlow);

            // logger.logs('saveResponse',saveResponse);

            if (customerAccept == true) {

              const partnerMaster = await PartnerMaster.findOne({
                partnerName: { $regex: new RegExp(loanInfo.partner, "i") },
              });

              if (!IsNullEmpty(partnerMaster)) {
                let loanOfferReponse = await loanOffer(loanInfo._id, true, 2, partnerMaster.partnerCode.toLowerCase());

                if (loanOfferReponse.success == true) {
                  let saveResponse = await loanInfo.save();
                  output = {
                    statusCode: 200,
                    status: 1,
                    description: "Success",
                  };
                } else {
                  output.description = "Database Updation Failed !!!";
                }
              }
            } else {
              let saveResponse = await loanInfo.save();
              output = {
                statusCode: 200,
                status: 1,
                description: "Success",
              };
            }
          } else {
            output.description = "register as partner on fundfina";
          }
        } else {
          loanInfo = await LoanInfo.find({ productId: partnerLoans.productId }).countDocuments();
          output.description = loanInfo > 0 ? "Please get it approved from Fundfina" : "Invalid product_id for Loan";
        }
      } else {
        output.description = "Invalid product_id";
      }
    }
  } catch (ex) {
    const model1 = { model: model, partnerId: partnerId };
    saveErrorLog("saveBorrowerLoanAcceptance", apiPath, model1, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
  }
  return output;
};

module.exports.statusUpdate = async function (loanId, status) {
  let output = {
    data: null,
    success: false,
    message: "Server Error Occurred",
    status: 501,
  };
  try {
    if (
      !IsNullEmpty(status) &&
      (status.toLowerCase() == "repaid completely" ||
        status.toLowerCase() == "rejected")
    ) {
      let loanInfo = await LoanInfo.findById(loanId);

      let statusPath = config.get("eko.statusUpdateUrl") + encodeURI(loanInfo.productId) + "/update";
      let loanStatus = status.toLowerCase() == "repaid completely" ? 5 : 6;

      let ekoRequest = {
        status: loanStatus,
      };

      // console.log(JSON.stringify(ekoRequest));
      if (!IsNullEmpty(loanInfo)) {
        let partnerLoans = await PartnerLoans.findOne({
          productId: loanInfo.productId,
          trxnId: loanInfo.trxnId,
        });

        const _body = JSON.stringify(ekoRequest);
        const developerKey = config.get("eko.developerKey");

        if (!IsNullEmpty(partnerLoans)) {
          let ekoResponse = await fetch(statusPath, {
            method: "post",
            body: _body,
            headers: {
              "Content-Type": "application/json",
              developer_key: developerKey,
            },
          })
            .then(function (u) {
              return u.json();
            })
            .then(function (json) {
              return json;
            })
            .catch((err) => console.error("err :", err));

          if (!IsNullEmpty(ekoResponse)) {
            if (!IsNullEmpty(ekoResponse.status)) {
              let statusUpdate = {
                loanStatus: loanStatus,
                status: ekoResponse.status,
                description: ekoResponse.description,
                insertedOn: Date.now(),
              };
              partnerLoans.statusUpdate.push(statusUpdate);
              await partnerLoans.save();

              if (ekoResponse.status == "1") {
                output = {
                  data: ekoResponse,
                  success: true,
                  message: "Status Udpated Successfully",
                  status: 200,
                };
              } else {
                output.data = ekoResponse;
                output.message = ekoResponse.description;
              }
            } else {
              output.data = ekoResponse;
            }
          } else {
            output = {
              data: ekoResponse,
              success: false,
              message: "Error Occurred",
              status: 501,
            };
          }
        } else {
          output.message = "Partner Loan not found";
        }
      } else {
        output.message = "Loan not found";
      }
    } else {
      output.message =
        "Status should be either RapidRepaid Completely Or Rejected";
    }
  } catch (ex) {
    const model = { loanId: loanId, status: status };
    saveErrorLog("statusUpdate", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
  }
  return output;
};

async function uploadImage(docNo, imageType, imageUrl, userInfo, extension) {
  let output = {
    success: false,
    filePath: null,
    message: "",
    status: 501,
  };
  try {
    const internalPath = userInfo._id;

    let fileName = "";
    let filePath = "";
    let poaType = "";
    let position = "";

    if (IsNullEmpty(extension)) {
      extension = getExtension(imageUrl);
    }

    if (!extension.includes(".")) extension = "." + extension;

    switch (imageType.toLowerCase()) {
      case "pan_front":
        position = "front";
        fileName = "pan_front_" + getCurrentTimeStamp() + extension;
        filePath = internalPath + "/PAN/" + fileName;
        poaType = "PAN";
        break;
      case "other_front":
        position = "front";
        fileName = "other_front_" + getCurrentTimeStamp() + extension;
        filePath = internalPath + "/POA/" + fileName;
        poaType = "Others";
        break;
      case "other_back":
        position = "back";
        fileName = "other_back_" + getCurrentTimeStamp() + extension;
        filePath = internalPath + "/POA/" + fileName;
        poaType = "Others";
        break;
      case "voter_id_front":
        position = "front";
        fileName = "voter_id_front_" + getCurrentTimeStamp() + extension;
        filePath = internalPath + "/POA/" + fileName;
        poaType = "Voter Id";
        break;
      case "voter_id_back":
        position = "back";
        fileName = "voter_id_back_" + getCurrentTimeStamp() + extension;
        filePath = internalPath + "/POA/" + fileName;
        poaType = "Voter Id";
        break;
      case "dl_front":
        position = "front";
        fileName = "dl_front_" + getCurrentTimeStamp() + extension;
        filePath = internalPath + "/POA/" + fileName;
        poaType = "Driving Licence";
        break;
      case "dl_back":
        position = "back";
        fileName = "dl_back_" + getCurrentTimeStamp() + extension;
        filePath = internalPath + "/POA/" + fileName;
        poaType = "Driving Licence";
        break;
      case "electricity_bill":
        position = "front";
        fileName = "electricity_bill_" + getCurrentTimeStamp() + extension;
        filePath = internalPath + "/POA/" + fileName;
        poaType = "Electricity Bill";
        break;
      case "gas_bill":
        position = "front";
        fileName = "gas_bill_" + getCurrentTimeStamp() + extension;
        filePath = internalPath + "/POA/" + fileName;
        poaType = "Gas Bill";
        break;
      case "water_bill":
        position = "front";
        fileName = "water_bill_" + getCurrentTimeStamp() + extension;
        filePath = internalPath + "/POA/" + fileName;
        poaType = "Water Bill";
        break;
      case "bank_statement":
        position = "front";
        fileName = "bank_statement_" + getCurrentTimeStamp() + extension;
        filePath = internalPath + "/POA/" + fileName;
        poaType = "Bank Statement";
        break;
      case "rent_agreement":
        position = "front";
        fileName = "rent_agreement_" + getCurrentTimeStamp() + extension;
        filePath = internalPath + "/POA/" + fileName;
        poaType = "Rent Agreement";
        break;
      case "ration_card":
        position = "front";
        fileName = "ration_card_" + getCurrentTimeStamp() + extension;
        filePath = internalPath + "/POA/" + fileName;
        poaType = "Ration Card";
        break;
      case "aadhaar_front":
        position = "front";
        fileName = "aadhaar_card_" + getCurrentTimeStamp() + extension;
        filePath = internalPath + "/POA/" + fileName;
        poaType = "Aadhaar Card";
        break;
      case "aadhaar_back":
        position = "back";
        fileName = "aadhaar_card_" + getCurrentTimeStamp() + extension;
        filePath = internalPath + "/POA/" + fileName;
        poaType = "Aadhaar Card";
        break;
      default:
        position = "front";
        fileName = "other_" + getCurrentTimeStamp() + extension;
        filePath = internalPath + "/POA/" + fileName;
        poaType = "Others";
        break;
    }

    let isExists = false;


    if (imageType == "pan_front") {
      if (IsNullEmpty(userInfo.panDocDetails)) {
        const downloadResponse = await downloadImageBuffer(imageUrl);

        filePath = "ProofImages/" + filePath;
        if (downloadResponse.success) {
          await uploadToS3(filePath, downloadResponse.data, "BUFFER")
          const panDoc = {
            fileName: fileName,
            filePath: filePath,
            position: position,
            isActive: true,
            insertedOn: Date.now(),
          };
          userInfo.panDocDetails.push(panDoc);

          output = {
            success: true,
            filePath: filePath,
            message: "downloaded successfully",
            status: 200,
          }
        } else {
          output.message = downloadResponse.message;
        }
      }
    } else {
      let poa = null;
      poa = await userInfo.poaDetails.find((x) => x.poaNo.substring(x.poaNo.length - 4) == docNo.substring(docNo.length - 4));
      // if (poaType == "Others")
      //   poa = await userInfo.poaDetails.find((x) => x.poaNo.substring(x.poaNo.length - 4) == docNo.substring(docNo.length - 4));
      // else
      //   poa = await userInfo.poaDetails.find((x) => x.poaNo.substring(x.poaNo.length - 4) == docNo.substring(docNo.length - 4) && x.poaType == poaType);

      //logger.logs("poa", poa);
      if (!IsNullEmpty(poa)) {
        if (!IsNullEmpty(poa.docDetails)) {
          const poaDoc = poa.docDetails.filter((x) => x.position == position);
          if (!IsNullEmpty(poaDoc)) isExists = true;
        }

        if (!isExists) {
          const downloadResponse = await downloadImageBuffer(imageUrl);

          if (downloadResponse.success) {
            filePath = "ProofImages/" + filePath;
            await uploadToS3(filePath, downloadResponse.data, "BUFFER")
            let _docDetails = {
              fileName: fileName,
              filePath: filePath,
              position: position,
              isActive: true,
              insertedOn: Date.now(),
            };
            poa.docDetails.push(_docDetails);

            output = {
              success: true,
              filePath: filePath,
              message: "downloaded successfully",
              status: 200,
            };
          }
        } else {
          output.message = "already exists";
        }
      } else {
        const downloadResponse = await downloadImageBuffer(imageUrl);
        if (downloadResponse.success) {
          filePath = "ProofImages/" + filePath;

          await uploadToS3(filePath, downloadResponse.data, "BUFFER")

          const poaDoc = {
            poaNo: docNo,
            poaType: poaType,
            poaVerificationDocType: poaType,
            docDetails: [
              {
                fileName: fileName,
                filePath: filePath,
                position: position,
                isActive: true,
                insertedOn: Date.now(),
              },
            ],
            insertedOn: Date.now(),
          };
          userInfo.poaDetails.push(poaDoc);

          output = {
            success: true,
            filePath: filePath,
            message: "downloaded successfully",
            status: 200,
          };
        } else {
          output.message = downloadResponse.message;
        }
      }
    }
  } catch (ex) {
    //console.log("error:", ex);
    const model = { docNo: docNo, imageType: imageType, imageUrl: imageUrl, userInfo: userInfo, extension: extension };
    saveErrorLog("uploadImage", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
  }
  return output;
};

// async function getTransactionFormatForEko(transactions) {
//   let output = {
//     data: null,
//     success: false,
//     message: "Server Error Occurred",
//     status: 501,
//   };
//   try {
//     if (transactions.length > 0) {
//       let transaction_summary = [];
//       for (const transaction of transactions) {
//         let monthYear = transaction.transactionsMonthYear.split("/");
//         if (monthYear.length > 1) {
//           if (!IsNullEmpty(monthYear[0]) && !IsNullEmpty(monthYear[1]) && !isNaN(transaction.primaryCount) && !isNaN(transaction.primary)) {
//             const summary = {
//               month: parseInt(monthYear[0]),
//               year: parseInt(monthYear[1]),
//               count: IsNullEmpty(transaction.primaryCount) ? 0 : transaction.primaryCount,
//               volume: IsNullEmpty(transaction.primary) ? 0 : transaction.primary,
//               max_wallet_balance: 0,
//               min_wallet_balance: 0,
//               verticals: [
//                 {
//                   volume: IsNullEmpty(transaction.primary) ? 0 : transaction.primary,
//                   service_name: transaction.transactionsType,
//                   count: IsNullEmpty(transaction.primaryCount) ? 0 : transaction.primaryCount
//                 }
//               ],
//               total_days: null,
//               active_days: null
//             };
//             transaction_summary.push(summary);
//           }
//         }
//       }
//       if (transaction_summary.length > 0) {
//         output = {
//           data: transaction_summary,
//           success: true,
//           message: "Generated Successfully",
//           status: 200,
//         };
//       } else {
//         output.data = "Please provide valid transactions";
//       }
//     } else {
//       output.message = "Please provide transactions";
//     }
//   } catch (ex) {
//     logger.logs("error", ex);
//     saveErrorLog("getTransactionFormatForEko", apiPath, transactions, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
//   }
//   return output;
// };

async function setDigiLockerFieldsInUserInfo(userId, digilocker) {
  let output = {
    success: false,
    message: "",
    status: 501,
  };
  try {
    logger.logs("userId", userId);
    const userInfo = await UserInfo.findById(userId);
    if (userInfo) {
      userInfo.panDocVerification = {
        isNumberVerified: true,
        isDocVerified: true,
        type: "digilocker",
        isDocMatch: true,
        createdOn: Date.now(),
        details: {
          name: digilocker.document_details.pan.name,
          docNo: digilocker.document_details.pan.docNo,
          dob: digilocker.document_details.pan.dob,
          document_type: digilocker.document_details.pan.document_type,
          id_proof_type: digilocker.document_details.pan.id_proof_type,
          typeOfHolder: digilocker.document_details.pan.typeOfHolder,
          isIndividual: digilocker.document_details.pan.isIndividual, //Optional
          isValid: digilocker.document_details.pan.isValid,  //Optional
          firstName: digilocker.document_details.pan.firstName, //Optional
          middleName: digilocker.document_details.pan.middleName, //Optional
          lastName: digilocker.document_details.pan.lastName, //Optional
          title: digilocker.document_details.pan.title, //Optional
          panStatus: digilocker.document_details.pan.panStatus, //Optional
          panStatusCode: digilocker.document_details.pan.panStatusCode, //Optional
          aadhaarSeedingStatus: digilocker.document_details.pan.aadhaarSeedingStatus, //Optional
          aadhaarSeedingStatusCode: digilocker.document_details.pan.aadhaarSeedingStatusCode, //Optional
          lastUpdatedOn: digilocker.document_details.pan.lastUpdatedOn,
          photoPath: null,
        },
        ocr: {},
        verificationResponse: {},
        errorDetails: {}
      };
      let photoPath = null;

      if (digilocker.document_details.aadhaar.image) {

        const staticPath = "ProofImages/" + userInfo._id;

        photoPath = staticPath + "/PoaPhoto/photo.jpg";
        let uploadedFile = await uploadToS3(photoPath, digilocker.document_details.aadhaar.image)
      }

      userInfo.poaDetails = [{
        poaNo: digilocker.document_details.aadhaar.docNo,
        poaType: "Aadhaar Card",
        poaVerificationDocType: "Aadhaar Card",
        isMasked: true,
        docDetails: [],
        insertedOn: Date.now(),
        docVerification: {
          isNumberVerified: true,
          isDocVerified: true,
          type: "digilocker",
          verificationType: "AADHAAR",
          isDocMatch: true,
          createdOn: Date.now(),
          dl: {},
          voterId: {},
          passport: {},
          uid: {
            details: {
              docNo: digilocker.document_details.aadhaar.docNo,
              document_type: digilocker.document_details.aadhaar.document_type,
              id_proof_type: digilocker.document_details.aadhaar.id_proof_type,
              gender: digilocker.document_details.aadhaar.gender,
              name: digilocker.document_details.aadhaar.name,
              dob: digilocker.document_details.aadhaar.dob,
              current_address: digilocker.document_details.aadhaar.current_address,
              permanent_address: digilocker.document_details.aadhaar.permanent_address,
              current_address_details: {
                address: digilocker.document_details.aadhaar.current_address_details.address,
                locality_or_post_office: digilocker.document_details.aadhaar.current_address_details.locality_or_post_office,
                district_or_city: digilocker.document_details.aadhaar.current_address_details.district_or_city,
                state: digilocker.document_details.aadhaar.current_address_details.state,
                pincode: digilocker.document_details.aadhaar.current_address_details.pincode
              },
              permanent_address_details: {
                address: digilocker.document_details.aadhaar.permanent_address_details.address,
                locality_or_post_office: digilocker.document_details.aadhaar.permanent_address_details.locality_or_post_office,
                district_or_city: digilocker.document_details.aadhaar.permanent_address_details.district_or_city,
                state: digilocker.document_details.aadhaar.permanent_address_details.state,
                pincode: digilocker.document_details.aadhaar.permanent_address_details.pincode
              },
              photoPath: photoPath,
            },
            ocr: {}
          }
        },
        errorDetails: {}
      }];

      userInfo.digilocker = {
        execution_request_id: digilocker.execution_request_id,
        created_at: new Date(digilocker.created_at),
        updated_at: new Date(digilocker.updated_at),
        reference_id: digilocker.reference_id,
        status: digilocker.status,
        processing_done: digilocker.processing_done,
        face_match_status: digilocker.face_match_status,
        validation_result: {},
        face_match_obj_type: digilocker.face_match_obj_type,
        obj_analysis_status: digilocker.obj_analysis_status
      }


      if (digilocker.document_details.pan.docPath) {
        const extension = getExtension(digilocker.document_details.pan.docPath);
        await uploadImage(digilocker.document_details.pan.docNo, "pan_front", digilocker.document_details.pan.docPath, userInfo, extension);
      }

      if (digilocker.document_details.aadhaar.docPath) {
        const extension = getExtension(digilocker.document_details.aadhaar.docPath);
        await uploadImage(digilocker.document_details.aadhaar.docNo, "aadhaar_front", digilocker.document_details.aadhaar.docPath, userInfo, extension);
      }

      let resultClientCode = await getClientCode(userInfo);

      if (resultClientCode.success == true) {
        userInfo.userDetails.clientCode = resultClientCode.data;
        userInfo.userDetails.kycStatus = "Approved";
        userInfo.userDetails.clientCodeGenDate = Date.now();
        userInfo.userDetails.kycRemarks = "DigiLocker Verified. Auto Approved By System";
        userInfo.userDetails.clientCodeGenBy = "System";
        userInfo.modifiedOn = Date.now();
        userInfo.userDetails.kycApprovedOn = Date.now();
        await userInfo.save();
        output = {
          success: true,
          message: "saved successfully",
          status: 200,
        };
      } else output.message = resultClientCode.message;
    } else output.message = "Issue occurred while saving DigiLocker Details";
  } catch (ex) {
    logger.logs("error", ex);
    saveErrorLog("setDigiLockerFieldsInUserInfo", apiPath, { userId, digilocker }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    return {
      success: false,
      message: ex.message,
      status: 501,
    };
  }
  return output;
};
module.exports.setDigiLockerFieldsInUserInfo = setDigiLockerFieldsInUserInfo;

async function UpdatePartnerLoan(partnerLoanId, model) {
  let output = {
    success: false,
    message: "",
    status: 200,
  };
  try {
    const partnerLoan = await PartnerLoans.findById(partnerLoanId);
    await backupPartnerLoans(partnerLoan._id);


    if (model.trxnId && model.trxnId != null && partnerLoan.trxnId != model.trxnId) {
      partnerLoan.trxnId = model.trxnId;
      isPartnerLoanUpdated = true;
    }

    let state = !IsNullEmpty(model.state) ? model.state.toUpperCase() : "";
    let city = !IsNullEmpty(model.city) ? model.city.toUpperCase() : "";

    if (!IsNullEmpty(model.pincode)) {
      const stateCityMaster = await getCityState(partnerLoan.partnerId, partnerLoan.merchantCode, model.state, model.city, model.pincode);
      state = stateCityMaster.state;
      city = stateCityMaster.city;
    }

    partnerLoan.loanRequest = {
      firstName: model.firstName,
      middleName: null,
      lastName: model.lastName,
      mobileNo: model.mobileNo,
      emailId: model.emailID,
      aadhaarNo: model.aadhaarNo,
      city: city,
      street: model.street,
      state: state,
      pincode: model.pincode,
      merchantType: model.agent_type,
      loanAmount: model.loan_amount,
      onboardedDate: model.onboardedDate,
      isWeekendLoan: model.isWeekendLoan,
      isActive: true,
      insertedOn: Date.now(),
      modifiedOn: null,
      deactivatedOn: null,
    };
    partnerLoan.panNo = model.panNo.toUpperCase();
    partnerLoan.ipAddress = model.ipAddress;
    partnerLoan.virtualAccountNo = model.virtualAccountNo;

    if (!IsNullEmpty(model.personalDetails)) {
      let personalDetails = {
        name: model.personalDetails.name,
        gender: model.personalDetails.gender,
        qualification: model.personalDetails.qualification,
        dob: model.personalDetails.dob,
        maritalStatus: model.personalDetails.maritalStatus,
        mobile: model.personalDetails.mobile,
        alternateMobile: model.personalDetails.alternate_mobile,
        homeOwnershipType: model.personalDetails.home_ownership_type,
        currentAdd: model.personalDetails.current_address,
        city: model.personalDetails.city,
        state: model.personalDetails.state,
        pincode: model.personalDetails.pincode,
        marital_status: model.personalDetails.marital_status,
        phoneConnectionType: model.personalDetails.phoneConnectionType,
        whatsAppInMobile: model.personalDetails.whatsAppInMobile,
        children: model.personalDetails.children,
        ownHome: model.personalDetails.ownHome,
        vehicle: model.personalDetails.vehicle,
        avgMonthlyOutgoings: model.personalDetails.avgMonthlyOutgoings,
        avgMonthlyIncome: model.personalDetails.avgMonthlyIncome,
        jobType: model.personalDetails.jobType,
        whiteGoodsIn24Months: model.personalDetails.whiteGoodsIn24Months,
        educationalDegree: model.personalDetails.educationalDegree
      };

      partnerLoan.personalDetails = personalDetails;
    }

    if (!IsNullEmpty(model.shopDetails)) {
      let shopDetails = {
        name: !IsNullEmpty(model.shopDetails.shopName)
          ? model.shopDetails.shopName
          : model.shopDetails.shop_name,
        type: !IsNullEmpty(model.shopDetails.shopType)
          ? model.shopDetails.shopType
          : model.shopDetails.shop_type,
        add: !IsNullEmpty(model.shopDetails.shopAddress)
          ? model.shopDetails.shopAddress
          : model.shopDetails.shop_address,
        city: model.shopDetails.city,
        state: model.shopDetails.state,
        pincode: model.shopDetails.pincode,
        location: !IsNullEmpty(model.shopDetails.shopLocation)
          ? model.shopDetails.shopLocation
          : model.shopDetails.shop_location,
        contactNo: model.shopDetails.shop_contact_number,
        ownershipType: model.shopDetails.shop_ownership_type,
        companyType: model.shopDetails.company_type,
        shopVinatge: model.shopDetails.shop_vintage,
        businessVintage: model.shopDetails.business_vintage,
      };

      partnerLoan.shopDetails = shopDetails;
    }

    if (!IsNullEmpty(model.accountDetails)) {
      let accountDetails = {
        mobile: model.accountDetails.mobile,
        panNo: model.accountDetails.panNumber,
        merchantCode: model.accountDetails.merchantCode,
      };
      partnerLoan.accoutDetails = accountDetails;
    }

    if (!IsNullEmpty(model.bankDetails)) {
      partnerLoan.bankDetails = model.bankDetails;
    }
    await partnerLoan.save();

    output = {
      success: true,
      message: "Updated Successfully",
      status: 200,
    };
  } catch (ex) {
    logger.logs("error", ex);
    saveErrorLog("updatePartnerLoan", apiPath, { partnerLoanId, model }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
  }
  return output;
};

// ------------------------------------------------------------------------------------------------------------------------------------------