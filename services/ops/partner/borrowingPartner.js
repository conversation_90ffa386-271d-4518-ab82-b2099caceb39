//Libraries
// ------------------------------------------------------------------------------------------------------------------------------------------//
const mongoose = require("mongoose");
const debug = require("debug")("app:borrowingPartner");
const IsNullEmpty = require("isnullemptyorwhitespace");
const { dateAdd } = require("dateadd");
const DateDiff = require("date-diff");
const config = require("config");
const fetch = require("node-fetch");
const apiPath = __dirname;
// ------------------------------------------------------------------------------------------------------------------------------------------//

//Models
// ------------------------------------------------------------------------------------------------------------------------------------------//
const { LoanInfo, InvoiceRequests } = require("../../../models/ops/loan/loan");
const { PartnerMaster, PreApprovals, PartnerLoans, PartnerAllocationRejection, ProductMaster, ProductPartnerConfig } = require("../../../models/ops/partner/partner");
const { MessageQueue } = require("../../../models/ops/registration/verification");
// ------------------------------------------------------------------------------------------------------------------------------------------//

//External Methods
// ------------------------------------------------------------------------------------------------------------------------------------------//
const { getDMYFormattedDate, getProductType, stringToBase64, pushToNotifications, round10, buildMobileNumberObject, getMobileNumberArray } = require("../common/common");
const { saveErrorLog } = require("../common/masters");
const loanService = require("../loan/loan");
const { UserInfo, UserPartnerInfo } = require("../../../models/ops/registration/users");
const { verifyCustomOtpForMerchant } = require("../../../services/ops/registration/verificationService");
const installmentService = require("../../../services/ops/loan/installments");
const Logger = require("../../../middleware/logger");
const logger = new Logger();


// ------------------------------------------------------------------------------------------------------------------------------------------//

//Internal WorkFlow Methods
// ------------------------------------------------------------------------------------------------------------------------------------------//
module.exports.getCreditLoanDetails = async function (model) {
    //logger.logs("model", model);
    let output = {
        data: null,
        success: false,
        message: "",
        status: 501,
    };
    try {
        if (model.partnerId) {
            const today = new Date();
            let query = { partnerId: mongoose.Types.ObjectId(model.partnerId), isParentLoan: true, status: "Onboarding Completed" };

            if (model.merchantCode) {
                query.merchantCode = model.merchantCode;
            }


            let loanInfo = await LoanInfo.aggregate([
                {
                    $match: query,
                },
                {
                    $lookup: {
                        from: "PreApprovals",
                        localField: "productId",
                        foreignField: "productId",
                        as: "preApprovals",
                    },
                },
                { $unwind: "$preApprovals" },
                {
                    $match: { "preApprovals.expiredOn": { $gte: today } },
                },
                {
                    $project: {
                        _id: 1,
                        loanNo: 1,
                        productId: 1,
                        merchantCode: 1,
                        startDate: "$preApprovals.offerSentOn",
                        endDate: "$preApprovals.expiredOn",
                    }
                }
            ]);

            let records = [];
            if (loanInfo && loanInfo.length > 0) {
                for (const loan of loanInfo) {
                    const record = {
                        merchantCode: loan.merchantCode,
                        approvedCreditLimit: null,
                        availableCreditLimit: null,
                        effectiveStartDate: null,
                        effectiveEndDate: null,
                        transactions: []
                    };
                    const creditLineBalance = await loanService.getCreditLineBalance(loan.productId);
                    if (creditLineBalance.success) {
                        record["approvedCreditLimit"] = creditLineBalance.approvedCreditLimit;
                        record["availableCreditLimit"] = creditLineBalance.availableCreditLimit;
                    }
                    record["effectiveStartDate"] = getDMYFormattedDate(loan.startDate, "-");
                    record["effectiveEndDate"] = getDMYFormattedDate(loan.endDate, "-");

                    //For Child Loans
                    const childLoans = await LoanInfo.find({ productId: loan.productId, isParentLoan: false, status: { $in: ["Close Prepaid", "Close Repaid", "Loan In Progress", "Written-Off", "WrittenOff Paid"] } }).sort({ "disbursalDetails.disbursedOn": -1 });

                    const recordChildLoans = [];

                    const loanIds = childLoans.map(x => x._id);
                    const loanNos = childLoans.map(x => x.loanNo);
                    const loanInstallments = await installmentService.getMultipleLoansInstallments(loanIds, loanNos);
                    if (!loanInstallments.success) {
                        output.message = loanInstallments.message;
                        return output;
                    }

                    for (const childLoan of childLoans) {
                        const recordChildLoan = {
                            transactionRef: childLoan.loanNo,
                            transactionDate: getDMYFormattedDate(childLoan.disbursalDetails.disbursedOn, "-"),
                            transactionAmount: childLoan.amount,
                            interestAmount: null,
                            repaymentDetails: [],
                            outstandingAmount: null,
                            paidAmount: null,
                            status: (childLoan.status == "Close Prepaid" || childLoan.status == "Close Repaid") ? "Paid" : "Unpaid",
                            paymentLink: null,
                            orderId: null
                        };

                        const installmentDetails = loanInstallments.data.filter(x => x.loanId.toString() == childLoan._id.toString());
                        if (installmentDetails) {
                            let interestAmount = 0;
                            let outstandingAmount = 0;
                            let paidAmount = 0;
                            let repaymentDetails = [];
                            for (const emi of installmentDetails) {
                                interestAmount = interestAmount + emi.installmentInterest;
                                if (emi.status == "Pending") {
                                    outstandingAmount = outstandingAmount + emi.installmentAmount;
                                } else {
                                    paidAmount = outstandingAmount + emi.installmentAmount;
                                }

                                const repaymentDetail = {
                                    amount: emi.installmentAmount,
                                    date: getDMYFormattedDate(emi.actualDueDate, "-"),
                                    paidOn: emi.transactionDate ? getDMYFormattedDate(emi.transactionDate, "-") : null,
                                };
                                repaymentDetails.push(repaymentDetail);
                            }
                            recordChildLoan.interestAmount = interestAmount;
                            recordChildLoan.outstandingAmount = outstandingAmount;
                            recordChildLoan.paidAmount = paidAmount;
                            recordChildLoan.repaymentDetails = repaymentDetails;
                        }

                        //For Order Id
                        if (childLoan.invoiceRequestId) {
                            const invoiceRequest = await InvoiceRequests.findById(childLoan.invoiceRequestId);
                            if (invoiceRequest) {
                                recordChildLoan.orderId = invoiceRequest.invoiceId;
                            }
                        }

                        //For Payment Link
                        if (model.isPaymentLinkRequired) {
                            recordChildLoan.paymentLink = {
                                link: "https://fndf.in",
                                expiryDate: getDMYFormattedDate(Date.now(), "-")
                            };
                        }

                        recordChildLoans.push(recordChildLoan);
                    }
                    record.transactions = recordChildLoans;
                    records.push(record);
                }
                output = {
                    data: records,
                    success: true,
                    message: "Fetched Successfully",
                    status: 200,
                };
            } else {
                output.message = "No Record Found";
            }
        } else {
            output.message = "Invalid Access";
        }
    } catch (ex) {
        //logger.logs(ex);
        saveErrorLog("getCreditLoanDetails", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};

async function callBorrowingPartnerAPI(model) {
    let output = {
        success: false,
        message: "",
        status: 501,
    };
    let isNewRequest = false;
    try {
        if (IsNullEmpty(model.source)) {
            output.message = "source is required";
        } else if (IsNullEmpty(model.loanId)) {
            output.message = "loanId is required";
        } else if (IsNullEmpty(model.apiType)) {
            output.message = "apiType is required";
        } else if (model.apiType == "sendNotification") {
            if (IsNullEmpty(model.type)) output.message = "type is mandatory";
        } else if (model.apiType == "loanOffer") {
            if (IsNullEmpty(model.isAccepted)) output.message = "isAccepted is mandatory";
            else if (IsNullEmpty(model.step)) output.message = "step is mandatory";
            else if (model.isAccepted == false && IsNullEmpty(model.reason)) output.message = "reason is mandatory";
            else if (IsNullEmpty(model.description)) output.message = "description is mandatory";
        } else if (model.apiType == "loanDisbursal") {
            if (IsNullEmpty(model.bankReferenceNo)) output.message = "bankReferenceNo is mandatory";
        } else if (model.apiType == "loanDisbursalFailed") {
            if (IsNullEmpty(model.errorCode)) output.message = "errorCode is mandatory";
            else if (IsNullEmpty(model.errorDesc)) output.message = "errorDesc is mandatory";
        }

        if (model.isRetryOffline && model.source != "schedular") {
            isNewRequest = true;
            model.success = false;
            model.errorMsg = null;
            model.partnerType = "Borrowing Partner";
            await saveMessageQueue(model, isNewRequest);
        }

        //Check for existing API
        if (output.message == "" && model.source != "schedular") {
            const messageQueue = await MessageQueue.countDocuments({ loanId: mongoose.Types.ObjectId(model.loanId), partnerType: "Borrowing Partner", isActive: true });

            //logger.logs("messageQueue", messageQueue);
            if (messageQueue > 0) {
                output.message = "Existing API Execution Pending";
            }
        }


        if (output.message == "") {
            switch (model.apiType) {
                case "sendNotification":
                    output = await sendNotification(model);
                    break;
                case "loanOffer":
                    output = await loanOffer(model);
                    break;
                case "loanDisbursal":
                    output = await loanDisbursal(model);
                    break;
                case "loanDisbursalFailed":
                    output = await loanDisbursal(model);
                    break;
            }
        }
    } catch (ex) {
        saveErrorLog("callBorrowingPartnerAPI", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    model.errorMsg = output.success == true ? "" : output.message;
    model.success = output.success;
    model.partnerType = "Borrowing Partner";

    if (model.isRetryOffline == false && model.source != "schedular")
        isNewRequest = true;
    else
        isNewRequest = false;

    saveMessageQueue(model, isNewRequest);

    return output;
};
module.exports.callBorrowingPartnerAPI = callBorrowingPartnerAPI;

module.exports.requestBorrowingPartnerMessageQueue = async function (model) {
    let output = {
        success: false,
        message: "",
        status: 501,
    };
    try {
        let query = { isActive: true, partnerType: "Borrowing Partner", noOfAttempts: { $lt: 6 } };

        if (!IsNullEmpty(model.subType)) {
            query["subType"] = model.subType;
        }

        if (!IsNullEmpty(model.loanId)) {
            query["loanId"] = mongoose.Types.ObjectId(model.loanId);
        }

        if (!IsNullEmpty(model.id)) {
            query["_id"] = mongoose.Types.ObjectId(model.id);
        }

        const messageQueue = await MessageQueue.find(query).sort({ insertedOn: 1 });

        if (!IsNullEmpty(messageQueue)) {
            for (messsage of messageQueue) {
                const model = messsage.requestModel;
                model.source = "schedular";
                model.partnerType = "Borrowing Partner";
                model.subType = messsage.subType;
                //console.log("model", model);
                const res = await callBorrowingPartnerAPI(model);
            }
        }
        output = {
            success: true,
            message: "Executed Successfully",
            status: 200,
        };
    } catch (ex) {
        //logger.logs(ex);
        saveErrorLog("requestMessageQueue", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};

async function saveMessageQueue(model, isNewRequest) {
    try {
        if (model.success || (!model.success && model.isRetryOffline)) {
            let messageQueue = null;
            if (model.type && model.type != null && model.type == 14 && isNewRequest == true) {
                messageQueue = new MessageQueue({
                    loanId: mongoose.Types.ObjectId(model.loanId),
                    source: model.source,
                    module: model.module,
                    apiType: model.apiType,
                    type: "API",
                    partnerType: model.partnerType,
                    emiNumber: (model.apiType == "loanRepayment") ? model.emiNumber : 0,
                    subType: model.apiType,
                    noOfAttempts: 0,
                    requestModel: model,
                    errorMsg: !(model.success) ? model.errorMsg : "",
                    isActive: !model.success,
                    insertedOn: Date.now(),
                    updateOn: Date.now()
                });
            } else {
                if (model.type == 14) {
                    messageQueue = await MessageQueue.findOne({
                        loanId: mongoose.Types.ObjectId(model.loanId), type: "API", module: model.module, "requestModel.type": model.type, isActive: true, "requestModel.rejectReason": model.rejectReason
                    });
                } else {
                    messageQueue = await MessageQueue.findOne({ loanId: mongoose.Types.ObjectId(model.loanId), type: "API", module: model.module, "requestModel.type": model.type, isActive: true });
                }

                if (IsNullEmpty(messageQueue)) {
                    messageQueue = new MessageQueue({
                        loanId: mongoose.Types.ObjectId(model.loanId),
                        source: model.source,
                        module: model.module,
                        apiType: model.apiType,
                        type: "API",
                        partnerType: model.partnerType,
                        emiNumber: (model.apiType == "loanRepayment") ? model.emiNumber : 0,
                        subType: model.apiType,
                        noOfAttempts: 0,
                        requestModel: model,
                        errorMsg: !(model.success) ? model.errorMsg : "",
                        isActive: !model.success,
                        insertedOn: Date.now(),
                        updateOn: Date.now()
                    });
                } else {
                    messageQueue.noOfAttempts = messageQueue.noOfAttempts + 1;
                    messageQueue.requestModel = model;
                    messageQueue.errorMsg = !(model.success) ? model.errorMsg : "";
                    messageQueue.isActive = !model.success;
                    messageQueue.updateOn = Date.now();
                }
            }
            await messageQueue.save();

            if (!model.success && model.isRetryOffline) {
                const lenderDetails = await PartnerMaster.findById(model.lenderId);
                if (lenderDetails) {
                    const subject = model.apiType + " request failed for Lending Partner " + lenderDetails.displayName;
                    const message = model.apiType + " request failed " + " <br/> " + "Error Msg : " + model.errorMsg;
                    postdisbursal.sendMailForFailure(subject, message);
                }
            }
        } else {
            const loanInfo = await LoanInfo.findById(model.loanId);

            const partnerAllocationRejection = new PartnerAllocationRejection({
                partnerId: mongoose.Types.ObjectId(model.lenderId),
                userId: mongoose.Types.ObjectId(loanInfo.userId),
                loanId: mongoose.Types.ObjectId(model.loanId),
                apiType: model.apiType,
                errorMsg: model.errorMsg,
                insertedOn: Date.now()
            });

            await partnerAllocationRejection.save();
        }
    }
    catch (ex) {
        //logger.logs(ex);
        saveErrorLog("saveMessageQueue", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }
};

async function sendNotification(model) {
    logger.logs("model", model);
    //loanId, type, reason = "", description = "", bankReferenceNo = "", reviewLink = ""
    let output = {
        data: null,
        success: false,
        message: "Some Error Occurred",
        status: 501,
    };
    try {
        logger.logs("inside sendNotification");

        if ((!IsNullEmpty(model.loanId), !IsNullEmpty(model.type))) {
            const loanInfo = await LoanInfo.findById(model.loanId);
            logger.logs("loanInfo.productType", loanInfo.productType);
            const productType = await getProductType(loanInfo.productType);
            logger.logs("productType", productType);
            if (!IsNullEmpty(loanInfo)) {
                const partnerMaster = await PartnerMaster.findById(loanInfo.partnerId);

                if (!IsNullEmpty(partnerMaster)) {
                    const today = new Date();

                    const preApproval = await PreApprovals.findOne({
                        partnerId: mongoose.Types.ObjectId(loanInfo.partnerId),
                        merchantCode: loanInfo.merchantCode,
                        productType: loanInfo.productType.split("_")[0],
                        $or: [{ trxnId: loanInfo.trxnId }, { productId: loanInfo.trxnId }, { productId: loanInfo.productId }]
                    }).sort({ insertedOn: -1 });

                    // let preApproval = await PreApprovals.findOne({ partnerId: mongoose.Types.ObjectId(loanInfo.partnerId), merchantCode: loanInfo.merchantCode, productType: productType, productId: loanInfo.trxnId }).sort({ insertedOn: -1 });

                    // if (IsNullEmpty(preApproval)) {
                    //     preApproval = await PreApprovals.findOne({ partnerId: mongoose.Types.ObjectId(loanInfo.partnerId), merchantCode: loanInfo.merchantCode, productType: productType, trxnId: loanInfo.trxnId }).sort({ insertedOn: -1 });
                    // }

                    let requestNotification = {
                        type: model.type,
                        user_id: loanInfo.merchantCode,
                        loan_uid: loanInfo.productId,
                        tenure: loanInfo.tenure,
                        loanNo: loanInfo.loanNo
                    };

                    if (productType === "LC") {
                        if (loanInfo.isParentLoan == true) {
                            requestNotification.amount_offered = loanInfo.loanAmount;
                        }
                        requestNotification.interest_rate_yearly = loanInfo.interestRate;
                    } else {
                        requestNotification.trxnId = loanInfo.trxnId;
                    }

                    let totalCharges = 0;
                    let processingChargeAmt = 0;

                    if (loanInfo && loanInfo.disbursalDetails && loanInfo.disbursalDetails.disbursalAmount && loanInfo.disbursalDetails.disbursalAmount != null) {
                        totalCharges = loanInfo.disbursalDetails.custTotalChargesRecovered;
                        processingChargeAmt = loanInfo.disbursalDetails.custProcessingFee;
                    } else {
                        const charges = await loanService.getLoanCharges(loanInfo.amount, loanInfo._id, preApproval._id, model.type == 5 ? true : false);

                        if (charges.success) {
                            totalCharges = charges.totalCharges;
                            processingChargeAmt = charges.processingChargeAmt;
                        } else {
                            output.message = charges.message;
                            return output;
                        }
                    }

                    if (model.type == 5 || model.type == 13) {
                        let schedule = await loanService.emiSchedule(loanInfo._id, today, false);
                        requestNotification.loan_amount = loanInfo.amount;
                        requestNotification.interest_rate_yearly = loanInfo.interestRate;

                        if (!IsNullEmpty(schedule.data)) {
                            requestNotification.emi_amount_interest = schedule.data[0].interestAmount;
                            requestNotification.emi_amount_principle = schedule.data[0].principalRecovered;
                            requestNotification.emi_amount = schedule.data[0].rePaymentAmount;
                            requestNotification.emi_counts = schedule.data.length;
                            requestNotification.instalment_due_date = schedule.data.length == 1 ? getYMDFormattedDate(schedule.data[0].repaymentDate, "-") : null;
                        }

                        if (model.type == 5) {
                            const disbursedAmount = loanInfo.amount - totalCharges;

                            requestNotification.bankReferenceNo = model.bankReferenceNo;

                            if (!IsNullEmpty(schedule.data)) {
                                requestNotification.amount_disbursed = loanInfo.productType == "DL_30" ? (disbursedAmount - schedule.data[0].rePaymentAmount) : disbursedAmount;
                            }
                        }

                        if (productType == "LC" && loanInfo.isParentLoan == false) {
                            requestNotification.order_id = null;
                            if (!IsNullEmpty(loanInfo.invoiceRequestId)) {
                                const invoiceRequest = await InvoiceRequests.findById(loanInfo.invoiceRequestId);
                                if (!IsNullEmpty(invoiceRequest)) {
                                    requestNotification.order_id = invoiceRequest.invoiceId;
                                }
                            }
                        }
                    } else if (model.type == 3) {
                        //admin rejection remark and description
                        requestNotification.processing_fee = processingChargeAmt;
                        requestNotification.totalCharges = totalCharges;
                        requestNotification.amount_offered = loanInfo.amount;
                        requestNotification.reason = model.reason;
                        requestNotification.description = model.description;
                    } else if (model.type == 9 && model.reviewLink != "") {
                        requestNotification.reviewLink = model.reviewLink;
                    } else if (model.type == 8) {
                        requestNotification.reason = model.reason;
                        requestNotification.description = model.description;
                    } else if (model.type == 14) {
                        requestNotification.rejectCode = model.rejectCode;
                        requestNotification.rejectReason = model.rejectReason;
                    } else if (productType != "LC") {
                        requestNotification.totalCharges = totalCharges;
                        requestNotification.processing_fee = processingChargeAmt;
                        requestNotification.amount_offered = loanInfo.amount;
                    }

                    const requestBody = JSON.stringify(requestNotification);

                    const conf = config.get(model.partnerCode.toLowerCase());

                    let tokenResponse = null;
                    if (conf.isTokenGenration == true) {
                        tokenResponse = await getToken(conf, model);
                        if (!tokenResponse.success) {
                            output.message = tokenResponse.message;
                            return output;
                        }
                    }

                    if (!IsNullEmpty(model.url)) {
                        let headers = JSON.parse(JSON.stringify(conf.headerConstants));
                        if (model.partnerCode.toLowerCase() == "pay") {
                            headers['x-api-token'] = "Basic " + tokenResponse.data.api_token.toString();
                            model.url = model.url + loanInfo.productId;
                        }

                        logger.logs("url", model.url);
                        logger.logs("headers", headers);
                        logger.logs("requestBody", requestBody);

                        const apiResponse = await fetch(model.url, {
                            method: "POST",
                            body: requestBody,
                            headers: headers,
                            timeout: 30000
                        }).then(function (u) {
                            return u.json();
                        }).then(function (json) {
                            return json;
                        }).catch((err) => {
                            logger.logs("err :", err);
                            return { status: false, error: err };
                        });

                        logger.logs("apiResponse", apiResponse);
                        if (!IsNullEmpty(apiResponse) && !IsNullEmpty(apiResponse.status)) {
                            let notifications = {
                                type: model.type,
                                processingFee: IsNullEmpty(requestNotification.processing_fee) ? null : requestNotification.processing_fee,
                                totalCharges: IsNullEmpty(requestNotification.totalCharges) ? null : requestNotification.totalCharges,
                                amountOffered: IsNullEmpty(requestNotification.amount_offered) ? null : requestNotification.amount_offered,
                                tenure: IsNullEmpty(requestNotification.tenure) ? null : requestNotification.tenure,
                                loanAmount: IsNullEmpty(requestNotification.loan_amount) ? null : requestNotification.loan_amount,
                                amountDisbursed: IsNullEmpty(requestNotification.amount_disbursed) ? null : requestNotification.amount_disbursed,
                                emiCounts: IsNullEmpty(requestNotification.emi_counts) ? null : requestNotification.emi_counts,
                                emiAmount: IsNullEmpty(requestNotification.emi_amount) ? null : requestNotification.emi_amount,
                                emiAmountInterest: IsNullEmpty(requestNotification.emi_amount_interest) ? null : requestNotification.emi_amount_interest,
                                emiAmountPrinciple: IsNullEmpty(requestNotification.emi_amount_principle) ? null : requestNotification.emi_amount_principle,
                                yearlyInterestRate: IsNullEmpty(requestNotification.interest_rate_yearly) ? null : requestNotification.interest_rate_yearly,
                                status: IsNullEmpty(apiResponse.status) ? null : apiResponse.status,
                                requestRef: IsNullEmpty(apiResponse.request_ref) ? null : apiResponse.request_ref,
                                err_code: IsNullEmpty(apiResponse.err_code) ? null : apiResponse.err_code,
                                desc: IsNullEmpty(apiResponse.desc) ? null : apiResponse.desc,
                                bankReferenceNo: model.bankReferenceNo,
                                insertedOn: Date.now(),
                            };
                            const partnerLoans = await PartnerLoans.findOne({ productId: loanInfo.productId, trxnId: loanInfo.trxnId });

                            if (!IsNullEmpty(partnerLoans)) {
                                partnerLoans.notifications.push(notifications);
                                await partnerLoans.save();
                            }

                            if (apiResponse.status == "success"
                                || apiResponse.desc == "Application already created with this loan id"
                                || (apiResponse.status != "success" && apiResponse.description && apiResponse.description == "Duplicate loan UID")
                                || (apiResponse.status != "success" && apiResponse.disbursement_status && apiResponse.disbursement_status == "Disbursed")
                                || (apiResponse.status != "success" && apiResponse.desc && apiResponse.desc == "Already Disbursed")) {
                                output.success = true;
                                output.message = "Notification Sent Successfully";
                                output.status = 200;
                                if (model.type == 1) {
                                    getMonthTillDateTransactions(partnerMaster._id, loanInfo.merchantCode, partnerMaster.partnerCode);
                                }
                            } else {
                                output.data = apiResponse;
                                output.message = apiResponse.desc ? apiResponse.desc : (apiResponse.description ? apiResponse.description : "");
                                output.status = 200;
                            }
                        } else {
                            output.success = false;
                            output.message = apiResponse;
                            output.status = 200;
                        }
                    } else {
                        output.message = "URL Path Not Found";
                    }

                } else {
                    output.message = "Partner not found";
                }
            } else {
                output.message = "Loan not found";
            }
        } else {
            output.message = "loanId and type are mandatory fields";
        }
    } catch (ex) {
        //logger.logs("error", ex);
        saveErrorLog("sendNotification", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};

async function loanOffer(model) {
    //step should be either 1 or 2
    //isAccepted is boolean
    //loanId, isAccepted, step, reason, description
    let output = {
        data: null,
        success: false,
        message: "Server Error Occurred",
        status: 501,
    };
    try {
        if (!IsNullEmpty(model.url)) {
            logger.logs("inside loanOffer");
            const loanInfo = await LoanInfo.findById(model.loanId);

            if (!IsNullEmpty(loanInfo)) {
                const partnerLoans = await PartnerLoans.findOne({ productId: loanInfo.productId, trxnId: loanInfo.trxnId });

                if (!IsNullEmpty(partnerLoans)) {
                    //logger.logs("partnerId : ", partnerLoans.partnerId);
                    const partnerMaster = await PartnerMaster.findById(partnerLoans.partnerId);

                    const isClaimed = model.isAccepted == true && model.step == 2 ? true : false;

                    //logger.logs("loanAmount : ", loanInfo.amount);
                    //logger.logs("processingCharge : ", partnerMaster.processingCharge);

                    let chargeInPer = 0;
                    const preApproval = await PreApprovals.findOne({
                        partnerId: mongoose.Types.ObjectId(loanInfo.partnerId),
                        merchantCode: loanInfo.merchantCode,
                        productType: loanInfo.productType.split("_")[0],
                        $or: [{ trxnId: loanInfo.trxnId }, { productId: loanInfo.trxnId }, { productId: loanInfo.productId }]
                    }).sort({ insertedOn: -1 });

                    const charges = await loanService.getLoanCharges(loanInfo.amount, loanInfo._id, preApproval._id, false);

                    if (charges.success) {
                        const today = new Date();
                        const tenure = loanInfo.tenure;
                        const expiryDate = dateAdd("day", 10, today);

                        let totalAmountPayable = 0;
                        let interestPayable = 0;
                        let emiAmount = 0;
                        let totalEmi = 0;
                        let schedule = null;

                        if (!IsNullEmpty(loanInfo.interestRate)) {
                            schedule = await loanService.emiSchedule(loanInfo._id, today, false);

                            if (schedule.success) {
                                let emiSchedule = schedule.data;
                                emiAmount = emiSchedule[0].rePaymentAmount;
                                for (const itemSchedule of emiSchedule) {
                                    totalEmi = totalEmi + 1;
                                    totalAmountPayable = totalAmountPayable + parseFloat(itemSchedule.rePaymentAmount);
                                    interestPayable = interestPayable + parseFloat(itemSchedule.interestAmount);
                                }
                            } else {
                                output.message = "EmiSchedule failed";
                                return output;
                            }
                        }

                        let disbursedAmount = loanInfo.amount - charges.totalCharges;

                        let loanOffer = {
                            step_num: model.step,
                            proceed: model.isAccepted,
                            claimed: isClaimed,
                            reason: model.isAccepted ? null : (model.reason ? model.reason : model.description),
                            description: model.isAccepted ? null : model.description,
                            loanAmount: loanInfo.amount,
                            interestRate: model.isAccepted == true ? loanInfo.interestRate : 0,
                            interestType: "percent",
                            repaymentDays: 1,
                            processingFee: charges.totalCharges,
                            loanType: "Emi",
                            tenure: tenure,
                            autoRepaymentStatus: 1,
                            additionalData: loanInfo.adminRemarks,
                            expiryDate: expiryDate,
                            totalAmountPayable: totalAmountPayable.toFixed(2),
                            interestPayable: interestPayable.toFixed(2),
                            emiAmount: parseFloat(emiAmount).toFixed(2),
                            totalEmi: parseInt(totalEmi),
                            requestedAt: loanInfo.insertedOn,
                            disbursedAmount: disbursedAmount,
                            isActive: true,
                            insertedOn: Date.now(),
                            deactivatedOn: null,
                        };

                        let activeOffers = partnerLoans.loanOffer.filter(
                            (x) => x.isActive == true
                        );

                        if (!IsNullEmpty(activeOffers)) {
                            for (itemOffers of activeOffers) {
                                itemOffers.isActive = false;
                                itemOffers.deactivatedOn = Date.now();
                            }
                        }

                        partnerLoans.loanOffer.push(loanOffer);
                        partnerLoans.save();

                        let loanOfferEko = {
                            product_id: partnerLoans.productId,
                            step_num: loanOffer.step_num,
                            proceed: loanOffer.proceed,
                            claimed: loanOffer.claimed,
                            data: {
                                loanAmount: loanOffer.loanAmount,
                                interestRate: loanOffer.interestRate,
                                interestType: loanOffer.interestType,
                                repaymentDays: loanOffer.repaymentDays,
                                processingFees: loanOffer.processingFee,
                                loanType: loanOffer.loanType,
                                tenure: loanOffer.tenure,
                                autoRepaymentStatus: loanOffer.autoRepaymentStatus,
                                additionalData: loanOffer.additionalData,
                                expiryDate: loanOffer.expiryDate,
                                totalAmountPayable: loanOffer.totalAmountPayable,
                                interestPayable: loanOffer.interestPayable,
                                emiAmount: loanOffer.emiAmount,
                                totalEmi: loanOffer.totalEmi,
                            },
                        };

                        if (partnerMaster.partnerCode.toLowerCase() != "eko") {
                            loanOfferEko.reason = model.isAccepted ? null : model.reason;
                            loanOfferEko.description = model.isAccepted ? null : model.description;
                        }

                        const conf = config.get(model.partnerCode.toLowerCase());

                        let tokenResponse = null;
                        if (conf.isTokenGenration == true) {
                            tokenResponse = await getToken(conf, model);
                            if (!tokenResponse.success) {
                                output.message = tokenResponse.message;
                                return output;
                            }
                        }

                        const _body = JSON.stringify(loanOfferEko);

                        // console.log("model.url", model.url);
                        // console.log("_body", _body);
                        const apiResponse = await fetch(model.url, {
                            method: "post",
                            body: _body,
                            headers: conf.headerConstants,
                            timeout: 30000
                        }).then(function (u) {
                            //console.log("u", u);
                            return u.json();
                        }).then(function (json) {
                            //console.log("json", json);
                            return json;
                        }).catch((err) => {
                            //logger.logs("err :", err);
                            return err;
                        });
                        // console.log("apiResponse", apiResponse);
                        if (apiResponse != null) {
                            if (!IsNullEmpty(apiResponse.status)) {
                                const status = (apiResponse.status.toString().toLowerCase() == "1" || apiResponse.status.toString().toLowerCase() == "true") ? true : false;
                                let loanOfferResponse = {
                                    description: apiResponse.description,
                                    status: apiResponse.status,
                                    isOfferAcceptance: (model.step == 2 && status) ? true : false,
                                    insertedOn: Date.now(),
                                };

                                partnerLoans.loanOfferResponse.push(loanOfferResponse);
                                partnerLoans.save();

                                if (status) {
                                    output = {
                                        data: apiResponse,
                                        success: true,
                                        message: "offer sent successfully",
                                        status: 200,
                                    };
                                } else {
                                    output.message = apiResponse.description;
                                }
                            } else {
                                output = {
                                    data: apiResponse,
                                    success: false,
                                    message: apiResponse.description,
                                    status: 501,
                                };
                            }
                        } else {
                            output = {
                                data: apiResponse,
                                success: false,
                                message: "No Response from Borrowing Partner",
                                status: 501,
                            };
                        }
                    } else output.message = charges.message;
                } else output.message = "Partner Loan not found";
            } else output.message = "Loan not found";
        } else output.message = "URL Path Not Found";
    } catch (ex) {
        logger.logs(ex);
        saveErrorLog("loanOffer", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }
    return output;
};

async function loanDisbursal(model) {
    //loanId, bankReferenceNo
    let output = {
        data: null,
        success: false,
        message: "Server Error Occurred",
        status: 501,
    };
    try {
        logger.logs("calling loan disbursal");

        if (!IsNullEmpty(model.url)) {
            let loanInfo = await LoanInfo.findById(model.loanId);

            if (!IsNullEmpty(loanInfo)) {
                const partnerLoans = await PartnerLoans.findOne({
                    productId: loanInfo.productId,
                    trxnId: loanInfo.trxnId,
                });

                if (!IsNullEmpty(partnerLoans)) {
                    let partnerMaster = await PartnerMaster.findById(
                        partnerLoans.partnerId
                    );

                    if (!IsNullEmpty(partnerMaster)) {
                        let chargeInPer = 0;

                        const preApproval = await PreApprovals.findOne({
                            partnerId: mongoose.Types.ObjectId(loanInfo.partnerId),
                            merchantCode: loanInfo.merchantCode,
                            productType: loanInfo.productType.split("_")[0],
                            $or: [{ trxnId: loanInfo.trxnId }, { productId: loanInfo.trxnId }]
                        }).sort({ insertedOn: -1 });

                        let totalCharges = 0;
                        //let processingChargeAmt = 0;

                        if (loanInfo && loanInfo.disbursalDetails && loanInfo.disbursalDetails.disbursalAmount && loanInfo.disbursalDetails.disbursalAmount != null) {
                            totalCharges = loanInfo.disbursalDetails.custTotalChargesRecovered;
                            //processingChargeAmt = loanInfo.disbursalDetails.custProcessingFee;
                        } else {
                            const charges = await loanService.getLoanCharges(loanInfo.amount, loanInfo._id, preApproval._id, true);

                            if (charges.success) {
                                totalCharges = charges.totalCharges;
                                //processingChargeAmt = charges.processingChargeAmt;
                            } else {
                                output.message = charges.message;
                                return output;
                            }
                        }

                        const disbursedAmount = loanInfo.amount - totalCharges;

                        let disbursal = {
                            source: 5,
                            amount: disbursedAmount,
                            bankReferenceNo: model.bankReferenceNo ? model.bankReferenceNo : null,
                            isDisbursed: true,
                            errorDesc: null,
                            insertedOn: Date.now(),
                        };

                        const today = new Date();

                        const emiDetails = await loanService.emiSchedule(loanInfo._id, today, false);

                        if (!emiDetails.success) {
                            output.message = emiDetails.message;
                            return output;
                        }

                        let totalInterest = 0;
                        let totalRepaymentAmount = 0;

                        for (const emiItem of emiDetails.data) {
                            totalInterest += emiItem.interestAmount;
                            totalRepaymentAmount += emiItem.rePaymentAmount;
                        }

                        let ekoRequest = {
                            productId: loanInfo.productId,
                            source: 5,
                            amount: disbursedAmount,
                            agentCode: loanInfo.merchantCode,
                            isDisbursed: true,
                            errorDesc: null,
                            loanAmount: loanInfo.amount,
                            emiAmount: loanInfo.emiAmount,
                            interestRate: loanInfo.interestRate,
                            totalInterestPayable: round10(totalInterest, -2),
                            totalAmountPayable: round10(totalRepaymentAmount, -2),
                            processingFee: loanInfo.disbursalDetails.processingFee
                        };

                        if (model.apiType == "loanDisbursalFailed") {
                            disbursal.isDisbursed = false;
                            disbursal.errorDesc = model.errorDesc;
                            ekoRequest.isDisbursed = false;
                            ekoRequest.errorDesc = model.errorDesc;
                        }

                        if (model.partnerCode.toLowerCase() == "pworld") {
                            ekoRequest.virtualBankAccountNo = loanInfo.disbursalBankDetails.accountNo;
                        }

                        if (model.partnerCode.toLowerCase() != "eko") {
                            ekoRequest.bankReferenceNo = model.bankReferenceNo;
                        }

                        partnerLoans.disbursal.push(disbursal);
                        await partnerLoans.save();

                        const conf = config.get(model.partnerCode.toLowerCase());
                        ////logger.logs("conf.isTokenGenration", conf.isTokenGenration);
                        let tokenResponse = null;
                        if (conf.isTokenGenration == true) {
                            tokenResponse = await getToken(conf, model);
                            if (!tokenResponse.success) {
                                output.message = tokenResponse.message;
                                return output;
                            }
                        }

                        const _body = JSON.stringify(ekoRequest);
                        // console.log("url", model.url);
                        // console.log("requestBody", _body);
                        // console.log("headers", conf.headerConstants);
                        const apiResponse = await fetch(model.url, {
                            method: "post",
                            body: _body,
                            headers: conf.headerConstants,
                            timeout: 30000
                        }).then(function (u) {
                            return u.json();
                        }).then(function (json) {
                            return json;
                        }).catch((err) => { logger.logs("err :", err); return err });

                        //console.log("loanDisbursal response", apiResponse);
                        if (apiResponse != null) {
                            if (!IsNullEmpty(apiResponse.status)) {
                                let disbursalResponse = {
                                    status: (apiResponse.status.toString().toLowerCase() == "1" || apiResponse.status.toString().toLowerCase() == "true" || apiResponse.description.toString() == "Disbursal already completed for given Product ID.") ? 1 : 0,
                                    discription: apiResponse.description,
                                    insertedOn: Date.now(),
                                };
                                partnerLoans.disbursalResponse.push(disbursalResponse);
                                partnerLoans.save();

                                ////logger.logs("apiResponse", apiResponse);
                                const status = (apiResponse.status.toString().toLowerCase() == "1" || apiResponse.status.toString().toLowerCase() == "true" || (apiResponse.description && apiResponse.description != null && apiResponse.description.toString() == "Disbursal already completed for given Product ID.")) ? true : false;

                                if (status) {
                                    if (model.apiType == "loanDisbursalFailed") {
                                        loanInfo.status = "Loan Is Funding";
                                        loanInfo.flowDetails.push({
                                            Step: "Loan Is Funding",
                                            changedBy: null,
                                            changedByRole: "System",
                                            insertedOn: Date.now()
                                        });

                                        loanInfo.loanContractDetails.isAccepted = false;
                                        loanInfo.loanContractDetails.acceptedOn = null;
                                        await loanInfo.save();
                                    }

                                    output = {
                                        data: apiResponse,
                                        success: true,
                                        message: model.apiType == "loanDisbursalFailed" ? "disbursal failed informed successfully" : "disbursed Successfully",
                                        status: 200,
                                    };
                                } else {
                                    output.message = apiResponse.description;
                                }
                            } else {
                                output = {
                                    data: apiResponse,
                                    success: false,
                                    message: apiResponse.description,
                                    status: 501,
                                };
                            }
                        } else {
                            output = {
                                data: apiResponse,
                                success: false,
                                message: "No response received from Borrwing Partner API",
                                status: 501,
                            };
                        }

                    } else output.message = "Partner not found";
                } else output.message = "Partner Loan not found";
            } else output.message = "Loan not found";
        } else output.message = "URL Path Not Found";
    } catch (ex) {
        logger.logs("ex", ex);
        saveErrorLog("loanDisbursal", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }
    return output;
};

async function getToken(conf, model) {
    let output = {
        data: null,
        success: false,
        message: "Some Error Occurred",
        status: 501,
    };
    try {
        let api_token = null;
        const partnerMaster = await PartnerMaster.findOne({ partnerCode: model.partnerCode });

        if (!IsNullEmpty(partnerMaster)) {
            const currentDate = new Date();
            if (!IsNullEmpty(partnerMaster.token)) {
                let diff = new DateDiff(partnerMaster.tokenExpireAt, currentDate);
                const diffMinutes = diff.minutes();
                if (diffMinutes > 7) {
                    api_token = partnerMaster.token;
                }
            }

            if (IsNullEmpty(api_token)) {
                const auth = "Basic " + stringToBase64(conf.tokenParameters.userName + ":" + conf.tokenParameters.password);

                let apiResponse = await fetch(conf.tokenGenrationUrl, {
                    method: "POST",
                    body: "",
                    headers: {
                        "Content-Type": "application/json",
                        Authorization: auth,
                    },
                }).then(function (u) {
                    return u.json();
                }).then(function (json) {
                    return json;
                }).catch((err) => logger.logs("err :", err));

                if (!IsNullEmpty(apiResponse) && !IsNullEmpty(apiResponse.status) && apiResponse.status.toLowerCase() == "success") {
                    const validtill = new Date(apiResponse.t_valid * 1000);
                    partnerMaster.token = apiResponse.api_token;
                    partnerMaster.tokenExpireAt = validtill;
                    await partnerMaster.save();
                    output = {
                        data: apiResponse,
                        success: true,
                        message: "token generated successfully",
                        status: 200,
                    };
                } else {
                    output.data = apiResponse;
                }
            } else {
                output = {
                    data: {
                        api_token: api_token
                    },
                    success: true,
                    message: "token generated successfully",
                    status: 200,
                };
            }

        } else {
            output.message = "No Partner Found";
        }
    } catch (ex) {
        //console.log(ex);
        output.message = ex.message;
        saveErrorLog("getToken", apiPath, null, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }
    return output;
};
module.exports.getToken = getToken;

async function getMonthTillDateTransactions(partnerId, merchantCode, partnerCode) {
    const model = { partnerId, merchantCode, partnerCode };
    try {
        if (partnerCode.toLowerCase() == "pay" || partnerCode.toLowerCase() == "bnkit" || partnerCode.toLowerCase() == "epay") {
            const body = { partnerId: partnerId, merchantCode: merchantCode };
            const _body = JSON.stringify(body);
            const creditURL = config.get("keys.creditApiUrl") + "/api/partner/getMonthTillDateTransactions";
            const response = await fetch(creditURL, {
                method: "post",
                body: _body,
                headers: { "Content-Type": "application/json" },
                timeout: 30000
            }).then(function (u) {
                return u.json();
            }).then(function (json) {
                return json;
            }).catch((err) => {
                logger.logs("err :", err);
                saveErrorLog("getMonthTillDateTransactions", apiPath, model, err);
                return err;
            });
        }
    } catch (ex) {
        //logger.logs(ex);
        output.message = ex.message;
        saveErrorLog("getMonthTillDateTransactions", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }

};

module.exports.getMonthTillDateTransactions = getMonthTillDateTransactions;

module.exports.confirmDisbursal = async function (model) {
    let output = {
        success: false,
        message: "Some Error Occurred",
        status: 501,
    };
    try {
        const loanInfo = await LoanInfo.findOne({ partnerId: mongoose.Types.ObjectId(model.partnerId), productId: model.productId, merchantCode: model.merchantCode });
        if (loanInfo) {
            loanInfo.partnerDisbursedOn = new Date(loanInfo.disbursalTimestamp);
            await loanInfo.save();
            output = {
                success: true,
                message: "Disbursed Successfully",
                status: 200,
            };
        } else {
            output.message = "No record found"
        }
    } catch (ex) {
        logger.logs("error", ex);
        output.message = ex.message;
        saveErrorLog("confirmDisbursal", apiPath, null, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }
    return output;
};

module.exports.generateOTP = async function (model) {
    let output = {
        success: false,
        message: "Some Error Occurred",
        status: 501,
    };
    try {
        const purpose = "OTP_Registeration";
        let preApproval = null;
        const today = new Date();
        const currentMonth = today.getMonth() + 1;
        const currentYear = today.getFullYear();
        let userId = null;
        let loanId = null;

        const partnerMaster = await PartnerMaster.findById(model.partnerId);

        preApproval = await PreApprovals.findOne({ partnerId: mongoose.Types.ObjectId(model.partnerId), merchantCode: model.req.merchantCode, status: "Sent", trxnId: model.req.offerId, transactionMonth: currentMonth, transactionYear: currentYear });

        if (!preApproval) {
            preApproval = await PreApprovals.findOne({ partnerId: model.partnerId, merchantCode: model.req.merchantCode, status: "Sent", productId: model.req.offerId, transactionMonth: currentMonth, transactionYear: currentYear });
        }

        if (preApproval) {
            let loanInfo = await LoanInfo.findOne({ partnerId: model.partnerId, merchantCode: model.req.merchantCode, trxnId: model.req.offerId });

            if (!loanInfo) {
                loanInfo = await LoanInfo.findOne({ partnerId: model.partnerId, merchantCode: model.req.merchantCode, productId: model.req.offerId });
            }

            if (!loanInfo) {
                const charges = await loanService.getLoanCharges(loanInfo.amount, loanInfo._id, preApproval._id, false);

                if (charges.success) {
                    //For New Loan
                    let userPartnerInfo = await UserPartnerInfo.findOne({ partnerId: model.partnerId, merchantCode: model.req.merchantCode });

                    //Update details in UserInfo
                    if (userPartnerInfo) {
                        const userInfo = await UserInfo.findById(userPartnerInfo.userId);
                        if (userInfo) {
                            //insert mobile no

                            let mobileNumberObj = buildMobileNumberObject(model.req.mobileNo)
                            if (userInfo.userDetails.mobileNo && userInfo.userDetails.mobileNo.length > 0) {
                                let checkExists = await userInfo.userDetails.mobileNo.find((element) => element.mobileNumber === model.req.mobileNo);

                                if (IsNullEmpty(checkExists)) {
                                    userInfo.userDetails.mobileNo.splice(0, 0, mobileNumberObj);
                                } else {
                                    let mobileNumberArray = getMobileNumberArray(userInfo.userDetails.mobileNo)
                                    const index = mobileNumberArray.indexOf(model.req.mobileNo);
                                    if (index > -1) {
                                        userInfo.userDetails.mobileNo.splice(index, 1);
                                    }
                                    userInfo.userDetails.mobileNo.splice(0, 0, mobileNumberObj);
                                }
                            } else {
                                userInfo.userDetails.mobileNo = [mobileNumberObj];
                            }

                            if (userInfo.userDetails.mobileNo) userInfo.userDetails.mobileNo.forEach((mobileNumberElement, index) => mobileNumberElement.isPrimary = index === 0)

                            await userInfo.save();

                            userId = userInfo._id;
                        }
                    } else {
                        const salt = await bycrypt.genSalt(10);
                        const password = await bycrypt.hash(model.req.merchantCode, salt);

                        const userInfo = new UserInfo({
                            panNo: null,
                            userDetails: {
                                userName: model.req.mobileNo,
                                password: password,
                                role: "User",
                                firstName: null,
                                middleName: null,
                                lastName: null,
                                dob: null,
                                employment: null,
                                isEmailValidated: true,
                                isMobileValidated: true,
                                clientCode: null,
                                clientCodeGenDate: null,
                                clientCodeGenBy: null,
                                kycStatus: null,
                            },
                            addressDetails: [],
                            shopDetails: [],
                            questionnairreDetails: {
                                phoneConnectionType: null,
                                whatsApp: null,
                                gender: null,
                                maritalStatus: null,
                                children: null,
                                degree: null,
                                monthlyIncome: null,
                                monthlyOutgoing: null,
                                jobType: null,
                                homeOwnershipType: null,
                                vehicle: null,
                                purchasedInOneYear: null,
                                recommended: null,
                                recommendedBy: null
                            },
                            ipAddress: null,
                            insertedOn: Date.now(),
                            modifiedOn: Date.now()
                        });
                        await userInfo.save();

                        userPartnerInfo = new UserPartnerInfo({
                            userId: mongoose.Types.ObjectId(userInfo._id),
                            partnerId: mongoose.Types.ObjectId(partnerId),
                            merchantCode: model.req.merchantCode,
                            isRetailer: true,
                            isPartnerUser: false,
                            onboardedDate: null,
                            reviewLink: null,
                            incorrectReviewDocument: null,
                            loanHistory: [],
                            insertedOn: Date.now(),
                            updatedOn: Date.now(),
                            bankDetails: [],
                            updateBankLink: null,
                            nsfDays: 0
                        });
                        await userPartnerInfo.save();

                        userId = userInfo._id;
                    }

                    let productType = "TL_90";
                    let interestType = "Compound Interest";
                    let campaignTime = "90 Days";
                    let maturity = 90;
                    let tenure = 90;
                    let interest = null;

                    productType = preApproval.productType.toLowerCase() == "dl" ? "DL_30" : (preApproval.productType.toLowerCase() == "lc" ? "LC_90" : "TL_90");
                    interestType = preApproval.productType.toLowerCase() == "dl" ? "Simple Interest" : preApproval.interestType;
                    loanAmount = preApproval.approvedAmount;

                    //interest = preApproval.annualIntrestRate.replace("%", "").trim();
                    interest = charges.processingChargePer;
                    maturity = preApproval.productType.toLowerCase() == "lc" ? (preApproval.tenureInDays / 30) : preApproval.tenureInDays;
                    tenure = preApproval.productType.toLowerCase() == "lc" ? 90 : preApproval.tenureInDays;

                    switch (maturity) {
                        case 90:
                            campaignTime = "90 Days";
                            break;
                        case 30:
                            campaignTime = "30 Days";
                            break;
                        default:
                            campaignTime = "90 Days";
                            break;
                    }

                    const loanDetails = await loanService.getLoanId();

                    if (loanDetails.success == true) {
                        loanInfo = new LoanInfo({
                            userId: mongoose.Types.ObjectId(userId),
                            loanNo: loanDetails.data,
                            partner: partnerMaster.partnerName,
                            partnerId: mongoose.Types.ObjectId(partnerMaster._id),
                            merchantCode: model.req.merchantCode,
                            merchantType: preApproval.merchantType,
                            trxnId: model.req.offerId,
                            productId: preApproval.productId,
                            productType: productType,
                            amount: loanAmount,
                            purpose: "Business",
                            description: "Working Capital Loan",
                            maturity: maturity,
                            tenure: tenure,
                            interestRate: interest,
                            currency: "Indian Rupee",
                            campaignTime: campaignTime,
                            loanType: "EMI",
                            repaymentFrequency: preApproval.productType.toLowerCase() == "lc" ? "Monthly" : "Daily",
                            additionalDescription: null,
                            isMaturityMonth: false,
                            interestType: interestType,
                            isDraft: true,
                            adminRemarks: null,
                            status: null,
                            isActive: true,
                            insertedOn: Date.now(),
                            updatedOn: Date.now(),
                            ipAddress: null,
                            isParentLoan: false
                        });
                        await loanInfo.save();
                    } else {
                        output.message = loanDetails.message;
                        return output;
                    }

                    loanId = loanInfo._id;
                } else output.message = charges.message;

            } else {
                const userInfo = await UserInfo.findById(loanInfo.userId);
                if (userInfo) {

                    let mobileNumberObj = buildMobileNumberObject(model.req.mobileNo)
                    //insert mobile no
                    if (userInfo.userDetails.mobileNo && userInfo.userDetails.mobileNo.length > 0) {
                        let checkExists = await userInfo.userDetails.mobileNo.find((element) => element.mobileNumber === model.req.mobileNo);

                        if (IsNullEmpty(checkExists)) {
                            userInfo.userDetails.mobileNo.splice(0, 0, mobileNumberObj);
                        } else {

                            let mobileNumberArray = getMobileNumberArray(userInfo.userDetails.mobileNo)
                            const index = userInfo.mobileNumberArray.indexOf(model.req.mobileNo);
                            if (index > -1) {
                                userInfo.userDetails.mobileNo.splice(index, 1);
                            }
                            userInfo.userDetails.mobileNo.splice(0, 0, mobileNumberObj);
                        }
                    } else {
                        userInfo.userDetails.mobileNo = [mobileNumberObj];
                    }
                    if (userInfo.userDetails.mobileNo) userInfo.userDetails.mobileNo.forEach((mobileNumberElement, index) => mobileNumberElement.isPrimary = index === 0)
                    await userInfo.save();

                    userId = userInfo._id;
                } else {
                    output.message = "User Details Not Found";
                    return output;
                }
                loanId = loanInfo._id;
            }

            if (userId && loanId) {
                const reqSendOTP = await loanService.sendOTPForMerchant(userId, loanId, [model.req.mobileNo], purpose, false, "SMS");
                if (reqSendOTP.success) {
                    output = {
                        success: true,
                        message: reqSendOTP.message,
                        status: 200,
                    };
                } else output.message = reqSendOTP.message;
                // const requestOTP = await generateOtpForMerchant(userId, purpose, loanId, model.req.mobileNo);
                // if (requestOTP.success == true) {
                //     let isSentOnMobile = false;
                //     let maskedMobile = await getMaskedMobileNo(mobile);

                //     let smsModel = getSmsModel();
                //     smsModel.To = model.req.mobileNo;
                //     smsModel.TemplateName = purpose; //Change Password
                //     smsModel.VAR1 = partnerMaster.brandName;
                //     smsModel.VAR2 = generateCustomOTP.otp;
                //     if (req.body.messageType === "Registration OTP") {
                //         smsModel.VAR2 = `@${config.get("keys.webSubdomain")}`;
                //         smsModel.VAR3 = `#${generateCustomOTP.otp}`;
                //     } else {
                //         smsModel.VAR3 = `@${config.get("keys.webSubdomain")}`;
                //         smsModel.VAR4 = `#${generateCustomOTP.otp}`;
                //     }

                //     smsModel.Type = "TSMS";
                //     smsModel.From = config.get("keys.from");

                //     //Save in Notification List
                //     const sendSms = await pushToNotifications(null, null, null, null, userId, loanId, "SMS", model.req.mobileNo, purpose, "otp", null, null, smsModel, false, "TSMS");

                //     //Send Notification
                //     if (sendSms.success)
                //         response = await postDisbursal.sendNotifications(sendSms.data._id);

                //     output = {
                //         success: true,
                //         message: "OTP Send Successfully",
                //         status: 200,
                //     };

                // } else {
                //     output.message = requestOTP.message;
                // }
            } else {
                output.message = "Some Error Occurred";
            }
        } else output.message = "Offer does not exists";
    } catch (ex) {
        logger.logs("error :", ex);
        output.message = ex.message;
        saveErrorLog("generateOTP", apiPath, null, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }
    return output;
};

module.exports.validateOTP = async function (model) {
    let output = {
        success: false,
        message: "Some Error Occurred",
        status: 501,
    };
    try {
        const purpose = "OTP_Registeration";
        const loanInfo = await LoanInfo.findOne({ partnerId: model.partnerId, trxnId: model.req.offerId });
        logger.logs("loanInfo", loanInfo._id);
        if (loanInfo) {
            const requestValidateOTP = await verifyCustomOtpForMerchant(loanInfo.userId, model.req.otp, purpose, loanInfo._id, false, "SMS");

            if (requestValidateOTP.success == true) {
                output = {
                    success: true,
                    message: requestValidateOTP.message,
                    status: 200,
                };
            } else {
                output.message = requestValidateOTP.message;
            }
        } else {
            output.message = "No Record Found";
        }
    } catch (ex) {
        logger.logs("error :", ex);
        output.message = ex.message;
        saveErrorLog("validateOTP", apiPath, null, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }
    return output;
};

module.exports.getOfferDetails = async function (model, displayPrefix = true, interestRate = null, isLoanExists = false) {
    let output = {
        data: null,
        success: false,
        message: "Some Error Occurred",
        status: 501,
    };
    try {
        let disDate = new Date(Date.now());

        if (model.req.loanAmount < 0) {
            output.message = "loanAmount should be greater than 0";
            return output;
        } else if (model.req.loanStartDate && model.req.loanStartDate != null) {
            const loanStartDate = model.req.loanStartDate.split("/");

            if (loanStartDate.length != 3 || loanStartDate[0].length != 4 || loanStartDate[1].length != 2 || loanStartDate[2].length != 2) {
                output.message = "Please provide loanStartDate in YYYY/MM/DD Format";
                return output;
            }
        }

        disDate = model.req.loanStartDate ? new Date(model.req.loanStartDate) : new Date(Date.now());

        let preApproval = await PreApprovals.findOne({ partnerId: mongoose.Types.ObjectId(model.partnerId), productId: model.req.productId });

        if (!preApproval && model.req.trxnId)
            preApproval = await PreApprovals.findOne({ partnerId: mongoose.Types.ObjectId(model.partnerId), trxnId: model.req.trxnId });

        if (preApproval) {
            const productMaster = await ProductMaster.findOne({ product: preApproval.productType });
            if (productMaster != null) {
                const productPartnerConfig = await ProductPartnerConfig.findOne({ partnerId: mongoose.Types.ObjectId(preApproval.partnerId), productId: mongoose.Types.ObjectId(productMaster._id) });

                if (productPartnerConfig != null) {
                    if (model.req.tenure && model.req.tenure != null) {
                        if (model.req.tenure < productPartnerConfig.minTenure || model.req.tenure > productPartnerConfig.maxTenure) {
                            output.message = `tenure should be between ${productPartnerConfig.minTenure} and ${productPartnerConfig.maxTenure}`;
                            return output;
                        }
                    }

                    let loanInfo = null;
                    let tenure = null;

                    let amount = model.req.loanAmount ? model.req.loanAmount : preApproval.approvedAmount;
                    let interestType = preApproval.interestType == "Simple Interest" ? "simple" : "compound";

                    tenure = model.req.tenure ? model.req.tenure : preApproval.productType.toLowerCase() == "lc" ? (preApproval.tenureInDays / 30) : preApproval.tenureInDays;

                    let repaymentFrequency = preApproval.productType.toLowerCase() == "lc" ? "Monthly" : "Daily";

                    let productType = preApproval.productType.toLowerCase() == "dl" ? "DL_30" : (preApproval.productType.toLowerCase() == "lc" ? "LC_90" : "TL_90");

                    let roundType = interestType === "compound" ? "upper" : "lower";

                    if (isLoanExists && isLoanExists == true) {
                        loanInfo = await LoanInfo.findOne({ productId: model.req.productId });
                        const _productType = getProductType(loanInfo.productType);

                        if (loanInfo) {
                            amount = model.req.loanAmount ? model.req.loanAmount : loanInfo.amount;
                            interestType = loanInfo.interestType == "Simple Interest" ? "simple" : "compound";
                            tenure = loanInfo.tenure;
                            repaymentFrequency = _productType.toLowerCase() == "lc" ? "Monthly" : "Daily";
                            productType = loanInfo.productType;
                            roundType = interestType === "compound" ? "upper" : "lower";
                        }
                    }

                    const charges = await loanService.getLoanCharges(amount, (loanInfo && loanInfo != null) ? loanInfo._id : null, preApproval._id, false);
                    //interestRate = interestRate != null ? interestRate : preApproval.annualIntrestRate.replace("%", "").trim();
                    if (charges.success) {
                        interestRate = interestRate != null ? interestRate : charges.interestRate;

                        const productMaster = await ProductMaster.findOne({ product: productType });
                        let interestComputation = "Post";

                        if (productMaster) {
                            const productPartnerConfig = await ProductPartnerConfig.findOne({ partnerId: mongoose.Types.ObjectId(model.req.partnerId), productId: mongoose.Types.ObjectId(productMaster._id) });

                            if (productPartnerConfig && productPartnerConfig.interestComputation == "Pre") {
                                interestComputation = "Pre";
                            }
                        }

                        const emis = await loanService.calculateEmi(amount, tenure, interestRate, false, repaymentFrequency, disDate, interestType, false, roundType, productType, false, null, interestComputation);

                        if (emis.length > 0) {
                            const installmentAmount = emis[0].rePaymentAmount;
                            const proessingFee = charges.processingChargeAmt;
                            const gstCharges = charges.gstCharge;
                            const stampingCharges = charges.stampingCharge;

                            const otherCharges = stampingCharges;
                            //const totalCharges = gstCharges + otherCharges;
                            const totalCharges = charges.gstCharge + charges.stampingCharge;
                            const latePaymentCharges = config.get("penaltyCharges");

                            const totalInterestPayable = emis.filter((x) => x.interestAmount != 0).reduce(function (prev, cur) {
                                return prev + cur.interestAmount;
                            }, 0);

                            let disbursalAmount = preApproval.productType.toLowerCase() == "lc" ? amount - proessingFee : amount - (proessingFee + gstCharges + stampingCharges);

                            if (interestComputation == "Pre") {
                                disbursalAmount = round10(disbursalAmount - totalInterestPayable, -2);
                            }

                            let repaymentSchedule = [];

                            for (const emi of emis) {
                                const emiOtherCharges = round10(emi.penalty + emi.moratoriumInterest, -2);
                                const emiTotalAmount = round10(emi.rePaymentAmount + emi.penalty + emi.moratoriumInterest, -2);
                                const closingBalance = round10(emi.principalOutstanding - emi.principalRecovered, -2);
                                const schedule = {
                                    installment: emi.emiNumber,
                                    installmentAmount: displayPrefix ? `INR ${emi.rePaymentAmount}` : `${emi.rePaymentAmount}`,
                                    otherCharges: displayPrefix ? `INR ${emiOtherCharges}` : `${emiOtherCharges}`,
                                    totalAmount: displayPrefix ? `INR ${emiTotalAmount}` : `${emiTotalAmount}`,
                                    dueOn: getDMYFormattedDate(emi.rePaymentActualDate, "/"),
                                    interestComponent: displayPrefix ? `INR ${emi.interestAmount}` : `${emi.interestAmount}`,
                                    principalComponenet: displayPrefix ? `INR ${emi.principalRecovered}` : `${emi.principalRecovered}`,
                                    principalOutstanding: displayPrefix ? `INR ${emi.principalOutstanding}` : `${emi.principalOutstanding}`,
                                    closingBalance: displayPrefix ? `INR ${closingBalance}` : `${closingBalance}`,
                                }
                                repaymentSchedule.push(schedule);
                            }

                            const _data = {
                                installmentAmount: displayPrefix ? `INR ${installmentAmount}` : `${installmentAmount}`,
                                interestRate: interestRate,
                                processingFeeInterestRate: displayPrefix ? preApproval.processingFee : preApproval.processingFee.replace("%", "").trim(),
                                processingFee: displayPrefix ? `INR ${proessingFee}` : `${proessingFee}`,
                                processingFeesWithGst: displayPrefix ? `INR ${gstCharges}` : `${gstCharges}`,
                                totalInterestPayable: displayPrefix ? `INR ${round10(totalInterestPayable, -2)}` : `${round10(totalInterestPayable, -2)}`,
                                otherCharges: displayPrefix ? `INR ${otherCharges}` : `${otherCharges}`,
                                totalCharges: displayPrefix ? `INR ${totalCharges}` : `${totalCharges}`,
                                totalDisbursedAmount: displayPrefix ? `INR ${disbursalAmount}` : `${disbursalAmount}`,
                                latePaymentCharges: displayPrefix ? `INR ${latePaymentCharges} per Emi` : `${latePaymentCharges}`,
                                repaymentSchedule: repaymentSchedule
                            }

                            output = {
                                data: _data,
                                success: true,
                                message: "Fetched Successfully",
                                status: 200,
                            };
                        }
                    } else output.message = charges.message;
                } else output.message = "Product Config Not Found";
            } else output.message = "Product Not Found";
        } else output.message = "Offer Not Found";

    } catch (ex) {
        logger.logs("error :", ex);
        output.message = ex.message;
        saveErrorLog("getOfferDetails", apiPath, null, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }
    return output;
};
//----------------------------------------------------------------------------------------------------------------------------------------//