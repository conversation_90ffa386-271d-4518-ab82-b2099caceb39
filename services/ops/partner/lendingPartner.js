//Libraries
// ------------------------------------------------------------------------------------------------------------------------------------------
const mongoose = require("mongoose");
const debug = require("debug")("app:lendingPartner");
const IsNullEmpty = require("isnullemptyorwhitespace");
const config = require("config");
const fetch = require("node-fetch");
const { dateAdd } = require("dateadd");
const DateDiff = require("date-diff");
const { URLSearchParams } = require('url');
const FormData = require('form-data');
const { date } = require("@hapi/joi");
const fs = require('fs');
const { mode } = require("crypto-js");
const { response } = require("express");
const csvConverter = require('json-2-csv');
const apiPath = __dirname;
// ------------------------------------------------------------------------------------------------------------------------------------------

//Models
// ------------------------------------------------------------------------------------------------------------------------------------------
const { getlenderPartnerCode, getDataForLoanApplication, getDataForLoanSanction, getDataForLoanRepayment, getDataForEmiSchedule, getDataforAddAddress, getDataForLoan, getDataForLoanDocuments, getBorrowingPartnerDetails, getLenderLoans, getDataForLoanCancellation, getDataForBankDetails } = require("../../../models/ops/partner/lendingPartner");
const { PartnerAllocationRejection, LendingPartnerAllocation, PreApprovals, PartnerMaster } = require("../../../models/ops/partner/partner");
const { LoanInfo, DocumentLog } = require("../../../models/ops/loan/loan");
const { MessageQueue } = require("../../../models/ops/registration/verification");
// ------------------------------------------------------------------------------------------------------------------------------------------

//External Methods
// ------------------------------------------------------------------------------------------------------------------------------------------
const { base64_encode, getMDYFormattedDate, sleep,
    getDMYFormattedDate, getName, getAddress,
    getExtension, getYMDFormattedDate, floor10, getProductType, round10, downloadImageFromS3 } = require("../../../services/ops/common/common");
const { saveErrorLog } = require("../../../services/ops/common/masters");
//const { getDisbursalCharges, getLoanCharges } = require("../../../services/ops/loan/loan");
const postdisbursal = require("../../../services/ops/loan/postDisbursal");
const { UserPartnerInfo, UserInfo } = require("../../../models/ops/registration/users");
const Logger = require("../../../middleware/logger");
const logger = new Logger();
// ------------------------------------------------------------------------------------------------------------------------------------------

//Internal WorkFlow Methods
// ------------------------------------------------------------------------------------------------------------------------------------------
async function callLendingPartnerAPI(model) {
    let output = {
        success: false,
        message: "",
        status: 501,
    };
    try {
        if (IsNullEmpty(model.source)) {
            output.message = "source is required";
        } else if (IsNullEmpty(model.loanId)) {
            output.message = "loanId is required";
        } else if (IsNullEmpty(model.apiType)) {
            output.message = "apiType is required";
        } else if (model.apiType == "loanApplication" && IsNullEmpty(model.lenderId)) {
            output.message = "lenderId is required";
        } else if (model.apiType == "loanSanction" && model.module != "makeLive" && IsNullEmpty(model.paymentMode)) {
            output.message = "paymentMode is required";
        } else if (model.apiType == "loanSanction" && model.module != "makeLive" && IsNullEmpty(model.referenceId)) {
            output.message = "referenceId is required";
        } else if (model.apiType == "loanRepayment" && IsNullEmpty(model.referenceNo)) {
            output.message = "referenceNo is required";
        } else if (model.apiType == "loanRepayment" && IsNullEmpty(model.emiNumber)) {
            output.message = "emiNumber is required";
        }

        if (output.message == "") {
            if (IsNullEmpty(model.partnerCode)) {
                const getPartnerCode = await getlenderPartnerCode(model.loanId);
                if (getPartnerCode.success) {
                    model.partnerCode = getPartnerCode.partnerCode;
                }
            }

            if (!IsNullEmpty(model.partnerCode)) {
                switch (model.apiType) {
                    case "loanApplication":
                        output = await sendLoanApplication(model);
                        break;
                    case "loanSanction":
                        output = await sendLoanSanction(model);
                        break;
                    case "loanDisbursement":
                        output = await sendDisbursementNotification(model);
                        break;
                    case "loanRepayment":
                        output = await sendLoanRepayment(model);
                        break;
                    case "emiSchedule":
                        output = await sendEmiSchedule(model);
                        break;
                    case "addressDetails":
                        output = await sendAddressDetails(model);
                        break;
                    case "loanDocuments":
                        output = await sendLoanDocuments(model);
                        break;
                    case "loanCancellation":
                        output = await sendLoanCancellation(model);
                        break;
                    case "bankDetails":
                        output = await sendBankDetails(model);
                        break;
                }
            } else {
                output.message = "Partner Code Not Found";
            }
        }

    } catch (ex) {
        saveErrorLog("callLendingPartnerAPI", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    model.errorMsg = output.success == true ? "" : output.message;
    model.success = output.success;
    model.partnerType = "Lending Partner";
    saveMessageQueue(model);
    return output;
};
module.exports.callLendingPartnerAPI = callLendingPartnerAPI;

module.exports.requestMessageQueue = async function (model) {
    let output = {
        success: false,
        message: "",
        status: 501,
    };
    try {
        let query = { isActive: true, partnerType: "Lending Partner", noOfAttempts: { $lt: 6 } };

        if (!IsNullEmpty(model.subType)) {
            query["subType"] = model.subType;
        }

        if (!IsNullEmpty(model.loanId)) {
            query["loanId"] = mongoose.Types.ObjectId(model.loanId);
        }

        if (IsNullEmpty(model.includeAll)) {
            query["requestModel.apiType"] = { $nin: ["loanApplication", "loanDisbursement"] }
        }

        const messageQueue = await MessageQueue.find(query).sort({ insertedOn: 1 });

        if (!IsNullEmpty(messageQueue)) {
            for (messsage of messageQueue) {
                const model = messsage.requestModel;
                model.source = "schedular";
                model.subType = messsage.subType;
                //logger.logs("model", model);
                const res = await callLendingPartnerAPI(model);
            }
        }
        output = {
            success: true,
            message: "Executed Successfully",
            status: 200,
        };
    } catch (ex) {
        logger.logs(ex);
        saveErrorLog("requestMessageQueue", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};

module.exports.getLoanDetails = async function (model) {
    let output = {
        data: null,
        success: false,
        message: "",
        status: 501,
    };
    try {
        if (model.type == "allLoans") {
            if (model.partnerId) {
                const lenderLoans = await getLenderLoans(model);

                if (lenderLoans.success) {
                    output = {
                        data: lenderLoans.data,
                        success: true,
                        message: "Fetched Successfully",
                        status: 200
                    };
                } else {
                    output.message = lenderLoans.message;
                    return output;
                }
            } else {
                output.message = "Invalid Access";
            }
        } else {
            const loanInfo = await LoanInfo.findOne({ "loanNo": model.loanNo }).select({ _id: 1, loanNo: 1 });
            if (loanInfo) {
                model.loanId = loanInfo._id;

                const getLenderDetails = await getlenderPartnerCode(model.loanId);
                if (getLenderDetails) {
                    if (getLenderDetails.partnerCode.toString() == model.partnerCode.toString()) {
                        if (model.type == "loanApplication") {
                            const dbDataResponse = await getDataForLoanApplication(model);
                            if (dbDataResponse.success) {
                                const data = dbDataResponse.data;
                                const setDataReponse = await setDataForLoanApplication(model, data, null);
                                if (setDataReponse.success) {
                                    output = {
                                        data: setDataReponse.data,
                                        success: true,
                                        message: "fetched successfully",
                                        status: 200,
                                    };
                                } else {
                                    saveErrorLog("getLoanDetails", apiPath, model, setDataReponse.message);
                                    output.message = "Some error occured. Please contact fundfina technical support.";
                                }
                            } else {
                                saveErrorLog("getLoanDetails", apiPath, model, dbDataResponse.message);
                                output.message = "Some error occured. Please contact fundfina technical support.";
                            }
                        } else if (model.type == "loanSanction") {
                            const dbDataResponse = await getDataForLoanSanction(model.loanId);
                            if (dbDataResponse.success) {
                                const data = dbDataResponse.data;
                                const setDataReponse = await setDataForLoanSanction(model, data);
                                if (setDataReponse.success) {
                                    output = {
                                        data: setDataReponse.data,
                                        success: true,
                                        message: "fetched successfully",
                                        status: 200,
                                    };
                                } else {
                                    saveErrorLog("getLoanDetails", apiPath, model, setDataReponse.message);
                                    output.message = "Some error occured. Please contact fundfina technical support.";
                                }
                            } else {
                                saveErrorLog("getLoanDetails", apiPath, model, dbDataResponse.message);
                                output.message = "Some error occured. Please contact fundfina technical support.";
                            }
                        } else if (model.type == "loanRepayment") {
                            //logger.logs("inside loanRepayment");
                            const dbDataResponse = await getDataForLoanRepayment(model.loanId, 0);
                            if (dbDataResponse.success) {
                                const data = dbDataResponse.data;
                                const setDataReponse = await setDataForLoanRepayment(model, data);

                                if (setDataReponse.success) {
                                    output = {
                                        data: setDataReponse.data,
                                        success: true,
                                        message: "fetched successfully",
                                        status: 200,
                                    };
                                } else {
                                    saveErrorLog("getLoanDetails", apiPath, model, setDataReponse.message);
                                    output.message = "Some error occured. Please contact fundfina technical support.";
                                }
                            } else {
                                saveErrorLog("getLoanDetails", apiPath, model, dbDataResponse.message);
                                output.message = "Some error occured. Please contact fundfina technical support.";
                            }
                        } else {
                            const dbDataResponse = await getDataForLoan(model.loanId);
                            if (dbDataResponse.success) {
                                const data = dbDataResponse.data;
                                const setDataReponse = await setDataForLoanApplication(model, data);
                                if (setDataReponse.success) {
                                    const record = setDataReponse.data;
                                    const setLoanSanctionDataReponse = await setDataForLoanSanction(model, data);
                                    if (setLoanSanctionDataReponse.success) {
                                        record.loanDetails = setLoanSanctionDataReponse.data;
                                        const setLoanRepaymentDataReponse = await setDataForLoanRepayment(model, data);
                                        record.repayments = setLoanRepaymentDataReponse.data;
                                        if (setLoanRepaymentDataReponse.success) {
                                            output = {
                                                data: record,
                                                success: true,
                                                message: "fetched successfully",
                                                status: 200,
                                            };
                                        } else {
                                            saveErrorLog("getLoanDetails", apiPath, model, setLoanRepaymentDataReponse.message);
                                            output.message = "Some error occured. Please contact fundfina technical support.";
                                        }

                                    } else {
                                        saveErrorLog("getLoanDetails", apiPath, model, setLoanSanctionDataReponse.message);
                                        output.message = "Some error occured. Please contact fundfina technical support.";
                                    }

                                } else {
                                    saveErrorLog("getLoanDetails", apiPath, model, setDataReponse.message);
                                    output.message = "Some error occured. Please contact fundfina technical support.";
                                }
                            } else {
                                saveErrorLog("getLoanDetails", apiPath, model, dbDataResponse.message);
                                output.message = "Some error occured. Please contact fundfina technical support.";
                            }
                        }
                    } else {
                        output.message = "Invalid Loan No";
                    }

                } else {
                    output.message = "Lending Partner Not Found";
                }
            } else {
                output.message = "Loan No Not Found";
            }
        }
    } catch (ex) {
        saveErrorLog("getLoanDetails", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};

async function getToken(conf, model) {
    let output = {
        token: null,
        tokenType: null,
        success: false,
        message: "",
        status: 501,
    };
    try {
        if (model.partnerCode.toLowerCase() == "aphe") {
            const params = new URLSearchParams();

            params.append('grant_type', conf.tokenParameters.grant_type);
            params.append('UserName', conf.tokenParameters.UserName);
            params.append('Password', conf.tokenParameters.Password);

            const url = conf.tokenUrl;
            logger.logs("url", url);
            logger.logs("params", params);
            const response = await fetch(url, { method: 'POST', body: params, timeout: 30000 })
                .then(function (u) {
                    return u.json();
                })
                .then(function (json) {
                    return json;
                })
                .catch((err) => {
                    console.error("err :", err);
                    return err;
                });

            logger.logs(response);
            if (response.hasOwnProperty('error')) {
                output.message = response.error_description;
            } else {
                //logger.logs(response);
                output = {
                    token: response.access_token,
                    tokenType: response.token_type,
                    success: true,
                    message: "fetched successfully",
                    status: 200,
                };
                if (response.hasOwnProperty('expires_in')) {
                    output.expiresIn = response.expires_in;
                }
                if (response.hasOwnProperty('refresh_token')) {
                    output.refreshToken = response.refresh_token;
                }
            }
        }
    }
    catch (ex) {
        logger.logs("getToken Error ", ex);
        const newModel = {
            model: model,
            conf: conf
        };
        saveErrorLog("getToken", apiPath, newModel, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};

async function sendLoanApplication(model) {
    logger.logs("calling loan application");
    let output = {
        success: false,
        message: "",
        status: 501,
    };
    try {
        let loanInfo = await LoanInfo.findById(model.loanId);

        const conf = config.get(model.partnerCode.toLowerCase());

        if (!IsNullEmpty(conf)) {
            const dbDataResponse = await getDataForLoanApplication(model);

            if (dbDataResponse.success) {
                const data = dbDataResponse.data;

                const setDataReponse = await setDataForLoanApplication(model, data, conf);
                if (setDataReponse.success) {
                    let tokenResponse = null;
                    if (conf.isTokenGenration == true) {
                        tokenResponse = await getToken(conf, model);
                        if (!tokenResponse.success) {
                            output.message = tokenResponse.message;
                            return output;
                        }
                    }

                    if (!IsNullEmpty(model.url)) {
                        let _body = JSON.stringify(setDataReponse.data);
                        // logger.logs("url", model.url);
                        // logger.logs("headers", conf.headerConstants);
                        // logger.logs("_body", _body);

                        let response = null;
                        let responseSuccess = false;
                        let lenderCustId = null;
                        let lenderLoanId = null;
                        let lenderLeadId = null;

                        if (model.partnerCode.toLowerCase() == "aphe") {
                            const authorization = tokenResponse.tokenType + " " + tokenResponse.token;
                            let header = {
                                "Content-Type": "application/json",
                                "Authorization": authorization,
                            }
                            response = await fetch(model.url, {
                                method: "post",
                                body: _body,
                                headers: header,
                                timeout: 30000
                            }).then(function (u) {
                                return u.json();
                            }).then(function (json) {
                                return json;
                            }).catch((err) => {
                                console.error("err :", err);
                                return err;
                            });

                            // const docLog = new DocumentLog({
                            //     loanId: model.loanId,
                            //     type: "loanApplication",
                            //     value1: _body,
                            //     value2: JSON.stringify(_body),
                            //     insertedOn: Date.now()
                            // });

                            // await docLog.save();

                            // logger.logs("response", response);

                            logger.logs("response", response);

                            if (Boolean(response.Status)) {
                                responseSuccess = true;
                                lenderCustId = response.Data.MemberID;
                                lenderLoanId = response.Data.LoginId;
                            } else {
                                const filePath = config.get("keys.errorFilePath");
                                const errorFileName = filePath + "Error_Aphelion_loanApplication" + model.loanId + ".txt";
                                fs.appendFile(errorFileName, _body, function (err) {
                                    logger.logs(err);
                                });

                                output.message = response.Message;
                            }

                        } else if (model.partnerCode.toLowerCase() == "cent") {
                            //const setResponse = await setDataForLoanPreSanction(model, data);
                            const request = setDataReponse.data;
                            const requestBody = {
                                "RequestHeader": conf.bodyConstants,
                                "RequestBody": request
                            }
                            _body = JSON.stringify(requestBody);

                            // logger.logs("model.url : ", model.url);
                            // logger.logs("request : ", _body);

                            response = await fetch(model.url, {
                                method: "post",
                                body: _body,
                                headers: conf.headerConstants,
                                timeout: 60000
                            }).then(function (u) {
                                return u.json();
                            }).then(function (json) {
                                return json;
                            }).catch((err) => {
                                console.error("err :", err);
                                return err;
                            });
                            logger.logs("response : ", response);

                            if (response.DataFromResult && !isNaN(response.DataFromResult)) {
                                responseSuccess = true;
                                lenderLoanId = response.DataFromResult;
                            } else {
                                output.message = response.DataFromResult;
                            }
                        } else if (model.partnerCode.toLowerCase() == "lbox") {
                            response = await fetch(model.url, {
                                method: "post",
                                body: _body,
                                headers: conf.headerConstants,
                                timeout: 30000
                            }).then(function (u) {
                                return u.json();
                            }).then(function (json) {
                                return json;
                            }).catch((err) => {
                                console.error("err :", err);
                                return err;
                            });
                            logger.logs("response loan application: ", response);

                            if (response.success) {
                                responseSuccess = true;
                                lenderCustId = response.data.lbUserId;
                                lenderLoanId = response.data.loanId;
                            } else {
                                output.message = response.msg;
                                if (!IsNullEmpty(response.data.errorData)) {
                                    let fields = null;
                                    if (!IsNullEmpty(response.data.errorField)) {
                                        fields = response.data.errorField.filter((x) => x.field != null).reduce(function (prev, cur) {
                                            return prev + cur.field + ",";
                                        }, " ");
                                    }
                                    output.message = fields ? (output.message + " " + response.data.errorData.join(",") + fields) : (output.message + " " + response.data.errorData.join(","));
                                }

                                if (response.msg.toLowerCase() == "invalid parameters") {
                                    logger.logs("inside save data", response.data);
                                    saveErrorLog("sendLoanApplication", apiPath, model, response.data);
                                }
                            }
                        } else {
                            // logger.logs("model.url", model.url);
                            // logger.logs("conf.headerConstants", conf.headerConstants);
                            // logger.logs("_body", _body);
                            response = await fetch(model.url, {
                                method: "post",
                                body: _body,
                                headers: conf.headerConstants,
                                timeout: 60000
                            }).then(function (u) {
                                return u.json();
                            }).then(function (json) {
                                return json;
                            }).catch((err) => {
                                console.error("err :", err);
                                return err;
                            });

                            // logger.logs("response", response);
                            if (Boolean(response.status) == true) {
                                responseSuccess = true;
                                lenderCustId = response.partnerCustRefId;
                                lenderLoanId = response.partnerLoanRefId;
                                lenderLeadId = response.partnerLeadId;
                            } else {
                                const filePath = config.get("keys.errorFilePath");
                                const errorFileName = filePath + "Error_" + model.partnerCode + "_" + model.loanId + ".txt";
                                fs.appendFile(errorFileName, _body, function (err) {
                                    logger.logs(err);
                                });
                                output.message = response.message;
                            }
                        }
                        model.response = response;


                        if (responseSuccess) {

                            if (loanInfo.lenderDetails != null && loanInfo.lenderDetails.length > 0) {
                                await LoanInfo.findByIdAndUpdate({ _id: loanInfo._id }, { $set: { "lenderDetails.0.lenderCustId": lenderCustId, "lenderDetails.0.lenderLoanId": lenderLoanId, "lenderDetails.0.lenderLeadId": lenderLeadId, "lenderDetails.0.amount": data.loanAmount, updateOn: Date.now() } });
                            } else {
                                const lender_Details = [];

                                lender_Details.push({
                                    lenderPartnerId: model.lenderId,
                                    lenderCustId: lenderCustId,
                                    lenderLoanId: lenderLoanId,
                                    lenderLeadId: lenderLeadId,
                                    amount: data.loanAmount,
                                    investedOn: Date.now()
                                });

                                await LoanInfo.findByIdAndUpdate({ _id: loanInfo._id }, { $set: { lenderDetails: lender_Details } });
                            }

                            saveLendingPartnerAllocation(loanInfo._id, true);

                            output = {
                                success: true,
                                message: "sent successfully",
                                status: 200,
                            };
                        } else if (!model.isRetryOffline) {
                            output.message = "Error from " + data.lenderName + " : " + output.message + ". Please select other Lending Partner"

                            saveErrorLog("sendLoanApplication", apiPath, model, output.message);
                        }
                    } else {
                        output.message = "URL Path Not Found";
                    }

                } else {
                    output.message = setDataReponse.message;
                }
            } else {
                output.message = dbDataResponse.message;
            }
        } else {
            output.message = "No Config found for partner " + model.partnerDisplayName;
            return output;
        }
    }
    catch (ex) {
        logger.logs(ex);
        saveErrorLog("sendLoanApplication", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex;
    }
    return output;
};

async function sendAddressDetails(model) {
    let output = {
        success: false,
        message: "",
        status: 501,
    };
    try {
        const conf = config.get(model.partnerCode.toLowerCase());
        if (!IsNullEmpty(conf)) {
            let tokenResponse = null;
            if (conf.isTokenGenration == true) {
                tokenResponse = await getToken(conf, model);
                if (!tokenResponse.success) {
                    output.message = tokenResponse.message;
                    return output;
                }
            }

            const dbDataResponse = await getDataforAddAddress(model.loanId);
            if (dbDataResponse.success) {
                const data = dbDataResponse.data;
                if (!IsNullEmpty(data.lenderCustId) && !IsNullEmpty(data.lenderLoanId)) {
                    if (model.partnerCode.toLowerCase() == "aphe") {
                        const request = {
                            MemberID: data.lenderCustId,
                            UserID: conf.tokenParameters.UserName,
                            PermanentAddPinCode: data.addressDetails[0].pincode,
                            CorrespondenceAddPinCode: data.addressDetails[0].pincode,
                        }
                        const address = ((((((!IsNullEmpty(data.addressDetails[0].add1) ? data.addressDetails[0].add1.trim() : "")
                            + " "
                            + (!IsNullEmpty(data.addressDetails[0].add2) ? data.addressDetails[0].add2.trim() : "")).toString().trim()
                            + " "
                            + (!IsNullEmpty(data.addressDetails[0].add3) ? data.addressDetails[0].add3.trim() : "")).toString().trim()
                            + " "
                            + (!IsNullEmpty(data.addressDetails[0].city) ? data.addressDetails[0].city.trim() : "")).toString().trim()
                            + " "
                            + (!IsNullEmpty(data.addressDetails[0].state) ? data.addressDetails[0].state.trim() : "")).toString().trim()
                            + " "
                            + (!IsNullEmpty(data.addressDetails[0].country) ? data.addressDetails[0].country.trim() : "")).toString().trim()

                        request.PermanentAddress = address;
                        request.CorrespondenceAddress = address;

                        const _body = JSON.stringify(request);
                        logger.logs("model.url", model.url);
                        logger.logs("request", _body);
                        const authorization = tokenResponse.tokenType + " " + tokenResponse.token;
                        let header = {
                            "Content-Type": "application/json",
                            "Authorization": authorization,
                        }

                        logger.logs("headers", header);
                        let response = await fetch(model.url, {
                            method: "post",
                            body: _body,
                            headers: header,
                            timeout: 30000
                        }).then(function (u) {
                            return u.json();
                        }).then(function (json) {
                            return json;
                        }).catch((err) => {
                            console.error("err :", err);
                            return err;
                        });

                        logger.logs("response", response);
                        logger.logs("response.Message", response.Message);

                        if (response.Status) {
                            output = {
                                success: true,
                                message: response.Message,
                                status: 200,
                            };
                        } else if (response.Message == "Permanent Address already exist!!!") {
                            output = {
                                success: true,
                                message: response.Message,
                                status: 200,
                            };
                        } else {
                            output.message = response.Message;
                            saveErrorLog("sendEmiSchedule", apiPath, model, response.Message);
                        }
                    } else {
                        output = {
                            success: true,
                            message: "Applicant Address Added Sucessfully!!!",
                            status: 200,
                        };
                    }
                } else {
                    output.message = "lenderCustId and lenderLoanId does not exists";
                }
            } else {
                output.message = dbDataResponse.message;
            }
        } else {
            output.message = "No Config found for partner " + model.partnerDisplayName;
            return output;
        }
    } catch (ex) {
        logger.logs(ex);
        saveErrorLog("sendAddressDetails", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;

};

async function sendEmiSchedule(model) {
    let output = {
        success: false,
        message: "",
        status: 501,
    };
    try {
        const conf = config.get(model.partnerCode.toLowerCase());

        if (!IsNullEmpty(conf)) {
            const dbDataResponse = await getDataForEmiSchedule(model.loanId);

            logger.logs("dbDataResponse", dbDataResponse);
            if (dbDataResponse.success) {
                const data = dbDataResponse.data;
                if (!IsNullEmpty(data.lenderCustId) && !IsNullEmpty(data.lenderLoanId)) {
                    if (model.partnerCode.toLowerCase() == "aphe") {
                        //await sleep(120000);
                        let tokenResponse = null;
                        if (conf.isTokenGenration == true) {
                            tokenResponse = await getToken(conf, model);
                            if (!tokenResponse.success) {
                                output.message = tokenResponse.message;
                                return output;
                            }
                        }

                        let frequency = "1";
                        switch (data.repaymentFrequency) {
                            case "Daily":
                                frequency = "1";
                                break;
                            case "Weekly":
                                frequency = "2";
                                break;
                            case "Forthnightly":
                                frequency = "3";
                                break;
                            case "Monthly":
                                frequency = "4";
                                break;
                            case "Bimonthly":
                                frequency = "5";
                                break;
                            case "Quarterly":
                                frequency = "6";
                                break;
                            case "Half-Yearly":
                                frequency = "7";
                                break;
                            case "Yearly":
                                frequency = "8";
                                break;
                            case "None":
                                frequency = "9";
                                break;
                            case "On Maturity":
                                frequency = "10";
                                break;
                            case "Adhoc":
                                frequency = "10";
                                break;
                            default:
                                frequency = "1";
                                break;
                        }

                        const request = {
                            UserID: conf.tokenParameters.UserName,
                            Frequency: frequency,
                            TenureInMonth: data.maturity,
                            LoanAcNo: data.lenderLoanId,
                            EMIScheduleList: []
                        };

                        for (emi of data.installments) {
                            const dueDate = getMDYFormattedDate(emi.actualDueDate, "/");
                            const schedule = {
                                InstallmentNo: emi.installmentNumber.toString(),
                                DueDate: dueDate,
                                Principal: emi.installmentPrincipal,
                                Interest: emi.installmentInterest,
                                EMI: emi.installmentAmount
                            };
                            request.EMIScheduleList.push(schedule);
                        }

                        const _body = JSON.stringify(request);
                        logger.logs("request", _body);
                        const authorization = tokenResponse.tokenType + " " + tokenResponse.token;
                        let header = {
                            "Content-Type": "application/json",
                            "Authorization": authorization,
                        }
                        let response = await fetch(model.url, {
                            method: "post",
                            body: _body,
                            headers: header,
                            timeout: 30000
                        }).then(function (u) {
                            return u.json();
                        }).then(function (json) {
                            return json;
                        }).catch((err) => {
                            console.error("err :", err);
                            return err;
                        });

                        logger.logs("response", response);
                        if (response.Status) {
                            output = {
                                success: true,
                                message: response.Message,
                                status: 200,
                            };
                        } else {
                            output.message = response.Message;
                            saveErrorLog("sendEmiSchedule", apiPath, model, response.Message);
                        }
                    } else if (model.partnerCode.toLowerCase() == "lbox") {
                        let body = {
                            loanId: data.lenderLoanId,
                            edi: []
                        }
                        for (let i = 0; i < data.installments.length; i++) {
                            body.edi.push({
                                counter: data.installments[i].installmentNumber.toString(),
                                interest: data.installments[i].installmentInterest.toString(),
                                principal: data.installments[i].installmentPrincipal.toString(),
                                balance: data.installments[i].initialPrincipalOutstanding.toString(),
                                amount: data.installments[i].installmentAmount.toString(),
                                dueDate: getYMDFormattedDate(data.installments[i].dueDate, "-").toString()
                            })
                        }

                        const _body = JSON.stringify(body);
                        logger.logs("reuqest", _body);
                        let response = await fetch(model.url, {
                            method: "post",
                            body: _body,
                            headers: conf.headerConstants,
                            timeout: 30000
                        }).then(function (u) {
                            return u.json();
                        }).then(function (json) {
                            return json;
                        }).catch((err) => {
                            console.error("err :", err);
                            return err;
                        });
                        logger.logs("response", response);
                        if (response.success || response.msg == "EDI Already Exist.") {
                            output = {
                                success: true,
                                message: response.msg,
                                status: 200,
                            };
                        } else {
                            output.message = response.msg;
                        }
                    }
                } else {
                    output.message = "lenderCustId and lenderLoanId does not exists";
                }
            } else {
                output.message = dbDataResponse.message;
            }
        } else {
            output.message = "No Config found for partner " + model.partnerDisplayName;
            return output;
        }
    } catch (ex) {
        logger.logs(ex);
        saveErrorLog("sendEmiSchedule", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};

// async function sendLoanPreSanction(model) {
//     let output = {
//         success: false,
//         message: "",
//         status: 501,
//     };
//     try {
//         logger.logs("model.partnerCode", model.partnerCode);
//         const dbDataResponse = await getDataForLoanPreSanction(model.loanId, model.lenderId);
//         if (dbDataResponse.success) {
//             const data = dbDataResponse.data;

//             if (model.partnerCode.toLowerCase() == config.get("cent.partnerCode")) {
//                 const setResponse = await setDataForLoanPreSanction(model, data);

//                 if (setResponse.success) {
//                     const request = setResponse.data;
//                     const requestBody = {
//                         "RequestHeader": {
//                             "UserID": config.get("cent.userId"),
//                             "Password": config.get("cent.password"),
//                             "PartnerID": config.get("cent.partnerId")
//                         },
//                         "RequestBody": request
//                     }
//                     const _body = JSON.stringify(requestBody);
//                     const config_UrlPath = model.partnerCode.toLowerCase() + ".loanSanction";
//                     const urlPath = config.get(config_UrlPath);
//                     logger.logs("request : ", _body);
//                     let header = {
//                         "Content-Type": "application/json"
//                     }

//                     response = await fetch(urlPath, {
//                         method: "post",
//                         body: _body,
//                         headers: header,
//                     }).then(function (u) {
//                         return u.json();
//                     }).then(function (json) {
//                         return json;
//                     }).catch((err) => {
//                         console.error("err :", err);
//                         return err;
//                     });
//                     logger.logs("loan Pre Sanction response : ", response);
//                     if (response.DataFromResult && !isNaN(response.DataFromResult)) {
//                         const loanInfo = await LoanInfo.findById(model.loanId);
//                         if (loanInfo.lenderDetails.length == 0) {
//                             loanInfo.lenderDetails.push({
//                                 lenderId: null,
//                                 lenderCustId: null,
//                                 lenderLoanId: response.DataFromResult,
//                                 amount: 0,
//                                 investedOn: Date.now()
//                             });
//                         } else {
//                             loanInfo.lenderDetails[0].lenderCustId = null;
//                             loanInfo.lenderDetails[0].lenderLoanId = response.DataFromResult;
//                         }

//                         await loanInfo.save();

//                         // const messageQueue = await MessageQueue.find({ loanId: mongoose.Types.ObjectId(model.loanId), apiType: "loanDocuments" });

//                         // if (messageQueue.length == 0) {
//                         //     const modelForLoanDocument = {
//                         //         apiType: "loanDocuments",
//                         //         loanId: model.loanId,
//                         //         source: "application",
//                         //         partnerCode: model.partnerCode
//                         //     };
//                         //     callLendingPartnerAPI(modelForLoanDocument);
//                         // }
//                         output = {
//                             success: true,
//                             message: "DataFromResult received as " + response.DataFromResult,
//                             status: 200,
//                         };
//                     } else {
//                         const message = `Error from ${data.lenderName} : ${response.DataFromResult} . Please select other lending partner.`
//                         output.message = message;
//                     }
//                 } else {
//                     output.message = setResponse.message;
//                 }
//             } else {
//                 const setResponse = await setDataForLoanPreSanction(model, data);

//                 if (setResponse.success) {
//                     const request = setResponse.data;
//                     const _body = JSON.stringify(request);
//                     const config_UrlPath = model.partnerCode.toLowerCase() + ".loanSanction";
//                     const urlPath = config.get(config_UrlPath);
//                     const config_authKey = model.partnerCode.toLowerCase() + ".authKey";
//                     const authKey = config.get(config_authKey);
//                     let header = {
//                         "Content-Type": "application/json",
//                         "authKey": authKey
//                     }

//                     response = await fetch(urlPath, {
//                         method: "post",
//                         body: _body,
//                         headers: header,
//                     }).then(function (u) {
//                         return u.json();
//                     }).then(function (json) {
//                         return json;
//                     }).catch((err) => {
//                         console.error("err :", err);
//                         return err;
//                     });
//                     if (Boolean(response.status) == false) {
//                         output.message = response.message;
//                     } else {
//                         output = {
//                             success: true,
//                             message: response.message,
//                             status: 200,
//                         };
//                     }
//                 } else {
//                     output.message = setResponse.message;
//                 }
//             }
//         } else {
//             output.message = dbDataResponse.message;
//         }
//     } catch (ex) {
//         logger.logs(ex);
//         saveErrorLog("sendLoanSanction", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
//         output.message = ex.message;
//     }
//     return output;
// };

async function sendLoanSanction(model) {
    let output = {
        success: false,
        message: "",
        status: 501,
    };
    try {
        const conf = config.get(model.partnerCode.toLowerCase());

        if (!IsNullEmpty(conf)) {
            let tokenResponse = null;
            let response = null;
            if (conf.isTokenGenration == true) {
                tokenResponse = await getToken(conf, model);
                if (!tokenResponse.success) {
                    output.message = tokenResponse.message;
                    return output;
                }
            }

            const dbDataResponse = await getDataForLoanSanction(model.loanId, model.module);

            if (dbDataResponse.success) {
                const data = dbDataResponse.data;

                if (model.partnerCode.toLowerCase() == "aphe") {
                    if (!IsNullEmpty(data.lenderCustId) && !IsNullEmpty(data.lenderLoanId)) {

                        const approvalDate = (data.approvedOn.getDate() < 10 ? '0' + data.approvedOn.getDate() : data.approvedOn.getDate()).toString() + "/" +
                            ((data.approvedOn.getMonth() + 1) < 10 ? '0' + (data.approvedOn.getMonth() + 1) : (data.approvedOn.getMonth() + 1)).toString() + "/" + data.approvedOn.getFullYear();

                        const disburesmentDate = (data.disbursedOn.getDate() < 10 ? '0' + data.disbursedOn.getDate() : data.disbursedOn.getDate()).toString() + "/" +
                            ((data.disbursedOn.getMonth() + 1) < 10 ? '0' + (data.disbursedOn.getMonth() + 1) : (data.disbursedOn.getMonth() + 1)).toString() + "/" + data.disbursedOn.getFullYear();

                        const firstEmiDateFormat = (data.firstEmiDate.getDate() < 10 ? '0' + data.firstEmiDate.getDate() : data.firstEmiDate.getDate()).toString() + "/" +
                            ((data.firstEmiDate.getMonth() + 1) < 10 ? '0' + (data.firstEmiDate.getMonth() + 1) : (data.firstEmiDate.getMonth() + 1)).toString() + "/" + data.firstEmiDate.getFullYear();

                        const lastEmiDateFormat = (data.lastEmiDate.getDate() < 10 ? '0' + data.lastEmiDate.getDate() : data.lastEmiDate.getDate()).toString() + "/" +
                            ((data.lastEmiDate.getMonth() + 1) < 10 ? '0' + (data.lastEmiDate.getMonth() + 1) : (data.lastEmiDate.getMonth() + 1)).toString() + "/" + data.lastEmiDate.getFullYear();

                        // logger.logs("approvalDate", approvalDate);
                        // logger.logs("disburesmentDate", disburesmentDate);
                        // logger.logs("firstEmiDateFormat", firstEmiDateFormat);
                        // logger.logs("lastEmiDateFormat", lastEmiDateFormat);
                        logger.logs("existingLoansFromSameLenderCount", data.existingLoansFromSameLenderCount);

                        let contractFilePath = data.contractFile

                        let contractFileStream = await downloadImageFromS3(contractFilePath, "buffer");

                        // let kfsFilePath = data.kfsFile;
                        // let kfsFileStream = null;

                        // if (kfsFilePath)
                        //     kfsFileStream = await downloadImageFromS3(kfsFilePath, "buffer");

                        // logger.logs("contractFileStream", contractFileStream ? "yes" : "no");
                        // logger.logs("kfsFilePath", kfsFilePath);
                        // logger.logs("kfsFileStream", kfsFileStream ? "yes" : "no");

                        const form = new FormData();
                        form.append('UserID', conf.tokenParameters.UserName);
                        form.append('MemberID', data.lenderCustId);
                        form.append('LoginId', data.lenderLoanId);
                        form.append('LoanExternalID', data.loanNo);
                        form.append('SanctionAmount', data.amount);
                        form.append('LoanCode', 63);
                        form.append('RecoveryMode', 1);
                        form.append('ApprovalDate', approvalDate);
                        form.append('LoanApproveStatus', 1);
                        form.append('MarginAmount', 0);
                        form.append('InterestRate', data.interestRate);
                        form.append('TotalAccruedInterest', round10(data.totalInterestAmount, -2));
                        form.append('Remark', data.adminRemarks);
                        form.append('LoanCalculationStartDateStr', disburesmentDate);
                        form.append('CalendarOption', 6);
                        form.append('Loanpurpose', 1);
                        form.append('LoanCycle', data.existingLoansFromSameLenderCount);
                        form.append('PaymentModeId', model.paymentMode.toLowerCase() == "neft" ? 4 : 5);
                        form.append('FirstEMIDate', firstEmiDateFormat);
                        form.append('LastEMIDate', lastEmiDateFormat);
                        form.append('FirstEmiAmount', data.firstEmiAmount);
                        form.append('LastEmiAmount', data.lastEmiAmount);
                        form.append('DisbRefID', model.referenceId);
                        form.append('BorrowerBank', data.bankName);
                        form.append('Branch', data.branch);
                        form.append('AccountNo', data.accountNo);
                        form.append('IFSCCode', data.ifsc);
                        form.append('my_buffer', new Buffer.alloc(10));
                        form.append('AgreementDoc', contractFileStream.data, "abc.pdf");
                        form.append('AgreementOther', "");

                        // if (kfsFileStream)
                        //     form.append('KfsDocument', kfsFileStream.data, "kfs.pdf");

                        // logger.logs("url", model.url);
                        // logger.logs('UserID', conf.tokenParameters.UserName);
                        // logger.logs('MemberID', data.lenderCustId);
                        // logger.logs('LoginId', data.lenderLoanId);
                        // logger.logs('LoanExternalID', data.loanNo);
                        // logger.logs('SanctionAmount', data.amount);
                        // logger.logs('LoanCode', 63);
                        // logger.logs('RecoveryMode', 1);
                        // logger.logs('ApprovalDate', approvalDate);
                        // logger.logs('LoanApproveStatus', 1);
                        // logger.logs('MarginAmount', 0);
                        // logger.logs('InterestRate', data.interestRate);
                        // logger.logs('TotalAccruedInterest', round10(data.totalInterestAmount, -2));
                        // logger.logs('Remark', data.adminRemarks);
                        // logger.logs('LoanCalculationStartDateStr', disburesmentDate);
                        // logger.logs('CalendarOption', 6);
                        // logger.logs('Loanpurpose', 1);
                        // logger.logs('LoanCycle', data.existingLoansFromSameLenderCount);
                        // logger.logs('PaymentModeId', model.paymentMode.toLowerCase() == "neft" ? 4 : 5);
                        // logger.logs('FirstEMIDate', firstEmiDateFormat);
                        // logger.logs('LastEMIDate', lastEmiDateFormat);
                        // logger.logs('FirstEmiAmount', data.firstEmiAmount);
                        // logger.logs('LastEmiAmount', data.lastEmiAmount);
                        // logger.logs('DisbRefID', model.referenceId);
                        // logger.logs('BorrowerBank', data.bankName);
                        // logger.logs('Branch', data.branch);
                        // logger.logs('AccountNo', data.accountNo);
                        // logger.logs('IFSCCode', data.ifsc);
                        // logger.logs("AgreementDoc", contractFileStream.data);
                        // logger.logs("AgreementOther", "");

                        const authorization = tokenResponse.tokenType + " " + tokenResponse.token;

                        const formHeaders = form.getHeaders();

                        let header = {
                            "Authorization": authorization,
                            ...formHeaders
                        }
                        const response = await fetch(model.url, { method: 'POST', body: form, headers: header, timeout: 30000 });
                        // logger.logs("model.url", model.url);
                        // logger.logs("response", response);
                        const responseData = await response.json();
                        // logger.logs("responseData", responseData);

                        if (responseData.Status == true || responseData.Message == "Loan Sanction Process already completed!") {
                            output = {
                                success: true,
                                message: responseData.Message,
                                status: 200,
                            };
                        } else if (responseData.Status == false) {
                            output.message = responseData.Message;
                        }
                    } else {
                        output.message = "lenderCustId and lenderLoanId does not exists";
                    }
                } else {
                    const setResponse = await setDataForLoanSanction(model, data);

                    if (setResponse.success) {
                        const request = setResponse.data;
                        const _body = JSON.stringify(request);
                        //logger.logs("model.url", model.url);
                        //logger.logs("_body", _body);
                        //await saveErrorLog("sendLoanSanction", _body, JSON.stringify(_body), model.url);
                        //const config_authKey = model.partnerCode.toLowerCase() + ".authKey";
                        response = await fetch(model.url, {
                            method: "post",
                            body: _body,
                            headers: conf.headerConstants,
                        }).then(function (u) {
                            return u.json();
                        }).then(function (json) {
                            return json;
                        }).catch((err) => {
                            console.error("err :", err);
                            return err;
                        });
                        //logger.logs("response", response);

                        if (Boolean(response.status) == false) {
                            output.message = response.message;
                        } else {
                            output = {
                                success: true,
                                message: response.message,
                                status: 200,
                            };
                        }
                    } else {
                        output.message = setResponse.message;
                    }
                }
            } else {
                output.message = dbDataResponse.message;
            }
        } else {
            output.message = "No Config found for partner " + model.partnerDisplayName;
            return output;
        }
    } catch (ex) {
        logger.logs(ex);
        saveErrorLog("sendLoanSanction", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};

async function sendLoanRepayment(model) {
    let output = {
        success: false,
        message: "",
        status: 501,
    };
    try {
        const conf = config.get(model.partnerCode.toLowerCase());

        if (!IsNullEmpty(conf)) {
            const dbDataResponse = await getDataForLoanRepayment(model.loanId, model.emiNumber);
            if (dbDataResponse.success) {
                const data = dbDataResponse.data;
                if (!IsNullEmpty(data.lenderLoanId)) {
                    //const emiDate = ((data.rePaymentActualDate.getMonth() + 1) < 10 ? '0' + (data.rePaymentActualDate.getMonth() + 1) : (data.rePaymentActualDate.getMonth() + 1)) + "/" + (data.rePaymentActualDate.getDate() < 10 ? '0' + data.rePaymentActualDate.getDate() : data.rePaymentActualDate.getDate()) + "/" + data.rePaymentActualDate.getFullYear();
                    const setResponse = await setDataForLoanRepayment(model, data, conf);
                    if (setResponse.success) {
                        let tokenResponse = null;
                        if (conf.isTokenGenration == true) {
                            tokenResponse = await getToken(conf, model);
                            if (!tokenResponse.success) {
                                output.message = tokenResponse.message;
                                return output;
                            }
                        }

                        const request = setResponse.data;
                        if (model.partnerCode.toLowerCase() == "aphe") {
                            const _body = JSON.stringify(request);
                            logger.logs("request", _body);
                            const authorization = tokenResponse.tokenType + " " + tokenResponse.token;
                            let header = {
                                "Content-Type": "application/json",
                                "Authorization": authorization,
                            }
                            let response = await fetch(model.url, {
                                method: "post",
                                body: _body,
                                headers: header,
                                timeout: 30000
                            }).then(function (u) {
                                return u.json();
                            }).then(function (json) {
                                return json;
                            }).catch((err) => {
                                console.error("err :", err);
                                return err;
                            });

                            logger.logs("response", response);
                            if (response.Status || response.Message == "This EMI is already Paid!") {
                                output = {
                                    success: true,
                                    message: response.Message,
                                    status: 200,
                                };
                            } else {
                                output.message = response.Message;
                                saveErrorLog("sendLoanRepayment", apiPath, model, response.Message);
                            }

                        } else if (model.partnerCode.toLowerCase() == "cent") {
                            const requestBody = {
                                "RequestHeader": conf.bodyConstants,
                                "RequestBody": request
                            }
                            const _body = JSON.stringify(requestBody);

                            logger.logs("request : ", _body);

                            const response = await fetch(model.url, {
                                method: "post",
                                body: _body,
                                headers: conf.headerConstants,
                            }).then(function (u) {
                                return u.json();
                            }).then(function (json) {
                                return json;
                            }).catch((err) => {
                                console.error("err :", err);
                                return err;
                            });
                            logger.logs("response : ", response);

                            if (!IsNullEmpty(response)) {
                                if (!IsNullEmpty(response.FundFinaCollectionResult)) {
                                    const result = response.FundFinaCollectionResult.split(":");

                                    if (result[0].toLowerCase() == "success") {
                                        output = {
                                            success: true,
                                            message: result[1],
                                            status: 200,
                                        };
                                    } else {
                                        output.message = result[1];
                                    }
                                } else {
                                    output.message = "Response parameters FundFinaCollectionResult Not Received";
                                }
                            } else {
                                output.message = "No response received";
                            }
                        } else {
                            const _body = JSON.stringify(request);
                            logger.logs("request", _body);
                            //logger.logs(model.url);
                            let response = await fetch(model.url, {
                                method: "post",
                                body: _body,
                                headers: conf.headerConstants,
                                timeout: 30000
                            }).then(function (u) {
                                return u.json();
                            }).then(function (json) {
                                return json;
                            }).catch((err) => {
                                console.error("err :", err);
                                return err;
                            });

                            logger.logs("response", response);
                            logger.logs("_body", _body);

                            if (model.partnerCode.toLowerCase() == "lbox") {
                                if (response.success == true || response.msg == "Some txIds are already exists in our records, Please remove them.") {
                                    output = {
                                        success: true,
                                        message: response.msg,
                                        status: 200,
                                    };
                                } else {
                                    output.message = response.msg;
                                    saveErrorLog("sendLoanRepayment", apiPath, model, response.msg);
                                }
                            } else {
                                if (response.Status) {
                                    output = {
                                        success: true,
                                        message: response.Message,
                                        status: 200,
                                    };
                                } else {
                                    output.message = response.Message;
                                    saveErrorLog("sendLoanRepayment", apiPath, model, response.Message);
                                }
                            }
                        }
                    } else {
                        output.message = setResponse.message;
                    }
                } else {
                    output.message = "No lenderLoanId found for partner " + model.partnerDisplayName;
                    return output;
                }
            } else {
                output.message = dbDataResponse.message;
            }
        } else {
            output.message = "No Config found for partner " + model.partnerDisplayName;
            return output;
        }
    } catch (ex) {
        logger.logs(ex);
        saveErrorLog("sendLoanRepayment", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};

// async function sendLoanDocuments(model) {
//     let output = {
//         success: false,
//         message: "Some Error Occurred",
//         status: 501,
//     };
//     try {
//         const conf = config.get(model.partnerCode.toLowerCase());
//         if (!IsNullEmpty(conf)) {
//             const dbDataResponse = await getDataForLoanDocuments(model.loanId);

//             if (dbDataResponse.success) {
//                 const data = dbDataResponse.data;
//                 if (model.partnerCode.toLowerCase() == "cent") {
//                     let uplodedFiles = [];
//                     if (!IsNullEmpty(data.lenderLoanId)) {
//                         const form = new FormData();
//                         form.append('ReferenceNo', data.lenderLoanId);
//                         form.append('UserID', conf.bodyConstants.UserID);
//                         form.append('Password', conf.bodyConstants.Password);

//                         if (!IsNullEmpty(data.profileDoc)) {
//                             let filePath = config.get("keys.proofImages") + data.profileDoc.replace("/ProofImages", "").replace("\ProofImages", "").trim();
//                             filePath = filePath.indexOf("\\") != -1 ? filePath.split("\\").join("/") : filePath;
//                             let extension = await getExtension(filePath);
//                             if (!extension.includes(".")) extension = "." + extension;
//                             const fileName = "SELFIE" + extension;
//                             uplodedFiles.push(fileName);
//                             form.append('image', fs.createReadStream(filePath), fileName);
//                         }

//                         if (!IsNullEmpty(data.panDocDetails)) {
//                             // const document = {
//                             //     DocumentType: "4",
//                             //     DocumentName: data.panDocDetails[0].fileName,
//                             //     DocumentNo: data.panNo
//                             // };
//                             let filePath = config.get("keys.proofImages") + data.panDocDetails[0].filePath.replace("/ProofImages", "").replace("\ProofImages", "").trim();
//                             filePath = filePath.indexOf("\\") != -1 ? filePath.split("\\").join("/") : filePath;
//                             filePath = filePath.indexOf("//") != -1 ? filePath.split("//").join("/") : filePath;
//                             let extension = await getExtension(filePath);
//                             if (!extension.includes(".")) extension = "." + extension;
//                             const fileName = "PAN" + extension;
//                             uplodedFiles.push(fileName);
//                             form.append('image', fs.createReadStream(filePath), fileName);
//                         }

//                         if (!IsNullEmpty(data.poaDetails)) {
//                             for (i = 0; i < data.poaDetails.length; i++) {
//                                 let fileName = "";
//                                 const poaType = IsNullEmpty(data.poaDetails[i].poaVerificationDocType) ? data.poaDetails[i].poaType : data.poaDetails[i].poaVerificationDocType;

//                                 switch (poaType) {
//                                     case "Driving Licence":
//                                         fileName = "POA-DL";
//                                         break;
//                                     case "Voter Id":
//                                         fileName = "POA-VoterIDF";
//                                         break;
//                                     case "Aadhaar Card":
//                                         fileName = "POA-AadhaarF";
//                                         break;
//                                     default:
//                                         fileName = "";
//                                         break;
//                                 }

//                                 if (fileName != "") {
//                                     let key = fileName;

//                                     let filePath = config.get("keys.proofImages") + data.poaDetails[i].docDetails[0].filePath.replace("/ProofImages", "").replace("\ProofImages", "").trim();
//                                     filePath = filePath.indexOf("\\") != -1 ? filePath.split("\\").join("/") : filePath;

//                                     let extension = getExtension(filePath);
//                                     if (!extension.includes(".")) extension = "." + extension;

//                                     fileName = fileName + extension;
//                                     uplodedFiles.push(fileName);
//                                     await form.append("image", fs.createReadStream(filePath), fileName);

//                                     if (data.poaDetails[i].docDetails.length > 1) {
//                                         switch (key) {
//                                             case "POA-VoterIDF":
//                                                 fileName = "POA-VoterIDB";
//                                                 break;
//                                             case "POA-AadhaarF":
//                                                 fileName = "POA-AadhaarB";
//                                                 break;
//                                             case "POA-VoterIDF":
//                                                 fileName = "POA-VoterIDB";
//                                                 break;
//                                             default:
//                                                 fileName = "";
//                                                 break;
//                                         }

//                                         //logger.logs("after filename ", fileName);
//                                         if (fileName != "") {
//                                             key = fileName;
//                                             let filePath = config.get("keys.proofImages") + data.poaDetails[i].docDetails[1].filePath.replace("/ProofImages", "").replace("\ProofImages", "").trim();
//                                             filePath = filePath.indexOf("\\") != -1 ? filePath.split("\\").join("/") : filePath;
//                                             let extension = getExtension(filePath);
//                                             if (!extension.includes(".")) extension = "." + extension;
//                                             fileName = fileName + extension;
//                                             uplodedFiles.push(fileName);
//                                             await form.append("image", fs.createReadStream(filePath), fileName);
//                                         }
//                                     }
//                                     i = -1;
//                                 }
//                             }
//                         }

//                         if (!IsNullEmpty(data.contractFile)) {
//                             let contractFilePath = config.get("keys.proofImages") + data.contractFile.replace("/ProofImages", "").replace("\ProofImages", "").trim();
//                             contractFilePath = contractFilePath.indexOf("\\") != -1 ? contractFilePath.split("\\").join("/") : contractFilePath;
//                             const fileName = "LOANAGREE.pdf";
//                             uplodedFiles.push(fileName);
//                             await form.append('image', fs.createReadStream(contractFilePath), fileName);
//                         }

//                         const formHeaders = form.getHeaders();
//                         //logger.logs("form :", form);
//                         try {
//                             const response = await fetch(model.url, { method: 'POST', body: form, headers: formHeaders, timeout: 90000 });
//                             logger.logs("response", response);

//                             const responseData = await response.json();
//                             //logger.logs("responseData", responseData);
//                             if (responseData.AllExternalImageUploadResult && responseData.AllExternalImageUploadResult.length > 0) {
//                                 let isUploadFailed = false;
//                                 for (let i = 0; i < uplodedFiles.length; i++) {
//                                     const fileResult = responseData.AllExternalImageUploadResult.filter((x) => x.FileName == uplodedFiles[i] && x.Status.toLowerCase() == "successful")
//                                     if (IsNullEmpty(fileResult)) {
//                                         output.message = "failed for " + uplodedFiles[i];
//                                         isUploadFailed = true;
//                                         i = uplodedFiles.length;
//                                     }
//                                 }
//                                 // for (doc of responseData.AllExternalImageUploadResult) {
//                                 //     if (doc.Status.toLowerCase() != "successful") {
//                                 //         output.message = "failed for " + doc.FileName;
//                                 //     }
//                                 // }
//                                 //logger.logs("isUploadFailed", isUploadFailed);
//                                 if (!isUploadFailed) {
//                                     output = {
//                                         success: true,
//                                         message: "documents uploaded successfully",
//                                         status: 200,
//                                     };
//                                 }
//                             }
//                         } catch (ex) {
//                             //logger.logs("saving centrum error");
//                             const filePath = config.get("keys.errorFilePath");
//                             const errorFileName = filePath + "Error_Centrum_" + model.loanId + ".txt";
//                             fs.appendFile(errorFileName, form, function (err) {
//                                 logger.logs(err);
//                             });
//                             saveErrorLog("sendLoanRepayment", apiPath, model, response);
//                             output.message = ex.message;
//                         }

//                     } else {
//                         output.message = "lender Loan Id does not exists";
//                     }
//                 } else if (model.partnerCode.toLowerCase() == "lbox") {
//                     let uplodedFiles = [];
//                     let responseData = null;
//                     if (!IsNullEmpty(data.lenderCustId)) {
//                         const url = model.url + "?lbUserId=" + data.lenderCustId;

//                         if (model.module == "makeLive") {
//                             if (!IsNullEmpty(data.panDocDetails)) {
//                                 const form = new FormData();
//                                 let filePath = config.get("keys.proofImages") + data.panDocDetails[0].filePath.replace("/ProofImages", "").replace("\ProofImages", "").trim();
//                                 filePath = filePath.indexOf("\\") != -1 ? filePath.split("\\").join("/") : filePath;
//                                 filePath = filePath.indexOf("//") != -1 ? filePath.split("//").join("/") : filePath;
//                                 let extension = await getExtension(filePath);
//                                 if (!extension.includes(".")) extension = "." + extension;
//                                 const fileName = "PAN" + extension;
//                                 uplodedFiles.push(fileName);

//                                 form.append('PAN', fs.createReadStream(filePath), fileName);
//                                 let formHeaders = form.getHeaders();
//                                 for (const [key, value] of Object.entries(conf.headerConstants)) {
//                                     formHeaders[key] = value;
//                                 }

//                                 try {
//                                     const response = await fetch(url, { method: 'POST', body: form, headers: formHeaders, timeout: 30000 });
//                                     logger.logs("response", response);
//                                     responseData = await response.json();
//                                     logger.logs("responseData", responseData);
//                                     if (!responseData.success) {
//                                         output.message = responseData.msg;
//                                         return output;
//                                     }
//                                 } catch (ex) {
//                                     logger.logs("error", ex);
//                                     output.message = responseData ? responseData.msg : ex.message;
//                                     return output;
//                                 }
//                             }

//                             if (!IsNullEmpty(data.poaDetails)) {
//                                 for (i = data.poaDetails.length - 1; i >= 0; i--) {
//                                     let fileName = "";
//                                     const poaType = IsNullEmpty(data.poaDetails[i].poaVerificationDocType) ? data.poaDetails[i].poaType : data.poaDetails[i].poaVerificationDocType;
//                                     switch (poaType) {
//                                         case "Voter Id":
//                                             fileName = "VOTER_ID";
//                                             break;
//                                         case "Aadhaar Card":
//                                             fileName = "AADHAR";
//                                             break;
//                                         case "Rent Agreement":
//                                             fileName = "RENT_AGREEMENT";
//                                             break;
//                                         case "Electricity Bill":
//                                             fileName = "ELECTRICITY_BILL";
//                                             break;
//                                         case "Water Bill":
//                                             fileName = "WATER_BILL";
//                                             break;
//                                         case "Gas Bill":
//                                             fileName = "GAS_BILL";
//                                             break;
//                                         case "Bank Statement":
//                                             fileName = "BANK_STATEMENT";
//                                             break;
//                                         case "Driving Licence":
//                                             fileName = "OTHER";
//                                             break;
//                                         default:
//                                             fileName = "";
//                                             break;
//                                     }

//                                     if (fileName != "") {
//                                         const form = new FormData();
//                                         let key = fileName;
//                                         let filePath = config.get("keys.proofImages") + data.poaDetails[i].docDetails[0].filePath.replace("/ProofImages", "").replace("\ProofImages", "").trim();
//                                         filePath = filePath.indexOf("\\") != -1 ? filePath.split("\\").join("/") : filePath;
//                                         let extension = getExtension(filePath);
//                                         if (!extension.includes(".")) extension = "." + extension;
//                                         let position = !IsNullEmpty(data.poaDetails[i].docDetails[0].position) ? "_" + data.poaDetails[i].docDetails[0].position : "_Front";
//                                         fileName = fileName + position + extension;
//                                         uplodedFiles.push(fileName);

//                                         await form.append(key, fs.createReadStream(filePath), fileName);
//                                         let formHeaders = form.getHeaders();
//                                         for (const [key, value] of Object.entries(conf.headerConstants)) {
//                                             formHeaders[key] = value;
//                                         }

//                                         try {
//                                             const response = await fetch(url, { method: 'POST', body: form, headers: formHeaders, timeout: 30000 });
//                                             const responseData = await response.json();
//                                             logger.logs("responseData", responseData);
//                                             if (!responseData.success) {
//                                                 output.message = responseData.msg;
//                                                 return output;
//                                             }
//                                         } catch (ex) {
//                                             output.message = responseData.msg;
//                                             return output;
//                                         }

//                                         if (data.poaDetails[i].docDetails.length > 1) {
//                                             const form1 = new FormData();
//                                             switch (key) {
//                                                 case "Voter Id":
//                                                     fileName = "VOTER_ID";
//                                                     break;
//                                                 case "Aadhaar Card":
//                                                     fileName = "AADHAR";
//                                                     break;
//                                                 case "Rent Agreement":
//                                                     fileName = "RENT_AGREEMENT";
//                                                     break;
//                                                 case "Electricity Bill":
//                                                     fileName = "ELECTRICITY_BILL";
//                                                     break;
//                                                 case "Water Bill":
//                                                     fileName = "WATER_BILL";
//                                                     break;
//                                                 case "Gas Bill":
//                                                     fileName = "GAS_BILL";
//                                                     break;
//                                                 case "Bank Statement":
//                                                     fileName = "BANK_STATEMENT";
//                                                     break;
//                                                 case "Driving Licence":
//                                                     fileName = "OTHER";
//                                                     break;
//                                                 default:
//                                                     fileName = "";
//                                                     break;
//                                             }
//                                             logger.logs("after filename ", fileName);
//                                             if (fileName != "") {
//                                                 key = fileName;
//                                                 let filePath = config.get("keys.proofImages") + data.poaDetails[i].docDetails[1].filePath.replace("/ProofImages", "").replace("\ProofImages", "").trim();
//                                                 filePath = filePath.indexOf("\\") != -1 ? filePath.split("\\").join("/") : filePath;
//                                                 let extension = getExtension(filePath);
//                                                 if (!extension.includes(".")) extension = "." + extension;
//                                                 position = !IsNullEmpty(data.poaDetails[i].docDetails[1].position) ? "_" + data.poaDetails[i].docDetails[1].position : "_back";
//                                                 fileName = fileName + position + extension;
//                                                 uplodedFiles.push(fileName);
//                                                 await form1.append("key", fs.createReadStream(filePath), fileName);
//                                                 let formHeaders = form1.getHeaders();
//                                                 for (const [key, value] of Object.entries(conf.headerConstants)) {
//                                                     formHeaders[key] = value;
//                                                 }
//                                                 try {
//                                                     const response = await fetch(url, { method: 'POST', body: form1, headers: formHeaders, timeout: 30000 });
//                                                     const responseData = await response.json();
//                                                     logger.logs("responseData", responseData);
//                                                     if (!responseData.success) {
//                                                         output.message = responseData.msg;
//                                                         return output;
//                                                     }
//                                                 } catch (ex) {
//                                                     output.message = responseData.msg;
//                                                     return output;
//                                                 }
//                                             }
//                                         }
//                                         i = -1;
//                                     }
//                                 }
//                             }

//                             //for Transactions
//                             let preApproval = await PreApprovals.findOne({ partnerId: mongoose.Types.ObjectId(data.partnerId), merchantCode: data.merchantCode, productId: data.productId });
//                             if (IsNullEmpty(preApproval)) {
//                                 preApproval = await PreApprovals.findOne({ partnerId: mongoose.Types.ObjectId(data.partnerId), merchantCode: data.merchantCode, trxnId: data.trxnId });
//                             }

//                             if (!IsNullEmpty(preApproval)) {
//                                 const body = { partnerId: data.partnerId, merchantCode: data.merchantCode, currentYear: preApproval.transactionYear, currentMonth: preApproval.transactionMonth };
//                                 const _body = JSON.stringify(body);
//                                 const creditURL = config.get("keys.creditApiUrl") + "/api/admin/getPartnerUserTransactions";
//                                 const response = await fetch(creditURL, {
//                                     method: "post",
//                                     body: _body,
//                                     headers: { "Content-Type": "application/json" },
//                                     timeout: 30000
//                                 }).then(function (u) {
//                                     return u.json();
//                                 }).then(function (json) {
//                                     return json;
//                                 }).catch((err) => {
//                                     console.error("err :", err);
//                                     return err;
//                                 });
//                                 logger.logs("creditresponse", response);
//                                 if (response.success) {
//                                     const fileName = data.merchantCode + "_" + preApproval.transactionYear + "_" + preApproval.transactionMonth.toString() + ".csv";

//                                     const dirPath = config.get("keys.tempPath");

//                                     if (!fs.existsSync(dirPath)) {
//                                         fs.mkdirSync(dirPath);
//                                     }

//                                     const filePath = dirPath + "/" + fileName;
//                                     logger.logs("filePath", filePath);
//                                     const csv = await csvConverter.json2csvAsync(response.data);
//                                     const csvResult = fs.writeFileSync(filePath, csv);

//                                     const form = new FormData();
//                                     form.append('OTHER', fs.createReadStream(filePath), fileName);
//                                     let formHeaders = form.getHeaders();
//                                     for (const [key, value] of Object.entries(conf.headerConstants)) {
//                                         formHeaders[key] = value;
//                                     }

//                                     try {
//                                         const response = await fetch(url, { method: 'POST', body: form, headers: formHeaders, timeout: 30000 });
//                                         logger.logs("response", response);
//                                         responseData = await response.json();
//                                         logger.logs("responseData", responseData);
//                                         logger.logs("url", url);
//                                         if (!responseData.success) {
//                                             output.message = responseData.message;
//                                             return output;
//                                         }
//                                     } catch (ex) {
//                                         logger.logs("error", ex);
//                                         output.message = responseData ? responseData.msg : ex.message;
//                                         return output;
//                                     }
//                                 }
//                             }
//                         } else if (model.module == "disbursement") {
//                             if (!IsNullEmpty(data.contractFile)) {
//                                 const form = new FormData();
//                                 let contractFilePath = config.get("keys.proofImages") + data.contractFile.replace("/ProofImages", "").replace("\ProofImages", "").trim();
//                                 contractFilePath = contractFilePath.indexOf("\\") != -1 ? contractFilePath.split("\\").join("/") : contractFilePath;
//                                 const fileName = "LOANAGREE.pdf";
//                                 uplodedFiles.push(fileName);
//                                 await form.append('LOAN_AGREEMENT', fs.createReadStream(contractFilePath), fileName);
//                                 let formHeaders = form.getHeaders();

//                                 for (const [key, value] of Object.entries(conf.headerConstants)) {
//                                     formHeaders[key] = value;
//                                 }

//                                 try {
//                                     const response = await fetch(url, { method: 'POST', body: form, headers: formHeaders, timeout: 30000 });
//                                     const responseData = await response.json();
//                                     logger.logs("responseData", responseData);
//                                     if (!responseData.success) {
//                                         output.message = responseData.msg;
//                                         return output;
//                                     }
//                                 } catch (ex) {
//                                     output.message = responseData.msg;
//                                     return output;
//                                 }
//                             }
//                         }

//                         output = {
//                             success: true,
//                             message: "Uploaded Successfully",
//                             status: 200,
//                         };
//                     } else {
//                         output.message = "Waiting for User Registration";
//                     }
//                 }
//             } else {
//                 output.message = dbDataResponse.message;
//             }
//         } else {
//             output.message = "No Config found for partner " + model.partnerDisplayName;
//             return output;
//         }
//     } catch (ex) {
//         logger.logs(ex);
//         saveErrorLog("sendLoanDocuments", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
//         output.message = ex.message;
//     }
//     return output;
// };

async function sendLoanDocuments(model) {
    let output = {
        success: false,
        message: "Some Error Occurred",
        status: 501,
    };
    try {
        const conf = config.get(model.partnerCode.toLowerCase());
        if (!IsNullEmpty(conf)) {
            const dbDataResponse = await getDataForLoanDocuments(model.loanId);

            if (dbDataResponse.success) {
                const data = dbDataResponse.data;
                if (model.partnerCode.toLowerCase() == "cent") {
                    let uploadResponse = null;
                    if (!IsNullEmpty(data.lenderLoanId)) {

                        if (!IsNullEmpty(data.profileDoc)) {
                            let filePath = data.profileDoc;
                            let extension = await getExtension(filePath);
                            if (!extension.includes(".")) extension = "." + extension;
                            const fileName = "SELFIE" + extension;
                            uploadResponse = await sendCentrumFile(fileName, filePath, data, conf, model.url);
                            logger.logs(uploadResponse);
                        }

                        if (!IsNullEmpty(data.panDocDetails)) {
                            // const document = {
                            //     DocumentType: "4",
                            //     DocumentName: data.panDocDetails[0].fileName,
                            //     DocumentNo: data.panNo
                            // };
                            let filePath = data.panDocDetails[0].filePath
                            let extension = await getExtension(filePath);
                            if (!extension.includes(".")) extension = "." + extension;
                            const fileName = "PAN" + extension;
                            uploadResponse = await sendCentrumFile(fileName, filePath, data, conf, model.url);
                            logger.logs(uploadResponse);
                            if (!uploadResponse.success) {
                                output.message = uploadResponse.message;
                                return output;
                            }

                        }

                        if (!IsNullEmpty(data.poaDetails)) {
                            for (let i = 0; i < data.poaDetails.length; i++) {
                                let fileName = "";
                                const poaType = IsNullEmpty(data.poaDetails[i].poaVerificationDocType) ? data.poaDetails[i].poaType : data.poaDetails[i].poaVerificationDocType;

                                switch (poaType) {
                                    case "Driving Licence":
                                        fileName = "POA-DL";
                                        break;
                                    case "Voter Id":
                                        fileName = "POA-VoterIDF";
                                        break;
                                    case "Aadhaar Card":
                                        fileName = "POA-AadhaarF";
                                        break;
                                    default:
                                        fileName = "";
                                        break;
                                }

                                if (fileName != "") {
                                    let key = fileName;

                                    let filePath = data.poaDetails[i].docDetails[0].filePath

                                    let extension = getExtension(filePath);
                                    if (!extension.includes(".")) extension = "." + extension;

                                    fileName = fileName + extension;
                                    uploadResponse = await sendCentrumFile(fileName, filePath, data, conf, model.url);
                                    logger.logs(uploadResponse);
                                    if (!uploadResponse.success) {
                                        output.message = uploadResponse.message;
                                        return output;
                                    }

                                    if (data.poaDetails[i].docDetails.length > 1) {
                                        switch (key) {
                                            case "POA-VoterIDF":
                                                fileName = "POA-VoterIDB";
                                                break;
                                            case "POA-AadhaarF":
                                                fileName = "POA-AadhaarB";
                                                break;
                                            case "POA-VoterIDF":
                                                fileName = "POA-VoterIDB";
                                                break;
                                            default:
                                                fileName = "";
                                                break;
                                        }

                                        //logger.logs("after filename ", fileName);
                                        if (fileName != "") {
                                            key = fileName;
                                            let filePath = data.poaDetails[i].docDetails[1].filePath
                                            let extension = getExtension(filePath);
                                            if (!extension.includes(".")) extension = "." + extension;
                                            fileName = fileName + extension;
                                            uploadResponse = await sendCentrumFile(fileName, filePath, data, conf, model.url);
                                            logger.logs(uploadResponse);
                                            if (!uploadResponse.success) {
                                                output.message = uploadResponse.message;
                                                return output;
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        if (!IsNullEmpty(data.contractFile)) {
                            let contractFilePath = data.contractFile
                            const fileName = "LOANAGREE.pdf";
                            logger.logs("sending contract file");
                            uploadResponse = await sendCentrumFile(fileName, contractFilePath, data, conf, model.url);
                            logger.logs(uploadResponse);
                            if (!uploadResponse.success) {
                                output.message = uploadResponse.message;
                                return output;
                            }
                        }

                        //KFS File send
                        // logger.logs("sending kfs file");
                        // if (!IsNullEmpty(data.kfsFile)) {
                        //     let kfsFilePath = data.kfsFile
                        //     const fileName = "KFS.pdf";
                        //     logger.logs("sending kfs file1");
                        //     uploadResponse = await sendCentrumFile(fileName, kfsFilePath, data, conf, model.url);
                        //     logger.logs("uploadResponse", uploadResponse);
                        //     if (!uploadResponse.success) {
                        //         output.message = uploadResponse.message;
                        //         return output;
                        //     }
                        // }

                        output = {
                            success: true,
                            message: "documents uploaded successfully",
                            status: 200,
                        };
                        return output;
                    } else {
                        output.message = "lender Loan Id does not exists";
                    }
                } else if (model.partnerCode.toLowerCase() == "lbox") {
                    let uplodedFiles = [];
                    let responseData = null;
                    if (!IsNullEmpty(data.lenderCustId)) {
                        const url = model.url + "?lbUserId=" + data.lenderCustId;

                        if (model.module == "makeLive") {
                            if (!IsNullEmpty(data.panDocDetails)) {
                                const form = new FormData();
                                let filePath = data.panDocDetails[0].filePath
                                let extension = await getExtension(filePath);
                                if (!extension.includes(".")) extension = "." + extension;
                                const fileName = "PAN" + extension;
                                uplodedFiles.push(fileName);

                                let downloadedFile = await downloadImageFromS3(filePath, "buffer");

                                form.append('PAN', downloadedFile.data, fileName);
                                let formHeaders = form.getHeaders();
                                for (const [key, value] of Object.entries(conf.headerConstants)) {
                                    formHeaders[key] = value;
                                }

                                try {
                                    const response = await fetch(url, { method: 'POST', body: form, headers: formHeaders, timeout: 30000 });
                                    logger.logs("response", response);
                                    responseData = await response.json();
                                    logger.logs("responseData", responseData);
                                    if (!responseData.success) {
                                        output.message = responseData.msg;
                                        return output;
                                    }
                                } catch (ex) {
                                    logger.logs("error", ex);
                                    output.message = responseData ? responseData.msg : ex.message;
                                    return output;
                                }
                            }

                            if (!IsNullEmpty(data.poaDetails)) {
                                for (i = data.poaDetails.length - 1; i >= 0; i--) {
                                    let fileName = "";
                                    const poaType = IsNullEmpty(data.poaDetails[i].poaVerificationDocType) ? data.poaDetails[i].poaType : data.poaDetails[i].poaVerificationDocType;
                                    switch (poaType) {
                                        case "Voter Id":
                                            fileName = "VOTER_ID";
                                            break;
                                        case "Aadhaar Card":
                                            fileName = "AADHAR";
                                            break;
                                        case "Rent Agreement":
                                            fileName = "RENT_AGREEMENT";
                                            break;
                                        case "Electricity Bill":
                                            fileName = "ELECTRICITY_BILL";
                                            break;
                                        case "Water Bill":
                                            fileName = "WATER_BILL";
                                            break;
                                        case "Gas Bill":
                                            fileName = "GAS_BILL";
                                            break;
                                        case "Bank Statement":
                                            fileName = "BANK_STATEMENT";
                                            break;
                                        case "Driving Licence":
                                            fileName = "OTHER";
                                            break;
                                        default:
                                            fileName = "";
                                            break;
                                    }

                                    if (fileName != "") {
                                        const form = new FormData();
                                        let key = fileName;
                                        let filePath = data.poaDetails[i].docDetails[0].filePath
                                        let extension = getExtension(filePath);
                                        if (!extension.includes(".")) extension = "." + extension;
                                        let position = !IsNullEmpty(data.poaDetails[i].docDetails[0].position) ? "_" + data.poaDetails[i].docDetails[0].position : "_Front";
                                        fileName = fileName + position + extension;
                                        uplodedFiles.push(fileName);

                                        let downloadedFile = await downloadImageFromS3(filePath, "buffer");

                                        form.append(key, downloadedFile.data, fileName);
                                        let formHeaders = form.getHeaders();
                                        for (const [key, value] of Object.entries(conf.headerConstants)) {
                                            formHeaders[key] = value;
                                        }

                                        try {
                                            const response = await fetch(url, { method: 'POST', body: form, headers: formHeaders, timeout: 30000 });
                                            const responseData = await response.json();
                                            logger.logs("responseData", responseData);
                                            if (!responseData.success) {
                                                output.message = responseData.msg;
                                                return output;
                                            }
                                        } catch (ex) {
                                            output.message = responseData.msg;
                                            return output;
                                        }

                                        if (data.poaDetails[i].docDetails.length > 1) {
                                            const form1 = new FormData();
                                            switch (key) {
                                                case "Voter Id":
                                                    fileName = "VOTER_ID";
                                                    break;
                                                case "Aadhaar Card":
                                                    fileName = "AADHAR";
                                                    break;
                                                case "Rent Agreement":
                                                    fileName = "RENT_AGREEMENT";
                                                    break;
                                                case "Electricity Bill":
                                                    fileName = "ELECTRICITY_BILL";
                                                    break;
                                                case "Water Bill":
                                                    fileName = "WATER_BILL";
                                                    break;
                                                case "Gas Bill":
                                                    fileName = "GAS_BILL";
                                                    break;
                                                case "Bank Statement":
                                                    fileName = "BANK_STATEMENT";
                                                    break;
                                                case "Driving Licence":
                                                    fileName = "OTHER";
                                                    break;
                                                default:
                                                    fileName = "";
                                                    break;
                                            }
                                            logger.logs("after filename ", fileName);
                                            if (fileName != "") {
                                                key = fileName;
                                                let filePath = data.poaDetails[i].docDetails[1].filePath;
                                                let extension = getExtension(filePath);
                                                if (!extension.includes(".")) extension = "." + extension;
                                                position = !IsNullEmpty(data.poaDetails[i].docDetails[1].position) ? "_" + data.poaDetails[i].docDetails[1].position : "_back";
                                                fileName = fileName + position + extension;
                                                uplodedFiles.push(fileName);

                                                let downloadedFile = await downloadImageFromS3(filePath, "buffer")

                                                form1.append("key", downloadedFile.data, fileName);

                                                let formHeaders = form1.getHeaders();
                                                for (const [key, value] of Object.entries(conf.headerConstants)) {
                                                    formHeaders[key] = value;
                                                }
                                                try {
                                                    const response = await fetch(url, { method: 'POST', body: form1, headers: formHeaders, timeout: 30000 });
                                                    const responseData = await response.json();
                                                    logger.logs("responseData", responseData);
                                                    if (!responseData.success) {
                                                        output.message = responseData.msg;
                                                        return output;
                                                    }
                                                } catch (ex) {
                                                    output.message = responseData.msg;
                                                    return output;
                                                }
                                            }
                                        }
                                        i = -1;
                                    }
                                }
                            }

                            //for Transactions
                            let preApproval = await PreApprovals.findOne({ partnerId: mongoose.Types.ObjectId(data.partnerId), merchantCode: data.merchantCode, productId: data.productId });
                            if (IsNullEmpty(preApproval)) {
                                preApproval = await PreApprovals.findOne({ partnerId: mongoose.Types.ObjectId(data.partnerId), merchantCode: data.merchantCode, trxnId: data.trxnId });
                            }

                            if (!IsNullEmpty(preApproval)) {
                                const body = { partnerId: data.partnerId, merchantCode: data.merchantCode, currentYear: preApproval.transactionYear, currentMonth: preApproval.transactionMonth };
                                const _body = JSON.stringify(body);
                                const creditURL = config.get("keys.creditApiUrl") + "/api/admin/getPartnerUserTransactions";
                                const response = await fetch(creditURL, {
                                    method: "post",
                                    body: _body,
                                    headers: { "Content-Type": "application/json" },
                                    timeout: 30000
                                }).then(function (u) {
                                    return u.json();
                                }).then(function (json) {
                                    return json;
                                }).catch((err) => {
                                    console.error("err :", err);
                                    return err;
                                });
                                logger.logs("creditresponse", response);
                                if (response.success) {
                                    const fileName = data.merchantCode + "_" + preApproval.transactionYear + "_" + preApproval.transactionMonth.toString() + ".csv";

                                    const dirPath = config.get("keys.tempPath");

                                    if (!fs.existsSync(dirPath)) {
                                        fs.mkdirSync(dirPath);
                                    }

                                    const filePath = dirPath + "/" + fileName;
                                    logger.logs("filePath", filePath);
                                    const csv = await csvConverter.json2csvAsync(response.data);
                                    const csvResult = fs.writeFileSync(filePath, csv);

                                    const form = new FormData();
                                    form.append('OTHER', fs.createReadStream(filePath), fileName);
                                    let formHeaders = form.getHeaders();
                                    for (const [key, value] of Object.entries(conf.headerConstants)) {
                                        formHeaders[key] = value;
                                    }

                                    try {
                                        const response = await fetch(url, { method: 'POST', body: form, headers: formHeaders, timeout: 30000 });
                                        logger.logs("response", response);
                                        responseData = await response.json();
                                        logger.logs("responseData", responseData);
                                        logger.logs("url", url);
                                        if (!responseData.success) {
                                            output.message = responseData.message;
                                            return output;
                                        }
                                    } catch (ex) {
                                        logger.logs("error", ex);
                                        output.message = responseData ? responseData.msg : ex.message;
                                        return output;
                                    }
                                }
                            }
                        } else if (model.module == "disbursement") {
                            if (!IsNullEmpty(data.contractFile)) {
                                const form = new FormData();
                                let contractFilePath = data.contractFile

                                const fileName = "LOANAGREE.pdf";
                                uplodedFiles.push(fileName);

                                let downloadedFile = await downloadImageFromS3(contractFilePath, "buffer")

                                await form.append('LOAN_AGREEMENT', downloadedFile.data, fileName);
                                let formHeaders = form.getHeaders();

                                for (const [key, value] of Object.entries(conf.headerConstants)) {
                                    formHeaders[key] = value;
                                }

                                try {
                                    const response = await fetch(url, { method: 'POST', body: form, headers: formHeaders, timeout: 30000 });
                                    const responseData = await response.json();

                                    if (!responseData.success) {
                                        output.message = responseData.msg;
                                        return output;
                                    }
                                } catch (ex) {
                                    output.message = ex;
                                    return output;
                                }
                            }

                            //KFS File Send
                            if (!IsNullEmpty(data.kfsFile)) {
                                const form = new FormData();
                                let kfsFilePath = data.kfsFile;

                                const fileName = "KFS.pdf";
                                uplodedFiles.push(fileName);

                                let downloadedFile = await downloadImageFromS3(kfsFilePath, "buffer")

                                await form.append('OTHER', downloadedFile.data, fileName);
                                let formHeaders = form.getHeaders();

                                for (const [key, value] of Object.entries(conf.headerConstants)) {
                                    formHeaders[key] = value;
                                }

                                try {
                                    const response = await fetch(url, { method: 'POST', body: form, headers: formHeaders, timeout: 30000 });
                                    const responseData = await response.json();

                                    if (!responseData.success) {
                                        output.message = responseData.msg;
                                        return output;
                                    }
                                } catch (ex) {
                                    output.message = ex;
                                    return output;
                                }
                            }
                        }

                        output = {
                            success: true,
                            message: "Uploaded Successfully",
                            status: 200,
                        };
                    } else {
                        output.message = "Waiting for User Registration";
                    }
                }
            } else {
                output.message = dbDataResponse.message;
            }
        } else {
            output.message = "No Config found for partner " + model.partnerDisplayName;
            return output;
        }
    } catch (ex) {
        logger.logs(ex);
        saveErrorLog("sendLoanDocuments", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};

async function sendCentrumFile(fileName, filePath, data, conf, url) {
    logger.logs("fileName", fileName);
    logger.logs("filePath", filePath);
    const model = { fileName, filePath };
    let output = {
        success: false,
        message: "Some Error Occurred",
        status: 501,
    };

    const form = new FormData();
    try {
        let uplodedFiles = [];

        form.append('ReferenceNo', data.lenderLoanId);
        form.append('UserID', conf.bodyConstants.UserID);
        form.append('Password', conf.bodyConstants.Password);
        uplodedFiles.push(fileName);

        if (filePath) {
            let downloadedFile = await downloadImageFromS3(filePath)

            if (downloadedFile.success)
                form.append('image', downloadedFile.data, fileName);
        }

        const formHeaders = form.getHeaders();
        //logger.logs("form :", form);
        logger.logs("url", url);
        const response = await fetch(url, { method: 'POST', body: form, headers: formHeaders, timeout: 90000 });
        logger.logs("response", response);

        const responseData = await response.json();
        //logger.logs("responseData", responseData);
        if (responseData.AllExternalImageUploadResult && responseData.AllExternalImageUploadResult.length > 0) {
            let isUploadFailed = false;
            for (let i = 0; i < uplodedFiles.length; i++) {
                const fileResult = responseData.AllExternalImageUploadResult.filter((x) => x.FileName == uplodedFiles[i] && x.Status.toLowerCase() == "successful")
                if (IsNullEmpty(fileResult)) {
                    output.message = "failed for " + uplodedFiles[i];
                    isUploadFailed = true;
                    i = uplodedFiles.length;
                }
            }
            if (!isUploadFailed) {
                output = {
                    success: true,
                    message: "documents uploaded successfully",
                    status: 200,
                };
            }

        }

    } catch (ex) {
        //logger.logs("saving centrum error");
        const filePath = config.get("keys.errorFilePath");
        const errorFileName = filePath + "Error_Centrum_" + model.loanId + ".txt";
        // fs.appendFile(errorFileName, form, function (err) {
        //     logger.logs(err);
        // });
        logger.logs("ex", ex);
        saveErrorLog("sendCentrumFile", apiPath, model, response);
        output.message = ex.message;
    }
    return output;
};

async function saveMessageQueue(model) {
    try {
        if (model.success || (!model.success && model.isRetryOffline)) {

            let messageQueue = null;
            if (model.apiType == "loanRepayment") {
                messageQueue = await MessageQueue.findOne({ loanId: mongoose.Types.ObjectId(model.loanId), type: "API", subType: model.apiType, emiNumber: model.emiNumber, isActive: true });
            } else {
                messageQueue = await MessageQueue.findOne({ loanId: mongoose.Types.ObjectId(model.loanId), type: "API", subType: model.apiType, module: model.module, isActive: true });
            }
            // logger.logs("model", model);
            // logger.logs("messageQueue", messageQueue);
            if (IsNullEmpty(messageQueue)) {
                messageQueue = new MessageQueue({
                    loanId: mongoose.Types.ObjectId(model.loanId),
                    source: model.source,
                    module: model.module,
                    type: "API",
                    partnerType: model.partnerType,
                    emiNumber: (model.apiType == "loanRepayment") ? model.emiNumber : 0,
                    subType: model.apiType,
                    noOfAttempts: 1,
                    requestModel: model,
                    errorMsg: !(model.success) ? model.errorMsg : "",
                    request: !(model.request) ? model.request : "",
                    response: !(model.response) ? model.response : "",
                    isActive: !model.success,
                    insertedOn: Date.now(),
                    updateOn: Date.now()
                });
            } else {
                messageQueue.noOfAttempts = messageQueue.noOfAttempts + 1;
                messageQueue.requestModel = model;
                messageQueue.errorMsg = !(model.success) ? model.errorMsg : "";
                messageQueue.isActive = !model.success;
                messageQueue.updateOn = Date.now();
                messageQueue.request = !(model.request) ? model.request : "";
                messageQueue.response = !(model.response) ? model.response : "";
            }
            await messageQueue.save();

            if (!model.success && model.isRetryOffline) {
                const lenderDetails = await PartnerMaster.findById(model.lenderId);
                if (lenderDetails) {
                    const subject = model.apiType + " request failed for Lending Partner " + lenderDetails.displayName;
                    const message = model.apiType + " request failed " + " <br/> " + "Error Msg : " + model.errorMsg;
                    postdisbursal.sendMailForFailure(subject, message);
                }
            }

            if (model.success && model.module == "makeLive" && (model.apiType == "loanApplication" || model.apiType == "loanDisbursement") && model.isLoanApprovalRequired == false) {
                await LoanInfo.findOneAndUpdate({ _id: mongoose.Types.ObjectId(model.loanId), status: "Lender Approval" }, {
                    $set: { status: "Lender Assigned", "lenderDetails.0.errorMsg": null, "lenderDetails.0.errorDate": null, adminModifiedOn: Date.now() },
                    $push: {
                        flowDetails: {
                            changedBy: null,
                            changedByRole: "System",
                            Step: "Lender Assigned",
                            insertedOn: Date.now()
                        }
                    }
                }, { new: true, useFindAndModify: false })
            } else if (model.module == "makeLive" && (model.apiType == "loanApplication" || model.apiType == "loanDisbursement")) {
                await LoanInfo.findByIdAndUpdate({ _id: model.loanId }, { $set: { "lenderDetails.0.errorMsg": model.errorMsg, "lenderDetails.0.errorDate": Date.now(), adminModifiedOn: Date.now() } });
            }
        } else {


            const loanInfo = await LoanInfo.findById(model.loanId).select({ userId: 1 });

            const partnerAllocationRejection = new PartnerAllocationRejection({
                partnerId: mongoose.Types.ObjectId(model.lenderId),
                userId: mongoose.Types.ObjectId(loanInfo.userId),
                loanId: mongoose.Types.ObjectId(model.loanId),
                apiType: model.apiType,
                errorMsg: model.errorMsg,
                insertedOn: Date.now()
            });

            await partnerAllocationRejection.save();
        }
    }
    catch (ex) {
        logger.logs(ex);
        saveErrorLog("saveMessageQueue", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }
};

async function setDataForLoanApplication(model, data, conf) {
    let output = {
        data: null,
        success: false,
        message: "",
        status: 501,
    };
    try {
        // const LoanRegMonth = data.loanReqDate.getMonth() + 1;
        // const LoanRegDate = (data.loanReqDate.getDate() < 10 ? ('0' + data.loanReqDate.getDate().toString()) : data.loanReqDate.getDate().toString()) + "/" + (LoanRegMonth < 10 ? ('0' + LoanRegMonth) : LoanRegMonth) + "/" + data.loanReqDate.getFullYear().toString();
        // const dobMonth = data.dob.getMonth() + 1;
        // BirthDate: (data.dob.getDate() < 10 ? ("0" + data.dob.getDate().toString()) : data.dob.getDate().toString()) + "/" + (dobMonth < 10 ? ("0" + dobMonth.toString()) : dobMonth.toString()) + "/" + data.dob.getFullYear().toString(),
        let request = {};

        if (model.partnerCode.toLowerCase() == "aphe") {
            request = {
                BorrowerExternalID: data.userId,
                UserID: conf.tokenParameters.UserName,
                BranchCode: conf.constantParameters.branchCode,
                LoanRegDate: getDMYFormattedDate(data.loanReqDate, "/"),
                AppFName: data.firstName,
                AppFatherFName: "NA",
                PanNumber: data.panNo,
                Signature: null,
                AadharNumber: "NA",
                BirthDate: getDMYFormattedDate(data.dob, "/"),
                LoanType: "unsecured",
                LoanProductID: 4,
                Education: "NA",
                MarketingExecutive: conf.constantParameters.marketingExceutive,
                Mobile: data.mobileNo[0],
                IncomeSource: "NA",
                SourceType: "NA",
                Duration: 0,
                NetIncome: 0,
                CreditBureau: "NA",
                CreditRatingAgency: "NA"
            };

            if (!IsNullEmpty(data.questionnairreDetails.gender)) {
                request.Gender = (data.questionnairreDetails.gender.toLowerCase() == "transgender") ? "other" : data.questionnairreDetails.gender.toLowerCase();
            } else {
                request.Gender = "NA";
            }

            if (!IsNullEmpty(data.questionnairreDetails.maritalStatus)) {
                request.MaritalStatus = (data.questionnairreDetails.maritalStatus.toLowerCase() == "single") ? "unmarried" : data.questionnairreDetails.maritalStatus.toLowerCase();
            }

            if (!IsNullEmpty(data.profileDoc)) {
                let filePath = data.profileDoc[0].filePath
                let downloadedFile = await downloadImageFromS3(filePath);
                request.Photo = downloadedFile.data || "";
            }

            let documentList = [];

            if (!IsNullEmpty(data.panDocDetails)) {
                const document = {
                    DocumentType: "4",
                    DocumentName: data.panDocDetails[0].fileName,
                    DocumentNo: data.panNo
                };
                let filePath = data.panDocDetails[0].filePath;

                let downloadedFile = await downloadImageFromS3(filePath);
                document.DocumentImg = downloadedFile.data;
                documentList.push(document);
            }

            if (!IsNullEmpty(data.poaDetails)) {
                for (i = 0; i < data.poaDetails.length; i++) {
                    let docType = null;
                    switch (data.poaDetails[i].poaType) {
                        case "Driving Licence":
                            docType = "2";
                            break;
                        case "Voter Id":
                            docType = "5";
                            break;
                        case "Electricity Bill":
                            docType = "1";
                            break;
                        case "Bank Statement":
                            docType = "9";
                            break;
                        case "Ration Card":
                            docType = "3";
                            break;
                        default:
                            docType = "0";
                            break;
                    }

                    if (docType != "0") {
                        const document = {
                            DocumentType: docType,
                            DocumentName: data.poaDetails[i].docDetails[0].fileName,
                            DocumentNo: data.poaDetails[i].poaNo
                        };

                        let filePath = data.poaDetails[i].docDetails[0].filePath
                        let downloadedFile = await downloadImageFromS3(filePath);

                        document.DocumentImg = downloadedFile.data;
                        documentList.push(document);
                    }
                }
            }

            if (documentList.length > 0) {
                request.DocumentList = documentList;
            }
        } else if (model.partnerCode.toLowerCase() == "cent") {
            const getBorrwingPartnerDetails = await getBorrowingPartnerDetails(model.loanId);
            if (getBorrwingPartnerDetails.success && getBorrwingPartnerDetails.processingCharge) {
                let gender = "";
                let maritalStatus = "";
                let poaDocType = "";
                let poaNo = "";
                let frequency = "";

                //const disbursalCharge = await getDisbursalCharges(data.loanAmount, getBorrwingPartnerDetails.processingCharge);

                if (!IsNullEmpty(data.questionnairreDetails.gender)) {
                    switch (data.questionnairreDetails.gender.toLowerCase()) {
                        case "male":
                            gender = "M";
                            break;
                        case "female":
                            gender = "F";
                            break;
                        case "transgender":
                            gender = "T";
                            break;
                    }
                }

                if (!IsNullEmpty(data.questionnairreDetails.maritalStatus)) {
                    switch (data.questionnairreDetails.maritalStatus.toLowerCase()) {
                        case "married":
                            maritalStatus = "M";
                            break;
                        case "unmarried":
                            maritalStatus = "U";
                            break;
                        case "Widow":
                            maritalStatus = "W";
                            break;
                        case "Divorced":
                            maritalStatus = "D";
                            break;
                    }
                }

                if (!IsNullEmpty(data.frequency)) {
                    switch (data.frequency.toLowerCase()) {
                        case "daily":
                            frequency = "D";
                            break;
                        case "weekly":
                            frequency = "W";
                            break;
                        case "monthly":
                            frequency = "M";
                            break;
                        case "fortnightly":
                            frequency = "F";
                            break;
                    }
                }

                if (data.poaDetails && data.poaDetails.length > 0) {
                    poaDocType = data.poaDetails[0].poaType;
                    poaNo = data.poaDetails[0].poaNo;
                }

                request = {
                    PreliminaryDetails: {
                        MobileNumber: data.mobileNo[0],
                        IsMobileNumberDifferentThenLinkedtoAadhaar: "",
                        FullName: getName(data.firstName, data.middleName, data.lastName),
                        MobileNumberLinkedWithAadhaar: "",
                        Gender: gender,
                        DateOfBirth: (getDMYFormattedDate(data.dob, "-")),
                        MaritalStatus: maritalStatus,
                        EmailID: data.emailId[0],
                        NumberOfDependants: ""
                    },
                    KYCDetails: {
                        PANCardNumber: data.panNo.toUpperCase(),
                        POADocumentType: poaDocType,
                        POANumber: poaNo,
                        AadhaarDocumentNumber: ""
                    },
                    ResidenceAddressDetails: {
                        Address: await getAddress(data.addressDetails[0].add1, data.addressDetails[0].add2, data.addressDetails[0].add3),
                        Pincode: data.addressDetails[0].pincode,
                        PincodeClassification: "",
                        Area: "",
                        CityDistrict: data.addressDetails[0].city,
                        State: data.addressDetails[0].state,
                        ResidenceOwnershipType: !IsNullEmpty(data.questionnairreDetails.homeOwnershipType) ? data.questionnairreDetails.homeOwnershipType : "",
                        SinceWhenAreYouStayingHere: ""
                    },
                    BusinessDetails: {
                        BusinessAddress: !IsNullEmpty(data.shopDetails[data.shopDetails.length - 1].shopAddress) ? data.shopDetails[data.shopDetails.length - 1].shopAddress : "",
                        Pincode: !IsNullEmpty(data.shopDetails[data.shopDetails.length - 1].pincode) ? data.shopDetails[data.shopDetails.length - 1].pincode : "",
                        PincodeClassification: "C",
                        CityDistrict: !IsNullEmpty(data.shopDetails[data.shopDetails.length - 1].city) ? data.shopDetails[data.shopDetails.length - 1].city : "",
                        State: !IsNullEmpty(data.shopDetails[data.shopDetails.length - 1].state) ? data.shopDetails[data.shopDetails.length - 1].state : "",
                        BusinessPremiseOwnershipType: !IsNullEmpty(data.shopDetails[data.shopDetails.length - 1].shopOwnershipType) ? data.shopDetails[data.shopDetails.length - 1].shopOwnershipType : "",
                        SinceWhenAreYouDoingBusinessHere: !IsNullEmpty(data.shopDetails[data.shopDetails.length - 1].shopVintage) ? data.shopDetails[data.shopDetails.length - 1].shopVintage : "",
                        NoOfYearsInThisBusiness: "",
                        StoreType: !IsNullEmpty(data.shopDetails[data.shopDetails.length - 1].shopType) ? data.shopDetails[data.shopDetails.length - 1].shopType : "",
                        StoreName: !IsNullEmpty(data.shopDetails[data.shopDetails.length - 1].shopName) ? data.shopDetails[data.shopDetails.length - 1].shopName : "",
                        HowDoYouDoYourSaleCollectionsInBusiness: "",
                        MonthlyTurnover: "",
                        GoogleMapAddress: "",
                        LatLongOfTheAddress: ""
                    },
                    BankDetails: {
                        BankAccountType: (data.accountType) ? (data.accountType.toLowerCase() == "saving" ? "S" : "C") : null,
                        BankName: data.bankName ? data.bankName : null,
                        AccountNumber: data.accountNo ? data.accountNo : null,
                        IFSCCode: data.ifsc ? data.ifsc : null,
                        AccountOperationalScince: ""
                    },
                    OtherDetails: {
                        MonthlyFamilyExpense: "",
                        AnyOtherIncome: "",
                        PurposeOfLoan: data.purpose,
                        ExistingLoan: "1",///?
                        TypeOfLoan: data.loanType,
                        ExistingCreditCard: ""
                    },
                    CustomerLoanDetails: {
                        LoanAmountRequired: data.loanAmount,
                        LoanTenureRequired: data.tenure,
                        ROIfixed: data.interestRate
                    },
                    InsuranceDetails: {
                        CustomerOptedForInsurance: "",
                        NomineeName: "",
                        RelationshipWithNominee: "",
                        NomineeGender: "",
                        NomineeDOB: ""
                    },
                    CAMDetails: {
                        NBTScore: 0,
                        PNBIncomeForLast24months: 0,
                        PNBGTVForLast24months: "0",
                        NBTRecommendedLoanAmount: "0",
                        NBTrecommendedTenure: "0",
                        CMLApprovedLoanAmount: data.loanAmount,
                        CMLApprovedTenure: data.tenure,
                        ROI: data.interestRate,
                        EstimatedDisbursementDate: getDMYFormattedDate(data.disbursedOn, "-"),
                        EstimatedCollectionDate: getDMYFormattedDate(data.firstEmiDate, "-"),
                        EMI: data.firstEmiAmount,
                        EstimatedBPIAmount: "0",
                        ProcessingFees: data.totalCharges,
                        CommentsAddedByCMLInCAM: "",
                        LoanApprovedRejected: "",
                        RejectedReason: "",
                        ApprovedPaymentFrequency: frequency
                    },
                    BranchInformation: {
                        BranchCode: "0000"
                    }
                }

                output = {
                    data: request,
                    success: true,
                    message: "Set Successfully",
                    status: 200,
                };

            } else {
                output.message = "processingCharge not found";
            }
        } else if (model.partnerCode.toLowerCase() == "lbox") {
            let title = "Mr.";
            if (!IsNullEmpty(data.questionnairreDetails.gender)) {
                switch (data.questionnairreDetails.gender.toLowerCase()) {
                    case "male":
                        title = "Mr.";
                        break;
                    case "female":
                        title = "Ms.";
                        break;
                    default:
                        title = "Mr.";
                        break;
                }
            }

            const diff = new DateDiff(new Date(Date.now()), new Date(data.onboardedDate));
            request = {
                email: data.emailId[0],
                title: title,
                firstName: getName(data.firstName, data.middleName, ""),
                lastName: data.lastName,
                mobile: data.mobileNo[0],
                pan: data.panNo,
                dob: data.dob ? (new Date(data.dob.getFullYear(), data.dob.getMonth(), data.dob.getDate(), 20, 30, 30, 0)).getTime() : null,
                street1: data.addressDetails[0].add1,
                city: data.addressDetails[0].city,
                pin: data.addressDetails[0].pincode,
                ip: data.ipAddress,
                ua: "Fundfina",
                loanType: model.lenderProductType,
                timestamp: (new Date(data.insertedOn)).getTime(),
                roi: data.interestRate,
                loanAmount: data.loanAmount,
                loanTenure: data.tenure,
                durationInDays: data.tenure,
                appId: data.loanNo,
                assessment: null,
                companyName: data.shopDetails[data.shopDetails.length - 1].shopName,
                totalCurrentWorkExpInYrs: diff.years(),
                residenceType: (data.shopDetails[data.shopDetails.length - 1].shopOwnershipType) ? (data.shopDetails[data.shopDetails.length - 1].shopOwnershipType.toLowerCase() == "rent" ? 80 : 38) : null,
            }
            const street2 = ((data.addressDetails[0].add2 ? data.addressDetails[0].add2 : "") + (data.addressDetails[0].add3 ? data.addressDetails[0].add3 : "")).trim();

            if (street2 != "") {
                request.street2 = street2;
            }

            if (!IsNullEmpty(data.middleName)) {
                request.fathersName = data.middleName;
            }

            if (data.userDeviceInfo != null) {
                const userDeviceIdentification = [];
                if (data.userDeviceInfo.browserName && data.userDeviceInfo.browserName != null && data.userDeviceInfo.browserName != "" && data.userDeviceInfo.browserName != "none") userDeviceIdentification.push(data.userDeviceInfo.browserName);

                if (data.userDeviceInfo.browserVersion && data.userDeviceInfo.browserVersion != null && data.userDeviceInfo.browserVersion != "" && data.userDeviceInfo.browserVersion != "none") userDeviceIdentification.push(data.userDeviceInfo.browserVersion);

                if (data.userDeviceInfo.fullBrowserVersion && data.userDeviceInfo.fullBrowserVersion != null && data.userDeviceInfo.fullBrowserVersion != "" && data.userDeviceInfo.fullBrowserVersion != "none") userDeviceIdentification.push(data.userDeviceInfo.fullBrowserVersion);

                if (data.userDeviceInfo.mobileVendor && data.userDeviceInfo.mobileVendor != null && data.userDeviceInfo.mobileVendor != "" && data.userDeviceInfo.mobileVendor != "none") userDeviceIdentification.push(data.userDeviceInfo.mobileVendor);

                if (data.userDeviceInfo.mobileModel && data.userDeviceInfo.mobileModel != null && data.userDeviceInfo.mobileModel != "" && data.userDeviceInfo.mobileModel != "none") userDeviceIdentification.push(data.userDeviceInfo.mobileModel);

                if (data.userDeviceInfo.osName && data.userDeviceInfo.osName != null && data.userDeviceInfo.osName != "" && data.userDeviceInfo.osName != "none") userDeviceIdentification.push(data.userDeviceInfo.osName);

                if (data.userDeviceInfo.osVersion && data.userDeviceInfo.osVersion != null && data.userDeviceInfo.osVersion != "" && data.userDeviceInfo.osVersion != "none") userDeviceIdentification.push(data.userDeviceInfo.osVersion);

                if (userDeviceIdentification.length > 0) request.ua = userDeviceIdentification.join();
            }
        } else {
            logger.logs("inside else");
            request = {
                kycDetails: {
                    firstName: data.firstName,
                    middleName: data.middleName,
                    lastName: data.lastName,
                    panNo: data.panNo,
                    dob: getDMYFormattedDate(data.dob, "/"),
                    gender: data.questionnairreDetails.gender,
                    maritalStatus: data.questionnairreDetails.maritalStatus,
                    email: data.emailId,
                    mobileNo: data.mobileNo,
                    kycStatus: data.kycStatus,
                    addressDetails: [],
                    shopDetails: [],
                    documents: [],
                    photoMatch: data.isPhotoMatch,
                    photoMatchPercentage: data.photoMatchPercentage

                },
                loanInformation: {
                    loanNo: data.loanNo,
                    loanCycle: data.loanCycle,
                    amount: data.loanAmount,
                    tenure: data.tenure,
                    frequency: data.frequency,
                    purpose: data.purpose,
                    description: data.description,
                    installmentAmount: data.firstEmiAmount,
                    interestRate: data.interestRate
                },
                questionnairre: {
                    recommended: null,
                    whatsApp: null,
                    gender: null,
                    maritalStatus: null,
                    children: null,
                    degree: null,
                    monthlyIncome: null,
                    monthlyOutgoing: null,
                    jobType: null,
                    homeOwnershipType: null,
                    vehicle: null,
                    purchasedInOneYear: null,
                    recommendedBy: null
                },
                loanHistory: data.loanHistory,
                trueScore: {
                    value: data.trueScore,
                    month: data.trueScoreMonth,
                    year: data.trueScoreYear,
                    monthlyTransactions: data.monthlyTransactions
                },
                digilocker: {
                    execution_request_id: null,
                    created_at: null,
                    updated_at: null,
                    reference_id: null,
                    status: null
                }
            }

            if (!IsNullEmpty(data.digilocker)) {
                request.digilocker.execution_request_id = !IsNullEmpty(data.digilocker.execution_request_id) ? data.digilocker.execution_request_id : null;
                request.digilocker.created_at = !IsNullEmpty(data.digilocker.created_at) ? getDMYFormattedDate(data.digilocker.created_at, "/") : null;
                request.digilocker.updated_at = !IsNullEmpty(data.digilocker.updated_at) ? getDMYFormattedDate(data.digilocker.updated_at, "/") : null;
                request.digilocker.reference_id = !IsNullEmpty(data.digilocker.reference_id) ? data.digilocker.reference_id : null;
                request.digilocker.status = !IsNullEmpty(data.digilocker.status) ? data.digilocker.status : null;
            }

            //Address Details
            if (!IsNullEmpty(data.addressDetails)) {
                for (const addressDetail of data.addressDetails) {
                    const address = {
                        addType: addressDetail.type ? addressDetail.type : "Permanent",
                        add1: addressDetail.add1,
                        add2: addressDetail.add2,
                        add3: addressDetail.add3,
                        city: addressDetail.city,
                        state: addressDetail.state,
                        country: addressDetail.country,
                        pincode: addressDetail.pincode
                    };
                    request.kycDetails.addressDetails.push(address);
                }
            }

            //Shop Details
            if (!IsNullEmpty(data.shopDetails)) {
                for (const shopDetail of data.shopDetails) {
                    const shop = {
                        shopName: shopDetail.shopName,
                        shopLocation: shopDetail.shopLocation,
                        shopType: shopDetail.shopType,
                        shopVintage: shopDetail.shopVintage,
                        shopOwnershipType: shopDetail.shopOwnershipType,
                        shopAddress: shopDetail.shopAddress,
                        city: shopDetail.city,
                        state: shopDetail.state,
                        pincode: shopDetail.pincode,
                        companyType: shopDetail.companyType,
                        businessVintage: shopDetail.businessVintage,
                        contactNo: shopDetail.contactNo,
                    }
                    request.kycDetails.shopDetails.push(shop);
                }
            }

            //Qustinnairre
            if (!IsNullEmpty(data.questionnairreDetails)) {
                request.questionnairre.recommended = data.questionnairreDetails.recommended;
                request.questionnairre.whatsApp = data.questionnairreDetails.whatsApp;
                request.questionnairre.gender = data.questionnairreDetails.gender;
                request.questionnairre.maritalStatus = data.questionnairreDetails.maritalStatus;
                request.questionnairre.children = data.questionnairreDetails.children;
                request.questionnairre.degree = data.questionnairreDetails.degree;
                request.questionnairre.monthlyIncome = data.questionnairreDetails.monthlyIncome;
                request.questionnairre.monthlyOutgoing = data.questionnairreDetails.monthlyOutgoing;
                request.questionnairre.jobType = data.questionnairreDetails.jobType;
                request.questionnairre.homeOwnershipType = data.questionnairreDetails.homeOwnershipType;
                request.questionnairre.vehicle = data.questionnairreDetails.vehicle;
            }

            let documentList = [];

            if (!IsNullEmpty(data.panDocDetails)) {
                const document = {
                    docType: "Pan",
                    docName: data.panDocDetails[0].fileName,
                    docNo: data.panNo,
                    docVertification: data.panDocVerification
                };
                let filePath = data.panDocDetails[0].filePath;
                const downloadedFile = await downloadImageFromS3(filePath);
                document.docImg = downloadedFile.data
                documentList.push(document);
            }

            const docPhoto = {
                docType: "Photo",
                docName: null,
                docNo: null,
                docVertification: null,
                docImg: null
            };

            const docVideo = {
                docType: "Video",
                docName: null,
                docVideo: null
            };

            if (data.userPhotoPath) {
                let filePath = data.userPhotoPath

                const downloadedFile = await downloadImageFromS3(filePath);

                if (downloadedFile.success) {
                    docPhoto.docImg = downloadedFile.data
                    documentList.push(docPhoto);
                } else {
                    filePath = config.get("keys.defaultUserPhoto");
                    docPhoto.docImg = base64_encode(filePath);
                    documentList.push(docPhoto);
                }
            } else {
                logger.logs("inside 1");
                filePath = config.get("keys.defaultUserPhoto");
                docPhoto.docImg = base64_encode(filePath);
                documentList.push(docPhoto);
            }

            if (data.userVideoPath) {
                const filePath = data.userVideoPath;
                const downloadedFile = await downloadImageFromS3(filePath);

                if (downloadedFile.success) {
                    docVideo.docVideo = downloadedFile.data;
                    const parts = data.userVideoPath.split('/');
                    if (parts.length > 0)
                        docVideo.docName = parts[parts.length - 1];
                }
            }

            documentList.push(docVideo);

            if (!IsNullEmpty(data.poaDetails)) {
                for (i = 0; i < data.poaDetails.length; i++) {
                    if (data.poaDetails[i].docDetails) { //added backimage - Uttam
                        const isExist = documentList.filter((x) => x.docType === data.poaDetails[i].poaType);
                        if (isExist.length === 0) {
                            for (const item of data.poaDetails[i].docDetails) {
                                let k = 0;
                                let position = "";
                                if (item.position) position = item.position;
                                else
                                    position = k == 0 ? "front" : "back";


                                const document = {
                                    docType: data.poaDetails[i].poaType,
                                    docName: item.fileName,
                                    //docName: data.poaDetails[i].docDetails[0].fileName,
                                    docNo: data.poaDetails[i].poaNo,
                                    docVertification: data.poaDetails[i].docVerification,
                                    position: position
                                };

                                let filePath = item.filePath
                                const downloadedFile = await downloadImageFromS3(filePath);

                                if (downloadedFile.success) {
                                    document.docImg = downloadedFile.data
                                }

                                documentList.push(document);
                                k++;
                            }
                        }
                    }
                }
            }

            if (documentList.length > 0) {
                request.kycDetails.documents = documentList;
            }
        }

        output = {
            data: request,
            success: true,
            message: "Set Successfully",
            status: 200,
        };
    } catch (ex) {
        logger.logs(ex);
        saveErrorLog("setDataForLoanApplication", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};

async function setDataForLoanSanction(model, data) {
    let output = {
        data: null,
        success: false,
        message: "",
        status: 501,
    };
    try {
        // let request = {
        //     loanNo: data.loanNo,
        //     partnerLoanRefId: data.lenderLoanId,
        //     partnerCustRefId: data.lenderCustId,
        //     principalAmount: data.amount,
        //     emiAmount: data.firstEmiAmount,
        //     tenure: data.maturity,
        //     frequency: data.repaymentFrequency,
        //     interestRate: data.interestRate,
        //     disbursalDate: getDMYFormattedDate(data.disbursedOn, "/"),
        //     repaymentSchedule: [],
        //     loanContractDocument: null,
        //     kfsDocument: null,
        //     photo: null,
        //     userBankDetails: {
        //         accountName: null,
        //         accountNo: null,
        //         bankName: null,
        //         ifsc: null,
        //         accountType: null,
        //         address: null,
        //         city: null,
        //         state: null
        //     },
        //     disbursementAccountDetails: {
        //         accountName: null,
        //         accountNo: null,
        //         bankName: null,
        //         ifsc: null,
        //         accountType: null,
        //         address: null,
        //         city: null,
        //         state: null
        //     }
        // }

        let request = {
            loanNo: data.loanNo,
            partnerLoanRefId: data.lenderLoanId,
            partnerCustRefId: data.lenderCustId,
            principalAmount: data.amount,
            emiAmount: data.firstEmiAmount,
            tenure: data.maturity,
            frequency: data.repaymentFrequency,
            interestRate: data.interestRate,
            disbursalDate: getDMYFormattedDate(data.disbursedOn, "/"),
            repaymentSchedule: [],
            loanContractDocument: null,
            photo: null,
            userBankDetails: {
                accountName: null,
                accountNo: null,
                bankName: null,
                ifsc: null,
                accountType: null,
                address: null,
                city: null,
                state: null
            },
            disbursementAccountDetails: {
                accountName: null,
                accountNo: null,
                bankName: null,
                ifsc: null,
                accountType: null,
                address: null,
                city: null,
                state: null
            }
        }

        //Emi Details
        for (record of data.installments) {
            const installment = {
                emiNumber: record.installmentNumber,
                emiAmount: record.installmentAmount,
                emiPrincipal: record.installmentPrincipal,
                emiInterest: record.installmentInterest,
                status: record.status,
                rePaymentDate: getDMYFormattedDate(record.dueDate, "/"),
                rePaymentActualDate: getDMYFormattedDate(record.actualDueDate, "/")
            }
            request.repaymentSchedule.push(installment);
        }

        //Contract File
        if (!IsNullEmpty(data.contractFile)) {
            let filePath = data.contractFile;
            const downloadedFile = await downloadImageFromS3(filePath);
            request.loanContractDocument = downloadedFile.data;
        }

        //User Photo
        if (!IsNullEmpty(data.photoPath)) {
            let filePath = data.photoPath
            const downloadedFile = await downloadImageFromS3(filePath);
            request.photo = downloadedFile.data
        }

        //KFS Document
        if (!IsNullEmpty(data.kfsFile)) {
            let filePath = data.kfsFile;
            const downloadedFile = await downloadImageFromS3(filePath);
            request.kfsDocument = downloadedFile.data;
        }

        //User Account Details
        if (data.userBankDetails && data.userBankDetails.length > 0) {
            request.userBankDetails = {
                accountName: data.userBankDetails[0].accountName,
                accountNo: data.userBankDetails[0].accountNo,
                bankName: data.userBankDetails[0].bankName,
                ifsc: data.userBankDetails[0].ifsc,
                accountType: data.userBankDetails[0].accountType,
                address: data.userBankDetails[0].address,
                city: data.userBankDetails[0].city,
                state: data.userBankDetails[0].state
            }
        }

        //Disbural Bank Details
        if (data.disbursalBankDetails) {
            request.disbursementAccountDetails = {
                accountName: data.disbursalBankDetails.accountName,
                accountNo: data.disbursalBankDetails.accountNo,
                bankName: data.disbursalBankDetails.bankName,
                ifsc: data.disbursalBankDetails.ifsc,
                accountType: data.disbursalBankDetails.accountType,
                address: data.disbursalBankDetails.address,
                city: data.disbursalBankDetails.city,
                state: data.disbursalBankDetails.state
            }
        }

        output = {
            data: request,
            success: true,
            message: "Set Successfully",
            status: 200,
        };

    } catch (ex) {
        logger.logs(ex);
        saveErrorLog("setDataForLoanSanction", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};

async function setDataForLoanRepayment(model, data, conf) {
    let output = {
        data: null,
        success: false,
        message: "",
        status: 501,
    };
    try {
        let request = {};
        const emiDate = getMDYFormattedDate(data.rePaymentActualDate, "/");
        if (model.partnerCode.toLowerCase() == "aphe") {
            request = {
                UserID: conf.tokenParameters.UserName,
                LoginID: data.lenderLoanId,
                OrderID: model.referenceNo,
                EMIDate: emiDate,
                RepaymentAmount: data.rePaymentAmount,
                Principal: floor10(data.principalRecovered, -2),
                Interest: data.interestAmount,
                DealyedInterest: data.penalty,
                Discount: 0
            }
        } else if (model.partnerCode.toLowerCase() == "lbox") {
            request = [{
                lbUserId: parseInt(data.lenderCustId),
                amount: (model.rePaymentAmount && model.rePaymentAmount != null) ? model.rePaymentAmount : data.rePaymentAmount,
                txType: "CREDIT",
                txId: model.referenceNo,
                loanId: parseInt(data.lenderLoanId),
                txTime: getYMDFormattedDate(data.updatedOn, "-")
            }]
        } else if (model.partnerCode.toLowerCase() == "cent") {
            request = {
                ReferenceNo: data.lenderLoanId,
                SettlementDate: getDMYFormattedDate(data.updatedOn, "-"),
                Amount: data.rePaymentAmount,
                PrincipalAmountRecovered: data.principalRecovered,
                InterestAmountRecovered: data.interestAmount
            }
        } else {
            request = {
                loanNo: data.loanNo,
                partnerLoanRefId: data.lenderLoanId,
                partnerCustRefId: data.lenderCustId,
                emiDetails: []
            };

            if (!IsNullEmpty(data.receivedEmiDetails)) {
                for (const record of data.receivedEmiDetails) {
                    const installment = {
                        emiNumber: record.installmentNumber,
                        emiAmount: record.installmentAmount,
                        status: record.status,
                        rePaymentDate: getDMYFormattedDate(record.dueDate, "/"),
                        rePaymentActualDate: getDMYFormattedDate(record.actualDueDate, "/"),
                        rePaymentReceivedDate: (installment.paymentDetails != null && installment.paymentDetails.length > 0) ? getDMYFormattedDate(installment.paymentDetails[installment.paymentDetails.length - 1].transactionDate, "/") : null
                    }
                    request.emiDetails.push(installment);
                }
            }
        }
        output = {
            data: request,
            success: true,
            message: "Set Successfully",
            status: 200,
        };
    } catch (ex) {
        logger.logs(ex);
        saveErrorLog("setDataForLoanRepayment", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};

async function sendDisbursementNotification(model) {
    let output = {
        success: false,
        message: "",
        status: 501,
    };
    try {
        let response = null;
        const conf = config.get(model.partnerCode.toLowerCase());
        let selectParameters = null;
        if (model.partnerCode.toLowerCase() == "cent") {
            selectParameters = { lenderDetails: 1, userId: 1 }
        } else {
            selectParameters = { lenderDetails: 1, amount: 1, maturity: 1, interestRate: 1, loanNo: 1, repaymentFrequency: 1, userId: 1 };
        }

        if (!IsNullEmpty(conf)) {
            const loanInfo = await LoanInfo.findById(model.loanId).select(selectParameters);
            if (!IsNullEmpty(loanInfo)) {
                const userInfo = await UserInfo.findById(loanInfo.userId);

                if (model.partnerCode.toLowerCase() == "cent") {
                    const disburesmentDate = getDMYFormattedDate(model.disbursementDate, "-");
                    let collectionStartDate = await dateAdd("day", 1, model.disbursementDate);
                    collectionStartDate = getDMYFormattedDate(collectionStartDate, "-");

                    if (!IsNullEmpty(loanInfo.lenderDetails[0].lenderLoanId)) {
                        const requestBody = {
                            RequestHeader: conf.bodyConstants,
                            RequestBody: {
                                LoanAppNo: loanInfo.lenderDetails[0].lenderLoanId,
                                DisburesmentDate: disburesmentDate,
                                CollectionStartDate: collectionStartDate
                            }
                        };

                        const _body = JSON.stringify(requestBody);

                        logger.logs("disbursement request : ", _body);
                        response = await fetch(model.url, {
                            method: "post",
                            body: _body,
                            headers: conf.headerConstants,
                            timeout: 30000
                        }).then(function (u) {
                            return u.json();
                        }).then(function (json) {
                            return json;
                        }).catch((err) => {
                            console.error("err :", err);
                            return err;
                        });
                        logger.logs("disbursement response : ", response);

                        if (!IsNullEmpty(response)) {
                            model.response = response;
                            if (!IsNullEmpty(response.FundFinaLoanDisbursementResult)) {
                                const result = response.FundFinaLoanDisbursementResult.split(":");

                                if ((result.length > 0 && result[0].toLowerCase() == "success") || (result.length > 1 && result[1] == "Loan Already Disbursed.")) {
                                    output = {
                                        success: true,
                                        message: "disbused successfully",
                                        status: 200,
                                    };
                                } else {
                                    output.message = result.errorMsg ? result.errorMsg : JSON.stringify(result);
                                }
                            } else {
                                output.message = "Response parameters FundFinaLoanDisbursementResult Not Received";
                            }
                        } else {
                            output.message = "No response received";
                        }
                    } else {
                        output.message = "Lender loan Id not found";
                    }
                } else if (model.partnerCode.toLowerCase() == "lbox") {
                    if (loanInfo.repaymentFrequency.toLowerCase() == "daily" || loanInfo.repaymentFrequency.toLowerCase() == "monthly") {
                        if (!IsNullEmpty(loanInfo.lenderDetails[0].lenderCustId)) {
                            const requestBody = {
                                lbUserId: loanInfo.lenderDetails[0].lenderCustId,
                                roi: loanInfo.interestRate,
                                loanAmount: loanInfo.amount,
                                loanTenure: loanInfo.maturity,
                                durationInDays: loanInfo.repaymentFrequency.toLowerCase() == "monthly" ? 0 : 1,
                                appId: loanInfo.loanNo,
                                loanType: model.lenderProductType ? model.lenderProductType : null,
                                assessment: null
                            };

                            const _body = JSON.stringify(requestBody);
                            const url = model.url + "?lbUserId=" + loanInfo.lenderDetails[0].lenderCustId;
                            const queryString = { lbUserId: loanInfo.lenderDetails[0].lenderCustId };
                            logger.logs("url loanDisbursement", url);
                            logger.logs("_body loanDisbursement", _body);
                            response = await fetch(url, {
                                method: "post",
                                body: _body,
                                headers: conf.headerConstants,
                                timeout: 30000
                            }).then(function (u) {
                                return u.json();
                            }).then(function (json) {
                                return json;
                            }).catch((err) => {
                                console.error("err :", err);
                                return err;
                            });
                            logger.logs("response loanDisbursement", response);

                            if (!IsNullEmpty(response)) {
                                model.response = response;
                                if (response.success && response.data && !IsNullEmpty(response.data.lbLoanId)) {
                                    loanInfo.lenderDetails[0].lenderLoanId = response.data.lbLoanId;
                                    await loanInfo.save();

                                    saveLendingPartnerAllocation(loanInfo._id, true);

                                    output = {
                                        success: true,
                                        message: response.msg,
                                        status: 200,
                                    };
                                } else {
                                    if (!model.isRetryOffline) {
                                        output.message = "Error From Partner " + model.partnerDisplayName + " : " + response.msg + ". Please select other Lending Partner";
                                    } else {
                                        output.message = response.msg;
                                    }
                                }
                            } else {
                                output.message = "No response from Partner " + model.partnerDisplayName;
                            }
                        } else {
                            output.message = "Waiting for Borrower Registration";
                        }
                    } else {
                        output.message = "repaymentFrequency is allowed only monthly or daily";
                    }
                } else {
                    output.message = "Please provide lending partner API";
                }
            } else {
                output.message = "Loan details not found";
            }
        } else {
            output.message = "No Config found for partner " + model.partnerDisplayName;
            return output;
        }
    } catch (ex) {
        logger.logs("Error", ex);
        saveErrorLog("sendDisbursementNotification", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};

async function sendLoanCancellation(model) {
    let output = {
        success: false,
        message: "",
        status: 501,
    };
    try {
        const conf = config.get(model.partnerCode.toLowerCase());

        if (!IsNullEmpty(conf)) {
            const dbDataResponse = await getDataForLoanCancellation(model.loanId);
            if (dbDataResponse.success) {
                const data = dbDataResponse.data;
                if (!IsNullEmpty(data.lenderLoanId)) {
                    let tokenResponse = null;
                    if (conf.isTokenGenration == true) {
                        tokenResponse = await getToken(conf, model);
                        if (!tokenResponse.success) {
                            output.message = tokenResponse.message;
                            return output;
                        }
                    }
                    if (model.partnerCode.toLowerCase() == "cent") {
                        const request = {
                            "LoanAppNo": model.lenderLoanId
                        };
                        const requestBody = {
                            "RequestHeader": conf.bodyConstants,
                            "RequestBody": request
                        }
                        const _body = JSON.stringify(requestBody);

                        logger.logs("request : ", _body);

                        const response = await fetch(model.url, {
                            method: "post",
                            body: _body,
                            headers: conf.headerConstants,
                        }).then(function (u) {
                            return u.json();
                        }).then(function (json) {
                            return json;
                        }).catch((err) => {
                            console.error("err :", err);
                            return err;
                        });
                        logger.logs("response : ", response);

                        if (!IsNullEmpty(response)) {
                            if (!IsNullEmpty(response.FundFinaCancelLoanApplicationResult)) {
                                const result = response.FundFinaCancelLoanApplicationResult.split(":");

                                if (result[0].toLowerCase() == "success" || result[1] == "Loan application has already been rejected.") {
                                    output = {
                                        success: true,
                                        message: result[1],
                                        status: 200,
                                    };
                                } else {
                                    output.message = result[1];
                                }
                            } else {
                                output.message = "Response parameters FundFinaCancelLoanApplicationResult Not Received";
                            }
                        } else {
                            output.message = "No response received";
                        }
                    } else {
                        const request = {
                            "partnerCustRefId": model.lenderCustId,
                            "partnerLoanRefId": model.lenderLoanId
                        };

                        const _body = JSON.stringify(request);

                        //logger.logs("url : ", model.url);
                        //logger.logs("request : ", _body);

                        const response = await fetch(model.url, {
                            method: "post",
                            body: _body,
                            headers: conf.headerConstants,
                        }).then(function (u) {
                            return u.json();
                        }).then(function (json) {
                            return json;
                        }).catch((err) => {
                            console.error("err :", err);
                            return err;
                        });
                        //logger.logs("response : ", response);

                        if (!IsNullEmpty(response) && (!IsNullEmpty(response.success))) {
                            if (response.success) {
                                output = {
                                    success: true,
                                    message: response.message,
                                    status: 200,
                                };
                            } else {
                                output.message = response.message;
                            }
                        } else {
                            output.message = "No response received";
                        }
                    }
                } else {
                    output.message = "No lenderLoanId found for partner " + model.partnerDisplayName;
                    return output;
                }
            } else {
                output.message = dbDataResponse.message;
            }
        } else {
            output.message = "No Config found for partner " + model.partnerDisplayName;
            return output;
        }
    } catch (ex) {
        logger.logs(ex);
        saveErrorLog("sendLoanSanction", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};

module.exports.confirmLoanRequest = async function (model) {
    let output = {
        success: false,
        message: "",
        status: 200,
    };
    try {
        // const userPartnerInfo = await UserPartnerInfo.findOne({ partnerId: mongoose.Types.ObjectId(model.partnerId), isPartnerUser: true });

        // if (userPartnerInfo) {
        let query = { loanNo: model.req.loanNo, "lenderDetails.lenderPartnerId": mongoose.Types.ObjectId(model.partnerId) };

        const loanInfo = await LoanInfo.findOne(query);
        if (loanInfo) {
            if (loanInfo.status == "Lender Approval") {
                if (IsNullEmpty(loanInfo.lenderDetails[0].lenderCustId) && !IsNullEmpty(model.req.partnerCustRefId)) {
                    loanInfo.lenderDetails[0].lenderCustId = model.req.partnerCustRefId;
                }

                if (IsNullEmpty(loanInfo.lenderDetails[0].lenderLoanId) && !IsNullEmpty(model.req.partnerLoanRefId)) {
                    loanInfo.lenderDetails[0].lenderLoanId = model.req.partnerLoanRefId;
                }

                if (IsNullEmpty(loanInfo.lenderDetails[0].lenderLoanId) && !IsNullEmpty(model.req.partnerLeadId)) {
                    loanInfo.lenderDetails[0].lenderLeadId = model.req.partnerLeadId;
                }

                if (!IsNullEmpty(loanInfo.lenderDetails[0].lenderCustId)) {
                    const productType = getProductType(loanInfo.productType);
                    loanInfo.status = "Lender Assigned";
                    loanInfo.flowDetails.push({
                        Step: (productType == "LC" && loanInfo.isParentLoan) ? "Onboarding Completed" : "Lender Assigned",
                        changedBy: null,
                        changedByRole: "System",
                        insertedOn: Date.now(),
                    });
                    await loanInfo.save();

                    const lenderId = loanInfo.lenderDetails[0].lenderPartnerId;

                    const lenderAllocation = await LendingPartnerAllocation.findOne({ loanId: loanInfo._id, lenderId: lenderId, isActive: true });

                    if (lenderAllocation) {

                        lenderAllocation.lenderApproval.status = "Approved By Lender";
                        lenderAllocation.lenderApproval.updatedBy = null;
                        lenderAllocation.lenderApproval.updatedByUser = "System";
                        lenderAllocation.lenderApproval.updatedOn = Date.now();
                        lenderAllocation.lenderApproval.ipAddress = null;
                        lenderAllocation.lenderApproval.comments = null;

                        await lenderAllocation.save();
                    }

                }
                output = {
                    success: true,
                    message: "Updated Successfully",
                    status: 200,
                };
            } else
                output.message = "Updation Not Allowed"
        } else {
            output.message = "No Loan Found";
        }
        // } else {
        //     output.message = "Please Register As Partner";
        // }
    } catch (ex) {
        logger.logs(ex);
        saveErrorLog("confirmLoanRequest", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};

async function saveLendingPartnerAllocation(loanId, isApproved) {
    let output = {
        success: false,
        message: "",
        status: 200,
    };

    try {
        const loanInfo = await LoanInfo.findById(loanId).select({ _id: 1, lenderDetails: 1, userId: 1 });
        const userInfo = await UserInfo.findById(loanInfo.userId).select({ panNo: 1, userDetails: 1 });

        let lendingPartnerAllocation = await LendingPartnerAllocation.findOne({ loanId: mongoose.Types.ObjectId(loanInfo._id), lenderId: loanInfo.lenderDetails[0].lenderPartnerId, isActive: true });

        if (lendingPartnerAllocation && lendingPartnerAllocation != null) {
            lendingPartnerAllocation.lenderId = loanInfo.lenderDetails[0].lenderPartnerId;
            lendingPartnerAllocation.lenderCustId = loanInfo.lenderDetails[0].lenderCustId;
            lendingPartnerAllocation.lenderLoanId = loanInfo.lenderDetails[0].lenderLoanId;
            lendingPartnerAllocation.lenderLeadId = loanInfo.lenderDetails[0].lenderLeadId;
            lendingPartnerAllocation.lenderProductType = loanInfo.lenderDetails[0].lenderProductType;
            lendingPartnerAllocation.mobileNo = userInfo.userDetails.mobileNo[0] ? userInfo.userDetails.mobileNo[0].mobileNumber : ""; //mobile number change
            lendingPartnerAllocation.panNo = userInfo.panNo;
            lendingPartnerAllocation.updatedOn = Date.now();
        } else {
            lendingPartnerAllocation = new LendingPartnerAllocation({
                userId: mongoose.Types.ObjectId(loanInfo.userId),
                loanId: mongoose.Types.ObjectId(loanInfo._id),
                lenderId: mongoose.Types.ObjectId(loanInfo.lenderDetails[0].lenderPartnerId),
                lenderCustId: loanInfo.lenderDetails[0].lenderCustId,
                lenderLoanId: loanInfo.lenderDetails[0].lenderLoanId,
                lenderLeadId: loanInfo.lenderDetails[0].lenderLeadId,
                lenderProductType: loanInfo.lenderDetails[0].lenderProductType,
                insertedOn: Date.now(),
                mobileNo: userInfo.userDetails.mobileNo[0] ? userInfo.userDetails.mobileNo[0].mobileNumber : "", //mobile number change
                panNo: userInfo.panNo,
                updatedOn: null,
                isActive: true
            });
        }

        //If lender approval not required, auto-approve loan 
        if (isApproved) {
            lendingPartnerAllocation.lenderApproval.status = "Approved By Lender";
            lendingPartnerAllocation.lenderApproval.updatedBy = null;
            lendingPartnerAllocation.lenderApproval.updatedByUser = "System";
            lendingPartnerAllocation.lenderApproval.updatedOn = Date.now();
            lendingPartnerAllocation.lenderApproval.ipAddress = null;
            lendingPartnerAllocation.lenderApproval.comments = null;
        }

        await lendingPartnerAllocation.save();

        output = {
            success: true,
            message: "Updated Successfully",
            status: 200,
        };
    } catch (ex) {
        logger.logs(ex);
        saveErrorLog("saveLendingPartnerAllocation", apiPath, { loanId, isLoanApprovalRequired }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};
module.exports.saveLendingPartnerAllocation = saveLendingPartnerAllocation;

async function sendBankDetails(model) {
    let output = {
        data: null,
        success: false,
        message: "",
        status: 501,
    };
    try {
        const conf = config.get(model.partnerCode.toLowerCase());
        if (!IsNullEmpty(conf)) {
            const dbDataResponse = await getDataForBankDetails(model.loanId);

            if (dbDataResponse.success) {
                const data = dbDataResponse.data;
                const setResponse = await setDataForBankDetails(model, data);

                if (setResponse.success) {
                    const request = setResponse.data;
                    const _body = JSON.stringify(request);
                    model.request = request;

                    if (model.partnerCode.toLowerCase() == "lbox") {

                        if (!IsNullEmpty(data.lenderCustId)) {
                            const endUrl = (model.isBankDetailsSubmitted && model.isBankDetailsSubmitted === true) ? "/bankdetails?lbUserId=" : "/save-borr-bankdetails?lbUserId=" + data.lenderCustId;

                            const url = model.url + endUrl;
                            logger.logs("url", url);
                            logger.logs("request", _body);

                            let response = await fetch(url, {
                                method: "post",
                                body: _body,
                                headers: conf.headerConstants,
                            }).then(function (u) {
                                return u.json();
                            }).then(function (json) {
                                return json;
                            }).catch((err) => {
                                console.error("err :", err);
                                return err;
                            });
                            logger.logs("response", response);
                            model.response = response;

                            if (Boolean(response.success) == false) {
                                if (model.isBankDetailsSubmitted == false && response.msg == "Bank details already exists. Please use UPDATE API.") {
                                    const url = model.url + "/bankdetails?lbUserId=" + data.lenderCustId;

                                    logger.logs("url", url);
                                    logger.logs("request", _body);

                                    let response = await fetch(url, {
                                        method: "put",
                                        body: _body,
                                        headers: conf.headerConstants,
                                    }).then(function (u) {
                                        return u.json();
                                    }).then(function (json) {
                                        return json;
                                    }).catch((err) => {
                                        console.error("err :", err);
                                        return err;
                                    });
                                    logger.logs("response : ", response);

                                    model.response = response;
                                    if (Boolean(response.success) == false) {
                                        output.message = response.msg;
                                    } else {
                                        output = {
                                            success: true,
                                            message: response.msg,
                                            status: 200,
                                        };
                                    }
                                } else output.message = response.msg;
                            } else {
                                output = {
                                    success: true,
                                    message: response.msg,
                                    status: 200,
                                };
                            }
                        } else {
                            output.message = "Waiting for User Registration";
                        }
                    } else {
                        response = await fetch(url, {
                            method: "post",
                            body: _body,
                            headers: conf.headerConstants,
                        }).then(function (u) {
                            return u.json();
                        }).then(function (json) {
                            return json;
                        }).catch((err) => {
                            console.error("err :", err);
                            return err;
                        });
                        logger.logs("response", response);
                        model.response = response;

                        if (Boolean(response.success) == false) {
                            output.message = response.message;
                        } else {
                            output = {
                                success: true,
                                message: response.message,
                                status: 200,
                            };
                        }
                    }
                } else {
                    output.message = setResponse.message;
                }

            } else {
                output.message = dbDataResponse.message;
            }
        } else {
            output.message = "No Config found for partner " + model.partnerDisplayName;
            return output;
        }
    } catch (ex) {
        logger.logs(ex);
        saveErrorLog("sendBankDetails", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }

    if (output.success == true) {
        await LendingPartnerAllocation.updateMany({ userId: mongoose.Types.ObjectId(model.userId), lenderId: mongoose.Types.ObjectId(model.lenderId), isBankDetailsSubmitted: false }, { $set: { isBankDetailsSubmitted: true } });
    }

    return output;
};

async function setDataForBankDetails(model, data) {
    let output = {
        data: null,
        success: false,
        message: "",
        status: 501,
    };
    try {
        let request = {};

        if (model.partnerCode.toLowerCase() == "lbox") {
            request = {
                accountNumber: data.disbursalBankDetails.accountNo,
                ifsc: data.disbursalBankDetails.ifsc,
                holderName: data.disbursalBankDetails.accountName,
                accountType: data.disbursalBankDetails.accountType.toUpperCase(),
                bankName: data.disbursalBankDetails.bankName,
                branchName: data.disbursalBankDetails.branch
            }
        } else {
            request = {
                accountNumber: data.disbursalBankDetails.accountNo,
                ifsc: data.disbursalBankDetails.ifsc,
                holderName: data.disbursalBankDetails.accountName,
                accountType: data.disbursalBankDetails.accountType,
                bankName: data.disbursalBankDetails.bankName,
                branchName: data.disbursalBankDetails.branch
            }
        }
        output = {
            data: request,
            success: true,
            message: "Set Successfully",
            status: 200,
        };
    } catch (ex) {
        logger.logs(ex);
        saveErrorLog("setDataForBankDetails", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};

//module.exports.getTokenFromAphelion = getTokenFromAphelion;
// ------------------------------------------------------------------------------------------------------------------------------------------