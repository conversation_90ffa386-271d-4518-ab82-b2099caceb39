//Libraries
// ------------------------------------------------------------------------------------------------------------------------------------------
const debug = require("debug")("app:payOne");
const IsNullEmpty = require("isnullemptyorwhitespace");
const config = require("config");
const fetch = require("node-fetch");
const mongoose = require("mongoose");
const DateDiff = require("date-diff");
const apiPath = __dirname;
const { dateAdd } = require("dateadd");
// ------------------------------------------------------------------------------------------------------------------------------------------

//Models
// ------------------------------------------------------------------------------------------------------------------------------------------
const { LoanInfo, InvoiceRequests } = require("../../../models/ops/loan/loan");
const { PartnerMaster, PartnerLoans, PreApprovals, PartnerTransactions, PartnerConfig } = require("../../../models/ops/partner/partner");
const { UserInfo, UserPartnerInfo } = require("../../../models/ops/registration/users");
// ------------------------------------------------------------------------------------------------------------------------------------------

//External Methods
// ------------------------------------------------------------------------------------------------------------------------------------------
const { stringToBase64, downloadImage, validatePan, validateMobileNumber,
  validateEmail, getPreApprovedProductId, getYMDFormattedDate, getCurrentTimeStamp, getName, getProductType, downloadImageBuffer } = require("../common/common");
const { validateKYC, PushToFundfina, backupPartnerLoans, getCityState } = require("../../../services/ops/partner/partner");
const partnerService = require("../../../services/ops/partner/partner");
const loanService = require("../../../services/ops/loan/loan");
const { saveErrorLog } = require("../../../services/ops/common/masters");
const { uploadToS3 } = require("../../../models/ops/aws/s3");
const Logger = require("../../../middleware/logger");
const logger = new Logger();
// ------------------------------------------------------------------------------------------------------------------------------------------

//Internal WorkFlow Methods
// ------------------------------------------------------------------------------------------------------------------------------------------
async function getPayOneToken() {
  let output = {
    data: null,
    success: false,
    message: "Some Error Occurred",
    status: 501,
  };
  try {
    let api_token = null;
    const partnerMaster = await PartnerMaster.findOne({ partnerCode: "Pay" });

    if (!IsNullEmpty(partnerMaster)) {
      const currentDate = new Date();
      if (!IsNullEmpty(partnerMaster.token)) {
        let diff = new DateDiff(partnerMaster.tokenExpireAt, currentDate);
        const diffMinutes = diff.minutes();
        if (diffMinutes > 7) {
          api_token = partnerMaster.token;
        }
      }

      if (IsNullEmpty(api_token)) {
        const auth = "Basic " + stringToBase64(config.get("pay.tokenParameters.userName") + ":" + config.get("pay.tokenParameters.password"));

        let apiResponse = await fetch(config.get("pay.tokenGenrationUrl"), {
          method: "POST",
          body: "",
          headers: {
            "Content-Type": "application/json",
            Authorization: auth,
          },
        })
          .then(function (u) {
            return u.json();
          })
          .then(function (json) {
            return json;
          })
          .catch((err) => console.error("err :", err));

        if (
          !IsNullEmpty(apiResponse) &&
          !IsNullEmpty(apiResponse.status) &&
          apiResponse.status.toLowerCase() == "success"
        ) {
          const validtill = new Date(apiResponse.t_valid * 1000);

          apiResponse.validtill = validtill;
          partnerMaster.token = apiResponse.api_token;
          partnerMaster.tokenExpireAt = validtill;

          await partnerMaster.save();
          output = {
            data: apiResponse,
            success: true,
            message: "token generated successfully",
            status: 200,
          };
        } else {
          output.data = apiResponse;
        }
      } else {
        output = {
          data: {
            api_token: api_token,
            validtill: partnerMaster.tokenExpireAt
          },
          success: true,
          message: "token generated successfully",
          status: 200,
        };
      }

    } else {
      output.message = "No Partner Found";
    }
  } catch (ex) {
    //console.log(ex);
    output.message = ex.message;
    saveErrorLog("getPayOneToken", apiPath, null, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
  }
  return output;
};
module.exports.getPayOneToken = getPayOneToken;

module.exports.getUserDetails = async function (authKey, model) {
  let output = {
    data: null,
    success: false,
    message: "Some Error Occurred",
    status: 200,
  };
  try {
    if (!IsNullEmpty(authKey)) {
      let partnerMaster = await PartnerMaster.findOne({ authKey: { $exists: true }, authKey: authKey });


      if (IsNullEmpty(partnerMaster)) {
        partnerMaster = await PartnerMaster.findOne({ shortauthKey: { $exists: true }, shortauthKey: authKey });
      }

      if (!IsNullEmpty(partnerMaster)) {
        const conf = config.get(partnerMaster.partnerCode.toLowerCase());
        if (conf) {
          if ((conf.userDataUrl && conf.userDataUrl != null) || conf.isByPassUserDataUrl == true) {
            let existingLoan = null;
            let loanStatus = "Initiated";

            //Checking for existing Loan By ProductID and isParentLoan
            existingLoan = await LoanInfo.findOne({
              partnerId: mongoose.Types.ObjectId(partnerMaster._id),
              merchantCode: model.userId,
              productId: model.trxnId,
              isParentLoan: true
            }).sort({ insertedOn: -1 });

            //Checking for existing Loan By ProductID
            if (IsNullEmpty(existingLoan)) {
              existingLoan = await LoanInfo.findOne({
                partnerId: mongoose.Types.ObjectId(partnerMaster._id),
                merchantCode: model.userId,
                productId: model.trxnId
              }).sort({ insertedOn: -1 });
            }

            //Checking for existing Loan By trxnId
            if (IsNullEmpty(existingLoan)) {
              existingLoan = await LoanInfo.findOne({
                partnerId: mongoose.Types.ObjectId(partnerMaster._id),
                merchantCode: model.userId,
                trxnId: model.trxnId
              }).sort({ insertedOn: -1 });
            }

            loanStatus = (existingLoan && existingLoan != null) ? existingLoan.status : "Initiated";

            if (existingLoan && existingLoan != null && loanStatus == "Under Review") {
              let _data = { productId: existingLoan.productId };

              let userInfo = await UserInfo.findById(existingLoan.userId);

              if (!IsNullEmpty(userInfo)) {
                _data.userId = userInfo._id;
                _data.kycStatus = userInfo.userDetails.kycStatus;
                _data.loanId = existingLoan._id;
                _data.loanStatus = existingLoan.status;
                _data.isDraft = existingLoan.isDraft;

                output = {
                  data: _data,
                  success: true,
                  message: "",
                  status: 200,
                };
              }
            } else if (existingLoan && existingLoan != null && loanStatus !== "Initiated") {
              if (!IsNullEmpty(model.trxnId))
                output.message = "TrxnId already Exists. Can Not Update. Please send new request with unique TrxnId.";
              else
                output.message = "Product Id already Exists. Can Not Update. Please send new request with unique product Id.";

              let partnerLoans = null;

              partnerLoans = await PartnerLoans.findOne({
                partnerId: partnerMaster._id,
                merchantCode: model.userId,
                productId: model.trxnId
              });

              if (IsNullEmpty(partnerLoans)) {
                partnerLoans = await PartnerLoans.findOne({
                  partnerId: partnerMaster._id,
                  merchantCode: model.userId,
                  trxnId: model.trxnId
                });
              }

              if (!IsNullEmpty(partnerLoans)) {
                let _data = { productId: partnerLoans.productId };

                let userInfo = await UserInfo.findOne({ panNo: { $regex: new RegExp(partnerLoans.panNo, "i") } });

                if (!IsNullEmpty(userInfo)) {
                  _data.userId = userInfo._id;
                  _data.kycStatus = userInfo.userDetails.kycStatus;

                  let loanInfo = await LoanInfo.findOne({
                    userId: mongoose.Types.ObjectId(userInfo._id),
                    productId: partnerLoans.productId,
                  });

                  if (!IsNullEmpty(loanInfo)) {
                    _data.loanId = loanInfo._id;
                    _data.loanStatus = loanInfo.status;
                    _data.isDraft = loanInfo.isDraft;
                  }

                  output = {
                    data: _data,
                    success: true,
                    message: "",
                    status: 200,
                  };

                  if (!IsNullEmpty(model.trxnId))
                    output.message = "TrxnId already Exists. Can Not Update. Please send new request with unique TrxnId.";
                  else
                    output.message = "Product Id already Exists. Can Not Update. Please send new request with unique product Id.";

                } else {
                  output.data = _data;
                  output.message = `Application with new pan ${partnerLoans.panNo} not allowed. Please contact Fundfina Support Team.`;
                }
              }
            } else {
              let isValidTrxnId = false;
              let query = null;

              if (partnerMaster.partnerCode.toLowerCase() == "pay") {
                query = { partnerId: mongoose.Types.ObjectId(partnerMaster._id), merchantCode: model.userId, trxnId: model.trxnId, status: "Sent" };
              } else {
                query = { partnerId: mongoose.Types.ObjectId(partnerMaster._id), merchantCode: model.userId, productId: model.trxnId, status: "Sent" };
              }

              const preApproval = await PreApprovals.findOne(query).sort({ modifiedOn: -1 });

              if (!IsNullEmpty(preApproval)) {
                if ((!IsNullEmpty(preApproval.trxnId) && preApproval.trxnId.toLowerCase() != model.trxnId.toString().toLowerCase())
                  && (!IsNullEmpty(preApproval.productId) && preApproval.productId.toLowerCase() != model.trxnId.toString().toLowerCase())) {
                  preApproval = await PreApprovals.findOne({ partnerId: partnerMaster._id, merchantCode: model.userId, $or: [{ trxnId: model.trxnId.toString() }, { productId: model.trxnId.toString() }] });
                  output.message = !IsNullEmpty(preApproval) ? "Offer is expired" : "invalid trxnId " + model.trxnId.toString();
                } else {
                  const today = new Date();

                  if (Date.diff(today, preApproval.expiredOn).days() < 0) {
                    isValidTrxnId = true;
                  } else {
                    output.message = "Offer is expired";
                    return output;
                  }
                }
              } else {
                output.message = "invalid trxnId / OfferId " + model.trxnId;
                return output;
              }

              // console.log("amount", model.loanAmount);
              // console.log("preApproval", preApproval);
              // console.log("approvedAmount", preApproval.approvedAmount);
              if (model.loanAmount && model.loanAmount != null) {
                if (model.loanAmount > preApproval.approvedAmount) {
                  isValidTrxnId = false;
                  output.message = "loan_amount should be less than equal to offer amount";
                } else if (model.loanAmount < config.get("keys.minLoanAmount")) {
                  isValidTrxnId = false;
                  output.message = `loan_amount should be greater than ${config.get("keys.minLoanAmount")}`;
                } else if (model.loanAmount % config.get("keys.loanAmountMultiple") != 0) {
                  isValidTrxnId = false;
                  output.message = `loan_amount should be multiple of ${config.get("keys.loanAmountMultiple")}`;
                }
              }
              logger.logs("preApproval", preApproval);

              if (isValidTrxnId && (preApproval.productType.toLowerCase() === "dl" || preApproval.productType.toLowerCase() === "tl-m" || preApproval.productType.toLowerCase() === "tl" || (preApproval.productType.toLowerCase() === "lc" && partnerMaster.partnerCode.toLowerCase() == "sd"))) {
                let apiResponse = null;

                if (partnerMaster.partnerCode.toLowerCase() == "pay") {
                  const tokenReponse = await getPayOneToken();
                  if (!IsNullEmpty(tokenReponse) && !IsNullEmpty(tokenReponse.success) && tokenReponse.success == true && !IsNullEmpty(tokenReponse.data.api_token)) {
                    //logger.logs("tokenReponse.data", tokenReponse.data);
                    let token = "Basic " + tokenReponse.data.api_token;

                    if (!IsNullEmpty(token)) {
                      let urlUserData = config.get("pay.userDataUrl") + model.userId;

                      //logger.logs("urlUserData :", urlUserData);
                      //logger.logs("token :", token);

                      apiResponse = await fetch(urlUserData, {
                        method: "POST",
                        body: JSON.stringify(""),
                        headers: {
                          "Content-Type": "application/json",
                          "x-api-token": token,
                        },
                      })
                        .then(function (u) {
                          return u.json();
                        })
                        .then(function (json) {
                          return json;
                        })
                        .catch((err) => {
                          console.error("err :", err);
                          return err;
                        });

                      //logger.logs("apiResponse :", apiResponse);

                    } else {
                      output.message = "Token not received from Pay1";
                    }
                  } else {
                    output.message = "Token not received from Pay1";
                  }
                } else {
                  const conf = config.get(partnerMaster.partnerCode.toLowerCase());
                  const url = conf.userDataUrl + model.userId;
                  apiResponse = await fetch(url, {
                    method: "Get",
                    headers: conf.headerConstants
                  })
                    .then(function (u) {
                      return u.json();
                    })
                    .then(function (json) {
                      return json;
                    })
                    .catch((err) => {
                      console.error("err :", err);
                      return err;
                    });
                }
                //console.log("Response : ", apiResponse);

                // if (!apiResponse.pan)
                //   apiResponse.pan = "**********";

                if (!IsNullEmpty(apiResponse) && !IsNullEmpty(apiResponse.status) && apiResponse.status == "success") {

                  const partnerConfig = await PartnerConfig.findOne({ partnerId: partnerMaster._id });

                  //if disbursal type is virtual then virtual account number is mandatory
                  if (IsNullEmpty(apiResponse.virtualAccountNo) && partnerConfig.disbursalType && partnerConfig.disbursalType.toLowerCase() === "virtual") {
                    output.message = `Virtual account number not received from Enterprise partner ${partnerMaster.partnerName}`;
                    return output;
                  }

                  let firstName = "";
                  let middleName = "";
                  let lastName = "";
                  if (!IsNullEmpty(apiResponse.name)) {
                    let names = apiResponse.name.split(" ");
                    if (names.length > 0) {
                      firstName = names[0];
                    }

                    if (names.length == 2) {
                      lastName = names[1];
                    } else {
                      for (i = 2; i < names.length - 1; i++) {
                        middleName = middleName + " " + names[i];
                      }
                      middleName = middleName.trim();

                      lastName = names[names.length - 1];
                    }
                  }

                  if (!IsNullEmpty(apiResponse.pan) && !IsNullEmpty(apiResponse.name) && !IsNullEmpty(apiResponse.mobile)) {
                    if (validatePan(apiResponse.pan.toUpperCase()) && validateMobileNumber(apiResponse.mobile) && (IsNullEmpty(apiResponse.email_id) || (!IsNullEmpty(apiResponse.email_id) && validateEmail(apiResponse.email_id)))
                    ) {
                      let partnerLoan = null;

                      partnerLoan = await PartnerLoans.findOne({
                        partnerId: mongoose.Types.ObjectId(partnerMaster._id),
                        merchantCode: model.userId,
                        productId: model.trxnId
                      });

                      if (IsNullEmpty(partnerLoan)) {
                        partnerLoan = await PartnerLoans.findOne({
                          partnerId: mongoose.Types.ObjectId(partnerMaster._id),
                          merchantCode: model.userId,
                          trxnId: model.trxnId
                        });
                      }

                      let state = !IsNullEmpty(apiResponse.state) ? apiResponse.state.toUpperCase() : null;
                      let city = !IsNullEmpty(apiResponse.city) ? apiResponse.city.toUpperCase() : null;

                      if (!IsNullEmpty(apiResponse.pincode)) {
                        const stateCityMaster = await getCityState(partnerMaster._id, model.userId, apiResponse.state, apiResponse.city, apiResponse.pincode);
                        state = stateCityMaster.state;
                        city = stateCityMaster.city;
                      }


                      //if (existingLoan && existingLoan != null && loanStatus == "Initiated") {
                      if ((existingLoan && existingLoan != null && loanStatus == "Initiated") || partnerLoan != null) {
                        if (!IsNullEmpty(partnerLoan)) {
                          if (partnerLoan.panNo.toUpperCase() !== apiResponse.pan.toUpperCase()) {
                            await backupPartnerLoans(partnerLoan._id);
                          }
                          partnerLoan.partnerId = partnerMaster._id;
                          partnerLoan.panNo = apiResponse.pan.toUpperCase();
                          partnerLoan.clientCode = null;
                          partnerLoan.merchantCode = model.userId;
                          partnerLoan.virtualAccountNo = !IsNullEmpty(apiResponse.virtualAccountNo) ? apiResponse.virtualAccountNo : null;
                          partnerLoan.ipAddress = !IsNullEmpty(apiResponse.ipAddress) ? apiResponse.ipAddress : null;
                          partnerLoan.loanRequest = {
                            firstName: firstName,
                            middleName: middleName,
                            lastName: lastName,
                            mobileNo: apiResponse.mobile,
                            emailId: apiResponse.email_id,
                            aadhaarNo: null,
                            city: city,
                            street: null,
                            state: state,
                            pincode: !IsNullEmpty(apiResponse.pincode) ? apiResponse.pincode : null,
                            merchantType: null,
                            onboardedDate: !IsNullEmpty(apiResponse.business_activation_date) ? apiResponse.business_activation_date : null,
                            isWeekendLoan: null,
                            isActive: true,
                            insertedOn: Date.now(),
                            modifiedOn: null,
                            deactivatedOn: null,
                          };
                          partnerLoan.personalDetails = {
                            name: apiResponse.name.trim(),
                            gender: null,
                            qualification: null,
                            dob: apiResponse.dob,
                            maritalStatus: null,
                            mobile: apiResponse.mobile.trim(),
                            alternateMobile: null,
                            homeOwnershipType: null,
                            currentAdd: !IsNullEmpty(apiResponse.address) ? apiResponse.address : null,
                            city: city,
                            state: state,
                            pincode: !IsNullEmpty(apiResponse.pincode) ? apiResponse.pincode.trim() : null,
                            marital_status: null,
                          };
                          partnerLoan.shopDetails = {
                            name: !IsNullEmpty(apiResponse.shop_name) ? apiResponse.shop_name.trim() : null,
                            type: !IsNullEmpty(apiResponse.shop_type) ? apiResponse.shop_type.trim() : null,
                            add: !IsNullEmpty(apiResponse.shop_address) ? apiResponse.shop_address.trim() : null,
                            city: !IsNullEmpty(apiResponse.shop_city) ? apiResponse.shop_city.trim().toUpperCase() : null,
                            state: !IsNullEmpty(apiResponse.shop_state) ? apiResponse.shop_state.trim().toUpperCase() : null,
                            pincode: !IsNullEmpty(apiResponse.shop_pincode) ? apiResponse.shop_pincode.trim() : null,
                            location: !IsNullEmpty(apiResponse.shop_location) ? apiResponse.shop_location.trim() : null,
                            contactNo: null,
                            ownershipType: !IsNullEmpty(apiResponse.shop_ownership) ? apiResponse.shop_ownership.trim() : null,
                            companyType: null,
                            shopVinatge: null,
                            businessVintage: null,
                          };
                          partnerLoan.accoutDetails = {
                            mobile: apiResponse.mobile.trim(),
                            panNo: apiResponse.pan.trim(),
                            merchantCode: model.userId,
                          };
                        }
                      } else {
                        partnerLoan = new PartnerLoans({
                          partnerId: partnerMaster._id,
                          trxnId: model.trxnId,
                          productId: (partnerMaster.partnerCode.toLowerCase() == "bnkit" || partnerMaster.partnerCode.toLowerCase() == "inmd" || partnerMaster.partnerCode.toLowerCase() == "epay" || partnerMaster.partnerCode.toLowerCase() == "sd") ? model.trxnId : preApproval.productId,
                          loanId: null,
                          panNo: apiResponse.pan.toUpperCase(),
                          clientCode: null,
                          merchantCode: model.userId,
                          virtualAccountNo: !IsNullEmpty(apiResponse.virtualAccountNo) ? apiResponse.virtualAccountNo : null,
                          ipAddress: !IsNullEmpty(apiResponse.ipAddress) ? apiResponse.ipAddress : null,
                          loanRequest: {
                            firstName: firstName,
                            middleName: middleName,
                            lastName: lastName,
                            mobileNo: apiResponse.mobile,
                            emailId: apiResponse.email_id,
                            aadhaarNo: null,
                            city: city,
                            street: null,
                            state: state,
                            pincode: !IsNullEmpty(apiResponse.pincode) ? apiResponse.pincode : null,
                            merchantType: null,
                            loanAmount: (model.loanAmount && model.loanAmount != null) ? model.loanAmount : preApproval.approvedAmount,
                            onboardedDate: !IsNullEmpty(apiResponse.business_activation_date) ? apiResponse.business_activation_date : null,
                            isWeekendLoan: null,
                            isActive: true,
                            insertedOn: Date.now(),
                            modifiedOn: null,
                            deactivatedOn: null,
                          },
                          personalDetails: {
                            name: apiResponse.name.trim(),
                            gender: null,
                            qualification: null,
                            dob: apiResponse.dob,
                            maritalStatus: null,
                            mobile: apiResponse.mobile.trim(),
                            alternateMobile: null,
                            homeOwnershipType: null,
                            currentAdd: !IsNullEmpty(apiResponse.address) ? apiResponse.address : null,
                            city: city,
                            state: state,
                            pincode: !IsNullEmpty(apiResponse.pincode) ? apiResponse.pincode.trim() : null,
                            marital_status: null,
                          },
                          shopDetails: {
                            name: !IsNullEmpty(apiResponse.shop_name) ? apiResponse.shop_name.trim() : null,
                            type: !IsNullEmpty(apiResponse.shop_type) ? apiResponse.shop_type.trim() : null,
                            add: !IsNullEmpty(apiResponse.shop_address) ? apiResponse.shop_address.trim() : null,
                            city: !IsNullEmpty(apiResponse.shop_city) ? apiResponse.shop_city.trim() : null,
                            state: !IsNullEmpty(apiResponse.shop_state) ? apiResponse.shop_state.trim() : null,
                            pincode: !IsNullEmpty(apiResponse.shop_pincode) ? apiResponse.shop_pincode.trim() : null,
                            location: !IsNullEmpty(apiResponse.shop_location) ? apiResponse.shop_location.trim() : null,
                            contactNo: null,
                            ownershipType: !IsNullEmpty(apiResponse.shop_ownership) ? apiResponse.shop_ownership.trim() : null,
                            companyType: null,
                            shopVinatge: null,
                            businessVintage: null,
                          },
                          accoutDetails: {
                            mobile: apiResponse.mobile.trim(),
                            panNo: apiResponse.pan.trim(),
                            merchantCode: model.userId,
                          },
                          insertedOn: Date.now()
                        });
                      }

                      logger.logs("apiResponse.bank_accounts", apiResponse.bank_accounts);
                      if (!IsNullEmpty(apiResponse.bank_accounts)) {
                        for (bankAccount of apiResponse.bank_accounts) {
                          let bankDetails = {
                            accountName: !IsNullEmpty(bankAccount.account_holder_name) ? bankAccount.account_holder_name.trim() : null,
                            bankName: !IsNullEmpty(bankAccount.bank_name) ? bankAccount.bank_name.trim() : null,
                            accountNumber: !IsNullEmpty(bankAccount.account_number) ? bankAccount.account_number.trim() : null,
                            ifsc: !IsNullEmpty(bankAccount.ifsc_code) ? bankAccount.ifsc_code.trim() : null,
                            bankAddress: !IsNullEmpty(bankAccount.bankAddress) ? bankAccount.bankAddress.trim() : null,
                          };
                          partnerLoan.bankDetails = bankDetails;
                        }
                      }

                      if (!IsNullEmpty(apiResponse.pan) && !IsNullEmpty(apiResponse.pan_front)) {
                        const document = {
                          docNo: apiResponse.pan,
                          docType: "pan_front",
                          docPath: apiResponse.pan_front,
                          docExtension: null
                        };
                        partnerLoan.documents.push(document);
                      }

                      if (!IsNullEmpty(apiResponse.voter_id) && !IsNullEmpty(apiResponse.voter_id_front)) {
                        const document = {
                          docNo: apiResponse.voter_id,
                          docType: "voter_id_front",
                          docPath: apiResponse.voter_id_front,
                          docExtension: null
                        };
                        partnerLoan.documents.push(document);
                      }

                      if (!IsNullEmpty(apiResponse.voter_id) && !IsNullEmpty(apiResponse.voter_id_back)) {
                        const document = {
                          docNo: apiResponse.voter_id,
                          docType: "voter_id_back",
                          docPath: apiResponse.voter_id_back,
                          docExtension: null
                        };
                        partnerLoan.documents.push(document);
                      }

                      if (!IsNullEmpty(apiResponse.dl) && !IsNullEmpty(apiResponse.dl_front)) {
                        const document = {
                          docNo: apiResponse.dl,
                          docType: "dl_front",
                          docPath: apiResponse.dl_front,
                          docExtension: null
                        };
                        partnerLoan.documents.push(document);
                      }

                      if (!IsNullEmpty(apiResponse.dl) && !IsNullEmpty(apiResponse.dl_back)) {
                        const document = {
                          docNo: apiResponse.dl,
                          docType: "dl_back",
                          docPath: apiResponse.dl_back,
                          docExtension: null
                        };
                        partnerLoan.documents.push(document);
                      }

                      if (!IsNullEmpty(apiResponse.aadhaar) && !IsNullEmpty(apiResponse.aadhaar_front)) {
                        const document = {
                          docNo: apiResponse.aadhaar,
                          docType: "aadhaar_front",
                          docPath: apiResponse.aadhaar_front,
                          docExtension: null
                        };
                        partnerLoan.documents.push(document);
                      }

                      if (!IsNullEmpty(apiResponse.aadhaar) && !IsNullEmpty(apiResponse.aadhaar_back)) {
                        const document = {
                          docNo: apiResponse.aadhaar,
                          docType: "aadhaar_back",
                          docPath: apiResponse.aadhaar_back,
                          docExtension: null
                        };
                        partnerLoan.documents.push(document);
                      }

                      await partnerLoan.save();

                      const pushResponse = await PushToFundfina(partnerLoan._id, model.isAutoLogin).catch(error => {
                        return error;
                      });

                      if (!IsNullEmpty(pushResponse) && !IsNullEmpty(pushResponse.success) && pushResponse.success == true) {
                        const productId = pushResponse.data;

                        let _data = {
                          productId: productId,
                        };

                        let userInfo = await UserInfo.findOne({
                          panNo: {
                            $regex: new RegExp(apiResponse.pan, "i"),
                          },
                        });

                        if (!IsNullEmpty(userInfo)) {
                          _data.userId = userInfo._id;
                          _data.kycStatus = userInfo.userDetails.kycStatus;

                          let loanInfo = await LoanInfo.findOne({
                            userId: mongoose.Types.ObjectId(userInfo._id),
                            productId: productId,
                          });

                          if (!IsNullEmpty(loanInfo)) {
                            _data.loanId = loanInfo._id;
                            _data.loanStatus = loanInfo.status;
                            _data.isDraft = loanInfo.isDraft;
                          }

                          const internalPath = userInfo._id;

                          if (!userInfo.panDocVerification || (userInfo.panDocVerification && userInfo.panDocVerification.type !== "digilocker")) {
                            if (!IsNullEmpty(apiResponse.pan_front)) {
                              await uploadImage(apiResponse.pan, "pan_front", apiResponse.pan_front, userInfo);
                              ////logger.logs("pan uploaded sucessfully");
                            }

                            if (!IsNullEmpty(apiResponse.voter_id_front)) {
                              await uploadImage(apiResponse.voter_id, "voter_id_front", apiResponse.voter_id_front, userInfo);
                            }

                            if (!IsNullEmpty(apiResponse.voter_id_back)) {
                              await uploadImage(apiResponse.voter_id, "voter_id_back", apiResponse.voter_id_back, userInfo);
                            }

                            if (!IsNullEmpty(apiResponse.dl_front)) {
                              await uploadImage(apiResponse.dl, "dl_front", apiResponse.dl_front, userInfo);
                            }

                            if (!IsNullEmpty(apiResponse.dl_back)) {
                              await uploadImage(apiResponse.dl, "dl_back", apiResponse.dl_back, userInfo);
                            }

                            if (!IsNullEmpty(apiResponse.aadhaar_front)) {
                              await uploadImage(apiResponse.aadhaar, "aadhaar_front", apiResponse.aadhaar_front, userInfo);
                            }

                            if (!IsNullEmpty(apiResponse.aadhaar_back)) {
                              await uploadImage(apiResponse.aadhaar, "aadhaar_back", apiResponse.aadhaar_back, userInfo);
                            }
                          }

                          let response = await userInfo.save();

                          if (response) {
                            validateKYC(userInfo._id, partnerMaster._id, "partnerMerchant");
                          }
                        }

                        output = {
                          data: _data,
                          success: true,
                          message: "data fetched successfully",
                          status: 200,
                        };
                      } else {
                        output = {
                          data: pushResponse,
                          success: false,
                          message: pushResponse.message,
                          status: 200,
                        };
                      }

                      await PartnerLoans.findOneAndUpdate({ _id: mongoose.Types.ObjectId(partnerLoan._id) }, {
                        $set: {
                          responseSuccess: pushResponse.success,
                          responseMessage: pushResponse.message
                        }
                      }, { new: true }).lean();
                      //logger.logs("output :", pushResponse);
                    } else if (!validatePan(apiResponse.pan.toUpperCase())) {
                      output.message = "Invalid Pan No " + apiResponse.pan;
                    } else if (!validateMobileNumber(apiResponse.mobile)) {
                      output.message = "Invalid Mobile No " + apiResponse.mobile;
                    } else if (!IsNullEmpty(apiResponse.email_id) && !validateEmail(apiResponse.email_id)) {
                      output.message = "Invalid Email Id " + apiResponse.email_id;
                    }
                  } else {
                    let missingFields = "";
                    if (IsNullEmpty(apiResponse.pan))
                      missingFields = ",pan";
                    if (IsNullEmpty(apiResponse.name))
                      missingFields = ",name";
                    if (IsNullEmpty(apiResponse.mobile))
                      missingFields = ",mobile";
                    output.message = "please provide mandatory fields " + missingFields.substring(1, missingFields.length);
                  }
                } else {
                  const model1 = { authKey: authKey, model: model };
                  saveErrorLog("getUserDetails--Issue From Partner API", apiPath, model1, apiResponse);
                  output.data = apiResponse;
                  output.message = "Error from " + partnerMaster.displayName + " :" + apiResponse.desc;
                }
              } else if (isValidTrxnId && preApproval.productType.toLowerCase() === "lc") {
                logger.logs("fetching line of credit");
                const lcResponse = await fetchLineOfCreditData(partnerMaster._id, model.userId, model.trxnId);
                if (lcResponse.success) {
                  output = {
                    data: lcResponse.data,
                    success: true,
                    message: "data fetched successfully",
                    status: 200,
                  };
                } else {
                  output.message = lcResponse.message;
                }
              }
            }
          } else {
            output.message = "Please share your apis with Fundfina for implementation";
          }
        } else {
          output.Description = "Config is not set by Fundfina. Please contact Fundfina."
        }
      } else {
        output.message = "Please register as partner";
        const model1 = { authKey: authKey, model: model };
        saveErrorLog("getUserDetails", apiPath, model1, "Please register as partner");
      }
    } else {
      output.message = "auth-key is mandatory";
      const model1 = { authKey: authKey, model: model };
      saveErrorLog("getUserDetails", apiPath, model1, "auth-key is mandatory");
    }
  } catch (ex) {
    logger.logs(ex);
    output.message = ex.message;
    const model1 = { authKey: authKey, model: model };
    saveErrorLog("getUserDetails", apiPath, model1, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
  }
  return output;
};

async function uploadImage(docNo, imageType, imageUrl, userInfo) {
  try {
    const internalPath = userInfo._id;

    let fileName = "";
    let filePath = "";
    let position = "";
    let poaType = "";
    let documents = null;

    switch (imageType) {
      case "pan_front":
        fileName = `pan_front_${getCurrentTimeStamp()}.jpg`;
        filePath = internalPath + "/PAN/" + fileName;
        position = "front";
        poaType = "PAN";
        break;
      case "voter_id_front":
        fileName = `voter_id_front_${getCurrentTimeStamp()}.jpg`;
        filePath = internalPath + "/POA/" + fileName;
        position = "front";
        poaType = "Voter Id";
        break;
      case "voter_id_back":
        fileName = `voter_id_back_${getCurrentTimeStamp()}.jpg`;
        filePath = internalPath + "/POA/" + fileName;
        position = "back";
        poaType = "Voter Id";
        break;
      case "dl_front":
        fileName = `dl_front_${getCurrentTimeStamp()}.jpg`;
        filePath = internalPath + "/POA/" + fileName;
        position = "front";
        poaType = "Driving Licence";
        break;
      case "dl_back":
        fileName = `dl_back_${getCurrentTimeStamp()}.jpg`;
        filePath = internalPath + "/POA/" + fileName;
        position = "back";
        poaType = "Driving Licence";
        break;
      case "aadhaar_front":
        fileName = `aadhaar_front_${getCurrentTimeStamp()}.jpg`;
        filePath = internalPath + "/POA/" + fileName;
        position = "front";
        poaType = "Aadhaar Card";
        break;
      case "aadhaar_back":
        fileName = `aadhaar_back_${getCurrentTimeStamp()}.jpg`;
        filePath = internalPath + "/POA/" + fileName;
        position = "back";
        poaType = "Aadhaar Card";
        break;
    }
    let isExists = false;


    if (imageType == "pan_front") {
      if (IsNullEmpty(userInfo.panDocDetails)) {
        let downloadResponse = await downloadImageBuffer(imageUrl);

        if (downloadResponse.success) {
          filePath = "ProofImages/" + filePath;
          await uploadToS3(filePath, downloadResponse.data, "BUFFER");

          const panDoc = {
            fileName: fileName,
            filePath: filePath,
            position: position,
            isActive: true,
            insertedOn: Date.now(),
          };
          userInfo.panDocDetails.push(panDoc);
        }
      }
    } else {
      let poa = await userInfo.poaDetails.find((x) => x.poaNo.substring(x.poaNo.length - 4) == docNo.substring(docNo.length - 4));
      // let poa = await userInfo.poaDetails.find((x) => x.poaNo.substring(x.poaNo.length - 4) == docNo.substring(docNo.length - 4) && x.poaType == poaType);

      if (!IsNullEmpty(poa)) {
        if (!IsNullEmpty(poa.docDetails)) {
          const poaDoc = poa.docDetails.filter((x) => x.position == position);
          if (!IsNullEmpty(poaDoc)) {
            isExists = true;
          }
        }

        if (!isExists) {
          const downloadResponse = await downloadImageBuffer(imageUrl);

          if (downloadResponse.success) {
            filePath = "ProofImages/" + filePath;
            await uploadToS3(filePath, downloadResponse.data, "BUFFER");

            let _docDetails = {
              fileName: fileName,
              filePath: filePath,
              position: position,
              isActive: true,
              insertedOn: Date.now(),
            };

            poa.docDetails.push(_docDetails);
          }
        }
      } else {
        const downloadResponse = await downloadImageBuffer(imageUrl);

        if (downloadResponse.success) {
          filePath = "ProofImages/" + filePath;
          await uploadToS3(filePath, downloadResponse.data, "BUFFER");

          const poaDoc = {
            poaNo: docNo,
            poaType: poaType,
            poaVerificationDocType: poaType,
            docDetails: [
              {
                fileName: fileName,
                filePath: filePath,
                position: position,
                isActive: true,
                insertedOn: Date.now(),
              },
            ],
            insertedOn: Date.now(),
          };
          userInfo.poaDetails.push(poaDoc);
        }
      }
    }

  } catch (ex) {
    const model = { docNo: docNo, imageType: imageType, imageUrl: imageUrl, userInfo: userInfo };
    saveErrorLog("uploadImage", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
  }
};

module.exports.getTransactionData = async function (model) {
  return new Promise(async (resolve, reject) => {
    let output = {
      data: null,
      success: false,
      message: "Some Error Occurred",
      currentPage: 0,
      totalPages: 0,
      recordsInserted: 0,
      pagesInserted: "",
      status: 501,
    };

    try {
      if (
        !IsNullEmpty(model.pageNo) &&
        !IsNullEmpty(model.isSinglePage) &&
        typeof model.pageNo == "number" &&
        typeof model.isSinglePage == "boolean"
      ) {
        let currentPage = model.pageNo;
        let totalPages = model.pageNo;

        output.currentPage = model.pageNo;

        const partnerMaster = await PartnerMaster.findOne({
          partnerCode: { $regex: new RegExp(model.partnerCode, "i") },
        });

        if (!IsNullEmpty(partnerMaster)) {
          while (currentPage <= totalPages) {
            //logger.logs("currentPage", currentPage);
            //logger.logs("totalPages", totalPages);

            output.currentPage = currentPage;

            const tokenReponse = await getPayOneToken();

            apiToken = "Basic " + tokenReponse.data.api_token;
            const notificationUrl = config.get("payOne.transactionData");
            const requestNotification = { page_number: currentPage };
            const requestBody = JSON.stringify(requestNotification);

            let apiResponse = await fetch(notificationUrl, {
              method: "POST",
              body: requestBody,
              headers: {
                "Content-Type": "application/json",
                "x-api-token": apiToken,
              },
            })
              .then(function (u) {
                return u.json();
              })
              .then(function (json) {
                return json;
              })
              .catch((err) => console.error("err :", err));

            if (
              !IsNullEmpty(apiResponse) &&
              !IsNullEmpty(apiResponse.status) &&
              apiResponse.status == "success"
            ) {
              if (!IsNullEmpty(apiResponse.transactional_data)) {
                output.totalPages = apiResponse.total_pages;

                if (!isSinglePage) totalPages = apiResponse.total_pages;

                for (itemTransaction of apiResponse.transactional_data) {
                  if (!IsNullEmpty(itemTransaction.user_id)) {
                    let partnerTransactions = await PartnerTransactions.findOne(
                      {
                        merchantCode: {
                          $regex: new RegExp(itemTransaction.user_id, "i"),
                        },
                      }
                    );

                    if (IsNullEmpty(partnerTransactions)) {
                      partnerTransactions = new PartnerTransactions({
                        partnerId: partnerMaster._id,
                        merchantCode: itemTransaction.user_id,
                        mobileNo: IsNullEmpty(itemTransaction.mobileNo) ? null : itemTransaction.mobileNo,
                        emailId: IsNullEmpty(itemTransaction.emailId) ? null : itemTransaction.mobileNo,
                        onboardedDate: IsNullEmpty(itemTransaction.joined_on)
                          ? null
                          : itemTransaction.joined_on,
                        pincode: IsNullEmpty(itemTransaction.pincode)
                          ? null
                          : itemTransaction.pincode,
                        pageNo: currentPage,
                        totalPages: IsNullEmpty(apiResponse.total_pages)
                          ? null
                          : apiResponse.total_pages,
                        requestRef: IsNullEmpty(apiResponse.request_ref)
                          ? null
                          : apiResponse.request_ref,
                        isNewTransaction: true,
                        insertedOn: Date.now(),
                        modifiedOn: null,
                      });
                    } else {
                      partnerTransactions.isNewTransaction = true;
                      partnerTransactions.modifiedOn = Date.now();
                    }

                    await pushTransactions(apiResponse, itemTransaction, partnerTransactions);

                    await partnerTransactions.save();

                    output.recordsInserted++;
                  }
                }
              } else {
                output.message = "transactional_data is empty";
                totalPages = currentPage;
                output.data = apiResponse;
                reject(output);
              }
            } else {
              output.data = apiResponse;
              reject(output);
            }
            output.pagesInserted =
              output.pagesInserted + String(currentPage) + ",";
            currentPage = currentPage + 1;
          }
          //output.message=output.message + " total pages saved";
          if (output.recordsInserted > 0) {
            output.message = "Inserted Successfully";
            output.success = true;
            resolve(output);
          }
        } else {
          output.message = "Partner Not Found";
          reject(output);
        }
      } else {
        output.message = "Valid values for pageNo and isSinglePage required";
        reject(output);
      }
    } catch (ex) {
      saveErrorLog("getTransactionData", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
      output.data = ex.message;
      reject(output);
    }
  });
};

async function pushTransactions(apiResponse, itemTransaction, partnerTransactions) {
  try {
    let months = [
      "jan",
      "feb",
      "mar",
      "apr",
      "may",
      "jun",
      "jul",
      "aug",
      "sep",
      "oct",
      "nov",
      "dec",
    ];
    if (!IsNullEmpty(itemTransaction.transaction_summary)) {
      for (itemTransactionSummary of itemTransaction.transaction_summary) {
        //Total Days
        if (!IsNullEmpty(itemTransactionSummary.total_days)) {
          let totalDays = partnerTransactions.totalDays.find(
            (x) => x.year == itemTransactionSummary.year
          );

          if (IsNullEmpty(totalDays)) {
            totalDays = {
              year: itemTransactionSummary.year,
              jan: 0,
              feb: 0,
              mar: 0,
              apr: 0,
              may: 0,
              jun: 0,
              jul: 0,
              aug: 0,
              sep: 0,
              oct: 0,
              nov: 0,
              dec: 0,
            };
            totalDays[months[itemTransactionSummary.month - 1]] =
              itemTransactionSummary.total_days;
            partnerTransactions.totalDays.push(totalDays);
          } else {
            for (itemTotalDays of partnerTransactions.totalDays) {
              if (itemTotalDays.year == itemTransactionSummary.year) {
                itemTotalDays[months[itemTransactionSummary.month - 1]] =
                  itemTransactionSummary.total_days;
              }
            }
          }
        }

        //Active Days
        if (!IsNullEmpty(itemTransactionSummary.active_days)) {
          let activeDays = partnerTransactions.activeDays.find(
            (x) => x.year == itemTransactionSummary.year
          );

          if (IsNullEmpty(activeDays)) {
            activeDays = {
              year: itemTransactionSummary.year,
              jan: 0,
              feb: 0,
              mar: 0,
              apr: 0,
              may: 0,
              jun: 0,
              jul: 0,
              aug: 0,
              sep: 0,
              oct: 0,
              nov: 0,
              dec: 0,
            };
            activeDays[months[itemTransactionSummary.month - 1]] =
              itemTransactionSummary.active_days;
            partnerTransactions.activeDays.push(activeDays);
          } else {
            for (itemActiveDays of partnerTransactions.activeDays) {
              if (itemActiveDays.year == itemTransactionSummary.year) {
                itemActiveDays[months[itemTransactionSummary.month - 1]] =
                  itemTransactionSummary.active_days;
              }
            }
          }
        }

        //Transaction Count
        if (!IsNullEmpty(itemTransactionSummary.count)) {
          let tranCount = partnerTransactions.tranCount.find(
            (x) => x.year == itemTransactionSummary.year
          );

          if (IsNullEmpty(tranCount)) {
            tranCount = {
              year: itemTransactionSummary.year,
              jan: 0,
              feb: 0,
              mar: 0,
              apr: 0,
              may: 0,
              jun: 0,
              jul: 0,
              aug: 0,
              sep: 0,
              oct: 0,
              nov: 0,
              dec: 0,
            };
            tranCount[months[itemTransactionSummary.month - 1]] =
              itemTransactionSummary.count;
            partnerTransactions.tranCount.push(tranCount);
          } else {
            for (itemtranCount of partnerTransactions.tranCount) {
              if (itemtranCount.year == itemTransactionSummary.year) {
                itemtranCount[months[itemTransactionSummary.month - 1]] =
                  itemTransactionSummary.count;
              }
            }
          }
        }

        //Transaction Volume
        if (!IsNullEmpty(itemTransactionSummary.volume)) {
          let tranVol = partnerTransactions.tranVol.find(
            (x) => x.year == itemTransactionSummary.year
          );

          if (IsNullEmpty(tranVol)) {
            tranVol = {
              year: itemTransactionSummary.year,
              jan: 0,
              feb: 0,
              mar: 0,
              apr: 0,
              may: 0,
              jun: 0,
              jul: 0,
              aug: 0,
              sep: 0,
              oct: 0,
              nov: 0,
              dec: 0,
            };
            tranVol[months[itemTransactionSummary.month - 1]] =
              itemTransactionSummary.volume;
            partnerTransactions.tranVol.push(tranVol);
          } else {
            for (itemtranVol of partnerTransactions.tranVol) {
              if (itemtranVol.year == itemTransactionSummary.year) {
                itemtranVol[months[itemTransactionSummary.month - 1]] =
                  itemTransactionSummary.volume;
              }
            }
          }
        }

        //Minimum Wallet Balance
        if (!IsNullEmpty(itemTransactionSummary.min_wallet_balance)) {
          let minWalletBalance = partnerTransactions.minWalletBalance.find(
            (x) => x.year == itemTransactionSummary.year
          );

          if (IsNullEmpty(minWalletBalance)) {
            minWalletBalance = {
              year: itemTransactionSummary.year,
              jan: 0,
              feb: 0,
              mar: 0,
              apr: 0,
              may: 0,
              jun: 0,
              jul: 0,
              aug: 0,
              sep: 0,
              oct: 0,
              nov: 0,
              dec: 0,
            };
            minWalletBalance[months[itemTransactionSummary.month - 1]] =
              itemTransactionSummary.min_wallet_balance;
            partnerTransactions.minWalletBalance.push(minWalletBalance);
          } else {
            for (itemMinWalletBalance of partnerTransactions.minWalletBalance) {
              if (itemMinWalletBalance.year == itemTransactionSummary.year) {
                itemMinWalletBalance[months[itemTransactionSummary.month - 1]] =
                  itemTransactionSummary.min_wallet_balance;
              }
            }
          }
        }

        //Maximum Wallet Balance
        if (!IsNullEmpty(itemTransactionSummary.max_wallet_balance)) {
          let maxWalletBalance = partnerTransactions.maxWalletBalance.find(
            (x) => x.year == itemTransactionSummary.year
          );

          if (IsNullEmpty(maxWalletBalance)) {
            maxWalletBalance = {
              year: itemTransactionSummary.year,
              jan: 0,
              feb: 0,
              mar: 0,
              apr: 0,
              may: 0,
              jun: 0,
              jul: 0,
              aug: 0,
              sep: 0,
              oct: 0,
              nov: 0,
              dec: 0,
            };
            maxWalletBalance[months[itemTransactionSummary.month - 1]] =
              itemTransactionSummary.max_wallet_balance;
            partnerTransactions.maxWalletBalance.push(maxWalletBalance);
          } else {
            for (itemMaxWalletBalance of partnerTransactions.maxWalletBalance) {
              if (itemMaxWalletBalance.year == itemTransactionSummary.year) {
                itemMaxWalletBalance[months[itemTransactionSummary.month - 1]] =
                  itemTransactionSummary.max_wallet_balance;
              }
            }
          }
        }

        //Verticles/Services
        if (!IsNullEmpty(itemTransactionSummary.verticals)) {
          for (itemVerticle of itemTransactionSummary.verticals) {
            //Service/Verticle Count
            if (!IsNullEmpty(itemVerticle.count)) {
              let serviceCount = partnerTransactions.serviceCount.find(
                (x) =>
                  x.year == itemTransactionSummary.year &&
                  x.serviceType == itemVerticle.service_name
              );

              if (IsNullEmpty(serviceCount)) {
                serviceCount = {
                  year: itemTransactionSummary.year,
                  serviceType: itemVerticle.service_name,
                  jan: 0,
                  feb: 0,
                  mar: 0,
                  apr: 0,
                  may: 0,
                  jun: 0,
                  jul: 0,
                  aug: 0,
                  sep: 0,
                  oct: 0,
                  nov: 0,
                  dec: 0,
                };

                serviceCount[months[itemTransactionSummary.month - 1]] =
                  itemVerticle.count;
                partnerTransactions.serviceCount.push(serviceCount);
              } else {
                for (itemServiceCount of partnerTransactions.serviceCount) {
                  if (
                    itemServiceCount.year == itemTransactionSummary.year &&
                    itemServiceCount.serviceType == itemVerticle.service_name
                  ) {
                    itemServiceCount[months[itemTransactionSummary.month - 1]] =
                      itemVerticle.count;
                  }
                }
              }
            }

            //Service/Verticle Volume
            if (!IsNullEmpty(itemVerticle.volume)) {
              let serviceVol = partnerTransactions.serviceVol.find(
                (x) =>
                  x.year == itemTransactionSummary.year &&
                  x.serviceType == itemVerticle.service_name
              );

              if (IsNullEmpty(serviceVol)) {
                serviceVol = {
                  year: itemTransactionSummary.year,
                  serviceType: itemVerticle.service_name,
                  jan: 0,
                  feb: 0,
                  mar: 0,
                  apr: 0,
                  may: 0,
                  jun: 0,
                  jul: 0,
                  aug: 0,
                  sep: 0,
                  oct: 0,
                  nov: 0,
                  dec: 0,
                };

                serviceVol[months[itemTransactionSummary.month - 1]] =
                  itemVerticle.volume;
                partnerTransactions.serviceVol.push(serviceVol);
              } else {
                for (itemServiceVol of partnerTransactions.serviceVol) {
                  if (
                    itemServiceVol.year == itemTransactionSummary.year &&
                    itemServiceVol.serviceType == itemVerticle.service_name
                  ) {
                    itemServiceVol[months[itemTransactionSummary.month - 1]] =
                      itemVerticle.volume;
                  }
                }
              }
            }
          }
        }
      }
    }
  } catch (ex) {
    const model = { apiResponse: apiResponse, itemTransaction: itemTransaction, partnerTransactions: partnerTransactions };
    saveErrorLog("pushTransactions", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
  }
};

async function fetchLineOfCreditData(_partnerId, _merchantCode, _productId) {
  let output = {
    data: null,
    success: false,
    message: "Some Error Occurred",
    status: 501,
  };
  const model1 = { _partnerId, _merchantCode, _productId };
  try {
    const preApproval = await PreApprovals.findOne({ partnerId: mongoose.Types.ObjectId(_partnerId), merchantCode: _merchantCode, productId: _productId });

    if (preApproval) {
      const loanInfo = await LoanInfo.findOne({ partnerId: mongoose.Types.ObjectId(_partnerId), merchantCode: _merchantCode, productId: _productId, isParentLoan: true });

      if (loanInfo) {
        const _data = {
          productId: _productId,
          userId: loanInfo.userId,
          loanId: loanInfo._id,
          loanStatus: loanInfo.status,
          isDraft: loanInfo.isDraft
        };

        output = {
          data: _data,
          success: true,
          message: "fetched successfully",
          status: 200,
        };
      } else {
        const userPartnerInfo = await UserPartnerInfo.findOne({ partnerId: mongoose.Types.ObjectId(_partnerId), merchantCode: _merchantCode });

        if (userPartnerInfo) {
          const userInfo = await UserInfo.findById(userPartnerInfo.userId);

          if (userInfo) {
            const charges = await loanService.getLoanCharges(preApproval.approvedAmount, null, preApproval._id);
            if (charges.success) {
              const partnerMaster = await PartnerMaster.findById(_partnerId);
              const partnerConfig = await PartnerConfig.findOne({ partnerId: mongoose.Types.ObjectId(_partnerId) });

              if (partnerMaster && partnerConfig) {
                const loanDetails = await loanService.getLoanId();

                const partnerLoan = new PartnerLoans({
                  partnerId: mongoose.Types.ObjectId(_partnerId),
                  trxnId: _productId,
                  productId: _productId,
                  loanId: loanDetails.data,
                  panNo: userInfo.panNo,
                  clientCode: null,
                  merchantCode: _merchantCode,
                  virtualAccountNo: null,
                  ipAddress: null,
                  loanRequest: {
                    firstName: userInfo.userDetails.firstName,
                    middleName: userInfo.userDetails.middleName,
                    lastName: userInfo.userDetails.lastName,
                    mobileNo: (userInfo.userDetails.mobileNo && userInfo.userDetails.mobileNo.length > 0) ? userInfo.userDetails.mobileNo[0].mobileNumber : null, //mobile number change
                    emailId: (userInfo.userDetails.emailId && userInfo.userDetails.emailId.length > 0) ? userInfo.userDetails.emailId[0] : null,
                    aadhaarNo: null,
                    city: (userInfo.addressDetails && userInfo.addressDetails.length > 0) ? userInfo.addressDetails[0].city : null,
                    street: null,
                    state: (userInfo.addressDetails && userInfo.addressDetails.length > 0) ? userInfo.addressDetails[0].state : null,
                    pincode: (userInfo.addressDetails && userInfo.addressDetails.length > 0) ? userInfo.addressDetails[0].pincode : null,
                    merchantType: null,
                    loanAmount: preApproval.approvedAmount,
                    onboardedDate: userPartnerInfo.onboardedDate,
                    isWeekendLoan: null,
                    isActive: true,
                    insertedOn: Date.now(),
                    modifiedOn: null,
                    deactivatedOn: null,
                  },
                  personalDetails: {
                    name: getName(userInfo.userDetails.firstName, userInfo.userDetails.middleName, userInfo.userDetails.lastName),
                    gender: null,
                    qualification: null,
                    dob: userInfo.userDetails.dob ? userInfo.userDetails.dob : null,
                    maritalStatus: null,
                    mobile: (userInfo.userDetails.mobileNo && userInfo.userDetails.mobileNo.length > 0) ? userInfo.userDetails.mobileNo[0].mobileNumber : null,
                    alternateMobile: null,
                    homeOwnershipType: null,
                    currentAdd: (userInfo.addressDetails && userInfo.addressDetails.length > 0) ? userInfo.addressDetails[0].add1 : null,
                    city: (userInfo.addressDetails && userInfo.addressDetails.length > 0) ? userInfo.addressDetails[0].city : null,
                    state: (userInfo.addressDetails && userInfo.addressDetails.length > 0) ? userInfo.addressDetails[0].state : null,
                    pincode: (userInfo.addressDetails && userInfo.addressDetails.length > 0) ? userInfo.addressDetails[0].pincode : null,
                    marital_status: null,
                  },
                  shopDetails: {
                    name: null,
                    type: null,
                    add: null,
                    city: null,
                    state: null,
                    pincode: null,
                    location: null,
                    contactNo: null,
                    ownershipType: null,
                    companyType: null,
                    shopVinatge: null,
                    businessVintage: null,
                  },
                  accoutDetails: {
                    mobile: (userInfo.userDetails.mobileNo && userInfo.userDetails.mobileNo.length > 0) ? userInfo.userDetails.mobileNo[0].mobileNumber : null,
                    panNo: userInfo.panNo,
                    merchantCode: _merchantCode,
                  },
                });

                await partnerLoan.save();

                const loanInfo = new LoanInfo({
                  userId: userInfo._id,
                  loanNo: loanDetails.data,
                  partner: partnerMaster.partnerName,
                  partnerId: partnerMaster._id,
                  merchantCode: _merchantCode,
                  merchantType: "Retailer",
                  trxnId: _productId,
                  productId: _productId,
                  productType: "LC_90",
                  amount: preApproval.approvedAmount,
                  trancheNo: null,
                  purpose: "Business",
                  description: "Working Capital Loan",
                  maturity: preApproval.tenureInDays / 30,
                  interestRate: charges.interestRate,
                  currency: "Indian Rupee",
                  campaignTime: "90 Days",
                  loanType: "EMI",
                  repaymentFrequency: "Monthly",
                  additionalDescription: null,
                  isMaturityMonth: false,
                  emiAmount: preApproval.emiAmt,
                  interestType: preApproval.interestType,
                  delinquencyString: null,
                  lenderDetails: [],
                  flowDetails: [],
                  loanContractDetails: null,
                  disbursalDetails: null,
                  isDraft: true,
                  status: "Initiated",
                  postDisbursalStatus: null,
                  adminRemarks: null,
                  clientRemarks: null,
                  rejectDescription: null,
                  isActive: true,
                  insertedOn: Date.now(),
                  updatedOn: Date.now(),
                  signatureDetails: null,
                  lastStepCompleted: null,
                  lastStepCompletedAt: null,
                  loanStepDetails: [],
                  adminModifiedOn: null,
                  ipAddress: null,
                  disbursalBankDetails: null,
                  primaryCollectionType: "",
                  secondaryCollectionType: "",
                  byPassEnach: false,
                  isParentLoan: true,
                  tenure: 90
                });

                loanInfo.loanStepDetails.push({
                  Step: "Contact Information",
                  isCompleted: true,
                  insertedOn: Date.now(),
                  completedOn: Date.now(),
                });

                loanInfo.loanStepDetails.push({
                  Step: "OTP Screen",
                  isCompleted: false,
                  insertedOn: Date.now(),
                  completedOn: null,
                });

                loanInfo.loanStepDetails.push({
                  Step: "OTP Matched",
                  isCompleted: false,
                  insertedOn: Date.now(),
                  completedOn: null
                });

                loanInfo.loanStepDetails.push({
                  Step: "Loan",
                  isCompleted: false,
                  insertedOn: Date.now(),
                  completedOn: null,
                });

                loanInfo.loanStepDetails.push({
                  Step: "KYC",
                  isCompleted: false,
                  insertedOn: Date.now(),
                  completedOn: null,
                });

                loanInfo.loanStepDetails.push({
                  Step: "Shop",
                  isCompleted: false,
                  insertedOn: Date.now(),
                  completedOn: null,
                });

                loanInfo.loanStepDetails.push({
                  Step: "Questionnairre",
                  isCompleted: false,
                  insertedOn: Date.now(),
                  completedOn: null,
                });

                loanInfo.loanStepDetails.push({
                  Step: "Review",
                  isCompleted: false,
                  insertedOn: Date.now(),
                  completedOn: null,
                });

                loanInfo.loanStepDetails.push({
                  Step: "Bank",
                  isCompleted: false,
                  insertedOn: Date.now(),
                  completedOn: null,
                });

                loanInfo.loanStepDetails.push({
                  Step: "eNach",
                  isCompleted: false,
                  insertedOn: Date.now(),
                  completedOn: null,
                });

                loanInfo.loanStepDetails.push({
                  Step: "Loan Contract",
                  isCompleted: false,
                  insertedOn: Date.now(),
                  completedOn: null,
                });

                loanInfo.lastStepCompleted = "Contact Information";
                loanInfo.lastStepCompletedAt = Date.now();

                await loanInfo.save();

                const _data = {
                  productId: _productId,
                  userId: loanInfo.userId,
                  loanId: loanInfo._id,
                  loanStatus: loanInfo.status,
                  isDraft: loanInfo.isDraft
                };
                output = {
                  data: _data,
                  success: true,
                  message: "saved successfully",
                  status: 200,
                };
              } else {
                output.message = "No User Details Found";
                saveErrorLog("Parner Not Found", apiPath, model1, output.message);
              }
            } else {
              output.message = charges.message;
              saveErrorLog("fetchLineOfCreditData", apiPath, model1, output.message);
            }
          } else {
            output.message = "No User Details Found";
            saveErrorLog("fetchLineOfCreditData", apiPath, model1, output.message);
          }

        } else {
          output.message = "No User Partner Info Found";
          saveErrorLog("fetchLineOfCreditData", apiPath, model1, output.message);
        }
      }
    } else {
      output.message = "No Offer Found";
      saveErrorLog("fetchLineOfCreditData", apiPath, model1, output.message);
    }
  } catch (ex) {
    logger.logs("Error", ex);
    output.message = ex.message;
    saveErrorLog("fetchLineOfCreditData", apiPath, model1, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
  }
  ////logger.logs("output", output);
  return output;
};
// ------------------------------------------------------------------------------------------------------------------------------------------
