//Libraries
// -------------------------------------------------------------------------------------------------------------------------
const mongoose = require("mongoose");
const moment = require("moment");
const axios = require("axios");

const { LoanInfo } = require("../../../models/ops/loan/loan");
const { PartnerMaster } = require("../../../models/ops/partner/partner");
const LnTFinance = require("../../../partnerApiCalls/LnTFinance");

// ------------------------------------------------------------------------------------------------------------------------------------------

//External Methods
// -------------------------------------------------------------------------------------------------------------------------
const Logger = require("../../../middleware/logger");
const logger = new Logger();

module.exports.sendApplicationToLnt = async ({ loanInfo, applicantUser, user, body }) => {
    try {
        logger.logs("Lead pushing to Lnt")
        let Loan = await LoanInfo.findById(loanInfo?._id);
        let lendingPartner = await PartnerMaster.findOne({ partnerName: body.partnerName });
        let companyName = "Other";
        let companyCategory = "CAT E";
        let mobileNumber = applicantUser?.userDetails?.mobileNo.filter(({ isPrimary, mobileNumber }) => isPrimary)[0].mobileNumber;
        let emailId = applicantUser?.userDetails?.emailId[0] || "";
        let params = {
            "uid": applicantUser?._id,
            "productCategory": "CL",
            "sourcingChannel": "CLEAG",
            "applicant_first_name": applicantUser?.userDetails?.firstName,
            "applicant_last_name": applicantUser?.userDetails?.lastName,
            "mobileNo": mobileNumber,
            "panNo": applicantUser?.panNo,
            "email": emailId,
            "pincode": Array.isArray(applicantUser?.addressDetails) ? applicantUser?.addressDetails[0]?.pincode : applicantUser?.addressDetails?.pincode,
            "employment_type": body.occupationType || "Self Employed",
            "dob": moment(applicantUser?.userDetails?.dob).format('DD-MM-YYYY'),
            "company_name": companyName,
            "education_qualification": applicantUser?.questionnairreDetails.degree == "Graduation" ? "Graduate" : "Non-graduate",
            "residence_ownership": applicantUser?.questionnairreDetails?.homeOwnershipType == "Rent" ? "Rented" : "Owned",
            "company_category": companyCategory
        }
        let missingKey = []
        for (let i in params) {
            if (params[i] === undefined || params[i] === '') {
                missingKey.push(i);
            }
        }
        logger.log("parameter passed for Lnt", params)
        let responseFromPartner = [];
        let message = ""
        let leadId = "";
        let leadCreation = await new LnTFinance().leadGeneration(params);
        message = leadCreation?.data?.errorDesc
        if (missingKey.length > 0) {
            message = leadCreation?.data?.errorDesc + ` Missing key is ${missingKey}`
        }
        logger.logs("response of leadcreation", leadCreation?.data)
        responseFromPartner.push({ leadCreationRes: leadCreation?.data });
        
        if(leadCreation?.data?.statusCode === 102) leadId = leadCreation?.data?.leadId;
        
        if (leadCreation?.data?.status === "SUCCESS") {
            leadId = leadCreation?.data?.leadId;
            let customerEligibleParams = {
                "uid": params?.uid,
                "productCategory": "CL",
                "sourcingChannel": "CLEAG"
            }
            let checkEligibility = await new LnTFinance().customerEligibility(customerEligibleParams);
            message = checkEligibility?.data?.errorDesc
            responseFromPartner.push({ eligibilityRes: checkEligibility?.data })
            logger.logs("response of customer eligibility", checkEligibility);
            if (checkEligibility?.data?.status === 'SUCCESS') {
                Loan.status = "Lender Approval"
                Loan.amount = checkEligibility?.data?.breMaxEligibilityAmount
                Loan.lenderDetails[0] = {
                    errorMsg: message,
                    errorDate: new Date(),
                    lenderLeadId: leadId,
                    lenderLoanId: checkEligibility?.data?.applicationId,
                    amount: Number(checkEligibility?.data?.breMaxEligibilityAmount)
                };
            }
        }
        loanInfo.tenure = body.tenure;
        loanInfo.adhocDays = body.adhocDays;
        loanInfo.interestRate = body.interestRate;
        loanInfo.interestType = body.interestType;
        loanInfo.repaymentFrequency = body.repaymentFrequency;
        loanInfo.adminModifiedOn = Date.now();
        Loan.lenderDetails[0] = {
            lenderPartnerId: mongoose.Types.ObjectId(lendingPartner._id),
            lenderLeadId: leadId,
            errorMsg: message,
            errorDate: new Date(),
        };
        await Loan.save();
        return true
    } catch (error) {
        console.log(error)
    }
}