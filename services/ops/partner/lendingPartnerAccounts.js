const { PartnerMaster, LenderAccountSummary, JobStatus, TrueScoreBeta } = require("../../../models/ops/partner/partner");
const { LoanInfo } = require("../../../models/ops/loan/loan");
const { PreApprovals } = require("../../../models/ops/partner/partner");
const { getAccoutBalance } = require('../disbursal/payment-gateway-service/payment-gateway-service');
const { saveErrorLog } = require("../../../services/ops/common/masters");
const mongoose = require("mongoose");
const Logger = require("../../../middleware/logger");
const logger = new Logger();
const apiPath = __dirname;

function buildLoanSummaryRecord(balanceRequest, balanceResponse) {
    let lenderAccountSummary = {};
    let lenderInfo = balanceRequest.requestInfo;

    lenderAccountSummary.lenderId = lenderInfo.lenderId;
    lenderAccountSummary.lenderName = lenderInfo.lenderName;
    lenderAccountSummary.lenderCode = lenderInfo.lenderCode;
    lenderAccountSummary.accountName = lenderInfo.name;
    lenderAccountSummary.accounType = lenderInfo.type;
    lenderAccountSummary.accountNumber = lenderInfo.no;
    lenderAccountSummary.ifsc = lenderInfo.ifsc;
    lenderAccountSummary.lenderClientCode = lenderInfo.lenderClientCode;
    lenderAccountSummary.availableBalance = balanceResponse.availableBalance;
    lenderAccountSummary.currentBalance = balanceResponse.currentBalance;

    return lenderAccountSummary
};

async function processResult(results) {

    try {

        let bulkWriteQuery = [];
        let failedList = [];

        for (let index = 0; index < results.length; index++) {

            let accountBalanceResponse = results[index].value;

            let [balanceError, balanceResponse, balanceRequest] = accountBalanceResponse;

            if (balanceError) {

                failedList.push(accountBalanceResponse);

            } else {
                let lenderInfo = balanceRequest.requestInfo;

                let accountSummaryRecord = buildLoanSummaryRecord(balanceRequest, balanceResponse);

                allocatedAmount = 0

                let lenderLoanAssgined = await LoanInfo.find({ status: "Lender Assigned", "lenderDetails.lenderPartnerId": mongoose.Types.ObjectId(lenderInfo.lenderId) }).lean();

                if (lenderLoanAssgined && lenderLoanAssgined.length) {
                    allocatedAmount = lenderLoanAssgined.reduce(
                        (previousValue, currentValue) =>
                            previousValue + currentValue.amount, 0
                    )
                }

                let currentBalance = Math.round(accountSummaryRecord.availableBalance - allocatedAmount);

                accountSummaryRecord.currentBalance = isNaN(currentBalance) ? 0 : currentBalance;

                let query = {
                    updateOne: {
                        filter: { lenderId: lenderInfo.lenderId, accountNumber: lenderInfo.accountNumber },
                        update: { ...accountSummaryRecord, updatedOn: new Date() },
                        upsert: true
                    }
                }

                bulkWriteQuery.push(query);
            }
        }

        let lenderAccountSummaryBulkUpdate = null;

        if (bulkWriteQuery.length) {
            lenderAccountSummaryBulkUpdate = await LenderAccountSummary.bulkWrite(bulkWriteQuery);
        }

        let payload = {
            status: "Completed",
            error: failedList && failedList.length ? failedList : null,
            updatedOn: new Date(),
        }

        let updateStatus = await updateJobStatus(payload);
    } catch (ex) {
        saveErrorLog("processResult", apiPath, "", ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }
};

function buildAccountSummaryRequest(lender) {

    let requestPayload = {};

    requestPayload = {
        lenderId: lender._id,
        lenderName: lender.partnerName,
        lenderCode: lender.partnerCode,
        accountNumber: lender.activeAccount.no,
        ...lender.activeAccount,
        lenderClientCode: lender.activeAccount.lenderClientCode //? lender.activeAccount.lenderClientCode : "ESSKAY" //For testing needs to be removed
    }

    return requestPayload
};

async function bulkLenderAccountsSummaryUpdate() {

    try {

        let lenderList = await PartnerMaster.find({ partnerType: "Lending Partner" }).lean();

        if (lenderList && lenderList.length) {
            lenderList.forEach((lender, index) => {
                if (lender.bankAccounts && lender.bankAccounts.length) {
                    let activeAccounts = lender.bankAccounts.filter((account) => account.type == "Disbursal Escrow" && account.isActive == true);
                    if (activeAccounts.length) {
                        lender.activeAccount = activeAccounts[0];
                    }
                }
            });

            let activeLenderList = lenderList.filter((lenderList => lenderList.activeAccount));
            let queryPromises = [];

            for (let index = 0; index < activeLenderList.length; index++) {
                let lenderDetails = activeLenderList[index];
                let payload = buildAccountSummaryRequest(lenderDetails);
                let queryPromise = getAccoutBalance(payload);
                queryPromises.push(queryPromise);
            }

            let results = await Promise.allSettled(queryPromises);

            processResult(results);
        }
    } catch (ex) {
        saveErrorLog("bulkLenderAccountsSummaryUpdate", apiPath, "", ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }
};

async function bulkAccountSummaryUpdateWrapper(req) {
    let output = {
        success: false,
        message: "Some Error Occurred",
        status: 501,
    };
    try {
        let bulkSummaryJob = await JobStatus.findOne({ name: "Bulk Account Summary" }).lean();

        if (bulkSummaryJob && bulkSummaryJob !== "InProgress") {
            let payload = {
                status: "InProgress",
                updatedOn: new Date()
            }
            let updateStatus = await updateJobStatus(payload);

            if (updateStatus) {

                bulkLenderAccountsSummaryUpdate();

                output = {
                    success: true,
                    message: "Account Summary bulk update job started",
                    status: 200,
                };

            }
        } else {
            output = {
                success: false,
                message: "Account Summary bulk update job is in progress",
                status: 400,
            };
        }

    } catch (ex) {
        saveErrorLog("bulkAccountSummaryUpdateWrapper", apiPath, "", ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }
    return output
};

async function updateJobStatus(payload) {
    let isUpdated = false;
    try {

        let updatedStatus = await JobStatus.update(
            { name: "Bulk Account Summary" },
            {
                $set: payload
            },
            { upsert: true }
        );

        if (updatedStatus) {
            isUpdated = true;
        }

    } catch (ex) {
        saveErrorLog("updateJobStatus", apiPath, payload, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }
    return isUpdated
};

async function getLenderAccountSummaryJobStatus(req) {
    let output = {
        success: false,
        message: "Some Error Occurred",
        status: 501,
    };
    try {
        let jobStatus = await JobStatus.findOne({ name: "Bulk Account Summary" }).lean();

        if (jobStatus) {

            let data = {
                status: jobStatus.status,
                lastUpdated: jobStatus.updatedOn
            }

            output = {
                success: true,
                message: "Account Summary bulk update job status",
                status: 200,
                data: data
            };
        }

    } catch (ex) {
        saveErrorLog("getLenderAccountSummaryJobStatus", apiPath, "", ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }

    return output

};

async function refreshLenderAccountSummary(model) {

    let output = {
        success: false,
        message: "Some Error Occurred",
        status: 501,
    };

    try {

        if (model) {

            let lenderAccount = await LenderAccountSummary.findOne({ lenderId: mongoose.Types.ObjectId(model.lenderId), accountNumber: model.accountNumber }).lean();

            if (lenderAccount) {
                let [balanceError, balanceResponse, balanceRequest] = await getAccoutBalance(lenderAccount);
                let payload = {};

                if (!balanceError) {

                    let allocatedAmount = 0;

                    let lenderLoanAssgined = await LoanInfo.find({ status: "Lender Assigned", "lenderDetails.lenderPartnerId": mongoose.Types.ObjectId(model.lenderId) }).lean();

                    if (lenderLoanAssgined && lenderLoanAssgined.length) {
                        allocatedAmount = lenderLoanAssgined.reduce(
                            (previousValue, currentValue) =>
                                previousValue + currentValue.amount, 0
                        )
                    }

                    payload.availableBalance = balanceResponse.availableBalance;
                    payload.currentBalance = Math.round(balanceResponse.availableBalance - allocatedAmount);
                    payload.updatedOn = new Date();

                    let updatedStatus = await LenderAccountSummary.update(
                        { lenderId: mongoose.Types.ObjectId(model.lenderId), accountNumber: model.accountNumber },
                        {
                            $set: payload
                        }
                    );

                    output = {
                        success: true,
                        message: "Account summary updated",
                        status: 200
                    };
                }
            }
        } else {
            output = {
                success: false,
                message: "Lender Id Missing",
                status: 400
            };
        }

    } catch (ex) {
        logger.logs(ex);
        saveErrorLog("refreshLenderAccountSummary", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }

    return output;
};

async function getLenderAccountSummaryList() {
    try {
        const report = await LenderAccountSummary.find()
            .select({
                "lenderId": 1,
                "accounType": 1,
                "accountName": 1,
                "accountNumber": 1,
                "availableBalance": 1,
                "currentBalance": 1,
                "lenderClientCode": 1,
                "lenderCode": 1,
                "lenderName": 1,
                "updatedOn": 1
            })
            .sort({ lenderName: 1 }).lean();
        return {
            data: report,
            success: true,
            message: "Data fetched successfully",
            status: 200,
        };
    } catch (ex) {
        saveErrorLog("getLenderAccountSummaryList", apiPath, "", ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        return {
            success: false,
            message: ex.message,
            status: 501,
        };
    }
};

async function getPreApprovalAmount(data) {
    try { 
        const preApproval = await PreApprovals.findOne({
            partnerId: mongoose.Types.ObjectId(data?.partnerId),
            merchantCode: data?.merchantCode
        }).sort({ insertedOn: -1 });
        if(preApproval?.approvedAmount){
            return {
                data: preApproval,
                success: true,
                message: "PreApproved amount fetched successfully.",
                status: 200,
            };
        }
        else{
            const trueScoreBeta=await TrueScoreBeta.findOne({
                partnerId: mongoose.Types.ObjectId(data?.partnerId),
                merchantCode: data?.merchantCode
            });  
            if(trueScoreBeta?.approvedAmount){
                return {
                    data: trueScoreBeta,
                    success: true,
                    message: "PreApproved amount fetched successfully.",
                    status: 200,
                };
            }
            return {
                data: null,
                success: false,
                message: "PreApproved Amount not found.",
                status: 200,
            };
        }
    } catch (ex) {
        saveErrorLog("getPreApprovalAmount", apiPath, "", ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        return {
            success: false,
            message: ex.message,
            status: 501,
        };
    }
};

module.exports = {
    bulkAccountSummaryUpdateWrapper,
    refreshLenderAccountSummary,
    getLenderAccountSummaryJobStatus,
    getLenderAccountSummaryList,
    getPreApprovalAmount
};