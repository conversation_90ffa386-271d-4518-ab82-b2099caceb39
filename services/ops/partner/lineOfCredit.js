//Libraries
// ------------------------------------------------------------------------------------------------------------------------------------------
const mongoose = require("mongoose");
const IsNullEmpty = require("isnullemptyorwhitespace");
const debug = require("debug")("app:lineOfCredit");
const _ = require("lodash");
const apiPath = __dirname;
// ------------------------------------------------------------------------------------------------------------------------------------------

//Models
// ------------------------------------------------------------------------------------------------------------------------------------------
const { PreApprovals, PartnerConfig, ProductPartnerConfig, PartnerMaster, ProductMaster } = require("../../../models/ops/partner/partner");
const { LoanInfo, InvoiceRequests } = require("../../../models/ops/loan/loan");
const { DigioMandateGeneration } = require("../../../models/ops/digio/digioApi");
// ------------------------------------------------------------------------------------------------------------------------------------------

//External Methods
// ------------------------------------------------------------------------------------------------------------------------------------------
const { saveErrorLog } = require("../common/masters");
const { UserPartnerInfo } = require("../../../models/ops/registration/users");
const { approvedByAdmin, loanIsFunding, checkCreditLimit } = require("../loan/workflow");
const { getLoanId } = require("../loan/loan");
const Logger = require("../../../middleware/logger");
const logger = new Logger();
// ------------------------------------------------------------------------------------------------------------------------------------------


//Internal Line Of Credit Methods
// ------------------------------------------------------------------------------------------------------------------------------------------
//For Line Of Credit Child Loans
async function saveChildLoan(model) {
    let output = {
        loanId: null,
        success: false,
        message: "",
        status: 200
    };
    try {
        logger.logs("model", model);
        let preApproval = null;
        const today = new Date();
        let counts = 0;
        if (model.isExcelUpload) {
            counts = await InvoiceRequests.find({ partnerId: mongoose.Types.ObjectId(model.partnerId), invoiceId: { $regex: model.invoiceId, $options: "i" }, isSaved: true }).countDocuments();
        } else {
            counts = await InvoiceRequests.find({ partnerId: mongoose.Types.ObjectId(model.partnerId), invoiceId: { $regex: model.invoiceId, $options: "i" }, isActive: false }).countDocuments();
        }

        if (counts > 0) {
            output.message = "Duplicate InvoiceId";
            return output;
        }

        if (IsNullEmpty(model.offerId)) {
            preApproval = await PreApprovals.findOne({ partnerId: mongoose.Types.ObjectId(model.partnerId), merchantCode: model.merchantCode }).sort({ insertedOn: -1 });
        } else {
            preApproval = await PreApprovals.findOne({ partnerId: mongoose.Types.ObjectId(model.partnerId), merchantCode: model.merchantCode, productId: model.offerId });
            if (IsNullEmpty(preApproval)) {
                preApproval = await PreApprovals.findOne({ partnerId: mongoose.Types.ObjectId(model.partnerId), merchantCode: model.merchantCode, trxnId: model.offerId });
            }
        }

        if (!IsNullEmpty(preApproval)) {
            if (Date.diff(preApproval.expiredOn, today).days() < 0) {
                output.message = "Offer Expired";
                return output;
            } else {
                let parentLoan = null;
                let loanInfo = null;

                if (!IsNullEmpty(preApproval.productId)) {
                    parentLoan = await LoanInfo.findOne({ partnerId: mongoose.Types.ObjectId(model.partnerId), merchantCode: model.merchantCode, productId: preApproval.productId, isParentLoan: true });
                } else if (!IsNullEmpty(preApproval.trxnId)) {
                    parentLoan = await LoanInfo.findOne({ partnerId: mongoose.Types.ObjectId(model.partnerId), merchantCode: model.merchantCode, trxnId: preApproval.trxnId, isParentLoan: true });
                }
                //logger.logs("parentLoan", parentLoan);
                if (!IsNullEmpty(parentLoan)) {
                    if (parentLoan.status != "Onboarding Completed") {
                        output.message = "Credit Limit Not Approved";
                        parentLoan = null;
                        return output;
                    } else {
                        const digioMandategeneration = await DigioMandateGeneration.findOne({ isActive: true, loanIds: parentLoan._id, status: "register_success" }).sort({ createdOn: -1 });

                        if (!digioMandategeneration) {
                            output.message = "Mandate Not Found";
                            parentLoan = null;
                            return output;
                        } else if (digioMandategeneration.status && (digioMandategeneration.status != "register_success" && digioMandategeneration.status != "success")) {
                            output.message = "Mandate is not registered";
                            parentLoan = null;
                            return output;
                        }

                        loanInfo = await LoanInfo.find({
                            partnerId: mongoose.Types.ObjectId(model.partnerId), merchantCode: model.merchantCode, productId: parentLoan.productId, isParentLoan: false, status: { $in: ["Loan In Progress", "Lender Assigned", "Initiated Fund Transfer", "Ready For Disbursal"] }
                        }).select({ loanNo: 1, amount: 1, partnerId: 1, productId: 1, trxnId: 1, isParentLoan: 1, status: 1 });

                        const checkLimit = await checkCreditLimit(model.partnerId, model.merchantCode, parentLoan.productId, model.invoiceAmount);

                        if (checkLimit.success) {
                            let invoiceRequest = await InvoiceRequests.findOne({ partnerId: mongoose.Types.ObjectId(model.partnerId), invoiceId: model.invoiceId });

                            if (invoiceRequest) {
                                const loanDetails = await getLoanId();

                                let campaignTime = null;
                                let days = 28;
                                const partneConfig = await PartnerConfig.findById(model.partnerId);
                                if (partneConfig && partneConfig.LC_InvoiceTenure && partneConfig.LC_InvoiceFrequency) {
                                    if (partneConfig.LC_InvoiceFrequency == "Days")
                                        days = partneConfig.LC_InvoiceFrequency;
                                    else if (partneConfig.LC_ParentFrequency == "Months")
                                        days = partneConfig.LC_InvoiceFrequency * 30;
                                    else if (partneConfig.LC_ParentFrequency == "Years")
                                        days = partneConfig.LC_InvoiceFrequency * 360;

                                    campaignTime = days.toString() + " Days";
                                } else {
                                    campaignTime = "28 Days";
                                }

                                const loanInfo = new LoanInfo({
                                    userId: mongoose.Types.ObjectId(parentLoan.userId),
                                    loanNo: loanDetails.data,
                                    partner: parentLoan.partner,
                                    partnerId: mongoose.Types.ObjectId(parentLoan.partnerId),
                                    merchantCode: parentLoan.merchantCode,
                                    merchantType: "Retailer",
                                    trxnId: parentLoan.trxnId,
                                    productId: parentLoan.productId,
                                    productType: "LC_28",
                                    amount: model.invoiceAmount,
                                    trancheNo: null,
                                    purpose: "Business",
                                    description: "Working Capital Loan",
                                    maturity: 1,
                                    interestRate: parentLoan.interestRate,
                                    currency: "Indian Rupee",
                                    campaignTime: campaignTime,
                                    loanType: "EMI",
                                    repaymentFrequency: "Adhoc",
                                    additionalDescription: null,
                                    isMaturityMonth: false,
                                    emiAmount: model.invoiceAmount,
                                    interestType: parentLoan.interestType,
                                    delinquencyString: null,
                                    lenderDetails: [],
                                    flowDetails: [],
                                    loanContractDetails: null,
                                    disbursalDetails: null,
                                    isDraft: false,
                                    status: "Under Review",
                                    postDisbursalStatus: null,
                                    adminRemarks: null,
                                    clientRemarks: null,
                                    rejectDescription: null,
                                    isActive: true,
                                    insertedOn: Date.now(),
                                    updatedOn: Date.now(),
                                    signatureDetails: null,
                                    lastStepCompleted: null,
                                    lastStepCompletedAt: null,
                                    loanStepDetails: [],
                                    adminModifiedOn: null,
                                    ipAddress: null,
                                    disbursalBankDetails: null,
                                    primaryCollectionType: "",
                                    secondaryCollectionType: "",
                                    byPassEnach: false,
                                    isParentLoan: false,
                                    invoiceRequestId: mongoose.Types.ObjectId(invoiceRequest._id),
                                    adhocDays: days,
                                    tenure: 28
                                });

                                loanInfo.loanStepDetails.push({
                                    Step: "Contact Information",
                                    isCompleted: true,
                                    insertedOn: Date.now(),
                                    completedOn: Date.now(),
                                });

                                loanInfo.loanStepDetails.push({
                                    Step: "OTP Screen",
                                    isCompleted: false,
                                    insertedOn: Date.now(),
                                    completedOn: null,
                                });

                                loanInfo.loanStepDetails.push({
                                    Step: "OTP Matched",
                                    isCompleted: false,
                                    insertedOn: Date.now(),
                                    completedOn: null
                                });

                                loanInfo.loanStepDetails.push({
                                    Step: "Loan",
                                    isCompleted: false,
                                    insertedOn: Date.now(),
                                    completedOn: null,
                                });

                                loanInfo.loanStepDetails.push({
                                    Step: "KYC",
                                    isCompleted: false,
                                    insertedOn: Date.now(),
                                    completedOn: null,
                                });

                                loanInfo.loanStepDetails.push({
                                    Step: "Shop",
                                    isCompleted: false,
                                    insertedOn: Date.now(),
                                    completedOn: null,
                                });

                                loanInfo.loanStepDetails.push({
                                    Step: "Questionnairre",
                                    isCompleted: false,
                                    insertedOn: Date.now(),
                                    completedOn: null,
                                });

                                loanInfo.loanStepDetails.push({
                                    Step: "Review",
                                    isCompleted: false,
                                    insertedOn: Date.now(),
                                    completedOn: null,
                                });

                                loanInfo.loanStepDetails.push({
                                    Step: "Bank",
                                    isCompleted: false,
                                    insertedOn: Date.now(),
                                    completedOn: null,
                                });

                                loanInfo.loanStepDetails.push({
                                    Step: "eNach",
                                    isCompleted: false,
                                    insertedOn: Date.now(),
                                    completedOn: null,
                                });

                                loanInfo.loanStepDetails.push({
                                    Step: "Loan Contract",
                                    isCompleted: false,
                                    insertedOn: Date.now(),
                                    completedOn: null,
                                });

                                await loanInfo.save();


                                invoiceRequest.isActive = false;
                                invoiceRequest.isSaved = true;
                                invoiceRequest.message = "Inserted Successfully";
                                invoiceRequest.updatedOn = Date.now();
                                await invoiceRequest.save();


                                output = {
                                    loanId: loanInfo._id,
                                    success: true,
                                    message: "Inserted Successfully",
                                    status: 200
                                }
                            } else {
                                output.message = "Invoice Request Not Found";
                                return output;
                            }
                        } else {
                            output.message = checkLimit.message;
                            loanInfo = null;
                        }
                    }
                } else {
                    output.message = "Credit Limit Not Approved";
                    parentLoan = null;
                    loanInfo = null;
                    return output;
                }

            }
        } else {
            output.message = "No Offer Exists";
            return output;
        }
    } catch (ex) {
        logger.logs("error", ex);
        saveErrorLog("saveChildLoans", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};
module.exports.saveChildLoan = saveChildLoan;

module.exports.createOrder = async function (model, partnerId) {
    //model={userId,orderId,transactionAmount,dot,items,isOnlineCheckOut}
    let output = {
        referenceNo: null,
        success: false,
        message: "Some Error Occurred",
        status: 200
    };
    try {
        const today = new Date();
        const userPartnerInfo = await UserPartnerInfo.findOne({ partnerId: mongoose.Types.ObjectId(partnerId), merchantCode: model.userId });

        const preApproval = await PreApprovals.findOne({ partnerId: mongoose.Types.ObjectId(partnerId), merchantCode: model.userId, status: "Sent", expiredOn: { $gt: today } }).sort({ insertedOn: -1 });

        if (!IsNullEmpty(preApproval)) {
            let invoiceRequest = new InvoiceRequests({
                partnerId: mongoose.Types.ObjectId(partnerId),
                merchantCode: model.userId,
                invoiceId: model.orderId,
                invoiceAmount: model.transactionAmount,
                source: "createOrder API",
                dotTransaction: new Date(model.dot),
                offerId: preApproval.productId,
                deliveryAgent: null,
                deliveryAgentMobile: null,
                items: model.items,
                message: null,
                isActive: true,
                insertedBy: userPartnerInfo.userId,
                insertedOn: Date.now(),
                updatedOn: Date.now()
            });
            await invoiceRequest.save();

            const body = {
                userId: userPartnerInfo.userId,
                partnerId: partnerId,
                merchantCode: model.userId,
                invoiceId: model.orderId,
                invoiceAmount: model.transactionAmount,
                isExcelUpload: false,
                offerId: preApproval.productId,
                deliveryAgent: null,
                deliveryAgentMobile: null
            };

            const response = await saveChildLoan(body);
            logger.logs("response", response);

            //invoiceRequest = await InvoiceRequests.findById(invoiceRequest._id);
            invoiceRequest.message = response.message;
            invoiceRequest.isActive = false;
            invoiceRequest.isSaved = response.success;
            invoiceRequest.updatedOn = Date.now();
            await invoiceRequest.save();

            if (response.success) {
                const loanId = response.loanId;
                if (!IsNullEmpty(loanId)) {
                    output = {
                        referenceNo: loanId,
                        success: true,
                        message: "Inserted Successfully",
                        status: 200
                    };
                    const childLoan = await LoanInfo.findById(loanId);
                    const parentLoan = await LoanInfo.findOne({ productId: childLoan.productId, isParentLoan: true });
                    if (!IsNullEmpty(parentLoan)) {
                        const responseApprovedByAdmin = await approvedByAdmin(childLoan._id, childLoan.interestType, childLoan.tenure, childLoan.maturity, childLoan.interestRate, childLoan.repaymentFrequency, childLoan.adhocDays, null, userPartnerInfo.userId);

                        logger.logs("responseApprovedByAdmin", responseApprovedByAdmin);
                        if (responseApprovedByAdmin.success && parentLoan.lenderDetails.length > 0 && parentLoan.lenderDetails[0].lenderPartnerId) {
                            const sendNotification = model.isOnlineCheckOut ? false : true;
                            const responseLoanIsFunding = await loanIsFunding(childLoan._id, userPartnerInfo.userId, sendNotification, "User");
                            logger.logs("responseLoanIsFunding", responseLoanIsFunding);
                        }
                    } else {
                        logger.logs("Parent Loan not Found");
                    }
                } else {
                    logger.logs("Loan No not Found");
                    return output;
                }
            } else {
                output.message = response.message;
            }
        } else {
            output.message = "No Active Offer Found. Please Contact Fundfina Support For New Offer.";
        }
    } catch (ex) {
        logger.logs("error", ex);
        saveErrorLog("createOrder", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    logger.logs("output", output);
    return output;
};

// ------------------------------------------------------------------------------------------------------------------------------------------