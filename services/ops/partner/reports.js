const mongoose = require("mongoose");
const IsNullEmpty = require("isnullemptyorwhitespace");
const config = require("config");
const csvConverter = require('json-2-csv');
const json2xls = require("json2xls");
const XLSX = require("xlsx");
const fs = require("fs");
const debug = require("debug")("app:PartnerReports");
const DateDiff = require("date-diff");
const { keyBy, groupBy, uniq } = require("lodash");
const aws = require("../../../models/ops/aws/aws-s3");

const { UserInfo, UserPartnerInfo } = require("../../../models/ops/registration/users");
const { LoanInfo } = require("../../../models/ops/loan/loan");
const { PartnerMaster, PreApprovals } = require("../../../models/ops/partner/partner");
const { <PERSON>rrorL<PERSON>, CityStateMaster } = require("../../../models/ops/common/master");
const { LoanDisbursmentTransactions } = require("../../../models/ops/loan/disbursal");

const { getName, getDMYFormattedDate, getFromDate, getToDate, round10, getYMDFormattedDate, FormatCells, leadingZeroFormatExcel } = require("../common/common");
const { getLoanCharges } = require("../../../services/ops/loan/loan");
const installmentService = require("../loan/installments");
const { saveErrorLog } = require("../common/masters");
const { dateAdd } = require("dateadd");
const Logger = require("../../../middleware/logger");
const logger = new Logger();

const apiPath = __dirname;

module.exports.exportPendingEmis = async function (partnerId) {
    let output = {
        success: false,
        message: "Some Error Occurred",
        status: 501,
    };
    try {
        const loanInfo = await LoanInfo.find({ partnerId: mongoose.Types.ObjectId(partnerId), status: "Loan In Progress" }).select({ _id: 1, loanNo: 1, amount: 1, merchantCode: 1, productId: 1, userId: 1 });

        if (loanInfo) {
            let userIds = [];
            let loanNos = [];

            for (let i = 0; i < loanInfo.length; i++) {
                userIds.push(loanInfo[i].userId);
                loanNos.push(loanInfo[i].loanNo);
            }

            userIds = [...new Set([...userIds])];

            const userInfo = await UserInfo.find({ _id: { $in: userIds } }).select({ userDetails: 1 });

            const today = new Date();

            let userHash = {};

            for (let i = 0; i < userInfo.length; i++) {
                userHash[userInfo[i]._id.toString()] = userInfo[i];
            }

            const allPendingEmis = await installmentService.getPeriodicInstallments(loanNos, ["Pending", "Partial"]);

            if (allPendingEmis.success) {
                let records = [];
                for (const loan of loanInfo) {
                    const user = userHash[loan.userId.toString()];

                    const installmentDetails = allPendingEmis.data.filter((x) => x.loanNo == loan.loanNo);

                    if (installmentDetails && installmentDetails.length > 0) {
                        for (const installment of installmentDetails) {

                            const record = {
                                loanNo: loan.loanNo,
                                name: getName(user.userDetails.firstName, user.userDetails.middleName, user.userDetails.lastName),
                                merchantCode: loan.merchantCode,
                                productId: loan.productId,
                                emiNumber: installment.installmentNumber,
                                interestAmount: installment.installmentInterest,
                                principalRecovered: installment.installmentPrincipal,
                                penalty: installment.feeAmount,
                                moratoriumInterest: installment.moratoriumInterest,
                                rePaymentAmount: installment.installmentAmount,
                                totalEmi: round10(installment.totalAmount, -2),
                                rePaymentStatus: installment.status,
                                rePaymentDate: getDMYFormattedDate(installment.dueDate, "-"),
                                loanAmount: loan.amount,
                                totalOutstanding: round10(installment.outstandingAmount, -2)
                            }
                            records.push(record);
                        }
                    }
                }
                if (records.length > 0) {
                    let today = new Date();
                    const currentMonth = today.getMonth() + 1;
                    const fileName = "PendingEmisReport_" + today.getDate() + "_" + currentMonth + "_" + today.getFullYear().toString().substring(2, 4) + ".xlsx";
                    let xls = json2xls(records);
                    const filePath = config.get("exportToExcelPath.generalReports") + fileName;
                    console.log(filePath);
                    fs.writeFileSync(filePath, xls, "binary");
                }
                output = {
                    success: true,
                    message: "Fetched Successfully",
                    status: 200,
                };
            } else output.message = allPendingEmis.message;
        } else output.message = "No record found";
    } catch (ex) {
        console.log(ex);
        saveErrorLog("exportPendingEmis", apiPath, null, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};

module.exports.exportCibilReportForLendingPartner = async function (model) {
    logger.logs("model.partnerId", model.partnerId);
    let output = {
        fileName: "",
        fileURL: "",
        success: false,
        message: "Some Error Occurred",
        status: 200,
    };
    try {
        const partnerMaster = await PartnerMaster.findById(model.partnerId);

        if (partnerMaster) {
            let fromDate = model.fromDate ? getFromDate(model.fromDate) : new Date(Date.now());
            let toDate = model.toDate ? getToDate(model.toDate) : new Date(Date.now());

            fromDate = new Date(fromDate.getFullYear(), fromDate.getMonth(), fromDate.getDate(), 23, 59, 59);
            toDate = new Date(toDate.getFullYear(), toDate.getMonth(), toDate.getDate(), 23, 59, 59);
            const diff = new DateDiff(toDate, fromDate).days();

            // if (diff > 31) {
            //     output.message = "Date range should not be more than 31 days";
            //     return output;
            // }
            let records = [];
            const postDisbursalStatus = config.get("keys.postDisbursalStatus");

            const limit = 100;

            const totalLoans = await LoanInfo.find({ "lenderDetails.lenderPartnerId": mongoose.Types.ObjectId(model.partnerId), "disbursalDetails.disbursedOn": { $gte: fromDate, $lte: toDate }, status: { $in: postDisbursalStatus } }).countDocuments();

            //logger.logs("totalLoans", totalLoans);

            let totalPages = parseInt(totalLoans / limit);
            let skip = 0;

            for (let page = 0; page < (totalPages + 1); page++) {
                skip = page * limit;

                const loans = await LoanInfo.find({ "lenderDetails.lenderPartnerId": mongoose.Types.ObjectId(model.partnerId), "disbursalDetails.disbursedOn": { $gte: fromDate, $lte: toDate }, status: { $in: postDisbursalStatus } }).select({ _id: 1, loanNo: 1, disbursalDetails: 1, amount: 1, interestRate: 1, tenure: 1, repaymentFrequency: 1, userId: 1 }).sort({ loanNo: 1 }).skip(skip).limit(limit);

                if (loans && loans.length > 0) {
                    const loanNos = [...new Set(loans.map(item => item.loanNo))];
                    const userIds = [...new Set(loans.map(item => item.userId))];

                    const users = await UserInfo.find({ _id: { $in: userIds } }).select({ userDetails: 1, questionnairreDetails: 1, panNo: 1, addressDetails: 1, poaDetails: 1 });

                    let userHash = {};
                    for (let i = 0; i < users.length; i++) {
                        userHash[users[i]._id.toString()] = users[i];
                    }

                    const loanInstallments = await installmentService.getMultipleLoansInstallments(null, loanNos);

                    if (loanInstallments.success) {
                        const loanFees = await installmentService.getMultipleLoanFees(null, loanNos);
                        if (loanFees.success) {
                            const today = new Date();
                            const emiDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1, 5, 30, 0, 0);

                            for (const loan of loans) {
                                const userInfo = userHash[loan.userId];

                                const record = {
                                    Consumer_Name: getName(userInfo.userDetails.firstName, userInfo.userDetails.middleName, userInfo.userDetails.lastName),
                                    CUSTOMER_DOB: getDMYFormattedDate(userInfo.userDetails.dob, ""),
                                    GENDER: userInfo.questionnairreDetails.gender,
                                    PAN_NO: userInfo.panNo,
                                    PASSPORT_NO: null,
                                    VOTER_ID_NO: null,
                                    DL_NO: null,
                                    AADHAR: null,
                                    TELEPHONE_NO: userInfo.userDetails.mobileNo[0] ? userInfo.userDetails.mobileNo[0].mobileNumber : "",
                                    E_MAIL_ID: userInfo.userDetails.emailId[0],
                                    ADDRESS_LINE_1: userInfo.addressDetails[0].add1,
                                    ADDRESS_LINE_2: userInfo.addressDetails[0].add2,
                                    ADDRESS_LINE_3: userInfo.addressDetails[0].add3,
                                    ADDRESS_LINE_4: null,
                                    ADDRESS_LINE_5: null,
                                    STATE: userInfo.addressDetails[0].state,
                                    PINCODE: userInfo.addressDetails[0].pincode,
                                    LOAN_NO: loan.loanNo,
                                    Ownership_Indicator: "1",
                                    LOAN_APPROVAL_DATE: getDMYFormattedDate(loan.disbursalDetails.disbursedOn, ""),
                                    TERMINATION_DATE: null,
                                    CUT_OFF_DATE: null,
                                    LOAN_AMOUNT: loan.amount,
                                    Current_Balance: null,
                                    Amount_Overdue: null,
                                    Past_Due: null,
                                    RATE_OF_INTEREST: loan.interestRate,
                                    REPAYMENT_TENURE: loan.tenure,
                                    EMI_AMOUNT: null,
                                    PAYMENT_FREQUENCY: loan.repaymentFrequency,
                                    OCCUPATION_CODE: null
                                };

                                const installments = loanInstallments.data.filter(x => x.loanId.toString() == loan._id.toString());

                                const fees = loanFees.data.filter(x => x.loanId.toString() == loan._id.toString());

                                if (installments && installments != null && installments.length > 0) {
                                    record.TERMINATION_DATE = getDMYFormattedDate(installments.filter(x => x.installmentNumber == installments.length)[0].actualDueDate, "");

                                    const totalEmiAmountOutstanding = installments.filter(x => x.status == "Pending")
                                        .reduce(function (prev, cur) {
                                            return prev + cur.installmentAmount;
                                        }, 0);

                                    // const totalEmiAmountOutstanding1 = installments.filter((x) => (x.status.toLowerCase() == "received") && x.transactionDate < toDate)
                                    //     .reduce(function (prev, cur) {
                                    //         return prev + cur.installmentAmount;
                                    //     }, 0);


                                    const totalMonotoriumInterest = installments.filter(x => x.status == "Pending")
                                        .reduce(function (prev, cur) {
                                            return prev + cur.moratoriumInterest;
                                        }, 0);

                                    // const totalMonotoriumInterest1 = installments.filter((x) => (x.status.toLowerCase() == "received") && x.transactionDate < toDate)
                                    //     .reduce(function (prev, cur) {
                                    //         return prev + cur.moratoriumInterest;
                                    //     }, 0);

                                    const totalLateFeeOutstanding = fees.filter(x => x.status == "Pending" && x.feeType == "Late Fee")
                                        .reduce(function (prev, cur) {
                                            return prev + cur.feeAmount;
                                        }, 0);

                                    // const totalLateFeeOutstanding1 = fees.filter((x) => x.status.toLowerCase() == "received" && x.feeType == "Late Fee" && x.feeDueDate < toDate)
                                    //     .reduce(function (prev, cur) {
                                    //         return prev + cur.feeAmount;
                                    //     }, 0);

                                    const totalOutstandingAmount = totalEmiAmountOutstanding + totalLateFeeOutstanding + totalMonotoriumInterest; //totalEmiAmountOutstanding1 + totalLateFeeOutstanding1 + totalMonotoriumInterest1;

                                    record.Current_Balance = Math.round((totalOutstandingAmount + Number.EPSILON) * 100) / 100;

                                    // const _amountOverdue = installments.filter((x) => x.status.toLowerCase() == "pending" && x.dueDate < toDate);
                                    // logger.logs("_amountOverdue", _amountOverdue);
                                    const pendingInstallements = installments.filter((x) => x.status.toLowerCase() == "pending" && x.dueDate < toDate);

                                    const Past_Due = pendingInstallements.length;

                                    const amountOverdue = pendingInstallements.reduce(function (prev, cur) {
                                        return prev + cur.installmentAmount + cur.moratoriumInterest;
                                    }, 0);

                                    // const _amountOverdue1 = installments.filter((x) => (x.status.toLowerCase() == "received") && x.transactionDate < toDate && x.dueDate < toDate && x.transactionDate > x.dueDate && getYMDFormattedDate(x.transactionDate, "-") != getYMDFormattedDate(x.dueDate, '-'));
                                    //logger.logs("_amountOverdue1", _amountOverdue1);

                                    // const amountOverdue1 = installments.filter((x) => (x.status.toLowerCase() == "received") && x.transactionDate < toDate && x.dueDate < toDate && x.transactionDate > x.dueDate && getYMDFormattedDate(x.transactionDate, "-") != getYMDFormattedDate(x.dueDate, '-'))
                                    //     .reduce(function (prev, cur) {
                                    //         logger.logs("installment no", cur.installmentNumber);
                                    //         logger.logs("dueDate", getYMDFormattedDate(cur.dueDate, "-"));
                                    //         logger.logs("transactionDate", getYMDFormattedDate(cur.transactionDate, "-"));

                                    //         return prev + cur.installmentAmount + cur.moratoriumInterest;
                                    //     }, 0);

                                    const feeOverDue = fees.filter((x) => x.status.toLowerCase() == "pending" && x.feeDueDate < toDate)
                                        .reduce(function (prev, cur) {
                                            return prev + cur.feeAmount;
                                        }, 0);

                                    // const feeOverDue1 = fees.filter((x) => (x.status.toLowerCase() == "received") && x.feeCollectedDate < toDate && x.feeDueDate < toDate && x.feeCollectedDate > x.feeDueDate && getYMDFormattedDate(x.feeCollectedDate, "-") != getYMDFormattedDate(x.feeDueDate, '-'))
                                    //     .reduce(function (prev, cur) {
                                    //         return prev + cur.feeAmount;
                                    //     }, 0);

                                    //logger.logs("amountOverdue", amountOverdue);
                                    //logger.logs("amountOverdue1", amountOverdue1);

                                    //record.Amount_Overdue = Math.round((amountOverdue + amountOverdue1 + Number.EPSILON) * 100) / 100;
                                    record.Amount_Overdue = amountOverdue ? amountOverdue.toFixed(2) : 0;



                                    const Past_Due1 = installments.filter((x) => (x.status.toLowerCase() == "received") && x.transactionDate < toDate && x.transactionDate > x.dueDate && getYMDFormattedDate(x.transactionDate, "-") != getYMDFormattedDate(x.dueDate, '-')).length;

                                    record.Past_Due = Past_Due + Past_Due1;

                                    record.EMI_AMOUNT = installments.filter(x => x.installmentNumber == 1)[0].installmentAmount;
                                }

                                if (userInfo.poaDetails) {
                                    const voterId = userInfo.poaDetails.find((x) => x.poaType == "Voter Id");

                                    if (voterId && voterId != null) {
                                        record.VOTER_ID_NO = voterId.poaNo;
                                    }

                                    const dl = userInfo.poaDetails.find((x) => x.poaType == "Driving Licence");
                                    if (dl && dl != null) {
                                        record.DL_NO = dl.poaNo;
                                    }

                                    const aadhaar = userInfo.poaDetails.find((x) => x.poaType == "Aadhaar Card");
                                    if (aadhaar && aadhaar != null) {
                                        record.AADHAR = aadhaar.poaNo;
                                    }
                                }
                                records.push(record);
                            }
                        } else loanFees.message

                    } output.message = loanInstallments.message;
                }
            }

            //logger.logs("records.length", records.length);
            if (records.length > 0) {
                let today = new Date();
                const currentMonth = today.getMonth() + 1;

                const fileName = "CibilReport_" + partnerMaster.partnerCode + today.getDate() + "_" + currentMonth + "_" + today.getFullYear().toString().substring(2, 4) + ".xlsx";
                let xls = json2xls(records);
                const filePath = config.get("exportToExcelPath.generalReports") + fileName;
                fs.writeFileSync(filePath, xls, "binary");
                logger.logs(filePath);
                await aws.upload(fileName, filePath);
                const singedURL = aws.getSignedUrlToDownload({ url: fileName });
                output = {
                    fileName: fileName,
                    fileURL: singedURL,
                    success: true,
                    message: "Fetched Successfully",
                    status: 200,
                };
            }
        } else output.message = "Partner Not Found";
    } catch (ex) {
        logger.logs(ex);
        saveErrorLog("exportCibilReportForLendingPartner", apiPath, null, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};

module.exports.exportCibilReportForLendingPartnerV1 = async function (model) {
    logger.logs("model.partnerId", model.partnerId);
    let output = {
        fileName: "",
        fileURL: "",
        success: false,
        message: "Some Error Occurred",
        status: 200,
    };
    try {
        const partnerMaster = await PartnerMaster.findById(model.partnerId);

        if (partnerMaster) {
            let fromDate = model.fromDate ? getFromDate(model.fromDate) : new Date(Date.now());
            let toDate = model.toDate ? getToDate(model.toDate) : new Date(Date.now());

            fromDate = new Date(fromDate.getFullYear(), fromDate.getMonth(), fromDate.getDate(), 0, 0, 0, 0);
            toDate = new Date(toDate.getFullYear(), toDate.getMonth(), toDate.getDate(), 23, 59, 59);

            let records = [];
            const postDisbursalStatus = config.get("keys.postDisbursalStatus");

            const limit = 100;

            const totalLoans = await LoanInfo.countDocuments({ "lenderDetails.lenderPartnerId": mongoose.Types.ObjectId(model.partnerId), "disbursalDetails.disbursedOn": { $gte: fromDate, $lte: toDate }, status: { $in: postDisbursalStatus } })

            let totalPages = parseInt(totalLoans / limit);
            let skip = 0;

            for (let page = 0; page < (totalPages + 1); page++) {
                skip = page * limit;

                const loans = await LoanInfo.find({ "lenderDetails.lenderPartnerId": mongoose.Types.ObjectId(model.partnerId), "disbursalDetails.disbursedOn": { $gte: fromDate, $lte: toDate }, status: { $in: postDisbursalStatus } }).select({ _id: 1, loanNo: 1, disbursalDetails: 1, amount: 1, interestRate: 1, tenure: 1, repaymentFrequency: 1, userId: 1 }).sort({ loanNo: 1 }).skip(skip).limit(limit);

                if (loans && loans.length > 0) {
                    const loanNos = [], userIds = []
                    loans.forEach(element => {
                        loanNos.push(element.loanNo)
                        userIds.push(element.userId)
                    })

                    const users = await UserInfo.find({ _id: { $in: userIds } }).select({ userDetails: 1, questionnairreDetails: 1, panNo: 1, addressDetails: 1, poaDetails: 1 });

                    let userHash = keyBy(users, "_id");

                    const loanInstallments = await installmentService.getMultipleLoansInstallments(null, loanNos);

                    let installmentHash = groupBy(loanInstallments.data, "loanId")

                    if (loanInstallments.success) {
                        const loanFees = await installmentService.getMultipleLoanFees(null, loanNos);

                        if (loanFees.success) {
                            let loanFeesHash = groupBy(loanFees.data, "loanId")

                            for (const loan of loans) {
                                const userInfo = userHash[loan.userId.toString()];
                                const record = {
                                    Consumer_Name: getName(userInfo.userDetails.firstName, userInfo.userDetails.middleName, userInfo.userDetails.lastName),
                                    CUSTOMER_DOB: getDMYFormattedDate(userInfo.userDetails.dob, ""),
                                    GENDER: userInfo.questionnairreDetails.gender,
                                    PAN_NO: userInfo.panNo,
                                    PASSPORT_NO: null,
                                    VOTER_ID_NO: null,
                                    DL_NO: null,
                                    AADHAR: null,
                                    TELEPHONE_NO: userInfo.userDetails.mobileNo[0] ? userInfo.userDetails.mobileNo[0].mobileNumber : "",
                                    E_MAIL_ID: userInfo.userDetails.emailId[0],
                                    ADDRESS_LINE_1: userInfo.addressDetails[0].add1,
                                    ADDRESS_LINE_2: userInfo.addressDetails[0].add2,
                                    ADDRESS_LINE_3: userInfo.addressDetails[0].add3,
                                    ADDRESS_LINE_4: null,
                                    ADDRESS_LINE_5: null,
                                    STATE: userInfo.addressDetails[0].state,
                                    PINCODE: userInfo.addressDetails[0].pincode,
                                    LOAN_NO: loan.loanNo,
                                    Ownership_Indicator: "1",
                                    LOAN_APPROVAL_DATE: getDMYFormattedDate(loan.disbursalDetails.disbursedOn, ""),
                                    TERMINATION_DATE: null,
                                    CUT_OFF_DATE: null,
                                    LOAN_AMOUNT: loan.amount,
                                    Current_Balance: null,
                                    Amount_Overdue: null,
                                    Past_Due: null,
                                    RATE_OF_INTEREST: loan.interestRate,
                                    REPAYMENT_TENURE: loan.tenure,
                                    EMI_AMOUNT: null,
                                    PAYMENT_FREQUENCY: loan.repaymentFrequency,
                                    OCCUPATION_CODE: null
                                };

                                const installments = installmentHash[loan._id.toString()] || [];
                                const fees = loanFeesHash[loan._id.toString()] || [];

                                if (installments && installments != null && installments.length > 0) {

                                    record.TERMINATION_DATE = getDMYFormattedDate(installments.find(x => x.installmentNumber == installments.length).actualDueDate, "");

                                    let totalEmiAmountOutstanding = 0, totalMonotoriumInterest = 0, totalLateFeeOutstanding = 0, pendingInstallements = 0, amountOverdue = 0

                                    let initialInstallment = null

                                    if (installments) {
                                        installments.forEach(element => {
                                            if (element.status === "Pending") {
                                                totalEmiAmountOutstanding += element.installmentAmount || 0
                                                totalMonotoriumInterest += element.moratoriumInterest || 0
                                                if (element.dueDate < toDate) {
                                                    pendingInstallements += 1
                                                    amountOverdue += (element.installmentAmount || 0) + (element.moratoriumInterest || 0)
                                                }
                                            }

                                            if (element.installmentNumber == 1) initialInstallment = element

                                        })
                                    }


                                    if (fees) {
                                        fees.forEach(element => {
                                            if (element.status === "Pending" && element.feeType === "Late Fee") {
                                                totalLateFeeOutstanding += element.feeAmount
                                            }
                                        })
                                    }

                                    const totalOutstandingAmount = totalEmiAmountOutstanding + totalLateFeeOutstanding + totalMonotoriumInterest;

                                    record.Current_Balance = Math.round((totalOutstandingAmount + Number.EPSILON) * 100) / 100;

                                    const Past_Due = pendingInstallements;

                                    record.Amount_Overdue = amountOverdue ? amountOverdue.toFixed(2) : 0;

                                    record.Past_Due = Past_Due

                                    record.EMI_AMOUNT = initialInstallment.installmentAmount;
                                }

                                if (userInfo.poaDetails) {
                                    const voterId = userInfo.poaDetails.find((x) => x.poaType == "Voter Id");

                                    if (voterId && voterId != null) {
                                        record.VOTER_ID_NO = voterId.poaNo;
                                    }

                                    const dl = userInfo.poaDetails.find((x) => x.poaType == "Driving Licence");
                                    if (dl && dl != null) {
                                        record.DL_NO = dl.poaNo;
                                    }

                                    const aadhaar = userInfo.poaDetails.find((x) => x.poaType == "Aadhaar Card");
                                    if (aadhaar && aadhaar != null) {
                                        record.AADHAR = aadhaar.poaNo;
                                    }
                                }
                                records.push(record);
                            }
                        } else loanFees.message

                    } output.message = loanInstallments.message;
                }
            }

            //logger.logs("records.length", records.length);
            if (records.length > 0) {
                let today = new Date();
                const currentMonth = today.getMonth() + 1;

                const fileName = "CibilReport_" + partnerMaster.partnerCode + today.getDate() + "_" + currentMonth + "_" + today.getFullYear().toString().substring(2, 4) + ".xlsx";
                let xls = json2xls(records);
                const filePath = config.get("exportToExcelPath.generalReports") + fileName;
                fs.writeFileSync(filePath, xls, "binary");
                logger.logs(filePath);
                await aws.upload(fileName, filePath);
                fs.unlinkSync(filePath)
                const singedURL = aws.getSignedUrlToDownload({ url: fileName });
                logger.logs("singedURL", singedURL);
                output = {
                    fileName: fileName,
                    fileURL: singedURL,
                    success: true,
                    message: "Fetched Successfully",
                    status: 200,
                };
            } else {
                output.message = "No Record Exists";
            }
        } else output.message = "Partner Not Found";
    } catch (ex) {
        logger.logs(ex);
        saveErrorLog("exportCibilReportForLendingPartner", apiPath, null, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};

module.exports.exportCibilReportForLendingPartnerV2 = async function (model) {
    logger.logs("model.partnerId", model.partnerId);
    let output = {
        fileName: "",
        fileURL: "",
        success: false,
        message: "Some Error Occurred",
        status: 200,
    };
    try {
        const partnerMaster = await PartnerMaster.findById(model.partnerId);
        if (partnerMaster) {
            let fromDate = model.fromDate ? getFromDate(model.fromDate) : new Date(Date.now());
            let toDate = model.toDate ? getToDate(model.toDate) : new Date(Date.now());

            fromDate = new Date(fromDate.getFullYear(), fromDate.getMonth(), fromDate.getDate(), 0, 0, 0, 0);
            loanInProgressFromDate = dateAdd("day", 270, fromDate)
            toDate = new Date(toDate.getFullYear(), toDate.getMonth(), toDate.getDate(), 23, 59, 59, 999);

            let records = [];
            const postDisbursalStatus = config.get("keys.postDisbursalStatus");

            const limit = 100;

            let matchQuery = {
                "lenderDetails.lenderPartnerId": mongoose.Types.ObjectId(model.partnerId),
                $or: [
                    {
                        "flowDetails": {
                            $elemMatch: {
                                "Step": {
                                    $in: ["Close Prepaid", "Close Repaid", "WrittenOff Paid"]
                                },
                                "insertedOn": { $gte: fromDate, $lte: toDate }
                            }
                        },
                    },
                    {
                        $and: [
                            {
                                "flowDetails": {
                                    $elemMatch: {
                                        "Step": "Written-Off",
                                        "insertedOn": { $lte: toDate }
                                    }
                                },
                            },
                            {
                                $or: [
                                    { "flowDetails.Step": { $ne: "WrittenOff Paid" } },
                                    {
                                        "flowDetails": {
                                            $elemMatch: {
                                                "Step": "WrittenOff Paid",
                                                "insertedOn": { $gt: toDate }
                                            }
                                        },
                                    },
                                ]
                            },
                        ]
                    }
                ]

            }

            let loanInProgressQuery = {
                "lenderDetails.lenderPartnerId": mongoose.Types.ObjectId(model.partnerId),

                $and: [
                    {
                        "flowDetails": {
                            $elemMatch: {
                                "Step": "Loan In Progress",
                                "insertedOn": { $lte: toDate }
                            }
                        },
                    },
                    {
                        $or: [
                            {
                                "flowDetails.Step": { $nin: ["Close Prepaid", "Close Repaid", "WrittenOff Paid", "Written-Off", "Loan Cancelled"] }
                            },
                            {
                                "flowDetails": {
                                    $elemMatch: {
                                        "Step": { $in: ["Close Prepaid", "Close Repaid", "WrittenOff Paid", "Written-Off", "Loan Cancelled"] },
                                        "insertedOn": { $gt: toDate }
                                    }
                                },
                            },
                        ]

                    },
                ]
            }

            let totalLoans = await LoanInfo.countDocuments(matchQuery)

            if (!totalLoans) {
                totalLoans = await LoanInfo.countDocuments(loanInProgressQuery)
            }

            let totalPages = parseInt(totalLoans / limit);
            let skip = 0;
            let currentDate = "'" + getDMYFormattedDate(new Date(), "")

            let statesQuery = [
                {
                    $group: {
                        "_id": "$state",
                        transUnionStateCode: { $first: "$transUnionStateCode" }
                    }
                }
            ]

            let states = await CityStateMaster.aggregate(statesQuery)

            let stateHash = {}

            states.forEach(element => {
                stateHash[element._id] = element.transUnionStateCode
            })

            for (let page = 0; page < (totalPages + 1); page++) {
                skip = page * limit;

                let loans = await LoanInfo.find(matchQuery).select({ _id: 1, loanNo: 1, disbursalDetails: 1, amount: 1, interestRate: 1, tenure: 1, repaymentFrequency: 1, userId: 1, status: 1, arrears: 1, flowDetails: 1, lastEmiPaidDate: 1 }).sort({ loanNo: 1 }).skip(skip).limit(limit);

                let loanInProgressLoans = await LoanInfo.find(loanInProgressQuery).select({ _id: 1, loanNo: 1, disbursalDetails: 1, amount: 1, interestRate: 1, tenure: 1, repaymentFrequency: 1, userId: 1, status: 1, arrears: 1, flowDetails: 1 }).sort({ loanNo: 1 }).skip(skip).limit(limit);
                if (loanInProgressLoans.length) {
                    loans.push(...loanInProgressLoans)
                }
                loans = uniq(loans, "loanNo")

                if (loans && loans.length > 0) {
                    const loanNos = [], userIds = []
                    loans.forEach(element => {
                        loanNos.push(element.loanNo)
                        userIds.push(element.userId)
                    })

                    const users = await UserInfo.find({ _id: { $in: userIds } }).select({ userDetails: 1, questionnairreDetails: 1, panNo: 1, addressDetails: 1, poaDetails: 1 });

                    let userHash = keyBy(users, "_id");

                    const loanInstallments = await installmentService.getMultipleLoansInstallments(null, loanNos);

                    let installmentHash = groupBy(loanInstallments.data, "loanId")

                    if (loanInstallments.success) {
                        const loanFees = await installmentService.getMultipleLoanFees(null, loanNos);

                        if (loanFees.success) {
                            let loanFeesHash = groupBy(loanFees.data, "loanId")

                            for (const loan of loans) {
                                const userInfo = userHash[loan.userId.toString()];
                                let gender = userInfo.questionnairreDetails.gender;
                                let emails = userInfo.userDetails.emailId
                                let name = getName(userInfo.userDetails.firstName, userInfo.userDetails.middleName, userInfo.userDetails.lastName)
                                name = name ? name.split(".").join("") : name
                                let address = userInfo.addressDetails[0].add1
                                if (address) {
                                    let splittedAddress = address.split(",")
                                    splittedAddress = splittedAddress.filter(a => a != "-")
                                    address = splittedAddress.join(",")
                                }

                                const record = { //the order should not change
                                    "Consumer Name": name,
                                    "Date of Birth": `'${getDMYFormattedDate(userInfo.userDetails.dob, "")}`,
                                    "Gender": gender === "Female" ? 1 : gender === "Male" ? 2 : 3,
                                    "Income Tax ID Number": userInfo.panNo,
                                    "Passport Number": "",
                                    "Passport Issue Date": "",
                                    "Passport Expiry Date": "",
                                    "Voter ID Number": "",
                                    "Driving License Number": "",
                                    "Driving License Issue Date": "",
                                    "Driving License Expiry Date": "",
                                    "Ration Card Number": "",
                                    "Universal ID Number": "",
                                    "Additional ID #1": "",
                                    "Additional ID #2": "",
                                    "Telephone No.Mobile": "",
                                    "Telephone No.Residence": "",
                                    "Telephone No.Office": "",
                                    "Extension Office": "",
                                    "Telephone No.Other": "",
                                    "Extension Other": "",
                                    "Email ID 1": emails[0],
                                    "Email ID 2": "",
                                    "Address Line 1": address,
                                    "State Code 1": leadingZeroFormatExcel(stateHash[userInfo.addressDetails[0].state] || "99", "00"), //  If the State Code is not known, then it should be set to 99
                                    "PIN Code 1": userInfo.addressDetails[0].pincode,
                                    "Address Category 1": "'04",
                                    "Residence Code 1": "",
                                    "Address Line 2": "",
                                    "State Code 2": "",
                                    "PIN Code 2": "",
                                    "Address Category 2": "",
                                    "Residence Code 2": "",
                                    "Current/New Member Code": "",
                                    "Current/New Member Short Name": "",
                                    "Curr/New Account No": loan.loanNo.toString(),
                                    "Account Type": 61,
                                    "Ownership Indicator": 1,
                                    "Date Opened/Disbursed": `'${getDMYFormattedDate(loan.disbursalDetails.disbursedOn, "")}`,
                                    "Date of Last Payment": loan.lastEmiPaidDate ? `'${getDMYFormattedDate(loan.lastEmiPaidDate, "")}` : "",
                                    "Date Closed": "",
                                    "Date Reported": currentDate,
                                    "High Credit/Sanctioned Amt": loan.amount,
                                    "Current  Balance": "",
                                    "Amt Overdue": "",
                                    "No of Days Past Due": "",
                                    "Old Mbr Code": "",
                                    "Old Mbr Short Name": "",
                                    "Old Acc No": "",
                                    "Old Acc Type": "",
                                    "Old Ownership Indicator": "",
                                    "Suit Filed / Wilful Default": "",
                                    "Credit Facility Status": loan.status === "Written-Off" ? "'02" : "",
                                    "Asset Classification": "",
                                    "Value of Collateral": "",
                                    "Type of Collateral": "",
                                    "Credit Limit": "",
                                    "Cash Limit": "",
                                    "Rate of Interest": "",
                                    "RepaymentTenure": "",
                                    "EMI Amount": "",
                                    "Written- off Amount (Total)": "",
                                    "Written- off Principal Amount": "",
                                    "Settlement Amt": "",
                                    "Payment Frequency": "",
                                    "Actual Payment Amt": "",
                                    "Occupation Code": "",
                                    "Income": "",
                                    "Net/Gross Income Indicator": "",
                                    "Monthly/Annual Income Indicator": "",
                                };

                                const installments = installmentHash[loan._id.toString()] || [];
                                const fees = loanFeesHash[loan._id.toString()] || [];

                                if (loan.status === "Written-Off") {
                                    let arrear = null

                                    if (loan.arrears && loan.arrears.length) {
                                        arrear = loan.arrears.find(item => item.delinquency == "Write-Off")
                                    }

                                    if (arrear) {
                                        record["Written- off Amount (Total)"] = Math.floor(arrear.amount)
                                        let writtenOffPrincipal = Math.floor(arrear.amount * (1 + (loan.interestRate / 100) * loan.tenure / 365))
                                        record["Written- off Principal Amount"] = writtenOffPrincipal

                                    }
                                }

                                if (installments && installments != null && installments.length > 0) {

                                    let totalEmiAmountOutstanding = 0, totalMonotoriumInterest = 0, totalLateFeeOutstanding = 0, pendingInstallements = 0, amountOverdue = 0, dpd = 0

                                    let initialInstallment = null

                                    if (installments) {
                                        installments.forEach(element => {
                                            if (element.status === "Pending") {
                                                totalEmiAmountOutstanding += element.installmentAmount || 0
                                                totalMonotoriumInterest += element.moratoriumInterest || 0
                                                if (element.dueDate < toDate) {
                                                    pendingInstallements += 1

                                                    let diDays = new DateDiff(toDate, element.dueDate).days()
                                                    amountOverdue += (element.installmentAmount || 0) + (element.moratoriumInterest || 0)
                                                    if (!dpd) dpd = Math.ceil(diDays)

                                                }
                                            }

                                            if (element.status === "Received") {

                                                if (element.transactionDate > toDate) {
                                                    totalEmiAmountOutstanding += element.installmentAmount || 0
                                                    totalMonotoriumInterest += element.moratoriumInterest || 0
                                                }

                                                if (element.dueDate < toDate && element.transactionDate > toDate) {
                                                    let diDays = new DateDiff(toDate, element.dueDate).days()
                                                    amountOverdue += (element.installmentAmount || 0) + (element.moratoriumInterest || 0)
                                                    if (!dpd) dpd = Math.ceil(diDays)
                                                }

                                            }

                                            if (element.installmentNumber == 1) initialInstallment = element

                                        })
                                    }

                                    if (fees) {
                                        fees.forEach(element => {
                                            if (element.status === "Pending" && element.feeType === "Late Fee") {
                                                totalLateFeeOutstanding += element.feeAmount
                                            }
                                        })
                                    }

                                    const totalOutstandingAmount = totalEmiAmountOutstanding + totalLateFeeOutstanding + totalMonotoriumInterest;

                                    if (loan.status === "Close Prepaid" || loan.status === "Close Repaid" || loan.status === "WrittenOff Paid") {
                                        let flowDetail = loan.flowDetails.find(it => it.Step === loan.status)
                                        if (flowDetail && (flowDetail.insertedOn > fromDate && flowDetail.insertedOn < toDate)) {
                                            record["Date Closed"] = `'${getDMYFormattedDate(flowDetail.insertedOn, "")}`
                                        }
                                    }

                                    record["Current  Balance"] = Math.round((totalOutstandingAmount + Number.EPSILON) * 100) / 100;

                                    record["Amt Overdue"] = amountOverdue ? Math.floor(amountOverdue) : 0;

                                    record["No of Days Past Due"] = dpd

                                    record["EMI Amount"] = initialInstallment.installmentAmount;
                                }

                                if (userInfo.poaDetails) {

                                    const voterId = userInfo.poaDetails.find((x) => x.poaType == "Voter Id");

                                    if (voterId && voterId != null) {
                                        record["Voter ID Number"] = voterId.poaNo;
                                    }

                                    const passport = userInfo.poaDetails.find((x) => x.poaType == "PASSPORT");

                                    if (passport && passport != null) {
                                        record["Voter ID Number"] = passport.poaNo;
                                    }

                                    const dl = userInfo.poaDetails.find((x) => x.poaType == "Driving Licence");
                                    if (dl && dl != null) {
                                        record["Driving License Number"] = dl.poaNo;
                                    }

                                    // const aadhaar = userInfo.poaDetails.find((x) => x.poaType == "Aadhaar Card");
                                    // if (aadhaar && aadhaar != null) {
                                    //     record.AADHAR = aadhaar.poaNo;
                                    // }

                                }
                                records.push(record);
                            }
                        } else loanFees.message

                    } output.message = loanInstallments.message;
                }
            }

            //logger.logs("records.length", records.length);
            if (records.length > 0) {
                let today = new Date();
                const currentMonth = today.getMonth() + 1;

                const fileName = "CibilReport_" + partnerMaster.partnerCode + today.getDate() + "_" + currentMonth + "_" + today.getFullYear().toString().substring(2, 4) + ".xlsx";
                let workbook = XLSX.utils.book_new()
                let worksheet = XLSX.utils.json_to_sheet(records)
                let dateFormat = "00000000"
                let twoDigitFormat = "00"
                let formatObj = {
                    "B": dateFormat,
                    "Y": twoDigitFormat,
                    "AA": twoDigitFormat,
                    "AN": dateFormat,
                    "AM": dateFormat,
                    "AO": dateFormat,
                    "AP": dateFormat,
                    "BA": twoDigitFormat
                }
                // FormatCells(XLSX, worksheet, formatObj)
                XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1")
                const filePath = config.get("exportToExcelPath.generalReports") + fileName;
                XLSX.writeFileSync(workbook, filePath)
                await aws.upload(fileName, filePath);
                fs.unlinkSync(filePath)
                const singedURL = aws.getSignedUrlToDownload({ url: fileName });
                output = {
                    fileName: fileName,
                    fileURL: singedURL,
                    success: true,
                    message: "Fetched Successfully",
                    status: 200,
                };
            } else {
                output.message = "No Record Exists";
            }
        } else output.message = "Partner Not Found";
    } catch (ex) {
        logger.logs(ex);
        saveErrorLog("exportCibilReportForLendingPartner", apiPath, null, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};

module.exports.exportLoansReport = async function (model) {
    let output = {
        fileName: "",
        fileURL: "",
        success: false,
        message: "Some Error Occurred",
        status: 501,
    };
    try {
        const loans = await LoanInfo.find({ partnerId: mongoose.Types.ObjectId(model.partnerId) }).select({ _id: 1, loanNo: 1, merchantCode: 1, trxnId: 1, productId: 1, adminRemarks: 1, rejectDescription: 1, amount: 1, disbursalDetails: 1 });

        const partnerMaster = await PartnerMaster.findById(model.partnerId).select({ partnerCode: 1 });
        const loanIds = loans.map(x => x._id)

        // const today = new Date();
        // const emiDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1, 5, 30, 0, 0);
        const allInstallments = await installmentService.getInstallmentsPending(loanIds);
        if (allInstallments.success) {
            let records = [];
            for (const loanInfo of loans) {
                const record = {
                    merchantCode: loanInfo.merchantCode,
                    loanNo: loanInfo.loanNo,
                    loanAmount: loanInfo.amount,
                    insertedOn: getDMYFormattedDate(loanInfo.insertedOn, "/"),
                    status: loanInfo.status,
                    adminRemarks: loanInfo.adminRemarks,
                    rejectDescription: loanInfo.rejectDescription,
                    disbursedAmount: 0,
                    outstandingPrincipal: 0,
                    outstandingInterest: 0
                };

                const installmentDetails = allInstallments.data.filter(x => x.loanId.toString() == loanInfo._id.toString());

                if (installmentDetails && installmentDetails != null && installmentDetails.length > 0) {
                    const overDueEmis = allPendingEmis.data;
                    record.outstandingPrincipal = installmentDetails.principalOutstanding;

                    record.outstandingInterest = installmentDetails.interestOutstanding;

                    if (loanInfo.disbursalDetails && loanInfo.disbursalDetails != null && loanInfo.disbursalDetails.disbursalAmount && loanInfo.disbursalDetails.disbursalAmount != null) {
                        record.disbursedAmount = loanInfo.disbursalDetails.disbursalAmount;
                    } else {
                        const preApproval = await PreApprovals.findOne({
                            partnerId: mongoose.Types.ObjectId(loanInfo.partnerId),
                            merchantCode: loanInfo.merchantCode,
                            productType: loanInfo.productType.split("_")[0],
                            $or: [{ trxnId: loanInfo.trxnId }, { productId: loanInfo.trxnId }]
                        }).sort({ insertedOn: -1 });

                        const charges = await getLoanCharges(loanInfo.amount, loanInfo._id, preApproval._id, false);
                        record.disbursedAmount = charges.disbursalAmount;
                    }
                }
                records.push(record);
            }

            if (records.length > 0) {
                let today = new Date();
                const currentMonth = today.getMonth() + 1;

                const fileName = "LoansReport_" + partnerMaster.partnerCode + today.getDate() + "_" + currentMonth + "_" + today.getFullYear().toString().substring(2, 4) + ".xlsx";
                let xls = json2xls(records);
                const filePath = config.get("exportToExcelPath.generalReports") + fileName;
                logger.logs(filePath);
                fs.writeFileSync(filePath, xls, "binary");
                await aws.upload(fileName, filePath);
                const singedURL = aws.getSignedUrlToDownload({ url: fileName });
                logger.logs("singedURL", singedURL);
                output = {
                    fileName: fileName,
                    fileURL: singedURL,
                    success: true,
                    message: "Fetched Successfully",
                    status: 200,
                };
            }
        } else output.message = allInstallments.message;

    } catch (ex) {
        logger.logs(ex);
        saveErrorLog("exportCibilReportForLendingPartner", apiPath, null, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        output.message = ex.message;
    }
    return output;
};