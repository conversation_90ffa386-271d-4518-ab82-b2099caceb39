
let { generateOtp, addMinutes, validateM<PERSON>ile<PERSON><PERSON><PERSON>, validateEmail, minutesDifference } = require('../../../services/ops/common/common');
let { OtpValidation, OtpValidationHistory } = require('../../../models/ops/otp/otp');
let { OTP_CHANNELS, OTP_CONFIG } = require('../common/constants').Constants
const { saveErrorLog } = require("../common/masters");
const apiPath = __dirname;

module.exports.sendOtp = async function (payload) {

    try {

        let { error: payloadError, response: payloadRes } = validatePayload(payload);

        if (payloadError) {
            return { error: payloadError, response: null }
        }

        let query = {
            notifyTo: payload.notifyTo,
            notificationType: payload.notificationType,
            channel: payload.channel
        }

        let otpRes = await OtpValidation.findOne(query).lean()

        if (otpRes) {
            let { error, response } = validateSendOtp(otpRes);

            if (error) {
                return { error, response: null }
            }
        }

        let otp = generateOtp(payload.otpLength);
        let now = new Date();


        let obj = {
            notifyTo: payload.notifyTo,
            notificationType: payload.notificationType,
            channel: payload.channel,
            otp,
            otpValidOn: addMinutes(OTP_CONFIG[payload.notificationType] ? OTP_CONFIG[payload.notificationType].OTP_VALIDITY_TIME : OTP_CONFIG["DEFAULT"].OTP_VALIDITY_TIME),
            lastNotifiedOn: now,
            validatedCount: 0,
            lastValidatedOn: null,
            insertedOn: now,
            modifiedOn: now
        }

        let isReset = false;
        if (otpRes) { //RESET
            let resetTimeInMin = OTP_CONFIG[payload.notificationType] ? OTP_CONFIG[payload.notificationType].SEND_OTP_RESET_TIME : OTP_CONFIG[payload.notificationType].SEND_OTP_RESET_TIME;
            let minutesDiff = minutesDifference(otpRes.lastNotifiedOn, now);

            if (minutesDiff >= resetTimeInMin) {
                obj.notifiedCount = 1;
                isReset = true
            }
        }

        let updateObj = { $set: obj }

        if (!isReset) {
            updateObj = { ...updateObj, $inc: { notifiedCount: 1 } }
        }

        if (otpRes)
            insertOtpValidationHistory(otpRes)

        let otpUpdateRes = await OtpValidation.updateOne(query, updateObj, { upsert: true })

        let remainingAttempts = null;
        let maxNotifyCount = payload.notificationType && OTP_CONFIG[payload.notificationType] ? OTP_CONFIG[payload.notificationType].MAX_SEND_OTP : OTP_CONFIG["DEFAULT"].MAX_SEND_OTP;
        if (otpRes) {
            remainingAttempts = maxNotifyCount - (otpRes.notifiedCount + 1)
        } else {
            remainingAttempts = maxNotifyCount - 1
        }

        obj.remainingAttempts = remainingAttempts

        return { error: null, response: obj };
    } catch (error) {
        saveErrorLog("sendOtp", apiPath, {}, error.stack ? error.stack.toString().replace(/\r?\n|\r/g, " ") : error);
        return { error, response: null }
    }

}

function validateSendOtp(otpRes) {

    try {

        let maxNotifyCount = otpRes.notificationType && OTP_CONFIG[otpRes.notificationType] ? OTP_CONFIG[otpRes.notificationType].MAX_SEND_OTP : OTP_CONFIG["DEFAULT"].MAX_SEND_OTP;
        let resetTimeInMin = otpRes.notificationType && OTP_CONFIG[otpRes.notificationType] ? OTP_CONFIG[otpRes.notificationType].SEND_OTP_RESET_TIME : OTP_CONFIG["DEFAULT"].SEND_OTP_RESET_TIME;
        let now = new Date();

        let minutesDiff = minutesDifference(otpRes.lastNotifiedOn, now);

        if (minutesDiff >= resetTimeInMin) {
            return { error: null, response: true }
        }

        // if (otpRes && (otpRes.notifiedCount >= maxNotifyCount)) {
        //     return { error: { message: "Too Many Attempts Please try Again After " + resetTimeInMin + ' Min' }, response: null }
        // }

        return { error: null, response: true };

    } catch (error) {
        return { error, response: null }
    }
}

async function validatePayload(payload) {

    if (!payload) {
        return { error: { message: "Missing Payload" }, response: null }
    }

    if (!payload.notifyTo) {
        return { error: { message: "Missing notifyTo" }, response: null }
    }

    if (!payload.channel) {
        return { error: { message: "Missing channel" }, response: null }
    }

    if (!payload.notificationType) {
        return { error: { message: "Missing notificationType" }, response: null }
    }

    if (payload.channel == OTP_CHANNELS.EMAIL && !validateEmail(payload.notifyTo)) {
        return { error: { message: "Invalid Email" }, response: null }
    }

    if ((payload.channel == OTP_CHANNELS.SMS ||
        payload.channel == OTP_CHANNELS.WHATSAPP) && !validateMobileNumber(payload.notifyTo)) {
        return { error: { message: "Inavlid MobileNo" }, response: null }
    }

    return { error: null, response: true }
}

module.exports.verifyOtp = async function (payload) {

    try {

        let maxValidateCount = OTP_CONFIG[payload.notificationType] ? OTP_CONFIG[payload.notificationType].MAX_OTP_VALIDATION : OTP_CONFIG["DEFAULT"].MAX_OTP_VALIDATION;
        let notificationResetTime = OTP_CONFIG[payload.notificationType] ? OTP_CONFIG[payload.notificationType].OTP_VALIDITY_TIME : OTP_CONFIG["DEFAULT"].OTP_VALIDITY_TIME;

        let { error, response } = validateVerifyOtpPayload(payload);

        if (error) {
            return { error, response: null }
        }

        let query = {
            notifyTo: payload.notifyTo,
            channel: payload.channel,
        }


        let otpRes = await OtpValidation.findOne(query).lean();

        console.log(otpRes, payload, "here")

        if (otpRes) {

            let obj = {}

            //RESET
            let isReset = false;
            let now = new Date();

            if (otpRes.lastValidatedOn) {
                let minutesDiff = minutesDifference(otpRes.lastValidatedOn, now);

                if (minutesDiff >= notificationResetTime) {
                    obj.validatedCount = 1;
                    isReset = true
                }
            }

            if (otpRes.validatedCount >= maxValidateCount && !isReset) {
                return { error: { message: "Too Many Invalid Attempts Please try Again After " + notificationResetTime + ' Min' }, response: null }
            }

            obj = {
                ...obj,
                lastValidatedOn: now,
                modifiedOn: now
            }

            if (otpRes)
                insertOtpValidationHistory(otpRes)

            let updateObj = { $set: obj }

            if (!isReset) {
                updateObj = { ...updateObj, $inc: { validatedCount: 1 } }
            }

            let updateRes = await OtpValidation.updateOne(query, updateObj)

            if (otpRes.otp != payload.otp) {
                return { error: { message: "Invalid OTP" }, response: null }
            }

            if (otpRes.otpValidOn <= now) {
                return { error: { message: "Otp Expired" }, response: null }
            }

            return { error: null, response: otpRes || {} }
        } else {
            return { error: { message: "Invalid Email/Mobile" }, response: null }
        }

    } catch (error) {
        saveErrorLog("verifyOtp", apiPath, {}, error.stack ? error.stack.toString().replace(/\r?\n|\r/g, " ") : error);
        return { error, response: null }
    }
}

async function validateVerifyOtpPayload(payload) {

    if (!payload) {
        return { error: { message: "Missing Payload" }, response: null }
    }

    if (!payload.otp) {
        return { error: { message: "Missing OTP" }, response: null }
    }

    if (!payload.notifyTo) {
        return { error: { message: "Missing notifyTo" }, response: null }
    }

    if (!payload.notificationType) {
        return { error: { message: "Missing notificationType" }, response: null }
    }

    if (!payload.channel) {
        return { error: { message: "Missing channel" }, response: null }
    }

    if (payload.channel == OTP_CHANNELS.EMAIL && !validateEmail(payload.notifyTo)) {
        return { error: { message: "Invalid Email" }, response: null }
    }

    if ((payload.channel == OTP_CHANNELS.SMS ||
        payload.channel == OTP_CHANNELS.WHATSAPP) && !validateMobileNumber(payload.notifyTo)) {
        return { error: { message: "Inavlid MobileNo" }, response: null }
    }

    return { error: null, response: true }
}

async function insertOtpValidationHistory(obj) {

    try {
        obj.otpValidationId = obj._id;
        delete obj._id
        let otpValidationHistory = new OtpValidationHistory(obj);
        otpValidationHistory.save()
    } catch (err) {
        saveErrorLog("insertOtpValidationHistory", apiPath, {}, error.stack ? error.stack.toString().replace(/\r?\n|\r/g, " ") : error);
    }
}