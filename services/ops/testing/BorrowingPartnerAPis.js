const mongoose = require("mongoose");
const { PaymentCollection } = require("../../../models/ops/loan/postDisbursal");

module.exports.testLoanRepayments = async function (model) {
    try {
        if (model.product_id && model.reference_no) {
            return {
                "product_id": model.product_id,
                "reference_no": model.reference_no,
                "status": true,
                "description": "Success"
            };
        } else if (model.product_id) {
            return {
                "product_id": model.product_id,
                "reference_no": null,
                "status": false,
                "description": "failed"
            };
        } else if (model.reference_no) {
            return {
                "product_id": null,
                "reference_no": model.reference_no,
                "status": false,
                "description": "failed"
            };
        }

    } catch (ex) {
        console.log(ex);
        return {
            "product_id": null,
            "reference_no": null,
            "status": false,
            "description": ex.message
        };
        saveErrorLog("testLoanRepayments", apiPath, null, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
    }
};

module.exports.testBulkLoanRepayment = async function (model) {
    return {
        "status": true,
        "message": "Bulk Loan Repayment Received successfully"
    };
};

module.exports.testLoanRepaymentEnquiry = async function (_productId, _referenceNo) {
    try {
        const paymentCollection = await PaymentCollection.findOne({ productId: _productId, referenceNo: _referenceNo });
        if (paymentCollection != null) {
            // return {
            //     repayment_status: "Auto Loan Repayed",
            //     due_date: paymentCollection.dueDate,
            //     repay_amount: paymentCollection.totalRepayAmount,
            //     paidAt: new Date(Date.now()),
            //     autoRetryAttempts: 1,
            //     referenceNumber: _referenceNo,
            //     paidOrNot: 1,
            //     activeOrNot: 0
            // };
            return {
                repayment_status: "Not Paid",
                due_date: null,
                repay_amount: null,
                paidAt: null,
                autoRetryAttempts: null,
                referenceNumber: _referenceNo,
                paidOrNot: 0,
                activeOrNot: null
            };
        } else {
            return {
                repayment_status: "Request No Found",
                due_date: null,
                repay_amount: null,
                paidAt: null,
                autoRetryAttempts: null,
                referenceNumber: _referenceNo,
                paidOrNot: 0,
                activeOrNot: null
            };
        }
    } catch (ex) {
        return {
            repayment_status: "Error Occurred",
            due_date: null,
            repay_amount: null,
            paidAt: null,
            autoRetryAttempts: null,
            referenceNumber: _referenceNo,
            paidOrNot: 0,
            activeOrNot: null
        };
    }
};

module.exports.testBulkLoanRepaymentEnquiry = async function (model) {
    let records = [];
    try {


        if (model.data && model.data.length > 0) {
            for (const data of model.data) {
                //For Success
                // if (data.loan_uid) {
                //     const record = {
                //         loan_uid: data.loan_uid,
                //         reference_no: data.reference_no,
                //         emi_number: data.emi_number,
                //         paid_status: 1,
                //         emi_timestamp: new Date(Date.now()),
                //         principal_paid: 1
                //     }
                //     records.push(record);
                // } else {
                //     const record = {
                //         productId: data.productId,
                //         reference_no: data.reference_no,
                //         emi_number: data.emi_number,
                //         paid_status: 1,
                //         emi_timestamp: new Date(Date.now()),
                //         principal_paid: 1
                //     }
                //     records.push(record);
                // }



                //For Failure
                if (data.loan_uid) {
                    const record = {
                        loan_uid: data.loan_uid,
                        reference_no: data.reference_no,
                        emi_number: data.emi_number,
                        invalid_res: ["emi_number"],
                        errors: ["emi_number does not exists"]
                    }
                    records.push(record);
                } else {
                    const record = {
                        productId: data.productId,
                        reference_no: data.reference_no,
                        emi_number: data.emi_number,
                        invalid_res: ["emi_number"],
                        errors: ["emi_number does not exists"]
                    }
                    records.push(record);
                }

            }
        }
        //Success Response
        // return {
        //     status: "success",
        //     reconciliation: records,
        //     error_enquires: [],
        //     error: []
        // };

        //Failure Response
        return {
            status: "success",
            reconciliation: [],
            error_enquires: records,
            error: []
        };

        //Error Response
        // return {
        //     status: "failed",
        //     desc: "Invalid Json arrays passed",
        //     err_code: "221",
        //     request_ref: "6ec1691c-1733-11ea-8ba3-0242ac11000a"
        // };

    } catch (ex) {
        if (model.data && model.data.length > 0) {
            for (const data of model.data) {
                const record = {
                    loan_uid: data.loan_uid,
                    reference_no: data.reference_no,
                    desc: ex.message
                }
                records.push(record);
            }
        }
        return {
            reconciliation: [],
            error_enquires: records,
            error: records
        };
    }
};