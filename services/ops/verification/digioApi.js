const mongoose = require("mongoose");
const config = require("config");
const fs = require("fs");

const { <PERSON><PERSON><PERSON><PERSON>, DigioBankList, DigioMandateGeneration, DigilockerLog } = require("../../../models/ops/digio/digioApi");
const { UserInfo, UserPartnerInfo, UserPartnerInfoHistory } = require("../../../models/ops/registration/users");
const { callDigioApi, getOcrId, sleep, getCurrentTimeStamp, getExtension, downloadImage, getMimeType, maskNumber, deleteFile, downloadImageBuffer, downloadImageFromS3, updateUserAndLoanSteps } = require("../common/common");
const { saveErrorLog } = require("../common/masters");
const { backupUserInfo } = require("../common/common");
const { BankBranchMaster } = require("../../../models/ops/common/master");
const { PartnerMaster, ConfigParameters, PartnerConfig } = require("../../../models/ops/partner/partner");
const apiPath = __dirname;
const { maskAadhaarUtil } = require("../identity-provider/provider-signzy");
const { LoanInfo } = require("../../../models/ops/loan/loan");
const { Stream } = require("stream");
const AWS = require("../../../models/ops/aws/s3");
const { ROLES } = require('../common/constants').Constants;


async function bankVerification(model, userId) {
    let requestSentOn = Date.now();

    try {
        const url = "client/verify/bank_account";

        const response = await callDigioApi("POST", url, model);

        let success = true;

        if (!response.verified)
            success = false;

        let digioLogModel = {
            request: model,
            response: response,
            apiCalled: "bankVerification",
            idNumber: model.beneficiary_account_no,
            success: success,
            requestSentOn: requestSentOn,
            responseReceivedOn: Date.now(),
            userId: userId ? userId : null
        }

        await log(digioLogModel);

        return { data: response, success: true, message: "" };
    }
    catch (ex) {
        console.log(ex);
        let digioLogModel = {
            request: model,
            response: ex.response.data,
            apiCalled: "bankVerification",
            idNumber: model.beneficiary_account_no,
            success: false,
            requestSentOn: requestSentOn,
            responseReceivedOn: Date.now(),
            userId: userId ? userId : null
        }

        await log(digioLogModel);

        return { data: ex.response.data, success: false, message: ex.response.statusText };
    }
}

module.exports.bankVerification = bankVerification;

async function gstVerification(model, userId) {

    let requestSentOn = Date.now();

    try {
        const url = "client/gst/details";

        const response = await callDigioApi("POST", url, model);

        let digioLogModel = {
            request: model,
            response: response,
            apiCalled: "gstVerification",
            idNumber: model.gstin,
            success: true,
            requestSentOn: requestSentOn,
            responseReceivedOn: Date.now(),
            userId: userId ? userId : null
        }

        await log(digioLogModel);

        return { data: response, success: true };
    }
    catch (ex) {

        let digioLogModel = {
            request: model,
            response: ex,
            apiCalled: "gstVerification",
            idNumber: model.gstin,
            success: false,
            requestSentOn: requestSentOn,
            responseReceivedOn: Date.now(),
            userId: userId ? userId : null
        }

        await log(digioLogModel);

        return { data: ex.response.data, success: false };
    }
}

module.exports.gstVerification = gstVerification;

async function panVerification(model, userId) {
    let requestSentOn = Date.now();

    try {
        const url = "v3/client/kyc/pan/verify";

        const response = await callDigioApi("POST", url, model);

        let success = true;

        if (response.error_message)
            success = false;

        let digioLogModel = {
            request: model,
            response: response,
            apiCalled: "panVerification",
            idNumber: model.pan_no,
            success: success,
            requestSentOn: requestSentOn,
            responseReceivedOn: Date.now(),
            userId: userId ? userId : null
        }

        await log(digioLogModel);

        return { data: response, success: true };
    }
    catch (ex) {

        let digioLogModel = {
            request: model,
            response: ex.response.data,
            apiCalled: "panVerification",
            idNumber: model.pan_no,
            success: false,
            requestSentOn: requestSentOn,
            responseReceivedOn: Date.now(),
            userId: userId ? userId : null
        }

        await log(digioLogModel);

        return { data: ex.response.data, success: false };
    }
}

module.exports.panVerification = panVerification;

async function voterIdVerification(model, userId) {

    let requestSentOn = Date.now();

    try {
        const url = "v3/client/kyc/fetch_id_data/VOTER_ID";

        const response = await callDigioApi("POST", url, model);

        let digioLogModel = {
            request: model,
            response: response,
            apiCalled: "voterIdVerification",
            idNumber: model.id_no,
            success: true,
            requestSentOn: requestSentOn,
            responseReceivedOn: Date.now(),
            userId: userId ? userId : null
        }

        await log(digioLogModel);

        return { data: response, success: true };
    }
    catch (ex) {

        let digioLogModel = {
            request: model,
            response: ex,
            apiCalled: "voterIdVerification",
            idNumber: model.id_no,
            success: false,
            requestSentOn: requestSentOn,
            responseReceivedOn: Date.now(),
            userId: userId ? userId : null
        }

        await log(digioLogModel);

        return { data: ex.response.data, success: false };
    }
}

module.exports.voterIdVerification = voterIdVerification;

async function dlVerification(model, userId) {

    let requestSentOn = Date.now();

    try {
        const url = "v3/client/kyc/fetch_id_data/DRIVING_LICENSE";

        const response = await callDigioApi("POST", url, model);

        let digioLogModel = {
            request: model,
            response: response,
            apiCalled: "dlVerification",
            idNumber: model.id_no,
            success: true,
            requestSentOn: requestSentOn,
            responseReceivedOn: Date.now(),
            userId: userId ? userId : null
        }

        await log(digioLogModel);

        return { data: response, success: true };
    }
    catch (ex) {

        let digioLogModel = {
            request: model,
            response: ex.response.data,
            apiCalled: "dlVerification",
            idNumber: model.id_no,
            success: false,
            requestSentOn: requestSentOn,
            responseReceivedOn: Date.now(),
            userId: userId ? userId : null
        }

        await log(digioLogModel);

        return { data: ex.response.data, success: false };
    }
}

module.exports.dlVerification = dlVerification;

async function ocr(model, userId) {

    let requestSentOn = Date.now();

    let request = Object.create(model);

    request.front_part = "base64String";
    request.back_part = "base64String";
    request.should_verify = model.should_verify;
    request.unique_request_id = model.unique_request_id;

    try {
        const url = "v3/client/kyc/analyze/idcard";

        let response = await callDigioApi("POST", url, model);

        let responseLog = Object.create(response);

        if (responseLog.encoded_image) responseLog.encoded_image = "base64String";
        if (responseLog.encoded_signature) responseLog.encoded_signature = "base64String";


        let digioLogModel = {
            request: model,
            response: response,
            apiCalled: "ocr",
            ocrType: response.id_type ? response.id_type : "",
            idNumber: model.id_no,
            success: true,
            requestSentOn: requestSentOn,
            responseReceivedOn: Date.now(),
            userId: userId ? userId : null
        }

        await log(digioLogModel);

        return { data: response, success: true };
    }
    catch (ex) {

        let digioLogModel = {
            request: model,
            response: ex.response && ex.response.data ? ex.response.data : null,
            apiCalled: "ocr",
            idNumber: model.id_no,
            success: false,
            requestSentOn: requestSentOn,
            responseReceivedOn: Date.now(),
            userId: userId ? userId : null
        }

        await log(digioLogModel);

        return { data: ex.response && ex.response.data ? ex.response.data : null, success: false };
    }
}

module.exports.ocr = ocr;

async function log(model) {

    let log = new DigioLog({
        request: JSON.stringify(model.request),
        response: JSON.stringify(model.response),
        apiCalled: model.apiCalled,
        idNumber: model.idNumber,
        success: model.success,
        requestSentOn: model.requestSentOn,
        responseReceivedOn: model.responseReceivedOn,
        userId: model.userId ? model.userId : null
    });

    let response = await log.save();

    return response;

}

module.exports.verifyClientBank = async function (userPartnerInfoId, accountNo, ifscCode) {

    const user = await UserPartnerInfo.findById(userPartnerInfoId);

    try {
        let model = {
            beneficiary_account_no: accountNo,
            beneficiary_ifsc: ifscCode
        }

        let response = await bankVerification(model, userId);

        if (response.success) {
            if (response.data.verified) {
                user.bankDetails[0].docVerification = {
                    isBankVerified: true,
                    id: response.data.id ? response.data.id : "",
                    verified: response.data.verified,
                    verified_at: response.data.verified_at,
                    beneficiary_name_with_bank: response.data.beneficiary_name_with_bank,
                    createdOn: Date.now()
                };

                user.bankDetails[0].errorDetails = {
                    details: response.data && response.data.error_message ? response.data.error_message : "",
                    code: "",
                    message: response.data && response.data.error_message ? response.data.error_message : "",
                };
            }
            else {
                user.bankDetails[0].docVerification = {
                    isBankVerified: false,
                    createdOn: Date.now()
                };

                user.bankDetails[0].errorDetails = {
                    details: response.data && response.data.id ? response.data.id : "",
                    code: "",
                    message: response.data && response.data.error_msg ? response.data.error_msg : "",
                };
            }
        }
        else {
            user.bankDetails[0].docVerification = {
                isBankVerified: false,
                createdOn: Date.now()
            };

            user.bankDetails[0].errorDetails = {
                details: response.data && response.data.details ? response.data.details : "",
                code: response.data && response.data.code ? response.data.code : "",
                message: response.data && response.data.message ? response.data.message : ""
            };
        }

        let saveData = await user.save();

        return saveData;
    }
    catch (ex) {
        // user.bankDetails[0].docVerification = {
        //     isBankVerified: false,
        //     createdOn: Date.now()
        // };

        // user.bankDetails[0].errorDetails = {
        //     details: ex.response && ex.response.data && ex.response && ex.response.data.details ? ex.response.data.details : "",
        //     code: ex.response && ex.response.data && ex.response && ex.response.data.code ? ex.response.data.code : "",
        //     message: ex.response && ex.response.data && ex.response && ex.response.data.message ? ex.response.data.message : ""
        // };

        // let saveData = await user.save();

        //return { data: "", success: false, message: "Error Occurred" };
        return user;
    }
}

module.exports.verifyPanData = async function (userId, panNo, fullName, dob) {

    const user = await UserInfo.findById(userId);

    let panDocVerification = {};

    try {
        let model = {
            pan_no: panNo,
            full_name: fullName,
            date_of_birth: dob
        }

        const response = await panVerification(model, userId);

        if (response.success) {

            panDocVerification = {
                isNumberVerified: response.data.is_pan_dob_valid ? response.data.is_pan_dob_valid : false,
                isDocVerified: user.panDocVerification.isDocVerified ? user.panDocVerification.isDocVerified : false,
                isDocMatch: false,
                type: "DATA_API",
                verificationResponse: {
                    is_pan_dob_valid: response.data.is_pan_dob_valid ? response.data.is_pan_dob_valid : false,
                    name_matched: response.data.name_matched ? response.data.name_matched : false,
                    verified: response.data.verified ? response.data.verified : false
                },
                createdOn: Date.now(),
                errorDetails: {
                    api: {
                        details: response.data && response.data.error_message ? response.data.error_message : "",
                        code: "",
                        message: response.data && response.data.error_message ? response.data.error_message : "",
                    }
                },
                details: user.panDocVerification && user.panDocVerification.details ? user.panDocVerification.details : null,
                ocr: user.panDocVerification && user.panDocVerification.ocr ? user.panDocVerification.ocr : null
            };

            if (user.panDocVerification && user.panDocVerification.ocr && user.panDocVerification.ocr.id_no)
                panDocVerification.isDocMatch = user.panDocVerification.ocr.id_no.toLowerCase() == panNo.toLowerCase() ? true : false;
        }
        else {
            panDocVerification = {
                isNumberVerified: false,
                isDocVerified: user.panDocVerification.isDocVerified ? user.panDocVerification.isDocVerified : false,
                isDocMatch: false,
                verificationResponse: {},
                createdOn: Date.now(),
                type: "DATA_API",
                errorDetails: {
                    api: {
                        details: response.data && response.data.details ? response.data.details : "",
                        code: response.data && response.data.code ? response.data.code : "",
                        message: response.data && response.data.message ? response.data.message : ""
                    }
                },
                details: user.panDocVerification && user.panDocVerification.details ? user.panDocVerification.details : null,
                ocr: user.panDocVerification && user.panDocVerification.ocr ? user.panDocVerification.ocr : null
            }
        }

        await UserInfo.updateOne({ _id: userId }, { $set: { panDocVerification: panDocVerification } });

        return {
            data: null,
            success: true,
            message: "Updated successfully"
        };
    }
    catch (ex) {
        saveErrorLog("verifyPanData", apiPath, { userId, panNo, fullName, dob }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);

        panDocVerification = {
            isNumberVerified: false,
            isDocVerified: user.panDocVerification.isDocVerified ? user.panDocVerification.isDocVerified : false,
            isDocMatch: false,
            verificationResponse: {},
            createdOn: Date.now(),
            type: "DATA_API",
            errorDetails: {
                api: {
                    details: ex.response && ex.response.data && ex.response && ex.response.data.details ? ex.response.data.details : "",
                    code: ex.response && ex.response.data && ex.response && ex.response.data.code ? ex.response.data.code : "",
                    message: ex.response && ex.response.data && ex.response && ex.response.data.message ? ex.response.data.message : ""
                }
            },
            details: user.panDocVerification && user.panDocVerification.details ? user.panDocVerification.details : null,
            ocr: user.panDocVerification && user.panDocVerification.ocr ? user.panDocVerification.ocr : null
        }

        await UserInfo.updateOne({ _id: userId }, { $set: { panDocVerification: panDocVerification } });

        return {
            data: null,
            success: false,
            message: ex.message ? ex.message : null
        };
    }
}

module.exports.verifyDlData = async function (userId, id_no, dob, defaultIndex = 0) {

    const user = await UserInfo.findById(userId);

    let docVerification = {};
    let errorDetails = {};

    try {
        let model = {
            id_no: id_no,
            dob: dob
        }

        const response = await dlVerification(model, userId);

        if (response.success) {

            docVerification = {
                isNumberVerified: true,
                isDocMatch: false,
                type: "DATA_API",
                verificationType: "DRIVING_LICENSE",
                dl: {
                    verificationResponse: {
                        verified: true,
                        Date_Of_Issue: response.data["Date Of Issue"] ? response.data["Date Of Issue"] : "",
                        NT: response.data["NT"] ? response.data["NT"] : "",
                        Old_New_DL_No: response.data["Old / New DL No."] ? response.data["Old / New DL No."] : "",
                        Holders_Name: response.data["Holder's Name"] ? response.data["Holder's Name"] : "",
                        Hazardous_Valid_Till: response.data["Hazardous Valid Till"] ? response.data["Hazardous Valid Till"] : "",
                        NonTransport: response.data["Non-Transport"] ? response.data["Non-Transport"] : "",
                        Transport: response.data["Transport"] ? response.data["Transport"] : "",
                        CurrentStatus: response.data["Current Status"] ? response.data["Current Status"] : "",
                        LastTransactionAt: response.data["Last Transaction At"] ? response.data["Last Transaction At"] : ""

                    },
                    ocr: user.poaDetails[defaultIndex].docVerification && user.poaDetails[defaultIndex].docVerification.dl && user.poaDetails[defaultIndex].docVerification.dl.ocr ? user.poaDetails[defaultIndex].docVerification.dl.ocr : null
                },
                createdOn: Date.now()
            }

            if (user.poaDetails[defaultIndex].docVerification && user.poaDetails[defaultIndex].docVerification.dl && user.poaDetails[defaultIndex].docVerification.dl.ocr && user.poaDetails[defaultIndex].docVerification.dl.ocr.id_no)
                docVerification.isDocMatch = user.poaDetails[defaultIndex].docVerification.dl.ocr.id_no.toLowerCase() == id_no.toLowerCase() ? true : false;

            errorDetails = {
                api: {
                    details: response.data && response.data.details ? response.data.details : "",
                    code: response.data && response.data.code ? response.data.code : "",
                    message: response.data && response.data.message ? response.data.message : ""
                }
            }

        }
        else {
            docVerification = {
                isNumberVerified: false,
                isDocMatch: false,
                type: "DATA_API"
            }

            errorDetails = {
                api: {
                    details: response.data && response.data.details ? response.data.details : "",
                    code: response.data && response.data.code ? response.data.code : "",
                    message: response.data && response.data.message ? response.data.message : ""
                }
            }
        }

        const updatePayload = {
            [`poaDetails.${defaultIndex}.docVerification`]: docVerification,
            [`poaDetails.${defaultIndex}.errorDetails`]: errorDetails
        }

        await UserInfo.updateOne({ _id: userId }, { $set: { ...updatePayload } });

        return {
            data: null,
            success: true,
            message: "Updated successfully"
        };

    }
    catch (ex) {
        saveErrorLog("verifyDlData", apiPath, { userId, id_no, dob, defaultIndex }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);

        // user.poaDetails[0].docVerification.isNumberVerified = false;
        // user.poaDetails[0].docVerification.isDocMatch = false;
        // user.poaDetails[0].docVerification.createdOn = Date.now();

        // user.poaDetails[0].docVerification.type = "DATA_API";

        // user.poaDetails[0].errorDetails.api = {
        //     details: ex.response && ex.response.data && ex.response && ex.response.data.details ? ex.response.data.details : "",
        //     code: ex.response && ex.response.data && ex.response && ex.response.data.code ? ex.response.data.code : "",
        //     message: ex.response && ex.response.data && ex.response && ex.response.data.message ? ex.response.data.message : ""
        // };

        // let saveData = await user.save();
        // return saveData;
        return {
            data: null,
            success: false,
            message: ex.message ? ex.message : null
        };
    }
}

module.exports.verifyVoterIdData = async function (userId, id_no, defaultIndex = 0) {

    const user = await UserInfo.findById(userId);

    let docVerification = {};
    let errorDetails = {};

    try {
        let model = {
            id_no: id_no
        }

        const response = await voterIdVerification(model, userId);

        if (response.success) {

            docVerification = {
                isNumberVerified: true,
                type: "DATA_API",
                verificationType: "VOTER_ID",
                isDocMatch: false,
                voterId: {
                    verificationResponse: {
                        verified: true,
                        pc_name: response.data.pc_name ? response.data.pc_name : "",
                        st_code: response.data.st_code ? response.data.st_code : "",
                        ps_lat_long_1_coordinate: response.data.ps_lat_long_1_coordinate ? response.data.ps_lat_long_1_coordinate : "",
                        gender: response.data.gender ? response.data.gender : "",
                        rln_name_v2: response.data.rln_name_v2 ? response.data.rln_name_v2 : "",
                        rln_name_v1: response.data.rln_name_v1 ? response.data.rln_name_v1 : "",
                        rln_name_v3: response.data.rln_name_v3 ? response.data.rln_name_v3 : "",
                        name_v1: response.data.name_v1 ? response.data.name_v1 : "",
                        epic_no: response.data.epic_no ? response.data.epic_no : "",
                        ac_name: response.data.ac_name ? response.data.ac_name : "",
                        name_v2: response.data.name_v2 ? response.data.name_v2 : "",
                        name_v3: response.data.name_v3 ? response.data.name_v3 : "",
                        ps_lat_long: response.data.pps_lat_longc_name ? response.data.pc_name : "",
                        pc_no: response.data.pc_no ? response.data.pc_no : "",
                        last_update: response.data.last_update ? response.data.last_update : "",
                        id: response.data.id ? response.data.id : "",
                        dist_no: response.data.dist_no ? response.data.dist_no : "",
                        ps_no: response.data.ps_no ? response.data.ps_no : "",
                        pc_name: response.data.pc_name ? response.data.pc_name : "",
                        ps_name_v1: response.data.ps_name_v1 ? response.data.ps_name_v1 : "",
                        st_name: response.data.st_name ? response.data.st_name : "",
                        dist_name: response.data.dist_name ? response.data.dist_name : "",
                        rln_type: response.data.rln_type ? response.data.rln_type : "",
                        pc_name_v1: response.data.pc_name_v1 ? response.data.pc_name_v1 : "",
                        part_name_v1: response.data.part_name_v1 ? response.data.part_name_v1 : "",
                        ac_name_v1: response.data.ac_name_v1 ? response.data.ac_name_v1 : "",
                        part_no: response.data.part_no ? response.data.part_no : "",
                        dist_name_v1: response.data.dist_name_v1 ? response.data.dist_name_v1 : "",
                        ps_lat_long_0_coordinate: response.data.ps_lat_long_0_coordinate ? response.data.ps_lat_long_0_coordinate : "",
                        _version_: response.data._version_ ? response.data._version_ : "",
                        name: response.data.name ? response.data.name : "",
                        section_no: response.data.section_no ? response.data.section_no : "",
                        ac_no: response.data.ac_no ? response.data.ac_no : "",
                        slno_inpart: response.data.slno_inpart ? response.data.slno_inpart : "",
                        rln_name: response.data.rln_name ? response.data.rln_name : "",
                        age: response.data.age ? response.data.age : "",
                        part_name: response.data.part_name ? response.data.part_name : "",
                        id_no: id_no ? id_no : ""
                    }
                },
                createdOn: Date.now()
            }

            if (user.poaDetails[defaultIndex].docVerification && user.poaDetails[defaultIndex].docVerification.voterId && user.poaDetails[defaultIndex].docVerification.voterId.ocr && user.poaDetails[defaultIndex].docVerification.voterId.ocr.id_no)
                docVerification.isDocMatch = user.poaDetails[defaultIndex].docVerification.voterId.ocr.id_no.toLowerCase() == id_no.toLowerCase() ? true : false;

            errorDetails = {
                api: {
                    details: response.data && response.data.details ? response.data.details : "",
                    code: response.data && response.data.code ? response.data.code : "",
                    message: response.data && response.data.message ? response.data.message : ""
                }
            }

        }
        else {
            docVerification = {
                isNumberVerified: false,
                isDocMatch: false,
                createdOn: Date.now(),
                type: "DATA_API"
            };

            errorDetails = {
                api: {
                    details: response.data && response.data.details ? response.data.details : "",
                    code: response.data && response.data.code ? response.data.code : "",
                    message: response.data && response.data.message ? response.data.message : ""
                }
            }

        }

        const updatePayload = {
            [`poaDetails.${defaultIndex}.docVerification`]: docVerification,
            [`poaDetails.${defaultIndex}.errorDetails`]: errorDetails
        }

        await UserInfo.updateOne({ _id: userId }, { $set: { ...updatePayload } });

        return {
            data: null,
            success: true,
            message: "Updated successfully"
        };
    }
    catch (ex) {
        saveErrorLog("verifyVoterIdData", apiPath, { userId, id_no, defaultIndex }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);

        // user.poaDetails[0].docVerification.isNumberVerified = false;
        // user.poaDetails[0].docVerification.isDocMatch = false;
        // user.poaDetails[0].docVerification.createdOn = Date.now();

        // user.poaDetails[0].docVerification.type = "DATA_API";

        // user.poaDetails[0].errorDetails.api = {
        //     details: ex.response && ex.response.data && ex.response && ex.response.data.details ? ex.response.data.details : "",
        //     code: ex.response && ex.response.data && ex.response && ex.response.data.code ? ex.response.data.code : "",
        //     message: ex.response && ex.response.data && ex.response && ex.response.data.message ? ex.response.data.message : ""
        // };

        // let saveData = await user.save();

        // return saveData;
        return {
            data: null,
            success: true,
            message: ex.message ? ex.message : null
        };
    }
}

module.exports.verifyGstData = async function (userId, gstin, defaultIndex = 0) {
    const user = await UserInfo.findById(userId);

    let docVerification = {};
    let errorDetails = {};

    try {
        let model = {
            gstin: gstin
        }

        const response = await gstVerification(model, userId);

        if (response.success) {

            if (response.data.error_message) {

                docVerification = {
                    isGstVerified: false,
                    createdOn: Date.now()
                };

                errorDetails = {
                    details: response.data && response.data.error_message ? response.data.error_message : "",
                    code: response.data && response.data.code ? response.data.code : "",
                    message: response.data && response.data.error_message ? response.data.error_message : ""
                };
            }
            else {
                docVerification = {
                    isGstVerified: true,
                    gstin: response.data.gstin ? response.data.gstin : "",
                    corporate_name: response.data.corporate_name ? response.data.corporate_name : "",
                    details: {
                        stj_cd: response.data.details && response.data.details.stj_cd ? response.data.details.stj_cd : "",
                        dty: response.data.details && response.data.details.dty ? response.data.details.dty : "",
                        lgnm: response.data.details && response.data.details.lgnm ? response.data.details.lgnm : "",
                        cxdt: response.data.details && response.data.details.cxdt ? response.data.details.cxdt : "",
                        gstin: response.data.details && response.data.details.gstin ? response.data.details.gstin : "",
                        nba: response.data.details && response.data.details.nba && response.data.details.nba[0] ? response.data.details.nba[0] : "",
                        lstupdt: response.data.details && response.data.details.lstupdt ? response.data.details.lstupdt : "",
                        ctb: response.data.details && response.data.details.ctb ? response.data.details.ctb : "",
                        rgdt: response.data.details && response.data.details.rgdt ? response.data.details.rgdt : "",
                    },
                    addr: {
                        bnm: response.data.details && response.data.details.pradr && response.data.details.pradr.addr && response.data.details.pradr.addr.bnm ? response.data.details.pradr.addr.bnm : "",
                        loc: response.data.details && response.data.details.pradr && response.data.details.pradr.addr && response.data.details.pradr.addr.loc ? response.data.details.pradr.addr.loc : "",
                        st: response.data.details && response.data.details.pradr && response.data.details.pradr.addr && response.data.details.pradr.addr.st ? response.data.details.pradr.addr.st : "",
                        bno: response.data.details && response.data.details.pradr && response.data.details.pradr.addr && response.data.details.pradr.addr.bno ? response.data.details.pradr.addr.bno : "",
                        dst: response.data.details && response.data.details.pradr && response.data.details.pradr.addr && response.data.details.pradr.addr.dst ? response.data.details.pradr.addr.dst : "",
                        stcd: response.data.details && response.data.details.pradr && response.data.details.pradr.addr && response.data.details.pradr.addr.stcd ? response.data.details.pradr.addr.stcd : "",
                        city: response.data.details && response.data.details.pradr && response.data.details.pradr.addr && response.data.details.pradr.addr.city ? response.data.details.pradr.addr.city : "",
                        flno: response.data.details && response.data.details.pradr && response.data.details.pradr.addr && response.data.details.pradr.addr.flno ? response.data.details.pradr.addr.flno : "",
                        lt: response.data.details && response.data.details.pradr && response.data.details.pradr.addr && response.data.details.pradr.addr.lt ? response.data.details.pradr.addr.lt : "",
                        pncd: response.data.details && response.data.details.pradr && response.data.details.pradr.addr && response.data.details.pradr.addr.pncd ? response.data.details.pradr.addr.pncd : "",
                        lg: response.data.details && response.data.details.pradr && response.data.details.pradr.addr && response.data.details.pradr.addr.lg ? response.data.details.pradr.addr.lg : "",
                    },
                    ntr: response.data.details && response.data.details.pradr && response.data.details.pradr.ntr ? response.data.details.pradr.ntr : "",
                    ctj_cd: response.data.details && response.data.details.ctj_cd ? response.data.details.ctj_cd : "",
                    trade_nam: response.data.details && response.data.details.trade_nam ? response.data.details.trade_nam : "",
                    sts: response.data.details && response.data.details.sts ? response.data.details.sts : "",
                    ctj: response.data.details && response.data.details.ctj ? response.data.details.ctj : "",
                    createdOn: Date.now()
                }

                errorDetails = {
                    details: "",
                    code: "",
                    message: ""
                };
            }
        }
        else {
            docVerification = {
                isGstVerified: false,
                createdOn: Date.now()
            };

            errorDetails = {
                details: response.data && response.data.details ? response.data.details : "",
                code: response.data && response.data.code ? response.data.code : "",
                message: response.data && response.data.message ? response.data.message : ""
            };
        }

        const updatePayload = {
            [`gstDetails.${defaultIndex}.docVerification`]: docVerification,
            [`gstDetails.${defaultIndex}.errorDetails`]: errorDetails
        }

        await UserInfo.updateOne({ _id: userId }, { $set: { ...updatePayload } });

        return {
            data: null,
            success: true,
            message: "Updated successfully"
        }
    }
    catch (ex) {
        saveErrorLog("verifyGstData", apiPath, { userId, gstin, defaultIndex }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        // user.gstDetails[0].docVerification = {
        //     isGstVerified: false,
        //     createdOn: Date.now()
        // };

        // user.gstDetails[0].errorDetails = {
        //     details: ex.response && ex.response.data && ex.response && ex.response.data.details ? ex.response.data.details : "",
        //     code: ex.response && ex.response.data && ex.response && ex.response.data.code ? ex.response.data.code : "",
        //     message: ex.response && ex.response.data && ex.response && ex.response.data.message ? ex.response.data.message : ""
        // };

        // let saveData = await user.save();

        // return saveData;
        return {
            data: null,
            success: false,
            message: ex.message
        }
    }

}

module.exports.ocrVerification = async function (userId, front_part, back_part, should_verify, idNumber, type, defaultIndex = 0) {
    let user = await UserInfo.findById(userId);

    try {

        const staticPath = "ProofImages/" + userId;

        // if (!fs.existsSync(proofImagePath))
        //     fs.mkdirSync(proofImagePath);

        let uniqueId = getOcrId(userId);

        front_part = front_part.replace(/^data:.+;base64,/, "");
        back_part = back_part.replace(/^data:.+;base64,/, "");

        let model = {};

        let docVerification = {};
        let errorDetails = {};
        let frontDownload = false;
        let backDownload = false;

        let frontFileName = "";
        let frontFilePath = "";
        let backFileName = "";
        let backFilePath = "";

        if (!back_part)

            model = {
                front_part: front_part,
                should_verify: should_verify,
                unique_request_id: uniqueId.data
            }
        else
            model = {
                front_part: front_part,
                back_part: back_part,
                should_verify: should_verify,
                unique_request_id: uniqueId.data
            }

        const response = await ocr(model, userId);

        if (response.success) {

            if (response.data.id_type == "PAN" && type == "PAN") {

                let photoPath = `${staticPath}/PanPhoto`

                let signaturePath = `${staticPath}/PanSignature`

                let staticPhotoPath = photoPath + "/photo.jpg";
                let staticSignaturePath = signaturePath + "/signature.jpg";

                if (response.data.encoded_image)
                    await AWS.uploadToS3(staticPhotoPath, response.data.encoded_image)
                else
                    staticPhotoPath = "";

                if (response.data.encoded_signature)
                    await AWS.uploadToS3(staticSignaturePath, response.data.encoded_signature)
                else
                    staticSignaturePath = ""

                let isDocMatch = false;
                let isNumberVerified = false;

                if (response.data.id_no) {
                    if (idNumber.trim().toLowerCase() == response.data.id_no.trim().toLowerCase()) isDocMatch = true;
                }

                if (response.data.pan_verification_response && response.data.pan_verification_response.is_pan_dob_valid && response.data.id_no)
                    if (idNumber.trim().toLowerCase() == response.data.id_no.trim().toLowerCase()) isNumberVerified = true;

                const panDocVerification = {
                    type: "Ocr",
                    isDocVerified: response.data.pan_verification_response && response.data.pan_verification_response.is_pan_dob_valid ? response.data.pan_verification_response.is_pan_dob_valid : false,
                    isNumberVerified: isNumberVerified,
                    isDocMatch: isDocMatch,
                    createdOn: Date.now(),
                    details: {
                        name: response.data.name ? response.data.name : "",
                        docNo: response.data.id_no ? response.data.id_no : "",
                        dob: response.data.dob ? response.data.dob : "",
                        document_type: "PAN",
                        id_proof_type: "ID_PROOF",
                        photoPath: staticPhotoPath,
                        fathers_name: response.data.fathers_name ? response.data.fathers_name : ""
                    },
                    ocr: {
                        name: response.data.name ? response.data.name : "",
                        fathers_name: response.data.fathers_name ? response.data.fathers_name : "",
                        id_no: response.data.id_no ? response.data.id_no : "",
                        dob: response.data.dob ? response.data.dob : "",
                        id_type: response.data.id_type ? response.data.id_type : "",
                        signaturePath: staticSignaturePath,
                        photoPath: staticPhotoPath
                    },
                    verificationResponse: {
                        is_pan_dob_valid: response.data.pan_verification_response && response.data.pan_verification_response.is_pan_dob_valid ? response.data.pan_verification_response.is_pan_dob_valid : false,
                        name_matched: response.data.pan_verification_response && response.data.pan_verification_response.name_matched ? response.data.pan_verification_response.name_matched : false,
                        verified: response.data.id_card_verification_response && response.data.id_card_verification_response.verified ? response.data.id_card_verification_response.verified : false
                    },
                    errorDetails: {
                        ocr: {
                            details: response.data && response.data.pan_verification_response && response.data.pan_verification_response.error_message ? response.data.pan_verification_response.error_message : "",
                            code: "",
                            message: response.data && response.data.pan_verification_response && response.data.pan_verification_response.error_message ? response.data.pan_verification_response.error_message : "",
                        }
                    }
                };

                await UserInfo.updateOne({ _id: userId }, { $set: { panDocVerification: panDocVerification } });
            }
            else {
                let photoPath = `${staticPath}/PoaPhoto`

                let signaturePath = `${staticPath}/PoaSignature`

                let staticPhotoPath = photoPath + "/photo.jpg";
                let staticSignaturePath = signaturePath + "/signature.jpg";

                if (response.data.encoded_image) await AWS.uploadToS3(staticPhotoPath, response.data.encoded_image)
                else staticPhotoPath = "";


                if (response.data.encoded_signature) await AWS.uploadToS3(staticSignaturePath, response.data.encoded_signature)
                else staticSignaturePath = ""

                if (response.data.id_type == "DRIVING_LICENSE" && type == "POA") {

                    let isDocMatch = false;
                    let isNumberVerified = false;

                    if (response.data.id_no) {
                        if (idNumber.trim().toLowerCase() == response.data.id_no.trim().toLowerCase()) isDocMatch = true;
                    }

                    if (response.data.id_card_verification_response && response.data.id_card_verification_response.verified && response.data.id_no) {
                        if (idNumber.trim().toLowerCase() == response.data.id_no.trim().toLowerCase()) isNumberVerified = true;
                    }

                    docVerification = {
                        type: "Ocr",
                        verificationType: response.data.id_type ? response.data.id_type : "",
                        isDocMatch: isDocMatch,
                        isDocVerified: response.data.id_card_verification_response && response.data.id_card_verification_response.verified ? response.data.id_card_verification_response.verified : false,
                        isNumberVerified: isNumberVerified,
                        createdOn: Date.now(),
                        dl: {
                            ocr: {
                                name: response.data.name ? response.data.name : "",
                                id_no: response.data.id_no ? response.data.id_no : "",
                                dob: response.data.dob ? response.data.dob : "",
                                id_type: response.data.id_type ? response.data.id_type : "",
                                photoPath: staticPhotoPath
                            },
                            verificationResponse: {
                                verified: response.data.id_card_verification_response && response.data.id_card_verification_response.verified ? response.data.id_card_verification_response.verified : false,
                                Date_Of_Issue: response.data.id_card_verification_response && response.data.id_card_verification_response.id_data && response.data.id_card_verification_response.id_data["Date Of Issue"] ? response.data.id_card_verification_response.id_data["Date Of Issue"] : "",
                                NT: response.data.id_card_verification_response && response.data.id_card_verification_response.id_data && response.data.id_card_verification_response.id_data["NT"] ? response.data.id_card_verification_response.id_data["NT"] : "",
                                Old_New_DL_No: response.data.id_card_verification_response && response.data.id_card_verification_response.id_data && response.data.id_card_verification_response.id_data["Old / New DL No."] ? response.data.id_card_verification_response.id_data["Old / New DL No."] : "",
                                Holders_Name: response.data.id_card_verification_response && response.data.id_card_verification_response.id_data && response.data.id_card_verification_response.id_data["Holder's Name"] ? response.data.id_card_verification_response.id_data["Holder's Name"] : "",
                                Hazardous_Valid_Till: response.data.id_card_verification_response && response.data.id_card_verification_response.id_data && response.data.id_card_verification_response.id_data["Hazardous Valid Till"] ? response.data.id_card_verification_response.id_data["Hazardous Valid Till"] : "",
                                NonTransport: response.data.id_card_verification_response && response.data.id_card_verification_response.id_data && response.data.id_card_verification_response.id_data["Non-Transport"] ? response.data.id_card_verification_response.id_data["Non-Transport"] : "",
                                Transport: response.data.id_card_verification_response && response.data.id_card_verification_response.id_data && response.data.id_card_verification_response.id_data["Transport"] ? response.data.id_card_verification_response.id_data["Transport"] : "",
                                CurrentStatus: response.data.id_card_verification_response && response.data.id_card_verification_response.id_data && response.data.id_card_verification_response.id_data["Current Status"] ? response.data.id_card_verification_response.id_data["Current Status"] : "",
                                LastTransactionAt: response.data.id_card_verification_response && response.data.id_card_verification_response.id_data && response.data.id_card_verification_response.id_data["Last Transaction At"] ? response.data.id_card_verification_response.id_data["Last Transaction At"] : ""
                            }
                        }
                    };

                    errorDetails = {
                        ocr: {
                            details: response.data && response.data.id_card_verification_response && response.data.id_card_verification_response.error_message ? response.data.id_card_verification_response.error_message : "",
                            code: "",
                            message: response.data && response.data.id_card_verification_response && response.data.id_card_verification_response.error_message ? response.data.id_card_verification_response.error_message : ""
                        }
                    };

                    const updatePayload = {
                        [`poaDetails.${defaultIndex}.docVerification`]: docVerification,
                        [`poaDetails.${defaultIndex}.errorDetails`]: errorDetails,
                        [`poaDetails.${defaultIndex}.poaVerificationDocType`]: "Driving Licence",
                        [`poaDetails.${defaultIndex}.poaType`]: "Driving Licence"
                    }

                    await UserInfo.updateOne({ _id: userId }, { $set: { ...updatePayload } });

                }
                else if (response.data.id_type == "PASSPORT" && type == "POA") {

                    let isDocMatch = false;

                    if (response.data.id_no) {
                        if (idNumber.trim().toLowerCase() == response.data.id_no.trim().toLowerCase()) isDocMatch = true;
                    }

                    docVerification = {
                        type: "Ocr",
                        verificationType: response.data.id_type,
                        isDocMatch: isDocMatch,
                        isNumberVerified: false,
                        isDocVerified: false,
                        createdOn: Date.now(),
                        passport: {
                            ocr: {
                                name: response.data.name ? response.data.name : "",
                                id_no: response.data.id_no ? response.data.id_no : "",
                                dob: response.data.dob ? response.data.dob : "",
                                id_type: response.data.id_type ? response.data.id_type : "",
                                gender: response.data.gender ? response.data.gender : "",
                                country_code: response.data.country_code ? response.data.country_code : "",
                                sur_name: response.data.sur_name ? response.data.sur_name : "",
                                nationality: response.data.nationality ? response.data.nationality : "",
                                place_of_birth: response.data.place_of_birth ? response.data.place_of_birth : "",
                                place_of_issue: response.data.place_of_issue ? response.data.place_of_issue : "",
                                photoPath: staticPhotoPath
                            }
                        }
                    }

                    errorDetails = {
                        ocr: {
                            details: "Passport Verification not available",
                            code: "",
                            message: "Passport Verification not available"
                        }
                    };

                    const updatePayload = {
                        [`poaDetails.${defaultIndex}.docVerification`]: docVerification,
                        [`poaDetails.${defaultIndex}.errorDetails`]: errorDetails,
                        [`poaDetails.${defaultIndex}.poaVerificationDocType`]: "Passport",
                        [`poaDetails.${defaultIndex}.poaType`]: "Others"
                    }

                    await UserInfo.updateOne({ _id: userId }, { $set: { ...updatePayload } });

                }
                else if (response.data.id_type == "AADHAAR" && type == "POA") {

                    let isDocMatch = false;

                    if (response.data.id_no)
                        if (idNumber.trim().toLowerCase() == response.data.id_no.trim().toLowerCase()) isDocMatch = true;

                    docVerification = {
                        type: "Ocr",
                        verificationType: response.data.id_type,
                        isDocMatch: isDocMatch,
                        isNumberVerified: false,
                        isDocVerified: false,
                        createdOn: Date.now(),
                        uid: {
                            details: {
                                docNo: response.data.id_no ? maskNumber(response.data.id_no, 4) : "",
                                document_type: "aadhaar",
                                id_proof_type: "ID_AND_ADDRESS_PROOF",
                                gender: response.data.gender ? response.data.gender : "",
                                name: response.data.name ? response.data.name : "",
                                dob: response.data.dob ? response.data.dob : "",
                                current_address: response.data.address ? response.data.address : "",
                                current_address_details: {
                                    address: response.data.address_information && response.data.address_information.address ? response.data.address_information.address : "",
                                    locality_or_post_office: response.data.address_information && response.data.address_information.locality_or_post_office ? response.data.address_information.locality_or_post_office : "",
                                    district_or_city: response.data.address_information && response.data.address_information.district_or_city ? response.data.address_information.district_or_city : "",
                                    state: response.data.address_information && response.data.address_information.state ? response.data.address_information.state : "",
                                    pincode: response.data.address_information && response.data.address_information.pincode ? response.data.address_information.pincode : "",
                                },
                                photoPath: staticPhotoPath,
                            },
                            ocr: {
                                name: response.data.name ? response.data.name : "",
                                id_no: response.data.id_no ? maskNumber(response.data.id_no, 4) : "",
                                dob: response.data.dob ? response.data.dob : "",
                                id_type: response.data.id_type ? response.data.id_type : "",
                                gender: response.data.gender ? response.data.gender : "",
                                address: response.data.address ? response.data.address : "",
                                photoPath: staticPhotoPath,
                                address_information: {
                                    address: response.data.address_information && response.data.address_information.address ? response.data.address_information.address : "",
                                    locality_or_post_office: response.data.address_information && response.data.address_information.locality_or_post_office ? response.data.address_information.locality_or_post_office : "",
                                    district_or_city: response.data.address_information && response.data.address_information.district_or_city ? response.data.address_information.district_or_city : "",
                                    state: response.data.address_information && response.data.address_information.state ? response.data.address_information.state : "",
                                    pincode: response.data.address_information && response.data.address_information.pincode ? response.data.address_information.pincode : "",
                                }

                            }
                        }
                    }

                    errorDetails = {
                        ocr: {
                            details: "Aadhaar Verification not available",
                            code: response.data && response.data.code ? response.data.code : "",
                            message: "Aadhaar Verification not available"
                        }
                    };

                    const updatePayload = {
                        [`poaDetails.${defaultIndex}.docVerification`]: docVerification,
                        [`poaDetails.${defaultIndex}.errorDetails`]: errorDetails,
                        [`poaDetails.${defaultIndex}.poaVerificationDocType`]: "Aadhaar Card",
                        [`poaDetails.${defaultIndex}.poaType`]: "Aadhaar Card"
                    }

                    await UserInfo.updateOne({ _id: userId }, { $set: { ...updatePayload } });
                }
                else if (response.data.id_type == "VOTER_ID" && type == "POA") {

                    let isDocMatch = false;
                    let isNumberVerified = false;

                    if (response.data.id_no) {
                        if (idNumber.trim().toLowerCase() == response.data.id_no.trim().toLowerCase()) isDocMatch = true;
                    }

                    if (response.data.id_card_verification_response.verified && response.data.id_no) {
                        if (idNumber.trim().toLowerCase() == response.data.id_no.trim().toLowerCase()) isNumberVerified = true;
                    }

                    docVerification = {
                        type: "Ocr",
                        verificationType: response.data.id_type,
                        isDocMatch: isDocMatch,
                        isDocVerified: response.data.id_card_verification_response.verified ? response.data.id_card_verification_response.verified : false,
                        isNumberVerified: isNumberVerified,
                        createdOn: Date.now(),
                        voterId: {
                            ocr: {
                                name: response.data.name ? response.data.name : "",
                                id_no: response.data.id_no ? response.data.id_no : "",
                                fathers_name: response.data.fathers_name ? response.data.fathers_name : "",
                                id_type: response.data.id_type ? response.data.id_type : "",
                                gender: response.data.gender ? response.data.gender : "",
                                address: response.data.address ? response.data.address : "",
                                date_of_issue: response.data.date_of_issue ? response.data.date_of_issue : "",
                                photoPath: staticPhotoPath,
                                address_information: {
                                    address: response.data.address_information && response.data.address_information.address ? response.data.address_information.address : "",
                                    locality_or_post_office: response.data.address_information && response.data.address_information.locality_or_post_office ? response.data.address_information.locality_or_post_office : "",
                                    district_or_city: response.data.address_information && response.data.address_information.district_or_city ? response.data.address_information.district_or_city : ""
                                }
                            },
                            verificationResponse: {
                                verified: response.data.id_card_verification_response && response.data.id_card_verification_response.verified ? response.data.id_card_verification_response.verified : false,
                                result_id_no: response.data.id_card_verification_response && response.data.id_card_verification_response.result && response.data.id_card_verification_response.result.id_no ? response.data.id_card_verification_response.result.id_no : false,
                                result_name: response.data.id_card_verification_response && response.data.id_card_verification_response.result && response.data.id_card_verification_response.result.name ? response.data.id_card_verification_response.result.name : false,
                                pc_name: response.data.id_card_verification_response && response.data.id_card_verification_response.id_data && response.data.id_card_verification_response.id_data.pc_name ? response.data.id_card_verification_response.id_data.pc_name : "",
                                st_code: response.data.id_card_verification_response && response.data.id_card_verification_response.id_data && response.data.id_card_verification_response.id_data.st_code ? response.data.id_card_verification_response.id_data.st_code : "",
                                ps_lat_long_1_coordinate: response.data.id_card_verification_response && response.data.id_card_verification_response.id_data && response.data.id_card_verification_response.id_data.ps_lat_long_1_coordinate ? response.data.id_card_verification_response.id_data.ps_lat_long_1_coordinate : "",
                                gender: response.data.id_card_verification_response && response.data.id_card_verification_response.id_data && response.data.id_card_verification_response.id_data.gender ? response.data.id_card_verification_response.id_data.gender : "",
                                rln_name_v2: response.data.id_card_verification_response && response.data.id_card_verification_response.id_data && response.data.id_card_verification_response.id_data.rln_name_v2 ? response.data.id_card_verification_response.id_data.rln_name_v2 : "",
                                rln_name_v1: response.data.id_card_verification_response && response.data.id_card_verification_response.id_data && response.data.id_card_verification_response.id_data.rln_name_v1 ? response.data.id_card_verification_response.id_data.rln_name_v1 : "",
                                rln_name_v3: response.data.id_card_verification_response && response.data.id_card_verification_response.id_data && response.data.id_card_verification_response.id_data.rln_name_v3 ? response.data.id_card_verification_response.id_data.rln_name_v3 : "",
                                name_v1: response.data.id_card_verification_response && response.data.id_card_verification_response.id_data && response.data.id_card_verification_response.id_data.name_v1 ? response.data.id_card_verification_response.id_data.name_v1 : "",
                                epic_no: response.data.id_card_verification_response && response.data.id_card_verification_response.id_data && response.data.id_card_verification_response.id_data.epic_no ? response.data.id_card_verification_response.id_data.epic_no : "",
                                ac_name: response.data.id_card_verification_response && response.data.id_card_verification_response.id_data && response.data.id_card_verification_response.id_data.ac_name ? response.data.id_card_verification_response.id_data.ac_name : "",
                                name_v2: response.data.id_card_verification_response && response.data.id_card_verification_response.id_data && response.data.id_card_verification_response.id_data.name_v2 ? response.data.id_card_verification_response.id_data.name_v2 : "",
                                name_v3: response.data.id_card_verification_response && response.data.id_card_verification_response.id_data && response.data.id_card_verification_response.id_data.name_v3 ? response.data.id_card_verification_response.id_data.name_v3 : "",
                                ps_lat_long: response.data.id_card_verification_response && response.data.id_card_verification_response.id_data && response.data.id_card_verification_response.id_data.ps_lat_long ? response.data.id_card_verification_response.id_data.ps_lat_long : "",
                                pc_no: response.data.id_card_verification_response && response.data.id_card_verification_response.id_data && response.data.id_card_verification_response.id_data.pc_no ? response.data.id_card_verification_response.id_data.pc_no : "",
                                last_update: response.data.id_card_verification_response && response.data.id_card_verification_response.id_data && response.data.id_card_verification_response.id_data.last_update ? response.data.id_card_verification_response.id_data.last_update : "",
                                id: response.data.id_card_verification_response && response.data.id_card_verification_response.id_data && response.data.id_card_verification_response.id_data.id ? response.data.id_card_verification_response.id_data.id : "",
                                dist_no: response.data.id_card_verification_response && response.data.id_card_verification_response.id_data && response.data.id_card_verification_response.id_data.dist_no ? response.data.id_card_verification_response.id_data.dist_no : "",
                                ps_no: response.data.id_card_verification_response && response.data.id_card_verification_response.id_data && response.data.id_card_verification_response.id_data.ps_no ? response.data.id_card_verification_response.id_data.ps_no : "",
                                pc_name: response.data.id_card_verification_response && response.data.id_card_verification_response.id_data && response.data.id_card_verification_response.id_data.pc_name ? response.data.id_card_verification_response.id_data.pc_name : "",
                                ps_name_v1: response.data.id_card_verification_response && response.data.id_card_verification_response.id_data && response.data.id_card_verification_response.id_data.ps_name_v1 ? response.data.id_card_verification_response.id_data.ps_name_v1 : "",
                                st_name: response.data.id_card_verification_response && response.data.id_card_verification_response.id_data && response.data.id_card_verification_response.id_data.st_name ? response.data.id_card_verification_response.id_data.st_name : "",
                                dist_name: response.data.id_card_verification_response && response.data.id_card_verification_response.id_data && response.data.id_card_verification_response.id_data.dist_name ? response.data.id_card_verification_response.id_data.dist_name : "",
                                rln_type: response.data.id_card_verification_response && response.data.id_card_verification_response.id_data && response.data.id_card_verification_response.id_data.rln_type ? response.data.id_card_verification_response.id_data.rln_type : "",
                                pc_name_v1: response.data.id_card_verification_response && response.data.id_card_verification_response.id_data && response.data.id_card_verification_response.id_data.pc_name_v1 ? response.data.id_card_verification_response.id_data.pc_name_v1 : "",
                                part_name_v1: response.data.id_card_verification_response && response.data.id_card_verification_response.id_data && response.data.id_card_verification_response.id_data.part_name_v1 ? response.data.id_card_verification_response.id_data.part_name_v1 : "",
                                ac_name_v1: response.data.id_card_verification_response && response.data.id_card_verification_response.id_data && response.data.id_card_verification_response.id_data.ac_name_v1 ? response.data.id_card_verification_response.id_data.ac_name_v1 : "",
                                part_no: response.data.id_card_verification_response && response.data.id_card_verification_response.id_data && response.data.id_card_verification_response.id_data.part_no ? response.data.id_card_verification_response.id_data.part_no : "",
                                dist_name_v1: response.data.id_card_verification_response && response.data.id_card_verification_response.id_data && response.data.id_card_verification_response.id_data.dist_name_v1 ? response.data.id_card_verification_response.id_data.dist_name_v1 : "",
                                ps_lat_long_0_coordinate: response.data.id_card_verification_response && response.data.id_card_verification_response.id_data && response.data.id_card_verification_response.id_data.ps_lat_long_0_coordinate ? response.data.id_card_verification_response.id_data.ps_lat_long_0_coordinate : "",
                                _version_: response.data.id_card_verification_response && response.data.id_card_verification_response.id_data && response.data.id_card_verification_response.id_data._version_ ? response.data.id_card_verification_response.id_data._version_ : "",
                                name: response.data.id_card_verification_response && response.data.id_card_verification_response.id_data && response.data.id_card_verification_response.id_data.name ? response.data.id_card_verification_response.id_data.name : "",
                                section_no: response.data.id_card_verification_response && response.data.id_card_verification_response.id_data && response.data.id_card_verification_response.id_data.section_no ? response.data.id_card_verification_response.id_data.section_no : "",
                                ac_no: response.data.id_card_verification_response && response.data.id_card_verification_response.id_data && response.data.id_card_verification_response.id_data.ac_no ? response.data.id_card_verification_response.id_data.ac_no : "",
                                slno_inpart: response.data.id_card_verification_response && response.data.id_card_verification_response.id_data && response.data.id_card_verification_response.id_data.slno_inpart ? response.data.id_card_verification_response.id_data.slno_inpart : "",
                                rln_name: response.data.id_card_verification_response && response.data.id_card_verification_response.id_data && response.data.id_card_verification_response.id_data.rln_name ? response.data.id_card_verification_response.id_data.rln_name : "",
                                age: response.data.id_card_verification_response && response.data.id_card_verification_response.id_data && response.data.id_card_verification_response.id_data.age ? response.data.id_card_verification_response.id_data.age : "",
                                part_name: response.data.id_card_verification_response && response.data.id_card_verification_response.id_data && response.data.id_card_verification_response.id_data.part_name ? response.data.id_card_verification_response.id_data.part_name : "",
                                id_no: response.data.id_card_verification_response && response.data.id_card_verification_response.id_data && response.data.id_card_verification_response.id_data.id_no ? response.data.id_card_verification_response.id_data.id_no : "",
                            }
                        }
                    }

                    errorDetails = {
                        ocr: {
                            details: response.data && response.data.id_card_verification_response && response.data.id_card_verification_response.error_message ? response.data.id_card_verification_response.error_message : "",
                            code: "",
                            message: response.data && response.data.id_card_verification_response && response.data.id_card_verification_response.error_message ? response.data.id_card_verification_response.error_message : ""
                        }
                    };

                    const updatePayload = {
                        [`poaDetails.${defaultIndex}.docVerification`]: docVerification,
                        [`poaDetails.${defaultIndex}.errorDetails`]: errorDetails,
                        [`poaDetails.${defaultIndex}.poaVerificationDocType`]: "Voter Id",
                        [`poaDetails.${defaultIndex}.poaType`]: "Voter Id"
                    }

                    await UserInfo.updateOne({ _id: userId }, { $set: { ...updatePayload } });
                }
                else {
                    if (type == "PAN") {

                        const panDocVerification = {
                            isDocMatch: false,
                            isNumberVerified: false,
                            isDocVerified: false,
                            type: "Ocr",
                            ocr: {},
                            verificationResponse: {},
                            createdOn: Date.now(),
                            errorDetails: {
                                ocr: {
                                    details: response.data && response.data.details ? response.data.details : "Not a valid  PAN Image",
                                    code: response.data && response.data.code ? response.data.code : "",
                                    message: response.data && response.data.message ? response.data.message : "Not a valid  PAN Image"
                                }
                            }
                        }

                        await UserInfo.updateOne({ _id: userId }, { $set: { panDocVerification: panDocVerification } });
                    }

                    if (type == "POA") {
                        docVerification = {
                            isDocMatch: false,
                            isDocVerified: false,
                            isNumberVerified: false,
                            ocr: {},
                            verificationResponse: {},
                            createdOn: Date.now(),
                            verificationType: "",
                            type: "Ocr",
                        }

                        errorDetails = {
                            ocr: {
                                details: response.data && response.data.details ? response.data.details : "Not a valid POA Image",
                                code: response.data && response.data.code ? response.data.code : "",
                                message: response.data && response.data.message ? response.data.message : "Not a valid POA Image"
                            },
                        };

                        const updatePayload = {
                            [`poaDetails.${defaultIndex}.docVerification`]: docVerification,
                            [`poaDetails.${defaultIndex}.errorDetails`]: errorDetails
                        }

                        await UserInfo.updateOne({ _id: userId }, { $set: { ...updatePayload } });
                    }
                }
            }
        }
        else {
            if (type == "PAN") {
                const panDocVerification = {
                    isDocMatch: false,
                    isNumberVerified: false,
                    isDocVerified: false,
                    ocr: {},
                    verificationResponse: {},
                    createdOn: Date.now(),
                    type: "Ocr",
                    errorDetails: {
                        ocr: {
                            details: response.data && response.data.details ? response.data.details : "Not a valid PAN Image",
                            code: response.data && response.data.code ? response.data.code : "",
                            message: response.data && response.data.message ? response.data.message : "Not a valid PAN Image"
                        }
                    }
                }

                await UserInfo.updateOne({ _id: userId }, { $set: { panDocVerification: panDocVerification } });
            }

            if (type == "POA") {
                docVerification = {
                    type: "Ocr",
                    verificationType: "",
                    isDocMatch: false,
                    isNumberVerified: false,
                    isDocVerified: false,
                    createdOn: Date.now(),
                    verificationResponse: {},
                    ocr: {}
                }

                errorDetails = {
                    ocr: {
                        details: response.data && response.data.details ? response.data.details : "Not a valid POA Image",
                        code: response.data && response.data.code ? response.data.code : "",
                        message: response.data && response.data.message ? response.data.message : "Not a valid POA Image"
                    }
                };

                const updatePayload = {
                    [`poaDetails.${defaultIndex}.docVerification`]: docVerification,
                    [`poaDetails.${defaultIndex}.errorDetails`]: errorDetails
                }

                await UserInfo.updateOne({ _id: userId }, { $set: { ...updatePayload } });
            }

        }

        let isMasked = false;
        //If Aadhaar, Mask
        if (type === "POA") {
            //Mask front image of Aadhaar
            user = await UserInfo.findById(userId);

            const poaType = user.poaDetails[defaultIndex].poaType ? user.poaDetails[defaultIndex].poaType : "";

            if (poaType === "Aadhaar Card") {
                if (front_part) {
                    const fileName = user.poaDetails[defaultIndex].docDetails && user.poaDetails[defaultIndex].docDetails[0] && user.poaDetails[defaultIndex].docDetails[0].fileName ? user.poaDetails[defaultIndex].docDetails[0].fileName : "";
                    const filePath = user.poaDetails[defaultIndex].docDetails && user.poaDetails[defaultIndex].docDetails[0] && user.poaDetails[defaultIndex].docDetails[0].filePath ? user.poaDetails[defaultIndex].docDetails[0].filePath : "";
                    if (fileName && filePath) {
                        const ext = getExtension(fileName);
                        const mimeType = getMimeType(ext);
                        if (mimeType) {
                            const maskAadhaar = await maskAadhaarUtil({ image: front_part, mimeType: mimeType });

                            if (maskAadhaar.success) {
                                const poaFileName = `poaFront_${getCurrentTimeStamp()}${getExtension(maskAadhaar.maskedUrl)}`;
                                const newFilePath = filePath.replace("wom/", "").replace(fileName, poaFileName);
                                const download = await downloadImageBuffer(maskAadhaar.maskedUrl);

                                if (download.success) {

                                    await AWS.uploadToS3(newFilePath, download.data, "BUFFER")

                                    frontFilePath = newFilePath;
                                    frontFileName = poaFileName;
                                    isMasked = true;
                                    await AWS.deleteFile(filePath);
                                    frontDownload = true;
                                }
                            }
                        }
                    }
                }

                //Mask back image of Aadhaar
                if (back_part) {
                    let fileName = user.poaDetails[defaultIndex].docDetails && user.poaDetails[defaultIndex].docDetails[1] && user.poaDetails[defaultIndex].docDetails[1].fileName ? user.poaDetails[defaultIndex].docDetails[1].fileName : "";
                    let filePath = user.poaDetails[defaultIndex].docDetails && user.poaDetails[defaultIndex].docDetails[1] && user.poaDetails[defaultIndex].docDetails[1].filePath ? user.poaDetails[defaultIndex].docDetails[1].filePath : "";
                    if (fileName && filePath) {
                        const ext = getExtension(fileName);
                        const mimeType = getMimeType(ext);
                        if (mimeType) {
                            const maskAadhaar = await maskAadhaarUtil({ image: back_part, mimeType: mimeType });

                            if (maskAadhaar.success) {
                                const poaFileName = `poaBack_${getCurrentTimeStamp()}${getExtension(maskAadhaar.maskedUrl)}`;
                                const newFilePath = filePath.replace("wom/", "").replace(fileName, poaFileName);
                                const download = await downloadImageBuffer(maskAadhaar.maskedUrl);
                                //to do test case
                                if (download.success) {
                                    await AWS.uploadToS3(newFilePath, download.data, "BUFFER")
                                    backFilePath = newFilePath;
                                    backFileName = poaFileName;
                                    isMasked = true;
                                    await AWS.deleteFile(filePath);
                                    backDownload = true;
                                }
                            }
                        }
                    }
                }

                let updatePayload = {};

                if (frontDownload)
                    updatePayload = {
                        [`poaDetails.${defaultIndex}.poaNo`]: maskNumber(user.poaDetails[defaultIndex].poaNo, 4),
                        [`poaDetails.${defaultIndex}.isMasked`]: isMasked ? true : false,
                        [`poaDetails.${defaultIndex}.docDetails.0.filePath`]: frontFilePath,
                        [`poaDetails.${defaultIndex}.docDetails.0.fileName`]: frontFileName
                    }
                else
                    updatePayload = {
                        [`poaDetails.${defaultIndex}.poaNo`]: maskNumber(user.poaDetails[defaultIndex].poaNo, 4),
                        [`poaDetails.${defaultIndex}.isMasked`]: isMasked ? true : false
                    }

                await UserInfo.updateOne({ _id: userId }, { $set: { ...updatePayload } });

                if (backDownload) {
                    updatePayload = {
                        [`poaDetails.${defaultIndex}.docDetails.1.filePath`]: backFilePath,
                        [`poaDetails.${defaultIndex}.docDetails.1.fileName`]: backFileName
                    }

                    await UserInfo.updateOne({ _id: userId }, { $set: { ...updatePayload } });
                }

            }
        }

        return {
            data: null,
            success: true,
            message: "Updated successfully"
        }

    }
    catch (ex) {
        console.log("error", ex);
        console.log("error", ex.message);

        saveErrorLog("ocrVerification", apiPath, { userId, front_part, back_part, should_verify, idNumber, type }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);

        return {
            data: null,
            success: false,
            message: ex.message ? ex.message : ""
        }
    }
}

async function createDirectoryAndSaveFile(path, imageStream, pathWithFileName) {
    if (!fs.existsSync(path)) fs.mkdirSync(path);

    // let data = imageStream.replace(/^data:image\/\w+;base64,/, "");
    // data = imageStream.replace(/^data:applicationpdf\/\w+;base64,/, "");

    let data = imageStream.replace(/^data:.+;base64,/, "");

    fs.writeFileSync(pathWithFileName, data, { encoding: "base64" }, function (
        err
    ) {
        if (err) return false;

        return true;
    });
}

module.exports.validateAndSaveBankDetails = async function (userId, partnerId, accountNo, ifscCode, accountType, accountName = null, fileName = null, bankDocument = null, merchantCode = null, loanId = null, updateStep = false) {
    try {
        if (!userId || !partnerId || !accountNo || !ifscCode)
            return {
                data: "",
                success: false,
                message: "Mandatory fields not provided",
                status: 200
            }

        const bankMaster = await BankBranchMaster.findOne({
            ifsc: {
                $regex: new RegExp("^" + ifscCode + "$", "i"),
            },
        });

        if (!bankMaster)
            return {
                data: "",
                success: false,
                message: "Invalid Ifsc Code",
                status: 200
            };

        let userPartnerInfo = null;

        if (merchantCode)
            userPartnerInfo = await UserPartnerInfo.findOne({ userId: userId, partnerId: partnerId, merchantCode: merchantCode });
        else
            userPartnerInfo = await UserPartnerInfo.findOne({ userId: userId, partnerId: partnerId });

        let response = {};

        let partnerMaster = await PartnerMaster.findById({ _id: partnerId });

        const model = {
            beneficiary_account_no: accountNo,
            beneficiary_ifsc: ifscCode
        }

        let loan = null;

        let user = await UserInfo.findById(userId);

        if (loanId)
            loan = await LoanInfo.findById(loanId);

        const partnerConfig = await PartnerConfig.findOne({ partnerId: partnerId });

        if (loan) {
            let productType = loan.productType ? loan.productType.split("_") : null;
            productType = productType && productType[0] ? productType[0] : "";

            const mandate = await checkExistingMandate(loan, productType);

            if (mandate.oldMandateExists || (loan.amount >= partnerConfig.eNachAmount && partnerConfig.isMandateRequired)) {
                const digioBank = await DigioBankList.findOne({
                    systemBankName: {
                        $regex: new RegExp("^" + bankMaster.bank + "$", "i")
                    }
                });

                // if (!digioBank || (digioBank && !digioBank.netBanking && !digioBank.debitCard && !digioBank.esign))
                //     return {
                //         data: "",
                //         success: false,
                //         message: "e-Nach option not available for selected bank. Please select different bank.",
                //         status: 200
                //     };
            }
        }

        const byPassBankValidation = await ConfigParameters.findOne({ channel: "Validation", templateType: "Bypass Bank Validation" });

        if (userPartnerInfo) {
            if (!userPartnerInfo.bankDetails || userPartnerInfo.bankDetails.length === 0) {
                //New Case
                response = await bankVerification(model, userId);

                const getBankModel = await setBankProperties(userId, accountNo, ifscCode, accountName, accountType, bankMaster, response, fileName, bankDocument, loanId);

                if (getBankModel.success || byPassBankValidation.isActive || !partnerConfig.isMandateRequired) {
                    userPartnerInfo.bankDetails = [];
                    userPartnerInfo.bankDetails.push(getBankModel.data);

                    await userPartnerInfo.save();

                    if (!userPartnerInfo.isPartnerUser && updateStep) {
                        const updateStep = await updateUserAndLoanSteps(loan, user, "Bank", null, null);

                        loan = updateStep.data.loan;

                        user = updateStep.data.user;

                        await loan.save();

                        await user.save();
                    }

                    if (userPartnerInfo.isPartnerUser && partnerMaster.partnerType === "Borrowing Partner") {

                        partnerMaster.bankAccounts = [];

                        const bankMasterModel = {
                            name: accountName ? accountName.toUpperCase() : "",
                            bankName: getBankModel.data.bankName ? getBankModel.data.bankName : "",
                            branch: getBankModel.data.branch ? getBankModel.data.branch : "",
                            city: getBankModel.data.city ? getBankModel.data.city : "",
                            state: getBankModel.data.state ? getBankModel.data.state : "",
                            address: getBankModel.data.address ? getBankModel.data.address : "",
                            type: accountType,
                            no: accountNo ? accountNo.toUpperCase() : "",
                            ifsc: ifscCode ? ifscCode.toUpperCase() : "",
                            isActive: true,
                            insertedOn: Date.now(),
                            updatedOn: null
                        };

                        partnerMaster.bankAccounts.push(bankMasterModel);
                        await partnerMaster.save();

                    }

                    return {
                        data: getBankModel.data,
                        success: true,
                        message: "Bank validated successfully",
                        status: 200
                    }
                }
                else
                    return getBankModel;
            }
            else {
                const currentAccountNumber = userPartnerInfo.bankDetails && userPartnerInfo.bankDetails[0] && userPartnerInfo.bankDetails[0].accountNo ? userPartnerInfo.bankDetails[0].accountNo.trim().toLowerCase() : "";
                const currentIfscCode = userPartnerInfo.bankDetails && userPartnerInfo.bankDetails[0] && userPartnerInfo.bankDetails[0].ifsc ? userPartnerInfo.bankDetails[0].ifsc.trim().toLowerCase() : "";
                const isAlreadyVerified = userPartnerInfo.bankDetails && userPartnerInfo.bankDetails[0] && userPartnerInfo.bankDetails[0].docVerification && userPartnerInfo.bankDetails[0].docVerification.isBankVerified ? true : false;
                const validatedForLoanId = userPartnerInfo.bankDetails && userPartnerInfo.bankDetails[0] && userPartnerInfo.bankDetails[0].docVerification && userPartnerInfo.bankDetails[0].docVerification.validatedForLoanId ? userPartnerInfo.bankDetails[0].docVerification.validatedForLoanId : null;

                if (currentAccountNumber === accountNo.toLowerCase() && currentIfscCode === ifscCode.toLowerCase() && (validatedForLoanId && validatedForLoanId.equals(loanId) && isAlreadyVerified)) {

                    await backupUserPartnerInfo(userPartnerInfo._id);

                    userPartnerInfo.bankDetails[0].accountType = accountType;
                    userPartnerInfo.bankDetails[0].accountName = accountName ? accountName.toUpperCase() : "";

                    await userPartnerInfo.save();

                    if (!userPartnerInfo.isPartnerUser && updateStep) {

                        const updateStep = await updateUserAndLoanSteps(loan, user, "Bank", null, null);

                        loan = updateStep.data.loan;

                        user = updateStep.data.user;

                        await loan.save();

                        await user.save();
                    }

                    return {
                        data: "",
                        success: true,
                        message: "Bank is already verified",
                        status: 200
                    }
                }
                else {
                    //Revalidate

                    response = await bankVerification(model, userId);

                    const getBankModel = await setBankProperties(userId, accountNo, ifscCode, accountName, accountType, bankMaster, response, fileName, bankDocument, loanId);

                    if (getBankModel.success || byPassBankValidation.isActive || !partnerConfig.isMandateRequired) {
                        await backupUserPartnerInfo(userPartnerInfo._id);

                        userPartnerInfo.bankDetails = [];
                        userPartnerInfo.bankDetails.push(getBankModel.data);

                        await userPartnerInfo.save();

                        if (!userPartnerInfo.isPartnerUser && updateStep) {

                            const updateStep = await updateUserAndLoanSteps(loan, user, "Bank", null, null);

                            loan = updateStep.data.loan;

                            user = updateStep.data.user;

                            await loan.save();

                            await user.save();
                        }

                        if (userPartnerInfo.isPartnerUser && partnerMaster.partnerType === "Borrowing Partner") {

                            partnerMaster.bankAccounts = [];

                            const bankMasterModel = {
                                name: accountName ? accountName.toUpperCase() : "",
                                bankName: getBankModel.data.bankName ? getBankModel.data.bankName : "",
                                branch: getBankModel.data.branch ? getBankModel.data.branch : "",
                                city: getBankModel.data.city ? getBankModel.data.city : "",
                                state: getBankModel.data.state ? getBankModel.data.state : "",
                                address: getBankModel.data.address ? getBankModel.data.address : "",
                                type: accountType,
                                no: accountNo ? accountNo.toUpperCase() : "",
                                ifsc: ifscCode ? ifscCode.toUpperCase() : "",
                                isActive: true,
                                insertedOn: Date.now(),
                                updatedOn: null
                            };

                            partnerMaster.bankAccounts.push(bankMasterModel);
                            await partnerMaster.save();

                        }

                        return {
                            data: getBankModel.data,
                            success: true,
                            message: "Bank validated successfully",
                            status: 200
                        }
                    }
                    else
                        return getBankModel;
                }
            }
        }
        else
            return {
                data: "",
                success: false,
                message: "No record found for current partner",
                status: 200
            }
    }
    catch (ex) {
        console.log("ex", ex);
        const model = { userId, partnerId, accountNo, ifscCode, accountType, accountName, fileName, bankDocument };
        bankDocument = null;
        saveErrorLog("validateAndSaveBankDetails", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        return {
            data: "",
            success: false,
            message: ex.message,
            status: 200
        }
    }
}

async function setBankProperties(userId, accountNo, ifsc, accountName, accountType, bankMaster, bankValidation, fileName = null, bankDocument = null, loanId = null) {
    try {
        const proofImagePath = `${config.get("keys.proofImages")}/${userId}`;

        let bankDoc_Details = null;
        let docVerification = {};
        let errorDetails = {};

        if (fileName && bankDocument) {
            const bankFileName = `bank_${getCurrentTimeStamp()}${getExtension(model.bankFile)}`;

            const bankImagePath = proofImagePath + "/BankProof";

            await createDirectoryAndSaveFile(
                bankImagePath,
                bankDocument,
                bankImagePath + "/" + bankFileName
            );

            bankDoc_Details = {
                fileName: bankFileName,
                filePath: staticPath + "/BankProof/" + bankFileName,
                isActive: true,
                insertedOn: Date.now(),
                position: "front"
            };
        }

        if (bankValidation.success) {
            if (bankValidation.data.verified) {
                docVerification = {
                    isBankVerified: true,
                    id: bankValidation.data.id ? bankValidation.data.id : "",
                    verified: bankValidation.data.verified,
                    verified_at: bankValidation.data.verified_at,
                    beneficiary_name_with_bank: bankValidation.data.beneficiary_name_with_bank,
                    createdOn: Date.now(),
                    validatedForLoanId: loanId
                };

                errorDetails = {
                    details: bankValidation.data && bankValidation.data.error_message ? bankValidation.data.error_message : "",
                    code: "",
                    message: bankValidation.data && bankValidation.data.error_message ? bankValidation.data.error_message : "",
                };
            }
            else {
                docVerification = {
                    isBankVerified: false,
                    createdOn: Date.now()
                };

                if (bankValidation.data && bankValidation.data.id)
                    errorDetails = {
                        details: bankValidation.data && bankValidation.data.id ? bankValidation.data.id : "",
                        code: "",
                        message: bankValidation.data && bankValidation.data.error_msg ? bankValidation.data.error_msg : "",
                    };
                else if (bankValidation.data && bankValidation.data.details)
                    errorDetails = {
                        details: bankValidation.data && bankValidation.data.details ? bankValidation.data.details : "",
                        code: bankValidation.data && bankValidation.data.code ? bankValidation.data.code : "",
                        message: bankValidation.data && bankValidation.data.message ? bankValidation.data.message : ""
                    };
            }
        }
        else {
            docVerification = {
                isBankVerified: false,
                createdOn: Date.now()
            };

            if (bankValidation.data && bankValidation.data.id)
                errorDetails = {
                    details: bankValidation.data && bankValidation.data.id ? bankValidation.data.id : "",
                    code: "",
                    message: bankValidation.data && bankValidation.data.error_msg ? bankValidation.data.error_msg : "",
                };
            else if (bankValidation.data && bankValidation.data.details)
                errorDetails = {
                    details: bankValidation.data && bankValidation.data.details ? bankValidation.data.details : "",
                    code: bankValidation.data && bankValidation.data.code ? bankValidation.data.code : "",
                    message: bankValidation.data && bankValidation.data.message ? bankValidation.data.message : ""
                };
        }

        const bankModel = {
            accountNo: accountNo.toUpperCase(),
            ifsc: ifsc.toUpperCase(),
            accountName: accountName ? accountName.toUpperCase() : "",
            bankName: bankMaster.bank,
            address: bankMaster.address,
            city: bankMaster.city,
            state: bankMaster.state,
            insertedOn: Date.now(),
            branch: bankMaster.branch,
            accountType: accountType,
            docDetails: bankDoc_Details,
            docVerification: docVerification,
            errorDetails: errorDetails
        };

        bankDocument = null;

        return {
            data: bankModel,
            success: docVerification.isBankVerified ? true : false,
            message: docVerification.isBankVerified ? "" : errorDetails.message,
            status: 200
        };
    }
    catch (ex) {
        const model = { userId: userId, accountNo: accountNo, ifsc: ifsc, bankMaster: bankMaster, fileName: fileName };
        bankDocument = null;
        saveErrorLog("setBankProperties", apiPath, model, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        return {
            data: "",
            success: false,
            message: ex.message,
            status: 200
        }
    }

}

async function backupUserPartnerInfo(userPartnerInfoId) {
    let output = {
        success: false,
        message: "Some Error Occurred",
        status: 501,
    };
    try {
        const userPartnerInfo = await UserPartnerInfo.findById(userPartnerInfoId);

        if (userPartnerInfo) {
            const tempUserPartnerInfo = userPartnerInfo.toJSON();
            tempUserPartnerInfo.userPartnerInfoId = tempUserPartnerInfo["_id"];
            delete tempUserPartnerInfo["_id"];
            const userPartnerInfoRepository = new UserPartnerInfoHistory(tempUserPartnerInfo);
            userPartnerInfoRepository.deletedOn = Date.now();
            await userPartnerInfoRepository.save();

            output = {
                success: true,
                message: "Saved Successfully",
                status: 200,
            };
        } else {
            output.message = "No record found";
        }
    } catch (ex) {
        output.message = ex.message;
    }
    return output;
};

module.exports.backupUserPartnerInfo = backupUserPartnerInfo;

async function checkExistingMandate(loan, productType) {
    try {
        const currentLoanMandate = await DigioMandateGeneration.findOne({ loanIds: loan._id, isActive: true });

        let mandate = null;

        let signedMandateExists = false;
        let oldMandateExists = false;
        //check if mandate exists for partner
        if (!currentLoanMandate) {

            mandate = await DigioMandateGeneration.findOne({
                userId: loan.userId,
                partnerId: loan.partnerId,
                isActive: true,
                status: "register_success"
            })
                .sort({ "mandateDetails.mandate_data.maximum_amount": -1, createdOn: -1 });

            if (productType === "LC" && !loan.isParentLoan && !mandate)
                return {
                    data: "",
                    success: false,
                    message: "No mandate found",
                    status: 200,
                    signedMandateExists: signedMandateExists,
                    oldMandateExists: oldMandateExists
                }

            const isMandateDetailsMatch = await checkMandateCurrentBankDetails(mandate, loan._id);

            if (mandate)
                oldMandateExists = true;

            if (!isMandateDetailsMatch.success) {
                return {
                    data: "",
                    success: true,
                    message: "Mandate Bank Details do not match",
                    status: 200,
                    signedMandateExists: signedMandateExists,
                    oldMandateExists: oldMandateExists
                }
            }
        }
        else if (currentLoanMandate && (currentLoanMandate.status === "success" || currentLoanMandate.status === "register_success")) {
            const isMandateDetailsMatch = await checkMandateCurrentBankDetails(currentLoanMandate, loan._id);

            signedMandateExists = isMandateDetailsMatch.success;
            oldMandateExists = true;
        }

        return {
            data: "",
            success: true,
            signedMandateExists: signedMandateExists,
            message: "",
            status: 200,
            oldMandateExists: oldMandateExists
        }
    }
    catch (ex) {
        saveErrorLog("checkAndAssignMandate", apiPath, { loan, productType }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);

        return {
            data: "",
            success: false,
            mandateExists: mandateExists,
            message: ex.message,
            status: 200,
            oldMandateExists: oldMandateExists
        }
    }
};

async function checkMandateCurrentBankDetails(mandate, loanId) {
    try {
        if (!mandate)
            return {
                data: "",
                success: false,
                message: "Mandate does not exists"
            }

        const loan = await LoanInfo.findById(loanId);

        const userPartnerInfo = await UserPartnerInfo.findOne({
            userId: mongoose.Types.ObjectId(loan.userId),
            partnerId: mongoose.Types.ObjectId(loan.partnerId),
            merchantCode: loan.merchantCode
        });

        let currentBankAccountNo = "";
        let currentBankIfscCode = "";
        let currentAccountName = "";

        let mandateBankAccountNo = "";
        let mandateBankIfscCode = "";
        let mandateBankAccountName = "";

        if (userPartnerInfo && userPartnerInfo.bankDetails && userPartnerInfo.bankDetails[0]) {
            currentBankAccountNo = userPartnerInfo.bankDetails[0].accountNo ? userPartnerInfo.bankDetails[0].accountNo.trim().toLowerCase() : "";
            currentBankIfscCode = userPartnerInfo.bankDetails[0].ifsc ? userPartnerInfo.bankDetails[0].ifsc.trim().toLowerCase() : "";
            currentAccountName = userPartnerInfo.bankDetails[0].accountName ? userPartnerInfo.bankDetails[0].accountName.trim().toLowerCase() : "";
        }

        if (mandate && mandate.mandateDetails && mandate.mandateDetails.mandate_data) {

            mandateBankAccountNo = mandate.mandateDetails
                .mandate_data.customer_account_number ? mandate.mandateDetails
                    .mandate_data.customer_account_number.trim().toLowerCase() : "";

            mandateBankIfscCode = mandate.mandateDetails
                .mandate_data.destination_bank_id ? mandate.mandateDetails
                    .mandate_data.destination_bank_id.trim().toLowerCase() : "";

            mandateBankAccountName = mandate.mandateDetails
                .mandate_data.customer_name ? mandate.mandateDetails
                    .mandate_data.customer_name.trim().toLowerCase() : "";
        }

        if (mandate
            && (currentBankAccountNo === mandateBankAccountNo)
            && (currentBankIfscCode === mandateBankIfscCode)
            && (currentAccountName === mandateBankAccountName)
        )
            return {
                data: "",
                success: true,
                message: "Mandate and Bank details match"
            }
        else
            return {
                data: "",
                success: false,
                message: "Mandate and Bank details do not match"
            }
    }
    catch (ex) {
        saveErrorLog("checkMandateCurrentBankDetails", apiPath, { mandate, loanId }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);
        return {
            data: "",
            success: false,
            message: ex.message
        }
    }

}

module.exports.checkMandateCurrentBankDetails = checkMandateCurrentBankDetails;

async function sendDigilockerRequest(loanId, req) {
    try {
        console.log("dig locker 1")
        const loan = await LoanInfo.findById(loanId);
        console.log("dig locker 2", loan)

        /** ROLE CHECK */
        if (!loan) {
            return {
                data: "",
                success: false,
                message: "Invalid LoanId"
            }
        }

        if (req && req.user.role == ROLES.USER && req.user.id != loan.userId.toString()) {
            return {
                data: "",
                success: false,
                message: "Access Denied"
            }
        }

        const user = await UserInfo.findById(loan.userId);

        let userIdentifier = user.userDetails.mobileNo[0] && user.userDetails.mobileNo[0].mobileNumber ? user.userDetails.mobileNo[0].mobileNumber : null;

        if (!userIdentifier)
            userIdentifier = user.userDetails.emailId[0] ? user.userDetails.emailId[0] : null;

        const name = `${user.userDetails.firstName} ${user.userDetails.lastName}`;

        const model = {
            customer_identifier: userIdentifier,
            customer_name: name,
            actions: [
                {
                    type: config.get("digio.digilocker.type"),
                    title: config.get("digio.digilocker.title"),
                    description: config.get("digio.digilocker.description"),
                    document_types: config.get("digio.digilocker.document_types")

                }
            ],
            notify_customer: false,
            expire_in_days: config.get("digio.digilocker.expire_in_days"),
            generate_access_token: config.get("digio.digilocker.generate_access_token")
        }

        const response = await callDigioApi("POST", `client/kyc/v2/request`, model);

        const digilockerModel = new DigilockerLog({
            userId: user._id,
            loanId: loanId,
            status: response.status,
            request: JSON.stringify(model),
            response: JSON.stringify(response),
            id: response.id,
            reference_id: response.reference_id,
            transaction_id: response.transaction_id,
            mediaId: null,
            error: null,
            insertedOn: Date.now(),
            updatedOn: null
        });

        await digilockerModel.save();

        let redirectUrl = `${config.get("digio.digilocker.linkUrl")}${response.id}/${response.reference_id}/${userIdentifier}?redirect_url=${config.get("digio.digilocker.redirectUrl")}&logo=${config.get("digio.digilocker.logoLink")}`;

        if (response.access_token && response.access_token.id)
            redirectUrl += `&token_id=${response.access_token.id}`;

        return {
            data: redirectUrl,
            success: true,
            message: "Link generated successfully"
        }
    }
    catch (ex) {
        saveErrorLog("sendDigilockerRequest", apiPath, { loanId }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);

        return {
            data: "",
            success: false,
            message: ex.message
        }
    }
}

async function sendDigilockerRequestInvoiceFinancing(loanId, req) {
    try {
        console.log("dig locker 1")
        const loan = await LoanInfo.findById(loanId);
        console.log("dig locker 2", loan)

        // /** ROLE CHECK */
        // if (!loan) {
        //     return {
        //         data: "",
        //         success: false,
        //         message: "Invalid LoanId"
        //     }
        // }

        // if (req && req.user.role == ROLES.USER && req.user.id != loan.userId.toString()) {
        //     return {
        //         data: "",
        //         success: false,
        //         message: "Access Denied"
        //     }
        // }

        const user = await UserInfo.findById(loan.userId);

        let userIdentifier = user.userDetails.mobileNo[0] && user.userDetails.mobileNo[0].mobileNumber ? user.userDetails.mobileNo[0].mobileNumber : null;

        if (!userIdentifier)
            userIdentifier = user.userDetails.emailId[0] ? user.userDetails.emailId[0] : null;

        const name = `${user.userDetails.firstName} ${user.userDetails.lastName}`;

        const model = {
            customer_identifier: userIdentifier,
            customer_name: name,
            actions: [
                {
                    type: config.get("digio.digilocker.type"),
                    title: config.get("digio.digilocker.title"),
                    description: config.get("digio.digilocker.description"),
                    document_types: config.get("digio.digilocker.document_types")

                }
            ],
            notify_customer: false,
            expire_in_days: config.get("digio.digilocker.expire_in_days"),
            generate_access_token: config.get("digio.digilocker.generate_access_token")
        }

        const response = await callDigioApi("POST", `client/kyc/v2/request`, model);

        const digilockerModel = new DigilockerLog({
            userId: user._id,
            loanId: loanId,
            status: response.status,
            request: JSON.stringify(model),
            response: JSON.stringify(response),
            id: response.id,
            reference_id: response.reference_id,
            transaction_id: response.transaction_id,
            mediaId: null,
            error: null,
            insertedOn: Date.now(),
            updatedOn: null
        });

        await digilockerModel.save();

        let redirectUrl = `${config.get("digio.digilocker.linkUrl")}${response.id}/${response.reference_id}/${userIdentifier}?redirect_url=${config.get("digio.digilocker.redirectUrlInvoiceFinancing")}&logo=${config.get("digio.digilocker.logoLink")}`;

        if (response.access_token && response.access_token.id)
            redirectUrl += `&token_id=${response.access_token.id}`;

        return {
            data: redirectUrl,
            success: true,
            message: "Link generated successfully"
        }
    }
    catch (ex) {
        saveErrorLog("sendDigilockerRequest", apiPath, { loanId }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);

        return {
            data: "",
            success: false,
            message: ex.message
        }
    }
}

module.exports.sendDigilockerRequestInvoiceFinancing = sendDigilockerRequestInvoiceFinancing;

module.exports.sendDigilockerRequest = sendDigilockerRequest;

async function updateDigilockerResponse(status, digio_doc_id, message) {
    try {
        const digilocker = await DigilockerLog.findOne({ id: digio_doc_id });

        if (digilocker) {
            const user = await UserInfo.findById(digilocker.userId);

            const loan = await LoanInfo.findById(digilocker.loanId);

            if (status && status.toLowerCase() === "success") {

                const model = {
                    status: "APPROVED"
                }

                const response = await callDigioApi("POST", `client/kyc/v2/request/${digio_doc_id}/manage_approval`, model);

                if (response && response.status && response.status.toLowerCase() === "approved") {
                    const staticPath = "ProofImages/" + user._id;

                    //take backup of userinfo below update
                    if (digilocker && digilocker.userId)
                        await backupUserInfo(digilocker.userId);

                    const details = {
                        aadhaar: {
                            id_number: response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.aadhaar && response.actions[0].details.aadhaar.id_number ? response.actions[0].details.aadhaar.id_number : "",
                            document_type: response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.aadhaar && response.actions[0].details.aadhaar.document_type ? response.actions[0].details.aadhaar.document_type : "",
                            id_proof_type: response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.aadhaar && response.actions[0].details.aadhaar.id_proof_type ? response.actions[0].details.aadhaar.id_proof_type : "",
                            gender: response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.aadhaar && response.actions[0].details.aadhaar.gender ? response.actions[0].details.aadhaar.gender : "",
                            image: response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.aadhaar && response.actions[0].details.aadhaar.image ? response.actions[0].details.aadhaar.image : "",
                            name: response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.aadhaar && response.actions[0].details.aadhaar.name ? response.actions[0].details.aadhaar.name : "",
                            dob: response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.aadhaar && response.actions[0].details.aadhaar.dob ? response.actions[0].details.aadhaar.dob : "",
                            current_address: response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.aadhaar && response.actions[0].details.aadhaar.current_address ? response.actions[0].details.aadhaar.current_address : "",
                            permanent_address: response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.aadhaar && response.actions[0].details.aadhaar.permanent_address ? response.actions[0].details.aadhaar.permanent_address : "",
                            current_address_details: {
                                address: response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.aadhaar && response.actions[0].details.aadhaar.current_address_details && response.actions[0].details.aadhaar.current_address_details.address ? response.actions[0].details.aadhaar.current_address_details.address : "",
                                locality_or_post_office: response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.aadhaar && response.actions[0].details.aadhaar.current_address_details && response.actions[0].details.aadhaar.current_address_details.locality_or_post_office ? response.actions[0].details.aadhaar.current_address_details.locality_or_post_office : "",
                                district_or_city: response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.aadhaar && response.actions[0].details.aadhaar.current_address_details && response.actions[0].details.aadhaar.current_address_details.district_or_city ? response.actions[0].details.aadhaar.current_address_details.district_or_city : "",
                                state: response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.aadhaar && response.actions[0].details.aadhaar.current_address_details && response.actions[0].details.aadhaar.current_address_details.state ? response.actions[0].details.aadhaar.current_address_details.state : "",
                                pincode: response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.aadhaar && response.actions[0].details.aadhaar.current_address_details && response.actions[0].details.aadhaar.current_address_details.pincode ? response.actions[0].details.aadhaar.current_address_details.pincode : "",
                            },
                            permanent_address_details: {
                                address: response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.aadhaar && response.actions[0].details.aadhaar.current_address_details && response.actions[0].details.aadhaar.permanent_address_details.address ? response.actions[0].details.aadhaar.permanent_address_details.address : "",
                                locality_or_post_office: response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.aadhaar && response.actions[0].details.aadhaar.permanent_address_details && response.actions[0].details.aadhaar.permanent_address_details.locality_or_post_office ? response.actions[0].details.aadhaar.permanent_address_details.locality_or_post_office : "",
                                district_or_city: response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.aadhaar && response.actions[0].details.aadhaar.permanent_address_details && response.actions[0].details.aadhaar.permanent_address_details.district_or_city ? response.actions[0].details.aadhaar.permanent_address_details.district_or_city : "",
                                state: response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.aadhaar && response.actions[0].details.aadhaar.permanent_address_details && response.actions[0].details.aadhaar.permanent_address_details.state ? response.actions[0].details.aadhaar.permanent_address_details.state : "",
                                pincode: response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.aadhaar && response.actions[0].details.aadhaar.permanent_address_details && response.actions[0].details.aadhaar.permanent_address_details.pincode ? response.actions[0].details.aadhaar.permanent_address_details.pincode : ""
                            }
                        },
                        pan: {
                            id_number: response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.pan && response.actions[0].details.pan.id_number ? response.actions[0].details.pan.id_number : "",
                            document_type: response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.pan && response.actions[0].details.pan.document_type ? response.actions[0].details.pan.document_type : "",
                            id_proof_type: response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.pan && response.actions[0].details.pan.id_proof_type ? response.actions[0].details.pan.id_proof_type : "",
                            gender: response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.pan && response.actions[0].details.pan.gender ? response.actions[0].details.pan.gender : "",
                            name: response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.pan && response.actions[0].details.pan.name ? response.actions[0].details.pan.name : "",
                            dob: response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.pan && response.actions[0].details.pan.dob ? response.actions[0].details.pan.dob : "",
                        },
                        insertedOn: digilocker.details && digilocker.details.insertedOn ? digilocker.details.insertedOn : Date.now(),
                        updatedOn: digilocker.details && digilocker.details.insertedOn ? Date.now() : null,
                        validation_result: response.actions && response.actions[0] && response.actions[0].validation_result ? response.actions[0].validation_result : "",
                        completed_at: response.actions && response.actions[0] && response.actions[0].completed_at ? response.actions[0].completed_at : "",
                        face_match_obj_type: response.actions && response.actions[0] && response.actions[0].face_match_obj_type ? response.actions[0].face_match_obj_type : "",
                        face_match_status: response.actions && response.actions[0] && response.actions[0].face_match_status ? response.actions[0].face_match_status : "",
                        obj_analysis_status: response.actions && response.actions[0] && response.actions[0].obj_analysis_status ? response.actions[0].obj_analysis_status : "",
                        processing_done: response.actions && response.actions[0] && response.actions[0].processing_done ? response.actions[0].processing_done : false,
                    };

                    const approvalResponse = {
                        response: JSON.stringify(response),
                        insertedOn: digilocker.approvalResponse && digilocker.approvalResponse.insertedOn ? digilocker.approvalResponse.insertedOn : Date.now(),
                        updatedOn: digilocker.approvalResponse && digilocker.approvalResponse.insertedOn ? Date.now() : null
                    };

                    digilocker.details = details;
                    digilocker.approvalResponse = approvalResponse;
                    digilocker.updatedOn = Date.now();
                    digilocker.status = response.status;
                    digilocker.mediaId = response.actions && response.actions[0] && response.actions[0].execution_request_id ? response.actions[0].execution_request_id : "";
                    digilocker.error = (!user.panNo || (user.panNo && user.panNo.toLowerCase() === response.actions[0].details.pan.id_number.toLowerCase())) ? "" : "PAN number does not match with existing record";
                    digilocker.success = true;

                    await digilocker.save();

                    if (!user.panNo || user.panNo && user.panNo.toLowerCase() === response.actions[0].details.pan.id_number.toLowerCase()) {
                        user.panDocVerification = {
                            isNumberVerified: true,
                            isDocVerified: true,
                            type: "digilocker",
                            isDocMatch: true,
                            createdOn: Date.now(),
                            details: {
                                name: details.pan.name,
                                docNo: details.pan.id_number,
                                dob: details.pan.dob,
                                document_type: details.pan.document_type,
                                id_proof_type: details.pan.id_proof_type
                            },
                            ocr: {},
                            verificationResponse: {},
                            errorDetails: {}
                        };

                        let photoPath = null;

                        if (response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.aadhaar && response.actions[0].details.aadhaar.image) {
                            photoPath = staticPath + "/PoaPhoto/photo.jpg";
                            await AWS.uploadToS3(photoPath, response.actions[0].details.aadhaar.image)
                        }

                        user.digilocker = {
                            execution_request_id: response.actions[0].execution_request_id,
                            created_at: new Date(response.actions[0].completed_at),
                            reference_id: response.reference_id,
                            status: response.actions[0].status,
                            processing_done: response.actions[0].processing_done,
                            face_match_status: response.actions[0].face_match_status,
                            validation_result: {},
                            face_match_obj_type: response.actions[0].face_match_obj_type,
                            obj_analysis_status: response.actions[0].obj_analysis_status
                        };

                        const panDocument = await callDigioApi("DOC", `client/kyc/v2/media/${digilocker.mediaId}?doc_type=PAN`, null);
                        const panFileName = `panFile_${getCurrentTimeStamp()}.pdf`;
                        const panImagePath = staticPath + "/PAN/" + panFileName;

                        await AWS.uploadToS3(panImagePath, panDocument, "BUFFER");

                        user.panDocDetails = [];

                        let panDoc_Details = {
                            fileName: panFileName,
                            filePath: panImagePath,
                            isActive: true,
                            insertedOn: Date.now()
                        };

                        user.panDocDetails.push(panDoc_Details);
                        user.panNo = response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.pan && response.actions[0].details.pan.id_number ? response.actions[0].details.pan.id_number : "";
                        const aadhaarDocument = await callDigioApi("DOC", `client/kyc/v2/media/${digilocker.mediaId}?doc_type=AADHAAR&xml=false`, null);

                        let docDetails = [];
                        user.poaDetails = [];

                        const poaFileName = `poaFront_${getCurrentTimeStamp()}.pdf`;
                        const poaImagePath = staticPath + "/POA/" + poaFileName;
                        await AWS.uploadToS3(poaImagePath, aadhaarDocument, "BUFFER")

                        let poaDoc_Details = {
                            fileName: poaFileName,
                            filePath: poaImagePath,
                            isActive: true,
                            insertedOn: Date.now(),
                            documentPassword: null,
                            position: "front"
                        };

                        docDetails.push(poaDoc_Details);

                        const poa_Details = {
                            poaNo: response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.aadhaar && response.actions[0].details.aadhaar.id_number ? response.actions[0].details.aadhaar.id_number : "",
                            poaType: "Aadhaar Card",
                            docDetails: docDetails,
                            insertedOn: Date.now(),
                            poaVerificationDocType: "Aadhaar Card",
                            isMasked: true,
                            docVerification: {
                                isNumberVerified: true,
                                isDocVerified: true,
                                type: "digilocker",
                                verificationType: "AADHAAR",
                                isDocMatch: true,
                                createdOn: Date.now(),
                                dl: {},
                                voterId: {},
                                passport: {},
                                uid: {
                                    details: {
                                        docNo: response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.aadhaar && response.actions[0].details.aadhaar.id_number ? response.actions[0].details.aadhaar.id_number : "",
                                        document_type: response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.aadhaar && response.actions[0].details.aadhaar.document_type ? response.actions[0].details.aadhaar.document_type : "",
                                        id_proof_type: response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.aadhaar && response.actions[0].details.aadhaar.id_proof_type ? response.actions[0].details.aadhaar.id_proof_type : "",
                                        gender: response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.aadhaar && response.actions[0].details.aadhaar.gender ? response.actions[0].details.aadhaar.gender : "",
                                        name: response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.aadhaar && response.actions[0].details.aadhaar.name ? response.actions[0].details.aadhaar.name : "",
                                        dob: response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.aadhaar && response.actions[0].details.aadhaar.dob ? response.actions[0].details.aadhaar.dob : "",
                                        current_address: response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.aadhaar && response.actions[0].details.aadhaar.current_address ? response.actions[0].details.aadhaar.current_address : "",
                                        permanent_address: response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.aadhaar && response.actions[0].details.aadhaar.permanent_address_details.address ? response.actions[0].details.aadhaar.permanent_address_details.address : "",
                                        current_address_details: {
                                            address: response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.aadhaar && response.actions[0].details.aadhaar.current_address_details && response.actions[0].details.aadhaar.current_address_details.address ? response.actions[0].details.aadhaar.current_address_details.address : "",
                                            locality_or_post_office: response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.aadhaar && response.actions[0].details.aadhaar.current_address_details && response.actions[0].details.aadhaar.current_address_details.locality_or_post_office ? response.actions[0].details.aadhaar.current_address_details.locality_or_post_office : "",
                                            district_or_city: response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.aadhaar && response.actions[0].details.aadhaar.current_address_details && response.actions[0].details.aadhaar.current_address_details.district_or_city ? response.actions[0].details.aadhaar.current_address_details.district_or_city : "",
                                            state: response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.aadhaar && response.actions[0].details.aadhaar.current_address_details && response.actions[0].details.aadhaar.current_address_details.state ? response.actions[0].details.aadhaar.current_address_details.state : "",
                                            pincode: response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.aadhaar && response.actions[0].details.aadhaar.current_address_details && response.actions[0].details.aadhaar.current_address_details.pincode ? response.actions[0].details.aadhaar.current_address_details.pincode : "",
                                        },
                                        permanent_address_details: {
                                            address: response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.aadhaar && response.actions[0].details.aadhaar.current_address_details && response.actions[0].details.aadhaar.permanent_address_details.address ? response.actions[0].details.aadhaar.permanent_address_details.address : "",
                                            locality_or_post_office: response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.aadhaar && response.actions[0].details.aadhaar.permanent_address_details && response.actions[0].details.aadhaar.permanent_address_details.locality_or_post_office ? response.actions[0].details.aadhaar.permanent_address_details.locality_or_post_office : "",
                                            district_or_city: response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.aadhaar && response.actions[0].details.aadhaar.permanent_address_details && response.actions[0].details.aadhaar.permanent_address_details.district_or_city ? response.actions[0].details.aadhaar.permanent_address_details.district_or_city : "",
                                            state: response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.aadhaar && response.actions[0].details.aadhaar.permanent_address_details && response.actions[0].details.aadhaar.permanent_address_details.state ? response.actions[0].details.aadhaar.permanent_address_details.state : "",
                                            pincode: response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.aadhaar && response.actions[0].details.aadhaar.permanent_address_details && response.actions[0].details.aadhaar.permanent_address_details.pincode ? response.actions[0].details.aadhaar.permanent_address_details.pincode : ""
                                        },
                                        photoPath: photoPath,
                                    },
                                    ocr: {}
                                }
                            },
                            errorDetails: {}
                        };

                        user.poaDetails.push(poa_Details);

                        let cadd1 = "";
                        let cadd2 = "";
                        let cadd3 = "";
                        let ccity = "";
                        let cstate = "";
                        let cpincode = "";

                        if (response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.aadhaar && response.actions[0].details.aadhaar.current_address_details && response.actions[0].details.aadhaar.current_address_details.address) {
                            cadd1 = response.actions[0].details.aadhaar.current_address_details.address.replace("#", "");
                            cadd1 = cadd1.replace("@", "");
                        }

                        if (response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.aadhaar && response.actions[0].details.aadhaar.current_address_details && response.actions[0].details.aadhaar.current_address_details.locality_or_post_office) {
                            cadd3 = response.actions[0].details.aadhaar.current_address_details.locality_or_post_office.replace("#", "");
                            cadd3 = cadd3.replace("@", "");
                        }

                        if (response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.aadhaar && response.actions[0].details.aadhaar.current_address_details && response.actions[0].details.aadhaar.current_address_details.district_or_city) {
                            ccity = response.actions[0].details.aadhaar.current_address_details.district_or_city.replace("#", "");
                            ccity = ccity.replace("@", "");
                        }

                        if (response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.aadhaar && response.actions[0].details.aadhaar.current_address_details && response.actions[0].details.aadhaar.current_address_details.state) {
                            cstate = response.actions[0].details.aadhaar.current_address_details.state.replace("#", "");
                            cstate = cstate.replace("@", "");
                        }

                        if (response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.aadhaar && response.actions[0].details.aadhaar.current_address_details && response.actions[0].details.aadhaar.current_address_details.pincode) {
                            cpincode = response.actions[0].details.aadhaar.current_address_details.pincode.replace("#", "");
                            cpincode = cpincode.replace("@", "");
                        }

                        user.addressDetails = [];

                        user.addressDetails.push({
                            add1: cadd1,
                            add2: cadd2,
                            add3: cadd3,
                            type: "correspondence",
                            city: ccity ? ccity.toUpperCase() : "",
                            state: cstate ? cstate.toUpperCase() : "",
                            country: "INDIA",
                            pincode: cpincode,
                            insertedOn: Date.now(),
                        });

                        let padd1 = "";
                        let padd2 = "";
                        let padd3 = "";
                        let pcity = "";
                        let pstate = "";
                        let ppincode = "";

                        if (response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.aadhaar && response.actions[0].details.aadhaar.permanent_address_details && response.actions[0].details.aadhaar.permanent_address_details.address) {
                            padd1 = response.actions[0].details.aadhaar.permanent_address_details.address.replace("#", "");
                            padd1 = padd1.replace("@", "");
                        }

                        if (response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.aadhaar && response.actions[0].details.aadhaar.permanent_address_details && response.actions[0].details.aadhaar.permanent_address_details.locality_or_post_office) {
                            padd3 = response.actions[0].details.aadhaar.permanent_address_details.locality_or_post_office.replace("#", "");
                            padd3 = padd3.replace("@", "");
                        }

                        if (response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.aadhaar && response.actions[0].details.aadhaar.permanent_address_details && response.actions[0].details.aadhaar.permanent_address_details.district_or_city) {
                            pcity = response.actions[0].details.aadhaar.permanent_address_details.district_or_city.replace("#", "");
                            pcity = pcity.replace("@", "");
                        }

                        if (response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.aadhaar && response.actions[0].details.aadhaar.permanent_address_details && response.actions[0].details.aadhaar.permanent_address_details.state) {
                            pstate = response.actions[0].details.aadhaar.permanent_address_details.state.replace("#", "");
                            pstate = pstate.replace("@", "");
                        }

                        if (response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.aadhaar && response.actions[0].details.aadhaar.permanent_address_details && response.actions[0].details.aadhaar.permanent_address_details.pincode) {
                            ppincode = response.actions[0].details.aadhaar.permanent_address_details.pincode.replace("#", "");
                            ppincode = ppincode.replace("@", "");
                        }

                        user.addressDetails.push({
                            add1: padd1,
                            add2: padd2,
                            add3: padd3,
                            type: "permanent",
                            city: pcity ? pcity.toUpperCase() : "",
                            state: pstate ? pstate.toUpperCase() : "",
                            country: "INDIA",
                            pincode: ppincode,
                            insertedOn: Date.now(),
                        });

                        let parts = "";

                        if (response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.pan && response.actions[0].details.pan.dob && response.actions[0].details.pan.dob.includes("/"))
                            parts = response.actions[0].details.pan.dob.split("/");
                        else if (response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.pan && response.actions[0].details.pan.dob && response.actions[0].details.pan.dob.includes("-"))
                            parts = response.actions[0].details.pan.dob.split("-");

                        if (parts) {
                            let dob = new Date(parts[2], parts[1] - 1, parts[0]);
                            user.userDetails.dob = dob;
                        }

                        let poiDob = response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.pan && response.actions[0].details.pan.dob ? response.actions[0].details.pan.dob.trim() : "";
                        let poiName = response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.pan && response.actions[0].details.pan.name ? response.actions[0].details.pan.name.trim().toLowerCase() : "";

                        if (poiDob && poiDob.includes("-"))
                            poiDob = poiDob.replace("-", "/");

                        let poaDob = response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.aadhaar && response.actions[0].details.aadhaar.dob ? response.actions[0].details.aadhaar.dob.trim() : "";
                        let poaName = response.actions && response.actions[0] && response.actions[0].details && response.actions[0].details.aadhaar && response.actions[0].details.aadhaar.name ? response.actions[0].details.aadhaar.name.trim().toLowerCase() : "";

                        if (poaDob && poaDob.includes("-"))
                            poaDob = poaDob.replace("-", "/");

                        let detailsMismatch = "None";

                        if (poiDob !== poaDob && poiName !== poaName)
                            detailsMismatch = "Name & DOB";
                        else if (poiName !== poaName)
                            detailsMismatch = "Name";
                        else if (poiDob !== poaDob)
                            detailsMismatch = "DOB";

                        if (detailsMismatch !== "None") {
                            let dateOfBirth1 = null;
                            let dateOfBirth2 = null;

                            if (poiDob) {
                                let parts = poiDob.split("/");
                                dateOfBirth1 = new Date(parts[2], parts[1] - 1, parts[0]);
                            }

                            if (poaDob) {
                                let parts = poaDob.split("/");
                                dateOfBirth2 = new Date(parts[2], parts[1] - 1, parts[0]);
                            }

                            user.userDetails.detailsMismatch = {
                                isMismatch: true,
                                type: detailsMismatch,
                                details: {
                                    poiName: poiName && (detailsMismatch === "Name" || detailsMismatch === "Name & DOB") ? poiName.trim().toUpperCase() : null,
                                    poaName: poaName && (detailsMismatch === "Name" || detailsMismatch === "Name & DOB") ? poaName.trim().toUpperCase() : null,
                                    poiDob: dateOfBirth1 && (detailsMismatch === "DOB" || detailsMismatch === "Name & DOB") ? dateOfBirth1 : null,
                                    poaDob: dateOfBirth2 && (detailsMismatch === "DOB" || detailsMismatch === "Name & DOB") ? dateOfBirth2 : null
                                },
                                insertedOn: user.userDetails.detailsMismatch && user.userDetails.detailsMismatch.insertedOn ? user.userDetails.detailsMismatch.insertedOn : Date.now(),
                                modifiedOn: user.userDetails.detailsMismatch && user.userDetails.detailsMismatch.insertedOn ? Date.now() : null
                            }
                        } else user.userDetails.detailsMismatch = null;

                        //Update user's name
                        if (poiName) {
                            let nameParts = poiName.split(' ');

                            if (nameParts.length == 1) {
                                user.userDetails.firstName = nameParts[0];
                                user.userDetails.lastName = ".";
                            }
                            else if (nameParts.length == 2) {
                                user.userDetails.firstName = nameParts[0];
                                user.userDetails.lastName = nameParts[1];
                            }
                            else if (nameParts.length > 2) {
                                let middleName = "";

                                let k = 0;

                                for (const names of nameParts) {
                                    if (k != 0 && k != nameParts.length - 1)
                                        middleName += `${names} `;
                                    k++;
                                }

                                user.userDetails.firstName = nameParts[0];
                                user.userDetails.middleName = middleName ? middleName.trim() : "";
                                user.userDetails.lastName = nameParts[nameParts.length - 1];
                            }
                        }

                        await user.save();

                        return {
                            userId: digilocker.userId,
                            loanId: digilocker.loanId,
                            partnerId: loan.partnerId,
                            success: true,
                            message: message,
                            isDigilocker: true
                        }
                    }
                    else
                        return {
                            userId: digilocker.userId,
                            loanId: digilocker.loanId,
                            partnerId: loan.partnerId,
                            success: true,
                            message: "PAN number doesn't match with existing record",
                            isDigilocker: false
                        }
                }
                else {
                    digilocker.details = {};
                    digilocker.updatedOn = Date.now();
                    digilocker.status = status;
                    digilocker.error = message;
                    digilocker.mediaId = null;

                    await digilocker.save();

                    return {
                        userId: digilocker.userId,
                        loanId: digilocker.loanId,
                        partnerId: loan.partnerId,
                        success: true,
                        message: message,
                        isDigilocker: false
                    }
                }
            }
            else
                return {
                    userId: digilocker.userId,
                    loanId: digilocker.loanId,
                    partnerId: loan.partnerId,
                    success: true,
                    message: "Digilocker flow not completed.",
                    isDigilocker: false
                }
        }
        else
            return {
                userId: null,
                loanId: null,
                success: false,
                message: "Record not found",
                isDigilocker: false
            }
    }
    catch (ex) {
        saveErrorLog("updateDigilockerResponse", apiPath, { status, digio_doc_id, message }, ex.stack ? ex.stack.toString().replace(/\r?\n|\r/g, " ") : ex);

        return {
            userId: null,
            loanId: null,
            success: false,
            message: ex.message,
            isDigilocker: false
        }
    }
}

module.exports.updateDigilockerResponse = updateDigilockerResponse;
