/**
 * Utility functions for phone number handling
 */

/**
 * Normalizes a phone number by standardizing the format
 * This function preserves the country code but ensures consistent formatting
 * @param {string} phone - The phone number to normalize
 * @returns {string|null} - The normalized phone number or null if invalid
 */
function normalizePhoneNumber(phone) {
  if (!phone) return null;

  // Remove all non-digit characters
  let digits = phone.replace(/\D/g, '');

  // Ensure we have a valid phone number (at least 8 digits)
  // Qatar numbers are typically 8 digits, while many other countries use 10+
  if (digits.length < 8) return null;

  // Instead of removing country codes, we'll keep the full number
  // This ensures we don't lose information about which country the number is from

  return digits;
}

/**
 * Checks if a phone number exists in the database, considering variations with/without country code
 * @param {Object} model - The Mongoose model to query
 * @param {string} phone - The phone number to check
 * @returns {Promise<Object|null>} - The existing user or null
 */
async function findUserByPhone(model, phone) {
  if (!phone) return null;

  const normalizedPhone = normalizePhoneNumber(phone);
  if (!normalizedPhone) return null;

  // First, try an exact match
  let user = await model.findOne({ mobileNo: normalizedPhone });
  if (user) return user;

  // If no exact match, try to match the last 8 digits (for Qatar numbers)
  // or last 10 digits (for other countries)
  const lastDigits = normalizedPhone.slice(-8); // For Qatar

  if (lastDigits.length >= 8) {
    // Use a regex to match numbers ending with these digits
    const regex = new RegExp(lastDigits + '$');
    user = await model.findOne({ mobileNo: regex });
    if (user) return user;
  }

  // If normalizedPhone is longer than 10 digits, also try matching the last 10 digits
  if (normalizedPhone.length > 10) {
    const last10Digits = normalizedPhone.slice(-10);
    const regex10 = new RegExp(last10Digits + '$');
    user = await model.findOne({ mobileNo: regex10 });
    if (user) return user;
  }

  return null;
}

module.exports = {
  normalizePhoneNumber,
  findUserByPhone
};
