/**
 * Authentication helper functions
 */
const crypto = require('crypto');
const bcrypt = require('bcrypt');

/**
 * Generates a secure random password
 * @returns {string} A random password
 */
function generateRandomPassword() {
  return crypto.randomBytes(12).toString('hex');
}

/**
 * Hashes a password using bcrypt
 * @param {string} password - The password to hash
 * @returns {Promise<string>} The hashed password
 */
async function hashPassword(password) {
  const saltRounds = 10;
  return await bcrypt.hash(password, saltRounds);
}

/**
 * Compares a password with a hashed password
 * @param {string} password - The password to compare
 * @param {string} hashedPassword - The hashed password
 * @returns {Promise<boolean>} True if the password matches
 */
async function comparePassword(password, hashedPassword) {
  return await bcrypt.compare(password, hashedPassword);
}

/**
 * Sends a password setup email to a user
 * @param {string} email - The user's email
 * @param {string} token - The password setup token
 * @returns {Promise<boolean>} True if the email was sent
 */
async function sendPasswordSetupEmail(email, token) {
  // Implement your email sending logic here
  // This is a placeholder
  console.log(`Sending password setup email to ${email} with token ${token}`);
  return true;
}

/**
 * Generates a password setup token
 * @returns {string} A password setup token
 */
function generatePasswordSetupToken() {
  return crypto.randomBytes(32).toString('hex');
}

/**
 * Checks if a user needs to set up a password
 * @param {Object} user - The user object
 * @returns {boolean} True if the user needs to set up a password
 */
function needsPasswordSetup(user) {
  // If the user has no password but has an email, they need to set up a password
  return !user.password && user.email;
}

module.exports = {
  generateRandomPassword,
  hashPassword,
  comparePassword,
  sendPasswordSetupEmail,
  generatePasswordSetupToken,
  needsPasswordSetup
};
