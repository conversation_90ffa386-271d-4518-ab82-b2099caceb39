/**
 * Invoice validation and duplicate detection utilities
 */

/**
 * Normalizes a string for comparison by removing special characters,
 * extra spaces, and converting to lowercase
 * @param {string} str - The string to normalize
 * @returns {string} Normalized string
 */
function normalizeString(str) {
  if (!str) return '';
  return str.toString()
    .toLowerCase()
    .replace(/[^\w\s]/g, '') // Remove special characters
    .replace(/\s+/g, ' ')    // Replace multiple spaces with single space
    .trim();
}

/**
 * Calculates Levenshtein distance between two strings
 * @param {string} str1 - First string
 * @param {string} str2 - Second string
 * @returns {number} The edit distance between the strings
 */
function levenshteinDistance(str1, str2) {
  const m = str1.length;
  const n = str2.length;
  
  // Create a matrix of size (m+1) x (n+1)
  const dp = Array(m + 1).fill().map(() => Array(n + 1).fill(0));
  
  // Fill the first row and column
  for (let i = 0; i <= m; i++) dp[i][0] = i;
  for (let j = 0; j <= n; j++) dp[0][j] = j;
  
  // Fill the rest of the matrix
  for (let i = 1; i <= m; i++) {
    for (let j = 1; j <= n; j++) {
      if (str1[i - 1] === str2[j - 1]) {
        dp[i][j] = dp[i - 1][j - 1];
      } else {
        dp[i][j] = 1 + Math.min(
          dp[i - 1][j],     // deletion
          dp[i][j - 1],     // insertion
          dp[i - 1][j - 1]  // substitution
        );
      }
    }
  }
  
  return dp[m][n];
}

/**
 * Calculates string similarity as a percentage (0-100)
 * @param {string} str1 - First string
 * @param {string} str2 - Second string
 * @returns {number} Similarity percentage
 */
function calculateStringSimilarity(str1, str2) {
  if (!str1 && !str2) return 100; // Both empty = 100% similar
  if (!str1 || !str2) return 0;   // One empty = 0% similar
  
  const normalizedStr1 = normalizeString(str1);
  const normalizedStr2 = normalizeString(str2);
  
  if (normalizedStr1 === normalizedStr2) return 100;
  
  const maxLength = Math.max(normalizedStr1.length, normalizedStr2.length);
  if (maxLength === 0) return 100;
  
  const distance = levenshteinDistance(normalizedStr1, normalizedStr2);
  return Math.round((1 - distance / maxLength) * 100);
}

/**
 * Checks if two amounts are similar within a given percentage threshold
 * @param {number|string} amount1 - First amount
 * @param {number|string} amount2 - Second amount
 * @param {number} thresholdPercent - Percentage threshold (default: 1%)
 * @returns {boolean} True if amounts are similar
 */
function areAmountsSimilar(amount1, amount2, thresholdPercent = 1) {
  const num1 = parseFloat(amount1);
  const num2 = parseFloat(amount2);
  
  if (isNaN(num1) || isNaN(num2)) return false;
  if (num1 === num2) return true;
  
  const maxAmount = Math.max(Math.abs(num1), Math.abs(num2));
  const difference = Math.abs(num1 - num2);
  const percentDifference = (difference / maxAmount) * 100;
  
  return percentDifference <= thresholdPercent;
}

/**
 * Checks if two dates are similar within a given day threshold
 * @param {string|Date} date1 - First date
 * @param {string|Date} date2 - Second date
 * @param {number} thresholdDays - Day threshold (default: 1 day)
 * @returns {boolean} True if dates are similar
 */
function areDatesSimilar(date1, date2, thresholdDays = 1) {
  try {
    const d1 = new Date(date1);
    const d2 = new Date(date2);
    
    if (isNaN(d1.getTime()) || isNaN(d2.getTime())) return false;
    
    const diffTime = Math.abs(d2 - d1);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return diffDays <= thresholdDays;
  } catch (error) {
    return false;
  }
}

/**
 * Validates an invoice number format
 * @param {string} invoiceNumber - The invoice number to validate
 * @returns {boolean} True if the format is valid
 */
function isValidInvoiceNumberFormat(invoiceNumber) {
  if (!invoiceNumber) return false;
  
  // Basic validation: at least 3 characters, alphanumeric with some special chars
  const regex = /^[a-zA-Z0-9\-_\/\.]{3,50}$/;
  return regex.test(invoiceNumber);
}

/**
 * Validates that an amount is positive
 * @param {number|string} amount - The amount to validate
 * @returns {boolean} True if the amount is valid and positive
 */
function isValidAmount(amount) {
  const num = parseFloat(amount);
  return !isNaN(num) && num > 0;
}

/**
 * Calculates a weighted similarity score between two invoices
 * @param {Object} invoice1 - First invoice object
 * @param {Object} invoice2 - Second invoice object
 * @returns {Object} Similarity score and details
 */
function calculateInvoiceSimilarity(invoice1, invoice2) {
  // Define weights for different fields
  const weights = {
    invoiceNumber: 0.5,  // 50% weight for invoice number
    totalAmount: 0.2,    // 20% weight for amount
    supplierName: 0.15,  // 15% weight for supplier
    invoiceDate: 0.1,    // 10% weight for invoice date
    customerName: 0.05   // 5% weight for customer name
  };
  
  // Calculate individual field similarities
  const invoiceNumberSimilarity = calculateStringSimilarity(
    invoice1.invoiceNumber, 
    invoice2.invoiceNumber
  );
  
  const amountSimilarity = areAmountsSimilar(
    invoice1.totalAmount, 
    invoice2.totalAmount, 
    2
  ) ? 100 : 0;
  
  const supplierSimilarity = calculateStringSimilarity(
    invoice1.supplierName, 
    invoice2.supplierName
  );
  
  const dateSimilarity = areDatesSimilar(
    invoice1.invoiceDate, 
    invoice2.invoiceDate, 
    2
  ) ? 100 : 0;
  
  const customerSimilarity = calculateStringSimilarity(
    invoice1.customerName, 
    invoice2.customerName
  );
  
  // Calculate weighted score
  const weightedScore = 
    (invoiceNumberSimilarity * weights.invoiceNumber) +
    (amountSimilarity * weights.totalAmount) +
    (supplierSimilarity * weights.supplierName) +
    (dateSimilarity * weights.invoiceDate) +
    (customerSimilarity * weights.customerName);
  
  return {
    score: weightedScore,
    details: {
      invoiceNumber: invoiceNumberSimilarity,
      amount: amountSimilarity,
      supplier: supplierSimilarity,
      date: dateSimilarity,
      customer: customerSimilarity
    },
    isExactMatch: invoiceNumberSimilarity === 100
  };
}

/**
 * Checks if an invoice is likely a duplicate based on similarity score
 * @param {Object} newInvoice - The new invoice to check
 * @param {Array<Object>} existingInvoices - Array of existing invoices
 * @param {number} threshold - Similarity threshold (default: 80)
 * @returns {Object|null} Potential duplicate or null if none found
 */
function findPotentialDuplicate(newInvoice, existingInvoices, threshold = 80) {
  if (!existingInvoices || !existingInvoices.length) return null;
  
  let highestSimilarity = 0;
  let potentialDuplicate = null;
  let similarityDetails = null;
  
  for (const existingInvoice of existingInvoices) {
    const similarity = calculateInvoiceSimilarity(newInvoice, existingInvoice);
    
    if (similarity.score > highestSimilarity) {
      highestSimilarity = similarity.score;
      potentialDuplicate = existingInvoice;
      similarityDetails = similarity.details;
    }
    
    // If we find an exact invoice number match, return immediately
    if (similarity.isExactMatch) {
      return {
        invoice: existingInvoice,
        score: similarity.score,
        details: similarity.details,
        isExactMatch: true
      };
    }
  }
  
  if (highestSimilarity >= threshold) {
    return {
      invoice: potentialDuplicate,
      score: highestSimilarity,
      details: similarityDetails,
      isExactMatch: false
    };
  }
  
  return null;
}

/**
 * Validates required invoice fields
 * @param {Object} invoice - The invoice to validate
 * @returns {Object} Validation result with errors if any
 */
function validateInvoiceFields(invoice) {
  const errors = [];
  
  // Check required fields
  if (!invoice.invoiceNumber) {
    errors.push({ field: 'invoiceNumber', message: 'Invoice number is required' });
  } else if (!isValidInvoiceNumberFormat(invoice.invoiceNumber)) {
    errors.push({ field: 'invoiceNumber', message: 'Invalid invoice number format' });
  }
  
  if (!invoice.totalAmount) {
    errors.push({ field: 'totalAmount', message: 'Total amount is required' });
  } else if (!isValidAmount(invoice.totalAmount)) {
    errors.push({ field: 'totalAmount', message: 'Amount must be a positive number' });
  }
  
  if (!invoice.supplierName) {
    errors.push({ field: 'supplierName', message: 'Supplier name is required' });
  }
  
  if (!invoice.invoiceDate) {
    errors.push({ field: 'invoiceDate', message: 'Invoice date is required' });
  } else {
    const date = new Date(invoice.invoiceDate);
    if (isNaN(date.getTime())) {
      errors.push({ field: 'invoiceDate', message: 'Invalid invoice date format' });
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

module.exports = {
  normalizeString,
  calculateStringSimilarity,
  areAmountsSimilar,
  areDatesSimilar,
  isValidInvoiceNumberFormat,
  isValidAmount,
  calculateInvoiceSimilarity,
  findPotentialDuplicate,
  validateInvoiceFields
};
