/**
 * Utility functions for normalizing user data
 */

/**
 * Normalizes an email address by trimming whitespace and converting to lowercase
 * Returns null for empty strings or undefined values
 * 
 * @param {string} email - The email address to normalize
 * @returns {string|null} - The normalized email or null
 */
function normalizeEmail(email) {
  if (!email || email === '') return null;
  return email.trim().toLowerCase();
}

/**
 * Normalizes a phone number by removing spaces, dashes, and other non-digit characters
 * Returns null for empty strings or undefined values
 * 
 * @param {string} phone - The phone number to normalize
 * @returns {string|null} - The normalized phone number or null
 */
function normalizePhone(phone) {
  if (!phone || phone === '') return null;
  
  // Remove spaces, dashes, parentheses, and plus signs
  let normalized = phone.replace(/[\s\-\(\)\+]/g, '');
  
  // If it's just an empty string after normalization, return null
  if (normalized === '') return null;
  
  return normalized;
}

/**
 * Checks if a user exists by email, phone, or Google ID
 * 
 * @param {Object} model - The InvoiceFinancingUsers model
 * @param {Object} criteria - Object containing email, phone, and/or googleId
 * @returns {Promise<Object|null>} - The existing user or null
 */
async function findExistingUser(model, { email, phone, googleId }) {
  const query = {
    $or: []
  };
  
  if (email) {
    query.$or.push({ email: normalizeEmail(email) });
  }
  
  if (phone) {
    query.$or.push({ mobileNo: normalizePhone(phone) });
  }
  
  if (googleId) {
    query.$or.push({ googleId });
  }
  
  // If no criteria provided, return null
  if (query.$or.length === 0) return null;
  
  return await model.findOne(query);
}

/**
 * Generates a specific error message for duplicate key errors
 * 
 * @param {Error} error - The MongoDB error object
 * @returns {Object} - Object with field and message properties
 */
function getDuplicateKeyErrorInfo(error) {
  if (!error || error.code !== 11000) {
    return { field: 'unknown', message: 'An error occurred' };
  }
  
  // Extract the field name from the error message
  const errorMessage = error.message || '';
  let field = 'account';
  
  if (errorMessage.includes('email')) {
    field = 'email';
  } else if (errorMessage.includes('mobileNo')) {
    field = 'mobile';
  } else if (errorMessage.includes('googleId')) {
    field = 'Google account';
  }
  
  const fieldName = field === 'email' ? 'Email address' : 
                    field === 'mobile' ? 'Mobile number' : 
                    field === 'Google account' ? 'Google account' : 'Account';
  
  return {
    field,
    message: `This ${fieldName.toLowerCase()} is already registered. Please login or use a different ${fieldName.toLowerCase()}.`
  };
}

module.exports = {
  normalizeEmail,
  normalizePhone,
  findExistingUser,
  getDuplicateKeyErrorInfo
};
