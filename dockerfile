# Stage 1: Build
FROM node:18-alpine AS build

# Set the working directory in the container
WORKDIR /usr/src/app

# Copy package.json and package-lock.json (if available)
COPY package*.json ./

# Install dependencies
RUN npm install

# Copy the rest of the application code
COPY . .

# Explicitly copy the models directory
COPY models /usr/src/app/models

# Build the application (if applicable)
# RUN npm run build

# Stage 2: Production
FROM node:18-alpine

# Set the working directory in the container
WORKDIR /usr/src/app

# Copy only the necessary files from the build stage
COPY --from=build /usr/src/app /usr/src/app

# Install only production dependencies
RUN npm install --omit=dev

# Install Chromium
RUN apk add --no-cache chromium

# Expose the port the app runs on
EXPOSE 8080

# Define the command to run the app
CMD [ "node", "index.js" ]