const express = require("express");
const app = express();
const winston = require("winston");
const config = require("config");
const os = require("os");
const cluster = require("cluster");
var cookieParser = require('cookie-parser');
const { saveErrorLog } = require("./services/ops/common/masters");
const { apiLogs } = require('./middleware/apiLogs')
const apiSecurity = require('./middleware/api_security');
const Logger = require("./middleware/logger");
const rTracer = require('cls-rtracer')
const { v4: uuidv4 } = require('uuid');




const logger = new Logger();
let bodyParser = require("body-parser");
app.use(bodyParser.json({ limit: '10mb' }));


// nginx reverse proxy(unsafe if app is bound to the public ip address)
// app.enable('trust proxy');
// app.set('trust proxy',function(){ return true; });
    
// cors
// nginx reverse proxy(unsafe if app is bound to the public ip address)
app.enable('trust proxy');
app.set('trust proxy', function () { return true; });
app.use(cookieParser());
app.use(express.json());

app.use(function (req, res, next) {
  res.setHeader("Access-Control-Allow-Origin", "*");
  res.setHeader("X-Frame-Options", "SAMEORIGIN");
  res.setHeader("Access-Control-Allow-Credentials", true);
  res.setHeader("Access-Control-Allow-Origin", "*");
  res.setHeader("Access-Control-Allow-Methods", "GET,PUT,POST,DELETE");
  res.setHeader(
    "Access-Control-Allow-Headers",
    "X-Requested-With, X-HTTP-Method-Override, Content-Type,Accept,content-type,application/json"
  );

  // res.setHeader(
  //   'Content-Security-Policy',
  //   "default-src 'self'; font-src 'self'; img-src 'self'; script-src 'self'; style-src 'self'; frame-src 'self'"
  // );
  next();
});



// app.use(apiLogs); // API LOGS MIDDLEWARE


app.use(express.static('portal/build'));

const path = require("path");

const clusterWorkerSize = os.cpus().length;
logger.logs("clusterWorkerSize", clusterWorkerSize);
app.use(apiSecurity.middleware);
app.use(rTracer.expressMiddleware({
  useHeader: false,
  requestIdFactory: (req) => ({
    id : uuidv4(),
    url: req.url,
    ip : req.headers['x-forwarded-for'] || req.socket.remoteAddress,
    userAgent : req.headers["user-agent"],
    method : req.method
  }),
}));  

app.use((req, res, next) => {
  //console.log("---->>>>>p",req.method);
  req.identifier = uuidv4();
  const b = req.body ? JSON.stringify(req.body) : " "
  const logString = `a request has been made ${req?.url}`;
  const poyloadString =  `payload ${b}`;
  logger.log(logString, "http");
  logger.log(poyloadString, "silly");
  next();
});
// test
app.get('/api/health', (req, res) => {
  return res.json({message: 'Server is healthy aryan!'})
});
 
// //require("./startup/logging")(); //uncomment while going live
require("./startup/cors")(app);
require("./startup/routes")(app);
require("./startup/config")();
require("./startup/debug")(app);
require("./startup/validation")();
require("./startup/views")(app);
require("./startup/db")();
// //require("./startup/db_JumpServer");

var partnerDBController = require('./database/partner-data/partner-db');
var creditDBController = require('./database/creditapi/credit-db');
var fundfinaDBController = require('./database/fundfina/fundfina-db');
var tinyurl = require('./database/tinyurl/tinyurl-db');

partnerDBController.init();
creditDBController.init();
fundfinaDBController.init();
tinyurl.init();

const port = process.env.PORT || 8080;

app.listen(port, () => {  
  logger.log(`the server started listining on port ${port} with  env: ${config.get('dbConfig.NODE_ENV')}`, "info");
});