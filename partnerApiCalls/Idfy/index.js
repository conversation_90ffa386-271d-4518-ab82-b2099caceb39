
const axios = require("axios");
const { v4: uuidv4 } = require('uuid');


class Idfy {

  constructor() {
    (this.baseHeader = {
      "Content-Type": "application/json",
    }),
      (this.header = {
        ...this.baseHeader,
        'account-id': 'ec7dd58f2c43/d61d41d3-d976-4c84-bf3c-0302b748d6f4',
        'api-key': 'b9bbe7fa-fc43-4e1c-8a8c-fb9b08d440b9',
      });
    
  }
  //udyog verification
  async udyogVerification(udyogNo) {
    const config = {
      method: "POST",
      baseURL: `https://eve.idfy.com/v3/tasks/async/verify_with_source/udyog_aadhaar`,
      headers: this.header,
      data : {
        "task_id": uuidv4(),
        "group_id": uuidv4(),
        "data": {
          "uam_number": udyogNo
        }
      }
    };

    try {
      let response = await axios(config);
      console.log("udyogVerification", response?.data)
      return response
    } catch (err) {
      console.log('err udyogVerification', err.response)
      return err.response;
    }
  }

  async verificationFromRequestId(requestId) {
    const config = {
      method: "GET",
      baseURL: `https://eve.idfy.com/v3/tasks?request_id=${requestId}`,
      headers: this.header,
    };

    try {
      let response = await axios(config);
      console.log("verificationFromRequestId", response?.data)
      return response
    } catch (err) {
      console.log('err verificationFromRequestId', err.response)
      return err.response;
    }
  }

  //udyam verification
  async udyamVerification(udyamNo) {
    const config = {
      method: "POST",
      baseURL: `https://eve.idfy.com/v3/tasks/async/verify_with_source/udyam_aadhaar`,
      headers: this.header,
      data : {
        "task_id": uuidv4(),
        "group_id": uuidv4(),
        "data": {
          "uam_number": udyamNo
        }
      }
    };

    try {
      let response = await axios(config);
      console.log("udyamVerification", response?.data)
      return response
    } catch (err) {
      console.log('err udyogVerification', err.response)
      return err.response;
    }
  }
  
}


module.exports = Idfy




