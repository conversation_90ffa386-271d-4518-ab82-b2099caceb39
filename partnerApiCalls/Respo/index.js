
const axios = require("axios");
const { v4: uuidv4 } = require('uuid');


class Respo {

    constructor() {
        (this.baseHeader = {
            "Content-Type": "application/json",
        }),
            (this.header = {
                ...this.baseHeader,
            });

    }

    //respo customerEligibility verification
    async customerEligibility(body) {
        const config = {
            method: "POST",
            baseURL: `https://prod.zype.co.in/attribution-service/api/v1/underwriting/customerEligibility`,
            headers: this.header,
            data: {
                ...body,
                partnerId: "6621091a8badf66b41dd0641"
            }
        };

        try {
            let response = await axios(config);
            // console.log("customerEligibility", response)
            return response
        } catch (err) {
            console.log('err customerEligibility', err.response)
            return err.response;
        }
    }


    async preApprovalOffer(body) {
        const config = {
            method: "POST",
            baseURL: `https://prod.zype.co.in/attribution-service/api/v1/underwriting/preApprovalOffer`,
            headers: this.header,
            data: {
                ...body,
                partnerId: "6621091a8badf66b41dd0641"
            }
        };

        try {
            let response = await axios(config);
            console.log("Pre-approval offer", response)
            return response
        } catch (err) {
            console.log('err Pre-approval offer', err.response)
            return err.response;
        }
    }

}


module.exports = Respo




