
const axios = require("axios");
const { v4: uuidv4 } = require('uuid');


class LnTFinance {

    constructor() {
        (this.baseHeader = {
            "Content-Type": "application/json",
        }),
            (this.header = {
                ...this.baseHeader,
                // ---- PROD credentails
                'Client_Id': 'XIPFRtb1wy-rlblw0AXEH-IHE0V3ut1c-qur1u73DY0',
                'Client_Secret_Key': 'cgja8fynve879ULWKSPMSTNOLGIJ6MVRJ9185iyiqegpyigUXDKSAD',
                'Partner_Name': 'CLEAG_FUNDFINA_MARKETPLACE',
            });

    }
    // customer Leadgenration
    async leadGeneration(body) {
        try {
            const config = {
                method: "POST",
                baseURL: `https://apicloud.ltfs.com:1129/LTFSD2cApp/api/CLEAG/LeadGenerate`,
                headers: this.header,
                data: {
                    ...body,
                }
            };
            let response = await axios(config);
            return response
        } catch (err) {
            console.log('err customerEligibility', err.response)
            return err
        }
    }

    // checking customer eligibility
    async customerEligibility(body) {
        const config = {
            method: "POST",
            baseURL: `https://apicloud.ltfs.com:1129/LTFSD2cApp/api/CLEAG/CheckEligibility`,
            headers: this.header,
            data: {
                ...body,
            }
        };

        try {
            let response = await axios(config);
            return response
        } catch (err) {
            console.log('err customerEligibility', err.response)
            return err;
        }
    }

    // checking customer eligibility
    async checkStatus(body) {
        const config = {
            method: "POST",
            baseURL: `https://apicloud.ltfs.com:1129/LTFSD2cApp/api/CLEAG/CheckStatus`,
            headers: this.header,
            data: {
                ...body,
            }
        };

        try {
            let response = await axios(config);
            return response
        } catch (err) {
            console.log('err customerEligibility', err.response)
            return err;
        }
    }

}


module.exports = LnTFinance



