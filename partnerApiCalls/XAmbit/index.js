
const axios = require("axios");
const config = require("config");
const moment = require('moment');
const fs = require('fs');


class XAmbit {

  constructor() {
    (this.baseHeader = {
      "Content-Type": "application/json",
    }),
      (this.header = {
        ...this.baseHeader,
        'X-HOST': config.get("Xambit.xHost"),
        'X-KEY': config.get("Xambit.xKey"),
      }),
      (this.header2 = {
        ...this.baseHeader,
        'X-HOST': 'fundfina.xambit.io',
        'X-KEY': 'key-01HFBCJ1WTSX9BWC0XNBPE17JE-01HFBCJ1WT7F48NTCKHPN2SP2T',
      });
    this.baseURL = config.get("Xambit.baseUrl")
  }
  //get public  gstDetails
  async getGstDetails(gstNo) {
    const config = {
      method: "GET",
      baseURL: `${this.baseURL}/${gstNo}?public=true`,
      headers: this.header,
    };

    try {
      console.log("gstdetails")
      let response = await axios(config);
      console.log("xmbitt", response)
      return response
    } catch (err) {
      console.log('err', gstNo, err.response)
      return err.response;
    }
  }
  // //create gst entry in xambit 
  // async createGstEntry(gstNo) {
  //   const config = {
  //     method: "POST",
  //     baseURL: `${this.baseURL}/${gstNo}`,
  //     headers: this.header,
  //   };

  //   try {
  //     let response = await axios(config);
  //     console.log("xmbit", response)
  //     return response
  //   } catch (err) {
  //     console.log('err', gstNo, err.response)
  //     return err.response;
  //   }
  // }


  //check gst details exist in xambit or not
  async checkGstNo(gstNo) {
    const config = {
      method: "GET",
      baseURL: `${this.baseURL}/${gstNo}/exists`,
      headers: this.header,
    };

    try {
      let response = await axios(config);

      return response
    } catch (err) {
      console.log(err)
      return err.response;
    }
  }

  // create new analysis in xambit and send otp for private data
  async createNewAnayalysisAndSendOtp(gstNo, gstUsername) {
    let endDate = moment(new Date()).format('YYYY-MM-DD');
    let startingDate = moment(new Date()).subtract(12, 'months').format("YYYY-MM-DD");
    const config = {
      method: "POST",
      baseURL: `${this.baseURL}/${gstNo}/otp`,
      headers: this.header,
      data: {
        gst_portal_user_name: gstUsername,
        src: "API",
        period: { start: startingDate, end: endDate },
        hook: {
          url: "https://uat.fundfina.com/ops/gst/privateDataWebhook",
          method: "POST"
        }

      }
    };

    try {
      console.log("send otp")
      let response = await axios(config);
      console.log("exist", response)
      return response
    } catch (err) {
      console.log(err);
      return err.response;
    }
  }

  // validate otp for private data
  async validateOtp(gstNo, gstUsername, otp) {
    const config = {
      method: "POST",
      baseURL: `${this.baseURL}/${gstNo}/otp/valid`,
      headers: this.header,
      data: {
        gst_portal_user_name: gstUsername,
        otp: otp,
        src: "API"
      }
    };

    try {
      let response = await axios(config);

      return response
    } catch (err) {
      console.log(err);
      return err.response;
    }
  }

  //gst data xAmbit analysis
  async gstDataAnalysis(gstNo) {
    const config = {
      method: "POST",
      baseURL: `${this.baseURL}/${gstNo}/basic`,
      headers: this.header,
      // data: {
      //   fin_years: 2,
      //   hook: `https://uat.fundfina.com/ops/gst/privateDataWebhook`
      // }
    };

    try {
      let response = await axios(config);
      console.log('response here', response)
      return response;
    } catch (err) {
      console.log(err);
      return err.response;
    }
  }


  //Bankstatement Get Analysis
 /* async getDataAnalysis(collectionId) {
    try {
      const axiosConfig = {
        method: 'get',
        maxBodyLength: Infinity,
        url: `https://xapp-bank-statement-5qxdkkqowq-el.a.run.app/api/x-01HMX6W3BDDNYTVN35CY7Q634V/collection/${collectionId}`,
        headers: this.header
      };

      const response = await axios.request(axiosConfig);
      return response.data;
    } catch (error) {
      console.error(error);
      if (error.response) {
        return { error: error.response.data, status: error.response.status };
      } else {
        return { error: 'Internal Server Error', status: 500 };
      }
    }
   }
   */

  // Bankstatement createNewAnalysis
 /* async createNewAnalysis() {
    try {
      let data = JSON.stringify({
        "name": "Prashant-API-Test",
       "aggregate": true,
        "infer": false,
        "hook": {
          "url": "https://413b-103-150-138-56.ngrok-free.app/ops/bankstatement/Bankstatementwebhook",
         "method": "POST",
          "headers": {
           "test_key": "test_value"
          }
       }
      });

      let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: 'https://xapp-bank-statement-5qxdkkqowq-el.a.run.app/api/x-01HMX6W3BDDNYTVN35CY7Q634V/collection',
        headers: this.header,
        data: data
      };

      const response = await axios.request(config);
      console.log(JSON.stringify(response.data));
      return { data: response.data };
    } catch (error) {
      console.log(error);
    }
   }
*/

  //Bankstatement  upload file
 /*async uploadFile(filename, filePath, uniqueId) {
    try {
      console.log("uniqueId inside xambit", uniqueId)
      const fileBuffer = this.getFileBuffer(filePath);
      const fileByteArray = Array.from(new Uint8Array(fileBuffer));
      console.log("fileByteArray", fileByteArray);

      const payload = {
        file: {
          blob: fileByteArray,
          category: 5,
          name: filename,
        },
      };

      // Axios post request call to Xambit API
      const response = await axios.post(`https://xapp-bank-statement-5qxdkkqowq-el.a.run.app/api/x-01HMX6W3BDDNYTVN35CY7Q634V/collection/${uniqueId}`, payload, {
        headers: this.header,
      });

      console.log("response in xambit", response)
      console.log(JSON.stringify(response.data));
      console.log("response.data", response.data);
      return { data: response.data };
    } catch (error) {
      console.log(`Error from xAmbit API: ${error.message}`);
      throw error;
    }
  }

  getFileBuffer(filePath) {
    const fileBuffer = fs.readFileSync(filePath);
    console.log("getfilebufferinside");
    const result = Array.from(new Uint8Array(fileBuffer));
    console.log("result is", result);
    return result;
  }
  */
}



module.exports = XAmbit




