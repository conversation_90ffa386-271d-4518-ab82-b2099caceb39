const axios = require("axios");

class PayMe {
  constructor() {
    (this.baseHeader = {
      "Content-Type": "application/json",
    }),
      (this.header = {
        ...this.baseHeader,
      });
  }
  async registerUser(body) {
    // Sample body:
    // {
    //     "email": "<EMAIL>",
    //     "merchant_id": "98cdbb76-30c2-4bc8-9656-774350eabe8d",
    //     "phone_number": "8890210575",
    //     "full_name": "Manit"
    // }

    const config = {
      method: "POST",
      baseURL: `https://weedori.paymeindia.in/api/authentication/register_user_merchant/`,
      headers: this.header,
      data: {
        ...body,
      },
    };

    try {
      let response = await axios(config);
      console.log("registerUser : Registered user, response: ", response);
      return response;
    } catch (err) {
      console.log(
        "registerUser : Error while registering user merchant:",
        err.response
      );
      return err.response;
    }
  }

  async checkUserExistence(body) {
    //Sample body:
    // {
    // //   "email": "<EMAIL>", (this was commented out in the body!)
    //      "merchant_id": "98cdbb76-30c2-4bc8-9656-774350eabe8d",
    //      "pan_card_number": "**********"
    // }
    const config = {
      method: "POST",
      baseURL: `https://weedori.paymeindia.in/api/authentication/check_user_merchant/`,
      headers: this.header,
      data: {
        ...body,
      },
    };

    try {
      let response = await axios(config);
      console.log(
        "checkUserExistence: Checker user merchant, response: ",
        response
      );
      return response;
    } catch (err) {
      console.log(
        "checkUserExistence: Error while checking user merchant:",
        err.response
      );
      return err.response;
    }
  }
}

module.exports = PayMe;
