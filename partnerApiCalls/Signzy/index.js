const axios = require("axios");

class Signzy {

  async searchByGstn(body) {

    const config = {
      method: "POST",
      baseURL: `https://api-preproduction.signzy.app/api/v3/gstn/gstndetailed`,
      headers: {
        "Authorization": "qI2WDJbk3jTdJCDcob3Wf9TmaeQNy1si",
        "x-client-unique-id": "<EMAIL>",
        "Content-Type": "application/json"
      },
      data: {
        ...body,
      },
    };

    try {
      let response = await axios(config)
      console.log(
        "signzySearchByGstn response: ",
        response
      );
      return response;
    } catch (err) {
      console.log(
        "signzySearchByGstn: Error while checking user GSTN:",
        err.response
      );
      return err.response;
    }

  }

}

module.exports = Signzy;
