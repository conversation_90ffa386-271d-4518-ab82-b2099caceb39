const express = require("express");
const users = require("../routes/ops/registration/users");
const home = require("../routes/ops/registration/home");
const auth = require("../routes/ops/registration/auth");
const error = require("../middleware/error");
const helmet = require("helmet");
const verification = require("../routes/ops/registration/verfication");
const loan = require("../routes/ops/loan/loan");
const masters = require("../routes/ops/common/masters");
const admin = require("../routes/ops/admin/admin");
const disbursal = require("../routes/ops/loan/disbursal");
const partner = require("../routes/ops/partner/partner");
const testPartner = require("../routes/ops/partner/testPartner");
const schedular = require("../routes/ops/partner/schedular");
const leadInfo = require("../routes/ops/partner/leadInfo");
const digio = require("../routes/ops/verification/digioApi");
const karix = require("../routes/ops/whatsApp/karix");
const awsServices = require("../routes/ops/aws/services");
const lendingPartner = require("../routes/ops/partner/lendingPartner");
const smsService = require("../routes/ops/sms/sms");
const reports = require("../routes/ops/partner/reports");
const settlement = require("../routes/ops/settlement/settlement");
const borrowingPartnerTesting = require("../routes/ops/testing/borrowingPartnerApi");
const mgmReports = require('../routes/ops/reports/reports')
const dbJob = require('../routes/jobs/db-job/db-job')
const Payment = require('../routes/ops/payment/payment')
const Agent = require('../routes/ops/agent/agent')
const noc = require('../routes/ops/noc/noc')
const user = require('../routes/ops/user/userCentricData');
const gst = require('../routes/ops/gst/index');
const bankstatement = require('../routes/ops/bankstatement/index');
const invoiceFinancing = require('../routes/ops/invoiceFinancing/index');
 
module.exports = function (app) {
  app.use(express.json({ limit: "5000mb", extended: true }));
  app.use(helmet());
  app.use("/ops/users", users);
  app.use("/", home);
  app.use("/ops/auth", auth);
  app.use("/ops/verification", verification);
  app.use("/ops/loan", loan);
  app.use("/ops/masters", masters);
  app.use("/ops/admin", admin);
  app.use("/ops/disbursal", disbursal);
  app.use("/ops/partner", partner);
  app.use("/ops/lendingpartner", lendingPartner);
  app.use("/ops/testpartner", testPartner);
  app.use("/ops/schedular", schedular);
  app.use("/ops/leadinfo", leadInfo);
  app.use("/ops/digio", digio);
  app.use("/ops/karix", karix);
  app.use("/ops/aws", awsServices);
  app.use("/ops/sms", smsService);
  app.use("/ops/reports", reports);
  app.use("/ops/mgmReports", mgmReports); // Management reports
  app.use("/ops/settlement", settlement);
  app.use("/job/dbjob", dbJob);
  app.use("/ops/testingBorrowingPartner", borrowingPartnerTesting);
  app.use("/ops/agent", Agent)
  app.use("/ops/payment", Payment);
  app.use("/ops/noc", noc);
  app.use("/ops/user", user);
  app.use("/ops/gst", gst);
  app.use("/ops/bankstatement", bankstatement);
  app.use("/ops/invoiceFinancing", invoiceFinancing); 
  app.use("/health", (req, res) => {
    res.send("All good mate");
  });
  app.use("/*", home);
 
  app.use(error);
};
