const winston = require("winston");
const config = require("config");
require("winston-mongodb");
require("express-async-errors");

module.exports = function () {
  if (!config.get("dbConfig.connectionString")) {
    throw new Error("Fatal Error: Connection String not defined!");
  } else {
    const connectionString = config.get("dbConfig.connectionString");

    winston.exceptions.handle(
      new winston.transports.Console({ colorize: true, prettyPrint: true }),
      new winston.transports.File({ filename: "logFile.log" }),
      new winston.transports.MongoDB({ db: connectionString, level: "info" })
    );

    process.on("unhandledRejection", (ex) => {
      throw ex;
    });

    winston.add(new winston.transports.File({ filename: "logFile.log" }));
    winston.add(
      new winston.transports.MongoDB({ db: connectionString, level: "info" })
    );
  }
};
