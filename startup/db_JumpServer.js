const winston = require("winston");
const dns = require('dns');
const utils = require('util');
const tunnel = require('tunnel-ssh');
const mongoose = require("mongoose");
const config = require("config");

const dbname = config.get("dbConfig.dbname");
const host = config.get("dbConfig.dbHost");
const StartUpService = require('./service');
const rndPort = 50000;


const makeTunnelConfig = (address) => ({
  username: 'ec2-user',
  host: '**************',
  agent: config.get('dbConfig.SSH_AUTH_SOCK'),
  privateKey: require('fs').readFileSync(config.get("dbConfig.pemPath")),
  port: 22,
  dstHost: address.name,
  dstPort: address.port,
  localPort: rndPort,
  keepAlive: true,
});


async function getConnectionParams() {
  const resolveSrv = utils.promisify(dns.resolveSrv);
  const resolveTxt = utils.promisify(dns.resolveTxt);
  const param = (await resolveTxt(host))[0][0];
  const hosts = (await resolveSrv(`_mongodb._tcp.${host}`))
  //console.log(hosts);
  return {
    config: makeTunnelConfig(hosts[2]),
    url: `${config.get("dbConfig.connectionString")}@127.0.0.1:${rndPort}/${dbname}?directConnection=true&readPreference=secondaryPreferred&w=majority&tls=true&tlsAllowInvalidHostnames=true&authMechanism=SCRAM-SHA-1&${param}`
  };

}
async function dbConnection() {
  const promiseTunnel = utils.promisify(tunnel);
  const connectionParams = await getConnectionParams();
  const mongoTunnel = await promiseTunnel(connectionParams.config)
  try {
    mongoose.connect(connectionParams.url, {
      ssl: true,
      sslValidate: false,
      autoIndex: true,
      connectTimeoutMS: 100000,
      keepAlive: true,
      useNewUrlParser: true,
      useUnifiedTopology: true
    }).then(() => {
      console.log("Connected to MongoDb " + Date.now())
      StartUpService.sharedStorageUpdate();
    });
  } catch (error) {
    console.log("error", error);
    mongoTunnel.map(e => e.close());
  }
}
dbConnection();
module.exports = dbConnection;