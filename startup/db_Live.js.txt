const winston = require("winston");
const mongoose = require("mongoose");
const debug = require("debug")("app:startup");
const config = require("config");

module.exports = function () {
  if (!config.get("dbConfig.connectionString")) {
    throw new Error("Fatal Error: Connection String not defined!");
  } else {
    const connectionString = config.get("dbConfig.connectionString");

    mongoose.connect(connectionString, {
      // useFindAndModify: true,
      // useUnifiedTopology: true,
      // useNewUrlParser: true,
      // useCreateIndex: true
    }).then(() => winston.info("Connected to MongoDb " + Date.now));

    // logger.logs("name: " + config.get("name"));
    // logger.logs("dbName: " + config.get("dbConfig.dbName"));
  }
};
