const winston = require("winston");
const mongoose = require("mongoose");
const debug = require("debug")("app:startup");
const config = require("config");
const StartUpService = require('./service');
const Logger = require("../middleware/logger");
const path = require('path');
const logger = new Logger()

module.exports = function () {
  if (!config.get("dbConfig.connectionString")) {
    throw new Error("Fatal Error: Connection String not defined!");
  } else {
    const connectionString = config.get("dbConfig.connectionString");
    const caPath = path.join(__dirname, '../certs/madad-ff-dev-uat-mongodb-private.crt');
    mongoose.connect(connectionString, {
      tls: true,
      tlsCAFile: caPath,
      keepAlive: true,
      useUnifiedTopology: true,
      useNewUrlParser: true,
      useCreateIndex: true,
      useFindAndModify: false,
    }).then(() => {
      console.log("Connected successfully to madad_ff_prod")
      // logger.log(
      //   `database info connected to MongoDb`,
      //   "info"
      // );
      // winston.info("Connected to MongoDb " + new Date().toLocaleString()) 
      StartUpService.sharedStorageUpdate();
    }).catch(err => {
      console.log(err);

    });

    // logger.logs("name: " + config.get("name"));
    // logger.logs("dbName: " + config.get("dbConfig.dbName"));
  }
};
