const { Roles } = require("../models/ops/roles/roles");
const { RolesActionList } = require("../models/ops/roles/roles-action-list");
const CommonSharedStorage = require("../shared-storage/common-shared-storage");
const { saveErrorLog } = require("../services/ops/common/masters");
const { PartnerMaster, PartnerConfig } = require("../models/ops/partner/partner");
const { UserPartnerInfo, UserInfo } = require("../models/ops/registration/users");
const apiPath = __dirname;

async function getRoles() {
    try {
        let roles = await Roles.find().lean();

        let rolesHash = {}

        for (let i = 0; i < roles.length; i++) {
            rolesHash[roles[i].name] = roles[i];
        }
        if (roles.length) {
            CommonSharedStorage.set("roles", rolesHash);
        }
    } catch (error) {
        console.log("error", error)
    }
}

async function getActions() {
    try {
        let actions = await RolesActionList.find().lean();
        if (actions.length) {
            CommonSharedStorage.set("actions", actions[0].actions);
        }
    } catch (error) {
        console.log("error", error)
    }
}

async function getPartnerMasterWithPartnerConfig() {
    try {
        let query = {
            partnerType: "Lending Partner",
            isActive: true
        }
        let partnerMasterRes = await PartnerMaster.find(query).lean()

        let partnerIds = partnerMasterRes.map(item => item._id);
        let partnerConfigRes = await PartnerConfig.find({ partnerId: { $in: partnerIds } }).lean();
        let partnerConfigResHash = {};
        partnerConfigRes.map(item => {
            partnerConfigResHash[item.partnerId.toString()] = item
        });

        for (let i = 0; i < partnerMasterRes.length; i++) {
            let partnerConfig = partnerConfigResHash[partnerMasterRes[i]._id.toString()];

            if (partnerConfig) {
                partnerMasterRes[i].partnerConfig = partnerConfig;
            }
        }
        CommonSharedStorage.set("partnermasterwithpartnerconfig", partnerMasterRes);

    } catch (error) {
        saveErrorLog("getPartnerMasterWithPartnerConfig", apiPath, {}, error.stack ? error.stack.toString().replace(/\r?\n|\r/g, " ") : error);
    }
}

async function sharedStorageUpdate(req, res) {
    getRoles()
    getActions()
    getPartnerMasterWithPartnerConfig()
}

module.exports = {
    getRoles,
    sharedStorageUpdate
}