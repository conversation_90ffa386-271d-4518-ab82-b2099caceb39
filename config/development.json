{"name": "Fundfina API - Development", "parentDir": "/home/<USER>/applications/live_applications/ProofImages/", "penaltyCharges": 17, "dbConfig": {"connectionString": "***************************************************************/", "CreditRiskEngineConnectionString": "***************************************************************/", "dbHost": "clusterprod.e2f2g.mongodb.net", "dbName": "fundfina", "debug": "app:*", "env": "production", "host": "mongodb://localhost/", "NODE_ENV": "production", "pemPath": "D:/Documents/Linux Server/ALL PEM/JumpServerLinux.pem", "port": 27017, "PORT": 8080}, "webtokens": {"jsonWebToken": "Fundf1na"}, "SSH_AUTH_SOCK": "", "mailConfig": {"email": "AKIAQHEPOETBFOP6PDTG", "password": "BMWcTCMxV9uNnqIyjiE8W74COY1VKiqb+lST01zoXIzu", "from": "Fundfina Support <<EMAIL>>", "port": 587, "host": "email-smtp.ap-south-1.amazonaws.com", "adminEmail": "<EMAIL>", "fromNoc": "Fundfina Support <<EMAIL>>"}, "keys": {"initialLimit": 100, "initialDays": 1, "2Factor": "https://2factor.in/API/", "2FactorApiKey": "c2c53ef5-1621-11e8-a895-0200cd936042", "from": "FNDFIN", "proofImages": "/home/<USER>/applications/live_applications/ProofImages", "loanContract": "https://app.fundfina.com/contractRedirect", "partnerCode": "FUND", "hideContractForPartner": "Pay", "webUrl": "https://app.fundfina.com/", "trancheAmount": 1000000, "encryptionKey": "fundf1n@2!", "editBankLink": "https://app.fundfina.com/updateBankRedirect", "renewalURL": "https://wa.me/+************", "fundfinaTeamTo": "<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>", "fundfinaTeamCc": "<EMAIL>", "fundfinaSupportTeam": "<EMAIL>;<EMAIL>;<EMAIL>", "SystemFailMailTo": "<EMAIL>;", "systemFailMailCc": "<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;", "creditApiUrl": "https://creditapi.fundfina.com", "tempPath": "/home/<USER>/applications/live_applications/tempFiles", "errorFilePath": "/home/<USER>/applications/live_applications/tempFiles/Error/", "creditLineConsent": "https://app.fundfina.com/creditLineRedirect", "webSubdomain": "app.fundfina.com", "defaultUserPhoto": "/home/<USER>/applications/live_applications/Node_FundfinaApi/images/userIcon.png", "imagePathEncryptionKey": "imagesecurity@fundfina!", "minLoanAmount": 10000, "loanAmountMultiple": 500, "cookieDomain": "localhost", "allowUpdateBankDetailsMax": 3, "compressSize": 2, "postDisbursalStatus": ["Loan In Progress", "Close Prepaid", "Close Repaid", "Written-Off"], "faceMatchMinPercentage": 75, "allowMaximumFaceMatch": 3, "dueGraceDays": 2, "dataIngestionApiUrl": "https://di.fundfina.com", "defaultWhatsappMediaFile": "https://ff-system-files.s3.ap-south-1.amazonaws.com/ff-whatsapp-template/images/Benefits+of+Fundfina+Loan.png", "geoIPApi": "https://ipapi.co", "geoIPApiKey": "bdt8kn0YKSjnkOUXsx6A515thgoCHYmNH0zRPAdSGxvQbEccLX"}, "fund": {"isByPassUserDataUrl": false, "isAutoCompleteKycSteps": false, "isDigiLockerMandatory": false, "isTokenGenration": false, "isBodyConstantParameters": false, "isHeaderParameters": false, "tokenGenrationUrl": null, "authKey": null, "bodyConstants": null, "headerConstants": null, "tokenParameters": null, "constantParameters": null, "kycSubmissionApis": null, "kycApprovedApis": null, "kycRejectedApis": null, "kycReviewedApis": null, "onboardingCompletedApis": null, "amountAuthorizedApis": null, "userDataUrl": null, "loanAcceptedApis": null, "loanRejectedApis": null, "loanReviewedApis": null, "loanContractSentApis": null, "contractSignedApis": null, "loanDisbursedApis": null, "pushPreApproval": null, "loanRepayment": null, "loanRepaymentEnquiry": null, "eNachFailureApis": null, "loanBulkRepayment": null, "loanRepaymentBulkEnquiry": null, "loanRepaymentBulkEnquiryCount": null, "loanRepaymentBulkCount": null, "isloan_uid": false}, "eko": {"isByPassUserDataUrl": true, "isAutoCompleteKycSteps": true, "isDigiLockerMandatory": false, "isTokenGenration": false, "isBodyConstantParameters": false, "isHeaderParameters": true, "tokenGenrationUrl": null, "authKey": "347c3e50605f67c21d3fa11f0c537efd", "bodyConstants": null, "headerConstants": {"Developer_key": "347c3e50605f67c21d3fa11f0c537efd", "Content-Type": "application/json"}, "tokenParameters": null, "constantParameters": null, "kycSubmissionApis": null, "kycApprovedApis": null, "kycRejectedApis": null, "kycReviewedApis": null, "offerRejectedByUserApis": null, "loanAcceptedApis": [{"productType": "TL", "apiType": "loanOffer", "url": "http://bank.webservices.eko.in/ekoloanwebservice/webapi/5/offerloan", "model": null, "isWaitForResponse": false, "isRetryOffline": true}], "loanRejectedApis": [{"productType": "TL", "apiType": "loanOffer", "url": "http://bank.webservices.eko.in/ekoloanwebservice/webapi/5/offerloan", "model": null, "isWaitForResponse": false, "isRetryOffline": true}], "loanReviewedApis": null, "loanContractSentApis": null, "contractSignedApis": null, "loanDisbursedApis": [{"productType": "TL", "apiType": "loanDisbursal", "url": "https://api.eko.in:25002/ekoicici/v1/loan/disburse", "isWaitForResponse": true, "isRetryOffline": false}], "pushPreApproval": "https://api.eko.in:25002/ekoicici/v1/loan/create", "loanRepayment": "https://api.eko.in:25002/ekoicici/v1/loan/repayment", "loanRepaymentEnquiry": "https://api.eko.in:25002/ekoicici/v1/loan/repayment/enquiry", "eNachFailureApis": null, "loanBulkRepayment": null, "loanRepaymentBulkEnquiry": null, "loanRepaymentBulkEnquiryCount": null, "loanRepaymentBulkCount": null, "isloan_uid": false}, "pay": {"isByPassUserDataUrl": false, "isAutoCompleteKycSteps": false, "isDigiLockerMandatory": false, "isTokenGenration": true, "isBodyConstantParameters": false, "isHeaderParameters": true, "tokenGenrationUrl": "https://loan.pay1.in/sdk/tokens", "authKey": null, "bodyConstants": null, "headerConstants": {"x-api-token": "", "Content-Type": "application/json"}, "tokenParameters": {"userName": "fundfina", "password": "Fu4nD7f!9n@"}, "constantParameters": null, "kycSubmissionApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://loan.pay1.in/sdk/loans/", "model": {"type": 1}, "isWaitForResponse": false, "isRetryOffline": true}, {"productType": "DL", "apiType": "sendNotification", "url": "https://loan.pay1.in/sdk/loans/", "model": {"type": 1}, "isWaitForResponse": false, "isRetryOffline": true}], "kycApprovedApis": null, "kycRejectedApis": null, "kycReviewedApis": null, "offerRejectedByUserApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://loan.pay1.in/sdk/loans/", "model": {"type": 6}, "isWaitForResponse": false, "isRetryOffline": true}, {"productType": "DL", "apiType": "sendNotification", "url": "https://loan.pay1.in/sdk/loans/", "model": {"type": 6}, "isWaitForResponse": false, "isRetryOffline": true}], "loanAcceptedApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://loan.pay1.in/sdk/loans/", "model": {"type": 2}, "isWaitForResponse": false, "isRetryOffline": true}, {"productType": "DL", "apiType": "sendNotification", "url": "https://loan.pay1.in/sdk/loans/", "model": {"type": 2}, "isWaitForResponse": false, "isRetryOffline": true}], "loanRejectedApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://loan.pay1.in/sdk/loans/", "model": {"type": 3}, "isWaitForResponse": false, "isRetryOffline": true}, {"productType": "DL", "apiType": "sendNotification", "url": "https://loan.pay1.in/sdk/loans/", "model": {"type": 3}, "isWaitForResponse": false, "isRetryOffline": true}], "loanReviewedApis": null, "loanContractSentApis": null, "contractSignedApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://loan.pay1.in/sdk/loans/", "model": {"type": 4}, "isWaitForResponse": false, "isRetryOffline": true}, {"productType": "DL", "apiType": "sendNotification", "url": "https://loan.pay1.in/sdk/loans/", "model": {"type": 4}, "isWaitForResponse": false, "isRetryOffline": true}], "loanDisbursedApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://loan.pay1.in/sdk/loans/", "model": {"type": 5}, "isWaitForResponse": true, "isRetryOffline": false}, {"productType": "DL", "apiType": "sendNotification", "url": "https://loan.pay1.in/sdk/loans/", "model": {"type": 5}, "isWaitForResponse": true, "isRetryOffline": false}], "pushPreApproval": "https://loan.pay1.in/sdk/push-preaprovals", "userDataUrl": "https://loan.pay1.in/sdk/customers/", "loanRepayment": null, "loanRepaymentEnquiry": null, "eNachFailureApis": null, "loanBulkRepayment": "https://loan.pay1.in/sdk/loan-repayment", "loanRepaymentBulkEnquiry": "https://loan.pay1.in/sdk/loan-reconciliation", "loanRepaymentBulkEnquiryCount": 50, "loanRepaymentBulkCount": 50, "isloan_uid": true}, "mpay": {"isByPassUserDataUrl": true, "isAutoCompleteKycSteps": true, "isDigiLockerMandatory": false, "isTokenGenration": false, "isBodyConstantParameters": false, "isHeaderParameters": true, "tokenGenrationUrl": null, "authKey": "zV3TLFTDlQukUxKQeNHaxibKAEsk6ayV", "bodyConstants": null, "headerConstants": {"dev_key": "zV3TLFTDlQukUxKQeNHaxibKAEsk6ayV", "Content-Type": "application/json"}, "tokenParameters": null, "constantParameters": null, "kycSubmissionApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://m.masterpay.pro/Retailer/fundfina/pushNotification", "model": {"type": 1}, "isWaitForResponse": false, "isRetryOffline": true}], "kycApprovedApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://m.masterpay.pro/Retailer/fundfina/pushNotification", "model": {"type": 7}, "isWaitForResponse": false, "isRetryOffline": true}], "kycRejectedApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://m.masterpay.pro/Retailer/fundfina/pushNotification", "model": {"type": 8}, "isWaitForResponse": false, "isRetryOffline": true}], "kycReviewedApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://m.masterpay.pro/Retailer/fundfina/pushNotification", "model": {"type": 9}, "isWaitForResponse": false, "isRetryOffline": true}], "offerRejectedByUserApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://m.masterpay.pro/Retailer/fundfina/pushNotification", "model": {"type": 6}, "isWaitForResponse": false, "isRetryOffline": true}], "loanAcceptedApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://m.masterpay.pro/Retailer/fundfina/pushNotification", "model": {"type": 2}, "isWaitForResponse": true, "isRetryOffline": false}], "loanRejectedApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://m.masterpay.pro/Retailer/fundfina/pushNotification", "model": {"type": 3}, "isWaitForResponse": false, "isRetryOffline": true}], "loanReviewedApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://m.masterpay.pro/Retailer/fundfina/pushNotification", "model": {"type": 10}, "isWaitForResponse": false, "isRetryOffline": true}], "loanContractSentApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://m.masterpay.pro/Retailer/fundfina/pushNotification", "model": {"type": 11}, "isWaitForResponse": true, "isRetryOffline": false}], "contractSignedApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://m.masterpay.pro/Retailer/fundfina/pushNotification", "model": {"type": 4}, "isWaitForResponse": false, "isRetryOffline": true}], "loanDisbursedApis": [{"productType": "TL", "apiType": "loanDisbursal", "url": "https://m.masterpay.pro/Retailer/fundfina/loanDisbursal", "model": null, "isWaitForResponse": true, "isRetryOffline": false}, {"productType": "TL", "apiType": "sendNotification", "url": "https://m.masterpay.pro/Retailer/fundfina/pushNotification", "model": {"type": 5}, "isWaitForResponse": false, "isRetryOffline": true}], "pushPreApproval": "https://m.masterpay.pro/Retailer/fundfina/pushPreApproval", "loanRepayment": "https://m.masterpay.pro/Retailer/fundfina/loanRepayment", "loanRepaymentEnquiry": "https://m.masterpay.pro/Retailer/fundfina/loanRepaymentEnquiry", "accountEmails": "<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>", "eNachFailureApis": null, "loanBulkRepayment": null, "loanRepaymentBulkEnquiry": null, "loanRepaymentBulkEnquiryCount": null, "loanRepaymentBulkCount": null, "isloan_uid": false}, "pworld": {"isByPassUserDataUrl": false, "isAutoCompleteKycSteps": false, "isDigiLockerMandatory": false, "isTokenGenration": false, "isBodyConstantParameters": false, "isHeaderParameters": true, "tokenGenrationUrl": null, "authKey": "Prs98!12sds", "bodyConstants": null, "headerConstants": {"auth-key": "Prs98!12sds", "Content-Type": "application/json"}, "tokenParameters": null, "constantParameters": null, "kycSubmissionApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://loan-engine.payworldindia.com/api/v1/fundfina/pushNotification", "model": {"type": 1}, "isWaitForResponse": false, "isRetryOffline": true}], "kycApprovedApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://loan-engine.payworldindia.com/api/v1/fundfina/pushNotification", "model": {"type": 7}, "isWaitForResponse": false, "isRetryOffline": true}], "kycRejectedApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://loan-engine.payworldindia.com/api/v1/fundfina/pushNotification", "model": {"type": 8}, "isWaitForResponse": false, "isRetryOffline": true}], "kycReviewedApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://loan-engine.payworldindia.com/api/v1/fundfina/pushNotification", "model": {"type": 9}, "isWaitForResponse": false, "isRetryOffline": true}], "offerRejectedByUserApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://loan-engine.payworldindia.com/api/v1/fundfina/pushNotification", "model": {"type": 6}, "isWaitForResponse": false, "isRetryOffline": true}], "loanAcceptedApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://loan-engine.payworldindia.com/api/v1/fundfina/pushNotification", "model": {"type": 2}, "isWaitForResponse": false, "isRetryOffline": true}], "loanRejectedApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://loan-engine.payworldindia.com/api/v1/fundfina/pushNotification", "model": {"type": 3}, "isWaitForResponse": false, "isRetryOffline": true}], "loanReviewedApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://loan-engine.payworldindia.com/api/v1/fundfina/pushNotification", "model": {"type": 10}, "isWaitForResponse": false, "isRetryOffline": true}], "loanContractSentApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://loan-engine.payworldindia.com/api/v1/fundfina/pushNotification", "model": {"type": 11}, "isWaitForResponse": false, "isRetryOffline": true}], "contractSignedApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://loan-engine.payworldindia.com/api/v1/fundfina/pushNotification", "model": {"type": 4}, "isWaitForResponse": false, "isRetryOffline": true}], "loanDisbursedApis": [{"productType": "TL", "apiType": "loanDisbursal", "url": "https://loan-engine.payworldindia.com/api/v1/fundfina/loanDisbursal", "isWaitForResponse": true, "isRetryOffline": false}, {"productType": "TL", "apiType": "sendNotification", "url": "https://loan-engine.payworldindia.com/api/v1/fundfina/pushNotification", "model": {"type": 5}, "isWaitForResponse": false, "isRetryOffline": true}], "userDataUrl": "https://loan-engine.payworldindia.com/api/v1/fundfina/getUserData/", "pushPreApproval": "https://loan-engine.payworldindia.com/api/v1/fundfina/pushPreApproval", "loanRepayment": null, "loanRepaymentEnquiry": null, "loanBulkRepayment": "https://loan-engine.payworldindia.com/api/v1/fundfina/bulkLoanRepayment", "loanRepaymentBulkEnquiry": "https://loan-engine.payworldindia.com/api/v1/fundfina/bulkLoanRepaymentEnquiry", "loanRepaymentBulkEnquiryCount": 25, "loanRepaymentBulkCount": 50}, "bnkit": {"isByPassUserDataUrl": false, "isAutoCompleteKycSteps": false, "isDigiLockerMandatory": false, "isTokenGenration": false, "isBodyConstantParameters": false, "isHeaderParameters": true, "tokenGenrationUrl": null, "authKey": "23#fdhg<PERSON>ey@6W0lkNsm51$GhjL86", "bodyConstants": null, "headerConstants": {"auth-key": "23#fdhg<PERSON>ey@6W0lkNsm51$GhjL86", "Content-Type": "application/json"}, "tokenParameters": null, "constantParameters": null, "kycSubmissionApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://red.bankit.in/WalletV1/loan/fundFina/pushNotification", "model": {"type": 1}, "isWaitForResponse": false, "isRetryOffline": true}], "kycApprovedApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://red.bankit.in/WalletV1/loan/fundFina/pushNotification", "model": {"type": 7}, "isWaitForResponse": false, "isRetryOffline": true}], "kycRejectedApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://red.bankit.in/WalletV1/loan/fundFina/pushNotification", "model": {"type": 8}, "isWaitForResponse": false, "isRetryOffline": true}], "kycReviewedApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://red.bankit.in/WalletV1/loan/fundFina/pushNotification", "model": {"type": 9}, "isWaitForResponse": false, "isRetryOffline": true}], "offerRejectedByUserApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://red.bankit.in/WalletV1/loan/fundFina/pushNotification", "model": {"type": 6}, "isWaitForResponse": false, "isRetryOffline": true}], "loanAcceptedApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://red.bankit.in/WalletV1/loan/fundFina/pushNotification", "model": {"type": 2}, "isWaitForResponse": false, "isRetryOffline": true}], "loanRejectedApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://red.bankit.in/WalletV1/loan/fundFina/pushNotification", "model": {"type": 3}, "isWaitForResponse": false, "isRetryOffline": true}], "loanReviewedApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://red.bankit.in/WalletV1/loan/fundFina/pushNotification", "model": {"type": 10}, "isWaitForResponse": false, "isRetryOffline": true}], "loanContractSentApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://red.bankit.in/WalletV1/loan/fundFina/pushNotification", "model": {"type": 11}, "isWaitForResponse": false, "isRetryOffline": true}], "contractSignedApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://red.bankit.in/WalletV1/loan/fundFina/pushNotification", "model": {"type": 4}, "isWaitForResponse": false, "isRetryOffline": true}], "loanDisbursedApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://red.bankit.in/WalletV1/loan/fundFina/pushNotification", "model": {"type": 5}, "isWaitForResponse": true, "isRetryOffline": false}], "pushPreApproval": "https://red.bankit.in/WalletV1/loan/fundFina/pushPreApproval", "userDataUrl": "https://red.bankit.in/WalletV1/loan/fundFina/getUserData/", "loanRepayment": "https://red.bankit.in/WalletV1/loan/fundFina/loanRepayment", "loanRepaymentEnquiry": "https://red.bankit.in/WalletV1/loan/fundFina/loanRepaymentEnquiry", "eNachFailureApis": null, "loanBulkRepayment": null, "loanRepaymentBulkEnquiry": null, "loanRepaymentBulkEnquiryCount": null, "loanRepaymentBulkCount": null, "isloan_uid": false}, "inmd": {"isByPassUserDataUrl": false, "isAutoCompleteKycSteps": false, "isDigiLockerMandatory": false, "isTokenGenration": false, "isBodyConstantParameters": false, "isHeaderParameters": true, "tokenGenrationUrl": null, "authKey": null, "bodyConstants": null, "headerConstants": {"Content-Type": "application/json"}, "tokenParameters": null, "constantParameters": null, "kycSubmissionApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://api.instantmudra.net/callback/fundfina/pushNotification", "model": {"type": 1}, "isWaitForResponse": false, "isRetryOffline": true}], "kycApprovedApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://api.instantmudra.net/callback/fundfina/pushNotification", "model": {"type": 7}, "isWaitForResponse": false, "isRetryOffline": true}], "kycRejectedApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://api.instantmudra.net/callback/fundfina/pushNotification", "model": {"type": 8}, "isWaitForResponse": false, "isRetryOffline": true}], "kycReviewedApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://api.instantmudra.net/callback/fundfina/pushNotification", "model": {"type": 9}, "isWaitForResponse": false, "isRetryOffline": true}], "offerRejectedByUserApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://api.instantmudra.net/callback/fundfina/pushNotification", "model": {"type": 6}, "isWaitForResponse": false, "isRetryOffline": true}], "loanAcceptedApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://api.instantmudra.net/callback/fundfina/pushNotification", "model": {"type": 2}, "isWaitForResponse": false, "isRetryOffline": true}], "loanRejectedApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://api.instantmudra.net/callback/fundfina/pushNotification", "model": {"type": 3}, "isWaitForResponse": false, "isRetryOffline": true}], "loanReviewedApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://api.instantmudra.net/callback/fundfina/pushNotification", "model": {"type": 10}, "isWaitForResponse": false, "isRetryOffline": true}], "loanContractSentApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://api.instantmudra.net/callback/fundfina/pushNotification", "model": {"type": 11}, "isWaitForResponse": false, "isRetryOffline": true}], "contractSignedApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://api.instantmudra.net/callback/fundfina/pushNotification", "model": {"type": 4}, "isWaitForResponse": false, "isRetryOffline": true}], "loanDisbursedApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://api.instantmudra.net/callback/fundfina/pushNotification", "model": {"type": 5}, "isWaitForResponse": true, "isRetryOffline": false}], "userDataUrl": "https://api.instantmudra.net/callback/fundfina/getUserData/", "loanRepayment": "https://api.instantmudra.net/callback/fundfina/emiDeduction", "loanRepaymentEnquiry": "https://api.instantmudra.net/callback/fundfina/emiCheck", "eNachFailureApis": null, "loanBulkRepayment": null, "loanRepaymentBulkEnquiry": null, "loanRepaymentBulkEnquiryCount": null, "loanRepaymentBulkCount": null, "isloan_uid": false}, "epay": {"isByPassUserDataUrl": false, "isAutoCompleteKycSteps": false, "isDigiLockerMandatory": false, "isTokenGenration": false, "isBodyConstantParameters": false, "isHeaderParameters": true, "tokenGenrationUrl": null, "authKey": "eWVzZG10OlMzY3JlVCNkbXQ=", "bodyConstants": null, "headerConstants": {"auth-key": "eWVzZG10OlMzY3JlVCNkbXQ=", "Content-Type": "application/json"}, "tokenParameters": null, "constantParameters": null, "kycSubmissionApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://epmoney.easypay.co.in/epyesbc/fundFina/pushNotification", "model": {"type": 1}, "isWaitForResponse": false, "isRetryOffline": true}], "kycApprovedApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://epmoney.easypay.co.in/epyesbc/fundFina/pushNotification", "model": {"type": 7}, "isWaitForResponse": false, "isRetryOffline": true}], "kycRejectedApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://epmoney.easypay.co.in/epyesbc/fundFina/pushNotification", "model": {"type": 8}, "isWaitForResponse": false, "isRetryOffline": true}], "kycReviewedApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://epmoney.easypay.co.in/epyesbc/fundFina/pushNotification", "model": {"type": 9}, "isWaitForResponse": false, "isRetryOffline": true}], "offerRejectedByUserApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://epmoney.easypay.co.in/epyesbc/fundFina/pushNotification", "model": {"type": 6}, "isWaitForResponse": false, "isRetryOffline": true}], "loanAcceptedApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://epmoney.easypay.co.in/epyesbc/fundFina/pushNotification", "model": {"type": 2}, "isWaitForResponse": false, "isRetryOffline": true}], "loanRejectedApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://epmoney.easypay.co.in/epyesbc/fundFina/pushNotification", "model": {"type": 3}, "isWaitForResponse": false, "isRetryOffline": true}], "loanReviewedApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://epmoney.easypay.co.in/epyesbc/fundFina/pushNotification", "model": {"type": 10}, "isWaitForResponse": false, "isRetryOffline": true}], "loanContractSentApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://epmoney.easypay.co.in/epyesbc/fundFina/pushNotification", "model": {"type": 11}, "isWaitForResponse": false, "isRetryOffline": true}], "contractSignedApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://epmoney.easypay.co.in/epyesbc/fundFina/pushNotification", "model": {"type": 4}, "isWaitForResponse": false, "isRetryOffline": true}], "loanDisbursedApis": [{"productType": "TL", "apiType": "loanDisbursal", "url": "https://epmoney.easypay.co.in/epyesbc/fundFina/loanDisbursal", "isWaitForResponse": true, "isRetryOffline": false}], "pushPreApproval": "https://epmoney.easypay.co.in/epyesbc/fundFina/pushPreApproval", "userDataUrl": "https://epmoney.easypay.co.in/epyesbc/fundFina/getUserData/", "loanRepayment": "https://epmoney.easypay.co.in/epyesbc/fundFina/loanRepayment", "loanRepaymentEnquiry": "https://epmoney.easypay.co.in/epyesbc/fundFina/loanRepaymentEnquiry", "eNachFailureApis": null, "loanBulkRepayment": null, "loanRepaymentBulkEnquiry": null, "loanRepaymentBulkEnquiryCount": null, "loanRepaymentBulkCount": null, "isloan_uid": false}, "25tk": {"isByPassUserDataUrl": true, "isAutoCompleteKycSteps": true, "isDigiLockerMandatory": false, "isTokenGenration": false, "isBodyConstantParameters": false, "isHeaderParameters": false, "tokenGenrationUrl": null, "authKey": null, "bodyConstants": null, "headerConstants": null, "tokenParameters": null, "constantParameters": null, "kycSubmissionApis": null, "kycApprovedApis": null, "kycRejectedApis": null, "kycReviewedApis": null, "loanAcceptedApis": null, "loanRejectedApis": null, "loanReviewedApis": null, "contractSignedApis": null, "loanContractSentApis": null, "onboardingCompletedApis": null, "loanDisbursedApis": null, "amountAuthorizedApis": null, "userDataUrl": null, "pushPreApproval": null, "loanRepayment": null, "loanRepaymentEnquiry": null, "loanBulkRepayment": null, "loanRepaymentBulkEnquiry": null, "loanRepaymentBulkEnquiryCount": null, "loanRepaymentBulkCount": null, "isloan_uid": false}, "rapi": {"isByPassUserDataUrl": false, "isAutoCompleteKycSteps": true, "isDigiLockerMandatory": true, "isTokenGenration": false, "isBodyConstantParameters": false, "isHeaderParameters": true, "tokenGenrationUrl": null, "authKey": "67yW3hn@8ui#o2", "bodyConstants": null, "headerConstants": {"auth-key": "67yW3hn@8ui#o2", "Content-Type": "application/json"}, "tokenParameters": null, "constantParameters": null, "kycSubmissionApis": null, "kycApprovedApis": null, "kycRejectedApis": null, "kycReviewedApis": null, "offerRejectedByUserApis": null, "loanAcceptedApis": null, "loanRejectedApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://api.rapimoney.com/lps/v1/fundfina/pushNotification", "model": {"type": 3}, "isWaitForResponse": false, "isRetryOffline": true}], "loanReviewedApis": null, "loanContractSentApis": null, "contractSignedApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://api.rapimoney.com/lps/v1/fundfina/pushNotification", "model": {"type": 4}, "isWaitForResponse": false, "isRetryOffline": true}], "loanDisbursedApis": [{"productType": "TL", "apiType": "loanDisbursal", "url": "https://api.rapimoney.com/lps/v1/fundfina/loanDisbursal", "isWaitForResponse": true, "isRetryOffline": false}], "loanDisbursalFailureApis": [{"productType": "TL", "apiType": "loanDisbursalRejection", "url": "https://api.rapimoney.com/lps/v1/fundfina/loanDisbursal", "isWaitForResponse": false, "isRetryOffline": true}], "eNachFailureApis": [{"productType": "TL", "apiType": "sendNotification", "url": "https://api.rapimoney.com/lps/v1/fundfina/pushNotification", "model": {"type": 14}, "isWaitForResponse": false, "isRetryOffline": true}], "pushPreApproval": "https://api.rapimoney.com/lps/v1/fundfina/pushPreApproval", "loanRepayment": null, "loanRepaymentEnquiry": "https://api.rapimoney.com/lpsj/v1/fundfina/loanRepaymentEnquiry", "loanBulkRepayment": "https://api.rapimoney.com/lpsj/v1/fundfina/bulkLoanRepayment", "loanRepaymentBulkEnquiry": null, "loanRepaymentBulkEnquiryCount": 50, "loanRepaymentBulkCount": 100, "isloan_uid": false}, "exportToExcelPath": {"ledgerAccount": "/home/<USER>/applications/live_applications/tempFiles/Reports/LedgerAccount Report/", "payoutEmi": "/home/<USER>/applications/live_applications/tempFiles/Reports/Repayment Report/", "generalReports": "/home/<USER>/applications/live_applications/tempFiles/Reports/General/"}, "lbox": {"isTokenGenration": false, "isBodyConstantParameters": false, "isHeaderParameters": true, "tokenGenrationUrl": null, "isLoanApprovalRequired": false, "authKey": null, "bodyConstants": null, "headerConstants": {"x-api-key": "5dfdXXdDAb23HJgVxsFuP1epWED17kk4xxpe5kPDjzYeCGuvFs3RfJUzsio8BsbKABBh7ERn173txd1hzLomJP4yE2Dit7CfEKGR", "x-api-code": "bts/Pf1qQ9PIWyUO2JJOMw==", "Content-Type": "application/json"}, "tokenParameters": null, "constantParameters": null, "makeLiveApis": [{"apiType": "loanApplication", "url": "https://api.lendbox.in/v1/partners/borrower/signup", "isWaitForResponse": true, "isRetryOffline": false}, {"apiType": "loanDisbursement", "url": "https://api.lendbox.in/v1/partners/create-loan", "isWaitForResponse": true, "isRetryOffline": false}, {"apiType": "loanDocuments", "url": "https://api.lendbox.in/v1/partners/upload/docs", "isWaitForResponse": false, "isRetryOffline": true}, {"apiType": "bankDetails", "url": "https://api.lendbox.in/v1/partners/user", "isWaitForResponse": false, "isRetryOffline": true}], "disbursementApis": [{"apiType": "loanDocuments", "url": "https://api.lendbox.in/v1/partners/upload/docs", "isWaitForResponse": true, "isRetryOffline": false}, {"apiType": "emiSchedule", "url": "https://api.lendbox.in/v1/partners/edi-update", "isWaitForResponse": false, "isRetryOffline": true}], "repaymentApis": [{"apiType": "loanRepayment", "url": "https://api.lendbox.in/v1/partners/wallet/orders", "isWaitForResponse": false, "isRetryOffline": true}], "cancellationApis": null}, "cent": {"isTokenGenration": false, "isBodyConstantParameters": true, "isHeaderParameters": true, "tokenGenrationUrl": null, "isLoanApprovalRequired": false, "authKey": null, "bodyConstants": {"UserID": "CENTRUMSME", "Password": "ABCD*1234", "PartnerID": "FDF"}, "headerConstants": {"Content-Type": "application/json"}, "tokenParameters": null, "constantParameters": null, "makeLiveApis": [{"apiType": "loanApplication", "url": "https://centrumsmemob.bijliftt.com/CentrumSmeService.svc/DataFrom", "isWaitForResponse": false, "isRetryOffline": true}], "disbursementApis": [{"apiType": "loanDisbursement", "url": "https://centrumsmemob.bijliftt.com/CentrumSmeService.svc/FundFinaLoanDisbursement", "isWaitForResponse": true, "isRetryOffline": false}, {"apiType": "loanDocuments", "url": "https://centrumsmemob.bijliftt.com/CentrumSmeService.svc/AllExternalImageUpload", "isWaitForResponse": false, "isRetryOffline": true}], "repaymentApis": null, "cancellationApis": [{"apiType": "loanCancellation", "url": "https://centrumsmemob.bijliftt.com/CentrumSmeService.svc/FundFinaCancelLoanApplication", "isWaitForResponse": true, "isRetryOffline": false}]}, "aphe": {"isTokenGenration": true, "isBodyConstantParameters": false, "isHeaderParameters": true, "tokenGenrationUrl": null, "isLoanApprovalRequired": false, "authKey": null, "bodyConstants": null, "headerConstants": null, "tokenParameters": {"grant_type": "password", "UserName": "REG1003", "Password": "reg03#873uy"}, "constantParameters": {"branchCode": "001", "partnerCode": "aphe", "marketingExceutive": 1, "loancode": 67}, "tokenUrl": "http://Aphelion.vexilinfotech.in/token", "makeLiveApis": [{"apiType": "loanApplication", "url": "http://Aphelion.vexilinfotech.in/api/Merchant/ProcessApplicationForm", "isWaitForResponse": false, "isRetryOffline": true}], "disbursementApis": [{"apiType": "loanSanction", "url": "http://Aphelion.vexilinfotech.in/api/Merchant/ProcessSanction", "isWaitForResponse": true, "isRetryOffline": false}, {"apiType": "addressDetails", "url": "http://Aphelion.vexilinfotech.in/api/Merchant/AddApplicantAddress", "isWaitForResponse": false, "isRetryOffline": true}, {"apiType": "emiSchedule", "url": "http://Aphelion.vexilinfotech.in/api/Merchant/PostEMISchedule", "isWaitForResponse": false, "isRetryOffline": true}], "repaymentApis": [{"apiType": "loanRepayment", "url": "http://Aphelion.vexilinfotech.in/api/Merchant/ProcessRepayment", "isWaitForResponse": false, "isRetryOffline": true}], "cancellationApis": null}, "fintree": {"isTokenGenration": false, "isBodyConstantParameters": false, "isHeaderParameters": false, "tokenGenrationUrl": null, "authKey": null, "bodyConstants": null, "headerConstants": null, "tokenParameters": null, "constantParameters": null, "makeLiveApis": null, "disbursementApis": null}, "virgo": {"isTokenGenration": false, "isBodyConstantParameters": false, "isHeaderParameters": false, "tokenGenrationUrl": null, "authKey": null, "bodyConstants": null, "headerConstants": null, "tokenParameters": null, "constantParameters": null, "makeLiveApis": null, "disbursementApis": null}, "emld": {"isTokenGenration": false, "isBodyConstantParameters": false, "isHeaderParameters": false, "tokenGenrationUrl": null, "authKey": null, "bodyConstants": null, "headerConstants": null, "tokenParameters": null, "constantParameters": null, "makeLiveApis": null, "disbursementApis": null}, "kudos": {"isTokenGenration": false, "isBodyConstantParameters": false, "isHeaderParameters": false, "tokenGenrationUrl": null, "authKey": null, "bodyConstants": null, "headerConstants": null, "tokenParameters": null, "constantParameters": null, "makeLiveApis": null, "disbursementApis": null}, "aaac": {"isTokenGenration": false, "isBodyConstantParameters": false, "isHeaderParameters": false, "tokenGenrationUrl": null, "authKey": null, "bodyConstants": null, "headerConstants": null, "tokenParameters": null, "constantParameters": null, "makeLiveApis": null, "disbursementApis": null}, "arth": {"isTokenGenration": false, "isBodyConstantParameters": true, "isHeaderParameters": true, "tokenGenrationUrl": null, "isLoanApprovalRequired": false, "authKey": null, "bodyConstants": null, "headerConstants": {"Content-Type": "application/json", "Authorization": "Basic RnVuZEZpbmFTeXM6RmZBcnRTeXMyNjQyMiQ="}, "tokenParameters": null, "constantParameters": null, "makeLiveApis": [{"apiType": "loanApplication", "url": "https://www.arthanfin.com/artpartner/loanApplication", "isWaitForResponse": true, "isRetryOffline": false}, {"apiType": "loanSanction", "url": "https://www.arthanfin.com/artpartner/loanSanction", "isWaitForResponse": false, "isRetryOffline": true}], "disbursementApis": null, "repaymentApis": null, "cancellationApis": [{"apiType": "loanCancellation", "url": "https://www.arthanfin.com/artpartner/cancelLoan", "isWaitForResponse": false, "isRetryOffline": true}]}, "shortUrl": {"baseUrl": "fndf.in", "mainOfferCode": "OFFERS", "completionReminderCode": "APPLICATION_COMPLETION_REMINDER", "contractCode": "CONTRACT_APPROVAL", "renewalsCode": "RENEWALS", "collectionCode": "COLLECTIONS", "recoveryCode": "RECOVERY", "eNachCode": "E_NACH_MANDATE_LINK", "reviewCode": "REVIEW_SHORT_LINK", "offerCode": "OFFER_SHORT_LINK", "consentCode": "LC_CONSENT_LINK", "bankLinkCode": "UPDATE_BANK_LINK", "incompleteApplicationCode": "INCOMPLETE_APPLICATION_LINK", "documentLink": "DOCUMENT_LINK"}, "rebrandly": {"apiKey": "********************************", "baseUrl": "https://api.rebrandly.com"}, "partnerConfig": {"bankValidationAmount": 0, "eNachAmount": 0, "disbursalType": "Partner", "collectionType": "Wallet", "LC_InvoiceTenure": 28, "LC_InvoiceFrequency": "Days", "LC_ParentTenure": 90, "LC_ParentFrequency": "Days", "settlementMode": "reducing", "primaryTrancheValue": ""}, "productPartnerConfig": {"TL": {"penaltyGraceDays": [{"frequency": "Daily", "days": 2}, {"frequency": "Weekly", "days": 2}, {"frequency": "Fortnightly", "days": 2}, {"frequency": "Monthly", "days": 4}, {"frequency": "Adhoc", "days": 2}], "childLoanApprovedLimitInPer": 0, "interestComputation": "Post"}, "DL": {"penaltyGraceDays": [{"frequency": "Daily", "days": 4}, {"frequency": "Weekly", "days": 4}, {"frequency": "Fortnightly", "days": 4}, {"frequency": "Monthly", "days": 4}, {"frequency": "Adhoc", "days": 4}], "childLoanApprovedLimitInPer": 0, "interestComputation": "Post"}, "LC": {"penaltyGraceDays": [{"frequency": "Daily", "days": 0}, {"frequency": "Weekly", "days": 0}, {"frequency": "Fortnightly", "days": 0}, {"frequency": "Monthly", "days": 0}, {"frequency": "Adhoc", "days": 0}], "childLoanApprovedLimitInPer": 10, "interestComputation": "Pre"}}, "digio": {"url": "https://api.digio.in/", "token": "Basic QUlWUUExRUlBMkNSNkdEVUtMTVpXUVBPWVlPUEZZN0E6MTY4V08xTzNRV0M1VllPTzNOTjZPV1dVNklRSlhaWjk=", "eNach": {"maximum_amount": 100000, "auth_mode": "api", "authModeApi": "api", "authModeAadhaar": "esign", "authModePhysical": "physical", "mandateType": "create", "corporate_config_id": "TSE210101163213541ASWBPI1AB12S8G", "corporate_account_number": "************", "instrument_type": "debit", "isRecurring": true, "management_category": "A001", "expire_in_days": "30", "createFormUrl": "v3/client/mandate/create_form", "nachLinkUrl": "https://app.digio.in/#/gateway/login/", "redirectUrl": "https://app.fundfina.com/eNachResponse", "createSchedule": "v3/client/nach_debit/scheduled/register", "getDebitStatus": "v3/client/nach_debit/scheduled/", "presentationFine": 0, "dayDelay": 1, "maxRetryCount": 0, "generate_access_token": true, "logoLink": "https://app.fundfina.com/mailHeader.png", "cancelMandate": "v3/client/mandate/registration_cancel", "frequency": "Adhoc", "defaultDays": 1, "physicalMandate": true}, "digilocker": {"type": "DIGILOCKER", "title": "Digilocker KYC", "description": "Please share your <PERSON><PERSON><PERSON><PERSON> and <PERSON> card from digilocker", "document_types": ["AADHAAR", "PAN"], "expire_in_days": 31, "generate_access_token": true, "linkUrl": "https://app.digio.in/#/gateway/login/", "redirectUrl": "http://localhost:3001/digilockerResponse", "redirectUrlInvoiceFinancing": "http://localhost:3001/digilockerResponseInvoiceFinancing", "logoLink": "https://app.fundfina.com/mailHeader.png"}}, "disbursal": {"clientCode": "FUNDMPL", "transactionCurrency": "INR", "beneficiaryAccountType": 11, "charge": {"transactionTypeCode": "LBT", "beneficaryName": "Fundfina Marketplace", "beneficaryAccountNo": "***********", "ifscCode": "IDFB0040101", "paymentType": "NEFT", "purposeCode": "OTH", "prefixCustomerRefNo": "L", "postfixCutomerRefNo": "PF", "postfixPreInterestCode": "PL"}, "fundfinaMarketingAccount": "***********", "amount": {"transactionTypeCode": "LBT", "prefixCustomerRefNo": "L", "purposeCode": "OTH"}, "toEmail": "<EMAIL>", "ccEmail": "<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>", "beneficaryEmail": "<EMAIL>", "beneficaryMobileNumber": "", "beneficaryAddress": "Mumbai", "paymentType": "IMPS", "paymentConfirmationRetry": 1, "cutOffTime": {"hours": 19, "minutes": 0}, "loanLink": "https://app.fundfina.com/loanInfo/"}, "payOut": {"clientCode": "FUNDMPL", "transactionCurrency": "INR", "beneficiaryAccountType": 11, "debitAccountNo": "***********", "toEmail": "<EMAIL>;", "ccEmail": "<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;"}, "karix": {"url": "https://rcmapi.instaalerts.zone/services/rcm/sendMessage", "token": "Bearer b3MRKjQdtJI2dDebIlLe8Q==", "mobileNo1": "************", "mobileNo2": "************", "version": "v1.0.9", "channel": "WABA", "recipient_type": "individual", "webHookDNId": "1001", "type": "TEMPLATE", "ffSupportLink": "fndf.in/1/22w3e3", "ffSupportNumber": "+************"}, "tinyUrl": {"url": "https://fndf.in", "account-id": "621d01f6cdcd443b835f460c", "secretKey": "4051c9d9d616df832g57865000", "urlPrefix": "fndf.in/"}, "idfcGateway": {"hostname": "api.idfcfirstbank.com", "passphrase": "Fundf1n@123g00gle", "hostUrl": "https://api.idfcfirstbank.com", "balanceEnquiryUrl": "/fundfina/enquireAccountBalance?apikey=FUNDFINA_Partner", "paymentUrl": "/fundfina/executeTransaction?apikey=FUNDFINA_Partner", "paymentEnquiryUrl": "/fundfina/enquirePaymentDebitTransaction?apikey=FUNDFINA_Partner", "cryptoKey": "laaXDTRDmgmYalkyGsrCqhBysSSHIjnd", "batchPaymentUrl": "/fundfina/multiPayment?apikey=FUNDFINA_Partner", "batchPaymentEnquiryUrl": "/fundfina/multiPaymentStatusEnquiry?apikey=FUNDFINA_Partner", "batchCryptoKey": "laaXDTRDmgmYalkyGsrCqhBysSSHIjnd", "batchClientCode": "FUNDFINA_Partner", "batchCorpId": "FNDFCC", "clientCode": "FUNDFINA_Partner"}, "App": {"signzy": {"username": "fundfina_prod", "password": "prEbEfiflcr0*Rothi4r", "url": {"login": "https://signzy.tech/api/v2/patrons/login", "createAuthUrl": "https://signzy.tech/api/v2/patrons/624efd763fe07648df72a9ae/digilockers", "getDetails": "https://signzy.tech/api/v2/patrons/624efd763fe07648df72a9ae/digilockers", "getPanDetails": "https://signzy.tech/api/v2/patrons/624efd763fe07648df72a9ae/panv2", "uploadFileBase64": "https://persist.signzy.tech/api/base64strings/upload", "createIdentityObject": "https://signzy.tech/api/v2/patrons/624efd763fe07648df72a9ae/identities", "maskAadhaar": "https://signzy.tech/api/v2/snoops", "maskCallbackUrl": "http://localhost:3000/ops/identity/mask/details", "videoVerification": "https://signzy.tech/api/v2/patrons/624efd763fe07648df72a9ae/videoiframes", "videoVerificationRedirectUrl": "https://app.fundfina.com/verifyLoanContract", "videoVerificationCallbackUrl": "https://api.fundfina.com/ops/admin/handleFaceMatchResponse"}, "videoRecordTime": "5", "email": "<EMAIL>", "appRedirectUrl": "http://localhost:3000/ops/identity/details"}}, "livf": {"isTokenGenration": false, "isBodyConstantParameters": true, "isHeaderParameters": true, "tokenGenrationUrl": null, "isLoanApprovalRequired": true, "authKey": null, "bodyConstants": null, "headerConstants": {"Content-Type": "application/json", "X-livfin-key": "9k9z$N%EH565"}, "tokenParameters": null, "constantParameters": null, "makeLiveApis": [{"apiType": "loanApplication", "url": "https://onboarding.applivfin.com:8443/gateway-0.0.1-SNAPSHOT/onboarding/onboarding-0.0.1-SNAPSHOT/loanApplication", "isWaitForResponse": true, "isRetryOffline": false}], "disbursementApis": [{"apiType": "loanSanction", "url": "https://onboarding.applivfin.com:8443/gateway-0.0.1-SNAPSHOT/onboarding/onboarding-0.0.1-SNAPSHOT/loanSanction", "isWaitForResponse": true, "isRetryOffline": false}], "repaymentApis": null, "cancellationApis": null}, "gcpCredentials": {"projectId": "ff-madad-inv", "bucketName": "madad_inv", "bankStatementsBucket": "madad_inv", "signUrlTimeout": 50000}, "awsCredentials": {"bucketName": "ff-temp-file", "awsRegion": "ap-south-1", "accessId": "********************", "accessKey": "mEcokBbcLjHrlDecQ0Q3Bvx4A8poTuvx/G1FLfBt", "signUrlTimeout": 300, "baseUrl": "https://ff-temp-file.s3.ap-south-1.amazonaws.com/", "bankStatementsBucketName": "prod-ff-reports"}, "awsSystemFilesCredentials": {"bucketName": "ff-system-files", "awsRegion": "ap-south-1", "accessId": "********************", "accessKey": "mEcokBbcLjHrlDecQ0Q3Bvx4A8poTuvx/G1FLfBt", "signUrlTimeout": 300, "baseUrl": "https://ff-system-files.s3.ap-south-1.amazonaws.com/"}, "repaymentCollectionAccount": {"accountName": "FUNDFINA MARKETPLACE PRIVATE LIMITED", "accountNo": "***********", "ifsc": "IDFB0040101"}, "awsProofImageCredentials": {"bucketName": "ffproofimages", "awsRegion": "ap-south-1", "accessId": "********************", "accessKey": "mEcokBbcLjHrlDecQ0Q3Bvx4A8poTuvx/G1FLfBt", "signUrlTimeout": 300, "baseUrl": "https://ffproofimages.ap-south-1.amazonaws.com/", "nocUrlTimeout": 432000}, "awsInvoiceImageCredentials": {"bucketName": "ffinvoices", "awsRegion": "ap-south-1", "accessId": "********************", "accessKey": "mEcokBbcLjHrlDecQ0Q3Bvx4A8poTuvx/G1FLfBt", "signUrlTimeout": 300, "baseUrl": "https://ffinvoices.ap-south-1.amazonaws.com/"}, "apiLogsMeta": {"insertLimit": 1000, "timeIntervalToInsert": 2}, "errorCode": {"faceNotMatch": 700, "genderMismatch": 701, "pendingForMandateRegistration": 702, "pendingForPhysicalNachPDC": 703}, "settlementMIS": {"maxLimit": 1000, "channel": {"api": "API"}}, "productMasterConfig": {"processingFees": [{"chargeType": "GST", "valueType": "PERCENTAGE", "value": 18}, {"chargeType": "STAMPING", "valueType": "FIXED", "value": 10}], "subVention": [{"chargeType": "GST", "valueType": "PERCENTAGE", "value": 18}]}, "easeBuzzGateway": {"key": "273A919AD1", "salt": "651605439C", "hostUrl": "https://wire.easebuzz.in", "balanceEnquiryUrl": "/api/v1/virtual_accounts/", "paymentUrl": "/api/v1/quick_transfers/initiate/", "paymentEnquiryUrl": "/api/v1/transfers/", "virtualAccountNumber": "101000000000083156"}, "lendBoxGateway": {"key": "5dfdXXdDAb23HJgVxsFuP1epWED17kk4xxpe5kPDjzYeCGuvFs3RfJUzsio8BsbKABBh7ERn173txd1hzLomJP4yE2Dit7CfEKGR", "code": "bts/Pf1qQ9PIWyUO2JJOMw==", "hostUrl": "https://api.lendbox.in", "batchPaymentUrl": "/v1/partners/wallet/orders", "batchPaymentEnquiryUrl": "/v1/partners/wallet/orders/status", "paymentEnquiryUrl": "/v1/partners/disbural-status", "bankAccountDetailsUpdate": "/v1/partners/user/bankdetails"}, "startupRefresh": {"secretKey": "Fundfina@123"}, "paymentService": {"accountId": "64266b6bfcf0da5b25390a64", "authToken": "4051c9d9d616df832380c8001", "baseUrl": "https://payment.fundfina.com", "paymentLink": "/api/payment/link", "paymentLinkList": "/api/paymentlink/list", "cancelPaymentLink": "/api/cancel/paymentLink", "paymentLinkDetails": "/api/payment/link/details", "settlementAlert": "/api/callback/settlement", "updatePaymentLinkStatus": "/api/update/paymentLink/status", "paymentsAdjustments": "/api/payment/adjustments"}, "manualQueue": {"baseUrl": "https://reconciliation.fundfina.com", "settlementUrl": "/api/manual/queue", "settlementTransaction": "/api/settlement/transaction", "secretKey": "FundF!na@123", "igonredMISList": "/api/igonored/mislog"}, "awsCredentialsRecon": {"bucketName": "prod-reconciliation"}, "invoiceUrl": {"url": "https://invoice.fundfina.com", "secretKey": "wxk3hgrsiof4ze6v9g7i"}, "reconService": {"baseUrl": "https://reconciliation.fundfina.com", "secretKey": "FundF!na@123", "lenderPayoutList": "/api/payout/list", "lenderPayoutInit": "/api/payout/init", "lenderPayoutStagingInit": "/api/payout/initiate/staging", "redisbursePayout": "/api/payout/redisburse", "settlementMISUTRList": "/api/settlementmisutr/list", "payoutOrderSplitDetails": "/api/payout/ordersplit/details", "redisbursePayoutV2": "/api/payout/redisbursev2", "createSettlementTransaction": "/api/settlement/transaction/create"}, "paymentApi": {"secretKey": "FundF!na@123"}, "nocEmailCredentials": {"email": "<EMAIL>", "password": "Fundfina@123"}, "reportsService": {"baseUrl": "https://reports.fundfina.com", "secretKey": "FundF!na@123", "reportLogList": "/api/reportservice/reportsjob/list", "reportDownloadUrl": "/api/reportservice/download/url", "reportTypesList": "/api/reportservice/report/types"}, "Xambit": {"baseUrl": "https://xapp-gstr-5qxdkkqowq-el.a.run.app/api/x-01HKCVQG8T65HQGP2NT3S5Q2RB/gstin", "xHost": "fundfina.xambit.io", "xKey": "key-01HEM3YV3JC3NYAKNG7JX2GS0W-01HEM3YV3J411HRBFYSS5S1EVE"}, "BankStatements": {"BankStatementsBucket": "bank-statement-pdf-storage/User-Bankstatement-pdf-files"}}